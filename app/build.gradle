apply plugin: 'com.android.application'

group = 'com.movisat.mupdfmini'
version = '1.26.3a'

dependencies {
	if (file('../lib/build.gradle').isFile())
		api project(':lib')
	else
		api 'com.artifex.mupdf:mini:1.26.3a'
}

// FIRMA - INI
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
	keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}
// FIRMA - FIN

android {
	compileSdkVersion 33
	defaultConfig {
		minSdkVersion 21
		targetSdkVersion 34
		versionName '25090201'
		versionCode 25090201
	}

	splits {
		abi {
			enable true
			universalApk true
		}
	}

	bundle {
		abi {
			enableSplit true
		}
	}
	// FIRMA - INI
	signingConfigs {
		release {
			keyAlias keystoreProperties['keyAlias']
			keyPassword keystoreProperties['keyPassword']
			storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
			storePassword keystoreProperties['storePassword']
		}
	}

	buildTypes {
		release {
			signingConfig signingConfigs.release
			minifyEnabled false
			proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
		}
	}
	// FIRMA - FIN
}
