commit a62faa233c1f0c6a3622628bc96d8443e57ceb7d
Author: ruben<PERSON> <<EMAIL>>
Date:   2025-09-02 14:04:20 +0200

    HU-ERAP-002/AP-628: (work-in-progress) al mantener pulsado el botón salir muestra el dialogo de salida

commit 0460e9fa2bca3b2419c3b15c136ec2801ff18bd0
Author: ruben<PERSON> <<EMAIL>>
Date:   2025-09-02 14:04:06 +0200

    HU-ERAP-002/AP-628: (work-in-progress) guarda logs en databases/log.db

commit 16ec129a2cb1db4183d4beca72e05f56f8bac0a5
Author: rubenvas <<EMAIL>>
Date:   2025-09-02 13:06:31 +0200

    HU-ERAP-002/AP-628: (work-in-progress) refactorización a la petición de permisos

commit 9656e850b09f0ae6d2a998d8610172a6cf487ebd
Author: rubenvas <<EMAIL>>
Date:   2025-09-02 12:27:09 +0200

    HU-ERAP-002/AP-628: (work-in-progress) mejorada petición de permisos, ahora si ya están otorgados no se vuelven a pedir

commit 4d0ad0623fc1e88304b83691496f434259e9538b
Author: rubenvas <<EMAIL>>
Date:   2025-09-02 09:48:04 +0200

    HU-ERAP-002/AP-628: (work-in-progress) code refactor

commit 71e483662d7a5f53ab4d899ea8462327af1f9cd9
Author: rubenvas <<EMAIL>>
Date:   2025-09-02 09:05:20 +0200

    HU-ERAP-002/AP-628: (work-in-progress) versión 25090201

commit e85316c2f5763aef3cbeb8ef544d12bcb41ea911
Author: rubenvas <<EMAIL>>
Date:   2025-09-01 14:21:01 +0200

    HU-ERAP-002/AP-628: (work-in-progress) optimización del código

commit d6ddac61eeb8ef6cc9c203f14f84abecf744de10
Author: rubenvas <<EMAIL>>
Date:   2025-09-01 13:59:57 +0200

    HU-ERAP-002/AP-628: (work-in-progress) desactiva el modo kiosco y device admin cuando se sale de la app

commit fcb6b552d6d24a50906804d69712a4fb956f9686
Author: rubenvas <<EMAIL>>
Date:   2025-09-01 13:19:08 +0200

    HU-ERAP-002/AP-628: (work-in-progress) primera version de la app como device owner y modo kiosco

commit 69595a7d86db38196f06c0fd7cad6c86aca37214
Author: rubenvas <<EMAIL>>
Date:   2025-08-11 13:46:53 +0200

    HU-ERAP-002/AP-628: (work-in-progress) implementado dialogo con código de salida solo cuando no está instalado EcoRutas

commit b47cce521ab01dac84c77f982ccfe4885ac470a3
Author: rubenvas <<EMAIL>>
Date:   2025-08-11 12:20:15 +0200

    HU-ERAP-002/AP-628: (work-in-progress) mejorada barra lateral

commit db9826fd4b1a244146866e15055afd174ed44c46
Author: rubenvas <<EMAIL>>
Date:   2025-08-11 11:41:27 +0200

    HU-ERAP-002/AP-628: (work-in-progress) mejorado espaciado de la barra lateral

commit b49acd9b8b6809141293d1032944fb098a30a20e
Author: rubenvas <<EMAIL>>
Date:   2025-08-11 10:58:16 +0200

    HU-ERAP-002/AP-628: (work-in-progress) mejorados los iconos de navegación de la barra lateral, ahora el botón atrás vuelve a ecorutas

commit fa182a7ce1e0fd8555015c0f160905efdbdb1b36
Author: rubenvas <<EMAIL>>
Date:   2025-08-08 13:57:57 +0200

    HU-ERAP-002/AP-628: (work-in-progress) mejorados los gestos con el panel lateral abierto

commit ef82a7f1dfd21ab0e118e446015551279fd4dc04
Author: rubenvas <<EMAIL>>
Date:   2025-08-08 13:31:17 +0200

    HU-ERAP-002/AP-628: (work-in-progress) subo a 8 segundos el tiempo de visualización de la barra lateral, aumento la opcidad

commit 42577c3686ca332c4039a39e7766430fb32875b1
Author: rubenvas <<EMAIL>>
Date:   2025-08-08 11:43:58 +0200

    HU-ERAP-002/AP-628: (work-in-progress) bloqueo de enlaces externos para no salir de la app

commit 9f00a557ec8e10315e1e2dd5de9e882340f21d66
Author: rubenvas <<EMAIL>>
Date:   2025-08-08 11:36:47 +0200

    HU-ERAP-002/AP-628: (work-in-progress) nueva barra lateral derecha, eliminacion de gestos para pasar de pagina

commit 4b79ed4ccbe70cc5c8dbefa2de25cf73a0a43887
Author: rubenvas <<EMAIL>>
Date:   2025-08-07 13:08:26 +0200

    HU-ERAP-002/AP-628: Cambio del nombre del paquete a com.movisat.mupdfmini

commit 877bb958db5ccd60b50b7d2de3ad0c0dd7035558
Author: rubenvas <<EMAIL>>
Date:   2025-08-07 12:09:47 +0200

    HU-ERAP-002/AP-628: Método que vuelve a ecorutas, varios iconos

commit b94f3cebf40108a017c7ee77fdd19f5e4ce9d913
Author: rubenvas <<EMAIL>>
Date:   2025-08-07 11:59:13 +0200

    Instrucciones para compilar MuPDF-Mini

commit d67f3b510a46b3374fc2d0be09a96476ea8d074d
Author: rubenvas <<EMAIL>>
Date:   2025-07-22 11:34:50 +0200

    Importación inicial de MuPDF mini
