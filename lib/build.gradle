apply plugin: 'com.android.library'
apply plugin: 'maven-publish'

group = 'com.movisat.mupdfmini'
version = '1.26.3a'

dependencies {
	implementation 'androidx.appcompat:appcompat:1.2.0'
	if (file('../jni/build.gradle').isFile())
		api project(':jni')
	else
		api 'com.artifex.mupdf:fitz:1.26.3'
}

android {
	compileSdkVersion 33
	defaultConfig {
		minSdkVersion 21
		targetSdkVersion 34
	}
	publishing {
		singleVariant("release") {
			withSourcesJar()
		}
	}
}

project.afterEvaluate {
	publishing {
		publications {
			release(MavenPublication) {
				artifactId 'mini'
				artifact(bundleReleaseAar)

				pom {
					name = 'mini'
					url = 'http://www.mupdf.com'
					licenses {
						license {
							name = 'GNU Affero General Public License'
							url = 'https://www.gnu.org/licenses/agpl-3.0.html'
						}
					}
				}
				pom.withXml {
					final dependenciesNode = asNode().appendNode('dependencies')
					configurations.implementation.allDependencies.each {
						def dependencyNode = dependenciesNode.appendNode('dependency')
						dependencyNode.appendNode('groupId', it.group)
						dependencyNode.appendNode('artifactId', it.name)
						dependencyNode.appendNode('version', it.version)
					}
				}
			}
		}
		repositories {
			maven {
				name 'Local'
				if (project.hasProperty('MAVEN_REPO')) {
					url = MAVEN_REPO
				} else {
					url = "file://${System.properties['user.home']}/MAVEN"
				}
			}
		}
	}
}
