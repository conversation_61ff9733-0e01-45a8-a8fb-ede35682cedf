<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:keepScreenOn="true">

    <com.movisat.mupdfmini.PageView
        android:id="@+id/page_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#505050"
        android:keepScreenOn="true" />

    <!-- New Sidebar Overlay: full-screen touch interceptor with right panel -->
    <RelativeLayout
        android:id="@+id/sidebar_overlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone">

        <LinearLayout
            android:id="@+id/sidebar_panel"
            android:layout_width="80dp"
            android:layout_height="match_parent"
            android:layout_alignParentEnd="true"
            android:background="#DD000000"
            android:orientation="vertical">

            <ImageButton
                android:id="@+id/btn_arrow_right"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="2"
                android:adjustViewBounds="true"
                android:background="@drawable/button"
                android:scaleType="centerInside"
                android:src="@drawable/ic_arrow_right_white_100dp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:background="#000000" />

            <ImageButton
                android:id="@+id/btn_arrow_left"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="2"
                android:adjustViewBounds="true"
                android:background="@drawable/button"
                android:scaleType="centerInside"
                android:src="@drawable/ic_arrow_left_white_100dp" />

            <View
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:background="#000000" />

            <Button
                android:id="@+id/btn_exit"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="2"
                android:background="@drawable/button"
                android:gravity="center"
                android:text="@string/exit"
                android:textAllCaps="true"
                android:textColor="#FFFFFF"
                android:textSize="30sp"
                android:textStyle="bold" />

            <View
                android:layout_width="match_parent"
                android:layout_height="2dp"
                android:background="#000000" />

            <!-- Row: page indicator + hide button -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/sidebar_page_label"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_gravity="center_vertical"
                    android:gravity="center"
                    android:text="- / -"
                    android:textColor="#FFFFFF"
                    android:textSize="18sp" />

                <ImageButton
                    android:id="@+id/btn_hide_sidebar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:adjustViewBounds="true"
                    android:background="@drawable/button"
                    android:scaleType="centerInside"
                    android:src="@drawable/ic_chevron_right_white_24dp" />
            </LinearLayout>

        </LinearLayout>
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_alignParentTop="true"
        android:background="#303030"
        android:orientation="horizontal"
        android:visibility="gone">

        <TextView
            android:id="@+id/title_label"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="12dp"
            android:layout_marginRight="12dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:singleLine="true"
            android:textColor="#FFFFFF"
            android:textSize="16sp" />

        <ImageButton
            android:id="@+id/search_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/button"
            android:src="@drawable/ic_search_white_24dp" />

        <ImageButton
            android:id="@+id/zoom_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/button"
            android:src="@drawable/ic_zoom_out_map_white_24dp"
            android:visibility="gone" />

        <ImageButton
            android:id="@+id/layout_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/button"
            android:src="@drawable/ic_format_size_white_24dp"
            android:visibility="gone" />

        <ImageButton
            android:id="@+id/outline_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/button"
            android:src="@drawable/ic_toc_white_24dp"
            android:visibility="gone" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/search_bar"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_alignParentTop="true"
        android:background="#303030"
        android:orientation="horizontal"
        android:visibility="gone">

        <ImageButton
            android:id="@+id/search_close_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/button"
            android:src="@drawable/ic_close_white_24dp" />

        <EditText
            android:id="@+id/search_text"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="8dp"
            android:layout_weight="1"
            android:background="@android:color/transparent"
            android:hint="@string/text_search_hint"
            android:imeOptions="actionSearch"
            android:importantForAutofill="no"
            android:inputType="text"
            android:singleLine="true"
            android:textColor="#FFFFFF"
            android:textColorHint="#a0a0a0"
            android:textSize="16sp" />

        <ImageButton
            android:id="@+id/search_backward_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/button"
            android:src="@drawable/ic_chevron_left_white_24dp" />

        <ImageButton
            android:id="@+id/search_forward_button"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:background="@drawable/button"
            android:src="@drawable/ic_chevron_right_white_24dp" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/navigation_bar"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_alignParentBottom="true"
        android:background="#303030"
        android:orientation="horizontal"
        android:visibility="gone">

        <SeekBar
            android:id="@+id/page_seekbar"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_weight="1"
            android:max="0"
            android:progressDrawable="@drawable/seek_line"
            android:thumb="@drawable/seek_thumb" />

        <TextView
            android:id="@+id/page_label"
            android:layout_width="110dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:ellipsize="end"
            android:gravity="end"
            android:padding="8dp"
            android:singleLine="true"
            android:text="- / -"
            android:textColor="#FFFFFF"
            android:textSize="16sp" />
    </LinearLayout>
</RelativeLayout>
