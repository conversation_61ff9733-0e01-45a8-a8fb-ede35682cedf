package com.movisat.mupdfmini.permissions;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.provider.Settings;
import android.os.Build;
import android.util.Log;

import androidx.core.content.ContextCompat;

import com.movisat.mupdfmini.AdminUtils;

import java.util.Arrays;
import java.util.List;

public final class PermissionManager {
    private static final String TAG = "MuPDF";
    private static final String PREFS = "perm_prefs";

    private PermissionManager() {
    }

    public static void ensureCriticalPermissions(Context context) {
        if (context == null) return;
        Log.i(TAG, "PermMgr - ensuring critical permissions");
        if (!AdminUtils.isDeviceRooted()) {
            Log.w(TAG, "PermMgr - device not rooted; skipping auto-grant");
            return;
        }
        ensureAll(context, Arrays.asList(
                "android.permission.SYSTEM_ALERT_WINDOW",
                "android.permission.WRITE_SETTINGS",
                "android.permission.WRITE_EXTERNAL_STORAGE",
                "android.permission.READ_PHONE_STATE",
                "android.permission.INJECT_EVENTS"
        ));
    }

    public static void ensureAll(Context context, List<String> permissionList) {
        final String packageName = context.getPackageName();
        for (String permission : permissionList) {
            final String basePermission = normalize(permission);
            if ("INJECT_EVENTS".equals(basePermission) && !isPrivilegedApp(context)) {
                Log.i(TAG, "PermMgr - skip INJECT_EVENTS (non-priv app)");
                continue;
            }
            if (hasAttempted(context, basePermission) && !isGranted(context, permission)) {
                Log.i(TAG, "PermMgr - skip auto-grant (attempted): " + basePermission);
                continue;
            }
            if (isGranted(context, permission)) {
                Log.i(TAG, "PermMgr - already granted: " + permission);
                continue;
            }

            grant(context, permission, packageName);
            boolean ok = isGranted(context, permission);
            if (!ok) {
                markAttempted(context, basePermission);
                Log.w(TAG, "PermMgr - post-grant still not granted: " + permission);
            }
        }
    }

    public static boolean isGranted(Context ctx, String permission) {
        if (ctx == null || permission == null) return false;
        String base = normalize(permission);
        switch (base) {
            case "SYSTEM_ALERT_WINDOW": {
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) return true;
                boolean allowed = Settings.canDrawOverlays(ctx);
                int mode = AppOpsHelper.getMode(ctx, appOpFor(base, true));
                Log.d(TAG, "PermMgr - OVERLAY canDraw=" + allowed + " appop=" + AppOpsHelper.modeToString(mode));
                return allowed;
            }
            case "WRITE_SETTINGS": {
                if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) return true;
                boolean allowed = Settings.System.canWrite(ctx);
                int mode = AppOpsHelper.getMode(ctx, appOpFor(base, true));
                Log.d(TAG, "PermMgr - WRITE_SETTINGS canWrite=" + allowed + " appop=" + AppOpsHelper.modeToString(mode));
                return allowed;
            }
            case "INJECT_EVENTS": {
                return ctx.getPackageManager().checkPermission("android.permission.INJECT_EVENTS", ctx.getPackageName()) == PackageManager.PERMISSION_GRANTED;
            }
            default:
                return ContextCompat.checkSelfPermission(ctx, permission) == PackageManager.PERMISSION_GRANTED;
        }
    }

    private static void grant(Context ctx, String permission, String pkg) {
        String base = normalize(permission);
        if ("SYSTEM_ALERT_WINDOW".equals(base) || "WRITE_SETTINGS".equals(base)) {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) return;
            String op = appOpFor(base, true);
            boolean ok = AppOpsHelper.setAllowed(ctx, pkg, op);
            if (!ok) {
                op = appOpFor(base, false);
                ok = AppOpsHelper.setAllowed(ctx, pkg, op);
            }
            if (ok) Log.i(TAG, "PermMgr - appops allowed: " + op);
            return;
        }

        // Dangerous permissions: only grant via pm on >=23; on 21–22 they are manifest-time
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            boolean ok = AdminUtils.executeCommand("pm grant " + pkg + " " + permission);
            if (ok) Log.i(TAG, "PermMgr - pm grant ok: " + permission);
        }
    }

    private static String appOpFor(String permissionName, boolean withAndroidPrefix) {
        String name;
        if ("SYSTEM_ALERT_WINDOW".equals(permissionName)) {
            name = withAndroidPrefix ? "android:system_alert_window" : "SYSTEM_ALERT_WINDOW";
        } else if ("WRITE_SETTINGS".equals(permissionName)) {
            name = withAndroidPrefix ? "android:write_settings" : "WRITE_SETTINGS";
        } else {
            name = permissionName;
        }
        return name;
    }

    private static String normalize(String full) {
        if (full == null) return "";
        if (full.startsWith("android.permission."))
            return full.substring("android.permission.".length());
        return full;
    }

    public static boolean isPrivilegedApp(Context context) {
        try {
            android.content.pm.ApplicationInfo ai = context.getApplicationInfo();
            if (ai == null) return false;
            String src = ai.sourceDir;
            return src != null && src.contains("/priv-app/");
        } catch (Throwable t) {
            return false;
        }
    }

    private static SharedPreferences prefs(Context ctx) {
        return ctx.getSharedPreferences(PREFS, Context.MODE_PRIVATE);
    }

    private static boolean hasAttempted(Context ctx, String perm) {
        return prefs(ctx).getBoolean("auto_grant_attempted_" + perm, false);
    }

    private static void markAttempted(Context ctx, String perm) {
        prefs(ctx).edit().putBoolean("auto_grant_attempted_" + perm, true).apply();
    }
}

