package com.movisat.mupdfmini.permissions;

import android.app.AppOpsManager;
import android.content.Context;

public final class AppOpsHelper {
    private AppOpsHelper() {
    }

    public static boolean setAllowed(Context context, String packageName, String op) {
        if (context == null || packageName == null || op == null) return false;
        // Use appops shell via root to set allowed
        return com.movisat.mupdfmini.AdminUtils.executeCommand("appops set " + packageName + " " + op + " allow");
    }

    public static int getMode(Context context, String op) {
        try {
            AppOpsManager aom = (AppOpsManager) context.getSystemService(Context.APP_OPS_SERVICE);
            if (aom == null) return AppOpsManager.MODE_ERRORED;
            return aom.checkOpNoThrow(op, android.os.Process.myUid(), context.getPackageName());
        } catch (Throwable t) {
            return AppOpsManager.MODE_ERRORED;
        }
    }

    public static String modeToString(int mode) {
        switch (mode) {
            case AppOpsManager.MODE_ALLOWED:
                return "allowed";
            case AppOpsManager.MODE_IGNORED:
                return "ignored";
            case AppOpsManager.MODE_ERRORED:
                return "errored";
            case AppOpsManager.MODE_DEFAULT:
                return "default";
            default:
                return String.valueOf(mode);
        }
    }
}

