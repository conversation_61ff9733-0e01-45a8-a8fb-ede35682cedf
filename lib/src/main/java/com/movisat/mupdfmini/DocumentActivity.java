package com.movisat.mupdfmini;

import com.artifex.mupdf.fitz.*;
import com.artifex.mupdf.fitz.android.*;

import android.Manifest;
import android.app.Activity;
import android.app.AlertDialog;
import android.content.ContentResolver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.FileUriExposedException;
import android.os.ParcelFileDescriptor;
import android.provider.OpenableColumns;
import android.text.Editable;
import android.text.InputType;
import android.text.TextWatcher;
import android.text.method.PasswordTransformationMethod;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MenuItem;
import android.view.View;
import android.view.MotionEvent;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.PopupMenu;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.RequiresApi;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Stack;

public class DocumentActivity extends Activity
{
	private final String APP = "MuPDF";

	public final int NAVIGATE_REQUEST = 1;

	protected Worker worker;
	protected SharedPreferences prefs;

	protected Document doc;

	protected String key;
	protected String mimetype;
	protected SeekableInputStream stream;
	protected byte[] buffer;

	protected boolean returnToLibraryActivity;
	protected boolean hasLoaded;
	protected boolean isReflowable;
	protected boolean fitPage;
	protected String title;
	protected ArrayList<OutlineActivity.Item> flatOutline;
	protected float layoutW, layoutH, layoutEm;
	protected float displayDPI;
	protected int canvasW, canvasH;
	protected float pageZoom;

	protected View currentBar;
	protected PageView pageView;
	protected View actionBar;
	protected TextView titleLabel;
	protected View searchButton;
	protected View searchBar;
	protected EditText searchText;
	protected View searchCloseButton;
	protected View searchBackwardButton;
	protected View searchForwardButton;
	protected View zoomButton;
	protected View layoutButton;
	protected PopupMenu layoutPopupMenu;
	protected View outlineButton;
	protected View navigationBar;
	protected TextView pageLabel;
	protected SeekBar pageSeekbar;

	// Sidebar overlay
	protected View sidebarOverlay;
	protected View sidebarPanel;
	protected View btnArrowRight;
	protected View btnArrowLeft;
	protected View btnExit;
	protected TextView sidebarPageLabel;
	protected android.os.Handler sidebarHandler = new android.os.Handler();
	protected final Runnable sidebarAutoHide = new Runnable() {
		@Override public void run() { hideSidebar(); }
	};

	// Sidebar overlay touch tracking
	private float overlayDownX, overlayDownY;
	private boolean overlayDragging;
	private int overlayTouchSlop;

	protected int pageCount;
	protected int currentPage;
	protected int searchHitPage;
	protected String searchNeedle;
	protected boolean stopSearch;
	protected Stack<Integer> history;
	protected boolean wentBack;
	// Marca para evitar desactivar kiosko/owner múltiples veces en el mismo ciclo
	private boolean kioskOwnerDisabled;

	private String toHex(byte[] digest) {
		StringBuilder builder = new StringBuilder(2 * digest.length);
		for (byte b : digest)
			builder.append(String.format("%02x", b));
		return builder.toString();
	}

	private void openInput(Uri uri, long size, String mimetype) throws IOException {
		ContentResolver cr = getContentResolver();

		Log.i(APP, "Opening document " + uri);

		InputStream is = cr.openInputStream(uri);
		byte[] buf = null;
		int used = -1;
		try {
			final int limit = 8 * 1024 * 1024;
			if (size < 0) { // size is unknown
				buf = new byte[limit];
				used = is.read(buf);
				boolean atEOF = is.read() == -1;
				if (used < 0 || (used == limit && !atEOF)) // no or partial data
					buf = null;
			} else if (size <= limit) { // size is known and below limit
				buf = new byte[(int) size];
				used = is.read(buf);
				if (used < 0 || used < size) // no or partial data
					buf = null;
			}
			if (buf != null && buf.length != used) {
				byte[] newbuf = new byte[used];
				System.arraycopy(buf, 0, newbuf, 0, used);
				buf = newbuf;
			}
		} catch (OutOfMemoryError e) {
			buf = null;
		} finally {
			is.close();
		}

		if (buf != null) {
			Log.i(APP, "  Opening document from memory buffer of size " + buf.length);
			buffer = buf;
		} else {
			Log.i(APP, "  Opening document from stream");
			stream = new ContentInputStream(cr, uri, size);
		}
	}

	public void onCreate(Bundle savedInstanceState) {
		super.onCreate(savedInstanceState);
		requestWindowFeature(Window.FEATURE_NO_TITLE);
		getWindow().addFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN);

		DisplayMetrics metrics = new DisplayMetrics();
		getWindowManager().getDefaultDisplay().getMetrics(metrics);
		displayDPI = metrics.densityDpi;

		setContentView(R.layout.document_activity);
		actionBar = findViewById(R.id.action_bar);
		searchBar = findViewById(R.id.search_bar);
		navigationBar = findViewById(R.id.navigation_bar);

		// Sidebar setup
		sidebarOverlay = findViewById(R.id.sidebar_overlay);
		sidebarPanel = findViewById(R.id.sidebar_panel);
		btnArrowRight = findViewById(R.id.btn_arrow_right);
		btnArrowLeft = findViewById(R.id.btn_arrow_left);
		btnExit = findViewById(R.id.btn_exit);
		sidebarPageLabel = findViewById(R.id.sidebar_page_label);

		// Set sidebar width to 25% of screen
		ViewGroup.LayoutParams lp = sidebarPanel.getLayoutParams();
        lp.width = Math.round(getResources().getDisplayMetrics().widthPixels * 0.25f);
		sidebarPanel.setLayoutParams(lp);

		overlayTouchSlop = ViewConfiguration.get(this).getScaledTouchSlop();
		sidebarOverlay.setOnTouchListener((v, event) -> {
			if (sidebarOverlay.getVisibility() != View.VISIBLE) return false;
			// If touch is inside panel, let children handle it
			float ex = event.getX();
			float ey = event.getY();
			int left = sidebarPanel.getLeft();
			int top = sidebarPanel.getTop();
			int right = sidebarPanel.getRight();
			int bottom = sidebarPanel.getBottom();
			boolean insidePanel = ex >= left && ex <= right && ey >= top && ey <= bottom;
			if (insidePanel) return false;

			switch (event.getActionMasked()) {
				case MotionEvent.ACTION_DOWN:
					overlayDownX = ex;
					overlayDownY = ey;
					overlayDragging = false;
					resetSidebarAutoHide();
					if (pageView != null) pageView.dispatchTouchEvent(event);
					return true;
				case MotionEvent.ACTION_POINTER_DOWN:
					overlayDragging = true;
					resetSidebarAutoHide();
					if (pageView != null) pageView.dispatchTouchEvent(event);
					return true;
				case MotionEvent.ACTION_MOVE: {
					float dx = Math.abs(ex - overlayDownX);
					float dy = Math.abs(ey - overlayDownY);
					if (!overlayDragging && (dx > overlayTouchSlop || dy > overlayTouchSlop)) overlayDragging = true;
					resetSidebarAutoHide();
					if (pageView != null) pageView.dispatchTouchEvent(event);
					return true;
				}
				case MotionEvent.ACTION_UP:
					if (!overlayDragging) {
						// Treat as a click for accessibility and a11y services
						v.performClick();
						hideSidebar();
						return true;
					}
					if (pageView != null) pageView.dispatchTouchEvent(event);
					overlayDragging = false;
					return true;
				case MotionEvent.ACTION_CANCEL:
					if (pageView != null) pageView.dispatchTouchEvent(event);
					overlayDragging = false;
					return true;
			}
			if (pageView != null) pageView.dispatchTouchEvent(event);
			return true;
		});
		sidebarPanel.setOnClickListener(v -> resetSidebarAutoHide());
		btnArrowRight.setOnClickListener(v -> {
            resetSidebarAutoHide();
            if (pageView != null) pageView.scrollToBottomOrNext();
        });
		btnArrowLeft.setOnClickListener(v -> {
            resetSidebarAutoHide();
            if (pageView != null) pageView.scrollToTopOrPrev();
        });
		btnExit.setOnClickListener(v -> {
            Log.i(APP, "Sidebar: SALIR pressed");
            launchEcosatApp();
        });

		View btnHide = findViewById(R.id.btn_hide_sidebar);
		if (btnHide != null) btnHide.setOnClickListener(v -> hideSidebar());

		currentBar = actionBar;

		Uri uri = getIntent().getData();
		mimetype = getIntent().getType();

		if (uri == null) {
			Toast.makeText(this, "No document uri to open", Toast.LENGTH_SHORT).show();
			return;
		}

		returnToLibraryActivity = getIntent().getIntExtra(getComponentName().getPackageName() + ".ReturnToLibraryActivity", 0) != 0;

		key = uri.toString();

		Log.i(APP, "OPEN URI " + uri.toString());
		Log.i(APP, "  MAGIC (Intent) " + mimetype);

		title = "";
		long size = -1;
		Cursor cursor = null;

		try {
			cursor = getContentResolver().query(uri, null, null, null, null, null);
			if (cursor != null && cursor.moveToFirst()){
				int idx;

				idx = cursor.getColumnIndex(OpenableColumns.DISPLAY_NAME);
				if (idx >= 0 && cursor.getType(idx) == Cursor.FIELD_TYPE_STRING)
					title = cursor.getString(idx);

				idx = cursor.getColumnIndex(OpenableColumns.SIZE);
				if (idx >= 0 && cursor.getType(idx) == Cursor.FIELD_TYPE_INTEGER)
					size = cursor.getLong(idx);

				if (size == 0)
					size = -1;
			}
		} catch (Exception x) {
			// Ignore any exception and depend on default values for title
			// and size (unless one was decoded
		} finally {
			if (cursor != null)
				cursor.close();
		}

		Log.i(APP, "  NAME " + title);
		Log.i(APP, "  SIZE " + size);

		if (mimetype == null || mimetype.equals("application/octet-stream")) {
			mimetype = getContentResolver().getType(uri);
			Log.i(APP, "  MAGIC (Resolver) " + mimetype);
		}
		if (mimetype == null || mimetype.equals("application/octet-stream")) {
			mimetype = title;
			Log.i(APP, "  MAGIC (Filename) " + mimetype);
		}

		try {
			openInput(uri, size, mimetype);
		} catch (Exception x) {
			Log.e(APP, x.toString());
			String text = x.getMessage();
			if (text == null)
				text = x.getClass().getName();
			Toast.makeText(this, text, Toast.LENGTH_SHORT).show();
		}

		titleLabel = (TextView)findViewById(R.id.title_label);
		titleLabel.setText(title);

		history = new Stack<Integer>();

		worker = new Worker(this);
		worker.start();

		prefs = getPreferences(Context.MODE_PRIVATE);
		layoutEm = prefs.getFloat("layoutEm", 8);
		fitPage = prefs.getBoolean("fitPage", false);
		currentPage = prefs.getInt(key, 0);
		searchHitPage = -1;
		hasLoaded = false;

		pageView = (PageView)findViewById(R.id.page_view);
		pageView.setActionListener(this);

		pageLabel = (TextView)findViewById(R.id.page_label);
		pageSeekbar = (SeekBar)findViewById(R.id.page_seekbar);
		pageSeekbar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
			public int newProgress = -1;
			public void onProgressChanged(SeekBar seekbar, int progress, boolean fromUser) {
				if (fromUser) {
					newProgress = progress;
					String txt = (progress+1) + " / " + pageCount;
					pageLabel.setText(txt);
					if (sidebarPageLabel != null) sidebarPageLabel.setText(txt);
				}
			}
			public void onStartTrackingTouch(SeekBar seekbar) {}
			public void onStopTrackingTouch(SeekBar seekbar) {
				gotoPage(newProgress);
			}
		});

		searchButton = findViewById(R.id.search_button);
		searchButton.setOnClickListener(new View.OnClickListener() {
			public void onClick(View v) {
				showSearch();
			}
		});
		searchText = (EditText)findViewById(R.id.search_text);
		searchText.setOnEditorActionListener(new TextView.OnEditorActionListener() {
			public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
				if (actionId == EditorInfo.IME_NULL && event.getAction() == KeyEvent.ACTION_DOWN) {
					search(1);
					return true;
				}
				if (actionId == EditorInfo.IME_ACTION_SEARCH) {
					search(1);
					return true;
				}
				return false;
			}
		});
		searchText.addTextChangedListener(new TextWatcher() {
			public void afterTextChanged(Editable s) {}
			public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
			public void onTextChanged(CharSequence s, int start, int before, int count) {
				resetSearch();
			}
		});
		searchCloseButton = findViewById(R.id.search_close_button);
		searchCloseButton.setOnClickListener(new View.OnClickListener() {
			public void onClick(View v) {
				hideSearch();
			}
		});
		searchBackwardButton = findViewById(R.id.search_backward_button);
		searchBackwardButton.setOnClickListener(new View.OnClickListener() {
			public void onClick(View v) {
				search(-1);
			}
		});
		searchForwardButton = findViewById(R.id.search_forward_button);
		searchForwardButton.setOnClickListener(new View.OnClickListener() {
			public void onClick(View v) {
				search(1);
			}
		});

		outlineButton = findViewById(R.id.outline_button);
		outlineButton.setOnClickListener(new View.OnClickListener() {
			public void onClick(View v) {
				Intent intent = new Intent(DocumentActivity.this, OutlineActivity.class);
				Bundle bundle = new Bundle();
				bundle.putInt("POSITION", currentPage);
				bundle.putSerializable("OUTLINE", flatOutline);
				intent.putExtras(bundle);
				startActivityForResult(intent, NAVIGATE_REQUEST);
			}
		});

		zoomButton = findViewById(R.id.zoom_button);
		zoomButton.setOnClickListener(new View.OnClickListener() {
			public void onClick(View v) {
				fitPage = !fitPage;
				loadPage();
			}
		});

		layoutButton = findViewById(R.id.layout_button);
		layoutPopupMenu = new PopupMenu(this, layoutButton);
		layoutPopupMenu.getMenuInflater().inflate(R.menu.layout_menu, layoutPopupMenu.getMenu());
		layoutPopupMenu.setOnMenuItemClickListener(new PopupMenu.OnMenuItemClickListener() {
			public boolean onMenuItemClick(MenuItem item) {
				float oldLayoutEm = layoutEm;
				int id = item.getItemId();
				if (id == R.id.action_layout_6pt) layoutEm = 6;
				else if (id == R.id.action_layout_7pt) layoutEm = 7;
				else if (id == R.id.action_layout_8pt) layoutEm = 8;
				else if (id == R.id.action_layout_9pt) layoutEm = 9;
				else if (id == R.id.action_layout_10pt) layoutEm = 10;
				else if (id == R.id.action_layout_11pt) layoutEm = 11;
				else if (id == R.id.action_layout_12pt) layoutEm = 12;
				else if (id == R.id.action_layout_13pt) layoutEm = 13;
				else if (id == R.id.action_layout_14pt) layoutEm = 14;
				else if (id == R.id.action_layout_15pt) layoutEm = 15;
				else if (id == R.id.action_layout_16pt) layoutEm = 16;
				if (oldLayoutEm != layoutEm)
					relayoutDocument();
				return true;
			}
		});
		layoutButton.setOnClickListener(new View.OnClickListener() {
			public void onClick(View v) {
				layoutPopupMenu.show();
			}
		});
		
		// Inicializar modo kiosko solo en RELEASE
		if (!BuildConfig.DEBUG) {
			initializeKioskMode();
		} else {
			Log.i(APP, "DocumentActivity - Modo DEBUG: kiosko omitido");
		}
	}

	public boolean onKeyUp(int keyCode, KeyEvent event) {
		switch (keyCode) {
		case KeyEvent.KEYCODE_PAGE_UP:
		case KeyEvent.KEYCODE_COMMA:
		case KeyEvent.KEYCODE_B:
			goBackward();
			return true;
		case KeyEvent.KEYCODE_PAGE_DOWN:
		case KeyEvent.KEYCODE_PERIOD:
		case KeyEvent.KEYCODE_SPACE:
			goForward();
			return true;
		case KeyEvent.KEYCODE_M:
			history.push(currentPage);
			return true;
		case KeyEvent.KEYCODE_T:
			if (!history.empty()) {
				currentPage = history.pop();
				loadPage();
			}
			return true;
		}
		return super.onKeyUp(keyCode, event);
	}

	public void onPageViewSizeChanged(int w, int h) {
		pageZoom = 1;
		canvasW = w;
		canvasH = h;
		layoutW = canvasW * 72 / displayDPI;
		layoutH = canvasH * 72 / displayDPI;
		if (!hasLoaded) {
			hasLoaded = true;
			openDocument();
		} else if (isReflowable) {
			relayoutDocument();
		} else {
			loadPage();
		}
	}

	public void onPageViewZoomChanged(float zoom) {
		if (zoom != pageZoom) {
			pageZoom = zoom;
			loadPage();
		}
	}

	protected void openDocument() {
		worker.add(new Worker.Task() {
			boolean needsPassword;
			public void work() {
				Log.i(APP, "open document");
				if (buffer != null)
					doc = Document.openDocument(buffer, mimetype);
				else
					doc = Document.openDocument(stream, mimetype);
				needsPassword = doc.needsPassword();
			}
			public void run() {
				if (needsPassword)
					askPassword(R.string.dlog_password_message);
				else
					loadDocument();
			}
		});
	}

	protected void askPassword(int message) {
		final EditText passwordView = new EditText(this);
		passwordView.setInputType(EditorInfo.TYPE_TEXT_VARIATION_PASSWORD);
		passwordView.setTransformationMethod(PasswordTransformationMethod.getInstance());

		AlertDialog.Builder builder = new AlertDialog.Builder(this);
		builder.setTitle(R.string.dlog_password_title);
		builder.setMessage(message);
		builder.setView(passwordView);
		builder.setPositiveButton(android.R.string.ok, new DialogInterface.OnClickListener() {
			public void onClick(DialogInterface dialog, int id) {
				checkPassword(passwordView.getText().toString());
			}
		});
		builder.setNegativeButton(android.R.string.cancel, new DialogInterface.OnClickListener() {
			public void onClick(DialogInterface dialog, int id) {
				finish();
			}
		});
		builder.setOnCancelListener(new DialogInterface.OnCancelListener() {
			public void onCancel(DialogInterface dialog) {
				finish();
			}
		});
		builder.create().show();
	}

	protected void checkPassword(final String password) {
		worker.add(new Worker.Task() {
			boolean passwordOkay;
			public void work() {
				Log.i(APP, "check password");
				passwordOkay = doc.authenticatePassword(password);
			}
			public void run() {
				if (passwordOkay)
					loadDocument();
				else
					askPassword(R.string.dlog_password_retry);
			}
		});
	}

	public void onPause() {
		super.onPause();
		if (prefs != null) {
			SharedPreferences.Editor editor = prefs.edit();
			editor.putFloat("layoutEm", layoutEm);
			editor.putBoolean("fitPage", fitPage);
			editor.putInt(key, currentPage);
			editor.apply();
		}
	}

	public void onBackPressed() {
		launchEcosatApp();
	}

	public void onActivityResult(int request, int result, Intent data) {
		if (request == NAVIGATE_REQUEST && result >= RESULT_FIRST_USER)
			gotoPage(result - RESULT_FIRST_USER);
	}

	protected void showKeyboard() {
		InputMethodManager imm = (InputMethodManager)getSystemService(Context.INPUT_METHOD_SERVICE);
		if (imm != null)
			imm.showSoftInput(searchText, 0);
	}

	protected void hideKeyboard() {
		InputMethodManager imm = (InputMethodManager)getSystemService(Context.INPUT_METHOD_SERVICE);
		if (imm != null)
			imm.hideSoftInputFromWindow(searchText.getWindowToken(), 0);
	}

	protected void resetSearch() {
		stopSearch = true;
		searchHitPage = -1;
		searchNeedle = null;
		pageView.resetHits();
	}

	protected void runSearch(final int startPage, final int direction, final String needle) {
		stopSearch = false;
		worker.add(new Worker.Task() {
			int searchPage = startPage;
			public void work() {
				if (stopSearch || needle != searchNeedle)
					return;
				for (int i = 0; i < 9; ++i) {
					Log.i(APP, "search page " + searchPage);
					Page page = doc.loadPage(searchPage);
					Quad[][] hits = page.search(searchNeedle);
					page.destroy();
					if (hits != null && hits.length > 0) {
						searchHitPage = searchPage;
						break;
					}
					searchPage += direction;
					if (searchPage < 0 || searchPage >= pageCount)
						break;
				}
			}
			public void run() {
				if (stopSearch || needle != searchNeedle) {
					pageLabel.setText((currentPage+1) + " / " + pageCount);
				} else if (searchHitPage == currentPage) {
					loadPage();
				} else if (searchHitPage >= 0) {
					history.push(currentPage);
					currentPage = searchHitPage;
					loadPage();
				} else {
					if (searchPage >= 0 && searchPage < pageCount) {
						pageLabel.setText((searchPage+1) + " / " + pageCount);
						worker.add(this);
					} else {
						pageLabel.setText((currentPage+1) + " / " + pageCount);
						Log.i(APP, "search not found");
						Toast.makeText(DocumentActivity.this, getString(R.string.toast_search_not_found), Toast.LENGTH_SHORT).show();
					}
				}
			}
		});
	}

	protected void search(int direction) {
		hideKeyboard();
		int startPage;
		if (searchHitPage == currentPage)
			startPage = currentPage + direction;
		else
			startPage = currentPage;
		searchHitPage = -1;
		searchNeedle = searchText.getText().toString();
		if (searchNeedle.length() == 0)
			searchNeedle = null;
		if (searchNeedle != null)
			if (startPage >= 0 && startPage < pageCount)
				runSearch(startPage, direction, searchNeedle);
	}

	protected void loadDocument() {
		worker.add(new Worker.Task() {
			public void work() {
				try {
					Log.i(APP, "load document");
					String metaTitle = doc.getMetaData(Document.META_INFO_TITLE);
					if (metaTitle != null && !metaTitle.equals(""))
						title = metaTitle;
					isReflowable = doc.isReflowable();
					if (isReflowable) {
						Log.i(APP, "layout document");
						doc.layout(layoutW, layoutH, layoutEm);
					}
					pageCount = doc.countPages();
				} catch (Throwable x) {
					doc = null;
					pageCount = 1;
					currentPage = 0;
					throw x;
				}
			}
			public void run() {
				if (currentPage < 0 || currentPage >= pageCount)
					currentPage = 0;
				titleLabel.setText(title);
				if (isReflowable)
					layoutButton.setVisibility(View.VISIBLE);
				else
					zoomButton.setVisibility(View.VISIBLE);
				loadPage();
				loadOutline();
				// Ensure legacy bars remain hidden; sidebar is the primary overlay
				actionBar.setVisibility(View.GONE);
				navigationBar.setVisibility(View.GONE);
			}
		});
	}

	protected void relayoutDocument() {
		worker.add(new Worker.Task() {
			public void work() {
				try {
					long mark = doc.makeBookmark(doc.locationFromPageNumber(currentPage));
					Log.i(APP, "relayout document");
					doc.layout(layoutW, layoutH, layoutEm);
					pageCount = doc.countPages();
					currentPage = doc.pageNumberFromLocation(doc.findBookmark(mark));
				} catch (Throwable x) {
					pageCount = 1;
					currentPage = 0;
					throw x;
				}
			}
			public void run() {
				loadPage();
				loadOutline();
			}
		});
	}

	private void loadOutline() {
		worker.add(new Worker.Task() {
			private void flattenOutline(Outline[] outline, String indent) {
				for (Outline node : outline) {
					if (node.title != null)
					{
						int outlinePage = doc.pageNumberFromLocation(doc.resolveLink(node));
						flatOutline.add(new OutlineActivity.Item(indent + node.title, node.uri, outlinePage));
					}
					if (node.down != null)
						flattenOutline(node.down, indent + "    ");
				}
			}
			public void work() {
				Log.i(APP, "load outline");
				Outline[] outline = doc.loadOutline();
				if (outline != null) {
					flatOutline = new ArrayList<OutlineActivity.Item>();
					flattenOutline(outline, "");
				} else {
					flatOutline = null;
				}
			}
			public void run() {
				if (flatOutline != null)
					outlineButton.setVisibility(View.VISIBLE);
			}
		});
	}

	protected void loadPage() {
		final int pageNumber = currentPage;
		final float zoom = pageZoom;
		stopSearch = true;
		worker.add(new Worker.Task() {
			public Bitmap bitmap;
			public Rect[] linkBounds;
			public String[] linkURIs;
			public Quad[][] hits;
			public void work() {
				try {
					Log.i(APP, "load page " + pageNumber);
					Page page = doc.loadPage(pageNumber);
					Log.i(APP, "draw page " + pageNumber + " zoom=" + zoom);
					Matrix ctm;
					if (fitPage)
						ctm = AndroidDrawDevice.fitPage(page, canvasW, canvasH);
					else
						ctm = AndroidDrawDevice.fitPageWidth(page, canvasW);
					Link[] links = page.getLinks();
					if (links == null)
					{
						linkBounds = new Rect[0];
						linkURIs = new String[0];
					}
					else
					{
						linkBounds = new Rect[links.length];
						linkURIs = new String[links.length];
						for (int i = 0; i < links.length; i++)
						{
							linkBounds[i] = links[i].getBounds().transform(ctm);
							linkURIs[i] = links[i].getURI();
						}
					}
					if (searchNeedle != null) {
						hits = page.search(searchNeedle);
						if (hits != null)
							for (Quad[] hit : hits)
								for (Quad chr : hit)
									chr.transform(ctm);
					}
					if (zoom != 1)
						ctm.scale(zoom);
					bitmap = AndroidDrawDevice.drawPage(page, ctm);
				} catch (Throwable x) {
					Log.e(APP, x.getMessage());
				}
			}
			public void run() {
				if (bitmap != null)
					pageView.setBitmap(bitmap, zoom, wentBack, linkBounds, linkURIs, hits);
				else
					pageView.setError();
				String txt = (currentPage+1) + " / " + pageCount;
				pageLabel.setText(txt);
				if (sidebarPageLabel != null) sidebarPageLabel.setText(txt);
				pageSeekbar.setMax(pageCount - 1);
				pageSeekbar.setProgress(pageNumber);
				wentBack = false;
			}
		});
	}

	protected void showSearch() {
		currentBar = searchBar;
		actionBar.setVisibility(View.GONE);
		searchBar.setVisibility(View.VISIBLE);
		searchBar.requestFocus();
		showKeyboard();
	}

	protected void hideSearch() {
		currentBar = actionBar;
		actionBar.setVisibility(View.VISIBLE);
		searchBar.setVisibility(View.GONE);
		hideKeyboard();
		resetSearch();
	}

	public void showSidebar() {
		try {
			if (sidebarOverlay.getVisibility() != View.VISIBLE) {
				Log.i(APP, "Sidebar: show");
				sidebarOverlay.setVisibility(View.VISIBLE);
			}
			resetSidebarAutoHide();
		} catch (Exception ignored) {}
	}

	public void hideSidebar() {
		try {
			Log.i(APP, "Sidebar: hide");
			sidebarHandler.removeCallbacks(sidebarAutoHide);
			sidebarOverlay.setVisibility(View.GONE);
		} catch (Exception ignored) {}
	}

	private void resetSidebarAutoHide() {
		sidebarHandler.removeCallbacks(sidebarAutoHide);
		sidebarHandler.postDelayed(sidebarAutoHide, 8000);
	}

	public void goBackward() {
		if (currentPage > 0) {
			wentBack = true;
			currentPage --;
			loadPage();
		}
	}

	public void goForward() {
		if (currentPage < pageCount - 1) {
			currentPage ++;
			loadPage();
		}
	}

	public void gotoPage(int p) {
		if (p >= 0 && p < pageCount && p != currentPage) {
			history.push(currentPage);
			currentPage = p;
			loadPage();
		}
	}

	public void gotoPage(String uri) {
		gotoPage(doc.pageNumberFromLocation(doc.resolveLink(uri)));
	}

    private void launchEcosatApp() {
        try {
            Intent intent = getPackageManager().getLaunchIntentForPackage("com.movisat.ecosat");
            if (intent != null) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_REORDER_TO_FRONT);
                startActivity(intent);
                finishAndRemoveTask();
            } else {
                showExitCodeDialog();
            }
        } catch (Exception ignored) {
            showExitCodeDialog();
        }
    }

	private void showExitCodeDialog() {
		AlertDialog.Builder builder = new AlertDialog.Builder(this);
		builder.setTitle(R.string.exit_code_title);

		EditText input = new EditText(this);
		input.setInputType(InputType.TYPE_CLASS_NUMBER);
		input.setHint(R.string.exit_code_hint);
		builder.setView(input);

		builder.setPositiveButton(R.string.accept, (dialog, which) -> {
			String code = input.getText().toString().trim();
            if ("15379".equals(code)) {
                // Código de salida: Desactivar kiosko y Device Owner, y salir
                disableKioskAndDeviceOwner();
                finishAndRemoveTask();
            } else {
                Toast.makeText(this, R.string.exit_code_error, Toast.LENGTH_SHORT).show();
            }
        });

		builder.setNegativeButton(R.string.cancel, (dialog, which) -> dialog.dismiss());

		builder.show();
	}

	private void disableKioskAndDeviceOwner() {
		try {
			AdminUtils.setKioskModeEnabled(this, false);
			AdminUtils.setHomeButtonEnabled(true);
			AdminUtils.removeDeviceOwner(this);
			AdminUtils.removeActiveAdmin(this);
			Log.i(APP, "Kiosko y Device Owner desactivados");
			kioskOwnerDisabled = true;
		} catch (Throwable t) {
			Log.e(APP, "Error desactivando kiosko/owner: " + t.getMessage());
		}
	}
	
    private void initializeKioskMode() {
        Log.i(APP, "DocumentActivity - Inicializando modo kiosko");

        // Solo en RELEASE, activar bloqueo total
        if (!BuildConfig.DEBUG) {
            AdminUtils.initializeKioskMode(this);
            AdminUtils.setKioskModeEnabled(this, true);
            Log.i(APP, "DocumentActivity - Modo kiosko ACTIVADO");
        } else {
            Log.i(APP, "DocumentActivity - Modo DEBUG: Kiosko desactivado");
        }
    }
	
	@Override
	protected void onResume() {
		super.onResume();
		if (!BuildConfig.DEBUG && !AdminUtils.isKioskMode() && !kioskOwnerDisabled) {
			Log.i(APP, "DocumentActivity - Reactivando modo kiosko");
			initializeKioskMode();
		}
	}

	@Override
	protected void onStop() {
		super.onStop();
		if (!isChangingConfigurations() && !kioskOwnerDisabled) {
			Log.i(APP, "DocumentActivity - onStop: desactivando kiosko/owner");
			disableKioskAndDeviceOwner();
		}
	}
}
