package com.movisat.mupdfmini;

import android.app.Activity;
import android.app.admin.DevicePolicyManager;
import android.content.ComponentName;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.provider.Settings;
import android.os.Build;
import android.util.Log;
import android.view.View;
import android.app.AppOpsManager;
import android.content.SharedPreferences;

import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserFactory;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;

import static android.content.Context.DEVICE_POLICY_SERVICE;
import androidx.core.content.ContextCompat;

public class AdminUtils {
	private static final String APP = "MuPDF";
	private static final int ACTIVE_ADMIN = 0;
	private static final int DEVICE_OWNER = 1;
	private static boolean kioskActive = false;

	/**
	 * MÉTODO PRINCIPAL: Activa/Desactiva el modo kiosko completo
	 */
	public static boolean setKioskModeEnabled(final Activity activity, boolean enabled) {
		// Para el modo debug no activar kiosko
		if (enabled && BuildConfig.DEBUG) {
			Log.i(APP, "Modo DEBUG: Kiosko desactivado para desarrollo");
			return false;
		}

		if (activity == null) {
			return false;
		}

        try {
            if (enabled) {
				// 1. ACTIVAR LOCK TASK MODE
                activity.startLockTask();

				// 2. OCULTAR UI DEL SISTEMA
                activity.runOnUiThread(() -> {
                    View decorView = activity.getWindow().getDecorView();
                    int visibility = View.SYSTEM_UI_FLAG_FULLSCREEN // Ocultar Status Bar
                            | View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY; // Mantener oculto
                    decorView.setSystemUiVisibility(visibility);
                });

				// 3. DESHABILITAR BOTÓN HOME
                setHomeButtonEnabled(false);

                kioskActive = true;
                Log.i(APP, "Modo kiosko ACTIVADO");

            } else {
				// SALIR DEL MODO KIOSKO
                activity.stopLockTask();
                setHomeButtonEnabled(true);
                kioskActive = false;
                Log.i(APP, "Modo kiosko DESACTIVADO");
            }

            return true;

        } catch (Exception e) {
            Log.e(APP, "Error en setKioskModeEnabled: " + e.getMessage());
        }
        return false;
	}

	/**
	 * Establece la aplicación como administrador activo
	 */
	public static boolean setActiveAdmin(Context context) {
		return execDpmCommand(context, ACTIVE_ADMIN);
	}

	/**
	 * Establece la aplicación como device-owner
	 */
	public static boolean setDeviceOwner(Context context) {
		return execDpmCommand(context, DEVICE_OWNER);
	}

	/**
	 * Desactiva device-owner
	 */
	public static boolean removeDeviceOwner(Context context) {
        try {
            DevicePolicyManager dpm = (DevicePolicyManager) context.getSystemService(DEVICE_POLICY_SERVICE);
            dpm.clearDeviceOwnerApp(context.getPackageName());
            Log.i(APP, "Device owner deshabilitado");
            return true;
        } catch (Exception e) {
            Log.e(APP, "Error deshabilitando device owner: " + e.getMessage());
        }
        return false;
	}

	/**
	 * Desactiva administrador activo
	 */
	public static boolean removeActiveAdmin(Context context) {
		try {
			ComponentName devAdminReceiver = new ComponentName(context, AdminReceiver.class);
			DevicePolicyManager dpm = (DevicePolicyManager) context.getSystemService(DEVICE_POLICY_SERVICE);
			dpm.removeActiveAdmin(devAdminReceiver);
			Log.i(APP, "Active admin deshabilitado");
			return true;
		} catch (Exception e) {
			Log.e(APP, "Error deshabilitando active admin: " + e.getMessage());
		}
		return false;
	}

	/**
	 * Configura paquetes permitidos para Lock Task Mode
	 */
	public static boolean setLockTaskPackages(Context context, String[] packages) {
		boolean res = false;
		try {
            DevicePolicyManager dpm = (DevicePolicyManager) context.getSystemService(DEVICE_POLICY_SERVICE);
            ComponentName mDeviceAdminRcvr = new ComponentName(context, AdminReceiver.class);
            dpm.setLockTaskPackages(mDeviceAdminRcvr, packages);
            res = true;
            Log.i(APP, "Lock task packages configurados: " + packages.length + " paquetes");
        } catch (Exception e) {
			Log.e(APP, "Error en setLockTaskPackages: " + e.getMessage());
		}
		return res;
	}

	/**
	 * CRÍTICO: Habilita/Deshabilita el botón HOME del sistema
	 */
	public static void setHomeButtonEnabled(boolean enabled) {
		boolean success = executeCommand("settings put secure user_setup_complete " + (enabled ? "1" : "0"));
		if (success) {
			Log.i(APP, "Botón HOME " + (enabled ? "habilitado" : "deshabilitado"));
		}
	}

	/**
	 * Verifica si la app es device-owner
	 */
	public static boolean isThisAppDeviceOwner(Context context) {
		String ownerPackage = getDeviceOwnerPackage();
		boolean isOwner = ownerPackage.equals(context.getPackageName());
		Log.i(APP, "App es device owner: " + isOwner + " (owner actual: " + ownerPackage + ")");
		return isOwner;
	}

	/**
	 * Obtiene el paquete device-owner actual
	 */
	public static String getDeviceOwnerPackage() {
		String ret = "";
		try {
			Process process = Runtime.getRuntime().exec("su");
			DataOutputStream dos = new DataOutputStream(process.getOutputStream());
			dos.writeBytes("cat /data/system/device_owner.xml\n");
			dos.writeBytes("exit\n");
			dos.flush();
			dos.close();
			process.waitFor();

			if (process != null) {
				XmlPullParserFactory xmlFactoryObject = XmlPullParserFactory.newInstance();
				XmlPullParser myParser = xmlFactoryObject.newPullParser();
				myParser.setInput(process.getInputStream(), null);

				int event = myParser.getEventType();
				while (event != XmlPullParser.END_DOCUMENT) {
					String name = myParser.getName();
					if (event == XmlPullParser.END_TAG) {
						if (name.equals("device-owner")) {
							ret = myParser.getAttributeValue(null, "package");
							return ret;
						}
					}
					event = myParser.next();
				}
			}
		} catch (Exception e) {
			Log.e(APP, "Error obteniendo device owner: " + e.getMessage());
		}
		return ret;
	}

	/**
	 * Ejecuta comandos DPM (Device Policy Manager)
	 */
	private static boolean execDpmCommand(Context context, int action) {
		boolean res = false;
		try {
			String adminReceiverComponent = context.getPackageName() + "/" +
					AdminReceiver.class.getCanonicalName();

			String dpmCommand;
			switch (action) {
				case ACTIVE_ADMIN:
					dpmCommand = "dpm set-active-admin " + adminReceiverComponent + "\n";
					break;
				case DEVICE_OWNER:
					dpmCommand = "dpm set-device-owner " + adminReceiverComponent + "\n";
					break;
				default:
					return false;
			}

			Process process = Runtime.getRuntime().exec("su");
			if (process != null) {
				DataOutputStream os = new DataOutputStream(process.getOutputStream());
				BufferedReader in = new BufferedReader(new InputStreamReader(process.getInputStream()));
				os.writeBytes(dpmCommand);
				os.writeBytes("exit\n");
				os.flush();
				os.close();

				if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.M) {
					process.waitFor();
					String line;
					if ((line = in.readLine()) != null)
						res = line.contains("Success");
					process.destroy();
				} else {
					res = true;
				}
				
				Log.i(APP, "Comando DPM ejecutado - Tipo: " + (action == ACTIVE_ADMIN ? "ACTIVE_ADMIN" : "DEVICE_OWNER") + " - Resultado: " + res);
			}
		} catch (Exception e) {
			Log.e(APP, "Error en execDpmCommand: " + e.getMessage());
		}
		return res;
	}

    /**
     * Ejecuta comando como root
     */
    public static boolean executeCommand(String command) {
		Process process = null;
		try {
			process = Runtime.getRuntime().exec("su");
			if (!command.endsWith("\n"))
				command += "\n";

			DataOutputStream dos = new DataOutputStream(process.getOutputStream());
			dos.writeBytes(command);
			dos.writeBytes("exit\n");
			dos.flush();
			dos.close();
			process.waitFor();

			int exitValue = process.exitValue();
			process.destroy();
			Log.d(APP, "Comando ejecutado: " + command.trim() + " - Exit code: " + exitValue);
			return exitValue == 0;
		} catch (Throwable e) {
			Log.e(APP, "Error ejecutando comando: " + e.getMessage());
		}
		return false;
	}

    /**
     * Otorga permisos específicos según API y tipo de permiso.
     */
    public static void grantPermission(Context context, String permission) {
        final String pkg = context.getPackageName();
        final String fullPerm = permission;
        final String perm = normalizePermission(permission);

        // Special permissions via AppOps on >= 23
        if (perm.equals("SYSTEM_ALERT_WINDOW") || perm.equals("WRITE_SETTINGS")) {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M) return; // Always granted
            // Try with android: prefix then plain op name
            String op = appOpForPermission(perm, true);
            boolean ok = executeCommand("appops set " + pkg + " " + op + " allow");
            if (!ok) {
                op = appOpForPermission(perm, false);
                ok = executeCommand("appops set " + pkg + " " + op + " allow");
            }
            if (ok) Log.i(APP, "Permiso otorgado con appops (" + op + "): " + perm);
            return;
        }

        // Dangerous permissions on >=23 via pm grant; no-op on <23
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            boolean ok = executeCommand("pm grant " + pkg + " " + fullPerm);
            if (ok) Log.i(APP, "Permiso otorgado con pm grant: " + fullPerm);
        }
    }

	/**
	 * Check if a permission is already granted for this app.
	 */
    public static boolean hasPermission(Context context, String permission) {
        if (context == null || permission == null)
            return false;

        try {
            final String perm = normalizePermission(permission);

            switch (perm) {
                case "INJECT_EVENTS":
                    return context.getPackageManager().checkPermission("android.permission.INJECT_EVENTS", context.getPackageName()) == PackageManager.PERMISSION_GRANTED;

                case "SYSTEM_ALERT_WINDOW": {
                    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M)
                        return true;
                    boolean allowed = Settings.canDrawOverlays(context);
                    int mode = getAppOpMode(context, appOpForPermission("SYSTEM_ALERT_WINDOW", true));
                    Log.d(APP, "Check OVERLAY: canDraw=" + allowed + " appop=" + modeToString(mode));
                    return allowed;
                }
                case "WRITE_SETTINGS": {
                    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.M)
                        return true;
                    boolean allowed = Settings.System.canWrite(context);
                    int mode = getAppOpMode(context, appOpForPermission("WRITE_SETTINGS", true));
                    Log.d(APP, "Check WRITE_SETTINGS: canWrite=" + allowed + " appop=" + modeToString(mode));
                    return allowed;
                }
            }

            return ContextCompat.checkSelfPermission(context, permission) == PackageManager.PERMISSION_GRANTED;
        } catch (Throwable t) {
            Log.w(APP, "Error checking permission (assume NOT granted): " + permission + " - " + t.getMessage());
            return false;
        }
    }

	/**
	 * Grant a permission only if not already granted.
	 */
    public static void grantPermissionIfNeeded(Context context, String permission) {
        try {
            final String perm = normalizePermission(permission);
            if (hasAttemptedAutoGrant(context, perm) && !hasPermission(context, permission)) {
                Log.i(APP, "Skipping auto-grant (previous attempt failed): " + perm);
                return;
            }

            if (perm.equals("INJECT_EVENTS")) {
                if (!isPrivilegedApp(context)) {
                    Log.i(APP, "Skipping privileged-only permission for non-privileged app: " + permission);
                    return;
                }
            }

            if (hasPermission(context, permission)) {
                Log.i(APP, "Permission already granted, skipping: " + permission);
                return;
            }
        } catch (Throwable t) {
            Log.w(APP, "Failed to verify permission, attempting grant: " + permission + " - " + t.getMessage());
        }

        grantPermission(context, permission);
        boolean nowGranted = hasPermission(context, permission);
        if (!nowGranted) {
            markAttemptedAutoGrant(context, normalizePermission(permission));
            Log.w(APP, "Auto-grant did not yield granted state; will skip next boot: " + permission);
        }
    }

    private static String normalizePermission(String permission) {
        if (permission == null) return "";
        if (permission.startsWith("android.permission."))
            return permission.substring("android.permission.".length());
        return permission;
    }

    private static String appOpForPermission(String permissionName, boolean withAndroidPrefix) {
        String appOpName;
        if ("SYSTEM_ALERT_WINDOW".equals(permissionName)) {
            appOpName = withAndroidPrefix ? "android:system_alert_window" : "SYSTEM_ALERT_WINDOW";
        } else if ("WRITE_SETTINGS".equals(permissionName)) {
            appOpName = withAndroidPrefix ? "android:write_settings" : "WRITE_SETTINGS";
        } else {
            appOpName = permissionName; // Fallback
        }
        return appOpName;
    }

    private static int getAppOpMode(Context context, String appOp) {
        try {
            AppOpsManager appOpsManager = (AppOpsManager) context.getSystemService(Context.APP_OPS_SERVICE);
            if (appOpsManager == null) return AppOpsManager.MODE_ERRORED;
            return appOpsManager.checkOpNoThrow(appOp, android.os.Process.myUid(), context.getPackageName());
        } catch (Throwable throwable) {
            return AppOpsManager.MODE_ERRORED;
        }
    }

    private static String modeToString(int mode) {
        switch (mode) {
            case AppOpsManager.MODE_ALLOWED: return "allowed";
            case AppOpsManager.MODE_IGNORED: return "ignored";
            case AppOpsManager.MODE_ERRORED: return "errored";
            case AppOpsManager.MODE_DEFAULT: return "default";
            default: return String.valueOf(mode);
        }
    }

    private static SharedPreferences getPermPrefs(Context ctx) {
        return ctx.getSharedPreferences("perm_prefs", Context.MODE_PRIVATE);
    }

    private static String attemptKey(String perm) {
        return "auto_grant_attempted_" + perm;
    }

    private static boolean hasAttemptedAutoGrant(Context ctx, String perm) {
        return getPermPrefs(ctx).getBoolean(attemptKey(perm), false);
    }

    private static void markAttemptedAutoGrant(Context ctx, String perm) {
        getPermPrefs(ctx).edit().putBoolean(attemptKey(perm), true).apply();
    }

	/**
	 * Returns true if the app is installed as a privileged system app (under /system/priv-app).
	 */
	public static boolean isPrivilegedApp(Context context) {
		try {
			ApplicationInfo ai = context.getApplicationInfo();
			if (ai == null) return false;
			String src = ai.sourceDir;
			return src != null && src.contains("/priv-app/");
		} catch (Throwable t) {
			return false;
		}
	}

	/**
	 * Estado del modo kiosko
	 */
	public static boolean isKioskMode() {
		return kioskActive;
	}

	/**
	 * Verifica si el dispositivo está rooteado
	 */
	public static boolean isDeviceRooted() {
		try {
			Process process = Runtime.getRuntime().exec("su");
			DataOutputStream dos = new DataOutputStream(process.getOutputStream());
			dos.writeBytes("exit\n");
			dos.flush();
			dos.close();
			process.waitFor();
			boolean rooted = process.exitValue() == 0;
			Log.i(APP, "Device rooted: " + rooted);
			return rooted;
		} catch (Throwable e) {
			Log.i(APP, "Device no rooted");
			return false;
		}
	}

	/**
	 * Inicializa permisos y configuraciones para kiosko
	 */
	public static void initializeKioskMode(Context context) {
		Log.i(APP, "Inicializando modo kiosko...");
		
		if (!isDeviceRooted()) {
			Log.w(APP, "ADVERTENCIA: El dispositivo no tiene root. Funcionalidad limitada.");
			return;
		}

		// Establecer como device owner y admin
		setDeviceOwner(context);
		setActiveAdmin(context);

		// Configurar paquetes permitidos
		String[] packages = {
			context.getPackageName(),
			"com.movisat.mupdfmini.app",
			// Permitir salir a EcoRutas/EcoSat desde modo kiosko
			"com.movisat.ecosat"
		};
		setLockTaskPackages(context, packages);

		Log.i(APP, "Inicialización completa");
	}
}
