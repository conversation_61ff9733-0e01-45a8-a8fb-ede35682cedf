package com.movisat.mupdfmini;

import android.app.Activity;
import android.app.admin.DevicePolicyManager;
import android.content.ComponentName;
import android.content.Context;
import android.os.Build;
import android.util.Log;
import android.view.View;

import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserFactory;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.InputStreamReader;

import static android.content.Context.DEVICE_POLICY_SERVICE;

public class AdminUtils {
	private static final String APP = "MuPDF";
	private static final int ACTIVE_ADMIN = 0;
	private static final int DEVICE_OWNER = 1;
	private static boolean kioskActive = false;

	/**
	 * MÉTODO PRINCIPAL: Activa/Desactiva el modo kiosko completo
	 */
	public static boolean setKioskModeEnabled(final Activity activity, boolean enabled) {
		// Para el modo debug no activar kiosko (cambiar según necesidad)
		if (enabled && BuildConfig.DEBUG) {
			Log.i(APP, "Modo DEBUG: Kiosko desactivado para desarrollo");
			return false;
		}

		if (activity == null) return false;

		if (Build.VERSION.SDK_INT >= 21) {
			try {
				if (enabled) {
					// 1. ACTIVAR LOCK TASK MODE
					activity.startLockTask();

					// 2. OCULTAR UI DEL SISTEMA
					activity.runOnUiThread(new Runnable() {
						@Override
						public void run() {
							View decorView = activity.getWindow().getDecorView();
							int visibility = View.SYSTEM_UI_FLAG_FULLSCREEN // Ocultar Status Bar
									| View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY; // Mantener oculto
							decorView.setSystemUiVisibility(visibility);
						}
					});

					// 3. DESHABILITAR BOTÓN HOME
					setHomeButtonEnabled(false);

					kioskActive = true;
					Log.i(APP, "Modo kiosko ACTIVADO");

				} else {
					// SALIR DEL MODO KIOSKO
					activity.stopLockTask();
					setHomeButtonEnabled(true);
					kioskActive = false;
					Log.i(APP, "Modo kiosko DESACTIVADO");
				}

				return true;

			} catch (Exception e) {
				Log.e(APP, "Error en setKioskModeEnabled: " + e.getMessage());
			}
		}
		return false;
	}

	/**
	 * Establece la aplicación como administrador activo
	 */
	public static boolean setActiveAdmin(Context context) {
		return execDpmCommand(context, ACTIVE_ADMIN);
	}

	/**
	 * Establece la aplicación como device-owner
	 */
	public static boolean setDeviceOwner(Context context) {
		return execDpmCommand(context, DEVICE_OWNER);
	}

	/**
	 * Desactiva device-owner
	 */
	public static boolean removeDeviceOwner(Context context) {
		if (android.os.Build.VERSION.SDK_INT >= 21) {
			try {
				DevicePolicyManager dpm = (DevicePolicyManager) context.getSystemService(DEVICE_POLICY_SERVICE);
				dpm.clearDeviceOwnerApp(context.getPackageName());
				Log.i(APP, "Device owner deshabilitado");
				return true;
			} catch (Exception e) {
				Log.e(APP, "Error deshabilitando device owner: " + e.getMessage());
			}
		}
		return false;
	}

	/**
	 * Desactiva administrador activo
	 */
	public static boolean removeActiveAdmin(Context context) {
		try {
			ComponentName devAdminReceiver = new ComponentName(context, AdminReceiver.class);
			DevicePolicyManager dpm = (DevicePolicyManager) context.getSystemService(DEVICE_POLICY_SERVICE);
			dpm.removeActiveAdmin(devAdminReceiver);
			Log.i(APP, "Active admin deshabilitado");
			return true;
		} catch (Exception e) {
			Log.e(APP, "Error deshabilitando active admin: " + e.getMessage());
		}
		return false;
	}

	/**
	 * Configura paquetes permitidos para Lock Task Mode
	 */
	public static boolean setLockTaskPackages(Context context, String[] packages) {
		boolean res = false;
		try {
			if (android.os.Build.VERSION.SDK_INT >= 21) {
				DevicePolicyManager dpm = (DevicePolicyManager) context.getSystemService(DEVICE_POLICY_SERVICE);
				ComponentName mDeviceAdminRcvr = new ComponentName(context, AdminReceiver.class);
				dpm.setLockTaskPackages(mDeviceAdminRcvr, packages);
				res = true;
				Log.i(APP, "Lock task packages configurados: " + packages.length + " paquetes");
			}
		} catch (Exception e) {
			Log.e(APP, "Error en setLockTaskPackages: " + e.getMessage());
		}
		return res;
	}

	/**
	 * CRÍTICO: Habilita/Deshabilita el botón HOME del sistema
	 */
	public static void setHomeButtonEnabled(boolean enabled) {
		boolean success = executeCommand("settings put secure user_setup_complete " + (enabled ? "1" : "0"));
		if (success) {
			Log.i(APP, "Botón HOME " + (enabled ? "habilitado" : "deshabilitado"));
		}
	}

	/**
	 * Verifica si la app es device-owner
	 */
	public static boolean isThisAppDeviceOwner(Context context) {
		String ownerPackage = getDeviceOwnerPackage();
		boolean isOwner = ownerPackage.equals(context.getPackageName());
		Log.i(APP, "App es device owner: " + isOwner + " (owner actual: " + ownerPackage + ")");
		return isOwner;
	}

	/**
	 * Obtiene el paquete device-owner actual
	 */
	public static String getDeviceOwnerPackage() {
		String ret = "";
		try {
			Process process = Runtime.getRuntime().exec("su");
			DataOutputStream dos = new DataOutputStream(process.getOutputStream());
			dos.writeBytes("cat /data/system/device_owner.xml\n");
			dos.writeBytes("exit\n");
			dos.flush();
			dos.close();
			process.waitFor();

			if (process != null) {
				XmlPullParserFactory xmlFactoryObject = XmlPullParserFactory.newInstance();
				XmlPullParser myParser = xmlFactoryObject.newPullParser();
				myParser.setInput(process.getInputStream(), null);

				int event = myParser.getEventType();
				while (event != XmlPullParser.END_DOCUMENT) {
					String name = myParser.getName();
					if (event == XmlPullParser.END_TAG) {
						if (name.equals("device-owner")) {
							ret = myParser.getAttributeValue(null, "package");
							return ret;
						}
					}
					event = myParser.next();
				}
			}
		} catch (Exception e) {
			Log.e(APP, "Error obteniendo device owner: " + e.getMessage());
		}
		return ret;
	}

	/**
	 * Ejecuta comandos DPM (Device Policy Manager)
	 */
	private static boolean execDpmCommand(Context context, int action) {
		boolean res = false;
		try {
			String adminReceiverComponent = context.getPackageName() + "/" +
					AdminReceiver.class.getCanonicalName();

			String dpmCommand;
			switch (action) {
				case ACTIVE_ADMIN:
					dpmCommand = "dpm set-active-admin " + adminReceiverComponent + "\n";
					break;
				case DEVICE_OWNER:
					dpmCommand = "dpm set-device-owner " + adminReceiverComponent + "\n";
					break;
				default:
					return false;
			}

			Process process = Runtime.getRuntime().exec("su");
			if (process != null) {
				DataOutputStream os = new DataOutputStream(process.getOutputStream());
				BufferedReader in = new BufferedReader(new InputStreamReader(process.getInputStream()));
				os.writeBytes(dpmCommand);
				os.writeBytes("exit\n");
				os.flush();
				os.close();

				if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.M) {
					process.waitFor();
					String line;
					if ((line = in.readLine()) != null)
						res = line.contains("Success");
					process.destroy();
				} else {
					res = true;
				}
				
				Log.i(APP, "Comando DPM ejecutado - Tipo: " + (action == ACTIVE_ADMIN ? "ACTIVE_ADMIN" : "DEVICE_OWNER") + " - Resultado: " + res);
			}
		} catch (Exception e) {
			Log.e(APP, "Error en execDpmCommand: " + e.getMessage());
		}
		return res;
	}

	/**
	 * Ejecuta comando como root
	 */
	public static boolean executeCommand(String command) {
		Process process = null;
		try {
			process = Runtime.getRuntime().exec("su");
			if (!command.endsWith("\n"))
				command += "\n";

			DataOutputStream dos = new DataOutputStream(process.getOutputStream());
			dos.writeBytes(command);
			dos.writeBytes("exit\n");
			dos.flush();
			dos.close();
			process.waitFor();

			int exitValue = process.exitValue();
			process.destroy();
			Log.d(APP, "Comando ejecutado: " + command.trim() + " - Exit code: " + exitValue);
			return exitValue == 0;
		} catch (Throwable e) {
			Log.e(APP, "Error ejecutando comando: " + e.getMessage());
		}
		return false;
	}

	/**
	 * Otorga permisos específicos
	 */
	public static void grantPermission(String permission) {
		final String PACKAGE = "com.movisat.mupdfmini";

		// Intentar con pm grant
		boolean granted = executeCommand("pm grant " + PACKAGE + " " + permission);
		if (granted) {
			Log.i(APP, "Permiso otorgado con pm grant: " + permission);
			return;
		}

		// Intentar con appops
		granted = executeCommand("appops set " + PACKAGE + " " + permission + " allow");
		if (granted) {
			Log.i(APP, "Permiso otorgado con appops: " + permission);
			return;
		}

		// Para permisos especiales
		if (permission.equals("WRITE_SETTINGS") || permission.equals("SYSTEM_ALERT_WINDOW")) {
			executeCommand("settings put system write_settings_package_name " + PACKAGE);
			executeCommand("appops set " + PACKAGE + " " + permission + " allow");
			Log.i(APP, "Permiso especial configurado: " + permission);
		}
	}

	/**
	 * Estado del modo kiosko
	 */
	public static boolean isKioskMode() {
		return kioskActive;
	}

	/**
	 * Verifica si el dispositivo está rooteado
	 */
	public static boolean isDeviceRooted() {
		try {
			Process process = Runtime.getRuntime().exec("su");
			DataOutputStream dos = new DataOutputStream(process.getOutputStream());
			dos.writeBytes("exit\n");
			dos.flush();
			dos.close();
			process.waitFor();
			boolean rooted = process.exitValue() == 0;
			Log.i(APP, "Device rooted: " + rooted);
			return rooted;
		} catch (Throwable e) {
			Log.i(APP, "Device no rooted");
			return false;
		}
	}

	/**
	 * Inicializa permisos y configuraciones para kiosko
	 */
	public static void initializeKioskMode(Context context) {
		Log.i(APP, "Inicializando modo kiosko...");
		
		if (!isDeviceRooted()) {
			Log.w(APP, "ADVERTENCIA: El dispositivo no tiene root. Funcionalidad limitada.");
			return;
		}

		// Establecer como device owner y admin
		setDeviceOwner(context);
		setActiveAdmin(context);

		// Configurar paquetes permitidos
		String[] packages = {
			context.getPackageName(),
			"com.movisat.mupdfmini.app",
			// Permitir salir a EcoRutas/EcoSat desde modo kiosko
			"com.movisat.ecosat"
		};
		setLockTaskPackages(context, packages);

		Log.i(APP, "Inicialización completa");
	}
}
