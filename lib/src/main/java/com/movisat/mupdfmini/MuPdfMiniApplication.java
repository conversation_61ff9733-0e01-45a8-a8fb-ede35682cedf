package com.movisat.mupdfmini;

import android.app.Application;
import android.os.Build;
import android.util.Log;

public class MuPdfMiniApplication extends Application {

	private static final String APP = "MuPDF";

	@Override
	public void onCreate() {
		super.onCreate();
		
		Log.i(APP, "Application - Inicializando MuPDF Mini");
		
		// Otorgar permisos automáticamente en dispositivos root
		grantCriticalPermissions();
		
		Log.i(APP, "Application - Inicialización completa");
	}

	private void grantCriticalPermissions() {
		Log.i(APP, "Application - Otorgando permisos críticos");
		
		if (!AdminUtils.isDeviceRooted()) {
			Log.w(APP, "Application - Dispositivo no rooteado, saltando permisos automáticos");
			return;
		}

		AdminUtils.grantPermissionIfNeeded(getApplicationContext(), "android.permission.SYSTEM_ALERT_WINDOW");
		AdminUtils.grantPermissionIfNeeded(getApplicationContext(), "android.permission.WRITE_SETTINGS");
		AdminUtils.grantPermissionIfNeeded(getApplicationContext(), "android.permission.WRITE_EXTERNAL_STORAGE");
		AdminUtils.grantPermissionIfNeeded(getApplicationContext(), "android.permission.READ_PHONE_STATE");

		// Only attempt INJECT_EVENTS if the app is actually a privileged system app
		if (AdminUtils.isPrivilegedApp(getApplicationContext())) {
			AdminUtils.grantPermissionIfNeeded(getApplicationContext(), "android.permission.INJECT_EVENTS");
		} else {
			Log.i(APP, "Skipping INJECT_EVENTS for non-privileged app");
		}
		
		Log.i(APP, "Application - Permisos críticos procesados");
	}
}
