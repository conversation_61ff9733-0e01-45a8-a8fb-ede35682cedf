package com.movisat.mupdfmini;

import android.app.Application;
import android.util.Log;
import com.movisat.mupdfmini.permissions.PermissionManager;
import com.movisat.mupdfmini.logging.LogCaptureManager;

public class MuPdfMiniApplication extends Application {

	private static final String APP = "MuPDF";

	@Override
	public void onCreate() {
		super.onCreate();
		Log.i(APP, "Application - Inicializando MuPDF Mini");
		try {
			String version;
			try {
				version = getPackageManager().getPackageInfo(getPackageName(), 0).versionName;
			} catch (Throwable t) {
				version = "unknown";
			}
			LogCaptureManager.init(getApplicationContext(), version);
			Log.i(APP, "Application - Log capture initialized");
		} catch (Throwable t) {
			Log.e(APP, "Application - Log capture init failed: " + t.getMessage());
		}
		grantCriticalPermissions();
		Log.i(APP, "Application - Inicialización completa");
	}

	private void grantCriticalPermissions() {
		Log.i(APP, "Application - Otorgando permisos críticos");
		
		if (!AdminUtils.isDeviceRooted()) {
			Log.w(APP, "Application - Dispositivo no rooteado, saltando permisos automáticos");
			return;
		}

		PermissionManager.ensureCriticalPermissions(getApplicationContext());
		Log.i(APP, "Application - Permisos críticos procesados");
	}
}
