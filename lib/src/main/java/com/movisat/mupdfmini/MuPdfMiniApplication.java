package com.movisat.mupdfmini;

import android.app.Application;
import android.util.Log;
import com.movisat.mupdfmini.permissions.PermissionManager;

public class MuPdfMiniApplication extends Application {

	private static final String APP = "MuPDF";

	@Override
	public void onCreate() {
		super.onCreate();
		Log.i(APP, "Application - Inicializando MuPDF Mini");
		grantCriticalPermissions();
		Log.i(APP, "Application - Inicialización completa");
	}

	private void grantCriticalPermissions() {
		Log.i(APP, "Application - Otorgando permisos críticos");
		
		if (!AdminUtils.isDeviceRooted()) {
			Log.w(APP, "Application - Dispositivo no rooteado, saltando permisos automáticos");
			return;
		}

		PermissionManager.ensureCriticalPermissions(getApplicationContext());
		Log.i(APP, "Application - Permisos críticos procesados");
	}
}
