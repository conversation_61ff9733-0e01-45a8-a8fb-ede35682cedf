package com.movisat.mupdfmini;

import android.app.Application;
import android.util.Log;

public class MuPdfMiniApplication extends Application {

	private static final String APP = "MuPDF";

	@Override
	public void onCreate() {
		super.onCreate();
		
		Log.i(APP, "Application - Inicializando MuPDF Mini");

		// Initialize kiosk lifecycle tracker (SDK 21–25)
		try { KioskController.init(this); } catch (Throwable ignored) {}
		
		// Otorgar permisos automáticamente en dispositivos root
		grantCriticalPermissions();
		
		Log.i(APP, "Application - Inicialización completa");
	}

	private void grantCriticalPermissions() {
		Log.i(APP, "Application - Otorgando permisos críticos");
		
		if (!AdminUtils.isDeviceRooted()) {
			Log.w(APP, "Application - Dispositivo no rooteado, saltando permisos automáticos");
			return;
		}
		
		// Permisos críticos para el funcionamiento
		AdminUtils.grantPermission("android.permission.SYSTEM_ALERT_WINDOW");
		AdminUtils.grantPermission("android.permission.WRITE_SETTINGS");
		AdminUtils.grantPermission("android.permission.INJECT_EVENTS");
		AdminUtils.grantPermission("android.permission.WRITE_EXTERNAL_STORAGE");
		AdminUtils.grantPermission("android.permission.READ_PRIVILEGED_PHONE_STATE");
		
		Log.i(APP, "Application - Permisos críticos procesados");
	}
}
