package com.movisat.mupdfmini.logging;

import java.util.Calendar;
import java.util.TimeZone;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

final class ThreadtimeParser
{
	private static final Pattern RE = Pattern.compile(
			"^(\\d{2}-\\d{2})\\s+(\\d{2}:\\d{2}:\\d{2}\\.\\d{3})\\s+(\\d+)\\s+(\\d+)\\s+([VDIWEAF])\\s+(\\S+?)\\s*:\\s(.*)$");

	static Parsed parse(String line) {
		Matcher m = RE.matcher(line);
		if (!m.matches()) return null;
		Parsed p = new Parsed();
		p.monthDay = m.group(1);
		p.time = m.group(2);
		try {
			p.pid = Integer.parseInt(m.group(3));
			p.tid = Integer.parseInt(m.group(4));
		} catch (NumberFormatException e) {
			return null;
		}
		p.levelChar = m.group(5).charAt(0);
		p.tag = m.group(6);
		p.msg = m.group(7);
		p.ts = toEpochMillis(p.monthDay, p.time);
		return p;
	}

	private static long toEpochMillis(String md, String hmsms) {
		// md: MM-DD, hmsms: HH:MM:SS.mmm
		int month = Integer.parseInt(md.substring(0, 2)) - 1; // Calendar month 0-based
		int day = Integer.parseInt(md.substring(3, 5));
		int hour = Integer.parseInt(hmsms.substring(0, 2));
		int min = Integer.parseInt(hmsms.substring(3, 5));
		int sec = Integer.parseInt(hmsms.substring(6, 8));
		int ms = Integer.parseInt(hmsms.substring(9, 12));
		Calendar c = Calendar.getInstance(TimeZone.getDefault());
		int year = c.get(Calendar.YEAR);
		c.clear();
		c.set(Calendar.YEAR, year);
		c.set(Calendar.MONTH, month);
		c.set(Calendar.DAY_OF_MONTH, day);
		c.set(Calendar.HOUR_OF_DAY, hour);
		c.set(Calendar.MINUTE, min);
		c.set(Calendar.SECOND, sec);
		c.set(Calendar.MILLISECOND, ms);
		return c.getTimeInMillis();
	}

	static int levelToInt(char level) {
		switch (level) {
			case 'V': return 2;
			case 'D': return 3;
			case 'I': return 4;
			case 'W': return 5;
			case 'E': return 6;
			case 'A':
			case 'F': return 7; // WTF / Fatal
			default: return 3;
		}
	}

	static final class Parsed {
		String monthDay;
		String time;
		long ts;
		int pid;
		int tid;
		char levelChar;
		String tag;
		String msg;
	}
}

