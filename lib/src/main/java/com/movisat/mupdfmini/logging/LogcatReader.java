package com.movisat.mupdfmini.logging;

import android.content.Context;
import android.os.Build;

import com.movisat.mupdfmini.AdminUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

public class LogcatReader implements Runnable
{
	private final Context ctx;
	private final String buffer; // main, crash, system
	private final LogWriter writer;
	private final String appVersion;
	private volatile boolean running;
	private Thread thread;
	private Process process;
	private final int myPid;

	public LogcatReader(Context ctx, String buffer, LogWriter writer, String appVersion) {
		this.ctx = ctx.getApplicationContext();
		this.buffer = buffer;
		this.writer = writer;
		this.appVersion = appVersion;
		this.myPid = android.os.Process.myPid();
	}

	public void start() {
		if (thread != null) return;
		running = true;
		thread = new Thread(this, "LogcatReader-" + buffer);
		thread.start();
	}

	public void stop() {
		running = false;
		try { if (process != null) process.destroy(); } catch (Throwable ignored) {}
		if (thread != null) thread.interrupt();
	}

	@Override
	public void run() {
		while (running) {
			BufferedReader br = null;
			try {
				Process p = startProcess();
				process = p;
				InputStream is = p.getInputStream();
				br = new BufferedReader(new InputStreamReader(is));
				String line;
				StringBuilder multiline = null;
				LogEntry current = null;
				while (running && (line = br.readLine()) != null) {
					ThreadtimeParser.Parsed parsed = ThreadtimeParser.parse(line);
					if (parsed != null) {
						if (current != null && multiline != null) {
							current.message = multiline.toString();
							writer.enqueue(current);
						}
						// new entry
						if (!acceptPid(parsed.pid)) {
							current = null;
							multiline = null;
							continue;
						}
						current = new LogEntry();
						current.ts = parsed.ts > 0 ? parsed.ts : System.currentTimeMillis();
						current.pid = parsed.pid;
						current.tid = parsed.tid;
						current.level = ThreadtimeParser.levelToInt(parsed.levelChar);
						current.tag = parsed.tag;
						current.buffer = buffer;
						current.source = "app-logcat";
						current.appVersion = appVersion;
						multiline = new StringBuilder();
						multiline.append(parsed.msg);
					} else if (multiline != null) {
						multiline.append('\n').append(line);
					}
				}
				if (current != null && multiline != null) {
					current.message = multiline.toString();
					writer.enqueue(current);
				}
			} catch (Throwable ignored) {
				// retry after a small delay
				try { Thread.sleep(1000); } catch (InterruptedException ignored2) {}
			} finally {
				try { if (br != null) br.close(); } catch (IOException ignored) {}
				try { if (process != null) process.destroy(); } catch (Throwable ignored) {}
			}
		}
	}

	private boolean acceptPid(int pid) {
		if (Build.VERSION.SDK_INT >= 24) return true; // using --pid so no need
		return pid == myPid;
	}

	private Process startProcess() throws IOException {
		List<String> args = new ArrayList<String>();
		args.add("logcat");
		args.add("-v");
		args.add("threadtime");
		args.add("-b");
		args.add(buffer);
		if (Build.VERSION.SDK_INT >= 24) {
			args.add("--pid");
			args.add(String.valueOf(myPid));
		}
		args.add("*:V");

		ProcessBuilder pb = new ProcessBuilder(args);
		try {
			return pb.start();
		} catch (IOException e) {
			// fallback to root
			if (AdminUtils.isDeviceRooted()) {
				ArrayList<String> suArgs = new ArrayList<String>();
				suArgs.add("su");
				suArgs.add("-c");
				suArgs.add(join(args, ' '));
				return new ProcessBuilder(suArgs).start();
			}
			throw e;
		}
	}

	private static String join(List<String> parts, char sep) {
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < parts.size(); i++) {
			if (i > 0) sb.append(sep);
			sb.append(parts.get(i));
		}
		return sb.toString();
	}
}

