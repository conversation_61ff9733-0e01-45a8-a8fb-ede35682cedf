package com.movisat.mupdfmini.logging;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;

public class LogDatabase extends SQLiteOpenHelper
{
	public static final String DB_NAME = "log.db";
	private static final int DB_VERSION = 2;

	public LogDatabase(Context context) {
		super(context, DB_NAME, null, DB_VERSION);
	}

	@Override
	public void onConfigure(SQLiteDatabase db) {
		super.onConfigure(db);
		try {
			db.enableWriteAheadLogging();
			db.execSQL("PRAGMA synchronous=NORMAL");
			db.execSQL("PRAGMA foreign_keys=OFF");
		} catch (Throwable ignored) {}
	}

	@Override
	public void onCreate(SQLiteDatabase db) {
		db.execSQL(
			"CREATE TABLE IF NOT EXISTS logs (" +
				"id INTEGER PRIMARY KEY AUTOINCREMENT, " +
				"ts INTEGER NOT NULL, " +
				"pid INTEGER NOT NULL, " +
				"tid INTEGER, " +
				"level INTEGER NOT NULL, " +
				"tag TEXT, " +
				"message TEXT NOT NULL, " +
				"buffer TEXT, " +
				"source TEXT, " +
				"app_version TEXT, " +
				"date TEXT" +
			")"
		);
		db.execSQL("CREATE INDEX IF NOT EXISTS idx_logs_ts ON logs(ts)");
		db.execSQL("CREATE INDEX IF NOT EXISTS idx_logs_level_ts ON logs(level, ts)");
		db.execSQL("CREATE INDEX IF NOT EXISTS idx_logs_tag_ts ON logs(tag, ts)");
		db.execSQL("CREATE INDEX IF NOT EXISTS idx_logs_pid_ts ON logs(pid, ts)");
	}

	@Override
	public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
		if (oldVersion < 2) {
			// Add human-readable date column and backfill from epoch millis
			db.execSQL("ALTER TABLE logs ADD COLUMN date TEXT");
			db.execSQL("UPDATE logs SET date = strftime('%Y-%m-%d %H:%M:%S', ts/1000, 'unixepoch', 'localtime') WHERE date IS NULL");
		}
	}

	public static String getDatabasePath(Context ctx) {
		return ctx.getDatabasePath(DB_NAME).getAbsolutePath();
	}
}

