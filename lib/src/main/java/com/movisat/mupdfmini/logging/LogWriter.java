package com.movisat.mupdfmini.logging;

import android.content.Context;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteException;
import android.database.sqlite.SQLiteStatement;
import android.os.SystemClock;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.LinkedBlockingQueue;

public class LogWriter implements Runnable
{
	private final LogDatabase helper;
	private final LinkedBlockingQueue<LogEntry> queue;
	private volatile boolean running;
	private Thread worker;

	public LogWriter(Context ctx) {
		helper = new LogDatabase(ctx.getApplicationContext());
		queue = new LinkedBlockingQueue<LogEntry>(5000);
	}

	public void start() {
		if (worker != null) return;
		running = true;
		worker = new Thread(this, "LogWriter");
		worker.start();
	}

	public void stop() {
		running = false;
		if (worker != null) worker.interrupt();
	}

	public boolean enqueue(LogEntry e) {
		if (!running) return false;
		return queue.offer(sanitize(e));
	}

	public void insertImmediate(LogEntry e) {
		// Synchronous insert for crash handler path
		SQLiteDatabase db = null;
		try {
			db = helper.getWritableDatabase();
			db.beginTransaction();
			SQLiteStatement st = db.compileStatement(
					"INSERT INTO logs(ts,pid,tid,level,tag,message,buffer,source,app_version) VALUES(?,?,?,?,?,?,?,?,?)");
			bind(st, sanitize(e));
			st.executeInsert();
			db.setTransactionSuccessful();
		} catch (Throwable ignored) {
		} finally {
			if (db != null) try { db.endTransaction(); } catch (Throwable ignored) {}
		}
	}

	@Override
	public void run() {
		ArrayList<LogEntry> batch = new ArrayList<LogEntry>(128);
		while (running) {
			try {
				LogEntry first = queue.poll(200, java.util.concurrent.TimeUnit.MILLISECONDS);
				if (first != null) {
					batch.clear();
					batch.add(first);
					queue.drainTo(batch, 127);
					insertBatch(batch);
				}
			} catch (InterruptedException ignored) {
			} catch (Throwable ignored) {
				// swallow and continue
			}
		}
	}

	private void insertBatch(List<LogEntry> items) throws SQLiteException {
		if (items == null || items.isEmpty()) return;
		SQLiteDatabase db = helper.getWritableDatabase();
		db.beginTransaction();
		SQLiteStatement st = db.compileStatement(
				"INSERT INTO logs(ts,pid,tid,level,tag,message,buffer,source,app_version) VALUES(?,?,?,?,?,?,?,?,?)");
		for (LogEntry e : items) {
			bind(st, e);
			st.executeInsert();
		}
		db.setTransactionSuccessful();
		db.endTransaction();
	}

	private static void bind(SQLiteStatement st, LogEntry e) {
		st.clearBindings();
		st.bindLong(1, e.ts);
		st.bindLong(2, e.pid);
		st.bindLong(3, e.tid);
		st.bindLong(4, e.level);
		if (e.tag != null) st.bindString(5, e.tag); else st.bindNull(5);
		if (e.message != null) st.bindString(6, e.message); else st.bindNull(6);
		if (e.buffer != null) st.bindString(7, e.buffer); else st.bindNull(7);
		if (e.source != null) st.bindString(8, e.source); else st.bindNull(8);
		if (e.appVersion != null) st.bindString(9, e.appVersion); else st.bindNull(9);
	}

	private static LogEntry sanitize(LogEntry e) {
		if (e == null) return null;
		if (e.message != null && e.message.length() > 16384)
			e.message = e.message.substring(0, 16384);
		if (e.tag != null && e.tag.length() > 64)
			e.tag = e.tag.substring(0, 64);
		if (e.buffer != null && e.buffer.length() > 16)
			e.buffer = e.buffer.substring(0, 16);
		return e;
	}

	public String databasePath(Context ctx) {
		return LogDatabase.getDatabasePath(ctx);
	}
}

