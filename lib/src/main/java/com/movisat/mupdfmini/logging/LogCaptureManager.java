package com.movisat.mupdfmini.logging;

import android.content.Context;

public final class LogCaptureManager
{
	private static volatile boolean initialized;
	private static LogWriter writer;
	private static LogcatReader mainReader;
	private static LogcatReader crashReader;
	private static String appVersion;

	private LogCaptureManager() {}

	public static synchronized void init(Context ctx, String versionName) {
		if (initialized || ctx == null) return;
		appVersion = versionName;
		writer = new LogWriter(ctx);
		writer.start();

        mainReader = new LogcatReader(ctx, "main", writer, appVersion);
        crashReader = new LogcatReader(ctx, "crash", writer, appVersion);
        mainReader.start();
        crashReader.start();

		installCrashHandler();
		initialized = true;
	}

	public static synchronized void shutdown() {
		if (!initialized) return;
		try { if (mainReader != null) mainReader.stop(); } catch (Throwable ignored) {}
		try { if (crashReader != null) crashReader.stop(); } catch (Throwable ignored) {}
		try { if (writer != null) writer.stop(); } catch (Throwable ignored) {}
		initialized = false;
	}

	private static void installCrashHandler() {
		final Thread.UncaughtExceptionHandler prev = Thread.getDefaultUncaughtExceptionHandler();
		Thread.setDefaultUncaughtExceptionHandler(new Thread.UncaughtExceptionHandler() {
			@Override public void uncaughtException(Thread t, Throwable e) {
				try {
					if (writer != null) {
						LogEntry entry = new LogEntry();
						entry.ts = System.currentTimeMillis();
						entry.pid = android.os.Process.myPid();
						entry.tid = (int) t.getId();
						entry.level = 7;
						entry.tag = "Uncaught";
						entry.buffer = "crash";
						entry.source = "crash-handler";
						entry.appVersion = appVersion;
						entry.message = stackTrace(e);
						writer.insertImmediate(entry);
					}
				} catch (Throwable ignored) {}
				if (prev != null) prev.uncaughtException(t, e);
			}
		});
	}

	private static String stackTrace(Throwable e) {
		try {
			java.io.StringWriter sw = new java.io.StringWriter();
			java.io.PrintWriter pw = new java.io.PrintWriter(sw);
			e.printStackTrace(pw);
			pw.flush();
			return sw.toString();
		} catch (Throwable t) {
			return String.valueOf(e);
		}
	}

    // Database path is the app's default DB directory:
    // /data/data/com.movisat.mupdfmini.app/databases/log.db
}
