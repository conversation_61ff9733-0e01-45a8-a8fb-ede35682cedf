<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
		package="com.movisat.mupdfmini">
	
	<!-- PERMISOS EXISTENTES -->
	<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
	
	<!-- PERMISOS CRÍTICOS PARA BLOQUEO TOTAL -->
	<uses-permission android:name="android.permission.INJECT_EVENTS" />
	<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
	<uses-permission android:name="android.permission.WRITE_SETTINGS" />
	<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
	<uses-permission android:name="android.permission.READ_PHONE_STATE" />
	<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
	<uses-permission android:name="android.permission.INTERNET" />
	<uses-permission android:name="android.permission.VIBRATE" />
	<uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
	<uses-permission android:name="android.permission.WAKE_LOCK" />
	<application>
		<activity android:name=".DocumentActivity"
				android:label="MuPDF mini"
				android:configChanges="orientation|screenSize|keyboardHidden"
				android:hardwareAccelerated="false"
				android:exported="true"
				>
			<intent-filter>
				<action android:name="android.intent.action.VIEW" />
				<category android:name="android.intent.category.BROWSABLE" />
				<category android:name="android.intent.category.DEFAULT" />
				<!-- list the mime-types we know about -->
				<data android:mimeType="application/pdf" />
				<data android:mimeType="application/vnd.ms-xpsdocument" />
				<data android:mimeType="application/oxps" />
				<data android:mimeType="application/vnd.comicbook+zip" />
				<data android:mimeType="application/x-cbz" />
				<data android:mimeType="application/epub+zip" />
				<data android:mimeType="application/x-fictionbook" />
				<data android:mimeType="application/x-mobipocket-ebook" />
				<!-- list application/octet-stream to catch the ones android doesn't recognize -->
				<data android:mimeType="application/octet-stream" />
			</intent-filter>
		</activity>
		<activity android:name=".OutlineActivity"
				android:configChanges="orientation|screenSize|keyboardHidden"
				>
		</activity>
		
		<!-- RECEIVER PARA DEVICE ADMIN -->
		<receiver
			android:name=".AdminReceiver"
            android:exported="true"
            android:label="MuPDF Mini Admin"
			android:permission="android.permission.BIND_DEVICE_ADMIN">
			<meta-data
				android:name="android.app.device_admin"
				android:resource="@xml/device_admin" />
			<intent-filter>
				<action android:name="android.app.action.DEVICE_ADMIN_ENABLED" />
			</intent-filter>
		</receiver>
		
	</application>
</manifest>
