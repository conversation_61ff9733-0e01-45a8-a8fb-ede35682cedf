/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_Archive */

#ifndef _Included_com_artifex_mupdf_fitz_Archive
#define _Included_com_artifex_mupdf_fitz_Archive
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_artifex_mupdf_fitz_Archive
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Archive_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Archive
 * Method:    newNativeArchive
 * Signature: (Ljava/lang/String;)J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_Archive_newNativeArchive
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_Archive
 * Method:    getFormat
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_Archive_getFormat
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Archive
 * Method:    countEntries
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Archive_countEntries
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Archive
 * Method:    listEntry
 * Signature: (I)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_Archive_listEntry
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Archive
 * Method:    hasEntry
 * Signature: (Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_Archive_hasEntry
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_Archive
 * Method:    readEntry
 * Signature: (Ljava/lang/String;)Lcom/artifex/mupdf/fitz/Buffer;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Archive_readEntry
  (JNIEnv *, jobject, jstring);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_BarcodeInfo */

#ifndef _Included_com_artifex_mupdf_fitz_BarcodeInfo
#define _Included_com_artifex_mupdf_fitz_BarcodeInfo
#ifdef __cplusplus
extern "C" {
#endif
#undef com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_NONE
#define com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_NONE 0L
#undef com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_AZTEC
#define com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_AZTEC 1L
#undef com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_CODABAR
#define com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_CODABAR 2L
#undef com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_CODE39
#define com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_CODE39 3L
#undef com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_CODE93
#define com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_CODE93 4L
#undef com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_CODE128
#define com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_CODE128 5L
#undef com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_DATABAR
#define com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_DATABAR 6L
#undef com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_DATABAREXPANDED
#define com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_DATABAREXPANDED 7L
#undef com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_DATAMATRIX
#define com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_DATAMATRIX 8L
#undef com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_EAN8
#define com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_EAN8 9L
#undef com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_EAN13
#define com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_EAN13 10L
#undef com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_ITF
#define com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_ITF 11L
#undef com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_MAXICODE
#define com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_MAXICODE 12L
#undef com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_PDF417
#define com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_PDF417 13L
#undef com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_QRCODE
#define com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_QRCODE 14L
#undef com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_UPCA
#define com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_UPCA 15L
#undef com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_UPCE
#define com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_UPCE 16L
#undef com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_MICROQRCODE
#define com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_MICROQRCODE 17L
#undef com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_RMQRCODE
#define com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_RMQRCODE 18L
#undef com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_DXFILMEDGE
#define com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_DXFILMEDGE 19L
#undef com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_DATABARLIMITED
#define com_artifex_mupdf_fitz_BarcodeInfo_BARCODE_DATABARLIMITED 20L
/*
 * Class:     com_artifex_mupdf_fitz_BarcodeInfo
 * Method:    toString
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_BarcodeInfo_toString
  (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_Buffer */

#ifndef _Included_com_artifex_mupdf_fitz_Buffer
#define _Included_com_artifex_mupdf_fitz_Buffer
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_artifex_mupdf_fitz_Buffer
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Buffer_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Buffer
 * Method:    newNativeBuffer
 * Signature: (I)J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_Buffer_newNativeBuffer
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Buffer
 * Method:    getLength
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Buffer_getLength
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Buffer
 * Method:    readByte
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Buffer_readByte
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Buffer
 * Method:    readBytes
 * Signature: (I[B)I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Buffer_readBytes
  (JNIEnv *, jobject, jint, jbyteArray);

/*
 * Class:     com_artifex_mupdf_fitz_Buffer
 * Method:    readBytesInto
 * Signature: (I[BII)I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Buffer_readBytesInto
  (JNIEnv *, jobject, jint, jbyteArray, jint, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Buffer
 * Method:    writeByte
 * Signature: (B)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Buffer_writeByte
  (JNIEnv *, jobject, jbyte);

/*
 * Class:     com_artifex_mupdf_fitz_Buffer
 * Method:    writeBytes
 * Signature: ([B)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Buffer_writeBytes
  (JNIEnv *, jobject, jbyteArray);

/*
 * Class:     com_artifex_mupdf_fitz_Buffer
 * Method:    writeBytesFrom
 * Signature: ([BII)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Buffer_writeBytesFrom
  (JNIEnv *, jobject, jbyteArray, jint, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Buffer
 * Method:    writeBuffer
 * Signature: (Lcom/artifex/mupdf/fitz/Buffer;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Buffer_writeBuffer
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Buffer
 * Method:    writeRune
 * Signature: (I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Buffer_writeRune
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Buffer
 * Method:    writeLine
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Buffer_writeLine
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_Buffer
 * Method:    writeLines
 * Signature: ([Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Buffer_writeLines
  (JNIEnv *, jobject, jobjectArray);

/*
 * Class:     com_artifex_mupdf_fitz_Buffer
 * Method:    slice
 * Signature: (II)Lcom/artifex/mupdf/fitz/Buffer;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Buffer_slice
  (JNIEnv *, jobject, jint, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Buffer
 * Method:    save
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Buffer_save
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_Buffer
 * Method:    asString
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_Buffer_asString
  (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_ColorSpace */

#ifndef _Included_com_artifex_mupdf_fitz_ColorSpace
#define _Included_com_artifex_mupdf_fitz_ColorSpace
#ifdef __cplusplus
extern "C" {
#endif
#undef com_artifex_mupdf_fitz_ColorSpace_NONE
#define com_artifex_mupdf_fitz_ColorSpace_NONE 0L
#undef com_artifex_mupdf_fitz_ColorSpace_GRAY
#define com_artifex_mupdf_fitz_ColorSpace_GRAY 1L
#undef com_artifex_mupdf_fitz_ColorSpace_RGB
#define com_artifex_mupdf_fitz_ColorSpace_RGB 2L
#undef com_artifex_mupdf_fitz_ColorSpace_BGR
#define com_artifex_mupdf_fitz_ColorSpace_BGR 3L
#undef com_artifex_mupdf_fitz_ColorSpace_CMYK
#define com_artifex_mupdf_fitz_ColorSpace_CMYK 4L
#undef com_artifex_mupdf_fitz_ColorSpace_LAB
#define com_artifex_mupdf_fitz_ColorSpace_LAB 5L
#undef com_artifex_mupdf_fitz_ColorSpace_INDEXED
#define com_artifex_mupdf_fitz_ColorSpace_INDEXED 6L
#undef com_artifex_mupdf_fitz_ColorSpace_SEPARATION
#define com_artifex_mupdf_fitz_ColorSpace_SEPARATION 7L
/*
 * Class:     com_artifex_mupdf_fitz_ColorSpace
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_ColorSpace_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_ColorSpace
 * Method:    newNativeColorSpace
 * Signature: (Ljava/lang/String;Lcom/artifex/mupdf/fitz/Buffer;)J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_ColorSpace_newNativeColorSpace
  (JNIEnv *, jobject, jstring, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_ColorSpace
 * Method:    nativeDeviceGray
 * Signature: ()J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_ColorSpace_nativeDeviceGray
  (JNIEnv *, jclass);

/*
 * Class:     com_artifex_mupdf_fitz_ColorSpace
 * Method:    nativeDeviceRGB
 * Signature: ()J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_ColorSpace_nativeDeviceRGB
  (JNIEnv *, jclass);

/*
 * Class:     com_artifex_mupdf_fitz_ColorSpace
 * Method:    nativeDeviceBGR
 * Signature: ()J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_ColorSpace_nativeDeviceBGR
  (JNIEnv *, jclass);

/*
 * Class:     com_artifex_mupdf_fitz_ColorSpace
 * Method:    nativeDeviceCMYK
 * Signature: ()J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_ColorSpace_nativeDeviceCMYK
  (JNIEnv *, jclass);

/*
 * Class:     com_artifex_mupdf_fitz_ColorSpace
 * Method:    getNumberOfComponents
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_ColorSpace_getNumberOfComponents
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_ColorSpace
 * Method:    toString
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_ColorSpace_toString
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_ColorSpace
 * Method:    isGray
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_ColorSpace_isGray
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_ColorSpace
 * Method:    isRGB
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_ColorSpace_isRGB
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_ColorSpace
 * Method:    isCMYK
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_ColorSpace_isCMYK
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_ColorSpace
 * Method:    isIndexed
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_ColorSpace_isIndexed
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_ColorSpace
 * Method:    isLab
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_ColorSpace_isLab
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_ColorSpace
 * Method:    isDeviceN
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_ColorSpace_isDeviceN
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_ColorSpace
 * Method:    isSubtractive
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_ColorSpace_isSubtractive
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_ColorSpace
 * Method:    getType
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_ColorSpace_getType
  (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_Context */

#ifndef _Included_com_artifex_mupdf_fitz_Context
#define _Included_com_artifex_mupdf_fitz_Context
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_artifex_mupdf_fitz_Context
 * Method:    initNative
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Context_initNative
  (JNIEnv *, jclass);

/*
 * Class:     com_artifex_mupdf_fitz_Context
 * Method:    emptyStore
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Context_emptyStore
  (JNIEnv *, jclass);

/*
 * Class:     com_artifex_mupdf_fitz_Context
 * Method:    shrinkStore
 * Signature: (I)Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_Context_shrinkStore
  (JNIEnv *, jclass, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Context
 * Method:    enableICC
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Context_enableICC
  (JNIEnv *, jclass);

/*
 * Class:     com_artifex_mupdf_fitz_Context
 * Method:    disableICC
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Context_disableICC
  (JNIEnv *, jclass);

/*
 * Class:     com_artifex_mupdf_fitz_Context
 * Method:    setAntiAliasLevel
 * Signature: (I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Context_setAntiAliasLevel
  (JNIEnv *, jclass, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Context
 * Method:    setUserCSS
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Context_setUserCSS
  (JNIEnv *, jclass, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_Context
 * Method:    useDocumentCSS
 * Signature: (Z)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Context_useDocumentCSS
  (JNIEnv *, jclass, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_Context
 * Method:    getVersion
 * Signature: ()Lcom/artifex/mupdf/fitz/Context/Version;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Context_getVersion
  (JNIEnv *, jclass);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_Cookie */

#ifndef _Included_com_artifex_mupdf_fitz_Cookie
#define _Included_com_artifex_mupdf_fitz_Cookie
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_artifex_mupdf_fitz_Cookie
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Cookie_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Cookie
 * Method:    newNative
 * Signature: ()J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_Cookie_newNative
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Cookie
 * Method:    abort
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Cookie_abort
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Cookie
 * Method:    getProgress
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Cookie_getProgress
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Cookie
 * Method:    getProgressMax
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Cookie_getProgressMax
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Cookie
 * Method:    getErrors
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Cookie_getErrors
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Cookie
 * Method:    getIncomplete
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_Cookie_getIncomplete
  (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_DOM */

#ifndef _Included_com_artifex_mupdf_fitz_DOM
#define _Included_com_artifex_mupdf_fitz_DOM
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_artifex_mupdf_fitz_DOM
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_DOM_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DOM
 * Method:    body
 * Signature: ()Lcom/artifex/mupdf/fitz/DOM;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_DOM_body
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DOM
 * Method:    document
 * Signature: ()Lcom/artifex/mupdf/fitz/DOM;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_DOM_document
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DOM
 * Method:    createTextNode
 * Signature: (Ljava/lang/String;)Lcom/artifex/mupdf/fitz/DOM;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_DOM_createTextNode
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_DOM
 * Method:    createElement
 * Signature: (Ljava/lang/String;)Lcom/artifex/mupdf/fitz/DOM;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_DOM_createElement
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_DOM
 * Method:    insertBefore
 * Signature: (Lcom/artifex/mupdf/fitz/DOM;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_DOM_insertBefore
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DOM
 * Method:    insertAfter
 * Signature: (Lcom/artifex/mupdf/fitz/DOM;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_DOM_insertAfter
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DOM
 * Method:    appendChild
 * Signature: (Lcom/artifex/mupdf/fitz/DOM;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_DOM_appendChild
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DOM
 * Method:    remove
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_DOM_remove
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DOM
 * Method:    clone
 * Signature: ()Lcom/artifex/mupdf/fitz/DOM;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_DOM_clone
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DOM
 * Method:    parent
 * Signature: ()Lcom/artifex/mupdf/fitz/DOM;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_DOM_parent
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DOM
 * Method:    firstChild
 * Signature: ()Lcom/artifex/mupdf/fitz/DOM;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_DOM_firstChild
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DOM
 * Method:    next
 * Signature: ()Lcom/artifex/mupdf/fitz/DOM;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_DOM_next
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DOM
 * Method:    previous
 * Signature: ()Lcom/artifex/mupdf/fitz/DOM;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_DOM_previous
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DOM
 * Method:    addAttribute
 * Signature: (Ljava/lang/String;Ljava/lang/String;)Lcom/artifex/mupdf/fitz/DOM;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_DOM_addAttribute
  (JNIEnv *, jobject, jstring, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_DOM
 * Method:    removeAttribute
 * Signature: (Ljava/lang/String;)Lcom/artifex/mupdf/fitz/DOM;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_DOM_removeAttribute
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_DOM
 * Method:    attribute
 * Signature: (Ljava/lang/String;)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_DOM_attribute
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_DOM
 * Method:    attributes
 * Signature: ()[Lcom/artifex/mupdf/fitz/DOM/DOMAttribute;
 */
JNIEXPORT jobjectArray JNICALL Java_com_artifex_mupdf_fitz_DOM_attributes
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DOM
 * Method:    find
 * Signature: (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lcom/artifex/mupdf/fitz/DOM;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_DOM_find
  (JNIEnv *, jobject, jstring, jstring, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_DOM
 * Method:    findNext
 * Signature: (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Lcom/artifex/mupdf/fitz/DOM;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_DOM_findNext
  (JNIEnv *, jobject, jstring, jstring, jstring);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_DefaultColorSpaces */

#ifndef _Included_com_artifex_mupdf_fitz_DefaultColorSpaces
#define _Included_com_artifex_mupdf_fitz_DefaultColorSpaces
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_artifex_mupdf_fitz_DefaultColorSpaces
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_DefaultColorSpaces_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DefaultColorSpaces
 * Method:    setDefaultGray
 * Signature: (Lcom/artifex/mupdf/fitz/ColorSpace;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_DefaultColorSpaces_setDefaultGray
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DefaultColorSpaces
 * Method:    setDefaultRGB
 * Signature: (Lcom/artifex/mupdf/fitz/ColorSpace;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_DefaultColorSpaces_setDefaultRGB
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DefaultColorSpaces
 * Method:    setDefaultCMYK
 * Signature: (Lcom/artifex/mupdf/fitz/ColorSpace;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_DefaultColorSpaces_setDefaultCMYK
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DefaultColorSpaces
 * Method:    setOutputIntent
 * Signature: (Lcom/artifex/mupdf/fitz/ColorSpace;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_DefaultColorSpaces_setOutputIntent
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DefaultColorSpaces
 * Method:    getDefaultGray
 * Signature: ()Lcom/artifex/mupdf/fitz/ColorSpace;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_DefaultColorSpaces_getDefaultGray
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DefaultColorSpaces
 * Method:    getDefaultRGB
 * Signature: ()Lcom/artifex/mupdf/fitz/ColorSpace;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_DefaultColorSpaces_getDefaultRGB
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DefaultColorSpaces
 * Method:    getDefaultCMYK
 * Signature: ()Lcom/artifex/mupdf/fitz/ColorSpace;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_DefaultColorSpaces_getDefaultCMYK
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DefaultColorSpaces
 * Method:    getOutputIntent
 * Signature: ()Lcom/artifex/mupdf/fitz/ColorSpace;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_DefaultColorSpaces_getOutputIntent
  (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_Device */

#ifndef _Included_com_artifex_mupdf_fitz_Device
#define _Included_com_artifex_mupdf_fitz_Device
#ifdef __cplusplus
extern "C" {
#endif
#undef com_artifex_mupdf_fitz_Device_BLEND_NORMAL
#define com_artifex_mupdf_fitz_Device_BLEND_NORMAL 0L
#undef com_artifex_mupdf_fitz_Device_BLEND_MULTIPLY
#define com_artifex_mupdf_fitz_Device_BLEND_MULTIPLY 1L
#undef com_artifex_mupdf_fitz_Device_BLEND_SCREEN
#define com_artifex_mupdf_fitz_Device_BLEND_SCREEN 2L
#undef com_artifex_mupdf_fitz_Device_BLEND_OVERLAY
#define com_artifex_mupdf_fitz_Device_BLEND_OVERLAY 3L
#undef com_artifex_mupdf_fitz_Device_BLEND_DARKEN
#define com_artifex_mupdf_fitz_Device_BLEND_DARKEN 4L
#undef com_artifex_mupdf_fitz_Device_BLEND_LIGHTEN
#define com_artifex_mupdf_fitz_Device_BLEND_LIGHTEN 5L
#undef com_artifex_mupdf_fitz_Device_BLEND_COLOR_DODGE
#define com_artifex_mupdf_fitz_Device_BLEND_COLOR_DODGE 6L
#undef com_artifex_mupdf_fitz_Device_BLEND_COLOR_BURN
#define com_artifex_mupdf_fitz_Device_BLEND_COLOR_BURN 7L
#undef com_artifex_mupdf_fitz_Device_BLEND_HARD_LIGHT
#define com_artifex_mupdf_fitz_Device_BLEND_HARD_LIGHT 8L
#undef com_artifex_mupdf_fitz_Device_BLEND_SOFT_LIGHT
#define com_artifex_mupdf_fitz_Device_BLEND_SOFT_LIGHT 9L
#undef com_artifex_mupdf_fitz_Device_BLEND_DIFFERENCE
#define com_artifex_mupdf_fitz_Device_BLEND_DIFFERENCE 10L
#undef com_artifex_mupdf_fitz_Device_BLEND_EXCLUSION
#define com_artifex_mupdf_fitz_Device_BLEND_EXCLUSION 11L
#undef com_artifex_mupdf_fitz_Device_BLEND_HUE
#define com_artifex_mupdf_fitz_Device_BLEND_HUE 12L
#undef com_artifex_mupdf_fitz_Device_BLEND_SATURATION
#define com_artifex_mupdf_fitz_Device_BLEND_SATURATION 13L
#undef com_artifex_mupdf_fitz_Device_BLEND_COLOR
#define com_artifex_mupdf_fitz_Device_BLEND_COLOR 14L
#undef com_artifex_mupdf_fitz_Device_BLEND_LUMINOSITY
#define com_artifex_mupdf_fitz_Device_BLEND_LUMINOSITY 15L
#undef com_artifex_mupdf_fitz_Device_DEVICE_FLAG_MASK
#define com_artifex_mupdf_fitz_Device_DEVICE_FLAG_MASK 1L
#undef com_artifex_mupdf_fitz_Device_DEVICE_FLAG_COLOR
#define com_artifex_mupdf_fitz_Device_DEVICE_FLAG_COLOR 2L
#undef com_artifex_mupdf_fitz_Device_DEVICE_FLAG_UNCACHEABLE
#define com_artifex_mupdf_fitz_Device_DEVICE_FLAG_UNCACHEABLE 4L
#undef com_artifex_mupdf_fitz_Device_DEVICE_FLAG_FILLCOLOR_UNDEFINED
#define com_artifex_mupdf_fitz_Device_DEVICE_FLAG_FILLCOLOR_UNDEFINED 8L
#undef com_artifex_mupdf_fitz_Device_DEVICE_FLAG_STROKECOLOR_UNDEFINED
#define com_artifex_mupdf_fitz_Device_DEVICE_FLAG_STROKECOLOR_UNDEFINED 16L
#undef com_artifex_mupdf_fitz_Device_DEVICE_FLAG_STARTCAP_UNDEFINED
#define com_artifex_mupdf_fitz_Device_DEVICE_FLAG_STARTCAP_UNDEFINED 32L
#undef com_artifex_mupdf_fitz_Device_DEVICE_FLAG_DASHCAP_UNDEFINED
#define com_artifex_mupdf_fitz_Device_DEVICE_FLAG_DASHCAP_UNDEFINED 64L
#undef com_artifex_mupdf_fitz_Device_DEVICE_FLAG_ENDCAP_UNDEFINED
#define com_artifex_mupdf_fitz_Device_DEVICE_FLAG_ENDCAP_UNDEFINED 128L
#undef com_artifex_mupdf_fitz_Device_DEVICE_FLAG_LINEJOIN_UNDEFINED
#define com_artifex_mupdf_fitz_Device_DEVICE_FLAG_LINEJOIN_UNDEFINED 256L
#undef com_artifex_mupdf_fitz_Device_DEVICE_FLAG_MITERLIMIT_UNDEFINED
#define com_artifex_mupdf_fitz_Device_DEVICE_FLAG_MITERLIMIT_UNDEFINED 512L
#undef com_artifex_mupdf_fitz_Device_DEVICE_FLAG_LINEWIDTH_UNDEFINED
#define com_artifex_mupdf_fitz_Device_DEVICE_FLAG_LINEWIDTH_UNDEFINED 1024L
#undef com_artifex_mupdf_fitz_Device_DEVICE_FLAG_BBOX_DEFINED
#define com_artifex_mupdf_fitz_Device_DEVICE_FLAG_BBOX_DEFINED 2048L
#undef com_artifex_mupdf_fitz_Device_DEVICE_FLAG_GRIDFIT_AS_TILED
#define com_artifex_mupdf_fitz_Device_DEVICE_FLAG_GRIDFIT_AS_TILED 4096L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_INVALID
#define com_artifex_mupdf_fitz_Device_STRUCTURE_INVALID -1L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_DOCUMENT
#define com_artifex_mupdf_fitz_Device_STRUCTURE_DOCUMENT 0L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_PART
#define com_artifex_mupdf_fitz_Device_STRUCTURE_PART 1L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_ART
#define com_artifex_mupdf_fitz_Device_STRUCTURE_ART 2L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_SECT
#define com_artifex_mupdf_fitz_Device_STRUCTURE_SECT 3L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_DIV
#define com_artifex_mupdf_fitz_Device_STRUCTURE_DIV 4L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_BLOCKQUOTE
#define com_artifex_mupdf_fitz_Device_STRUCTURE_BLOCKQUOTE 5L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_CAPTION
#define com_artifex_mupdf_fitz_Device_STRUCTURE_CAPTION 6L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_TOC
#define com_artifex_mupdf_fitz_Device_STRUCTURE_TOC 7L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_TOCI
#define com_artifex_mupdf_fitz_Device_STRUCTURE_TOCI 8L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_INDEX
#define com_artifex_mupdf_fitz_Device_STRUCTURE_INDEX 9L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_NONSTRUCT
#define com_artifex_mupdf_fitz_Device_STRUCTURE_NONSTRUCT 10L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_PRIVATE
#define com_artifex_mupdf_fitz_Device_STRUCTURE_PRIVATE 11L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_DOCUMENTFRAGMENT
#define com_artifex_mupdf_fitz_Device_STRUCTURE_DOCUMENTFRAGMENT 12L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_ASIDE
#define com_artifex_mupdf_fitz_Device_STRUCTURE_ASIDE 13L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_TITLE
#define com_artifex_mupdf_fitz_Device_STRUCTURE_TITLE 14L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_FENOTE
#define com_artifex_mupdf_fitz_Device_STRUCTURE_FENOTE 15L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_SUB
#define com_artifex_mupdf_fitz_Device_STRUCTURE_SUB 16L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_P
#define com_artifex_mupdf_fitz_Device_STRUCTURE_P 17L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_H
#define com_artifex_mupdf_fitz_Device_STRUCTURE_H 18L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_H1
#define com_artifex_mupdf_fitz_Device_STRUCTURE_H1 19L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_H2
#define com_artifex_mupdf_fitz_Device_STRUCTURE_H2 20L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_H3
#define com_artifex_mupdf_fitz_Device_STRUCTURE_H3 21L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_H4
#define com_artifex_mupdf_fitz_Device_STRUCTURE_H4 22L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_H5
#define com_artifex_mupdf_fitz_Device_STRUCTURE_H5 23L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_H6
#define com_artifex_mupdf_fitz_Device_STRUCTURE_H6 24L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_LIST
#define com_artifex_mupdf_fitz_Device_STRUCTURE_LIST 25L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_LISTITEM
#define com_artifex_mupdf_fitz_Device_STRUCTURE_LISTITEM 26L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_LABEL
#define com_artifex_mupdf_fitz_Device_STRUCTURE_LABEL 27L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_LISTBODY
#define com_artifex_mupdf_fitz_Device_STRUCTURE_LISTBODY 28L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_TABLE
#define com_artifex_mupdf_fitz_Device_STRUCTURE_TABLE 29L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_TR
#define com_artifex_mupdf_fitz_Device_STRUCTURE_TR 30L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_TH
#define com_artifex_mupdf_fitz_Device_STRUCTURE_TH 31L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_TD
#define com_artifex_mupdf_fitz_Device_STRUCTURE_TD 32L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_THEAD
#define com_artifex_mupdf_fitz_Device_STRUCTURE_THEAD 33L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_TBODY
#define com_artifex_mupdf_fitz_Device_STRUCTURE_TBODY 34L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_TFOOT
#define com_artifex_mupdf_fitz_Device_STRUCTURE_TFOOT 35L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_SPAN
#define com_artifex_mupdf_fitz_Device_STRUCTURE_SPAN 36L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_QUOTE
#define com_artifex_mupdf_fitz_Device_STRUCTURE_QUOTE 37L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_NOTE
#define com_artifex_mupdf_fitz_Device_STRUCTURE_NOTE 38L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_REFERENCE
#define com_artifex_mupdf_fitz_Device_STRUCTURE_REFERENCE 39L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_BIBENTRY
#define com_artifex_mupdf_fitz_Device_STRUCTURE_BIBENTRY 40L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_CODE
#define com_artifex_mupdf_fitz_Device_STRUCTURE_CODE 41L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_LINK
#define com_artifex_mupdf_fitz_Device_STRUCTURE_LINK 42L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_ANNOT
#define com_artifex_mupdf_fitz_Device_STRUCTURE_ANNOT 43L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_EM
#define com_artifex_mupdf_fitz_Device_STRUCTURE_EM 44L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_STRONG
#define com_artifex_mupdf_fitz_Device_STRUCTURE_STRONG 45L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_RUBY
#define com_artifex_mupdf_fitz_Device_STRUCTURE_RUBY 46L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_RB
#define com_artifex_mupdf_fitz_Device_STRUCTURE_RB 47L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_RT
#define com_artifex_mupdf_fitz_Device_STRUCTURE_RT 48L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_RP
#define com_artifex_mupdf_fitz_Device_STRUCTURE_RP 49L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_WARICHU
#define com_artifex_mupdf_fitz_Device_STRUCTURE_WARICHU 50L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_WT
#define com_artifex_mupdf_fitz_Device_STRUCTURE_WT 51L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_WP
#define com_artifex_mupdf_fitz_Device_STRUCTURE_WP 52L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_FIGURE
#define com_artifex_mupdf_fitz_Device_STRUCTURE_FIGURE 53L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_FORMULA
#define com_artifex_mupdf_fitz_Device_STRUCTURE_FORMULA 54L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_FORM
#define com_artifex_mupdf_fitz_Device_STRUCTURE_FORM 55L
#undef com_artifex_mupdf_fitz_Device_STRUCTURE_ARTIFACT
#define com_artifex_mupdf_fitz_Device_STRUCTURE_ARTIFACT 56L
#undef com_artifex_mupdf_fitz_Device_METATEXT_ACTUALTEXT
#define com_artifex_mupdf_fitz_Device_METATEXT_ACTUALTEXT 0L
#undef com_artifex_mupdf_fitz_Device_METATEXT_ALT
#define com_artifex_mupdf_fitz_Device_METATEXT_ALT 1L
#undef com_artifex_mupdf_fitz_Device_METATEXT_ABBREVIATION
#define com_artifex_mupdf_fitz_Device_METATEXT_ABBREVIATION 2L
#undef com_artifex_mupdf_fitz_Device_METATEXT_TITLE
#define com_artifex_mupdf_fitz_Device_METATEXT_TITLE 3L
/*
 * Class:     com_artifex_mupdf_fitz_Device
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Device_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Device
 * Method:    newNative
 * Signature: ()J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_Device_newNative
  (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_DisplayList */

#ifndef _Included_com_artifex_mupdf_fitz_DisplayList
#define _Included_com_artifex_mupdf_fitz_DisplayList
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_artifex_mupdf_fitz_DisplayList
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_DisplayList_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DisplayList
 * Method:    newNative
 * Signature: (Lcom/artifex/mupdf/fitz/Rect;)J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_DisplayList_newNative
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DisplayList
 * Method:    getBounds
 * Signature: ()Lcom/artifex/mupdf/fitz/Rect;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_DisplayList_getBounds
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DisplayList
 * Method:    toPixmap
 * Signature: (Lcom/artifex/mupdf/fitz/Matrix;Lcom/artifex/mupdf/fitz/ColorSpace;Z)Lcom/artifex/mupdf/fitz/Pixmap;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_DisplayList_toPixmap
  (JNIEnv *, jobject, jobject, jobject, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_DisplayList
 * Method:    toStructuredText
 * Signature: (Ljava/lang/String;)Lcom/artifex/mupdf/fitz/StructuredText;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_DisplayList_toStructuredText
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_DisplayList
 * Method:    search
 * Signature: (Ljava/lang/String;)[[Lcom/artifex/mupdf/fitz/Quad;
 */
JNIEXPORT jobjectArray JNICALL Java_com_artifex_mupdf_fitz_DisplayList_search
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_DisplayList
 * Method:    run
 * Signature: (Lcom/artifex/mupdf/fitz/Device;Lcom/artifex/mupdf/fitz/Matrix;Lcom/artifex/mupdf/fitz/Rect;Lcom/artifex/mupdf/fitz/Cookie;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_DisplayList_run
  (JNIEnv *, jobject, jobject, jobject, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DisplayList
 * Method:    decodeBarcode
 * Signature: (Lcom/artifex/mupdf/fitz/Rect;F)Lcom/artifex/mupdf/fitz/BarcodeInfo;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_DisplayList_decodeBarcode
  (JNIEnv *, jobject, jobject, jfloat);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_DisplayListDevice */

#ifndef _Included_com_artifex_mupdf_fitz_DisplayListDevice
#define _Included_com_artifex_mupdf_fitz_DisplayListDevice
#ifdef __cplusplus
extern "C" {
#endif
#undef com_artifex_mupdf_fitz_DisplayListDevice_BLEND_NORMAL
#define com_artifex_mupdf_fitz_DisplayListDevice_BLEND_NORMAL 0L
#undef com_artifex_mupdf_fitz_DisplayListDevice_BLEND_MULTIPLY
#define com_artifex_mupdf_fitz_DisplayListDevice_BLEND_MULTIPLY 1L
#undef com_artifex_mupdf_fitz_DisplayListDevice_BLEND_SCREEN
#define com_artifex_mupdf_fitz_DisplayListDevice_BLEND_SCREEN 2L
#undef com_artifex_mupdf_fitz_DisplayListDevice_BLEND_OVERLAY
#define com_artifex_mupdf_fitz_DisplayListDevice_BLEND_OVERLAY 3L
#undef com_artifex_mupdf_fitz_DisplayListDevice_BLEND_DARKEN
#define com_artifex_mupdf_fitz_DisplayListDevice_BLEND_DARKEN 4L
#undef com_artifex_mupdf_fitz_DisplayListDevice_BLEND_LIGHTEN
#define com_artifex_mupdf_fitz_DisplayListDevice_BLEND_LIGHTEN 5L
#undef com_artifex_mupdf_fitz_DisplayListDevice_BLEND_COLOR_DODGE
#define com_artifex_mupdf_fitz_DisplayListDevice_BLEND_COLOR_DODGE 6L
#undef com_artifex_mupdf_fitz_DisplayListDevice_BLEND_COLOR_BURN
#define com_artifex_mupdf_fitz_DisplayListDevice_BLEND_COLOR_BURN 7L
#undef com_artifex_mupdf_fitz_DisplayListDevice_BLEND_HARD_LIGHT
#define com_artifex_mupdf_fitz_DisplayListDevice_BLEND_HARD_LIGHT 8L
#undef com_artifex_mupdf_fitz_DisplayListDevice_BLEND_SOFT_LIGHT
#define com_artifex_mupdf_fitz_DisplayListDevice_BLEND_SOFT_LIGHT 9L
#undef com_artifex_mupdf_fitz_DisplayListDevice_BLEND_DIFFERENCE
#define com_artifex_mupdf_fitz_DisplayListDevice_BLEND_DIFFERENCE 10L
#undef com_artifex_mupdf_fitz_DisplayListDevice_BLEND_EXCLUSION
#define com_artifex_mupdf_fitz_DisplayListDevice_BLEND_EXCLUSION 11L
#undef com_artifex_mupdf_fitz_DisplayListDevice_BLEND_HUE
#define com_artifex_mupdf_fitz_DisplayListDevice_BLEND_HUE 12L
#undef com_artifex_mupdf_fitz_DisplayListDevice_BLEND_SATURATION
#define com_artifex_mupdf_fitz_DisplayListDevice_BLEND_SATURATION 13L
#undef com_artifex_mupdf_fitz_DisplayListDevice_BLEND_COLOR
#define com_artifex_mupdf_fitz_DisplayListDevice_BLEND_COLOR 14L
#undef com_artifex_mupdf_fitz_DisplayListDevice_BLEND_LUMINOSITY
#define com_artifex_mupdf_fitz_DisplayListDevice_BLEND_LUMINOSITY 15L
#undef com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_MASK
#define com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_MASK 1L
#undef com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_COLOR
#define com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_COLOR 2L
#undef com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_UNCACHEABLE
#define com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_UNCACHEABLE 4L
#undef com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_FILLCOLOR_UNDEFINED
#define com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_FILLCOLOR_UNDEFINED 8L
#undef com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_STROKECOLOR_UNDEFINED
#define com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_STROKECOLOR_UNDEFINED 16L
#undef com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_STARTCAP_UNDEFINED
#define com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_STARTCAP_UNDEFINED 32L
#undef com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_DASHCAP_UNDEFINED
#define com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_DASHCAP_UNDEFINED 64L
#undef com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_ENDCAP_UNDEFINED
#define com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_ENDCAP_UNDEFINED 128L
#undef com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_LINEJOIN_UNDEFINED
#define com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_LINEJOIN_UNDEFINED 256L
#undef com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_MITERLIMIT_UNDEFINED
#define com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_MITERLIMIT_UNDEFINED 512L
#undef com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_LINEWIDTH_UNDEFINED
#define com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_LINEWIDTH_UNDEFINED 1024L
#undef com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_BBOX_DEFINED
#define com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_BBOX_DEFINED 2048L
#undef com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_GRIDFIT_AS_TILED
#define com_artifex_mupdf_fitz_DisplayListDevice_DEVICE_FLAG_GRIDFIT_AS_TILED 4096L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_INVALID
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_INVALID -1L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_DOCUMENT
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_DOCUMENT 0L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_PART
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_PART 1L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_ART
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_ART 2L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_SECT
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_SECT 3L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_DIV
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_DIV 4L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_BLOCKQUOTE
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_BLOCKQUOTE 5L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_CAPTION
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_CAPTION 6L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_TOC
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_TOC 7L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_TOCI
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_TOCI 8L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_INDEX
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_INDEX 9L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_NONSTRUCT
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_NONSTRUCT 10L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_PRIVATE
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_PRIVATE 11L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_DOCUMENTFRAGMENT
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_DOCUMENTFRAGMENT 12L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_ASIDE
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_ASIDE 13L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_TITLE
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_TITLE 14L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_FENOTE
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_FENOTE 15L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_SUB
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_SUB 16L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_P
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_P 17L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_H
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_H 18L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_H1
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_H1 19L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_H2
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_H2 20L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_H3
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_H3 21L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_H4
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_H4 22L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_H5
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_H5 23L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_H6
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_H6 24L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_LIST
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_LIST 25L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_LISTITEM
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_LISTITEM 26L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_LABEL
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_LABEL 27L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_LISTBODY
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_LISTBODY 28L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_TABLE
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_TABLE 29L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_TR
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_TR 30L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_TH
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_TH 31L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_TD
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_TD 32L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_THEAD
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_THEAD 33L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_TBODY
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_TBODY 34L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_TFOOT
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_TFOOT 35L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_SPAN
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_SPAN 36L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_QUOTE
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_QUOTE 37L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_NOTE
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_NOTE 38L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_REFERENCE
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_REFERENCE 39L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_BIBENTRY
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_BIBENTRY 40L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_CODE
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_CODE 41L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_LINK
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_LINK 42L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_ANNOT
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_ANNOT 43L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_EM
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_EM 44L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_STRONG
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_STRONG 45L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_RUBY
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_RUBY 46L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_RB
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_RB 47L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_RT
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_RT 48L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_RP
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_RP 49L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_WARICHU
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_WARICHU 50L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_WT
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_WT 51L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_WP
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_WP 52L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_FIGURE
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_FIGURE 53L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_FORMULA
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_FORMULA 54L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_FORM
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_FORM 55L
#undef com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_ARTIFACT
#define com_artifex_mupdf_fitz_DisplayListDevice_STRUCTURE_ARTIFACT 56L
#undef com_artifex_mupdf_fitz_DisplayListDevice_METATEXT_ACTUALTEXT
#define com_artifex_mupdf_fitz_DisplayListDevice_METATEXT_ACTUALTEXT 0L
#undef com_artifex_mupdf_fitz_DisplayListDevice_METATEXT_ALT
#define com_artifex_mupdf_fitz_DisplayListDevice_METATEXT_ALT 1L
#undef com_artifex_mupdf_fitz_DisplayListDevice_METATEXT_ABBREVIATION
#define com_artifex_mupdf_fitz_DisplayListDevice_METATEXT_ABBREVIATION 2L
#undef com_artifex_mupdf_fitz_DisplayListDevice_METATEXT_TITLE
#define com_artifex_mupdf_fitz_DisplayListDevice_METATEXT_TITLE 3L
/*
 * Class:     com_artifex_mupdf_fitz_DisplayListDevice
 * Method:    newNative
 * Signature: (Lcom/artifex/mupdf/fitz/DisplayList;)J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_DisplayListDevice_newNative
  (JNIEnv *, jclass, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_Document */

#ifndef _Included_com_artifex_mupdf_fitz_Document
#define _Included_com_artifex_mupdf_fitz_Document
#ifdef __cplusplus
extern "C" {
#endif
#undef com_artifex_mupdf_fitz_Document_PERMISSION_PRINT
#define com_artifex_mupdf_fitz_Document_PERMISSION_PRINT 112L
#undef com_artifex_mupdf_fitz_Document_PERMISSION_COPY
#define com_artifex_mupdf_fitz_Document_PERMISSION_COPY 99L
#undef com_artifex_mupdf_fitz_Document_PERMISSION_EDIT
#define com_artifex_mupdf_fitz_Document_PERMISSION_EDIT 101L
#undef com_artifex_mupdf_fitz_Document_PERMISSION_ANNOTATE
#define com_artifex_mupdf_fitz_Document_PERMISSION_ANNOTATE 110L
#undef com_artifex_mupdf_fitz_Document_PERMISSION_FORM
#define com_artifex_mupdf_fitz_Document_PERMISSION_FORM 102L
#undef com_artifex_mupdf_fitz_Document_PERMISSION_ACCESSIBILITY
#define com_artifex_mupdf_fitz_Document_PERMISSION_ACCESSIBILITY 121L
#undef com_artifex_mupdf_fitz_Document_PERMISSION_ASSEMBLE
#define com_artifex_mupdf_fitz_Document_PERMISSION_ASSEMBLE 97L
#undef com_artifex_mupdf_fitz_Document_PERMISSION_PRINT_HQ
#define com_artifex_mupdf_fitz_Document_PERMISSION_PRINT_HQ 104L
/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Document_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    openNativeWithPath
 * Signature: (Ljava/lang/String;Ljava/lang/String;)Lcom/artifex/mupdf/fitz/Document;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Document_openNativeWithPath
  (JNIEnv *, jclass, jstring, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    openNativeWithBuffer
 * Signature: (Ljava/lang/String;[B[B)Lcom/artifex/mupdf/fitz/Document;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Document_openNativeWithBuffer
  (JNIEnv *, jclass, jstring, jbyteArray, jbyteArray);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    openNativeWithStream
 * Signature: (Ljava/lang/String;Lcom/artifex/mupdf/fitz/SeekableInputStream;Lcom/artifex/mupdf/fitz/SeekableInputStream;)Lcom/artifex/mupdf/fitz/Document;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Document_openNativeWithStream
  (JNIEnv *, jclass, jstring, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    openNativeWithPathAndStream
 * Signature: (Ljava/lang/String;Lcom/artifex/mupdf/fitz/SeekableInputStream;)Lcom/artifex/mupdf/fitz/Document;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Document_openNativeWithPathAndStream
  (JNIEnv *, jclass, jstring, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    recognize
 * Signature: (Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_Document_recognize
  (JNIEnv *, jclass, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    supportsAccelerator
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_Document_supportsAccelerator
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    saveAccelerator
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Document_saveAccelerator
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    outputAccelerator
 * Signature: (Lcom/artifex/mupdf/fitz/SeekableOutputStream;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Document_outputAccelerator
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    needsPassword
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_Document_needsPassword
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    authenticatePassword
 * Signature: (Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_Document_authenticatePassword
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    countChapters
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Document_countChapters
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    countPages
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Document_countPages
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    loadPage
 * Signature: (II)Lcom/artifex/mupdf/fitz/Page;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Document_loadPage
  (JNIEnv *, jobject, jint, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    search
 * Signature: (IILjava/lang/String;)[[Lcom/artifex/mupdf/fitz/Quad;
 */
JNIEXPORT jobjectArray JNICALL Java_com_artifex_mupdf_fitz_Document_search
  (JNIEnv *, jobject, jint, jint, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    resolveLink
 * Signature: (Ljava/lang/String;)Lcom/artifex/mupdf/fitz/Location;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Document_resolveLink
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    resolveLinkDestination
 * Signature: (Ljava/lang/String;)Lcom/artifex/mupdf/fitz/LinkDestination;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Document_resolveLinkDestination
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    loadOutline
 * Signature: ()[Lcom/artifex/mupdf/fitz/Outline;
 */
JNIEXPORT jobjectArray JNICALL Java_com_artifex_mupdf_fitz_Document_loadOutline
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    outlineIterator
 * Signature: ()Lcom/artifex/mupdf/fitz/OutlineIterator;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Document_outlineIterator
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    getMetaData
 * Signature: (Ljava/lang/String;)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_Document_getMetaData
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    setMetaData
 * Signature: (Ljava/lang/String;Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Document_setMetaData
  (JNIEnv *, jobject, jstring, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    isReflowable
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_Document_isReflowable
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    layout
 * Signature: (FFF)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Document_layout
  (JNIEnv *, jobject, jfloat, jfloat, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    findBookmark
 * Signature: (J)Lcom/artifex/mupdf/fitz/Location;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Document_findBookmark
  (JNIEnv *, jobject, jlong);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    makeBookmark
 * Signature: (II)J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_Document_makeBookmark
  (JNIEnv *, jobject, jint, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    hasPermission
 * Signature: (I)Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_Document_hasPermission
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    isUnencryptedPDF
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_Document_isUnencryptedPDF
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    formatLinkURI
 * Signature: (Lcom/artifex/mupdf/fitz/LinkDestination;)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_Document_formatLinkURI
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Document
 * Method:    asPDF
 * Signature: ()Lcom/artifex/mupdf/fitz/PDFDocument;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Document_asPDF
  (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_DocumentWriter */

#ifndef _Included_com_artifex_mupdf_fitz_DocumentWriter
#define _Included_com_artifex_mupdf_fitz_DocumentWriter
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_artifex_mupdf_fitz_DocumentWriter
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_DocumentWriter_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DocumentWriter
 * Method:    newNativeDocumentWriter
 * Signature: (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_DocumentWriter_newNativeDocumentWriter
  (JNIEnv *, jclass, jstring, jstring, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_DocumentWriter
 * Method:    newNativeDocumentWriterWithSeekableOutputStream
 * Signature: (Lcom/artifex/mupdf/fitz/SeekableOutputStream;Ljava/lang/String;Ljava/lang/String;)J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_DocumentWriter_newNativeDocumentWriterWithSeekableOutputStream
  (JNIEnv *, jclass, jobject, jstring, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_DocumentWriter
 * Method:    newNativeDocumentWriterWithBuffer
 * Signature: (Lcom/artifex/mupdf/fitz/Buffer;Ljava/lang/String;Ljava/lang/String;)J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_DocumentWriter_newNativeDocumentWriterWithBuffer
  (JNIEnv *, jclass, jobject, jstring, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_DocumentWriter
 * Method:    beginPage
 * Signature: (Lcom/artifex/mupdf/fitz/Rect;)Lcom/artifex/mupdf/fitz/Device;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_DocumentWriter_beginPage
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DocumentWriter
 * Method:    endPage
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_DocumentWriter_endPage
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DocumentWriter
 * Method:    close
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_DocumentWriter_close
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_DocumentWriter
 * Method:    addOCRListener
 * Signature: (Lcom/artifex/mupdf/fitz/DocumentWriter/OCRListener;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_DocumentWriter_addOCRListener
  (JNIEnv *, jobject, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_DrawDevice */

#ifndef _Included_com_artifex_mupdf_fitz_DrawDevice
#define _Included_com_artifex_mupdf_fitz_DrawDevice
#ifdef __cplusplus
extern "C" {
#endif
#undef com_artifex_mupdf_fitz_DrawDevice_BLEND_NORMAL
#define com_artifex_mupdf_fitz_DrawDevice_BLEND_NORMAL 0L
#undef com_artifex_mupdf_fitz_DrawDevice_BLEND_MULTIPLY
#define com_artifex_mupdf_fitz_DrawDevice_BLEND_MULTIPLY 1L
#undef com_artifex_mupdf_fitz_DrawDevice_BLEND_SCREEN
#define com_artifex_mupdf_fitz_DrawDevice_BLEND_SCREEN 2L
#undef com_artifex_mupdf_fitz_DrawDevice_BLEND_OVERLAY
#define com_artifex_mupdf_fitz_DrawDevice_BLEND_OVERLAY 3L
#undef com_artifex_mupdf_fitz_DrawDevice_BLEND_DARKEN
#define com_artifex_mupdf_fitz_DrawDevice_BLEND_DARKEN 4L
#undef com_artifex_mupdf_fitz_DrawDevice_BLEND_LIGHTEN
#define com_artifex_mupdf_fitz_DrawDevice_BLEND_LIGHTEN 5L
#undef com_artifex_mupdf_fitz_DrawDevice_BLEND_COLOR_DODGE
#define com_artifex_mupdf_fitz_DrawDevice_BLEND_COLOR_DODGE 6L
#undef com_artifex_mupdf_fitz_DrawDevice_BLEND_COLOR_BURN
#define com_artifex_mupdf_fitz_DrawDevice_BLEND_COLOR_BURN 7L
#undef com_artifex_mupdf_fitz_DrawDevice_BLEND_HARD_LIGHT
#define com_artifex_mupdf_fitz_DrawDevice_BLEND_HARD_LIGHT 8L
#undef com_artifex_mupdf_fitz_DrawDevice_BLEND_SOFT_LIGHT
#define com_artifex_mupdf_fitz_DrawDevice_BLEND_SOFT_LIGHT 9L
#undef com_artifex_mupdf_fitz_DrawDevice_BLEND_DIFFERENCE
#define com_artifex_mupdf_fitz_DrawDevice_BLEND_DIFFERENCE 10L
#undef com_artifex_mupdf_fitz_DrawDevice_BLEND_EXCLUSION
#define com_artifex_mupdf_fitz_DrawDevice_BLEND_EXCLUSION 11L
#undef com_artifex_mupdf_fitz_DrawDevice_BLEND_HUE
#define com_artifex_mupdf_fitz_DrawDevice_BLEND_HUE 12L
#undef com_artifex_mupdf_fitz_DrawDevice_BLEND_SATURATION
#define com_artifex_mupdf_fitz_DrawDevice_BLEND_SATURATION 13L
#undef com_artifex_mupdf_fitz_DrawDevice_BLEND_COLOR
#define com_artifex_mupdf_fitz_DrawDevice_BLEND_COLOR 14L
#undef com_artifex_mupdf_fitz_DrawDevice_BLEND_LUMINOSITY
#define com_artifex_mupdf_fitz_DrawDevice_BLEND_LUMINOSITY 15L
#undef com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_MASK
#define com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_MASK 1L
#undef com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_COLOR
#define com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_COLOR 2L
#undef com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_UNCACHEABLE
#define com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_UNCACHEABLE 4L
#undef com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_FILLCOLOR_UNDEFINED
#define com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_FILLCOLOR_UNDEFINED 8L
#undef com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_STROKECOLOR_UNDEFINED
#define com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_STROKECOLOR_UNDEFINED 16L
#undef com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_STARTCAP_UNDEFINED
#define com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_STARTCAP_UNDEFINED 32L
#undef com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_DASHCAP_UNDEFINED
#define com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_DASHCAP_UNDEFINED 64L
#undef com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_ENDCAP_UNDEFINED
#define com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_ENDCAP_UNDEFINED 128L
#undef com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_LINEJOIN_UNDEFINED
#define com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_LINEJOIN_UNDEFINED 256L
#undef com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_MITERLIMIT_UNDEFINED
#define com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_MITERLIMIT_UNDEFINED 512L
#undef com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_LINEWIDTH_UNDEFINED
#define com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_LINEWIDTH_UNDEFINED 1024L
#undef com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_BBOX_DEFINED
#define com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_BBOX_DEFINED 2048L
#undef com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_GRIDFIT_AS_TILED
#define com_artifex_mupdf_fitz_DrawDevice_DEVICE_FLAG_GRIDFIT_AS_TILED 4096L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_INVALID
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_INVALID -1L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_DOCUMENT
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_DOCUMENT 0L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_PART
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_PART 1L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_ART
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_ART 2L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_SECT
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_SECT 3L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_DIV
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_DIV 4L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_BLOCKQUOTE
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_BLOCKQUOTE 5L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_CAPTION
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_CAPTION 6L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_TOC
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_TOC 7L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_TOCI
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_TOCI 8L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_INDEX
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_INDEX 9L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_NONSTRUCT
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_NONSTRUCT 10L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_PRIVATE
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_PRIVATE 11L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_DOCUMENTFRAGMENT
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_DOCUMENTFRAGMENT 12L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_ASIDE
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_ASIDE 13L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_TITLE
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_TITLE 14L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_FENOTE
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_FENOTE 15L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_SUB
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_SUB 16L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_P
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_P 17L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_H
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_H 18L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_H1
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_H1 19L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_H2
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_H2 20L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_H3
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_H3 21L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_H4
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_H4 22L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_H5
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_H5 23L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_H6
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_H6 24L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_LIST
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_LIST 25L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_LISTITEM
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_LISTITEM 26L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_LABEL
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_LABEL 27L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_LISTBODY
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_LISTBODY 28L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_TABLE
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_TABLE 29L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_TR
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_TR 30L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_TH
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_TH 31L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_TD
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_TD 32L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_THEAD
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_THEAD 33L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_TBODY
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_TBODY 34L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_TFOOT
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_TFOOT 35L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_SPAN
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_SPAN 36L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_QUOTE
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_QUOTE 37L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_NOTE
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_NOTE 38L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_REFERENCE
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_REFERENCE 39L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_BIBENTRY
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_BIBENTRY 40L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_CODE
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_CODE 41L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_LINK
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_LINK 42L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_ANNOT
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_ANNOT 43L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_EM
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_EM 44L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_STRONG
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_STRONG 45L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_RUBY
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_RUBY 46L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_RB
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_RB 47L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_RT
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_RT 48L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_RP
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_RP 49L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_WARICHU
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_WARICHU 50L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_WT
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_WT 51L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_WP
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_WP 52L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_FIGURE
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_FIGURE 53L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_FORMULA
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_FORMULA 54L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_FORM
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_FORM 55L
#undef com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_ARTIFACT
#define com_artifex_mupdf_fitz_DrawDevice_STRUCTURE_ARTIFACT 56L
#undef com_artifex_mupdf_fitz_DrawDevice_METATEXT_ACTUALTEXT
#define com_artifex_mupdf_fitz_DrawDevice_METATEXT_ACTUALTEXT 0L
#undef com_artifex_mupdf_fitz_DrawDevice_METATEXT_ALT
#define com_artifex_mupdf_fitz_DrawDevice_METATEXT_ALT 1L
#undef com_artifex_mupdf_fitz_DrawDevice_METATEXT_ABBREVIATION
#define com_artifex_mupdf_fitz_DrawDevice_METATEXT_ABBREVIATION 2L
#undef com_artifex_mupdf_fitz_DrawDevice_METATEXT_TITLE
#define com_artifex_mupdf_fitz_DrawDevice_METATEXT_TITLE 3L
/*
 * Class:     com_artifex_mupdf_fitz_DrawDevice
 * Method:    newNative
 * Signature: (Lcom/artifex/mupdf/fitz/Matrix;Lcom/artifex/mupdf/fitz/Pixmap;)J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_DrawDevice_newNative
  (JNIEnv *, jclass, jobject, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_FitzInputStream */

#ifndef _Included_com_artifex_mupdf_fitz_FitzInputStream
#define _Included_com_artifex_mupdf_fitz_FitzInputStream
#ifdef __cplusplus
extern "C" {
#endif
#undef com_artifex_mupdf_fitz_FitzInputStream_MAX_SKIP_BUFFER_SIZE
#define com_artifex_mupdf_fitz_FitzInputStream_MAX_SKIP_BUFFER_SIZE 2048L
#undef com_artifex_mupdf_fitz_FitzInputStream_DEFAULT_BUFFER_SIZE
#define com_artifex_mupdf_fitz_FitzInputStream_DEFAULT_BUFFER_SIZE 8192L
#undef com_artifex_mupdf_fitz_FitzInputStream_MAX_BUFFER_SIZE
#define com_artifex_mupdf_fitz_FitzInputStream_MAX_BUFFER_SIZE 2147483639L
/*
 * Class:     com_artifex_mupdf_fitz_FitzInputStream
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_FitzInputStream_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_FitzInputStream
 * Method:    mark
 * Signature: (I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_FitzInputStream_mark
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_FitzInputStream
 * Method:    markSupported
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_FitzInputStream_markSupported
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_FitzInputStream
 * Method:    reset
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_FitzInputStream_reset
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_FitzInputStream
 * Method:    available
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_FitzInputStream_available
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_FitzInputStream
 * Method:    readByte
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_FitzInputStream_readByte
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_FitzInputStream
 * Method:    readArray
 * Signature: ([BII)I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_FitzInputStream_readArray
  (JNIEnv *, jobject, jbyteArray, jint, jint);

/*
 * Class:     com_artifex_mupdf_fitz_FitzInputStream
 * Method:    close
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_FitzInputStream_close
  (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_Font */

#ifndef _Included_com_artifex_mupdf_fitz_Font
#define _Included_com_artifex_mupdf_fitz_Font
#ifdef __cplusplus
extern "C" {
#endif
#undef com_artifex_mupdf_fitz_Font_SIMPLE_ENCODING_LATIN
#define com_artifex_mupdf_fitz_Font_SIMPLE_ENCODING_LATIN 0L
#undef com_artifex_mupdf_fitz_Font_SIMPLE_ENCODING_GREEK
#define com_artifex_mupdf_fitz_Font_SIMPLE_ENCODING_GREEK 1L
#undef com_artifex_mupdf_fitz_Font_SIMPLE_ENCODING_CYRILLIC
#define com_artifex_mupdf_fitz_Font_SIMPLE_ENCODING_CYRILLIC 2L
#undef com_artifex_mupdf_fitz_Font_ADOBE_CNS
#define com_artifex_mupdf_fitz_Font_ADOBE_CNS 0L
#undef com_artifex_mupdf_fitz_Font_ADOBE_GB
#define com_artifex_mupdf_fitz_Font_ADOBE_GB 1L
#undef com_artifex_mupdf_fitz_Font_ADOBE_JAPAN
#define com_artifex_mupdf_fitz_Font_ADOBE_JAPAN 2L
#undef com_artifex_mupdf_fitz_Font_ADOBE_KOREA
#define com_artifex_mupdf_fitz_Font_ADOBE_KOREA 3L
/*
 * Class:     com_artifex_mupdf_fitz_Font
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Font_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Font
 * Method:    newNative
 * Signature: (Ljava/lang/String;I)J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_Font_newNative
  (JNIEnv *, jobject, jstring, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Font
 * Method:    getName
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_Font_getName
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Font
 * Method:    isMono
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_Font_isMono
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Font
 * Method:    isSerif
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_Font_isSerif
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Font
 * Method:    isBold
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_Font_isBold
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Font
 * Method:    isItalic
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_Font_isItalic
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Font
 * Method:    encodeCharacter
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Font_encodeCharacter
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Font
 * Method:    advanceGlyph
 * Signature: (IZ)F
 */
JNIEXPORT jfloat JNICALL Java_com_artifex_mupdf_fitz_Font_advanceGlyph
  (JNIEnv *, jobject, jint, jboolean);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_Image */

#ifndef _Included_com_artifex_mupdf_fitz_Image
#define _Included_com_artifex_mupdf_fitz_Image
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_artifex_mupdf_fitz_Image
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Image_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Image
 * Method:    newNativeFromPixmap
 * Signature: (Lcom/artifex/mupdf/fitz/Pixmap;)J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_Image_newNativeFromPixmap
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Image
 * Method:    newNativeFromFile
 * Signature: (Ljava/lang/String;)J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_Image_newNativeFromFile
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_Image
 * Method:    newNativeFromBytes
 * Signature: ([B)J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_Image_newNativeFromBytes
  (JNIEnv *, jobject, jbyteArray);

/*
 * Class:     com_artifex_mupdf_fitz_Image
 * Method:    newNativeFromBuffer
 * Signature: (Lcom/artifex/mupdf/fitz/Buffer;)J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_Image_newNativeFromBuffer
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Image
 * Method:    getWidth
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Image_getWidth
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Image
 * Method:    getHeight
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Image_getHeight
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Image
 * Method:    getXResolution
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Image_getXResolution
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Image
 * Method:    getYResolution
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Image_getYResolution
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Image
 * Method:    getColorSpace
 * Signature: ()Lcom/artifex/mupdf/fitz/ColorSpace;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Image_getColorSpace
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Image
 * Method:    getNumberOfComponents
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Image_getNumberOfComponents
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Image
 * Method:    getBitsPerComponent
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Image_getBitsPerComponent
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Image
 * Method:    getImageMask
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_Image_getImageMask
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Image
 * Method:    getInterpolate
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_Image_getInterpolate
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Image
 * Method:    getColorKey
 * Signature: ()[I
 */
JNIEXPORT jintArray JNICALL Java_com_artifex_mupdf_fitz_Image_getColorKey
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Image
 * Method:    getDecode
 * Signature: ()[F
 */
JNIEXPORT jfloatArray JNICALL Java_com_artifex_mupdf_fitz_Image_getDecode
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Image
 * Method:    getOrientation
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Image_getOrientation
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Image
 * Method:    getMask
 * Signature: ()Lcom/artifex/mupdf/fitz/Image;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Image_getMask
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Image
 * Method:    setOrientation
 * Signature: (I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Image_setOrientation
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Image
 * Method:    toPixmap
 * Signature: ()Lcom/artifex/mupdf/fitz/Pixmap;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Image_toPixmap
  (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_Link */

#ifndef _Included_com_artifex_mupdf_fitz_Link
#define _Included_com_artifex_mupdf_fitz_Link
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_artifex_mupdf_fitz_Link
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Link_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Link
 * Method:    getBounds
 * Signature: ()Lcom/artifex/mupdf/fitz/Rect;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Link_getBounds
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Link
 * Method:    setBounds
 * Signature: (Lcom/artifex/mupdf/fitz/Rect;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Link_setBounds
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Link
 * Method:    getURI
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_Link_getURI
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Link
 * Method:    setURI
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Link_setURI
  (JNIEnv *, jobject, jstring);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_LinkDestination */

#ifndef _Included_com_artifex_mupdf_fitz_LinkDestination
#define _Included_com_artifex_mupdf_fitz_LinkDestination
#ifdef __cplusplus
extern "C" {
#endif
#undef com_artifex_mupdf_fitz_LinkDestination_LINK_DEST_FIT
#define com_artifex_mupdf_fitz_LinkDestination_LINK_DEST_FIT 0L
#undef com_artifex_mupdf_fitz_LinkDestination_LINK_DEST_FIT_B
#define com_artifex_mupdf_fitz_LinkDestination_LINK_DEST_FIT_B 1L
#undef com_artifex_mupdf_fitz_LinkDestination_LINK_DEST_FIT_H
#define com_artifex_mupdf_fitz_LinkDestination_LINK_DEST_FIT_H 2L
#undef com_artifex_mupdf_fitz_LinkDestination_LINK_DEST_FIT_BH
#define com_artifex_mupdf_fitz_LinkDestination_LINK_DEST_FIT_BH 3L
#undef com_artifex_mupdf_fitz_LinkDestination_LINK_DEST_FIT_V
#define com_artifex_mupdf_fitz_LinkDestination_LINK_DEST_FIT_V 4L
#undef com_artifex_mupdf_fitz_LinkDestination_LINK_DEST_FIT_BV
#define com_artifex_mupdf_fitz_LinkDestination_LINK_DEST_FIT_BV 5L
#undef com_artifex_mupdf_fitz_LinkDestination_LINK_DEST_FIT_R
#define com_artifex_mupdf_fitz_LinkDestination_LINK_DEST_FIT_R 6L
#undef com_artifex_mupdf_fitz_LinkDestination_LINK_DEST_XYZ
#define com_artifex_mupdf_fitz_LinkDestination_LINK_DEST_XYZ 7L
#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_MultiArchive */

#ifndef _Included_com_artifex_mupdf_fitz_MultiArchive
#define _Included_com_artifex_mupdf_fitz_MultiArchive
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_artifex_mupdf_fitz_MultiArchive
 * Method:    newNativeMultiArchive
 * Signature: ()J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_MultiArchive_newNativeMultiArchive
  (JNIEnv *, jclass);

/*
 * Class:     com_artifex_mupdf_fitz_MultiArchive
 * Method:    mountArchive
 * Signature: (Lcom/artifex/mupdf/fitz/Archive;Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_MultiArchive_mountArchive
  (JNIEnv *, jobject, jobject, jstring);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_NativeDevice */

#ifndef _Included_com_artifex_mupdf_fitz_NativeDevice
#define _Included_com_artifex_mupdf_fitz_NativeDevice
#ifdef __cplusplus
extern "C" {
#endif
#undef com_artifex_mupdf_fitz_NativeDevice_BLEND_NORMAL
#define com_artifex_mupdf_fitz_NativeDevice_BLEND_NORMAL 0L
#undef com_artifex_mupdf_fitz_NativeDevice_BLEND_MULTIPLY
#define com_artifex_mupdf_fitz_NativeDevice_BLEND_MULTIPLY 1L
#undef com_artifex_mupdf_fitz_NativeDevice_BLEND_SCREEN
#define com_artifex_mupdf_fitz_NativeDevice_BLEND_SCREEN 2L
#undef com_artifex_mupdf_fitz_NativeDevice_BLEND_OVERLAY
#define com_artifex_mupdf_fitz_NativeDevice_BLEND_OVERLAY 3L
#undef com_artifex_mupdf_fitz_NativeDevice_BLEND_DARKEN
#define com_artifex_mupdf_fitz_NativeDevice_BLEND_DARKEN 4L
#undef com_artifex_mupdf_fitz_NativeDevice_BLEND_LIGHTEN
#define com_artifex_mupdf_fitz_NativeDevice_BLEND_LIGHTEN 5L
#undef com_artifex_mupdf_fitz_NativeDevice_BLEND_COLOR_DODGE
#define com_artifex_mupdf_fitz_NativeDevice_BLEND_COLOR_DODGE 6L
#undef com_artifex_mupdf_fitz_NativeDevice_BLEND_COLOR_BURN
#define com_artifex_mupdf_fitz_NativeDevice_BLEND_COLOR_BURN 7L
#undef com_artifex_mupdf_fitz_NativeDevice_BLEND_HARD_LIGHT
#define com_artifex_mupdf_fitz_NativeDevice_BLEND_HARD_LIGHT 8L
#undef com_artifex_mupdf_fitz_NativeDevice_BLEND_SOFT_LIGHT
#define com_artifex_mupdf_fitz_NativeDevice_BLEND_SOFT_LIGHT 9L
#undef com_artifex_mupdf_fitz_NativeDevice_BLEND_DIFFERENCE
#define com_artifex_mupdf_fitz_NativeDevice_BLEND_DIFFERENCE 10L
#undef com_artifex_mupdf_fitz_NativeDevice_BLEND_EXCLUSION
#define com_artifex_mupdf_fitz_NativeDevice_BLEND_EXCLUSION 11L
#undef com_artifex_mupdf_fitz_NativeDevice_BLEND_HUE
#define com_artifex_mupdf_fitz_NativeDevice_BLEND_HUE 12L
#undef com_artifex_mupdf_fitz_NativeDevice_BLEND_SATURATION
#define com_artifex_mupdf_fitz_NativeDevice_BLEND_SATURATION 13L
#undef com_artifex_mupdf_fitz_NativeDevice_BLEND_COLOR
#define com_artifex_mupdf_fitz_NativeDevice_BLEND_COLOR 14L
#undef com_artifex_mupdf_fitz_NativeDevice_BLEND_LUMINOSITY
#define com_artifex_mupdf_fitz_NativeDevice_BLEND_LUMINOSITY 15L
#undef com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_MASK
#define com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_MASK 1L
#undef com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_COLOR
#define com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_COLOR 2L
#undef com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_UNCACHEABLE
#define com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_UNCACHEABLE 4L
#undef com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_FILLCOLOR_UNDEFINED
#define com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_FILLCOLOR_UNDEFINED 8L
#undef com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_STROKECOLOR_UNDEFINED
#define com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_STROKECOLOR_UNDEFINED 16L
#undef com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_STARTCAP_UNDEFINED
#define com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_STARTCAP_UNDEFINED 32L
#undef com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_DASHCAP_UNDEFINED
#define com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_DASHCAP_UNDEFINED 64L
#undef com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_ENDCAP_UNDEFINED
#define com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_ENDCAP_UNDEFINED 128L
#undef com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_LINEJOIN_UNDEFINED
#define com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_LINEJOIN_UNDEFINED 256L
#undef com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_MITERLIMIT_UNDEFINED
#define com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_MITERLIMIT_UNDEFINED 512L
#undef com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_LINEWIDTH_UNDEFINED
#define com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_LINEWIDTH_UNDEFINED 1024L
#undef com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_BBOX_DEFINED
#define com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_BBOX_DEFINED 2048L
#undef com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_GRIDFIT_AS_TILED
#define com_artifex_mupdf_fitz_NativeDevice_DEVICE_FLAG_GRIDFIT_AS_TILED 4096L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_INVALID
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_INVALID -1L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_DOCUMENT
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_DOCUMENT 0L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_PART
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_PART 1L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_ART
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_ART 2L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_SECT
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_SECT 3L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_DIV
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_DIV 4L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_BLOCKQUOTE
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_BLOCKQUOTE 5L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_CAPTION
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_CAPTION 6L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_TOC
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_TOC 7L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_TOCI
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_TOCI 8L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_INDEX
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_INDEX 9L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_NONSTRUCT
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_NONSTRUCT 10L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_PRIVATE
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_PRIVATE 11L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_DOCUMENTFRAGMENT
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_DOCUMENTFRAGMENT 12L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_ASIDE
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_ASIDE 13L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_TITLE
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_TITLE 14L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_FENOTE
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_FENOTE 15L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_SUB
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_SUB 16L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_P
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_P 17L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_H
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_H 18L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_H1
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_H1 19L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_H2
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_H2 20L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_H3
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_H3 21L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_H4
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_H4 22L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_H5
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_H5 23L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_H6
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_H6 24L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_LIST
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_LIST 25L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_LISTITEM
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_LISTITEM 26L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_LABEL
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_LABEL 27L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_LISTBODY
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_LISTBODY 28L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_TABLE
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_TABLE 29L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_TR
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_TR 30L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_TH
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_TH 31L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_TD
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_TD 32L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_THEAD
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_THEAD 33L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_TBODY
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_TBODY 34L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_TFOOT
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_TFOOT 35L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_SPAN
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_SPAN 36L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_QUOTE
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_QUOTE 37L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_NOTE
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_NOTE 38L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_REFERENCE
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_REFERENCE 39L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_BIBENTRY
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_BIBENTRY 40L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_CODE
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_CODE 41L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_LINK
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_LINK 42L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_ANNOT
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_ANNOT 43L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_EM
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_EM 44L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_STRONG
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_STRONG 45L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_RUBY
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_RUBY 46L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_RB
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_RB 47L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_RT
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_RT 48L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_RP
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_RP 49L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_WARICHU
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_WARICHU 50L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_WT
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_WT 51L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_WP
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_WP 52L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_FIGURE
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_FIGURE 53L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_FORMULA
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_FORMULA 54L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_FORM
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_FORM 55L
#undef com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_ARTIFACT
#define com_artifex_mupdf_fitz_NativeDevice_STRUCTURE_ARTIFACT 56L
#undef com_artifex_mupdf_fitz_NativeDevice_METATEXT_ACTUALTEXT
#define com_artifex_mupdf_fitz_NativeDevice_METATEXT_ACTUALTEXT 0L
#undef com_artifex_mupdf_fitz_NativeDevice_METATEXT_ALT
#define com_artifex_mupdf_fitz_NativeDevice_METATEXT_ALT 1L
#undef com_artifex_mupdf_fitz_NativeDevice_METATEXT_ABBREVIATION
#define com_artifex_mupdf_fitz_NativeDevice_METATEXT_ABBREVIATION 2L
#undef com_artifex_mupdf_fitz_NativeDevice_METATEXT_TITLE
#define com_artifex_mupdf_fitz_NativeDevice_METATEXT_TITLE 3L
/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    close
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_close
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    fillPath
 * Signature: (Lcom/artifex/mupdf/fitz/Path;ZLcom/artifex/mupdf/fitz/Matrix;Lcom/artifex/mupdf/fitz/ColorSpace;[FFI)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_fillPath
  (JNIEnv *, jobject, jobject, jboolean, jobject, jobject, jfloatArray, jfloat, jint);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    strokePath
 * Signature: (Lcom/artifex/mupdf/fitz/Path;Lcom/artifex/mupdf/fitz/StrokeState;Lcom/artifex/mupdf/fitz/Matrix;Lcom/artifex/mupdf/fitz/ColorSpace;[FFI)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_strokePath
  (JNIEnv *, jobject, jobject, jobject, jobject, jobject, jfloatArray, jfloat, jint);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    clipPath
 * Signature: (Lcom/artifex/mupdf/fitz/Path;ZLcom/artifex/mupdf/fitz/Matrix;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_clipPath
  (JNIEnv *, jobject, jobject, jboolean, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    clipStrokePath
 * Signature: (Lcom/artifex/mupdf/fitz/Path;Lcom/artifex/mupdf/fitz/StrokeState;Lcom/artifex/mupdf/fitz/Matrix;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_clipStrokePath
  (JNIEnv *, jobject, jobject, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    fillText
 * Signature: (Lcom/artifex/mupdf/fitz/Text;Lcom/artifex/mupdf/fitz/Matrix;Lcom/artifex/mupdf/fitz/ColorSpace;[FFI)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_fillText
  (JNIEnv *, jobject, jobject, jobject, jobject, jfloatArray, jfloat, jint);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    strokeText
 * Signature: (Lcom/artifex/mupdf/fitz/Text;Lcom/artifex/mupdf/fitz/StrokeState;Lcom/artifex/mupdf/fitz/Matrix;Lcom/artifex/mupdf/fitz/ColorSpace;[FFI)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_strokeText
  (JNIEnv *, jobject, jobject, jobject, jobject, jobject, jfloatArray, jfloat, jint);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    clipText
 * Signature: (Lcom/artifex/mupdf/fitz/Text;Lcom/artifex/mupdf/fitz/Matrix;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_clipText
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    clipStrokeText
 * Signature: (Lcom/artifex/mupdf/fitz/Text;Lcom/artifex/mupdf/fitz/StrokeState;Lcom/artifex/mupdf/fitz/Matrix;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_clipStrokeText
  (JNIEnv *, jobject, jobject, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    ignoreText
 * Signature: (Lcom/artifex/mupdf/fitz/Text;Lcom/artifex/mupdf/fitz/Matrix;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_ignoreText
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    fillShade
 * Signature: (Lcom/artifex/mupdf/fitz/Shade;Lcom/artifex/mupdf/fitz/Matrix;FI)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_fillShade
  (JNIEnv *, jobject, jobject, jobject, jfloat, jint);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    fillImage
 * Signature: (Lcom/artifex/mupdf/fitz/Image;Lcom/artifex/mupdf/fitz/Matrix;FI)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_fillImage
  (JNIEnv *, jobject, jobject, jobject, jfloat, jint);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    fillImageMask
 * Signature: (Lcom/artifex/mupdf/fitz/Image;Lcom/artifex/mupdf/fitz/Matrix;Lcom/artifex/mupdf/fitz/ColorSpace;[FFI)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_fillImageMask
  (JNIEnv *, jobject, jobject, jobject, jobject, jfloatArray, jfloat, jint);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    clipImageMask
 * Signature: (Lcom/artifex/mupdf/fitz/Image;Lcom/artifex/mupdf/fitz/Matrix;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_clipImageMask
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    popClip
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_popClip
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    beginMask
 * Signature: (Lcom/artifex/mupdf/fitz/Rect;ZLcom/artifex/mupdf/fitz/ColorSpace;[FI)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_beginMask
  (JNIEnv *, jobject, jobject, jboolean, jobject, jfloatArray, jint);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    endMask
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_endMask
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    beginGroup
 * Signature: (Lcom/artifex/mupdf/fitz/Rect;Lcom/artifex/mupdf/fitz/ColorSpace;ZZIF)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_beginGroup
  (JNIEnv *, jobject, jobject, jobject, jboolean, jboolean, jint, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    endGroup
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_endGroup
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    beginTile
 * Signature: (Lcom/artifex/mupdf/fitz/Rect;Lcom/artifex/mupdf/fitz/Rect;FFLcom/artifex/mupdf/fitz/Matrix;I)I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_beginTile
  (JNIEnv *, jobject, jobject, jobject, jfloat, jfloat, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    endTile
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_endTile
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    renderFlags
 * Signature: (II)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_renderFlags
  (JNIEnv *, jobject, jint, jint);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    setDefaultColorSpaces
 * Signature: (Lcom/artifex/mupdf/fitz/DefaultColorSpaces;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_setDefaultColorSpaces
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    beginLayer
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_beginLayer
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    endLayer
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_endLayer
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    beginStructure
 * Signature: (ILjava/lang/String;I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_beginStructure
  (JNIEnv *, jobject, jint, jstring, jint);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    endStructure
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_endStructure
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    beginMetatext
 * Signature: (ILjava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_beginMetatext
  (JNIEnv *, jobject, jint, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_NativeDevice
 * Method:    endMetatext
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_NativeDevice_endMetatext
  (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_OutlineIterator */

#ifndef _Included_com_artifex_mupdf_fitz_OutlineIterator
#define _Included_com_artifex_mupdf_fitz_OutlineIterator
#ifdef __cplusplus
extern "C" {
#endif
#undef com_artifex_mupdf_fitz_OutlineIterator_FLAG_BOLD
#define com_artifex_mupdf_fitz_OutlineIterator_FLAG_BOLD 1L
#undef com_artifex_mupdf_fitz_OutlineIterator_FLAG_ITALIC
#define com_artifex_mupdf_fitz_OutlineIterator_FLAG_ITALIC 2L
/*
 * Class:     com_artifex_mupdf_fitz_OutlineIterator
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_OutlineIterator_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_OutlineIterator
 * Method:    next
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_OutlineIterator_next
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_OutlineIterator
 * Method:    prev
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_OutlineIterator_prev
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_OutlineIterator
 * Method:    up
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_OutlineIterator_up
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_OutlineIterator
 * Method:    down
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_OutlineIterator_down
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_OutlineIterator
 * Method:    insert
 * Signature: (Ljava/lang/String;Ljava/lang/String;ZFFFI)I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_OutlineIterator_insert
  (JNIEnv *, jobject, jstring, jstring, jboolean, jfloat, jfloat, jfloat, jint);

/*
 * Class:     com_artifex_mupdf_fitz_OutlineIterator
 * Method:    update
 * Signature: (Ljava/lang/String;Ljava/lang/String;ZFFFI)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_OutlineIterator_update
  (JNIEnv *, jobject, jstring, jstring, jboolean, jfloat, jfloat, jfloat, jint);

/*
 * Class:     com_artifex_mupdf_fitz_OutlineIterator
 * Method:    item
 * Signature: ()Lcom/artifex/mupdf/fitz/OutlineIterator/OutlineItem;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_OutlineIterator_item
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_OutlineIterator
 * Method:    delete
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_OutlineIterator_delete
  (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_PDFAnnotation */

#ifndef _Included_com_artifex_mupdf_fitz_PDFAnnotation
#define _Included_com_artifex_mupdf_fitz_PDFAnnotation
#ifdef __cplusplus
extern "C" {
#endif
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_TEXT
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_TEXT 0L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_LINK
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_LINK 1L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_FREE_TEXT
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_FREE_TEXT 2L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_LINE
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_LINE 3L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_SQUARE
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_SQUARE 4L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_CIRCLE
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_CIRCLE 5L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_POLYGON
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_POLYGON 6L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_POLY_LINE
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_POLY_LINE 7L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_HIGHLIGHT
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_HIGHLIGHT 8L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_UNDERLINE
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_UNDERLINE 9L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_SQUIGGLY
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_SQUIGGLY 10L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_STRIKE_OUT
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_STRIKE_OUT 11L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_REDACT
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_REDACT 12L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_STAMP
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_STAMP 13L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_CARET
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_CARET 14L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_INK
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_INK 15L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_POPUP
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_POPUP 16L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_FILE_ATTACHMENT
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_FILE_ATTACHMENT 17L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_SOUND
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_SOUND 18L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_MOVIE
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_MOVIE 19L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_RICH_MEDIA
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_RICH_MEDIA 20L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_WIDGET
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_WIDGET 21L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_SCREEN
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_SCREEN 22L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_PRINTER_MARK
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_PRINTER_MARK 23L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_TRAP_NET
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_TRAP_NET 24L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_WATERMARK
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_WATERMARK 25L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_3D
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_3D 26L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_PROJECTION
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_PROJECTION 27L
#undef com_artifex_mupdf_fitz_PDFAnnotation_TYPE_UNKNOWN
#define com_artifex_mupdf_fitz_PDFAnnotation_TYPE_UNKNOWN -1L
#undef com_artifex_mupdf_fitz_PDFAnnotation_LINE_ENDING_NONE
#define com_artifex_mupdf_fitz_PDFAnnotation_LINE_ENDING_NONE 0L
#undef com_artifex_mupdf_fitz_PDFAnnotation_LINE_ENDING_SQUARE
#define com_artifex_mupdf_fitz_PDFAnnotation_LINE_ENDING_SQUARE 1L
#undef com_artifex_mupdf_fitz_PDFAnnotation_LINE_ENDING_CIRCLE
#define com_artifex_mupdf_fitz_PDFAnnotation_LINE_ENDING_CIRCLE 2L
#undef com_artifex_mupdf_fitz_PDFAnnotation_LINE_ENDING_DIAMOND
#define com_artifex_mupdf_fitz_PDFAnnotation_LINE_ENDING_DIAMOND 3L
#undef com_artifex_mupdf_fitz_PDFAnnotation_LINE_ENDING_OPEN_ARROW
#define com_artifex_mupdf_fitz_PDFAnnotation_LINE_ENDING_OPEN_ARROW 4L
#undef com_artifex_mupdf_fitz_PDFAnnotation_LINE_ENDING_CLOSED_ARROW
#define com_artifex_mupdf_fitz_PDFAnnotation_LINE_ENDING_CLOSED_ARROW 5L
#undef com_artifex_mupdf_fitz_PDFAnnotation_LINE_ENDING_BUTT
#define com_artifex_mupdf_fitz_PDFAnnotation_LINE_ENDING_BUTT 6L
#undef com_artifex_mupdf_fitz_PDFAnnotation_LINE_ENDING_R_OPEN_ARROW
#define com_artifex_mupdf_fitz_PDFAnnotation_LINE_ENDING_R_OPEN_ARROW 7L
#undef com_artifex_mupdf_fitz_PDFAnnotation_LINE_ENDING_R_CLOSED_ARROW
#define com_artifex_mupdf_fitz_PDFAnnotation_LINE_ENDING_R_CLOSED_ARROW 8L
#undef com_artifex_mupdf_fitz_PDFAnnotation_LINE_ENDING_SLASH
#define com_artifex_mupdf_fitz_PDFAnnotation_LINE_ENDING_SLASH 9L
#undef com_artifex_mupdf_fitz_PDFAnnotation_BORDER_STYLE_SOLID
#define com_artifex_mupdf_fitz_PDFAnnotation_BORDER_STYLE_SOLID 0L
#undef com_artifex_mupdf_fitz_PDFAnnotation_BORDER_STYLE_DASHED
#define com_artifex_mupdf_fitz_PDFAnnotation_BORDER_STYLE_DASHED 1L
#undef com_artifex_mupdf_fitz_PDFAnnotation_BORDER_STYLE_BEVELED
#define com_artifex_mupdf_fitz_PDFAnnotation_BORDER_STYLE_BEVELED 2L
#undef com_artifex_mupdf_fitz_PDFAnnotation_BORDER_STYLE_INSET
#define com_artifex_mupdf_fitz_PDFAnnotation_BORDER_STYLE_INSET 3L
#undef com_artifex_mupdf_fitz_PDFAnnotation_BORDER_STYLE_UNDERLINE
#define com_artifex_mupdf_fitz_PDFAnnotation_BORDER_STYLE_UNDERLINE 4L
#undef com_artifex_mupdf_fitz_PDFAnnotation_BORDER_EFFECT_NONE
#define com_artifex_mupdf_fitz_PDFAnnotation_BORDER_EFFECT_NONE 0L
#undef com_artifex_mupdf_fitz_PDFAnnotation_BORDER_EFFECT_CLOUDY
#define com_artifex_mupdf_fitz_PDFAnnotation_BORDER_EFFECT_CLOUDY 1L
#undef com_artifex_mupdf_fitz_PDFAnnotation_IS_INVISIBLE
#define com_artifex_mupdf_fitz_PDFAnnotation_IS_INVISIBLE 1L
#undef com_artifex_mupdf_fitz_PDFAnnotation_IS_HIDDEN
#define com_artifex_mupdf_fitz_PDFAnnotation_IS_HIDDEN 2L
#undef com_artifex_mupdf_fitz_PDFAnnotation_IS_PRINT
#define com_artifex_mupdf_fitz_PDFAnnotation_IS_PRINT 4L
#undef com_artifex_mupdf_fitz_PDFAnnotation_IS_NO_ZOOM
#define com_artifex_mupdf_fitz_PDFAnnotation_IS_NO_ZOOM 8L
#undef com_artifex_mupdf_fitz_PDFAnnotation_IS_NO_ROTATE
#define com_artifex_mupdf_fitz_PDFAnnotation_IS_NO_ROTATE 16L
#undef com_artifex_mupdf_fitz_PDFAnnotation_IS_NO_VIEW
#define com_artifex_mupdf_fitz_PDFAnnotation_IS_NO_VIEW 32L
#undef com_artifex_mupdf_fitz_PDFAnnotation_IS_READ_ONLY
#define com_artifex_mupdf_fitz_PDFAnnotation_IS_READ_ONLY 64L
#undef com_artifex_mupdf_fitz_PDFAnnotation_IS_LOCKED
#define com_artifex_mupdf_fitz_PDFAnnotation_IS_LOCKED 128L
#undef com_artifex_mupdf_fitz_PDFAnnotation_IS_TOGGLE_NO_VIEW
#define com_artifex_mupdf_fitz_PDFAnnotation_IS_TOGGLE_NO_VIEW 256L
#undef com_artifex_mupdf_fitz_PDFAnnotation_IS_LOCKED_CONTENTS
#define com_artifex_mupdf_fitz_PDFAnnotation_IS_LOCKED_CONTENTS 512L
#undef com_artifex_mupdf_fitz_PDFAnnotation_IT_DEFAULT
#define com_artifex_mupdf_fitz_PDFAnnotation_IT_DEFAULT 0L
#undef com_artifex_mupdf_fitz_PDFAnnotation_IT_FREETEXT_CALLOUT
#define com_artifex_mupdf_fitz_PDFAnnotation_IT_FREETEXT_CALLOUT 1L
#undef com_artifex_mupdf_fitz_PDFAnnotation_IT_FREETEXT_TYPEWRITER
#define com_artifex_mupdf_fitz_PDFAnnotation_IT_FREETEXT_TYPEWRITER 2L
#undef com_artifex_mupdf_fitz_PDFAnnotation_IT_LINE_ARROW
#define com_artifex_mupdf_fitz_PDFAnnotation_IT_LINE_ARROW 3L
#undef com_artifex_mupdf_fitz_PDFAnnotation_IT_LINE_DIMENSION
#define com_artifex_mupdf_fitz_PDFAnnotation_IT_LINE_DIMENSION 4L
#undef com_artifex_mupdf_fitz_PDFAnnotation_IT_POLYLINE_DIMENSION
#define com_artifex_mupdf_fitz_PDFAnnotation_IT_POLYLINE_DIMENSION 5L
#undef com_artifex_mupdf_fitz_PDFAnnotation_IT_POLYGON_CLOUD
#define com_artifex_mupdf_fitz_PDFAnnotation_IT_POLYGON_CLOUD 6L
#undef com_artifex_mupdf_fitz_PDFAnnotation_IT_POLYGON_DIMENSION
#define com_artifex_mupdf_fitz_PDFAnnotation_IT_POLYGON_DIMENSION 7L
#undef com_artifex_mupdf_fitz_PDFAnnotation_IT_STAMP_IMAGE
#define com_artifex_mupdf_fitz_PDFAnnotation_IT_STAMP_IMAGE 8L
#undef com_artifex_mupdf_fitz_PDFAnnotation_IT_STAMP_SNAPSHOT
#define com_artifex_mupdf_fitz_PDFAnnotation_IT_STAMP_SNAPSHOT 9L
#undef com_artifex_mupdf_fitz_PDFAnnotation_IT_UNKNOWN
#define com_artifex_mupdf_fitz_PDFAnnotation_IT_UNKNOWN 255L
/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    run
 * Signature: (Lcom/artifex/mupdf/fitz/Device;Lcom/artifex/mupdf/fitz/Matrix;Lcom/artifex/mupdf/fitz/Cookie;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_run
  (JNIEnv *, jobject, jobject, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    toPixmap
 * Signature: (Lcom/artifex/mupdf/fitz/Matrix;Lcom/artifex/mupdf/fitz/ColorSpace;Z)Lcom/artifex/mupdf/fitz/Pixmap;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_toPixmap
  (JNIEnv *, jobject, jobject, jobject, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getBounds
 * Signature: ()Lcom/artifex/mupdf/fitz/Rect;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getBounds
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    toDisplayList
 * Signature: ()Lcom/artifex/mupdf/fitz/DisplayList;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_toDisplayList
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getType
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getType
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getFlags
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getFlags
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setFlags
 * Signature: (I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setFlags
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getContents
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getContents
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setContents
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setContents
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    hasRichContents
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_hasRichContents
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getRichContents
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getRichContents
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setRichContents
 * Signature: (Ljava/lang/String;Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setRichContents
  (JNIEnv *, jobject, jstring, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    hasRichDefaults
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_hasRichDefaults
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getRichDefaults
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getRichDefaults
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setRichDefaults
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setRichDefaults
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getColor
 * Signature: ()[F
 */
JNIEXPORT jfloatArray JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getColor
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setColor
 * Signature: ([F)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setColor
  (JNIEnv *, jobject, jfloatArray);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getOpacity
 * Signature: ()F
 */
JNIEXPORT jfloat JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getOpacity
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setOpacity
 * Signature: (F)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setOpacity
  (JNIEnv *, jobject, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getCreationDateNative
 * Signature: ()J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getCreationDateNative
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setCreationDate
 * Signature: (J)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setCreationDate
  (JNIEnv *, jobject, jlong);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getModificationDateNative
 * Signature: ()J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getModificationDateNative
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setModificationDate
 * Signature: (J)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setModificationDate
  (JNIEnv *, jobject, jlong);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    hasRect
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_hasRect
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getRect
 * Signature: ()Lcom/artifex/mupdf/fitz/Rect;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getRect
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setRect
 * Signature: (Lcom/artifex/mupdf/fitz/Rect;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setRect
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    hasInteriorColor
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_hasInteriorColor
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getInteriorColor
 * Signature: ()[F
 */
JNIEXPORT jfloatArray JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getInteriorColor
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setInteriorColor
 * Signature: ([F)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setInteriorColor
  (JNIEnv *, jobject, jfloatArray);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    hasAuthor
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_hasAuthor
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getAuthor
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getAuthor
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setAuthor
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setAuthor
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    hasLineEndingStyles
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_hasLineEndingStyles
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getLineEndingStyles
 * Signature: ()[I
 */
JNIEXPORT jintArray JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getLineEndingStyles
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setLineEndingStyles
 * Signature: (II)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setLineEndingStyles
  (JNIEnv *, jobject, jint, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    hasBorder
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_hasBorder
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getBorderStyle
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getBorderStyle
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setBorderStyle
 * Signature: (I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setBorderStyle
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getBorderWidth
 * Signature: ()F
 */
JNIEXPORT jfloat JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getBorderWidth
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setBorderWidth
 * Signature: (F)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setBorderWidth
  (JNIEnv *, jobject, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getBorderDashCount
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getBorderDashCount
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getBorderDashItem
 * Signature: (I)F
 */
JNIEXPORT jfloat JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getBorderDashItem
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    clearBorderDash
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_clearBorderDash
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    addBorderDashItem
 * Signature: (F)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_addBorderDashItem
  (JNIEnv *, jobject, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    hasBorderEffect
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_hasBorderEffect
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getBorderEffect
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getBorderEffect
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setBorderEffect
 * Signature: (I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setBorderEffect
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getBorderEffectIntensity
 * Signature: ()F
 */
JNIEXPORT jfloat JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getBorderEffectIntensity
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setBorderEffectIntensity
 * Signature: (F)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setBorderEffectIntensity
  (JNIEnv *, jobject, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    hasQuadPoints
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_hasQuadPoints
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getQuadPointCount
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getQuadPointCount
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getQuadPoint
 * Signature: (I)Lcom/artifex/mupdf/fitz/Quad;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getQuadPoint
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    clearQuadPoints
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_clearQuadPoints
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    addQuadPoint
 * Signature: (Lcom/artifex/mupdf/fitz/Quad;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_addQuadPoint
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    hasVertices
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_hasVertices
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getVertexCount
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getVertexCount
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getVertex
 * Signature: (I)Lcom/artifex/mupdf/fitz/Point;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getVertex
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    clearVertices
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_clearVertices
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    addVertex
 * Signature: (FF)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_addVertex
  (JNIEnv *, jobject, jfloat, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    hasInkList
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_hasInkList
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getInkListCount
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getInkListCount
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getInkListStrokeCount
 * Signature: (I)I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getInkListStrokeCount
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getInkListStrokeVertex
 * Signature: (II)Lcom/artifex/mupdf/fitz/Point;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getInkListStrokeVertex
  (JNIEnv *, jobject, jint, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    clearInkList
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_clearInkList
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    addInkListStroke
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_addInkListStroke
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    addInkListStrokeVertex
 * Signature: (FF)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_addInkListStrokeVertex
  (JNIEnv *, jobject, jfloat, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    hasCallout
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_hasCallout
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getCalloutStyle
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getCalloutStyle
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setCalloutStyle
 * Signature: (I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setCalloutStyle
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getCalloutPoint
 * Signature: ()Lcom/artifex/mupdf/fitz/Point;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getCalloutPoint
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setCalloutPoint
 * Signature: (Lcom/artifex/mupdf/fitz/Point;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setCalloutPoint
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getCalloutLine
 * Signature: ()[Lcom/artifex/mupdf/fitz/Point;
 */
JNIEXPORT jobjectArray JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getCalloutLine
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setCalloutLineNative
 * Signature: (ILcom/artifex/mupdf/fitz/Point;Lcom/artifex/mupdf/fitz/Point;Lcom/artifex/mupdf/fitz/Point;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setCalloutLineNative
  (JNIEnv *, jobject, jint, jobject, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    hasIcon
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_hasIcon
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getIcon
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getIcon
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setIcon
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setIcon
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    hasPopup
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_hasPopup
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getPopup
 * Signature: ()Lcom/artifex/mupdf/fitz/Rect;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getPopup
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setPopup
 * Signature: (Lcom/artifex/mupdf/fitz/Rect;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setPopup
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    hasOpen
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_hasOpen
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getIsOpen
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getIsOpen
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setIsOpen
 * Signature: (Z)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setIsOpen
  (JNIEnv *, jobject, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    hasLine
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_hasLine
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getLine
 * Signature: ()[Lcom/artifex/mupdf/fitz/Point;
 */
JNIEXPORT jobjectArray JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getLine
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setLine
 * Signature: (Lcom/artifex/mupdf/fitz/Point;Lcom/artifex/mupdf/fitz/Point;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setLine
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getLineLeader
 * Signature: ()F
 */
JNIEXPORT jfloat JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getLineLeader
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setLineLeader
 * Signature: (F)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setLineLeader
  (JNIEnv *, jobject, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getLineLeaderExtension
 * Signature: ()F
 */
JNIEXPORT jfloat JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getLineLeaderExtension
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setLineLeaderExtension
 * Signature: (F)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setLineLeaderExtension
  (JNIEnv *, jobject, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getLineLeaderOffset
 * Signature: ()F
 */
JNIEXPORT jfloat JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getLineLeaderOffset
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setLineLeaderOffset
 * Signature: (F)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setLineLeaderOffset
  (JNIEnv *, jobject, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getLineCaption
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getLineCaption
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setLineCaption
 * Signature: (Z)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setLineCaption
  (JNIEnv *, jobject, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getLineCaptionOffset
 * Signature: ()Lcom/artifex/mupdf/fitz/Point;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getLineCaptionOffset
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setLineCaptionOffset
 * Signature: (Lcom/artifex/mupdf/fitz/Point;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setLineCaptionOffset
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    hasFilespec
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_hasFilespec
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setFilespec
 * Signature: (Lcom/artifex/mupdf/fitz/PDFObject;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setFilespec
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getFilespec
 * Signature: ()Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getFilespec
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    hasIntent
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_hasIntent
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getIntent
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getIntent
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setIntent
 * Signature: (I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setIntent
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    eventEnter
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_eventEnter
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    eventExit
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_eventExit
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    eventDown
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_eventDown
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    eventUp
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_eventUp
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    eventFocus
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_eventFocus
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    eventBlur
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_eventBlur
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    requestSynthesis
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_requestSynthesis
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    requestResynthesis
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_requestResynthesis
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    update
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_update
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getHot
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getHot
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setHot
 * Signature: (Z)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setHot
  (JNIEnv *, jobject, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getObject
 * Signature: ()Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getObject
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getLanguage
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getLanguage
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setLanguage
 * Signature: (I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setLanguage
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    hasQuadding
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_hasQuadding
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getQuadding
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getQuadding
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setQuadding
 * Signature: (I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setQuadding
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    hasDefaultAppearance
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_hasDefaultAppearance
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getDefaultAppearance
 * Signature: ()Lcom/artifex/mupdf/fitz/DefaultAppearance;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getDefaultAppearance
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setDefaultAppearance
 * Signature: (Ljava/lang/String;F[F)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setDefaultAppearance
  (JNIEnv *, jobject, jstring, jfloat, jfloatArray);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setNativeAppearance
 * Signature: (Ljava/lang/String;Ljava/lang/String;Lcom/artifex/mupdf/fitz/Matrix;Lcom/artifex/mupdf/fitz/Rect;Lcom/artifex/mupdf/fitz/PDFObject;Lcom/artifex/mupdf/fitz/Buffer;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setNativeAppearance
  (JNIEnv *, jobject, jstring, jstring, jobject, jobject, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setNativeAppearanceDisplayList
 * Signature: (Ljava/lang/String;Ljava/lang/String;Lcom/artifex/mupdf/fitz/Matrix;Lcom/artifex/mupdf/fitz/DisplayList;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setNativeAppearanceDisplayList
  (JNIEnv *, jobject, jstring, jstring, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setNativeAppearanceImage
 * Signature: (Lcom/artifex/mupdf/fitz/Image;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setNativeAppearanceImage
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getStampImageObject
 * Signature: ()Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getStampImageObject
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setStampImageObject
 * Signature: (Lcom/artifex/mupdf/fitz/PDFObject;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setStampImageObject
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setStampImage
 * Signature: (Lcom/artifex/mupdf/fitz/Image;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setStampImage
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    getHiddenForEditing
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_getHiddenForEditing
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    setHiddenForEditing
 * Signature: (Z)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_setHiddenForEditing
  (JNIEnv *, jobject, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    applyRedaction
 * Signature: (ZIII)Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_applyRedaction
  (JNIEnv *, jobject, jboolean, jint, jint, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFAnnotation
 * Method:    process
 * Signature: (Lcom/artifex/mupdf/fitz/PDFProcessor;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFAnnotation_process
  (JNIEnv *, jobject, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_PDFDocument */

#ifndef _Included_com_artifex_mupdf_fitz_PDFDocument
#define _Included_com_artifex_mupdf_fitz_PDFDocument
#ifdef __cplusplus
extern "C" {
#endif
#undef com_artifex_mupdf_fitz_PDFDocument_PERMISSION_PRINT
#define com_artifex_mupdf_fitz_PDFDocument_PERMISSION_PRINT 112L
#undef com_artifex_mupdf_fitz_PDFDocument_PERMISSION_COPY
#define com_artifex_mupdf_fitz_PDFDocument_PERMISSION_COPY 99L
#undef com_artifex_mupdf_fitz_PDFDocument_PERMISSION_EDIT
#define com_artifex_mupdf_fitz_PDFDocument_PERMISSION_EDIT 101L
#undef com_artifex_mupdf_fitz_PDFDocument_PERMISSION_ANNOTATE
#define com_artifex_mupdf_fitz_PDFDocument_PERMISSION_ANNOTATE 110L
#undef com_artifex_mupdf_fitz_PDFDocument_PERMISSION_FORM
#define com_artifex_mupdf_fitz_PDFDocument_PERMISSION_FORM 102L
#undef com_artifex_mupdf_fitz_PDFDocument_PERMISSION_ACCESSIBILITY
#define com_artifex_mupdf_fitz_PDFDocument_PERMISSION_ACCESSIBILITY 121L
#undef com_artifex_mupdf_fitz_PDFDocument_PERMISSION_ASSEMBLE
#define com_artifex_mupdf_fitz_PDFDocument_PERMISSION_ASSEMBLE 97L
#undef com_artifex_mupdf_fitz_PDFDocument_PERMISSION_PRINT_HQ
#define com_artifex_mupdf_fitz_PDFDocument_PERMISSION_PRINT_HQ 104L
#undef com_artifex_mupdf_fitz_PDFDocument_LANGUAGE_UNSET
#define com_artifex_mupdf_fitz_PDFDocument_LANGUAGE_UNSET 0L
#undef com_artifex_mupdf_fitz_PDFDocument_LANGUAGE_ur
#define com_artifex_mupdf_fitz_PDFDocument_LANGUAGE_ur 507L
#undef com_artifex_mupdf_fitz_PDFDocument_LANGUAGE_urd
#define com_artifex_mupdf_fitz_PDFDocument_LANGUAGE_urd 3423L
#undef com_artifex_mupdf_fitz_PDFDocument_LANGUAGE_ko
#define com_artifex_mupdf_fitz_PDFDocument_LANGUAGE_ko 416L
#undef com_artifex_mupdf_fitz_PDFDocument_LANGUAGE_ja
#define com_artifex_mupdf_fitz_PDFDocument_LANGUAGE_ja 37L
#undef com_artifex_mupdf_fitz_PDFDocument_LANGUAGE_zh
#define com_artifex_mupdf_fitz_PDFDocument_LANGUAGE_zh 242L
#undef com_artifex_mupdf_fitz_PDFDocument_LANGUAGE_zh_Hans
#define com_artifex_mupdf_fitz_PDFDocument_LANGUAGE_zh_Hans 14093L
#undef com_artifex_mupdf_fitz_PDFDocument_LANGUAGE_zh_Hant
#define com_artifex_mupdf_fitz_PDFDocument_LANGUAGE_zh_Hant 14822L
#undef com_artifex_mupdf_fitz_PDFDocument_PAGE_LABEL_NONE
#define com_artifex_mupdf_fitz_PDFDocument_PAGE_LABEL_NONE 0L
#undef com_artifex_mupdf_fitz_PDFDocument_PAGE_LABEL_DECIMAL
#define com_artifex_mupdf_fitz_PDFDocument_PAGE_LABEL_DECIMAL 68L
#undef com_artifex_mupdf_fitz_PDFDocument_PAGE_LABEL_ROMAN_UC
#define com_artifex_mupdf_fitz_PDFDocument_PAGE_LABEL_ROMAN_UC 82L
#undef com_artifex_mupdf_fitz_PDFDocument_PAGE_LABEL_ROMAN_LC
#define com_artifex_mupdf_fitz_PDFDocument_PAGE_LABEL_ROMAN_LC 114L
#undef com_artifex_mupdf_fitz_PDFDocument_PAGE_LABEL_ALPHA_UC
#define com_artifex_mupdf_fitz_PDFDocument_PAGE_LABEL_ALPHA_UC 65L
#undef com_artifex_mupdf_fitz_PDFDocument_PAGE_LABEL_ALPHA_LC
#define com_artifex_mupdf_fitz_PDFDocument_PAGE_LABEL_ALPHA_LC 97L
#undef com_artifex_mupdf_fitz_PDFDocument_NOT_ZUGFERD
#define com_artifex_mupdf_fitz_PDFDocument_NOT_ZUGFERD 0L
#undef com_artifex_mupdf_fitz_PDFDocument_ZUGFERD_COMFORT
#define com_artifex_mupdf_fitz_PDFDocument_ZUGFERD_COMFORT 1L
#undef com_artifex_mupdf_fitz_PDFDocument_ZUGFERD_BASIC
#define com_artifex_mupdf_fitz_PDFDocument_ZUGFERD_BASIC 2L
#undef com_artifex_mupdf_fitz_PDFDocument_ZUGFERD_EXTENDED
#define com_artifex_mupdf_fitz_PDFDocument_ZUGFERD_EXTENDED 3L
#undef com_artifex_mupdf_fitz_PDFDocument_ZUGFERD_BASIC_WL
#define com_artifex_mupdf_fitz_PDFDocument_ZUGFERD_BASIC_WL 4L
#undef com_artifex_mupdf_fitz_PDFDocument_ZUGFERD_MINIMUM
#define com_artifex_mupdf_fitz_PDFDocument_ZUGFERD_MINIMUM 5L
#undef com_artifex_mupdf_fitz_PDFDocument_ZUGFERD_XRECHNUNG
#define com_artifex_mupdf_fitz_PDFDocument_ZUGFERD_XRECHNUNG 6L
#undef com_artifex_mupdf_fitz_PDFDocument_ZUGFERD_UNKNOWN
#define com_artifex_mupdf_fitz_PDFDocument_ZUGFERD_UNKNOWN 7L
/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    newNative
 * Signature: ()J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_newNative
  (JNIEnv *, jclass);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    findPage
 * Signature: (I)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_findPage
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    getTrailer
 * Signature: ()Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_getTrailer
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    countObjects
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_countObjects
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    newNull
 * Signature: ()Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_newNull
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    newBoolean
 * Signature: (Z)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_newBoolean
  (JNIEnv *, jobject, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    newInteger
 * Signature: (I)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_newInteger
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    newReal
 * Signature: (F)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_newReal
  (JNIEnv *, jobject, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    newString
 * Signature: (Ljava/lang/String;)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_newString
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    newByteString
 * Signature: ([B)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_newByteString
  (JNIEnv *, jobject, jbyteArray);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    newName
 * Signature: (Ljava/lang/String;)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_newName
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    newIndirect
 * Signature: (II)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_newIndirect
  (JNIEnv *, jobject, jint, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    newArray
 * Signature: ()Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_newArray
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    newDictionary
 * Signature: ()Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_newDictionary
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    addObject
 * Signature: (Lcom/artifex/mupdf/fitz/PDFObject;)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_addObject
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    createObject
 * Signature: ()Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_createObject
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    deleteObject
 * Signature: (I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_deleteObject
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    newPDFGraftMap
 * Signature: ()Lcom/artifex/mupdf/fitz/PDFGraftMap;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_newPDFGraftMap
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    graftObject
 * Signature: (Lcom/artifex/mupdf/fitz/PDFObject;)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_graftObject
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    graftPage
 * Signature: (ILcom/artifex/mupdf/fitz/PDFDocument;I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_graftPage
  (JNIEnv *, jobject, jint, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    addStreamBuffer
 * Signature: (Lcom/artifex/mupdf/fitz/Buffer;Ljava/lang/Object;Z)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_addStreamBuffer
  (JNIEnv *, jobject, jobject, jobject, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    addStreamString
 * Signature: (Ljava/lang/String;Ljava/lang/Object;Z)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_addStreamString
  (JNIEnv *, jobject, jstring, jobject, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    addPageBuffer
 * Signature: (Lcom/artifex/mupdf/fitz/Rect;ILcom/artifex/mupdf/fitz/PDFObject;Lcom/artifex/mupdf/fitz/Buffer;)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_addPageBuffer
  (JNIEnv *, jobject, jobject, jint, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    addPageString
 * Signature: (Lcom/artifex/mupdf/fitz/Rect;ILcom/artifex/mupdf/fitz/PDFObject;Ljava/lang/String;)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_addPageString
  (JNIEnv *, jobject, jobject, jint, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    insertPage
 * Signature: (ILcom/artifex/mupdf/fitz/PDFObject;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_insertPage
  (JNIEnv *, jobject, jint, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    deletePage
 * Signature: (I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_deletePage
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    addImage
 * Signature: (Lcom/artifex/mupdf/fitz/Image;)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_addImage
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    addSimpleFont
 * Signature: (Lcom/artifex/mupdf/fitz/Font;I)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_addSimpleFont
  (JNIEnv *, jobject, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    addCJKFont
 * Signature: (Lcom/artifex/mupdf/fitz/Font;IIZ)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_addCJKFont
  (JNIEnv *, jobject, jobject, jint, jint, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    addFont
 * Signature: (Lcom/artifex/mupdf/fitz/Font;)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_addFont
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    hasUnsavedChanges
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_hasUnsavedChanges
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    wasRepaired
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_wasRepaired
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    canBeSavedIncrementally
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_canBeSavedIncrementally
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    isRedacted
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_isRedacted
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    rearrangePages
 * Signature: ([I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_rearrangePages
  (JNIEnv *, jobject, jintArray);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    save
 * Signature: (Ljava/lang/String;Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_save
  (JNIEnv *, jobject, jstring, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    nativeSaveWithStream
 * Signature: (Lcom/artifex/mupdf/fitz/SeekableInputOutputStream;Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_nativeSaveWithStream
  (JNIEnv *, jobject, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    enableJs
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_enableJs
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    disableJs
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_disableJs
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    isJsSupported
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_isJsSupported
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    setJsEventListener
 * Signature: (Lcom/artifex/mupdf/fitz/PDFDocument/JsEventListener;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_setJsEventListener
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    calculate
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_calculate
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    getVersion
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_getVersion
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    countVersions
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_countVersions
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    countUnsavedVersions
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_countUnsavedVersions
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    validateChangeHistory
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_validateChangeHistory
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    wasPureXFA
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_wasPureXFA
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    wasLinearized
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_wasLinearized
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    enableJournal
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_enableJournal
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    saveJournal
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_saveJournal
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    saveJournalWithStream
 * Signature: (Lcom/artifex/mupdf/fitz/SeekableOutputStream;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_saveJournalWithStream
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    loadJournal
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_loadJournal
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    loadJournalWithStream
 * Signature: (Lcom/artifex/mupdf/fitz/SeekableInputStream;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_loadJournalWithStream
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    undoRedoPosition
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_undoRedoPosition
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    undoRedoSteps
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_undoRedoSteps
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    undoRedoStep
 * Signature: (I)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_undoRedoStep
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    canUndo
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_canUndo
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    canRedo
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_canRedo
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    undo
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_undo
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    redo
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_redo
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    beginOperation
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_beginOperation
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    beginImplicitOperation
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_beginImplicitOperation
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    endOperation
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_endOperation
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    abandonOperation
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_abandonOperation
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    getLanguage
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_getLanguage
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    setLanguage
 * Signature: (I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_setLanguage
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    setPageLabels
 * Signature: (IILjava/lang/String;I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_setPageLabels
  (JNIEnv *, jobject, jint, jint, jstring, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    deletePageLabels
 * Signature: (I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_deletePageLabels
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    formatURIFromPathAndNamedDest
 * Signature: (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_formatURIFromPathAndNamedDest
  (JNIEnv *, jclass, jstring, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    formatURIFromPathAndExplicitDest
 * Signature: (Ljava/lang/String;Lcom/artifex/mupdf/fitz/LinkDestination;)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_formatURIFromPathAndExplicitDest
  (JNIEnv *, jclass, jstring, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    appendNamedDestToURI
 * Signature: (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_appendNamedDestToURI
  (JNIEnv *, jclass, jstring, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    appendExplicitDestToURI
 * Signature: (Ljava/lang/String;Lcom/artifex/mupdf/fitz/LinkDestination;)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_appendExplicitDestToURI
  (JNIEnv *, jclass, jstring, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    countSignatures
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_countSignatures
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    addEmbeddedFile
 * Signature: (Ljava/lang/String;Ljava/lang/String;Lcom/artifex/mupdf/fitz/Buffer;JJZ)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_addEmbeddedFile
  (JNIEnv *, jobject, jstring, jstring, jobject, jlong, jlong, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    getFilespecParams
 * Signature: (Lcom/artifex/mupdf/fitz/PDFObject;)Lcom/artifex/mupdf/fitz/PDFDocument/PDFFilespecParams;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_getFilespecParams
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    loadEmbeddedFileContents
 * Signature: (Lcom/artifex/mupdf/fitz/PDFObject;)Lcom/artifex/mupdf/fitz/Buffer;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_loadEmbeddedFileContents
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    verifyEmbeddedFileChecksum
 * Signature: (Lcom/artifex/mupdf/fitz/PDFObject;)Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_verifyEmbeddedFileChecksum
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    isEmbeddedFile
 * Signature: (Lcom/artifex/mupdf/fitz/PDFObject;)Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_isEmbeddedFile
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    countLayers
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_countLayers
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    isLayerVisible
 * Signature: (I)Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_isLayerVisible
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    setLayerVisible
 * Signature: (IZ)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_setLayerVisible
  (JNIEnv *, jobject, jint, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    getLayerName
 * Signature: (I)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_getLayerName
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    countAssociatedFiles
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_countAssociatedFiles
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    associatedFile
 * Signature: (I)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_associatedFile
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    zugferdProfile
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_zugferdProfile
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    zugferdVersion
 * Signature: ()F
 */
JNIEXPORT jfloat JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_zugferdVersion
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    zugferdXML
 * Signature: ()Lcom/artifex/mupdf/fitz/Buffer;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_zugferdXML
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    loadImage
 * Signature: (Lcom/artifex/mupdf/fitz/PDFObject;)Lcom/artifex/mupdf/fitz/Image;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_loadImage
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    lookupDest
 * Signature: (Lcom/artifex/mupdf/fitz/PDFObject;)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_lookupDest
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    subsetFonts
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_subsetFonts
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFDocument
 * Method:    bake
 * Signature: (ZZ)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFDocument_bake
  (JNIEnv *, jobject, jboolean, jboolean);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_PDFGraftMap */

#ifndef _Included_com_artifex_mupdf_fitz_PDFGraftMap
#define _Included_com_artifex_mupdf_fitz_PDFGraftMap
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_artifex_mupdf_fitz_PDFGraftMap
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFGraftMap_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFGraftMap
 * Method:    graftObject
 * Signature: (Lcom/artifex/mupdf/fitz/PDFObject;)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFGraftMap_graftObject
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFGraftMap
 * Method:    graftPage
 * Signature: (ILcom/artifex/mupdf/fitz/PDFDocument;I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFGraftMap_graftPage
  (JNIEnv *, jobject, jint, jobject, jint);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_PDFObject */

#ifndef _Included_com_artifex_mupdf_fitz_PDFObject
#define _Included_com_artifex_mupdf_fitz_PDFObject
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    isIndirect
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFObject_isIndirect
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    isBoolean
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFObject_isBoolean
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    isInteger
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFObject_isInteger
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    isReal
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFObject_isReal
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    isNumber
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFObject_isNumber
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    isString
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFObject_isString
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    isName
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFObject_isName
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    isArray
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFObject_isArray
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    isDictionary
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFObject_isDictionary
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    isStream
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFObject_isStream
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    asBoolean
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFObject_asBoolean
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    asInteger
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFObject_asInteger
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    asFloat
 * Signature: ()F
 */
JNIEXPORT jfloat JNICALL Java_com_artifex_mupdf_fitz_PDFObject_asFloat
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    asIndirect
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFObject_asIndirect
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    asName
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_PDFObject_asName
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    asString
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_PDFObject_asString
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    asByteString
 * Signature: ()[B
 */
JNIEXPORT jbyteArray JNICALL Java_com_artifex_mupdf_fitz_PDFObject_asByteString
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    toString
 * Signature: (ZZ)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_PDFObject_toString
  (JNIEnv *, jobject, jboolean, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    resolve
 * Signature: ()Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFObject_resolve
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    equals
 * Signature: (Lcom/artifex/mupdf/fitz/PDFObject;)Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFObject_equals
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    readStream
 * Signature: ()[B
 */
JNIEXPORT jbyteArray JNICALL Java_com_artifex_mupdf_fitz_PDFObject_readStream
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    readRawStream
 * Signature: ()[B
 */
JNIEXPORT jbyteArray JNICALL Java_com_artifex_mupdf_fitz_PDFObject_readRawStream
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    writeObject
 * Signature: (Lcom/artifex/mupdf/fitz/PDFObject;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_writeObject
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    writeStreamBuffer
 * Signature: (Lcom/artifex/mupdf/fitz/Buffer;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_writeStreamBuffer
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    writeStreamString
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_writeStreamString
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    writeRawStreamBuffer
 * Signature: (Lcom/artifex/mupdf/fitz/Buffer;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_writeRawStreamBuffer
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    writeRawStreamString
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_writeRawStreamString
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    getArray
 * Signature: (I)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFObject_getArray
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    getDictionary
 * Signature: (Ljava/lang/String;Z)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFObject_getDictionary
  (JNIEnv *, jobject, jstring, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    getDictionaryKey
 * Signature: (I)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFObject_getDictionaryKey
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    putArrayBoolean
 * Signature: (IZ)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_putArrayBoolean
  (JNIEnv *, jobject, jint, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    putArrayInteger
 * Signature: (II)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_putArrayInteger
  (JNIEnv *, jobject, jint, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    putArrayFloat
 * Signature: (IF)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_putArrayFloat
  (JNIEnv *, jobject, jint, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    putArrayString
 * Signature: (ILjava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_putArrayString
  (JNIEnv *, jobject, jint, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    putArrayPDFObject
 * Signature: (ILcom/artifex/mupdf/fitz/PDFObject;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_putArrayPDFObject
  (JNIEnv *, jobject, jint, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    putDictionaryStringBoolean
 * Signature: (Ljava/lang/String;Z)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_putDictionaryStringBoolean
  (JNIEnv *, jobject, jstring, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    putDictionaryStringInteger
 * Signature: (Ljava/lang/String;I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_putDictionaryStringInteger
  (JNIEnv *, jobject, jstring, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    putDictionaryStringFloat
 * Signature: (Ljava/lang/String;F)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_putDictionaryStringFloat
  (JNIEnv *, jobject, jstring, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    putDictionaryStringString
 * Signature: (Ljava/lang/String;Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_putDictionaryStringString
  (JNIEnv *, jobject, jstring, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    putDictionaryStringPDFObject
 * Signature: (Ljava/lang/String;Lcom/artifex/mupdf/fitz/PDFObject;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_putDictionaryStringPDFObject
  (JNIEnv *, jobject, jstring, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    putDictionaryStringRect
 * Signature: (Ljava/lang/String;Lcom/artifex/mupdf/fitz/Rect;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_putDictionaryStringRect
  (JNIEnv *, jobject, jstring, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    putDictionaryStringMatrix
 * Signature: (Ljava/lang/String;Lcom/artifex/mupdf/fitz/Matrix;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_putDictionaryStringMatrix
  (JNIEnv *, jobject, jstring, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    putDictionaryStringDate
 * Signature: (Ljava/lang/String;J)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_putDictionaryStringDate
  (JNIEnv *, jobject, jstring, jlong);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    putDictionaryPDFObjectBoolean
 * Signature: (Lcom/artifex/mupdf/fitz/PDFObject;Z)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_putDictionaryPDFObjectBoolean
  (JNIEnv *, jobject, jobject, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    putDictionaryPDFObjectInteger
 * Signature: (Lcom/artifex/mupdf/fitz/PDFObject;I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_putDictionaryPDFObjectInteger
  (JNIEnv *, jobject, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    putDictionaryPDFObjectFloat
 * Signature: (Lcom/artifex/mupdf/fitz/PDFObject;F)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_putDictionaryPDFObjectFloat
  (JNIEnv *, jobject, jobject, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    putDictionaryPDFObjectString
 * Signature: (Lcom/artifex/mupdf/fitz/PDFObject;Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_putDictionaryPDFObjectString
  (JNIEnv *, jobject, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    putDictionaryPDFObjectPDFObject
 * Signature: (Lcom/artifex/mupdf/fitz/PDFObject;Lcom/artifex/mupdf/fitz/PDFObject;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_putDictionaryPDFObjectPDFObject
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    putDictionaryPDFObjectRect
 * Signature: (Lcom/artifex/mupdf/fitz/PDFObject;Lcom/artifex/mupdf/fitz/Rect;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_putDictionaryPDFObjectRect
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    putDictionaryPDFObjectMatrix
 * Signature: (Lcom/artifex/mupdf/fitz/PDFObject;Lcom/artifex/mupdf/fitz/Matrix;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_putDictionaryPDFObjectMatrix
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    putDictionaryPDFObjectDate
 * Signature: (Lcom/artifex/mupdf/fitz/PDFObject;J)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_putDictionaryPDFObjectDate
  (JNIEnv *, jobject, jobject, jlong);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    deleteArray
 * Signature: (I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_deleteArray
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    deleteDictionaryString
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_deleteDictionaryString
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    deleteDictionaryPDFObject
 * Signature: (Lcom/artifex/mupdf/fitz/PDFObject;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_deleteDictionaryPDFObject
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    size
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFObject_size
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    pushBoolean
 * Signature: (Z)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_pushBoolean
  (JNIEnv *, jobject, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    pushInteger
 * Signature: (I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_pushInteger
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    pushFloat
 * Signature: (F)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_pushFloat
  (JNIEnv *, jobject, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    pushString
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_pushString
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    pushPDFObject
 * Signature: (Lcom/artifex/mupdf/fitz/PDFObject;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFObject_pushPDFObject
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFObject
 * Method:    isFilespec
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFObject_isFilespec
  (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_PDFPage */

#ifndef _Included_com_artifex_mupdf_fitz_PDFPage
#define _Included_com_artifex_mupdf_fitz_PDFPage
#ifdef __cplusplus
extern "C" {
#endif
#undef com_artifex_mupdf_fitz_PDFPage_MEDIA_BOX
#define com_artifex_mupdf_fitz_PDFPage_MEDIA_BOX 0L
#undef com_artifex_mupdf_fitz_PDFPage_CROP_BOX
#define com_artifex_mupdf_fitz_PDFPage_CROP_BOX 1L
#undef com_artifex_mupdf_fitz_PDFPage_BLEED_BOX
#define com_artifex_mupdf_fitz_PDFPage_BLEED_BOX 2L
#undef com_artifex_mupdf_fitz_PDFPage_TRIM_BOX
#define com_artifex_mupdf_fitz_PDFPage_TRIM_BOX 3L
#undef com_artifex_mupdf_fitz_PDFPage_ART_BOX
#define com_artifex_mupdf_fitz_PDFPage_ART_BOX 4L
#undef com_artifex_mupdf_fitz_PDFPage_UNKNOWN_BOX
#define com_artifex_mupdf_fitz_PDFPage_UNKNOWN_BOX 5L
#undef com_artifex_mupdf_fitz_PDFPage_REDACT_IMAGE_NONE
#define com_artifex_mupdf_fitz_PDFPage_REDACT_IMAGE_NONE 0L
#undef com_artifex_mupdf_fitz_PDFPage_REDACT_IMAGE_REMOVE
#define com_artifex_mupdf_fitz_PDFPage_REDACT_IMAGE_REMOVE 1L
#undef com_artifex_mupdf_fitz_PDFPage_REDACT_IMAGE_PIXELS
#define com_artifex_mupdf_fitz_PDFPage_REDACT_IMAGE_PIXELS 2L
#undef com_artifex_mupdf_fitz_PDFPage_REDACT_IMAGE_REMOVE_UNLESS_INVISIBLE
#define com_artifex_mupdf_fitz_PDFPage_REDACT_IMAGE_REMOVE_UNLESS_INVISIBLE 3L
#undef com_artifex_mupdf_fitz_PDFPage_REDACT_LINE_ART_NONE
#define com_artifex_mupdf_fitz_PDFPage_REDACT_LINE_ART_NONE 0L
#undef com_artifex_mupdf_fitz_PDFPage_REDACT_LINE_ART_REMOVE_IF_COVERED
#define com_artifex_mupdf_fitz_PDFPage_REDACT_LINE_ART_REMOVE_IF_COVERED 1L
#undef com_artifex_mupdf_fitz_PDFPage_REDACT_LINE_ART_REMOVE_IF_TOUCHED
#define com_artifex_mupdf_fitz_PDFPage_REDACT_LINE_ART_REMOVE_IF_TOUCHED 2L
#undef com_artifex_mupdf_fitz_PDFPage_REDACT_TEXT_REMOVE
#define com_artifex_mupdf_fitz_PDFPage_REDACT_TEXT_REMOVE 0L
#undef com_artifex_mupdf_fitz_PDFPage_REDACT_TEXT_NONE
#define com_artifex_mupdf_fitz_PDFPage_REDACT_TEXT_NONE 1L
/*
 * Class:     com_artifex_mupdf_fitz_PDFPage
 * Method:    getObject
 * Signature: ()Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFPage_getObject
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFPage
 * Method:    getAnnotations
 * Signature: ()[Lcom/artifex/mupdf/fitz/PDFAnnotation;
 */
JNIEXPORT jobjectArray JNICALL Java_com_artifex_mupdf_fitz_PDFPage_getAnnotations
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFPage
 * Method:    createAnnotation
 * Signature: (I)Lcom/artifex/mupdf/fitz/PDFAnnotation;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFPage_createAnnotation
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFPage
 * Method:    deleteAnnotation
 * Signature: (Lcom/artifex/mupdf/fitz/PDFAnnotation;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFPage_deleteAnnotation
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFPage
 * Method:    applyRedactions
 * Signature: (ZIII)Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFPage_applyRedactions
  (JNIEnv *, jobject, jboolean, jint, jint, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFPage
 * Method:    update
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFPage_update
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFPage
 * Method:    getWidgets
 * Signature: ()[Lcom/artifex/mupdf/fitz/PDFWidget;
 */
JNIEXPORT jobjectArray JNICALL Java_com_artifex_mupdf_fitz_PDFPage_getWidgets
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFPage
 * Method:    createSignature
 * Signature: ()Lcom/artifex/mupdf/fitz/PDFWidget;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFPage_createSignature
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFPage
 * Method:    getTransform
 * Signature: ()Lcom/artifex/mupdf/fitz/Matrix;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFPage_getTransform
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFPage
 * Method:    setPageBox
 * Signature: (ILcom/artifex/mupdf/fitz/Rect;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFPage_setPageBox
  (JNIEnv *, jobject, jint, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFPage
 * Method:    countAssociatedFiles
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFPage_countAssociatedFiles
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFPage
 * Method:    associatedFile
 * Signature: (I)Lcom/artifex/mupdf/fitz/PDFObject;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFPage_associatedFile
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_PDFPage
 * Method:    process
 * Signature: (Lcom/artifex/mupdf/fitz/PDFProcessor;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFPage_process
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFPage
 * Method:    toPixmap
 * Signature: (Lcom/artifex/mupdf/fitz/Matrix;Lcom/artifex/mupdf/fitz/ColorSpace;ZZLjava/lang/String;I)Lcom/artifex/mupdf/fitz/Pixmap;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFPage_toPixmap
  (JNIEnv *, jobject, jobject, jobject, jboolean, jboolean, jstring, jint);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_PDFWidget */

#ifndef _Included_com_artifex_mupdf_fitz_PDFWidget
#define _Included_com_artifex_mupdf_fitz_PDFWidget
#ifdef __cplusplus
extern "C" {
#endif
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_TEXT
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_TEXT 0L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_LINK
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_LINK 1L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_FREE_TEXT
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_FREE_TEXT 2L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_LINE
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_LINE 3L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_SQUARE
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_SQUARE 4L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_CIRCLE
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_CIRCLE 5L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_POLYGON
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_POLYGON 6L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_POLY_LINE
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_POLY_LINE 7L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_HIGHLIGHT
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_HIGHLIGHT 8L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_UNDERLINE
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_UNDERLINE 9L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_SQUIGGLY
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_SQUIGGLY 10L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_STRIKE_OUT
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_STRIKE_OUT 11L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_REDACT
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_REDACT 12L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_STAMP
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_STAMP 13L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_CARET
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_CARET 14L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_INK
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_INK 15L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_POPUP
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_POPUP 16L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_FILE_ATTACHMENT
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_FILE_ATTACHMENT 17L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_SOUND
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_SOUND 18L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_MOVIE
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_MOVIE 19L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_RICH_MEDIA
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_RICH_MEDIA 20L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_WIDGET
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_WIDGET 21L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_SCREEN
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_SCREEN 22L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_PRINTER_MARK
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_PRINTER_MARK 23L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_TRAP_NET
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_TRAP_NET 24L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_WATERMARK
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_WATERMARK 25L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_3D
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_3D 26L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_PROJECTION
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_PROJECTION 27L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_UNKNOWN
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_UNKNOWN -1L
#undef com_artifex_mupdf_fitz_PDFWidget_LINE_ENDING_NONE
#define com_artifex_mupdf_fitz_PDFWidget_LINE_ENDING_NONE 0L
#undef com_artifex_mupdf_fitz_PDFWidget_LINE_ENDING_SQUARE
#define com_artifex_mupdf_fitz_PDFWidget_LINE_ENDING_SQUARE 1L
#undef com_artifex_mupdf_fitz_PDFWidget_LINE_ENDING_CIRCLE
#define com_artifex_mupdf_fitz_PDFWidget_LINE_ENDING_CIRCLE 2L
#undef com_artifex_mupdf_fitz_PDFWidget_LINE_ENDING_DIAMOND
#define com_artifex_mupdf_fitz_PDFWidget_LINE_ENDING_DIAMOND 3L
#undef com_artifex_mupdf_fitz_PDFWidget_LINE_ENDING_OPEN_ARROW
#define com_artifex_mupdf_fitz_PDFWidget_LINE_ENDING_OPEN_ARROW 4L
#undef com_artifex_mupdf_fitz_PDFWidget_LINE_ENDING_CLOSED_ARROW
#define com_artifex_mupdf_fitz_PDFWidget_LINE_ENDING_CLOSED_ARROW 5L
#undef com_artifex_mupdf_fitz_PDFWidget_LINE_ENDING_BUTT
#define com_artifex_mupdf_fitz_PDFWidget_LINE_ENDING_BUTT 6L
#undef com_artifex_mupdf_fitz_PDFWidget_LINE_ENDING_R_OPEN_ARROW
#define com_artifex_mupdf_fitz_PDFWidget_LINE_ENDING_R_OPEN_ARROW 7L
#undef com_artifex_mupdf_fitz_PDFWidget_LINE_ENDING_R_CLOSED_ARROW
#define com_artifex_mupdf_fitz_PDFWidget_LINE_ENDING_R_CLOSED_ARROW 8L
#undef com_artifex_mupdf_fitz_PDFWidget_LINE_ENDING_SLASH
#define com_artifex_mupdf_fitz_PDFWidget_LINE_ENDING_SLASH 9L
#undef com_artifex_mupdf_fitz_PDFWidget_BORDER_STYLE_SOLID
#define com_artifex_mupdf_fitz_PDFWidget_BORDER_STYLE_SOLID 0L
#undef com_artifex_mupdf_fitz_PDFWidget_BORDER_STYLE_DASHED
#define com_artifex_mupdf_fitz_PDFWidget_BORDER_STYLE_DASHED 1L
#undef com_artifex_mupdf_fitz_PDFWidget_BORDER_STYLE_BEVELED
#define com_artifex_mupdf_fitz_PDFWidget_BORDER_STYLE_BEVELED 2L
#undef com_artifex_mupdf_fitz_PDFWidget_BORDER_STYLE_INSET
#define com_artifex_mupdf_fitz_PDFWidget_BORDER_STYLE_INSET 3L
#undef com_artifex_mupdf_fitz_PDFWidget_BORDER_STYLE_UNDERLINE
#define com_artifex_mupdf_fitz_PDFWidget_BORDER_STYLE_UNDERLINE 4L
#undef com_artifex_mupdf_fitz_PDFWidget_BORDER_EFFECT_NONE
#define com_artifex_mupdf_fitz_PDFWidget_BORDER_EFFECT_NONE 0L
#undef com_artifex_mupdf_fitz_PDFWidget_BORDER_EFFECT_CLOUDY
#define com_artifex_mupdf_fitz_PDFWidget_BORDER_EFFECT_CLOUDY 1L
#undef com_artifex_mupdf_fitz_PDFWidget_IS_INVISIBLE
#define com_artifex_mupdf_fitz_PDFWidget_IS_INVISIBLE 1L
#undef com_artifex_mupdf_fitz_PDFWidget_IS_HIDDEN
#define com_artifex_mupdf_fitz_PDFWidget_IS_HIDDEN 2L
#undef com_artifex_mupdf_fitz_PDFWidget_IS_PRINT
#define com_artifex_mupdf_fitz_PDFWidget_IS_PRINT 4L
#undef com_artifex_mupdf_fitz_PDFWidget_IS_NO_ZOOM
#define com_artifex_mupdf_fitz_PDFWidget_IS_NO_ZOOM 8L
#undef com_artifex_mupdf_fitz_PDFWidget_IS_NO_ROTATE
#define com_artifex_mupdf_fitz_PDFWidget_IS_NO_ROTATE 16L
#undef com_artifex_mupdf_fitz_PDFWidget_IS_NO_VIEW
#define com_artifex_mupdf_fitz_PDFWidget_IS_NO_VIEW 32L
#undef com_artifex_mupdf_fitz_PDFWidget_IS_READ_ONLY
#define com_artifex_mupdf_fitz_PDFWidget_IS_READ_ONLY 64L
#undef com_artifex_mupdf_fitz_PDFWidget_IS_LOCKED
#define com_artifex_mupdf_fitz_PDFWidget_IS_LOCKED 128L
#undef com_artifex_mupdf_fitz_PDFWidget_IS_TOGGLE_NO_VIEW
#define com_artifex_mupdf_fitz_PDFWidget_IS_TOGGLE_NO_VIEW 256L
#undef com_artifex_mupdf_fitz_PDFWidget_IS_LOCKED_CONTENTS
#define com_artifex_mupdf_fitz_PDFWidget_IS_LOCKED_CONTENTS 512L
#undef com_artifex_mupdf_fitz_PDFWidget_IT_DEFAULT
#define com_artifex_mupdf_fitz_PDFWidget_IT_DEFAULT 0L
#undef com_artifex_mupdf_fitz_PDFWidget_IT_FREETEXT_CALLOUT
#define com_artifex_mupdf_fitz_PDFWidget_IT_FREETEXT_CALLOUT 1L
#undef com_artifex_mupdf_fitz_PDFWidget_IT_FREETEXT_TYPEWRITER
#define com_artifex_mupdf_fitz_PDFWidget_IT_FREETEXT_TYPEWRITER 2L
#undef com_artifex_mupdf_fitz_PDFWidget_IT_LINE_ARROW
#define com_artifex_mupdf_fitz_PDFWidget_IT_LINE_ARROW 3L
#undef com_artifex_mupdf_fitz_PDFWidget_IT_LINE_DIMENSION
#define com_artifex_mupdf_fitz_PDFWidget_IT_LINE_DIMENSION 4L
#undef com_artifex_mupdf_fitz_PDFWidget_IT_POLYLINE_DIMENSION
#define com_artifex_mupdf_fitz_PDFWidget_IT_POLYLINE_DIMENSION 5L
#undef com_artifex_mupdf_fitz_PDFWidget_IT_POLYGON_CLOUD
#define com_artifex_mupdf_fitz_PDFWidget_IT_POLYGON_CLOUD 6L
#undef com_artifex_mupdf_fitz_PDFWidget_IT_POLYGON_DIMENSION
#define com_artifex_mupdf_fitz_PDFWidget_IT_POLYGON_DIMENSION 7L
#undef com_artifex_mupdf_fitz_PDFWidget_IT_STAMP_IMAGE
#define com_artifex_mupdf_fitz_PDFWidget_IT_STAMP_IMAGE 8L
#undef com_artifex_mupdf_fitz_PDFWidget_IT_STAMP_SNAPSHOT
#define com_artifex_mupdf_fitz_PDFWidget_IT_STAMP_SNAPSHOT 9L
#undef com_artifex_mupdf_fitz_PDFWidget_IT_UNKNOWN
#define com_artifex_mupdf_fitz_PDFWidget_IT_UNKNOWN 255L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_UNKNOWN
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_UNKNOWN 0L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_BUTTON
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_BUTTON 1L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_CHECKBOX
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_CHECKBOX 2L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_COMBOBOX
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_COMBOBOX 3L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_LISTBOX
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_LISTBOX 4L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_RADIOBUTTON
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_RADIOBUTTON 5L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_SIGNATURE
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_SIGNATURE 6L
#undef com_artifex_mupdf_fitz_PDFWidget_TYPE_TEXT
#define com_artifex_mupdf_fitz_PDFWidget_TYPE_TEXT 7L
#undef com_artifex_mupdf_fitz_PDFWidget_TX_FORMAT_NONE
#define com_artifex_mupdf_fitz_PDFWidget_TX_FORMAT_NONE 0L
#undef com_artifex_mupdf_fitz_PDFWidget_TX_FORMAT_NUMBER
#define com_artifex_mupdf_fitz_PDFWidget_TX_FORMAT_NUMBER 1L
#undef com_artifex_mupdf_fitz_PDFWidget_TX_FORMAT_SPECIAL
#define com_artifex_mupdf_fitz_PDFWidget_TX_FORMAT_SPECIAL 2L
#undef com_artifex_mupdf_fitz_PDFWidget_TX_FORMAT_DATE
#define com_artifex_mupdf_fitz_PDFWidget_TX_FORMAT_DATE 3L
#undef com_artifex_mupdf_fitz_PDFWidget_TX_FORMAT_TIME
#define com_artifex_mupdf_fitz_PDFWidget_TX_FORMAT_TIME 4L
#undef com_artifex_mupdf_fitz_PDFWidget_FIELD_IS_READ_ONLY
#define com_artifex_mupdf_fitz_PDFWidget_FIELD_IS_READ_ONLY 1L
#undef com_artifex_mupdf_fitz_PDFWidget_FIELD_IS_REQUIRED
#define com_artifex_mupdf_fitz_PDFWidget_FIELD_IS_REQUIRED 2L
#undef com_artifex_mupdf_fitz_PDFWidget_FIELD_IS_NO_EXPORT
#define com_artifex_mupdf_fitz_PDFWidget_FIELD_IS_NO_EXPORT 4L
#undef com_artifex_mupdf_fitz_PDFWidget_TX_FIELD_IS_MULTILINE
#define com_artifex_mupdf_fitz_PDFWidget_TX_FIELD_IS_MULTILINE 4096L
#undef com_artifex_mupdf_fitz_PDFWidget_TX_FIELD_IS_PASSWORD
#define com_artifex_mupdf_fitz_PDFWidget_TX_FIELD_IS_PASSWORD 8192L
#undef com_artifex_mupdf_fitz_PDFWidget_TX_FIELD_IS_COMB
#define com_artifex_mupdf_fitz_PDFWidget_TX_FIELD_IS_COMB 16777216L
#undef com_artifex_mupdf_fitz_PDFWidget_BTN_FIELD_IS_NO_TOGGLE_TO_OFF
#define com_artifex_mupdf_fitz_PDFWidget_BTN_FIELD_IS_NO_TOGGLE_TO_OFF 16384L
#undef com_artifex_mupdf_fitz_PDFWidget_BTN_FIELD_IS_RADIO
#define com_artifex_mupdf_fitz_PDFWidget_BTN_FIELD_IS_RADIO 32768L
#undef com_artifex_mupdf_fitz_PDFWidget_BTN_FIELD_IS_PUSHBUTTON
#define com_artifex_mupdf_fitz_PDFWidget_BTN_FIELD_IS_PUSHBUTTON 65536L
#undef com_artifex_mupdf_fitz_PDFWidget_CH_FIELD_IS_COMBO
#define com_artifex_mupdf_fitz_PDFWidget_CH_FIELD_IS_COMBO 131072L
#undef com_artifex_mupdf_fitz_PDFWidget_CH_FIELD_IS_EDIT
#define com_artifex_mupdf_fitz_PDFWidget_CH_FIELD_IS_EDIT 262144L
#undef com_artifex_mupdf_fitz_PDFWidget_CH_FIELD_IS_SORT
#define com_artifex_mupdf_fitz_PDFWidget_CH_FIELD_IS_SORT 524288L
#undef com_artifex_mupdf_fitz_PDFWidget_CH_FIELD_IS_MULTI_SELECT
#define com_artifex_mupdf_fitz_PDFWidget_CH_FIELD_IS_MULTI_SELECT 2097152L
#undef com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_SHOW_LABELS
#define com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_SHOW_LABELS 1L
#undef com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_SHOW_DN
#define com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_SHOW_DN 2L
#undef com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_SHOW_DATE
#define com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_SHOW_DATE 4L
#undef com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_SHOW_TEXT_NAME
#define com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_SHOW_TEXT_NAME 8L
#undef com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_SHOW_GRAPHIC_NAME
#define com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_SHOW_GRAPHIC_NAME 16L
#undef com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_SHOW_LOGO
#define com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_SHOW_LOGO 32L
#undef com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_DEFAULT_APPEARANCE
#define com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_DEFAULT_APPEARANCE 63L
#undef com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_ERROR_OKAY
#define com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_ERROR_OKAY 0L
#undef com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_ERROR_NO_SIGNATURES
#define com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_ERROR_NO_SIGNATURES 1L
#undef com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_ERROR_NO_CERTIFICATE
#define com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_ERROR_NO_CERTIFICATE 2L
#undef com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_ERROR_DIGEST_FAILURE
#define com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_ERROR_DIGEST_FAILURE 3L
#undef com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_ERROR_SELF_SIGNED
#define com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_ERROR_SELF_SIGNED 4L
#undef com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_ERROR_SELF_SIGNED_IN_CHAIN
#define com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_ERROR_SELF_SIGNED_IN_CHAIN 5L
#undef com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_ERROR_NOT_TRUSTED
#define com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_ERROR_NOT_TRUSTED 6L
#undef com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_ERROR_NOT_SIGNED
#define com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_ERROR_NOT_SIGNED 7L
#undef com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_ERROR_UNKNOWN
#define com_artifex_mupdf_fitz_PDFWidget_SIGNATURE_ERROR_UNKNOWN 8L
/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    getValue
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_getValue
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    setValue
 * Signature: (Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_setValue
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    getLabel
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_getLabel
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    getName
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_getName
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    toggle
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_toggle
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    setTextValue
 * Signature: (Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_setTextValue
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    getEditingState
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_getEditingState
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    setEditingState
 * Signature: (Z)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_setEditingState
  (JNIEnv *, jobject, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    textQuads
 * Signature: ()[Lcom/artifex/mupdf/fitz/Quad;
 */
JNIEXPORT jobjectArray JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_textQuads
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    setEditing
 * Signature: (Z)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_setEditing
  (JNIEnv *, jobject, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    isEditing
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_isEditing
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    setChoiceValue
 * Signature: (Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_setChoiceValue
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    previewSignatureNative
 * Signature: (IIILcom/artifex/mupdf/fitz/PKCS7Signer;ILcom/artifex/mupdf/fitz/Image;Ljava/lang/String;Ljava/lang/String;)Lcom/artifex/mupdf/fitz/Pixmap;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_previewSignatureNative
  (JNIEnv *, jclass, jint, jint, jint, jobject, jint, jobject, jstring, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    signNative
 * Signature: (Lcom/artifex/mupdf/fitz/PKCS7Signer;ILcom/artifex/mupdf/fitz/Image;Ljava/lang/String;Ljava/lang/String;)Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_signNative
  (JNIEnv *, jobject, jobject, jint, jobject, jstring, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    checkCertificate
 * Signature: (Lcom/artifex/mupdf/fitz/PKCS7Verifier;)I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_checkCertificate
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    checkDigest
 * Signature: (Lcom/artifex/mupdf/fitz/PKCS7Verifier;)I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_checkDigest
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    incrementalChangeSinceSigning
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_incrementalChangeSinceSigning
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    getDistinguishedName
 * Signature: (Lcom/artifex/mupdf/fitz/PKCS7Verifier;)Lcom/artifex/mupdf/fitz/PKCS7DistinguishedName;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_getDistinguishedName
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    getSignatory
 * Signature: (Lcom/artifex/mupdf/fitz/PKCS7Verifier;)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_getSignatory
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    incrementalChangesSinceSigning
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_incrementalChangesSinceSigning
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    validateSignature
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_validateSignature
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    clearSignature
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_clearSignature
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    isSigned
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_isSigned
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PDFWidget
 * Method:    layoutTextWidget
 * Signature: ()Lcom/artifex/mupdf/fitz/PDFWidget/TextWidgetLayout;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_PDFWidget_layoutTextWidget
  (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_PKCS7Signer */

#ifndef _Included_com_artifex_mupdf_fitz_PKCS7Signer
#define _Included_com_artifex_mupdf_fitz_PKCS7Signer
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_artifex_mupdf_fitz_PKCS7Signer
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PKCS7Signer_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PKCS7Signer
 * Method:    newNative
 * Signature: (Lcom/artifex/mupdf/fitz/PKCS7Signer;)J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_PKCS7Signer_newNative
  (JNIEnv *, jobject, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_PKCS7Verifier */

#ifndef _Included_com_artifex_mupdf_fitz_PKCS7Verifier
#define _Included_com_artifex_mupdf_fitz_PKCS7Verifier
#ifdef __cplusplus
extern "C" {
#endif
#undef com_artifex_mupdf_fitz_PKCS7Verifier_PKCS7VerifierOK
#define com_artifex_mupdf_fitz_PKCS7Verifier_PKCS7VerifierOK 0L
#undef com_artifex_mupdf_fitz_PKCS7Verifier_PKCS7VerifierNoSignature
#define com_artifex_mupdf_fitz_PKCS7Verifier_PKCS7VerifierNoSignature 1L
#undef com_artifex_mupdf_fitz_PKCS7Verifier_PKCS7VerifierNoCertificate
#define com_artifex_mupdf_fitz_PKCS7Verifier_PKCS7VerifierNoCertificate 2L
#undef com_artifex_mupdf_fitz_PKCS7Verifier_PKCS7VerifierDigestFailure
#define com_artifex_mupdf_fitz_PKCS7Verifier_PKCS7VerifierDigestFailure 3L
#undef com_artifex_mupdf_fitz_PKCS7Verifier_PKCS7VerifierSelfSigned
#define com_artifex_mupdf_fitz_PKCS7Verifier_PKCS7VerifierSelfSigned 4L
#undef com_artifex_mupdf_fitz_PKCS7Verifier_PKCS7VerifierSelfSignedInChain
#define com_artifex_mupdf_fitz_PKCS7Verifier_PKCS7VerifierSelfSignedInChain 5L
#undef com_artifex_mupdf_fitz_PKCS7Verifier_PKCS7VerifierNotTrusted
#define com_artifex_mupdf_fitz_PKCS7Verifier_PKCS7VerifierNotTrusted 6L
#undef com_artifex_mupdf_fitz_PKCS7Verifier_PKCS7VerifierUnknown
#define com_artifex_mupdf_fitz_PKCS7Verifier_PKCS7VerifierUnknown -1L
/*
 * Class:     com_artifex_mupdf_fitz_PKCS7Verifier
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_PKCS7Verifier_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_PKCS7Verifier
 * Method:    newNative
 * Signature: (Lcom/artifex/mupdf/fitz/PKCS7Verifier;)J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_PKCS7Verifier_newNative
  (JNIEnv *, jobject, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_Page */

#ifndef _Included_com_artifex_mupdf_fitz_Page
#define _Included_com_artifex_mupdf_fitz_Page
#ifdef __cplusplus
extern "C" {
#endif
#undef com_artifex_mupdf_fitz_Page_MEDIA_BOX
#define com_artifex_mupdf_fitz_Page_MEDIA_BOX 0L
#undef com_artifex_mupdf_fitz_Page_CROP_BOX
#define com_artifex_mupdf_fitz_Page_CROP_BOX 1L
#undef com_artifex_mupdf_fitz_Page_BLEED_BOX
#define com_artifex_mupdf_fitz_Page_BLEED_BOX 2L
#undef com_artifex_mupdf_fitz_Page_TRIM_BOX
#define com_artifex_mupdf_fitz_Page_TRIM_BOX 3L
#undef com_artifex_mupdf_fitz_Page_ART_BOX
#define com_artifex_mupdf_fitz_Page_ART_BOX 4L
#undef com_artifex_mupdf_fitz_Page_UNKNOWN_BOX
#define com_artifex_mupdf_fitz_Page_UNKNOWN_BOX 5L
/*
 * Class:     com_artifex_mupdf_fitz_Page
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Page_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Page
 * Method:    getBoundsNative
 * Signature: (I)Lcom/artifex/mupdf/fitz/Rect;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Page_getBoundsNative
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Page
 * Method:    run
 * Signature: (Lcom/artifex/mupdf/fitz/Device;Lcom/artifex/mupdf/fitz/Matrix;Lcom/artifex/mupdf/fitz/Cookie;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Page_run
  (JNIEnv *, jobject, jobject, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Page
 * Method:    runPageContents
 * Signature: (Lcom/artifex/mupdf/fitz/Device;Lcom/artifex/mupdf/fitz/Matrix;Lcom/artifex/mupdf/fitz/Cookie;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Page_runPageContents
  (JNIEnv *, jobject, jobject, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Page
 * Method:    runPageAnnots
 * Signature: (Lcom/artifex/mupdf/fitz/Device;Lcom/artifex/mupdf/fitz/Matrix;Lcom/artifex/mupdf/fitz/Cookie;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Page_runPageAnnots
  (JNIEnv *, jobject, jobject, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Page
 * Method:    runPageWidgets
 * Signature: (Lcom/artifex/mupdf/fitz/Device;Lcom/artifex/mupdf/fitz/Matrix;Lcom/artifex/mupdf/fitz/Cookie;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Page_runPageWidgets
  (JNIEnv *, jobject, jobject, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Page
 * Method:    getLinks
 * Signature: ()[Lcom/artifex/mupdf/fitz/Link;
 */
JNIEXPORT jobjectArray JNICALL Java_com_artifex_mupdf_fitz_Page_getLinks
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Page
 * Method:    toPixmap
 * Signature: (Lcom/artifex/mupdf/fitz/Matrix;Lcom/artifex/mupdf/fitz/ColorSpace;ZZ)Lcom/artifex/mupdf/fitz/Pixmap;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Page_toPixmap
  (JNIEnv *, jobject, jobject, jobject, jboolean, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_Page
 * Method:    toDisplayList
 * Signature: (Z)Lcom/artifex/mupdf/fitz/DisplayList;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Page_toDisplayList
  (JNIEnv *, jobject, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_Page
 * Method:    toStructuredText
 * Signature: (Ljava/lang/String;)Lcom/artifex/mupdf/fitz/StructuredText;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Page_toStructuredText
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_Page
 * Method:    search
 * Signature: (Ljava/lang/String;)[[Lcom/artifex/mupdf/fitz/Quad;
 */
JNIEXPORT jobjectArray JNICALL Java_com_artifex_mupdf_fitz_Page_search
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_Page
 * Method:    textAsHtml
 * Signature: ()[B
 */
JNIEXPORT jbyteArray JNICALL Java_com_artifex_mupdf_fitz_Page_textAsHtml
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Page
 * Method:    getDocument
 * Signature: ()Lcom/artifex/mupdf/fitz/Document;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Page_getDocument
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Page
 * Method:    createLink
 * Signature: (Lcom/artifex/mupdf/fitz/Rect;Ljava/lang/String;)Lcom/artifex/mupdf/fitz/Link;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Page_createLink
  (JNIEnv *, jobject, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_Page
 * Method:    deleteLink
 * Signature: (Lcom/artifex/mupdf/fitz/Link;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Page_deleteLink
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Page
 * Method:    getLabel
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_Page_getLabel
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Page
 * Method:    decodeBarcode
 * Signature: (Lcom/artifex/mupdf/fitz/Rect;F)Lcom/artifex/mupdf/fitz/BarcodeInfo;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Page_decodeBarcode
  (JNIEnv *, jobject, jobject, jfloat);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_Path */

#ifndef _Included_com_artifex_mupdf_fitz_Path
#define _Included_com_artifex_mupdf_fitz_Path
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_artifex_mupdf_fitz_Path
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Path_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Path
 * Method:    newNative
 * Signature: ()J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_Path_newNative
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Path
 * Method:    cloneNative
 * Signature: ()J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_Path_cloneNative
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Path
 * Method:    currentPoint
 * Signature: ()Lcom/artifex/mupdf/fitz/Point;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Path_currentPoint
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Path
 * Method:    moveTo
 * Signature: (FF)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Path_moveTo
  (JNIEnv *, jobject, jfloat, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_Path
 * Method:    lineTo
 * Signature: (FF)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Path_lineTo
  (JNIEnv *, jobject, jfloat, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_Path
 * Method:    curveTo
 * Signature: (FFFFFF)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Path_curveTo
  (JNIEnv *, jobject, jfloat, jfloat, jfloat, jfloat, jfloat, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_Path
 * Method:    curveToV
 * Signature: (FFFF)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Path_curveToV
  (JNIEnv *, jobject, jfloat, jfloat, jfloat, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_Path
 * Method:    curveToY
 * Signature: (FFFF)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Path_curveToY
  (JNIEnv *, jobject, jfloat, jfloat, jfloat, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_Path
 * Method:    rect
 * Signature: (IIII)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Path_rect
  (JNIEnv *, jobject, jint, jint, jint, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Path
 * Method:    closePath
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Path_closePath
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Path
 * Method:    transform
 * Signature: (Lcom/artifex/mupdf/fitz/Matrix;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Path_transform
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Path
 * Method:    getBounds
 * Signature: (Lcom/artifex/mupdf/fitz/StrokeState;Lcom/artifex/mupdf/fitz/Matrix;)Lcom/artifex/mupdf/fitz/Rect;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Path_getBounds
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Path
 * Method:    walk
 * Signature: (Lcom/artifex/mupdf/fitz/PathWalker;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Path_walk
  (JNIEnv *, jobject, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_Pixmap */

#ifndef _Included_com_artifex_mupdf_fitz_Pixmap
#define _Included_com_artifex_mupdf_fitz_Pixmap
#ifdef __cplusplus
extern "C" {
#endif
#undef com_artifex_mupdf_fitz_Pixmap_DESKEW_BORDER_INCREASE
#define com_artifex_mupdf_fitz_Pixmap_DESKEW_BORDER_INCREASE 0L
#undef com_artifex_mupdf_fitz_Pixmap_DESKEW_BORDER_MAINTAIN
#define com_artifex_mupdf_fitz_Pixmap_DESKEW_BORDER_MAINTAIN 1L
#undef com_artifex_mupdf_fitz_Pixmap_DESKEW_BORDER_DECREASE
#define com_artifex_mupdf_fitz_Pixmap_DESKEW_BORDER_DECREASE 2L
/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Pixmap_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    newNative
 * Signature: (Lcom/artifex/mupdf/fitz/ColorSpace;IIIIZ)J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_Pixmap_newNative
  (JNIEnv *, jobject, jobject, jint, jint, jint, jint, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    newNativeFromColorAndMask
 * Signature: (Lcom/artifex/mupdf/fitz/Pixmap;Lcom/artifex/mupdf/fitz/Pixmap;)J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_Pixmap_newNativeFromColorAndMask
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    newNativeDeskew
 * Signature: (FI)J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_Pixmap_newNativeDeskew
  (JNIEnv *, jobject, jfloat, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    clear
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Pixmap_clear
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    clearWithValue
 * Signature: (I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Pixmap_clearWithValue
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    asPNG
 * Signature: ()Lcom/artifex/mupdf/fitz/Buffer;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Pixmap_asPNG
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    asJPEG
 * Signature: (IZ)Lcom/artifex/mupdf/fitz/Buffer;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Pixmap_asJPEG
  (JNIEnv *, jobject, jint, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    asPAM
 * Signature: ()Lcom/artifex/mupdf/fitz/Buffer;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Pixmap_asPAM
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    asPNM
 * Signature: ()Lcom/artifex/mupdf/fitz/Buffer;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Pixmap_asPNM
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    asPBM
 * Signature: ()Lcom/artifex/mupdf/fitz/Buffer;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Pixmap_asPBM
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    asPKM
 * Signature: ()Lcom/artifex/mupdf/fitz/Buffer;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Pixmap_asPKM
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    asJPX
 * Signature: (I)Lcom/artifex/mupdf/fitz/Buffer;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Pixmap_asJPX
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    saveAsPNG
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Pixmap_saveAsPNG
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    saveAsJPEG
 * Signature: (Ljava/lang/String;I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Pixmap_saveAsJPEG
  (JNIEnv *, jobject, jstring, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    saveAsPAM
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Pixmap_saveAsPAM
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    saveAsPNM
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Pixmap_saveAsPNM
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    saveAsPBM
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Pixmap_saveAsPBM
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    saveAsPKM
 * Signature: (Ljava/lang/String;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Pixmap_saveAsPKM
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    saveAsJPX
 * Signature: (Ljava/lang/String;I)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Pixmap_saveAsJPX
  (JNIEnv *, jobject, jstring, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    getX
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Pixmap_getX
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    getY
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Pixmap_getY
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    getWidth
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Pixmap_getWidth
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    getHeight
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Pixmap_getHeight
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    getStride
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Pixmap_getStride
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    getNumberOfComponents
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Pixmap_getNumberOfComponents
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    getAlpha
 * Signature: ()Z
 */
JNIEXPORT jboolean JNICALL Java_com_artifex_mupdf_fitz_Pixmap_getAlpha
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    getColorSpace
 * Signature: ()Lcom/artifex/mupdf/fitz/ColorSpace;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Pixmap_getColorSpace
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    getSamples
 * Signature: ()[B
 */
JNIEXPORT jbyteArray JNICALL Java_com_artifex_mupdf_fitz_Pixmap_getSamples
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    getSample
 * Signature: (III)B
 */
JNIEXPORT jbyte JNICALL Java_com_artifex_mupdf_fitz_Pixmap_getSample
  (JNIEnv *, jobject, jint, jint, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    getPixels
 * Signature: ()[I
 */
JNIEXPORT jintArray JNICALL Java_com_artifex_mupdf_fitz_Pixmap_getPixels
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    getXResolution
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Pixmap_getXResolution
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    getYResolution
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Pixmap_getYResolution
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    setResolution
 * Signature: (II)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Pixmap_setResolution
  (JNIEnv *, jobject, jint, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    invert
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Pixmap_invert
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    invertLuminance
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Pixmap_invertLuminance
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    gamma
 * Signature: (F)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Pixmap_gamma
  (JNIEnv *, jobject, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    tint
 * Signature: (II)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Pixmap_tint
  (JNIEnv *, jobject, jint, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    convertToColorSpace
 * Signature: (Lcom/artifex/mupdf/fitz/ColorSpace;Lcom/artifex/mupdf/fitz/ColorSpace;Lcom/artifex/mupdf/fitz/DefaultColorSpaces;IZ)Lcom/artifex/mupdf/fitz/Pixmap;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Pixmap_convertToColorSpace
  (JNIEnv *, jobject, jobject, jobject, jobject, jint, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    computeMD5
 * Signature: ()[B
 */
JNIEXPORT jbyteArray JNICALL Java_com_artifex_mupdf_fitz_Pixmap_computeMD5
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    detectSkew
 * Signature: ()F
 */
JNIEXPORT jfloat JNICALL Java_com_artifex_mupdf_fitz_Pixmap_detectSkew
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    warp
 * Signature: (Lcom/artifex/mupdf/fitz/Quad;II)Lcom/artifex/mupdf/fitz/Pixmap;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Pixmap_warp
  (JNIEnv *, jobject, jobject, jint, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    autowarp
 * Signature: (Lcom/artifex/mupdf/fitz/Quad;)Lcom/artifex/mupdf/fitz/Pixmap;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Pixmap_autowarp
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    detectDocument
 * Signature: ()Lcom/artifex/mupdf/fitz/Quad;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Pixmap_detectDocument
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    decodeBarcode
 * Signature: (F)Lcom/artifex/mupdf/fitz/BarcodeInfo;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Pixmap_decodeBarcode
  (JNIEnv *, jobject, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_Pixmap
 * Method:    encodeBarcode
 * Signature: (ILjava/lang/String;IIZZ)Lcom/artifex/mupdf/fitz/Pixmap;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Pixmap_encodeBarcode
  (JNIEnv *, jclass, jint, jstring, jint, jint, jboolean, jboolean);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_Rect */

#ifndef _Included_com_artifex_mupdf_fitz_Rect
#define _Included_com_artifex_mupdf_fitz_Rect
#ifdef __cplusplus
extern "C" {
#endif
#undef com_artifex_mupdf_fitz_Rect_MIN_INF_RECT
#define com_artifex_mupdf_fitz_Rect_MIN_INF_RECT -2147483648L
#undef com_artifex_mupdf_fitz_Rect_MAX_INF_RECT
#define com_artifex_mupdf_fitz_Rect_MAX_INF_RECT 2147483520L
/*
 * Class:     com_artifex_mupdf_fitz_Rect
 * Method:    adjustForStroke
 * Signature: (Lcom/artifex/mupdf/fitz/StrokeState;Lcom/artifex/mupdf/fitz/Matrix;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Rect_adjustForStroke
  (JNIEnv *, jobject, jobject, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_Shade */

#ifndef _Included_com_artifex_mupdf_fitz_Shade
#define _Included_com_artifex_mupdf_fitz_Shade
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_artifex_mupdf_fitz_Shade
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Shade_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Shade
 * Method:    getBounds
 * Signature: (Lcom/artifex/mupdf/fitz/Matrix;)Lcom/artifex/mupdf/fitz/Rect;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Shade_getBounds
  (JNIEnv *, jobject, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_Story */

#ifndef _Included_com_artifex_mupdf_fitz_Story
#define _Included_com_artifex_mupdf_fitz_Story
#ifdef __cplusplus
extern "C" {
#endif
#undef com_artifex_mupdf_fitz_Story_FLAGS_NO_OVERFLOW
#define com_artifex_mupdf_fitz_Story_FLAGS_NO_OVERFLOW 1L
#undef com_artifex_mupdf_fitz_Story_ALL_FITTED
#define com_artifex_mupdf_fitz_Story_ALL_FITTED 0L
#undef com_artifex_mupdf_fitz_Story_OVERFLOW_WIDTH
#define com_artifex_mupdf_fitz_Story_OVERFLOW_WIDTH 2L
/*
 * Class:     com_artifex_mupdf_fitz_Story
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Story_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Story
 * Method:    newStory
 * Signature: ([B[BFLcom/artifex/mupdf/fitz/Archive;)J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_Story_newStory
  (JNIEnv *, jclass, jbyteArray, jbyteArray, jfloat, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Story
 * Method:    place
 * Signature: (Lcom/artifex/mupdf/fitz/Rect;Lcom/artifex/mupdf/fitz/Rect;I)I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_Story_place
  (JNIEnv *, jobject, jobject, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_Story
 * Method:    draw
 * Signature: (Lcom/artifex/mupdf/fitz/Device;Lcom/artifex/mupdf/fitz/Matrix;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Story_draw
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Story
 * Method:    document
 * Signature: ()Lcom/artifex/mupdf/fitz/DOM;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Story_document
  (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_StrokeState */

#ifndef _Included_com_artifex_mupdf_fitz_StrokeState
#define _Included_com_artifex_mupdf_fitz_StrokeState
#ifdef __cplusplus
extern "C" {
#endif
#undef com_artifex_mupdf_fitz_StrokeState_LINE_CAP_BUTT
#define com_artifex_mupdf_fitz_StrokeState_LINE_CAP_BUTT 0L
#undef com_artifex_mupdf_fitz_StrokeState_LINE_CAP_ROUND
#define com_artifex_mupdf_fitz_StrokeState_LINE_CAP_ROUND 1L
#undef com_artifex_mupdf_fitz_StrokeState_LINE_CAP_SQUARE
#define com_artifex_mupdf_fitz_StrokeState_LINE_CAP_SQUARE 2L
#undef com_artifex_mupdf_fitz_StrokeState_LINE_CAP_TRIANGLE
#define com_artifex_mupdf_fitz_StrokeState_LINE_CAP_TRIANGLE 3L
#undef com_artifex_mupdf_fitz_StrokeState_LINE_JOIN_MITER
#define com_artifex_mupdf_fitz_StrokeState_LINE_JOIN_MITER 0L
#undef com_artifex_mupdf_fitz_StrokeState_LINE_JOIN_ROUND
#define com_artifex_mupdf_fitz_StrokeState_LINE_JOIN_ROUND 1L
#undef com_artifex_mupdf_fitz_StrokeState_LINE_JOIN_BEVEL
#define com_artifex_mupdf_fitz_StrokeState_LINE_JOIN_BEVEL 2L
#undef com_artifex_mupdf_fitz_StrokeState_LINE_JOIN_MITER_XPS
#define com_artifex_mupdf_fitz_StrokeState_LINE_JOIN_MITER_XPS 3L
/*
 * Class:     com_artifex_mupdf_fitz_StrokeState
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_StrokeState_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_StrokeState
 * Method:    newNativeStrokeState
 * Signature: (IIFFF[F)J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_StrokeState_newNativeStrokeState
  (JNIEnv *, jobject, jint, jint, jfloat, jfloat, jfloat, jfloatArray);

/*
 * Class:     com_artifex_mupdf_fitz_StrokeState
 * Method:    getStartCap
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_StrokeState_getStartCap
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_StrokeState
 * Method:    getDashCap
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_StrokeState_getDashCap
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_StrokeState
 * Method:    getEndCap
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_StrokeState_getEndCap
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_StrokeState
 * Method:    getLineJoin
 * Signature: ()I
 */
JNIEXPORT jint JNICALL Java_com_artifex_mupdf_fitz_StrokeState_getLineJoin
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_StrokeState
 * Method:    getLineWidth
 * Signature: ()F
 */
JNIEXPORT jfloat JNICALL Java_com_artifex_mupdf_fitz_StrokeState_getLineWidth
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_StrokeState
 * Method:    getMiterLimit
 * Signature: ()F
 */
JNIEXPORT jfloat JNICALL Java_com_artifex_mupdf_fitz_StrokeState_getMiterLimit
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_StrokeState
 * Method:    getDashPhase
 * Signature: ()F
 */
JNIEXPORT jfloat JNICALL Java_com_artifex_mupdf_fitz_StrokeState_getDashPhase
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_StrokeState
 * Method:    getDashPattern
 * Signature: ()[F
 */
JNIEXPORT jfloatArray JNICALL Java_com_artifex_mupdf_fitz_StrokeState_getDashPattern
  (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_StructuredText */

#ifndef _Included_com_artifex_mupdf_fitz_StructuredText
#define _Included_com_artifex_mupdf_fitz_StructuredText
#ifdef __cplusplus
extern "C" {
#endif
#undef com_artifex_mupdf_fitz_StructuredText_SELECT_CHARS
#define com_artifex_mupdf_fitz_StructuredText_SELECT_CHARS 0L
#undef com_artifex_mupdf_fitz_StructuredText_SELECT_WORDS
#define com_artifex_mupdf_fitz_StructuredText_SELECT_WORDS 1L
#undef com_artifex_mupdf_fitz_StructuredText_SELECT_LINES
#define com_artifex_mupdf_fitz_StructuredText_SELECT_LINES 2L
#undef com_artifex_mupdf_fitz_StructuredText_VECTOR_IS_STROKED
#define com_artifex_mupdf_fitz_StructuredText_VECTOR_IS_STROKED 1L
#undef com_artifex_mupdf_fitz_StructuredText_VECTOR_IS_RECTANGLE
#define com_artifex_mupdf_fitz_StructuredText_VECTOR_IS_RECTANGLE 2L
/*
 * Class:     com_artifex_mupdf_fitz_StructuredText
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_StructuredText_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_StructuredText
 * Method:    search
 * Signature: (Ljava/lang/String;)[[Lcom/artifex/mupdf/fitz/Quad;
 */
JNIEXPORT jobjectArray JNICALL Java_com_artifex_mupdf_fitz_StructuredText_search
  (JNIEnv *, jobject, jstring);

/*
 * Class:     com_artifex_mupdf_fitz_StructuredText
 * Method:    highlight
 * Signature: (Lcom/artifex/mupdf/fitz/Point;Lcom/artifex/mupdf/fitz/Point;)[Lcom/artifex/mupdf/fitz/Quad;
 */
JNIEXPORT jobjectArray JNICALL Java_com_artifex_mupdf_fitz_StructuredText_highlight
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_StructuredText
 * Method:    snapSelection
 * Signature: (Lcom/artifex/mupdf/fitz/Point;Lcom/artifex/mupdf/fitz/Point;I)Lcom/artifex/mupdf/fitz/Quad;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_StructuredText_snapSelection
  (JNIEnv *, jobject, jobject, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_StructuredText
 * Method:    copy
 * Signature: (Lcom/artifex/mupdf/fitz/Point;Lcom/artifex/mupdf/fitz/Point;)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_StructuredText_copy
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_StructuredText
 * Method:    walk
 * Signature: (Lcom/artifex/mupdf/fitz/StructuredTextWalker;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_StructuredText_walk
  (JNIEnv *, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_StructuredText
 * Method:    asJSON
 * Signature: (F)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_StructuredText_asJSON
  (JNIEnv *, jobject, jfloat);

/*
 * Class:     com_artifex_mupdf_fitz_StructuredText
 * Method:    asHTML
 * Signature: (I)Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_StructuredText_asHTML
  (JNIEnv *, jobject, jint);

/*
 * Class:     com_artifex_mupdf_fitz_StructuredText
 * Method:    asText
 * Signature: ()Ljava/lang/String;
 */
JNIEXPORT jstring JNICALL Java_com_artifex_mupdf_fitz_StructuredText_asText
  (JNIEnv *, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_Text */

#ifndef _Included_com_artifex_mupdf_fitz_Text
#define _Included_com_artifex_mupdf_fitz_Text
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_artifex_mupdf_fitz_Text
 * Method:    finalize
 * Signature: ()V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Text_finalize
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Text
 * Method:    newNative
 * Signature: ()J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_Text_newNative
  (JNIEnv *, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Text
 * Method:    showGlyph
 * Signature: (Lcom/artifex/mupdf/fitz/Font;Lcom/artifex/mupdf/fitz/Matrix;IIZ)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Text_showGlyph
  (JNIEnv *, jobject, jobject, jobject, jint, jint, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_Text
 * Method:    showString
 * Signature: (Lcom/artifex/mupdf/fitz/Font;Lcom/artifex/mupdf/fitz/Matrix;Ljava/lang/String;Z)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Text_showString
  (JNIEnv *, jobject, jobject, jobject, jstring, jboolean);

/*
 * Class:     com_artifex_mupdf_fitz_Text
 * Method:    getBounds
 * Signature: (Lcom/artifex/mupdf/fitz/StrokeState;Lcom/artifex/mupdf/fitz/Matrix;)Lcom/artifex/mupdf/fitz/Rect;
 */
JNIEXPORT jobject JNICALL Java_com_artifex_mupdf_fitz_Text_getBounds
  (JNIEnv *, jobject, jobject, jobject);

/*
 * Class:     com_artifex_mupdf_fitz_Text
 * Method:    walk
 * Signature: (Lcom/artifex/mupdf/fitz/TextWalker;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_Text_walk
  (JNIEnv *, jobject, jobject);

#ifdef __cplusplus
}
#endif
#endif
/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class com_artifex_mupdf_fitz_TreeArchive */

#ifndef _Included_com_artifex_mupdf_fitz_TreeArchive
#define _Included_com_artifex_mupdf_fitz_TreeArchive
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     com_artifex_mupdf_fitz_TreeArchive
 * Method:    newNativeTreeArchive
 * Signature: ()J
 */
JNIEXPORT jlong JNICALL Java_com_artifex_mupdf_fitz_TreeArchive_newNativeTreeArchive
  (JNIEnv *, jclass);

/*
 * Class:     com_artifex_mupdf_fitz_TreeArchive
 * Method:    add
 * Signature: (Ljava/lang/String;Lcom/artifex/mupdf/fitz/Buffer;)V
 */
JNIEXPORT void JNICALL Java_com_artifex_mupdf_fitz_TreeArchive_add
  (JNIEnv *, jobject, jstring, jobject);

#ifdef __cplusplus
}
#endif
#endif
