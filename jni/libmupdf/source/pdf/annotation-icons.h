/*
These 8x8 icons have been converted from the MIT licensed
https://github.com/iconic/open-iconic
Copyright (c) 2014 Waybury
 */

static const char *icon_comment =
	".09 0 m\n"
	".03 0 0 .04 0 .09 c\n"
	"0 5.9 l\n"
	"0 5.95 .04 6 .09 6 c\n"
	"6 6 l\n"
	"8 8 l\n"
	"8 .08 l\n"
	"8 .02 7.96 -.01 7.91 -.01 c\n"
	".1 -.01 l\n"
	"h\n"
	"f\n";

static const char *icon_key =
	"5.5 0 m\n"
	"4.12 0 3 1.12 3 2.5 c\n"
	"3 2.66 3 2.82 3.03 2.97 c\n"
	"0 6 l\n"
	"0 8 l\n"
	"3 8 l\n"
	"3 6 l\n"
	"5 6 l\n"
	"5 5 l\n"
	"5.03 4.97 l\n"
	"5.18 5 5.34 5 5.5 5 c\n"
	"6.88 5 8 3.88 8 2.5 c\n"
	"8 1.12 6.88 0 5.5 0 c\n"
	"h\n"
	"6 1 m\n"
	"6.55 1 7 1.45 7 2 c\n"
	"7 2.55 6.55 3 6 3 c\n"
	"5.45 3 5 2.55 5 2 c\n"
	"5 1.45 5.45 1 6 1 c\n"
	"h\n"
	"f\n";

static const char *icon_note =
	"0 0 8 1 re\n"
	"0 2 8 1 re\n"
	"0 4 8 1 re\n"
	"0 6 8 1 re\n"
	"f\n";

static const char *icon_help =
	"1 0 0 1 1 0 cm\n"
	"2.47 0 m\n"
	"1.62 0 .99 .26 .59 .66 c\n"
	".19 1.06 .05 1.56 0 1.94 c\n"
	"1 2.07 l\n"
	"1.04 1.82 1.12 1.57 1.31 1.38 c\n"
	"1.50 1.19 1.80 1 2.47 1.00 c\n"
	"3.13 1 3.49 1.16 3.69 1.34 c\n"
	"3.89 1.52 3.97 1.74 3.97 2 c\n"
	"3.97 2.83 3.63 3.06 3.13 3.50 c\n"
	"2.63 3.94 1.97 4.58 1.97 5.75 c\n"
	"1.97 6 l\n"
	"2.97 6 l\n"
	"2.97 5.75 l\n"
	"2.97 4.92 3.28 4.69 3.78 4.25 c\n"
	"4.28 3.81 4.97 3.17 4.97 2 c\n"
	"4.97 1.52 4.80 .98 4.38 .59 c\n"
	"3.95 .20 3.31 0 2.47 .00 c\n"
	"h\n"
	"1.97 7 m\n"
	"1.97 8 l\n"
	"2.97 8 l\n"
	"2.97 7 l\n"
	"1.97 7 l\n"
	"h\n"
	"f\n";

static const char *icon_graph =
	"7.03 0 m\n"
	"4 3 l\n"
	"3 2 l\n"
	"0 5.03 l\n"
	"1 6.03 l\n"
	"3 4 l\n"
	"4 5 l\n"
	"8 1 l\n"
	"7.03 0 l\n"
	"h\n"
	"0 7 m\n"
	"0 8 l\n"
	"8 8 l\n"
	"8 7 l\n"
	"0 7 l\n"
	"h\n"
	"f\n";

static const char *icon_push_pin =
	"1.34 0 m\n"
	".92 .04 .76 .64 1.1 .89 c\n"
	"1.34 1.08 1.65 .97 1.93 1 c\n"
	"2.08 .98 1.96 1.22 2 1.32 c\n"
	"2 1.88 2 2.44 2 3 c\n"
	"1.6 3.01 1.2 2.98 .8 3.02 c\n"
	".35 3.11 -.01 3.54 0 4 c\n"
	"1 4 2 4 3 4 c\n"
	"3 5 3 6 3 7 c\n"
	"3.146 7.33 3.29 7.67 3.44 8 c\n"
	"3.62 7.66 3.83 7.32 4 6.98 c\n"
	"4 5.99 4 4.99 4 4 c\n"
	"5 4 6 4 7 4 c\n"
	"7.02 3.42 6.46 2.94 5.89 3 c\n"
	"5.6 3 5.3 3 5 3 c\n"
	"5 2.33 5 1.67 5 1 c\n"
	"5.30 .98 5.67 1.09 5.89 .81 c\n"
	"6.16 .5 5.89 -.038 5.48 0 c\n"
	"4.15 0 2.83 0 1.5 0 c\n"
	"h\n"
	"f\n";

static const char *icon_paperclip =
	"5 0 m\n"
	"4.49 0 3.98 .21 3.59 .59 c\n"
	".81 3.31 l\n"
	"-.26 4.38 -.26 6.11 .81 7.19 c\n"
	"1.88 8.26 3.61 8.26 4.69 7.19 c\n"
	"5.94 5.94 l\n"
	"5.25 5.25 l\n"
	"4.09 6.38 l\n"
	"4 6.51 l\n"
	"3.31 7.2 2.19 7.2 1.5 6.51 c\n"
	".82 5.83 .84 4.73 1.5 4.04 c\n"
	"4.28 1.29 l\n"
	"4.67 .9 5.32 .9 5.72 1.29 c\n"
	"6.11 1.68 6.09 2.3 5.72 2.7 c\n"
	"3.22 5.17 l\n"
	"3.12 5.27 2.95 5.27 2.84 5.17 c\n"
	"2.74 5.07 2.74 4.9 2.84 4.79 c\n"
	"2.9 4.76 l\n"
	"3.81 3.82 l\n"
	"3.12 3.13 l\n"
	"2.15 4.1 l\n"
	"1.67 4.58 1.67 5.37 2.15 5.85 c\n"
	"2.63 6.33 3.42 6.34 3.9 5.85 c\n"
	"6.4 3.41 l\n"
	"7.18 2.63 7.18 1.37 6.4 .6 c\n"
	"6.01 .21 5.51 .01 4.99 .01 c\n"
	"h\n"
	"f\n";

static const char *icon_tag =
	"0 0 m\n"
	"0 3 l\n"
	"5 8 l\n"
	"8 5 l\n"
	"3 0 l\n"
	"0 0 l\n"
	"h\n"
	"2 1 m\n"
	"2.55 1 3 1.45 3 2 c\n"
	"3 2.55 2.55 3 2 3 c\n"
	"1.45 3 1 2.55 1 2 c\n"
	"1 1.45 1.45 1 2 1 c\n"
	"h\n"
	"f\n";

static const char *icon_speaker =
	"3.34 0 m\n"
	"2 2 l\n"
	"0 2 l\n"
	"0 6 l\n"
	"2 6 l\n"
	"3.34 8 l\n"
	"4 8 l\n"
	"4 0 l\n"
	"3.34 0 l\n"
	"h\n"
	"5 1 m\n"
	"5 2 l\n"
	"5.17 2 5.34 2.02 5.5 2.06 c\n"
	"6.36 2.28 7 3.06 7 4 c\n"
	"7 4.94 6.37 5.72 5.5 5.94 c\n"
	"5.34 5.98 5.17 6 5 6 c\n"
	"5 7 l\n"
	"5.25 7 5.48 6.96 5.72 6.91 c\n"
	"5.75 6.91 l\n"
	"7.05 6.58 8 5.4 8 4 c\n"
	"8 2.6 7.05 1.42 5.75 1.09 c\n"
	"5.52 1.03 5.26 1 5 1 c\n"
	"h\n"
	"5 3 m\n"
	"5 5 l\n"
	"5.09 5 5.18 4.99 5.25 4.97 c\n"
	"5.68 4.86 6 4.46 6 4 c\n"
	"6 3.54 5.69 3.14 5.25 3.03 c\n"
	"5.17 3.01 5.08 3 5 3 c\n"
	"h\n"
	"f\n";

static const char *icon_mic =
	"1 0 0 1 1 0 cm\n"
	"2.91 -.03 m\n"
	"2.49 .03 2.11 .34 2.02 .76 c\n"
	"1.97 1.12 2.01 1.48 2 1.84 c\n"
	"2.01 2.29 1.98 2.73 2.02 3.17 c\n"
	"2.1 3.72 2.68 4.11 3.21 3.98 c\n"
	"3.7 3.89 4.05 3.39 4 2.9 c\n"
	"4 2.21 4.01 1.53 3.99 .84 c\n"
	"3.94 .32 3.43 -.09 2.91 -.03 c\n"
	"h\n"
	".34 2 m\n"
	".1 2.07 -.04 2.34 - 2.58 c\n"
	"-.02 3.18 .03 3.81 .32 4.34 c\n"
	".75 5.18 1.58 5.78 2.5 5.94 c\n"
	"2.5 6.29 2.5 6.65 2.5 7 c\n"
	"2.11 7.01 1.68 6.94 1.36 7.23 c\n"
	"1.14 7.41 .96 7.75 1.02 8 c\n"
	"2.35 8 3.67 8 5 8 c\n"
	"5.02 7.43 4.47 6.94 3.9 7 c\n"
	"3.77 7 3.63 7 3.5 7 c\n"
	"3.5 6.65 3.5 6.29 3.5 5.94 c\n"
	"4.82 5.73 5.92 4.55 5.99 3.21 c\n"
	"5.99 2.93 6.02 2.65 5.98 2.37 c\n"
	"5.91 2.05 5.49 1.89 5.23 2.08 c\n"
	"5.03 2.2 4.97 2.45 5 2.66 c\n"
	"5.02 3.15 4.97 3.66 4.69 4.08 c\n"
	"4.18 4.9 3.05 5.24 2.17 4.82 c\n"
	"1.42 4.5 .94 3.67 1 2.86 c\n"
	".99 2.61 1.05 2.32 .85 2.13 c\n"
	".71 1.99 .52 1.98 .34 2 c\n"
	"h\n"
	"f\n";

static const char *icon_star =
	"4 0 m\n"
	"3 3 l\n"
	"0 3 l\n"
	"2.5 5 l\n"
	"1.5 8 l\n"
	"4 6 l\n"
	"6.5 8 l\n"
	"5.5 5 l\n"
	"8 3 l\n"
	"5 3 l\n"
	"4 0 l\n"
	"h\n"
	"f\n";

/*
These 8x8 icons have been converted from the MIT licensed
https://github.com/michaelampr/jam
Copyright (c) 2017 Michael Amprimo
*/

static const char *icon_insert =
	"8 5 m\n"
	"4 0 l\n"
	"0 5 l\n"
	"f\n";

static const char *icon_new_paragraph =
	"8 8 m\n"
	"4 0 l\n"
	"0 8 l\n"
	"f\n";

static const char *icon_paragraph =
	"7 0 m\n"
	"2 0 l\n"
	"1 0 0 1 0 2 c\n"
	"0 3 1 4 2 4 c\n"
	"3 4 l\n"
	"3 8 l\n"
	"4 8 l\n"
	"4 1 l\n"
	"5 1 l\n"
	"5 8 l\n"
	"6 8 l\n"
	"6 1 l\n"
	"7 1 l\n"
	"f\n";
