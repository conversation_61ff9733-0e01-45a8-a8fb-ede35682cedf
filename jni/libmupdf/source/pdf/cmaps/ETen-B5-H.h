/* This is an automatically generated file. Do not edit. */

/* ETen-B5-H */

static const pdf_range cmap_ETen_B5_H_ranges[] = {
{0x20,0x7e,0x3550},
{0xa140,0xa158,0x63},
{0xa159,0xa15c,0x35af},
{0xa15d,0xa17e,0x80},
{0xa1a1,0xa1f5,0xa2},
{0xa1f6,0xa1f6,0xf8},
{0xa1f7,0xa1f7,0xf7},
{0xa1f8,0xa1fe,0xf9},
{0xa240,0xa27e,0x100},
{0xa2a1,0xa2fe,0x13f},
{0xa340,0xa37e,0x19d},
{0xa3a1,0xa3bb,0x1dc},
{0xa3bd,0xa3bf,0x1f7},
{0xa440,0xa47e,0x253},
{0xa4a1,0xa4fe,0x292},
{0xa540,0xa57e,0x2f0},
{0xa5a1,0xa5fe,0x32f},
{0xa640,0xa67e,0x38d},
{0xa6a1,0xa6fe,0x3cc},
{0xa740,0xa77e,0x42a},
{0xa7a1,0xa7fe,0x469},
{0xa840,0xa87e,0x4c7},
{0xa8a1,0xa8fe,0x506},
{0xa940,0xa97e,0x564},
{0xa9a1,0xa9fe,0x5a3},
{0xaa40,0xaa7e,0x601},
{0xaaa1,0xaafe,0x640},
{0xab40,0xab7e,0x69e},
{0xaba1,0xabfe,0x6dd},
{0xac40,0xac7e,0x73b},
{0xaca1,0xacfd,0x77a},
{0xacfe,0xacfe,0x97f},
{0xad40,0xad7e,0x7d7},
{0xada1,0xadfe,0x816},
{0xae40,0xae7e,0x874},
{0xaea1,0xaefe,0x8b3},
{0xaf40,0xaf7e,0x911},
{0xafa1,0xafcf,0x950},
{0xafd0,0xaffe,0x980},
{0xb040,0xb07e,0x9af},
{0xb0a1,0xb0fe,0x9ee},
{0xb140,0xb17e,0xa4c},
{0xb1a1,0xb1fe,0xa8b},
{0xb240,0xb27e,0xae9},
{0xb2a1,0xb2fe,0xb28},
{0xb340,0xb37e,0xb86},
{0xb3a1,0xb3fe,0xbc5},
{0xb440,0xb47e,0xc23},
{0xb4a1,0xb4fe,0xc62},
{0xb540,0xb57e,0xcc0},
{0xb5a1,0xb5fe,0xcff},
{0xb640,0xb67e,0xd5d},
{0xb6a1,0xb6fe,0xd9c},
{0xb740,0xb77e,0xdfa},
{0xb7a1,0xb7fe,0xe39},
{0xb840,0xb87e,0xe97},
{0xb8a1,0xb8fe,0xed6},
{0xb940,0xb97e,0xf34},
{0xb9a1,0xb9fe,0xf73},
{0xba40,0xba7e,0xfd1},
{0xbaa1,0xbafe,0x1010},
{0xbb40,0xbb7e,0x106e},
{0xbba1,0xbbc7,0x10ad},
{0xbbc8,0xbbfe,0x10d5},
{0xbc40,0xbc7e,0x110c},
{0xbca1,0xbcfe,0x114b},
{0xbd40,0xbd7e,0x11a9},
{0xbda1,0xbdfe,0x11e8},
{0xbe40,0xbe51,0x1246},
{0xbe52,0xbe52,0x10d4},
{0xbe53,0xbe7e,0x1258},
{0xbea1,0xbefe,0x1284},
{0xbf40,0xbf7e,0x12e2},
{0xbfa1,0xbffe,0x1321},
{0xc040,0xc07e,0x137f},
{0xc0a1,0xc0fe,0x13be},
{0xc140,0xc17e,0x141c},
{0xc1a1,0xc1aa,0x145b},
{0xc1ab,0xc1fe,0x1466},
{0xc240,0xc27e,0x14ba},
{0xc2a1,0xc2ca,0x14f9},
{0xc2cb,0xc2cb,0x1465},
{0xc2cc,0xc2fe,0x1523},
{0xc340,0xc360,0x1556},
{0xc361,0xc37e,0x1578},
{0xc3a1,0xc3b8,0x1596},
{0xc3b9,0xc3b9,0x15af},
{0xc3ba,0xc3ba,0x15ae},
{0xc3bb,0xc3fe,0x15b0},
{0xc440,0xc455,0x15f4},
{0xc456,0xc456,0x1577},
{0xc457,0xc47e,0x160a},
{0xc4a1,0xc4fe,0x1632},
{0xc540,0xc57e,0x1690},
{0xc5a1,0xc5fe,0x16cf},
{0xc640,0xc67e,0x172d},
{0xc6a1,0xc6be,0x1fa},
{0xc6bf,0xc6d7,0x219},
{0xc6d8,0xc6de,0x35b3},
{0xc6df,0xc6df,0x1794},
{0xc6e0,0xc6fe,0x35ba},
{0xc740,0xc77e,0x35d9},
{0xc7a1,0xc7fe,0x3618},
{0xc840,0xc87e,0x3676},
{0xc8a1,0xc8d3,0x36b5},
{0xc940,0xc949,0x176c},
{0xc94a,0xc94a,0x274},
{0xc94b,0xc96b,0x1776},
{0xc96c,0xc97e,0x1798},
{0xc9a1,0xc9bd,0x17ab},
{0xc9be,0xc9be,0x1797},
{0xc9bf,0xc9ec,0x17c8},
{0xc9ed,0xc9fe,0x17f7},
{0xca40,0xca7e,0x1809},
{0xcaa1,0xcaf6,0x1848},
{0xcaf7,0xcaf7,0x17f6},
{0xcaf8,0xcafe,0x189e},
{0xcb40,0xcb7e,0x18a5},
{0xcba1,0xcbfe,0x18e4},
{0xcc40,0xcc7e,0x1942},
{0xcca1,0xccfe,0x1981},
{0xcd40,0xcd7e,0x19df},
{0xcda1,0xcdfe,0x1a1e},
{0xce40,0xce7e,0x1a7c},
{0xcea1,0xcefe,0x1abb},
{0xcf40,0xcf7e,0x1b19},
{0xcfa1,0xcffe,0x1b58},
{0xd040,0xd07e,0x1bb6},
{0xd0a1,0xd0fe,0x1bf5},
{0xd140,0xd17e,0x1c53},
{0xd1a1,0xd1fe,0x1c92},
{0xd240,0xd27e,0x1cf0},
{0xd2a1,0xd2fe,0x1d2f},
{0xd340,0xd37e,0x1d8d},
{0xd3a1,0xd3fe,0x1dcc},
{0xd440,0xd47e,0x1e2a},
{0xd4a1,0xd4fe,0x1e69},
{0xd540,0xd57e,0x1ec7},
{0xd5a1,0xd5fe,0x1f06},
{0xd640,0xd67e,0x1f64},
{0xd6a1,0xd6cb,0x1fa3},
{0xd6cc,0xd6cc,0x2254},
{0xd6cd,0xd6fe,0x1fcf},
{0xd740,0xd779,0x2001},
{0xd77a,0xd77a,0x22b9},
{0xd77b,0xd77e,0x203b},
{0xd7a1,0xd7fe,0x203f},
{0xd840,0xd87e,0x209d},
{0xd8a1,0xd8fe,0x20dc},
{0xd940,0xd97e,0x213a},
{0xd9a1,0xd9fe,0x2179},
{0xda40,0xda7e,0x21d7},
{0xdaa1,0xdade,0x2216},
{0xdadf,0xdadf,0x1fce},
{0xdae0,0xdafe,0x2255},
{0xdb40,0xdb7e,0x2274},
{0xdba1,0xdba6,0x22b3},
{0xdba7,0xdbfe,0x22ba},
{0xdc40,0xdc7e,0x2312},
{0xdca1,0xdcfe,0x2351},
{0xdd40,0xdd7e,0x23af},
{0xdda1,0xddfb,0x23ee},
{0xddfc,0xddfc,0x2381},
{0xddfd,0xddfe,0x2449},
{0xde40,0xde7e,0x244b},
{0xdea1,0xdefe,0x248a},
{0xdf40,0xdf7e,0x24e8},
{0xdfa1,0xdffe,0x2527},
{0xe040,0xe07e,0x2585},
{0xe0a1,0xe0fe,0x25c4},
{0xe140,0xe17e,0x2622},
{0xe1a1,0xe1fe,0x2661},
{0xe240,0xe27e,0x26bf},
{0xe2a1,0xe2fe,0x26fe},
{0xe340,0xe37e,0x275c},
{0xe3a1,0xe3fe,0x279b},
{0xe440,0xe47e,0x27f9},
{0xe4a1,0xe4fe,0x2838},
{0xe540,0xe57e,0x2896},
{0xe5a1,0xe5fe,0x28d5},
{0xe640,0xe67e,0x2933},
{0xe6a1,0xe6fe,0x2972},
{0xe740,0xe77e,0x29d0},
{0xe7a1,0xe7fe,0x2a0f},
{0xe840,0xe87e,0x2a6d},
{0xe8a1,0xe8a2,0x2aac},
{0xe8a3,0xe8fe,0x2aaf},
{0xe940,0xe975,0x2b0b},
{0xe976,0xe97e,0x2b42},
{0xe9a1,0xe9fe,0x2b4b},
{0xea40,0xea7e,0x2ba9},
{0xeaa1,0xeafe,0x2be8},
{0xeb40,0xeb5a,0x2c46},
{0xeb5b,0xeb7e,0x2c62},
{0xeba1,0xebf0,0x2c86},
{0xebf1,0xebf1,0x2aae},
{0xebf2,0xebfe,0x2cd6},
{0xec40,0xec7e,0x2ce3},
{0xeca1,0xecdd,0x2d22},
{0xecde,0xecde,0x2b41},
{0xecdf,0xecfe,0x2d5f},
{0xed40,0xed7e,0x2d7f},
{0xeda1,0xeda9,0x2dbe},
{0xedaa,0xedfe,0x2dc8},
{0xee40,0xee7e,0x2e1d},
{0xeea1,0xeeea,0x2e5c},
{0xeeeb,0xeeeb,0x3014},
{0xeeec,0xeefe,0x2ea6},
{0xef40,0xef7e,0x2eb9},
{0xefa1,0xeffe,0x2ef8},
{0xf040,0xf055,0x2f56},
{0xf056,0xf056,0x2dc7},
{0xf057,0xf07e,0x2f6c},
{0xf0a1,0xf0ca,0x2f94},
{0xf0cb,0xf0cb,0x2c61},
{0xf0cc,0xf0fe,0x2fbe},
{0xf140,0xf162,0x2ff1},
{0xf163,0xf16a,0x3015},
{0xf16b,0xf16b,0x3160},
{0xf16c,0xf17e,0x301d},
{0xf1a1,0xf1fe,0x3030},
{0xf240,0xf267,0x308e},
{0xf268,0xf268,0x31ef},
{0xf269,0xf27e,0x30b6},
{0xf2a1,0xf2c2,0x30cc},
{0xf2c3,0xf2fe,0x30ef},
{0xf340,0xf374,0x312b},
{0xf375,0xf37e,0x3161},
{0xf3a1,0xf3fe,0x316b},
{0xf440,0xf465,0x31c9},
{0xf466,0xf47e,0x31f0},
{0xf4a1,0xf4b4,0x3209},
{0xf4b5,0xf4b5,0x30ee},
{0xf4b6,0xf4fc,0x321d},
{0xf4fd,0xf4fe,0x3265},
{0xf540,0xf57e,0x3267},
{0xf5a1,0xf5fe,0x32a6},
{0xf640,0xf662,0x3304},
{0xf663,0xf663,0x3264},
{0xf664,0xf67e,0x3327},
{0xf6a1,0xf6fe,0x3342},
{0xf740,0xf77e,0x33a0},
{0xf7a1,0xf7fe,0x33df},
{0xf840,0xf87e,0x343d},
{0xf8a1,0xf8fe,0x347c},
{0xf940,0xf976,0x34da},
{0xf977,0xf97e,0x3512},
{0xf9a1,0xf9c3,0x351a},
{0xf9c4,0xf9c4,0x3511},
{0xf9c5,0xf9c5,0x353d},
{0xf9c6,0xf9c6,0x3549},
{0xf9c7,0xf9d1,0x353e},
{0xf9d2,0xf9d5,0x354a},
{0xf9d6,0xf9fe,0x36e8},
};

static pdf_cmap cmap_ETen_B5_H = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "ETen-B5-H",
	/* usecmap */ "", NULL,
	/* wmode */ 0,
	/* codespaces */ 2, {
		{ 1, 0x00, 0x80 },
		{ 2, 0xa140, 0xfefe },
	},
	254, 254, (pdf_range*)cmap_ETen_B5_H_ranges,
	0, 0, NULL, /* xranges */
	0, 0, NULL, /* mranges */
	0, 0, NULL, /* table */
	0, 0, 0, NULL /* splay tree */
};
