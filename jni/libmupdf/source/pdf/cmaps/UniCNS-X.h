/* This is an automatically generated file. Do not edit. */

/* UniCNS-X */

static const pdf_range cmap_UniCNS_X_ranges[] = {
{0x20,0x7e,0x1},
{0xa2,0xa3,0x106},
{0xa5,0xa5,0x104},
{0xa7,0xa7,0xb2},
{0xa8,0xa8,0x35b3},
{0xac,0xac,0x36e1},
{0xb0,0xb0,0x118},
{0xb1,0xb1,0xd4},
{0xb7,0xb7,0x73},
{0xc0,0xc0,0x4964},
{0xc1,0xc1,0x4962},
{0xc8,0xc8,0x4968},
{0xc9,0xc9,0x4966},
{0xca,0xca,0x4971},
{0xd2,0xd2,0x496c},
{0xd3,0xd3,0x496a},
{0xd7,0xd7,0xd2},
{0xe0,0xe0,0x4975},
{0xe1,0xe1,0x4973},
{0xe8,0xe8,0x497a},
{0xe9,0xe9,0x4978},
{0xea,0xea,0x4990},
{0xec,0xec,0x497e},
{0xed,0xed,0x497c},
{0xf2,0xf2,0x4982},
{0xf3,0xf3,0x4980},
{0xf7,0xf7,0xd3},
{0xf8,0xf8,0x4998},
{0xf9,0xf9,0x4986},
{0xfa,0xfa,0x4984},
{0xfc,0xfc,0x498b},
{0x100,0x100,0x4961},
{0x101,0x101,0x4972},
{0x112,0x112,0x4965},
{0x113,0x113,0x4977},
{0x11a,0x11a,0x4967},
{0x11b,0x11b,0x4979},
{0x12b,0x12b,0x497b},
{0x14b,0x14b,0x4999},
{0x14c,0x14c,0x4969},
{0x14d,0x14d,0x497f},
{0x153,0x153,0x4997},
{0x16b,0x16b,0x4983},
{0x1cd,0x1cd,0x4963},
{0x1ce,0x1ce,0x4974},
{0x1d0,0x1d0,0x497d},
{0x1d1,0x1d1,0x496b},
{0x1d2,0x1d2,0x4981},
{0x1d4,0x1d4,0x4985},
{0x1d6,0x1d6,0x4987},
{0x1d8,0x1d8,0x4988},
{0x1da,0x1da,0x4989},
{0x1dc,0x1dc,0x498a},
{0x250,0x250,0x4993},
{0x251,0x251,0x4976},
{0x254,0x254,0x4995},
{0x25b,0x25b,0x4994},
{0x261,0x261,0x4991},
{0x26a,0x26a,0x499b},
{0x275,0x275,0x4996},
{0x283,0x283,0x4992},
{0x28a,0x28a,0x499a},
{0x2c6,0x2c6,0x35b4},
{0x2c7,0x2c7,0x1f8},
{0x2ca,0x2ca,0x1f7},
{0x2cb,0x2cb,0x1f9},
{0x2d9,0x2d9,0x1f6},
{0x308,0x308,0x35b3},
{0x391,0x3a1,0x1a1},
{0x3a3,0x3a9,0x1b2},
{0x3b1,0x3c1,0x1b9},
{0x3c3,0x3c9,0x1ca},
{0x401,0x401,0x3670},
{0x410,0x415,0x366a},
{0x416,0x435,0x3671},
{0x436,0x44f,0x3692},
{0x451,0x451,0x3691},
{0x1ebe,0x1ebe,0x496e},
{0x1ebf,0x1ebf,0x498d},
{0x1ec0,0x1ec0,0x4970},
{0x1ec1,0x1ec1,0x498f},
{0x2013,0x2013,0x79},
{0x2014,0x2014,0x7b},
{0x2018,0x2019,0xa6},
{0x201c,0x201d,0xa8},
{0x2022,0x2022,0x68},
{0x2025,0x2025,0x6f},
{0x2026,0x2026,0x6e},
{0x2032,0x2032,0xad},
{0x2035,0x2035,0xac},
{0x203b,0x203b,0xb1},
{0x203e,0x203e,0xc3},
{0x20ac,0x20ac,0x44c1},
{0x2103,0x2103,0x10a},
{0x2105,0x2105,0xc2},
{0x2109,0x2109,0x10b},
{0x2116,0x2116,0x36e6},
{0x2121,0x2121,0x36e7},
{0x2160,0x2169,0x157},
{0x2170,0x2179,0x20e},
{0x2190,0x2190,0xf8},
{0x2191,0x2191,0xf5},
{0x2192,0x2192,0xf7},
{0x2193,0x2193,0xf6},
{0x2196,0x2197,0xf9},
{0x2198,0x2198,0xfc},
{0x2199,0x2199,0xfb},
{0x21b8,0x21b9,0x36ad},
{0x21e7,0x21e7,0x36ac},
{0x221a,0x221a,0xd5},
{0x221e,0x221e,0xdc},
{0x221f,0x221f,0xe9},
{0x2220,0x2220,0xe8},
{0x2223,0x2223,0xfe},
{0x2225,0x2225,0xfd},
{0x2229,0x222a,0xe5},
{0x222b,0x222b,0xed},
{0x222e,0x222e,0xee},
{0x2234,0x2234,0xf0},
{0x2235,0x2235,0xef},
{0x223c,0x223c,0xe4},
{0x2252,0x2252,0xdd},
{0x2260,0x2260,0xdb},
{0x2261,0x2261,0xde},
{0x2266,0x2267,0xd9},
{0x22a5,0x22a5,0xe7},
{0x22bf,0x22bf,0xea},
{0x2400,0x241f,0x232},
{0x2421,0x2421,0x252},
{0x2460,0x2469,0x1fa},
{0x2474,0x247d,0x204},
{0x2500,0x2500,0x137},
{0x2502,0x2502,0x138},
{0x250c,0x250c,0x13a},
{0x2510,0x2510,0x13b},
{0x2514,0x2514,0x13c},
{0x2518,0x2518,0x13d},
{0x251c,0x251c,0x135},
{0x2524,0x2524,0x134},
{0x252c,0x252c,0x133},
{0x2534,0x2534,0x132},
{0x253c,0x253c,0x131},
{0x2551,0x2551,0x370a},
{0x2552,0x2552,0x36f8},
{0x2553,0x2553,0x3701},
{0x2554,0x2554,0x36ef},
{0x2555,0x2555,0x36fa},
{0x2556,0x2556,0x3703},
{0x2557,0x2557,0x36f1},
{0x2558,0x2558,0x36fe},
{0x2559,0x2559,0x3707},
{0x255a,0x255a,0x36f5},
{0x255b,0x255b,0x3700},
{0x255c,0x255c,0x3709},
{0x255d,0x255d,0x36f7},
{0x255f,0x255f,0x3704},
{0x2560,0x2560,0x36f2},
{0x2562,0x2562,0x3706},
{0x2563,0x2563,0x36f4},
{0x2564,0x2564,0x36f9},
{0x2565,0x2565,0x3702},
{0x2566,0x2566,0x36f0},
{0x2567,0x2567,0x36ff},
{0x2568,0x2568,0x3708},
{0x2569,0x2569,0x36f6},
{0x256b,0x256b,0x3705},
{0x256c,0x256c,0x36f3},
{0x256d,0x256e,0x13e},
{0x256f,0x256f,0x141},
{0x2570,0x2570,0x140},
{0x2571,0x2573,0x14a},
{0x2581,0x2588,0x122},
{0x2589,0x2589,0x130},
{0x258a,0x258a,0x12f},
{0x258b,0x258b,0x12e},
{0x258c,0x258c,0x12d},
{0x258d,0x258d,0x12c},
{0x258e,0x258e,0x12b},
{0x258f,0x258f,0x12a},
{0x2593,0x2593,0x3710},
{0x2594,0x2594,0x136},
{0x2595,0x2595,0x139},
{0x25a0,0x25a0,0xbe},
{0x25a1,0x25a1,0xbd},
{0x25b2,0x25b2,0xb7},
{0x25b3,0x25b3,0xb6},
{0x25bc,0x25bc,0xc0},
{0x25bd,0x25bd,0xbf},
{0x25c6,0x25c6,0xbc},
{0x25c7,0x25c7,0xbb},
{0x25cb,0x25cb,0xb4},
{0x25ce,0x25ce,0xb8},
{0x25cf,0x25cf,0xb5},
{0x25e2,0x25e3,0x146},
{0x25e4,0x25e4,0x149},
{0x25e5,0x25e5,0x148},
{0x2605,0x2605,0xba},
{0x2606,0x2606,0xb9},
{0x2609,0x2609,0xf4},
{0x2640,0x2640,0xf1},
{0x2641,0x2641,0xf3},
{0x2642,0x2642,0xf2},
{0x273d,0x273d,0x35c0},
{0x2e80,0x2e80,0x44c8},
{0x2e84,0x2e84,0x451c},
{0x2e86,0x2e88,0x451d},
{0x2e8a,0x2e8a,0x4520},
{0x2e8c,0x2e8d,0x4521},
{0x2e95,0x2e95,0x4523},
{0x2e9c,0x2e9c,0x4524},
{0x2ea5,0x2ea5,0x4525},
{0x2ea7,0x2ea7,0x4526},
{0x2eaa,0x2eaa,0x4527},
{0x2eac,0x2eac,0x4528},
{0x2eae,0x2eae,0x4529},
{0x2eb6,0x2eb6,0x452a},
{0x2ebc,0x2ebc,0x452b},
{0x2ebe,0x2ebe,0x452c},
{0x2eca,0x2eca,0x452d},
{0x2ecc,0x2ecd,0x452e},
{0x2ecf,0x2ecf,0x4530},
{0x2ed6,0x2ed7,0x4531},
{0x2ede,0x2ede,0x4533},
{0x2f33,0x2f33,0x227},
{0x3000,0x3000,0x63},
{0x3001,0x3002,0x65},
{0x3003,0x3003,0xb3},
{0x3005,0x3007,0x35ba},
{0x3008,0x3009,0x94},
{0x300a,0x300b,0x90},
{0x300c,0x300d,0x98},
{0x300e,0x300f,0x9c},
{0x3010,0x3011,0x8c},
{0x3012,0x3012,0x105},
{0x3014,0x3015,0x88},
{0x301d,0x301e,0xaa},
{0x3021,0x3029,0x161},
{0x3041,0x3093,0x35c1},
{0x309b,0x309c,0x44c6},
{0x309d,0x309e,0x35b7},
{0x30a1,0x30f6,0x3614},
{0x30fc,0x30fc,0x35bd},
{0x30fd,0x30fe,0x35b5},
{0x3105,0x3129,0x1d1},
{0x3231,0x3231,0x36e5},
{0x32a3,0x32a3,0xc1},
{0x338e,0x338f,0x115},
{0x339c,0x339e,0x110},
{0x33a1,0x33a1,0x114},
{0x33c4,0x33c4,0x117},
{0x33ce,0x33ce,0x113},
{0x33d1,0x33d1,0xec},
{0x33d2,0x33d2,0xeb},
{0x33d5,0x33d5,0x10f},
{0x3435,0x3435,0x39bd},
{0x3440,0x3440,0x3c67},
{0x344c,0x344c,0x4593},
{0x3464,0x3464,0x3a85},
{0x3473,0x3473,0x3dc5},
{0x347a,0x347a,0x4033},
{0x347d,0x347d,0x4597},
{0x347e,0x347e,0x46a3},
{0x3493,0x3493,0x439e},
{0x3496,0x3496,0x37dc},
{0x34a5,0x34a5,0x4598},
{0x34af,0x34af,0x3c7f},
{0x34bc,0x34bc,0x4380},
{0x34c1,0x34c1,0x44fb},
{0x34c8,0x34c8,0x3d00},
{0x34df,0x34df,0x3ea4},
{0x34e4,0x34e4,0x3e54},
{0x34fb,0x34fb,0x3dca},
{0x3506,0x3506,0x4336},
{0x353e,0x353e,0x44e7},
{0x3551,0x3551,0x45a1},
{0x3553,0x3553,0x43a5},
{0x3561,0x3561,0x40d8},
{0x356d,0x356d,0x45a4},
{0x3570,0x3570,0x3b2f},
{0x3572,0x3572,0x45a5},
{0x3577,0x3577,0x3ecb},
{0x3578,0x3578,0x4379},
{0x3584,0x3584,0x39fb},
{0x3597,0x3597,0x3b2d},
{0x3598,0x3598,0x45b0},
{0x35a1,0x35a1,0x40e2},
{0x35a5,0x35a5,0x45b1},
{0x35ad,0x35ad,0x3efc},
{0x35bf,0x35bf,0x45b2},
{0x35c1,0x35c1,0x4580},
{0x35c5,0x35c5,0x45b4},
{0x35c7,0x35c7,0x459f},
{0x35ca,0x35ca,0x3e43},
{0x35ce,0x35ce,0x3e81},
{0x35d2,0x35d2,0x3fc9},
{0x35d6,0x35d6,0x3fb5},
{0x35db,0x35db,0x470d},
{0x35dd,0x35dd,0x43ac},
{0x35f1,0x35f1,0x4696},
{0x35f2,0x35f2,0x4627},
{0x35f3,0x35f3,0x3f6c},
{0x35fb,0x35fb,0x45c8},
{0x35fe,0x35fe,0x3f6a},
{0x3609,0x3609,0x45f5},
{0x3618,0x3618,0x4871},
{0x361a,0x361a,0x461a},
{0x3623,0x3623,0x40c6},
{0x362d,0x362d,0x3e86},
{0x3635,0x3635,0x492e},
{0x3639,0x3639,0x4165},
{0x363e,0x363e,0x3a08},
{0x3647,0x3647,0x4806},
{0x3648,0x3648,0x3806},
{0x3649,0x3649,0x4013},
{0x364e,0x364e,0x4698},
{0x365f,0x365f,0x3df3},
{0x367a,0x367a,0x3ee3},
{0x3681,0x3681,0x45a6},
{0x369a,0x369a,0x3c71},
{0x36a5,0x36a5,0x4902},
{0x36aa,0x36aa,0x3b30},
{0x36ac,0x36ac,0x4900},
{0x36b0,0x36b0,0x3cdf},
{0x36b1,0x36b1,0x40cd},
{0x36b5,0x36b5,0x3bc2},
{0x36b9,0x36b9,0x4887},
{0x36bc,0x36bc,0x3cff},
{0x36c1,0x36c1,0x37c5},
{0x36c3,0x36c3,0x40e5},
{0x36c4,0x36c4,0x3905},
{0x36c5,0x36c5,0x4296},
{0x36c7,0x36c7,0x3d3a},
{0x36c8,0x36c8,0x4820},
{0x36d3,0x36d3,0x3a38},
{0x36d4,0x36d4,0x3bb3},
{0x36d6,0x36d6,0x3d0c},
{0x36dd,0x36dd,0x3a36},
{0x36e1,0x36e1,0x397c},
{0x36e2,0x36e2,0x3cdd},
{0x36e5,0x36e5,0x4216},
{0x36e6,0x36e6,0x40fc},
{0x36f5,0x36f5,0x3a18},
{0x3701,0x3701,0x3a34},
{0x3703,0x3703,0x460f},
{0x3708,0x3708,0x40ff},
{0x370a,0x370a,0x3cd5},
{0x370d,0x370d,0x4238},
{0x371c,0x371c,0x3dfe},
{0x3722,0x3722,0x3979},
{0x3723,0x3723,0x3980},
{0x3725,0x3725,0x3849},
{0x372c,0x372c,0x3c8c},
{0x372d,0x372d,0x3d37},
{0x3730,0x3730,0x495c},
{0x3732,0x3732,0x4106},
{0x3733,0x3733,0x3997},
{0x373a,0x373a,0x3e56},
{0x3740,0x3740,0x4202},
{0x3743,0x3743,0x4036},
{0x3762,0x3762,0x3db6},
{0x376f,0x376f,0x47cb},
{0x3797,0x3797,0x45ed},
{0x37a0,0x37a0,0x3a28},
{0x37b9,0x37b9,0x43b7},
{0x37be,0x37be,0x393e},
{0x37f2,0x37f2,0x3ba1},
{0x37f8,0x37f8,0x42d2},
{0x37fb,0x37fb,0x3ef5},
{0x380f,0x380f,0x462c},
{0x3819,0x3819,0x39af},
{0x3820,0x3820,0x462f},
{0x382d,0x382d,0x412e},
{0x3836,0x3836,0x4133},
{0x3838,0x3838,0x43bb},
{0x3863,0x3863,0x46c3},
{0x38a0,0x38a0,0x4145},
{0x38c3,0x38c3,0x3912},
{0x38cc,0x38cc,0x4076},
{0x38d1,0x38d1,0x3a95},
{0x38fa,0x38fa,0x44eb},
{0x3908,0x3908,0x4632},
{0x3914,0x3914,0x43be},
{0x3927,0x3927,0x3c31},
{0x3932,0x3932,0x4182},
{0x393f,0x393f,0x4633},
{0x394d,0x394d,0x4634},
{0x3963,0x3963,0x4163},
{0x3980,0x3980,0x3874},
{0x3989,0x3989,0x4638},
{0x398a,0x398a,0x3ce8},
{0x3992,0x3992,0x4376},
{0x3999,0x3999,0x39ba},
{0x399b,0x399b,0x3db3},
{0x39a1,0x39a1,0x3e19},
{0x39a4,0x39a4,0x3e0f},
{0x39b8,0x39b8,0x463b},
{0x39dc,0x39dc,0x3ece},
{0x39e2,0x39e2,0x46c8},
{0x39e5,0x39e5,0x393b},
{0x39ec,0x39ec,0x4310},
{0x39f8,0x39f8,0x463e},
{0x39fb,0x39fb,0x4345},
{0x39fe,0x39fe,0x4368},
{0x3a01,0x3a01,0x41e0},
{0x3a03,0x3a03,0x4640},
{0x3a06,0x3a06,0x4377},
{0x3a17,0x3a17,0x4190},
{0x3a18,0x3a18,0x438f},
{0x3a29,0x3a29,0x3a5e},
{0x3a2a,0x3a2a,0x3edf},
{0x3a34,0x3a34,0x4319},
{0x3a4b,0x3a4b,0x4644},
{0x3a52,0x3a52,0x3ed3},
{0x3a57,0x3a57,0x419e},
{0x3a5c,0x3a5c,0x3fc4},
{0x3a5e,0x3a5e,0x3b07},
{0x3a66,0x3a66,0x419c},
{0x3a67,0x3a67,0x4333},
{0x3a97,0x3a97,0x4647},
{0x3aab,0x3aab,0x4091},
{0x3abd,0x3abd,0x4649},
{0x3ade,0x3ade,0x414c},
{0x3ae0,0x3ae0,0x3a7a},
{0x3af0,0x3af0,0x46b2},
{0x3af2,0x3af2,0x464c},
{0x3afb,0x3afb,0x3af2},
{0x3b0e,0x3b0e,0x38e8},
{0x3b19,0x3b19,0x46c5},
{0x3b22,0x3b22,0x464e},
{0x3b2b,0x3b2b,0x4956},
{0x3b39,0x3b39,0x474b},
{0x3b42,0x3b42,0x4650},
{0x3b58,0x3b58,0x4652},
{0x3b60,0x3b60,0x393a},
{0x3b71,0x3b71,0x4656},
{0x3b72,0x3b72,0x4655},
{0x3b7b,0x3b7b,0x4657},
{0x3b7c,0x3b7c,0x385a},
{0x3b80,0x3b80,0x41e2},
{0x3b96,0x3b96,0x3a9c},
{0x3b99,0x3b99,0x3a98},
{0x3ba1,0x3ba1,0x41e9},
{0x3bbc,0x3bbc,0x43c8},
{0x3bbe,0x3bbe,0x3db1},
{0x3bc2,0x3bc2,0x4134},
{0x3bc4,0x3bc4,0x3aa0},
{0x3bd7,0x3bd7,0x3aac},
{0x3bdd,0x3bdd,0x465f},
{0x3bec,0x3bec,0x4664},
{0x3bf2,0x3bf2,0x4666},
{0x3bf3,0x3bf3,0x41f3},
{0x3bf4,0x3bf4,0x3a6e},
{0x3c0d,0x3c0d,0x41f7},
{0x3c11,0x3c11,0x3e40},
{0x3c15,0x3c15,0x3998},
{0x3c54,0x3c54,0x3e00},
{0x3ccb,0x3ccb,0x4670},
{0x3ccd,0x3ccd,0x3ce5},
{0x3cd1,0x3cd1,0x4003},
{0x3cd6,0x3cd6,0x3cf7},
{0x3cdc,0x3cdc,0x404e},
{0x3ceb,0x3ceb,0x4217},
{0x3cef,0x3cef,0x4675},
{0x3d13,0x3d13,0x3773},
{0x3d1d,0x3d1d,0x393c},
{0x3d32,0x3d32,0x4957},
{0x3d3b,0x3d3b,0x4245},
{0x3d46,0x3d46,0x4685},
{0x3d4c,0x3d4c,0x3ceb},
{0x3d4e,0x3d4e,0x4242},
{0x3d51,0x3d51,0x38ea},
{0x3d5f,0x3d5f,0x4159},
{0x3d62,0x3d62,0x3c5e},
{0x3d69,0x3d69,0x3cea},
{0x3d6a,0x3d6a,0x4689},
{0x3d6f,0x3d6f,0x3cfc},
{0x3d75,0x3d75,0x468a},
{0x3d7d,0x3d7d,0x3c2f},
{0x3d85,0x3d85,0x494b},
{0x3d8a,0x3d8a,0x468d},
{0x3d8f,0x3d8f,0x3abd},
{0x3d91,0x3d91,0x468f},
{0x3da5,0x3da5,0x3d56},
{0x3dad,0x3dad,0x4699},
{0x3db4,0x3db4,0x40a6},
{0x3dbf,0x3dbf,0x37d0},
{0x3dc6,0x3dc6,0x48de},
{0x3dc7,0x3dc7,0x4164},
{0x3dcc,0x3dcc,0x3d6f},
{0x3dcd,0x3dcd,0x3af3},
{0x3dd3,0x3dd3,0x37e1},
{0x3ddb,0x3ddb,0x3fff},
{0x3de7,0x3de7,0x3999},
{0x3de8,0x3de8,0x425d},
{0x3deb,0x3deb,0x3e5a},
{0x3df3,0x3df3,0x46d4},
{0x3df7,0x3df7,0x48ab},
{0x3dfc,0x3dfc,0x462b},
{0x3dfd,0x3dfd,0x3c14},
{0x3e06,0x3e06,0x491d},
{0x3e40,0x3e40,0x4169},
{0x3e43,0x3e43,0x436d},
{0x3e48,0x3e48,0x4595},
{0x3e55,0x3e55,0x427f},
{0x3e74,0x3e74,0x3ee2},
{0x3ea8,0x3ea8,0x4304},
{0x3ea9,0x3ea9,0x46ed},
{0x3eaa,0x3eaa,0x4075},
{0x3ead,0x3ead,0x3b9d},
{0x3eb1,0x3eb1,0x3ad8},
{0x3eb8,0x3eb8,0x3a4b},
{0x3ebf,0x3ebf,0x3b0b},
{0x3ec2,0x3ec2,0x3bd8},
{0x3ec7,0x3ec7,0x3975},
{0x3eca,0x3eca,0x46f1},
{0x3ecc,0x3ecc,0x3be2},
{0x3ed0,0x3ed0,0x3854},
{0x3ed1,0x3ed1,0x46f2},
{0x3ed6,0x3ed6,0x3cad},
{0x3ed7,0x3ed7,0x429f},
{0x3eda,0x3eda,0x3d02},
{0x3ede,0x3ede,0x39f2},
{0x3ee1,0x3ee1,0x3ca8},
{0x3ee2,0x3ee2,0x46f6},
{0x3ee7,0x3ee7,0x3bdc},
{0x3ee9,0x3ee9,0x3ca4},
{0x3eeb,0x3eeb,0x396a},
{0x3ef0,0x3ef0,0x46f7},
{0x3ef3,0x3ef3,0x3add},
{0x3ef4,0x3ef4,0x46f8},
{0x3efa,0x3efa,0x46f9},
{0x3efc,0x3efc,0x3be8},
{0x3eff,0x3eff,0x3af5},
{0x3f00,0x3f00,0x3c0d},
{0x3f04,0x3f04,0x42c3},
{0x3f06,0x3f06,0x3ad7},
{0x3f0e,0x3f0e,0x46fb},
{0x3f53,0x3f53,0x46fc},
{0x3f58,0x3f58,0x3ae9},
{0x3f59,0x3f59,0x4089},
{0x3f63,0x3f63,0x3ae6},
{0x3f7c,0x3f7c,0x4700},
{0x3f93,0x3f93,0x45cd},
{0x3fc0,0x3fc0,0x43cf},
{0x3fd7,0x3fd7,0x43d1},
{0x3fdc,0x3fdc,0x4704},
{0x3fe5,0x3fe5,0x46df},
{0x3fed,0x3fed,0x4335},
{0x3ff9,0x3ff9,0x45d7},
{0x3ffa,0x3ffa,0x4354},
{0x4004,0x4004,0x410e},
{0x401d,0x401d,0x4709},
{0x4039,0x4039,0x470b},
{0x4045,0x4045,0x470c},
{0x4053,0x4053,0x45b6},
{0x4057,0x4057,0x399d},
{0x4062,0x4062,0x3bcb},
{0x4065,0x4065,0x3fd3},
{0x406a,0x406a,0x470f},
{0x406f,0x406f,0x4710},
{0x40a8,0x40a8,0x43d5},
{0x40bb,0x40bb,0x45c0},
{0x40bf,0x40bf,0x3eec},
{0x40c8,0x40c8,0x3b0e},
{0x40d8,0x40d8,0x41ab},
{0x40df,0x40df,0x3e17},
{0x40fa,0x40fa,0x3ebe},
{0x4103,0x4103,0x43d7},
{0x4104,0x4104,0x425c},
{0x4109,0x4109,0x471c},
{0x410e,0x410e,0x3b1b},
{0x4132,0x4132,0x3b25},
{0x4167,0x4167,0x471f},
{0x416c,0x416c,0x38ae},
{0x416e,0x416e,0x3b23},
{0x417f,0x417f,0x3b82},
{0x4190,0x4190,0x46c0},
{0x41b2,0x41b2,0x4720},
{0x41c4,0x41c4,0x4723},
{0x41ca,0x41ca,0x373f},
{0x41cf,0x41cf,0x4726},
{0x41db,0x41db,0x37bf},
{0x41ef,0x41ef,0x3743},
{0x41f9,0x41f9,0x3b3e},
{0x4211,0x4211,0x3b41},
{0x4240,0x4240,0x37f1},
{0x4260,0x4260,0x472b},
{0x426a,0x426a,0x3b55},
{0x427a,0x427a,0x472c},
{0x428c,0x428c,0x472f},
{0x4294,0x4294,0x4731},
{0x42b5,0x42b5,0x4010},
{0x42b9,0x42b9,0x38a6},
{0x42bc,0x42bc,0x3c8a},
{0x42f4,0x42f4,0x3bb9},
{0x42fb,0x42fb,0x3cee},
{0x42fc,0x42fc,0x41e6},
{0x432b,0x432b,0x377d},
{0x436e,0x436e,0x46ca},
{0x4397,0x4397,0x473b},
{0x43ba,0x43ba,0x435f},
{0x43c1,0x43c1,0x4695},
{0x43d9,0x43d9,0x433e},
{0x43df,0x43df,0x3e49},
{0x43ed,0x43ed,0x4745},
{0x43f2,0x43f2,0x3e48},
{0x4401,0x4401,0x474a},
{0x4402,0x4402,0x3b73},
{0x4413,0x4413,0x474f},
{0x4425,0x4425,0x4751},
{0x442d,0x442d,0x4752},
{0x447a,0x447a,0x37af},
{0x448f,0x448f,0x4758},
{0x449f,0x449f,0x3ae2},
{0x44a0,0x44a0,0x37ed},
{0x44a2,0x44a2,0x4079},
{0x44b0,0x44b0,0x475c},
{0x44b7,0x44b7,0x3fa1},
{0x44c0,0x44c0,0x3c07},
{0x44c5,0x44c5,0x4210},
{0x44ce,0x44ce,0x3d23},
{0x44dd,0x44dd,0x39dd},
{0x44df,0x44df,0x3d22},
{0x44e4,0x44e4,0x37e2},
{0x44e9,0x44e9,0x41cf},
{0x44ea,0x44ea,0x3b71},
{0x44eb,0x44eb,0x3cf2},
{0x44ec,0x44ec,0x3eb4},
{0x44f4,0x44f4,0x3992},
{0x4503,0x4503,0x469f},
{0x4504,0x4504,0x4763},
{0x4509,0x4509,0x3e50},
{0x450b,0x450b,0x37d4},
{0x4516,0x4516,0x37f9},
{0x451d,0x451d,0x3767},
{0x4527,0x4527,0x37f7},
{0x452e,0x452e,0x3cd3},
{0x4533,0x4533,0x3c51},
{0x453b,0x453b,0x476a},
{0x453d,0x453d,0x38c4},
{0x453f,0x453f,0x3e12},
{0x4543,0x4543,0x37f3},
{0x4551,0x4551,0x3ae4},
{0x4552,0x4552,0x40b3},
{0x4555,0x4555,0x423e},
{0x455c,0x455c,0x378b},
{0x4562,0x4562,0x4940},
{0x456a,0x456a,0x3804},
{0x4577,0x4577,0x476e},
{0x4585,0x4585,0x38c5},
{0x45e9,0x45e9,0x3ee4},
{0x4606,0x4606,0x4773},
{0x460f,0x460f,0x3815},
{0x4615,0x4615,0x3843},
{0x4617,0x4617,0x4774},
{0x465b,0x465b,0x381d},
{0x467a,0x467a,0x39e9},
{0x4680,0x4680,0x3d01},
{0x46cf,0x46cf,0x3ba0},
{0x46d0,0x46d0,0x3dfa},
{0x46f5,0x46f5,0x3b9f},
{0x4713,0x4713,0x3833},
{0x4718,0x4718,0x3dc7},
{0x474e,0x474e,0x3ebc},
{0x477c,0x477c,0x3dcd},
{0x4798,0x4798,0x4781},
{0x47a6,0x47a6,0x40a3},
{0x47b6,0x47b6,0x3eea},
{0x47d5,0x47d5,0x431a},
{0x47ed,0x47ed,0x4783},
{0x47f4,0x47f4,0x432f},
{0x4800,0x4800,0x461e},
{0x480b,0x480b,0x4352},
{0x4837,0x4837,0x4787},
{0x485d,0x485d,0x410f},
{0x4871,0x4871,0x3d03},
{0x489b,0x489b,0x3bbd},
{0x48ad,0x48ad,0x4791},
{0x48ae,0x48ae,0x494d},
{0x48d0,0x48d0,0x3da7},
{0x48dd,0x48dd,0x4120},
{0x48ed,0x48ed,0x4288},
{0x48f3,0x48f3,0x3ec1},
{0x48fa,0x48fa,0x3e44},
{0x4906,0x4906,0x3bc7},
{0x4911,0x4911,0x4584},
{0x491e,0x491e,0x4794},
{0x4925,0x4925,0x3c0f},
{0x492a,0x492a,0x46ae},
{0x492d,0x492d,0x46cd},
{0x4935,0x4935,0x3cc3},
{0x493c,0x493c,0x3bf8},
{0x493e,0x493e,0x3d06},
{0x4945,0x4945,0x47a3},
{0x4951,0x4951,0x47a4},
{0x4953,0x4953,0x42ad},
{0x4965,0x4965,0x3899},
{0x496a,0x496a,0x47a9},
{0x4972,0x4972,0x3a24},
{0x4989,0x4989,0x379b},
{0x49a1,0x49a1,0x38b7},
{0x49a7,0x49a7,0x47ae},
{0x49df,0x49df,0x38aa},
{0x49e5,0x49e5,0x47b1},
{0x49e7,0x49e7,0x4621},
{0x4a0f,0x4a0f,0x38c3},
{0x4a1d,0x4a1d,0x3bec},
{0x4a24,0x4a24,0x47b2},
{0x4a35,0x4a35,0x47b4},
{0x4a96,0x4a96,0x3ce7},
{0x4ab4,0x4ab4,0x4361},
{0x4ab8,0x4ab8,0x3da8},
{0x4ad1,0x4ad1,0x38e3},
{0x4ae4,0x4ae4,0x47b7},
{0x4aff,0x4aff,0x38f2},
{0x4b19,0x4b19,0x47b9},
{0x4b2c,0x4b2c,0x461f},
{0x4b37,0x4b37,0x41a9},
{0x4b6f,0x4b6f,0x3c16},
{0x4b70,0x4b70,0x47c0},
{0x4b72,0x4b72,0x38fc},
{0x4b7b,0x4b7b,0x3c8d},
{0x4b7e,0x4b7e,0x400a},
{0x4b8e,0x4b8e,0x39f7},
{0x4b90,0x4b90,0x3c20},
{0x4b93,0x4b93,0x3a8c},
{0x4b96,0x4b96,0x3942},
{0x4b97,0x4b97,0x3c24},
{0x4b9d,0x4b9d,0x47c2},
{0x4bbd,0x4bbd,0x3c23},
{0x4bbe,0x4bbe,0x3954},
{0x4bc0,0x4bc0,0x3ddc},
{0x4c04,0x4c04,0x3fbb},
{0x4c07,0x4c07,0x3fb7},
{0x4c0e,0x4c0e,0x390c},
{0x4c3b,0x4c3b,0x3f3c},
{0x4c3e,0x4c3e,0x457b},
{0x4c5b,0x4c5b,0x3ed9},
{0x4c6d,0x4c6d,0x47c9},
{0x4c7d,0x4c7d,0x3e66},
{0x4ca4,0x4ca4,0x48be},
{0x4cae,0x4cae,0x3c42},
{0x4cb0,0x4cb0,0x3c45},
{0x4cb7,0x4cb7,0x3e21},
{0x4ccd,0x4ccd,0x4578},
{0x4ce1,0x4ce1,0x3ef3},
{0x4ced,0x4ced,0x40ab},
{0x4d09,0x4d09,0x3ed6},
{0x4d10,0x4d10,0x4117},
{0x4d34,0x4d34,0x3935},
{0x4d91,0x4d91,0x43f5},
{0x4d9c,0x4d9c,0x48c4},
{0x4e00,0x4e00,0x253},
{0x4e01,0x4e01,0x255},
{0x4e03,0x4e03,0x256},
{0x4e04,0x4e04,0x48fe},
{0x4e07,0x4e07,0x1771},
{0x4e08,0x4e08,0x269},
{0x4e09,0x4e09,0x267},
{0x4e0a,0x4e0a,0x26a},
{0x4e0b,0x4e0b,0x268},
{0x4e0c,0x4e0c,0x1772},
{0x4e0d,0x4e0d,0x294},
{0x4e0e,0x4e0e,0x177a},
{0x4e0f,0x4e0f,0x1778},
{0x4e10,0x4e10,0x293},
{0x4e11,0x4e11,0x292},
{0x4e14,0x4e14,0x2f2},
{0x4e15,0x4e15,0x2f1},
{0x4e16,0x4e16,0x2f0},
{0x4e18,0x4e18,0x2f3},
{0x4e19,0x4e19,0x2ef},
{0x4e1a,0x4e1a,0x48fd},
{0x4e1c,0x4e1c,0x48e0},
{0x4e1e,0x4e1f,0x36e},
{0x4e21,0x4e21,0x3d6d},
{0x4e24,0x4e24,0x458d},
{0x4e26,0x4e26,0x528},
{0x4e28,0x4e28,0x218},
{0x4e2a,0x4e2a,0x3f57},
{0x4e2b,0x4e2b,0x26b},
{0x4e2c,0x4e2c,0x44f3},
{0x4e2d,0x4e2d,0x295},
{0x4e2e,0x4e2e,0x177b},
{0x4e30,0x4e30,0x296},
{0x4e31,0x4e31,0x178e},
{0x4e32,0x4e32,0x415},
{0x4e33,0x4e33,0x18f4},
{0x4e36,0x4e36,0x219},
{0x4e37,0x4e37,0x4517},
{0x4e38,0x4e38,0x26c},
{0x4e39,0x4e39,0x297},
{0x4e3b,0x4e3b,0x2f4},
{0x4e3c,0x4e3c,0x178f},
{0x4e3d,0x4e3d,0x4537},
{0x4e3f,0x4e3f,0x21a},
{0x4e41,0x4e41,0x36af},
{0x4e42,0x4e42,0x176c},
{0x4e43,0x4e43,0x257},
{0x4e45,0x4e45,0x26e},
{0x4e47,0x4e47,0x1773},
{0x4e48,0x4e48,0x26f},
{0x4e49,0x4e49,0x408e},
{0x4e4b,0x4e4b,0x298},
{0x4e4d,0x4e4d,0x2f5},
{0x4e4e,0x4e4e,0x2f7},
{0x4e4f,0x4e4f,0x2f6},
{0x4e52,0x4e53,0x370},
{0x4e56,0x4e56,0x529},
{0x4e58,0x4e58,0x831},
{0x4e59,0x4e59,0x254},
{0x4e5a,0x4e5a,0x36b1},
{0x4e5b,0x4e5b,0x44e5},
{0x4e5c,0x4e5c,0x176d},
{0x4e5d,0x4e5d,0x258},
{0x4e5e,0x4e5e,0x271},
{0x4e5f,0x4e5f,0x270},
{0x4e69,0x4e69,0x372},
{0x4e6a,0x4e6a,0x3de5},
{0x4e73,0x4e73,0x52a},
{0x4e78,0x4e78,0x3d8e},
{0x4e7e,0x4e7e,0x9fb},
{0x4e7f,0x4e7f,0x1e35},
{0x4e80,0x4e80,0x458e},
{0x4e81,0x4e81,0x43bc},
{0x4e82,0x4e82,0xdbe},
{0x4e83,0x4e84,0x2361},
{0x4e85,0x4e85,0x21b},
{0x4e86,0x4e86,0x259},
{0x4e87,0x4e87,0x458f},
{0x4e88,0x4e88,0x29a},
{0x4e89,0x4e89,0x459c},
{0x4e8b,0x4e8b,0x52b},
{0x4e8c,0x4e8c,0x25a},
{0x4e8d,0x4e8d,0x1774},
{0x4e8e,0x4e8e,0x272},
{0x4e91,0x4e91,0x29b},
{0x4e92,0x4e92,0x29d},
{0x4e93,0x4e93,0x177c},
{0x4e94,0x4e94,0x29e},
{0x4e95,0x4e95,0x29c},
{0x4e98,0x4e98,0x39c4},
{0x4e99,0x4e99,0x373},
{0x4e9a,0x4e9a,0x48d5},
{0x4e9b,0x4e9b,0x52c},
{0x4e9e,0x4e9e,0x52d},
{0x4e9f,0x4e9f,0x6a3},
{0x4ea0,0x4ea0,0x21c},
{0x4ea1,0x4ea1,0x273},
{0x4ea2,0x4ea2,0x29f},
{0x4ea4,0x4ea4,0x374},
{0x4ea5,0x4ea5,0x376},
{0x4ea6,0x4ea6,0x375},
{0x4ea8,0x4ea8,0x416},
{0x4eab,0x4eac,0x52e},
{0x4ead,0x4eae,0x6a4},
{0x4eb3,0x4eb3,0x832},
{0x4eb6,0x4eb6,0x2363},
{0x4eb7,0x4eb7,0x413c},
{0x4eb9,0x4eb9,0x3377},
{0x4eba,0x4eba,0x25b},
{0x4ebb,0x4ebb,0x44e6},
{0x4ebc,0x4ebc,0x39b1},
{0x4ebf,0x4ebf,0x4590},
{0x4ec0,0x4ec0,0x2a1},
{0x4ec1,0x4ec1,0x2a0},
{0x4ec2,0x4ec2,0x177d},
{0x4ec3,0x4ec3,0x2a2},
{0x4ec4,0x4ec4,0x2a8},
{0x4ec6,0x4ec7,0x2a3},
{0x4ec8,0x4ec8,0x177f},
{0x4ec9,0x4ec9,0x177e},
{0x4eca,0x4ecb,0x2a6},
{0x4ecd,0x4ecd,0x2a5},
{0x4ece,0x4ece,0x3f62},
{0x4ed4,0x4ed7,0x2fa},
{0x4ed8,0x4ed8,0x2f9},
{0x4ed9,0x4ed9,0x300},
{0x4eda,0x4eda,0x1795},
{0x4edc,0x4edc,0x1791},
{0x4edd,0x4edd,0x1794},
{0x4ede,0x4ede,0x301},
{0x4edf,0x4edf,0x311},
{0x4ee1,0x4ee1,0x1793},
{0x4ee3,0x4ee4,0x2fe},
{0x4ee5,0x4ee5,0x2f8},
{0x4ee8,0x4ee8,0x1790},
{0x4ee9,0x4ee9,0x1792},
{0x4eea,0x4eea,0x48cb},
{0x4eeb,0x4eeb,0x4591},
{0x4eee,0x4eee,0x3d76},
{0x4ef0,0x4ef0,0x383},
{0x4ef1,0x4ef1,0x17ba},
{0x4ef2,0x4ef2,0x380},
{0x4ef3,0x4ef3,0x384},
{0x4ef4,0x4ef4,0x17c4},
{0x4ef5,0x4ef5,0x17b8},
{0x4ef6,0x4ef6,0x381},
{0x4ef7,0x4ef7,0x17bc},
{0x4ef8,0x4ef8,0x39ad},
{0x4efb,0x4efb,0x382},
{0x4efd,0x4efd,0x385},
{0x4eff,0x4eff,0x377},
{0x4f00,0x4f00,0x17bb},
{0x4f01,0x4f01,0x386},
{0x4f02,0x4f02,0x17bf},
{0x4f03,0x4f03,0x39c8},
{0x4f04,0x4f04,0x17c3},
{0x4f05,0x4f05,0x17c0},
{0x4f08,0x4f08,0x17bd},
{0x4f09,0x4f09,0x378},
{0x4f0a,0x4f0a,0x37a},
{0x4f0b,0x4f0b,0x387},
{0x4f0d,0x4f0d,0x37c},
{0x4f0e,0x4f0e,0x17b5},
{0x4f0f,0x4f0f,0x37f},
{0x4f10,0x4f11,0x37d},
{0x4f12,0x4f12,0x17c5},
{0x4f13,0x4f13,0x17c2},
{0x4f14,0x4f14,0x17b9},
{0x4f15,0x4f15,0x37b},
{0x4f18,0x4f18,0x17b6},
{0x4f19,0x4f19,0x379},
{0x4f1a,0x4f1a,0x453c},
{0x4f1d,0x4f1d,0x17be},
{0x4f22,0x4f22,0x17c1},
{0x4f28,0x4f28,0x453d},
{0x4f29,0x4f29,0x39be},
{0x4f2c,0x4f2c,0x17b7},
{0x4f2d,0x4f2d,0x182f},
{0x4f2f,0x4f2f,0x42c},
{0x4f30,0x4f30,0x41f},
{0x4f32,0x4f32,0x393d},
{0x4f33,0x4f33,0x1830},
{0x4f34,0x4f34,0x41c},
{0x4f36,0x4f36,0x42e},
{0x4f37,0x4f37,0x4592},
{0x4f38,0x4f38,0x424},
{0x4f39,0x4f39,0x3f65},
{0x4f3a,0x4f3a,0x423},
{0x4f3b,0x4f3b,0x1824},
{0x4f3c,0x4f3c,0x427},
{0x4f3d,0x4f3d,0x422},
{0x4f3e,0x4f3e,0x1829},
{0x4f3f,0x4f3f,0x1831},
{0x4f41,0x4f41,0x182d},
{0x4f42,0x4f42,0x39cb},
{0x4f43,0x4f43,0x425},
{0x4f45,0x4f45,0x3b8f},
{0x4f46,0x4f46,0x428},
{0x4f47,0x4f47,0x419},
{0x4f48,0x4f48,0x431},
{0x4f49,0x4f49,0x1826},
{0x4f4b,0x4f4b,0x39b4},
{0x4f4c,0x4f4c,0x1900},
{0x4f4d,0x4f4d,0x417},
{0x4f4e,0x4f4e,0x42d},
{0x4f4f,0x4f4f,0x418},
{0x4f50,0x4f51,0x420},
{0x4f52,0x4f52,0x182b},
{0x4f53,0x4f53,0x1827},
{0x4f54,0x4f54,0x426},
{0x4f55,0x4f55,0x41e},
{0x4f56,0x4f56,0x1823},
{0x4f57,0x4f57,0x41a},
{0x4f58,0x4f58,0x182e},
{0x4f59,0x4f59,0x42f},
{0x4f5a,0x4f5a,0x432},
{0x4f5b,0x4f5b,0x41d},
{0x4f5c,0x4f5c,0x42a},
{0x4f5d,0x4f5d,0x430},
{0x4f5e,0x4f5e,0x41b},
{0x4f5f,0x4f5f,0x182c},
{0x4f60,0x4f60,0x42b},
{0x4f61,0x4f61,0x1832},
{0x4f62,0x4f62,0x1825},
{0x4f63,0x4f63,0x429},
{0x4f64,0x4f64,0x1828},
{0x4f67,0x4f67,0x182a},
{0x4f69,0x4f69,0x53d},
{0x4f6a,0x4f6a,0x1902},
{0x4f6b,0x4f6b,0x190e},
{0x4f6c,0x4f6c,0x535},
{0x4f6e,0x4f6e,0x190f},
{0x4f6f,0x4f6f,0x530},
{0x4f70,0x4f70,0x53a},
{0x4f72,0x4f72,0x3c72},
{0x4f73,0x4f73,0x533},
{0x4f74,0x4f74,0x18fc},
{0x4f75,0x4f75,0x53b},
{0x4f76,0x4f76,0x18fb},
{0x4f77,0x4f77,0x18ff},
{0x4f78,0x4f78,0x1906},
{0x4f79,0x4f79,0x1904},
{0x4f7a,0x4f7a,0x543},
{0x4f7b,0x4f7b,0x53e},
{0x4f7c,0x4f7c,0x18f6},
{0x4f7d,0x4f7d,0x18f8},
{0x4f7e,0x4f7e,0x540},
{0x4f7f,0x4f7f,0x534},
{0x4f80,0x4f80,0x18f9},
{0x4f81,0x4f81,0x1905},
{0x4f82,0x4f82,0x190c},
{0x4f83,0x4f83,0x539},
{0x4f84,0x4f84,0x18fe},
{0x4f85,0x4f85,0x18f7},
{0x4f86,0x4f86,0x538},
{0x4f87,0x4f87,0x18fa},
{0x4f88,0x4f88,0x53c},
{0x4f89,0x4f89,0x18fd},
{0x4f8a,0x4f8a,0x39b5},
{0x4f8b,0x4f8b,0x537},
{0x4f8d,0x4f8d,0x532},
{0x4f8f,0x4f8f,0x541},
{0x4f90,0x4f90,0x1907},
{0x4f91,0x4f91,0x542},
{0x4f92,0x4f92,0x190b},
{0x4f94,0x4f94,0x1909},
{0x4f95,0x4f95,0x190d},
{0x4f96,0x4f96,0x53f},
{0x4f97,0x4f97,0x1901},
{0x4f98,0x4f98,0x18f5},
{0x4f9a,0x4f9a,0x1903},
{0x4f9b,0x4f9b,0x536},
{0x4f9c,0x4f9c,0x1908},
{0x4f9d,0x4f9d,0x531},
{0x4f9e,0x4f9e,0x190a},
{0x4fa2,0x4fa2,0x39c7},
{0x4fa8,0x4fa8,0x453e},
{0x4fab,0x4fab,0x4022},
{0x4fae,0x4fae,0x6b4},
{0x4faf,0x4faf,0x6a8},
{0x4fb0,0x4fb0,0x3d4a},
{0x4fb2,0x4fb2,0x1a5d},
{0x4fb3,0x4fb3,0x1a65},
{0x4fb5,0x4fb5,0x6a7},
{0x4fb6,0x4fb6,0x6af},
{0x4fb7,0x4fb7,0x6bb},
{0x4fb9,0x4fb9,0x1a6b},
{0x4fba,0x4fba,0x1a69},
{0x4fbb,0x4fbb,0x1a64},
{0x4fbd,0x4fbd,0x4594},
{0x4fbf,0x4fbf,0x6a9},
{0x4fc0,0x4fc0,0x1a6a},
{0x4fc1,0x4fc1,0x1a60},
{0x4fc2,0x4fc2,0x6b7},
{0x4fc3,0x4fc3,0x6ae},
{0x4fc4,0x4fc4,0x6b6},
{0x4fc5,0x4fc5,0x1a5b},
{0x4fc7,0x4fc7,0x1a67},
{0x4fc8,0x4fc8,0x46e8},
{0x4fc9,0x4fc9,0x1a5e},
{0x4fca,0x4fca,0x6b2},
{0x4fcb,0x4fcb,0x1a5f},
{0x4fcc,0x4fcc,0x39cf},
{0x4fcd,0x4fcd,0x1a5a},
{0x4fce,0x4fce,0x6b9},
{0x4fcf,0x4fcf,0x6ac},
{0x4fd0,0x4fd0,0x6b5},
{0x4fd1,0x4fd1,0x6ab},
{0x4fd3,0x4fd3,0x1a5c},
{0x4fd4,0x4fd4,0x1a61},
{0x4fd6,0x4fd6,0x1a68},
{0x4fd7,0x4fd7,0x6b3},
{0x4fd8,0x4fd8,0x6b0},
{0x4fd9,0x4fd9,0x1a63},
{0x4fda,0x4fda,0x6b8},
{0x4fdb,0x4fdb,0x1a66},
{0x4fdc,0x4fdc,0x1a62},
{0x4fdd,0x4fdd,0x6ad},
{0x4fde,0x4fde,0x6ba},
{0x4fdf,0x4fdf,0x6b1},
{0x4fe0,0x4fe0,0x6aa},
{0x4fe1,0x4fe1,0x6a6},
{0x4fe4,0x4fe4,0x3c66},
{0x4fe5,0x4fe5,0x39d0},
{0x4fec,0x4fec,0x1a6c},
{0x4fee,0x4fee,0x84c},
{0x4fef,0x4fef,0x836},
{0x4ff1,0x4ff1,0x846},
{0x4ff2,0x4ff2,0x3f28},
{0x4ff3,0x4ff3,0x84b},
{0x4ff4,0x4ff4,0x1c24},
{0x4ff5,0x4ff5,0x1c23},
{0x4ff6,0x4ff7,0x1c28},
{0x4ff8,0x4ff8,0x839},
{0x4ff9,0x4ff9,0x37b3},
{0x4ffa,0x4ffa,0x842},
{0x4ffd,0x4ffd,0x3f26},
{0x4ffe,0x4ffe,0x84f},
{0x5000,0x5000,0x843},
{0x5003,0x5003,0x4596},
{0x5005,0x5005,0x1c1d},
{0x5006,0x5006,0x83c},
{0x5007,0x5007,0x1c1e},
{0x5008,0x5008,0x4024},
{0x5009,0x5009,0x851},
{0x500b,0x500b,0x848},
{0x500c,0x500d,0x833},
{0x500e,0x500e,0x1c31},
{0x500f,0x500f,0xa0c},
{0x5011,0x5011,0x841},
{0x5012,0x5012,0x840},
{0x5013,0x5013,0x1c1f},
{0x5014,0x5014,0x844},
{0x5015,0x5015,0x1e45},
{0x5016,0x5016,0x83b},
{0x5017,0x5017,0x1c2a},
{0x5018,0x5018,0x84a},
{0x5019,0x5019,0x849},
{0x501a,0x501a,0x83f},
{0x501b,0x501b,0x1c22},
{0x501c,0x501c,0x1c2b},
{0x501e,0x501e,0x1c1c},
{0x501f,0x501f,0x83e},
{0x5020,0x5020,0x1c2c},
{0x5021,0x5021,0x847},
{0x5022,0x5022,0x1c20},
{0x5023,0x5023,0x835},
{0x5025,0x5025,0x838},
{0x5026,0x5026,0x837},
{0x5027,0x5027,0x1c2d},
{0x5028,0x5028,0x845},
{0x5029,0x5029,0x83a},
{0x502a,0x502a,0x84e},
{0x502b,0x502b,0x850},
{0x502c,0x502c,0x1c27},
{0x502d,0x502d,0x84d},
{0x502e,0x502e,0x39cc},
{0x502f,0x502f,0x1c2f},
{0x5030,0x5030,0x1c21},
{0x5031,0x5031,0x1c30},
{0x5033,0x5033,0x1c25},
{0x5034,0x5034,0x3910},
{0x5035,0x5035,0x1c2e},
{0x5037,0x5037,0x1c26},
{0x503c,0x503c,0x83d},
{0x5040,0x5040,0x1e4d},
{0x5041,0x5041,0x1e41},
{0x5043,0x5043,0xa00},
{0x5045,0x5045,0x1e46},
{0x5046,0x5046,0x1e4c},
{0x5047,0x5047,0x9ff},
{0x5048,0x5048,0x1e3f},
{0x5049,0x5049,0xa03},
{0x504a,0x504a,0x1e43},
{0x504b,0x504b,0x1e3c},
{0x504c,0x504c,0xa01},
{0x504d,0x504d,0x1e40},
{0x504e,0x504e,0xa06},
{0x504f,0x504f,0xa0b},
{0x5051,0x5051,0x1e51},
{0x5053,0x5053,0x1e3b},
{0x5055,0x5055,0xa07},
{0x5056,0x5056,0x3f2c},
{0x5057,0x5057,0x1e50},
{0x5058,0x5058,0x39d1},
{0x505a,0x505a,0xa02},
{0x505b,0x505b,0x1e42},
{0x505c,0x505c,0x9fe},
{0x505d,0x505d,0x1e3d},
{0x505e,0x505e,0x1e39},
{0x505f,0x505f,0x1e47},
{0x5060,0x5060,0x1e3a},
{0x5061,0x5061,0x1e38},
{0x5062,0x5062,0x1e44},
{0x5063,0x5064,0x1e4a},
{0x5065,0x5065,0xa04},
{0x5066,0x5066,0x3dc9},
{0x5068,0x5068,0x20b8},
{0x5069,0x5069,0x1e48},
{0x506a,0x506a,0x1e37},
{0x506b,0x506b,0x1e49},
{0x506c,0x506c,0x39cd},
{0x506d,0x506d,0xa0e},
{0x506e,0x506e,0x1e4e},
{0x506f,0x506f,0xa0d},
{0x5070,0x5070,0x1e36},
{0x5072,0x5072,0x1e3e},
{0x5073,0x5073,0x1e4f},
{0x5074,0x5074,0xa09},
{0x5075,0x5075,0xa08},
{0x5076,0x5076,0xa05},
{0x5077,0x5077,0xa0a},
{0x507a,0x507a,0x9fc},
{0x507d,0x507d,0x9fd},
{0x5080,0x5080,0xbec},
{0x5081,0x5081,0x39ce},
{0x5082,0x5082,0x20bb},
{0x5083,0x5083,0x20b4},
{0x5085,0x5085,0xbe9},
{0x5087,0x5087,0x20bc},
{0x5088,0x5088,0x439c},
{0x508b,0x508b,0x20b2},
{0x508c,0x508c,0x20b5},
{0x508d,0x508d,0xbe8},
{0x508e,0x508e,0x20b6},
{0x5090,0x5090,0x41ec},
{0x5091,0x5091,0xbeb},
{0x5092,0x5092,0x20ba},
{0x5094,0x5094,0x20b0},
{0x5095,0x5095,0x20af},
{0x5096,0x5096,0xbed},
{0x5098,0x5098,0xbee},
{0x5099,0x5099,0xbea},
{0x509a,0x509a,0xbef},
{0x509b,0x509b,0x20ae},
{0x509c,0x509c,0x20b9},
{0x509d,0x509d,0x20b7},
{0x509e,0x509e,0x20b1},
{0x50a2,0x50a2,0xbe7},
{0x50a3,0x50a3,0x20b3},
{0x50a6,0x50a6,0x3f3f},
{0x50ac,0x50ac,0xdc5},
{0x50ad,0x50ad,0xdbf},
{0x50ae,0x50ae,0x2367},
{0x50af,0x50af,0xdc8},
{0x50b0,0x50b0,0x236d},
{0x50b1,0x50b1,0x2370},
{0x50b2,0x50b3,0xdc1},
{0x50b4,0x50b4,0x236a},
{0x50b5,0x50b5,0xdc0},
{0x50b6,0x50b6,0x2373},
{0x50b7,0x50b7,0xdc6},
{0x50b8,0x50b8,0x2374},
{0x50ba,0x50ba,0x236f},
{0x50bb,0x50bb,0xdc7},
{0x50bd,0x50bd,0x2364},
{0x50be,0x50be,0xdc4},
{0x50bf,0x50bf,0x2365},
{0x50c1,0x50c1,0x236e},
{0x50c2,0x50c2,0x236c},
{0x50c4,0x50c4,0x2368},
{0x50c5,0x50c5,0xdc3},
{0x50c6,0x50c6,0x2366},
{0x50c7,0x50c7,0xdc9},
{0x50c8,0x50c8,0x236b},
{0x50c9,0x50c9,0x2372},
{0x50ca,0x50ca,0x2369},
{0x50cb,0x50cb,0x2371},
{0x50cd,0x50cd,0x39c5},
{0x50ce,0x50ce,0xf88},
{0x50cf,0x50cf,0xf85},
{0x50d0,0x50d0,0x38d1},
{0x50d1,0x50d1,0xf86},
{0x50d3,0x50d3,0x261c},
{0x50d4,0x50d4,0x2614},
{0x50d5,0x50d5,0xf84},
{0x50d6,0x50d6,0xf81},
{0x50d7,0x50d7,0x2615},
{0x50d9,0x50d9,0x3fce},
{0x50da,0x50da,0xf83},
{0x50db,0x50db,0x2618},
{0x50dd,0x50dd,0x261a},
{0x50de,0x50de,0x4031},
{0x50df,0x50df,0x3afd},
{0x50e0,0x50e0,0x2621},
{0x50e1,0x50e1,0x4171},
{0x50e3,0x50e3,0x2620},
{0x50e4,0x50e4,0x261b},
{0x50e5,0x50e5,0xf80},
{0x50e6,0x50e6,0x2613},
{0x50e7,0x50e7,0xf7e},
{0x50e8,0x50e8,0x2616},
{0x50e9,0x50e9,0xf89},
{0x50ea,0x50ea,0x2619},
{0x50ec,0x50ec,0x261d},
{0x50ed,0x50ed,0xf82},
{0x50ee,0x50ee,0xf7f},
{0x50ef,0x50ef,0x261f},
{0x50f0,0x50f0,0x261e},
{0x50f1,0x50f1,0xf87},
{0x50f3,0x50f3,0x2617},
{0x50f4,0x50f4,0x3ce9},
{0x50f5,0x50f5,0x1105},
{0x50f6,0x50f6,0x2883},
{0x50f8,0x50f8,0x2880},
{0x50f9,0x50f9,0x1106},
{0x50fb,0x50fb,0x1104},
{0x50fc,0x50fc,0x39d2},
{0x50fd,0x50fd,0x2887},
{0x50fe,0x50fe,0x2884},
{0x50ff,0x50ff,0x287d},
{0x5100,0x5100,0x1103},
{0x5101,0x5101,0x4032},
{0x5102,0x5102,0x1107},
{0x5103,0x5103,0x287e},
{0x5104,0x5104,0x1102},
{0x5105,0x5105,0x110a},
{0x5106,0x5107,0x2881},
{0x5108,0x5109,0x1108},
{0x510a,0x510a,0x2888},
{0x510b,0x510c,0x2885},
{0x510d,0x510d,0x39c6},
{0x510e,0x510e,0x4034},
{0x5110,0x5110,0x128d},
{0x5111,0x5111,0x2b04},
{0x5112,0x5112,0x128a},
{0x5113,0x5113,0x2b01},
{0x5114,0x5114,0x128c},
{0x5115,0x5115,0x128e},
{0x5117,0x5117,0x2b02},
{0x5118,0x5118,0x128b},
{0x511a,0x511a,0x2b03},
{0x511c,0x511c,0x2b00},
{0x511f,0x511f,0x13b5},
{0x5120,0x5120,0x2d3e},
{0x5121,0x5121,0x13b6},
{0x5122,0x5122,0x2d3c},
{0x5124,0x5124,0x2d3d},
{0x5125,0x5125,0x2d3b},
{0x5126,0x5126,0x2d3a},
{0x5129,0x5129,0x2d3f},
{0x512a,0x512a,0x13b4},
{0x512b,0x512b,0x39ae},
{0x512d,0x512e,0x2f0f},
{0x5130,0x5130,0x287f},
{0x5131,0x5131,0x2f0e},
{0x5132,0x5132,0x13b7},
{0x5133,0x5133,0x1575},
{0x5134,0x5135,0x307a},
{0x5137,0x5138,0x1668},
{0x5139,0x5139,0x32c6},
{0x513a,0x513a,0x32c5},
{0x513b,0x513b,0x16bc},
{0x513c,0x513c,0x16bb},
{0x513d,0x513d,0x340e},
{0x513f,0x513f,0x25c},
{0x5140,0x5140,0x274},
{0x5141,0x5141,0x2aa},
{0x5143,0x5143,0x2a9},
{0x5144,0x5144,0x303},
{0x5145,0x5145,0x302},
{0x5146,0x5146,0x38a},
{0x5147,0x5147,0x389},
{0x5148,0x5148,0x38b},
{0x5149,0x5149,0x388},
{0x514b,0x514b,0x434},
{0x514c,0x514c,0x433},
{0x514d,0x514d,0x435},
{0x5152,0x5152,0x545},
{0x5154,0x5154,0x544},
{0x5155,0x5155,0x546},
{0x5156,0x5156,0x439a},
{0x5157,0x5157,0x6bc},
{0x5159,0x5159,0x119},
{0x515a,0x515a,0x1c32},
{0x515b,0x515b,0x11a},
{0x515c,0x515c,0xa0f},
{0x515d,0x515d,0x11c},
{0x515e,0x515e,0x11b},
{0x515f,0x515f,0x20bd},
{0x5160,0x5160,0x403a},
{0x5161,0x5161,0x11d},
{0x5162,0x5162,0xf8a},
{0x5163,0x5163,0x11e},
{0x5165,0x5165,0x25d},
{0x5167,0x5167,0x2ab},
{0x5168,0x5168,0x38c},
{0x5169,0x5169,0x547},
{0x516a,0x516a,0x403c},
{0x516b,0x516b,0x25e},
{0x516c,0x516c,0x2ae},
{0x516d,0x516e,0x2ac},
{0x5171,0x5171,0x38d},
{0x5174,0x5174,0x453f},
{0x5175,0x5175,0x436},
{0x5176,0x5176,0x549},
{0x5177,0x5177,0x548},
{0x5178,0x5178,0x54a},
{0x5179,0x5179,0x3ace},
{0x517c,0x517c,0x852},
{0x5180,0x5180,0x128f},
{0x5182,0x5182,0x21d},
{0x5186,0x5186,0x439f},
{0x5187,0x5187,0x1779},
{0x5188,0x5188,0x36b6},
{0x5189,0x518a,0x304},
{0x518d,0x518d,0x38e},
{0x518f,0x518f,0x1833},
{0x5191,0x5191,0x6be},
{0x5192,0x5192,0x6bd},
{0x5193,0x5193,0x1c34},
{0x5194,0x5194,0x1c33},
{0x5195,0x5195,0xa10},
{0x5196,0x5196,0x21e},
{0x5197,0x5197,0x2af},
{0x5198,0x5198,0x1780},
{0x519a,0x519a,0x3ea3},
{0x519c,0x519c,0x4540},
{0x519e,0x519e,0x1910},
{0x51a0,0x51a0,0x6bf},
{0x51a2,0x51a2,0x855},
{0x51a4,0x51a5,0x853},
{0x51a7,0x51a7,0x3ea6},
{0x51a8,0x51a8,0x39da},
{0x51aa,0x51aa,0x1290},
{0x51ab,0x51ab,0x21f},
{0x51ac,0x51ac,0x306},
{0x51b0,0x51b0,0x38f},
{0x51b1,0x51b1,0x17c6},
{0x51b2,0x51b2,0x4048},
{0x51b3,0x51b3,0x4051},
{0x51b4,0x51b4,0x3d7c},
{0x51b5,0x51b5,0x4012},
{0x51b6,0x51b7,0x437},
{0x51b8,0x51b8,0x4049},
{0x51b9,0x51b9,0x1834},
{0x51bc,0x51bc,0x1911},
{0x51bd,0x51bd,0x54b},
{0x51be,0x51be,0x1912},
{0x51c3,0x51c3,0x39db},
{0x51c4,0x51c5,0x1c36},
{0x51c6,0x51c6,0x858},
{0x51c7,0x51c7,0x422a},
{0x51c8,0x51c8,0x1c38},
{0x51c9,0x51c9,0x404b},
{0x51ca,0x51ca,0x1c35},
{0x51cb,0x51cb,0x859},
{0x51cc,0x51cc,0x857},
{0x51cd,0x51cd,0x856},
{0x51ce,0x51ce,0x1c39},
{0x51cf,0x51cf,0x404c},
{0x51d0,0x51d0,0x1e52},
{0x51d1,0x51d1,0x404d},
{0x51d2,0x51d2,0x3dad},
{0x51d3,0x51d3,0x404f},
{0x51d4,0x51d4,0x20be},
{0x51d7,0x51d7,0x2375},
{0x51d8,0x51d8,0x2622},
{0x51db,0x51db,0x459a},
{0x51dc,0x51dc,0x110b},
{0x51dd,0x51dd,0x1291},
{0x51de,0x51de,0x2b05},
{0x51df,0x51df,0x424f},
{0x51e0,0x51e0,0x25f},
{0x51e1,0x51e1,0x26d},
{0x51e4,0x51e4,0x4541},
{0x51ed,0x51ed,0x4054},
{0x51f0,0x51f0,0xa11},
{0x51f1,0x51f1,0xbf1},
{0x51f3,0x51f3,0xf8b},
{0x51f4,0x51f4,0x4178},
{0x51f5,0x51f5,0x176e},
{0x51f6,0x51f6,0x2b0},
{0x51f8,0x51f8,0x309},
{0x51f9,0x51fa,0x307},
{0x51fc,0x51fc,0x459b},
{0x51fd,0x51fd,0x54c},
{0x51fe,0x51fe,0x3f0a},
{0x5200,0x5201,0x260},
{0x5202,0x5202,0x36b3},
{0x5203,0x5203,0x275},
{0x5205,0x5205,0x43a1},
{0x5206,0x5208,0x2b1},
{0x5209,0x5209,0x1797},
{0x520a,0x520a,0x30a},
{0x520b,0x520b,0x4059},
{0x520c,0x520c,0x1796},
{0x520e,0x520e,0x393},
{0x5210,0x5210,0x17c8},
{0x5211,0x5212,0x391},
{0x5213,0x5213,0x17c7},
{0x5216,0x5216,0x394},
{0x5217,0x5217,0x390},
{0x521c,0x521c,0x1835},
{0x521d,0x521d,0x68b},
{0x521e,0x521e,0x1836},
{0x521f,0x521f,0x3f60},
{0x5220,0x5220,0x4930},
{0x5221,0x5221,0x1837},
{0x5224,0x5224,0x43a},
{0x5225,0x5225,0x439},
{0x5226,0x5226,0x405a},
{0x5227,0x5227,0x43a2},
{0x5228,0x5228,0x43d},
{0x5229,0x522a,0x43b},
{0x522e,0x522e,0x552},
{0x5230,0x5230,0x551},
{0x5231,0x5231,0x1917},
{0x5232,0x5233,0x1914},
{0x5234,0x5234,0x3efb},
{0x5235,0x5235,0x1913},
{0x5236,0x5236,0x553},
{0x5237,0x5237,0x54f},
{0x5238,0x5238,0x54e},
{0x523a,0x523a,0x550},
{0x523b,0x523b,0x54d},
{0x523c,0x523c,0x405b},
{0x5241,0x5241,0x554},
{0x5243,0x5243,0x6c1},
{0x5244,0x5244,0x1a6d},
{0x5246,0x5246,0x1916},
{0x5247,0x5247,0x6c6},
{0x5249,0x5249,0x1a6e},
{0x524a,0x524a,0x6c2},
{0x524b,0x524b,0x6c5},
{0x524c,0x524c,0x6c4},
{0x524d,0x524d,0x6c3},
{0x524e,0x524e,0x6c0},
{0x5252,0x5252,0x1c3c},
{0x5254,0x5254,0x85c},
{0x5255,0x5255,0x1c3f},
{0x5256,0x5256,0x85a},
{0x5257,0x5257,0x405d},
{0x5259,0x5259,0x39e4},
{0x525a,0x525a,0x1c3b},
{0x525b,0x525b,0x85d},
{0x525c,0x525c,0x85b},
{0x525d,0x525d,0x85e},
{0x525e,0x525f,0x1c3d},
{0x5260,0x5260,0x3f29},
{0x5261,0x5261,0x1c3a},
{0x5262,0x5262,0x1c40},
{0x5268,0x5268,0x4619},
{0x5269,0x5269,0xbf5},
{0x526a,0x526a,0xa12},
{0x526b,0x526b,0x1e53},
{0x526c,0x526c,0x1e55},
{0x526d,0x526d,0x1e54},
{0x526e,0x526e,0x1e56},
{0x526f,0x526f,0xa13},
{0x5272,0x5272,0xbf2},
{0x5273,0x5273,0x3f43},
{0x5274,0x5275,0xbf3},
{0x5277,0x5277,0xdcb},
{0x5278,0x5278,0x2377},
{0x5279,0x5279,0x43a3},
{0x527a,0x527a,0x2376},
{0x527b,0x527c,0x2378},
{0x527d,0x527d,0xdcc},
{0x527f,0x527f,0xdca},
{0x5280,0x5281,0x2623},
{0x5282,0x5282,0xf8d},
{0x5283,0x5283,0xf8c},
{0x5284,0x5284,0x2776},
{0x5287,0x5289,0x110c},
{0x528a,0x528a,0x1110},
{0x528b,0x528c,0x2889},
{0x528d,0x528d,0x110f},
{0x528f,0x528f,0x3dbf},
{0x5290,0x5290,0x459d},
{0x5291,0x5291,0x1292},
{0x5293,0x5293,0x1293},
{0x5294,0x5294,0x405e},
{0x5296,0x5296,0x307c},
{0x5297,0x5297,0x32c8},
{0x5298,0x5298,0x32c7},
{0x5299,0x5299,0x340f},
{0x529a,0x529a,0x3f68},
{0x529b,0x529b,0x262},
{0x529f,0x529f,0x30c},
{0x52a0,0x52a0,0x30b},
{0x52a1,0x52a1,0x4542},
{0x52a3,0x52a3,0x395},
{0x52a4,0x52a4,0x39e5},
{0x52a6,0x52a6,0x17c9},
{0x52a8,0x52a8,0x4543},
{0x52a9,0x52aa,0x43f},
{0x52ab,0x52ab,0x43e},
{0x52ac,0x52ac,0x441},
{0x52ad,0x52ae,0x1838},
{0x52b5,0x52b5,0x405c},
{0x52b9,0x52b9,0x405f},
{0x52bb,0x52bb,0x556},
{0x52bc,0x52bc,0x1918},
{0x52be,0x52be,0x555},
{0x52c0,0x52c0,0x1a6f},
{0x52c1,0x52c1,0x6ca},
{0x52c2,0x52c2,0x1a70},
{0x52c3,0x52c3,0x6c9},
{0x52c5,0x52c5,0x4060},
{0x52c7,0x52c7,0x6c7},
{0x52c9,0x52c9,0x6c8},
{0x52cc,0x52cc,0x3ee1},
{0x52cd,0x52cd,0x1c41},
{0x52d0,0x52d0,0x4109},
{0x52d1,0x52d1,0x3f2a},
{0x52d2,0x52d2,0xa14},
{0x52d3,0x52d3,0x1e58},
{0x52d5,0x52d5,0xa17},
{0x52d6,0x52d6,0x1e57},
{0x52d7,0x52d7,0xacf},
{0x52d8,0x52d8,0xa16},
{0x52d9,0x52d9,0xa15},
{0x52db,0x52db,0xbf8},
{0x52dd,0x52dd,0xbf7},
{0x52de,0x52de,0xbf6},
{0x52df,0x52df,0xdcd},
{0x52e0,0x52e0,0x4063},
{0x52e1,0x52e1,0x39e7},
{0x52e2,0x52e3,0xdd0},
{0x52e4,0x52e4,0xdcf},
{0x52e6,0x52e6,0xdce},
{0x52e9,0x52e9,0x2625},
{0x52eb,0x52eb,0x2626},
{0x52ef,0x52ef,0x288c},
{0x52f0,0x52f0,0x1111},
{0x52f1,0x52f1,0x288b},
{0x52f3,0x52f3,0x1294},
{0x52f4,0x52f4,0x2d40},
{0x52f5,0x52f5,0x13b8},
{0x52f7,0x52f7,0x307d},
{0x52f8,0x52f8,0x1609},
{0x52f9,0x52f9,0x220},
{0x52fa,0x52fa,0x276},
{0x52fb,0x52fb,0x2b4},
{0x52fc,0x52fc,0x1781},
{0x52fe,0x52ff,0x2b5},
{0x5301,0x5301,0x3d79},
{0x5305,0x5306,0x30d},
{0x5308,0x5308,0x396},
{0x5309,0x5309,0x183a},
{0x530a,0x530b,0x1919},
{0x530d,0x530d,0x6cb},
{0x530e,0x530e,0x1c42},
{0x530f,0x530f,0xa19},
{0x5310,0x5310,0xa18},
{0x5311,0x5311,0x20c0},
{0x5312,0x5312,0x20bf},
{0x5315,0x5315,0x263},
{0x5316,0x5316,0x2b7},
{0x5317,0x5317,0x30f},
{0x5319,0x5319,0xa1a},
{0x531a,0x531a,0x176f},
{0x531c,0x531c,0x1798},
{0x531d,0x531d,0x310},
{0x531f,0x531f,0x17cb},
{0x5320,0x5320,0x398},
{0x5321,0x5321,0x397},
{0x5322,0x5322,0x17ca},
{0x5323,0x5323,0x442},
{0x5327,0x5327,0x459e},
{0x532a,0x532a,0x85f},
{0x532c,0x532c,0x3f36},
{0x532d,0x532d,0x1e59},
{0x532f,0x532f,0xdd2},
{0x5330,0x5330,0x2627},
{0x5331,0x5331,0xf8e},
{0x5332,0x5332,0x3fd0},
{0x5333,0x5333,0x406d},
{0x5334,0x5334,0x2b06},
{0x5337,0x5337,0x31c7},
{0x5338,0x5338,0x221},
{0x5339,0x5339,0x2b8},
{0x533b,0x533b,0x4544},
{0x533c,0x533c,0x191b},
{0x533d,0x533d,0x1a71},
{0x533e,0x533e,0xa1d},
{0x533f,0x5340,0xa1b},
{0x5341,0x5341,0x264},
{0x5342,0x5342,0x4947},
{0x5343,0x5343,0x277},
{0x5344,0x5344,0x16b},
{0x5345,0x5345,0x2bb},
{0x5347,0x5347,0x2ba},
{0x5348,0x5348,0x2b9},
{0x5349,0x5349,0x313},
{0x534a,0x534a,0x312},
{0x534c,0x534c,0x1799},
{0x534d,0x534d,0x17cc},
{0x534e,0x534e,0x4545},
{0x5351,0x5351,0x55a},
{0x5352,0x5352,0x557},
{0x5353,0x5353,0x559},
{0x5354,0x5354,0x558},
{0x5357,0x5357,0x6cc},
{0x535a,0x535a,0xbf9},
{0x535c,0x535c,0x265},
{0x535d,0x535d,0x4501},
{0x535e,0x535e,0x2bc},
{0x535f,0x535f,0x43a7},
{0x5360,0x5360,0x315},
{0x5361,0x5361,0x314},
{0x5363,0x5363,0x183b},
{0x5364,0x5364,0x480a},
{0x5366,0x5366,0x55b},
{0x5367,0x5367,0x3ecd},
{0x5369,0x5369,0x222},
{0x536c,0x536c,0x1782},
{0x536d,0x536d,0x407a},
{0x536e,0x536e,0x317},
{0x536f,0x536f,0x316},
{0x5370,0x5371,0x399},
{0x5372,0x5372,0x183c},
{0x5373,0x5373,0x443},
{0x5374,0x5374,0x407b},
{0x5375,0x5375,0x444},
{0x5377,0x5379,0x55c},
{0x537b,0x537b,0x6cd},
{0x537c,0x537c,0x1a72},
{0x537d,0x537d,0x4901},
{0x537e,0x537e,0x407d},
{0x537f,0x537f,0x860},
{0x5382,0x5382,0x1770},
{0x5384,0x5384,0x2bd},
{0x538a,0x538a,0x17cd},
{0x538e,0x538f,0x183d},
{0x5392,0x5392,0x191c},
{0x5393,0x5393,0x4082},
{0x5394,0x5394,0x191d},
{0x5396,0x5396,0x1a74},
{0x5397,0x5397,0x1a73},
{0x5398,0x5398,0x1a76},
{0x5399,0x5399,0x1a75},
{0x539a,0x539a,0x6ce},
{0x539c,0x539c,0x1e5a},
{0x539d,0x539d,0x862},
{0x539e,0x539e,0x1c43},
{0x539f,0x539f,0x861},
{0x53a0,0x53a0,0x4084},
{0x53a2,0x53a2,0x3e2b},
{0x53a4,0x53a4,0x20c1},
{0x53a5,0x53a5,0xbfa},
{0x53a6,0x53a6,0x413b},
{0x53a7,0x53a7,0x20c2},
{0x53a8,0x53a8,0x413e},
{0x53a9,0x53a9,0x45a0},
{0x53aa,0x53aa,0x3fc6},
{0x53ab,0x53ab,0x4085},
{0x53ac,0x53ac,0x2628},
{0x53ad,0x53ad,0xf8f},
{0x53ae,0x53ae,0x4086},
{0x53b0,0x53b0,0x45a2},
{0x53b2,0x53b2,0x1112},
{0x53b4,0x53b4,0x307e},
{0x53b6,0x53b6,0x223},
{0x53b9,0x53b9,0x1783},
{0x53bb,0x53bb,0x318},
{0x53c1,0x53c1,0x408b},
{0x53c2,0x53c2,0x43a6},
{0x53c3,0x53c3,0xa1e},
{0x53c5,0x53c5,0x408c},
{0x53c8,0x53c8,0x266},
{0x53c9,0x53c9,0x278},
{0x53ca,0x53ca,0x2bf},
{0x53cb,0x53cb,0x2be},
{0x53cc,0x53cc,0x38b6},
{0x53cd,0x53cd,0x2c0},
{0x53d0,0x53d0,0x39fa},
{0x53d1,0x53d1,0x4546},
{0x53d2,0x53d2,0x3e3d},
{0x53d4,0x53d4,0x560},
{0x53d6,0x53d6,0x55f},
{0x53d7,0x53d7,0x561},
{0x53d8,0x53d8,0x4547},
{0x53d9,0x53d9,0x4090},
{0x53da,0x53da,0x3f39},
{0x53db,0x53db,0x6cf},
{0x53df,0x53df,0x863},
{0x53e0,0x53e0,0x4093},
{0x53e1,0x53e1,0x2b07},
{0x53e2,0x53e2,0x14c9},
{0x53e3,0x53e3,0x279},
{0x53e4,0x53e4,0x31a},
{0x53e5,0x53e5,0x329},
{0x53e6,0x53e6,0x324},
{0x53e8,0x53e8,0x31f},
{0x53e9,0x53e9,0x31e},
{0x53ea,0x53ea,0x325},
{0x53eb,0x53eb,0x323},
{0x53ec,0x53ec,0x31c},
{0x53ed,0x53ed,0x32a},
{0x53ee,0x53ee,0x31d},
{0x53ef,0x53ef,0x319},
{0x53f0,0x53f0,0x328},
{0x53f1,0x53f1,0x327},
{0x53f2,0x53f2,0x326},
{0x53f3,0x53f3,0x31b},
{0x53f5,0x53f5,0x322},
{0x53f6,0x53f6,0x4096},
{0x53f7,0x53f7,0x3808},
{0x53f8,0x53f8,0x321},
{0x53fb,0x53fb,0x32b},
{0x53fc,0x53fc,0x320},
{0x53fe,0x53fe,0x40c1},
{0x5401,0x5401,0x3a0},
{0x5403,0x5403,0x3a6},
{0x5404,0x5404,0x3a2},
{0x5406,0x5406,0x3a8},
{0x5407,0x5407,0x17ce},
{0x5408,0x5408,0x3a5},
{0x5409,0x5409,0x39b},
{0x540a,0x540a,0x39e},
{0x540b,0x540b,0x3a1},
{0x540c,0x540c,0x39d},
{0x540d,0x540d,0x3a4},
{0x540e,0x540e,0x3a7},
{0x540f,0x540f,0x39c},
{0x5410,0x5410,0x39f},
{0x5411,0x5411,0x3a3},
{0x5412,0x5412,0x3a9},
{0x5413,0x5413,0x4098},
{0x5414,0x5414,0x3c77},
{0x5416,0x5416,0x3e7f},
{0x5418,0x5418,0x1847},
{0x5419,0x5419,0x1844},
{0x541a,0x541a,0x3e64},
{0x541b,0x541b,0x451},
{0x541c,0x541c,0x1845},
{0x541d,0x541d,0x445},
{0x541e,0x541e,0x447},
{0x541f,0x541f,0x45f},
{0x5420,0x5420,0x45a},
{0x5421,0x5421,0x4203},
{0x5423,0x5423,0x45a3},
{0x5424,0x5424,0x184c},
{0x5425,0x5425,0x1846},
{0x5426,0x5426,0x449},
{0x5427,0x5427,0x44b},
{0x5428,0x5428,0x184b},
{0x5429,0x5429,0x452},
{0x542a,0x542a,0x1841},
{0x542b,0x542b,0x45e},
{0x542c,0x542c,0x460},
{0x542d,0x542d,0x446},
{0x542e,0x542e,0x457},
{0x542f,0x542f,0x40ac},
{0x5430,0x5430,0x183f},
{0x5431,0x5431,0x45d},
{0x5432,0x5432,0x3e75},
{0x5433,0x5433,0x44e},
{0x5435,0x5436,0x458},
{0x5437,0x5437,0x1840},
{0x5438,0x5438,0x456},
{0x5439,0x5439,0x454},
{0x543b,0x543b,0x455},
{0x543c,0x543c,0x45b},
{0x543d,0x543d,0x1848},
{0x543e,0x543e,0x448},
{0x5440,0x5440,0x45c},
{0x5441,0x5441,0x184a},
{0x5442,0x5442,0x450},
{0x5443,0x5443,0x44d},
{0x5445,0x5445,0x1843},
{0x5446,0x5446,0x44c},
{0x5447,0x5447,0x184d},
{0x5448,0x5448,0x44f},
{0x544a,0x544a,0x453},
{0x544b,0x544b,0x3ed1},
{0x544d,0x544d,0x3ea7},
{0x544e,0x544e,0x44a},
{0x544f,0x544f,0x1849},
{0x5454,0x5454,0x1842},
{0x5460,0x5460,0x192e},
{0x5461,0x5461,0x192d},
{0x5462,0x5462,0x573},
{0x5463,0x5463,0x1930},
{0x5464,0x5464,0x1932},
{0x5465,0x5465,0x1927},
{0x5466,0x5466,0x192a},
{0x5467,0x5467,0x1931},
{0x5468,0x5468,0x574},
{0x5469,0x5469,0x3f32},
{0x546a,0x546a,0x3d83},
{0x546b,0x546b,0x1924},
{0x546c,0x546c,0x1928},
{0x546d,0x546d,0x409d},
{0x546f,0x546f,0x192c},
{0x5470,0x5470,0x1a85},
{0x5471,0x5471,0x56f},
{0x5472,0x5472,0x1a89},
{0x5473,0x5473,0x562},
{0x5474,0x5474,0x1929},
{0x5475,0x5475,0x563},
{0x5476,0x5476,0x570},
{0x5477,0x5477,0x569},
{0x5478,0x5478,0x565},
{0x547a,0x547a,0x1925},
{0x547b,0x547b,0x568},
{0x547c,0x547c,0x56d},
{0x547d,0x547d,0x576},
{0x547e,0x547e,0x1926},
{0x547f,0x547f,0x191f},
{0x5480,0x5480,0x567},
{0x5481,0x5481,0x1920},
{0x5482,0x5482,0x1922},
{0x5484,0x5484,0x56a},
{0x5485,0x5485,0x46d9},
{0x5486,0x5486,0x56c},
{0x5487,0x5487,0x191e},
{0x5488,0x5488,0x1923},
{0x548b,0x548b,0x575},
{0x548c,0x548c,0x571},
{0x548d,0x548d,0x192b},
{0x548e,0x548e,0x577},
{0x548f,0x548f,0x40a1},
{0x5490,0x5490,0x56e},
{0x5491,0x5491,0x1921},
{0x5492,0x5492,0x56b},
{0x5493,0x5493,0x45a7},
{0x5494,0x5494,0x3e98},
{0x5495,0x5495,0x566},
{0x5496,0x5496,0x564},
{0x5497,0x5497,0x3e8a},
{0x5498,0x5498,0x192f},
{0x549a,0x549a,0x572},
{0x549c,0x549c,0x3f66},
{0x549e,0x549e,0x47ed},
{0x54a0,0x54a0,0x1a84},
{0x54a1,0x54a1,0x1a78},
{0x54a2,0x54a2,0x1a87},
{0x54a3,0x54a3,0x45a8},
{0x54a4,0x54a4,0x40a2},
{0x54a5,0x54a5,0x1a7a},
{0x54a6,0x54a6,0x6d6},
{0x54a7,0x54a7,0x6e4},
{0x54a8,0x54a8,0x6d2},
{0x54a9,0x54a9,0x6e3},
{0x54aa,0x54aa,0x6db},
{0x54ab,0x54ab,0x6e0},
{0x54ac,0x54ac,0x6d0},
{0x54ad,0x54ad,0x1a79},
{0x54ae,0x54ae,0x1a7f},
{0x54af,0x54af,0x6df},
{0x54b0,0x54b0,0x1a8b},
{0x54b1,0x54b1,0x6e1},
{0x54b2,0x54b2,0x3744},
{0x54b3,0x54b3,0x6d7},
{0x54b4,0x54b4,0x45a9},
{0x54b6,0x54b6,0x1a81},
{0x54b7,0x54b7,0x1a7e},
{0x54b8,0x54b8,0x6d5},
{0x54b9,0x54b9,0x45aa},
{0x54ba,0x54ba,0x1a77},
{0x54bb,0x54bb,0x6e2},
{0x54bc,0x54bc,0x1a86},
{0x54bd,0x54bd,0x6da},
{0x54be,0x54be,0x1a88},
{0x54bf,0x54bf,0x6e5},
{0x54c0,0x54c0,0x6d1},
{0x54c1,0x54c1,0x6dc},
{0x54c2,0x54c2,0x6d9},
{0x54c3,0x54c3,0x1a7c},
{0x54c4,0x54c4,0x6dd},
{0x54c5,0x54c6,0x1a82},
{0x54c7,0x54c7,0x6d8},
{0x54c8,0x54c8,0x6de},
{0x54c9,0x54c9,0x6d4},
{0x54cb,0x54cb,0x39a3},
{0x54cd,0x54cd,0x3a00},
{0x54ce,0x54ce,0x6d3},
{0x54cf,0x54cf,0x1a7b},
{0x54d0,0x54d0,0x45ab},
{0x54d6,0x54d6,0x1a80},
{0x54da,0x54da,0x4923},
{0x54de,0x54de,0x1a8a},
{0x54e0,0x54e0,0x1c57},
{0x54e1,0x54e1,0x870},
{0x54e2,0x54e2,0x1c45},
{0x54e3,0x54e3,0x4341},
{0x54e4,0x54e4,0x1c4a},
{0x54e5,0x54e5,0x869},
{0x54e6,0x54e6,0x874},
{0x54e7,0x54e7,0x1c48},
{0x54e8,0x54e8,0x864},
{0x54e9,0x54e9,0x86e},
{0x54ea,0x54ea,0x873},
{0x54eb,0x54eb,0x1c4f},
{0x54ed,0x54ed,0x86f},
{0x54ee,0x54ee,0x872},
{0x54ef,0x54ef,0x45ac},
{0x54f1,0x54f1,0x1c52},
{0x54f2,0x54f2,0x86a},
{0x54f3,0x54f3,0x1c49},
{0x54f7,0x54f8,0x1c55},
{0x54fa,0x54fa,0x86c},
{0x54fb,0x54fb,0x1c54},
{0x54fc,0x54fc,0x868},
{0x54fd,0x54fd,0x877},
{0x54ff,0x54ff,0x1c4c},
{0x5501,0x5501,0x866},
{0x5502,0x5502,0x3ccc},
{0x5503,0x5503,0x1c59},
{0x5504,0x5504,0x1c4d},
{0x5505,0x5505,0x1c51},
{0x5506,0x5506,0x86b},
{0x5507,0x5507,0x876},
{0x5508,0x5508,0x1c4e},
{0x5509,0x5509,0x871},
{0x550a,0x550a,0x1c53},
{0x550b,0x550b,0x1c5a},
{0x550c,0x550c,0x1e69},
{0x550d,0x550d,0x3a73},
{0x550e,0x550e,0x1c58},
{0x550f,0x550f,0x878},
{0x5510,0x5510,0x865},
{0x5511,0x5511,0x1c50},
{0x5512,0x5512,0x1c47},
{0x5513,0x5513,0x3ea9},
{0x5514,0x5514,0x86d},
{0x5517,0x5517,0x1c46},
{0x5518,0x5518,0x45ad},
{0x551a,0x551a,0x1c4b},
{0x551e,0x551e,0x3ea8},
{0x5523,0x5523,0x45ae},
{0x5525,0x5525,0x4309},
{0x5526,0x5526,0x1c44},
{0x5527,0x5527,0x875},
{0x5528,0x5528,0x45af},
{0x552a,0x552a,0x1e61},
{0x552b,0x552b,0x409a},
{0x552c,0x552c,0xa31},
{0x552d,0x552d,0x1e6f},
{0x552e,0x552e,0xa2f},
{0x552f,0x552f,0xa2c},
{0x5530,0x5530,0x1e66},
{0x5531,0x5531,0xa28},
{0x5532,0x5532,0x1e6a},
{0x5533,0x5533,0xa33},
{0x5534,0x5534,0x1e60},
{0x5535,0x5535,0x1e65},
{0x5536,0x5536,0x1e64},
{0x5537,0x5537,0x867},
{0x5538,0x5538,0xa2e},
{0x5539,0x5539,0x1e6d},
{0x553b,0x553b,0x1e70},
{0x553c,0x553c,0x1e5d},
{0x553e,0x553e,0xc0c},
{0x553f,0x553f,0x43a9},
{0x5540,0x5540,0x1e71},
{0x5541,0x5541,0xa34},
{0x5543,0x5543,0xa26},
{0x5544,0x5544,0xa23},
{0x5545,0x5545,0x1e68},
{0x5546,0x5546,0xa20},
{0x5547,0x5547,0x40aa},
{0x5548,0x5548,0x1e6e},
{0x5549,0x5549,0x4068},
{0x554a,0x554a,0xa27},
{0x554b,0x554b,0x1e72},
{0x554d,0x554d,0x1e5e},
{0x554e,0x554e,0x1e6c},
{0x554f,0x554f,0xa2a},
{0x5550,0x5550,0x1e5f},
{0x5551,0x5551,0x1e62},
{0x5552,0x5552,0x1e67},
{0x5555,0x5555,0xa2b},
{0x5556,0x5556,0xa29},
{0x5557,0x5557,0xa35},
{0x555c,0x555c,0xa30},
{0x555d,0x555d,0x40a0},
{0x555e,0x555e,0xa24},
{0x555f,0x555f,0xabc},
{0x5561,0x5561,0xa25},
{0x5562,0x5562,0x1e63},
{0x5563,0x5563,0xa32},
{0x5564,0x5564,0xa2d},
{0x5565,0x5565,0x1e6b},
{0x5566,0x5566,0xa22},
{0x5569,0x5569,0x3e9f},
{0x556a,0x556a,0xa21},
{0x556b,0x556b,0x3b2e},
{0x5571,0x5571,0x3bc6},
{0x5572,0x5572,0x3e8b},
{0x5573,0x5573,0x3f24},
{0x5575,0x5576,0x1e5b},
{0x5577,0x5577,0x20c7},
{0x5579,0x5579,0x435d},
{0x557b,0x557b,0xbfb},
{0x557c,0x557c,0xbfe},
{0x557d,0x557d,0x20d2},
{0x557e,0x557e,0xc12},
{0x557f,0x557f,0x20d5},
{0x5580,0x5580,0xbfc},
{0x5581,0x5581,0x20ce},
{0x5582,0x5582,0xc02},
{0x5583,0x5583,0xc08},
{0x5584,0x5584,0xd1b},
{0x5586,0x5586,0x40ae},
{0x5587,0x5587,0xc06},
{0x5588,0x5588,0x20cb},
{0x5589,0x5589,0xc13},
{0x558a,0x558a,0xbff},
{0x558b,0x558b,0xc07},
{0x558c,0x558c,0x20d3},
{0x558d,0x558d,0x2387},
{0x558e,0x558e,0x20d8},
{0x558f,0x558f,0x20cc},
{0x5590,0x5590,0x430e},
{0x5591,0x5591,0x20c3},
{0x5592,0x5592,0x20d0},
{0x5593,0x5593,0x20ca},
{0x5594,0x5594,0xc05},
{0x5595,0x5595,0x20d6},
{0x5598,0x5598,0xc01},
{0x5599,0x5599,0xc15},
{0x559a,0x559a,0xc0e},
{0x559c,0x559c,0xc03},
{0x559d,0x559d,0xc00},
{0x559f,0x559f,0xc0b},
{0x55a1,0x55a1,0x20d7},
{0x55a2,0x55a2,0x20c9},
{0x55a3,0x55a3,0x20cf},
{0x55a4,0x55a4,0x20d1},
{0x55a5,0x55a5,0x20c5},
{0x55a6,0x55a6,0x20d4},
{0x55a7,0x55a7,0xbfd},
{0x55a8,0x55a8,0x20c4},
{0x55a9,0x55a9,0x40af},
{0x55aa,0x55aa,0xc04},
{0x55ab,0x55ab,0xc14},
{0x55ac,0x55ac,0xc10},
{0x55ad,0x55ad,0x20c6},
{0x55ae,0x55ae,0xc0a},
{0x55b0,0x55b0,0x38f5},
{0x55b1,0x55b1,0xc11},
{0x55b2,0x55b2,0xc0d},
{0x55b3,0x55b3,0xc09},
{0x55b4,0x55b4,0x39fe},
{0x55b5,0x55b5,0x20cd},
{0x55ba,0x55ba,0x3e89},
{0x55bb,0x55bb,0xc0f},
{0x55bc,0x55bc,0x3dc3},
{0x55bf,0x55bf,0x2385},
{0x55c0,0x55c0,0x2381},
{0x55c1,0x55c1,0x3e4f},
{0x55c2,0x55c2,0x2390},
{0x55c3,0x55c3,0x237a},
{0x55c4,0x55c4,0x2383},
{0x55c5,0x55c6,0xde0},
{0x55c7,0x55c7,0xdd9},
{0x55c8,0x55c8,0x238c},
{0x55c9,0x55c9,0xde3},
{0x55ca,0x55ca,0x237f},
{0x55cb,0x55cb,0x237e},
{0x55cc,0x55cc,0x237c},
{0x55cd,0x55cd,0x238e},
{0x55ce,0x55ce,0xdd7},
{0x55cf,0x55cf,0x2388},
{0x55d0,0x55d0,0x237d},
{0x55d1,0x55d1,0xdda},
{0x55d2,0x55d2,0x2386},
{0x55d3,0x55d3,0xdd5},
{0x55d4,0x55d4,0x2382},
{0x55d5,0x55d5,0x2389},
{0x55d6,0x55d6,0x238b},
{0x55d7,0x55d7,0x45b3},
{0x55d8,0x55d8,0x43ab},
{0x55d9,0x55d9,0x238f},
{0x55da,0x55da,0xdde},
{0x55db,0x55db,0x237b},
{0x55dc,0x55dc,0xdd8},
{0x55dd,0x55dd,0x2380},
{0x55df,0x55df,0xdd3},
{0x55e1,0x55e1,0xddf},
{0x55e2,0x55e2,0x238a},
{0x55e3,0x55e4,0xddb},
{0x55e5,0x55e5,0xde2},
{0x55e6,0x55e6,0xdd6},
{0x55e7,0x55e7,0x11f},
{0x55e8,0x55e8,0xdd4},
{0x55e9,0x55e9,0x2384},
{0x55ea,0x55ea,0x3e7c},
{0x55ec,0x55ec,0x37d2},
{0x55ef,0x55ef,0xddd},
{0x55f0,0x55f0,0x3e88},
{0x55f1,0x55f1,0x3e83},
{0x55f2,0x55f2,0x238d},
{0x55f5,0x55f5,0x4786},
{0x55f6,0x55f6,0xf9f},
{0x55f7,0x55f7,0xf9a},
{0x55f9,0x55f9,0x2637},
{0x55fa,0x55fa,0x2633},
{0x55fb,0x55fb,0x4626},
{0x55fc,0x55fc,0x262d},
{0x55fd,0x55fd,0xf94},
{0x55fe,0x55fe,0xf90},
{0x55ff,0x55ff,0x2636},
{0x5600,0x5600,0xf91},
{0x5601,0x5601,0x2630},
{0x5602,0x5602,0x2632},
{0x5604,0x5604,0x2635},
{0x5605,0x5605,0x3e82},
{0x5606,0x5606,0xf96},
{0x5608,0x5608,0xf9d},
{0x5609,0x5609,0xf97},
{0x560c,0x560c,0x262b},
{0x560d,0x560e,0xf98},
{0x560f,0x560f,0x262e},
{0x5610,0x5610,0xf9e},
{0x5611,0x5611,0x3f4d},
{0x5612,0x5612,0x262c},
{0x5613,0x5613,0x2631},
{0x5614,0x5614,0xf95},
{0x5615,0x5615,0x262a},
{0x5616,0x5616,0xf9b},
{0x5617,0x5617,0xf93},
{0x561b,0x561b,0xf92},
{0x561c,0x561c,0x262f},
{0x561d,0x561d,0x2634},
{0x561e,0x561e,0x3e68},
{0x561f,0x561f,0xf9c},
{0x5620,0x5620,0x3f7d},
{0x5621,0x5621,0x43ad},
{0x5622,0x5622,0x3e67},
{0x5623,0x5623,0x4707},
{0x5625,0x5625,0x3e78},
{0x5627,0x5627,0x2629},
{0x5629,0x5629,0x1119},
{0x562a,0x562a,0x289d},
{0x562c,0x562c,0x289a},
{0x562d,0x562d,0x3e63},
{0x562e,0x562e,0x1113},
{0x562f,0x5630,0x111f},
{0x5632,0x5632,0x1116},
{0x5633,0x5633,0x2898},
{0x5634,0x5634,0x1118},
{0x5635,0x5635,0x2890},
{0x5636,0x5636,0x111e},
{0x5637,0x5637,0x40b7},
{0x5638,0x5638,0x289c},
{0x5639,0x5639,0x1115},
{0x563a,0x563a,0x289e},
{0x563b,0x563b,0x1114},
{0x563d,0x563d,0x2899},
{0x563e,0x563e,0x289b},
{0x563f,0x563f,0x1117},
{0x5640,0x5640,0x2897},
{0x5641,0x5641,0x2891},
{0x5642,0x5642,0x288e},
{0x5643,0x5643,0x3e7e},
{0x5645,0x5645,0x20c8},
{0x5646,0x5646,0x2894},
{0x5648,0x5648,0x288d},
{0x5649,0x5649,0x2893},
{0x564a,0x564a,0x2892},
{0x564c,0x564c,0x288f},
{0x564d,0x564d,0x40bc},
{0x564e,0x564e,0x111b},
{0x564f,0x564f,0x40bd},
{0x5650,0x5650,0x47cf},
{0x5652,0x5652,0x45c2},
{0x5653,0x5653,0x111a},
{0x5654,0x5654,0x43af},
{0x5657,0x5657,0x111c},
{0x5658,0x5658,0x2895},
{0x5659,0x5659,0x1295},
{0x565a,0x565a,0x2896},
{0x565d,0x565d,0x3ef9},
{0x565e,0x565e,0x2b10},
{0x5660,0x5660,0x2b09},
{0x5661,0x5661,0x3812},
{0x5662,0x5662,0x12a1},
{0x5663,0x5663,0x2b0d},
{0x5664,0x5664,0x1299},
{0x5665,0x5665,0x129d},
{0x5666,0x5666,0x2b0c},
{0x5668,0x5668,0x129c},
{0x5669,0x5669,0x1298},
{0x566a,0x566a,0x129b},
{0x566b,0x566b,0x1296},
{0x566c,0x566c,0x12a0},
{0x566d,0x566d,0x2b0e},
{0x566e,0x566e,0x2b0a},
{0x566f,0x566f,0x129f},
{0x5670,0x5670,0x2b08},
{0x5671,0x5671,0x129e},
{0x5672,0x5672,0x2b0f},
{0x5673,0x5673,0x2b0b},
{0x5674,0x5674,0x111d},
{0x5676,0x5676,0x12a2},
{0x5677,0x5677,0x2b11},
{0x5678,0x5678,0x129a},
{0x5679,0x5679,0x1297},
{0x567a,0x567a,0x3d85},
{0x567b,0x567b,0x3eb7},
{0x567c,0x567c,0x3eed},
{0x567e,0x567e,0x2d47},
{0x567f,0x567f,0x2d49},
{0x5680,0x5680,0x13ba},
{0x5681,0x5681,0x2d4a},
{0x5682,0x5682,0x2d48},
{0x5683,0x5683,0x2d46},
{0x5684,0x5684,0x2d45},
{0x5685,0x5685,0x13bc},
{0x5686,0x5686,0x2d44},
{0x5687,0x5687,0x13bd},
{0x5689,0x5689,0x4628},
{0x568a,0x568a,0x3949},
{0x568b,0x568b,0x3e4c},
{0x568c,0x568d,0x2d42},
{0x568e,0x568e,0x13b9},
{0x568f,0x568f,0x13be},
{0x5690,0x5690,0x13bb},
{0x5692,0x5692,0x39a6},
{0x5693,0x5693,0x2d41},
{0x5695,0x5695,0x14ca},
{0x5697,0x5697,0x2f13},
{0x5698,0x5698,0x2f11},
{0x5699,0x5699,0x2f16},
{0x569a,0x569a,0x2f14},
{0x569c,0x569c,0x2f12},
{0x569d,0x569d,0x2f15},
{0x569e,0x569e,0x39a4},
{0x569f,0x569f,0x3948},
{0x56a1,0x56a1,0x436b},
{0x56a4,0x56a4,0x3cc8},
{0x56a5,0x56a5,0x1576},
{0x56a6,0x56a7,0x3081},
{0x56a8,0x56a8,0x1577},
{0x56aa,0x56aa,0x3083},
{0x56ab,0x56ab,0x307f},
{0x56ac,0x56ac,0x3084},
{0x56ad,0x56ad,0x3080},
{0x56ae,0x56ae,0x14cb},
{0x56af,0x56af,0x45f7},
{0x56b1,0x56b1,0x463f},
{0x56b2,0x56b2,0x31c8},
{0x56b3,0x56b3,0x31ca},
{0x56b4,0x56b4,0x160c},
{0x56b5,0x56b5,0x31c9},
{0x56b6,0x56b6,0x160b},
{0x56b7,0x56b7,0x160a},
{0x56b9,0x56b9,0x486f},
{0x56bc,0x56bc,0x160d},
{0x56bd,0x56be,0x32ca},
{0x56bf,0x56bf,0x3e5d},
{0x56c0,0x56c0,0x166b},
{0x56c1,0x56c1,0x166a},
{0x56c2,0x56c2,0x166c},
{0x56c3,0x56c3,0x32c9},
{0x56c5,0x56c5,0x3379},
{0x56c6,0x56c6,0x3378},
{0x56c8,0x56c8,0x16bd},
{0x56c9,0x56c9,0x16bf},
{0x56ca,0x56ca,0x16be},
{0x56cb,0x56cb,0x337a},
{0x56cc,0x56cc,0x16f6},
{0x56cd,0x56cd,0x3481},
{0x56d1,0x56d1,0x171f},
{0x56d3,0x56d3,0x3480},
{0x56d4,0x56d4,0x34c9},
{0x56d6,0x56d6,0x488a},
{0x56d7,0x56d7,0x1775},
{0x56da,0x56da,0x32d},
{0x56db,0x56db,0x32c},
{0x56dd,0x56dd,0x3ac},
{0x56de,0x56de,0x3ab},
{0x56df,0x56df,0x17d0},
{0x56e0,0x56e0,0x3aa},
{0x56e1,0x56e1,0x17cf},
{0x56e2,0x56e2,0x4548},
{0x56e4,0x56e4,0x463},
{0x56e5,0x56e5,0x1850},
{0x56e7,0x56e7,0x184f},
{0x56ea,0x56ea,0x461},
{0x56eb,0x56eb,0x464},
{0x56ed,0x56ed,0x40c4},
{0x56ee,0x56ee,0x184e},
{0x56ef,0x56ef,0x40c3},
{0x56f0,0x56f0,0x462},
{0x56f1,0x56f1,0x40bf},
{0x56f7,0x56f7,0x1933},
{0x56f9,0x56f9,0x1934},
{0x56fa,0x56fa,0x578},
{0x56fd,0x56fd,0x3d64},
{0x56ff,0x56ff,0x6e6},
{0x5700,0x5700,0x40c2},
{0x5701,0x5702,0x1c5b},
{0x5703,0x5704,0x879},
{0x5707,0x5707,0x1e74},
{0x5708,0x5708,0xa36},
{0x5709,0x5709,0xa38},
{0x570a,0x570a,0x1e73},
{0x570b,0x570b,0xa37},
{0x570c,0x570c,0x20d9},
{0x570d,0x570d,0xc16},
{0x5712,0x5713,0xde4},
{0x5714,0x5714,0x2391},
{0x5715,0x5715,0x3e36},
{0x5716,0x5716,0xfa1},
{0x5718,0x5718,0xfa0},
{0x571a,0x571a,0x289f},
{0x571b,0x571b,0x2b13},
{0x571c,0x571c,0x2b12},
{0x571d,0x571d,0x3a02},
{0x571e,0x571e,0x3505},
{0x571f,0x571f,0x27a},
{0x5720,0x5720,0x1784},
{0x5722,0x5723,0x179a},
{0x5728,0x5728,0x3af},
{0x5729,0x5729,0x3b3},
{0x572a,0x572a,0x17d2},
{0x572c,0x572c,0x3b1},
{0x572d,0x572d,0x3b0},
{0x572e,0x572e,0x17d1},
{0x572f,0x572f,0x3b2},
{0x5730,0x5730,0x3ae},
{0x5732,0x5732,0x3af9},
{0x5733,0x5733,0x3ad},
{0x5734,0x5734,0x17d3},
{0x573b,0x573b,0x46e},
{0x573e,0x573e,0x46b},
{0x573f,0x573f,0x4855},
{0x5740,0x5740,0x467},
{0x5741,0x5741,0x1851},
{0x5742,0x5742,0x40cc},
{0x5743,0x5743,0x40de},
{0x5745,0x5745,0x1852},
{0x5746,0x5746,0x40c8},
{0x5747,0x5747,0x469},
{0x5749,0x5749,0x1854},
{0x574a,0x574a,0x465},
{0x574b,0x574b,0x1855},
{0x574c,0x574c,0x1853},
{0x574d,0x574d,0x468},
{0x574e,0x574e,0x46a},
{0x574f,0x574f,0x46d},
{0x5750,0x5750,0x46c},
{0x5751,0x5751,0x466},
{0x5752,0x5752,0x1856},
{0x5754,0x5754,0x4785},
{0x5757,0x5757,0x47e6},
{0x575b,0x575b,0x3982},
{0x575f,0x575f,0x3fbf},
{0x5761,0x5761,0x57d},
{0x5762,0x5762,0x1941},
{0x5764,0x5764,0x57f},
{0x5766,0x5766,0x57e},
{0x5767,0x5767,0x3f2b},
{0x5768,0x5768,0x1942},
{0x5769,0x5769,0x57c},
{0x576a,0x576a,0x57b},
{0x576b,0x576b,0x1938},
{0x576d,0x576d,0x1937},
{0x576f,0x576f,0x1935},
{0x5770,0x5770,0x193a},
{0x5771,0x5771,0x1939},
{0x5772,0x5772,0x1936},
{0x5773,0x5774,0x193f},
{0x5775,0x5775,0x193d},
{0x5776,0x5776,0x193b},
{0x5777,0x5777,0x57a},
{0x577b,0x577b,0x193e},
{0x577c,0x577c,0x580},
{0x577d,0x577d,0x1943},
{0x577e,0x577e,0x46dc},
{0x577f,0x577f,0x3a07},
{0x5780,0x5780,0x193c},
{0x5782,0x5782,0x6e7},
{0x5783,0x5783,0x579},
{0x5788,0x5788,0x484b},
{0x578a,0x578a,0x3c7b},
{0x578b,0x578b,0x6e8},
{0x578c,0x578c,0x1a90},
{0x578d,0x578d,0x3a06},
{0x578f,0x578f,0x1a96},
{0x5790,0x5790,0x4166},
{0x5793,0x5793,0x6ee},
{0x5794,0x5794,0x1a94},
{0x5795,0x5795,0x1a9a},
{0x5797,0x5797,0x1a91},
{0x5798,0x5798,0x1a95},
{0x5799,0x5799,0x1a97},
{0x579a,0x579a,0x1a99},
{0x579b,0x579b,0x1a93},
{0x579c,0x579c,0x4608},
{0x579d,0x579d,0x1a92},
{0x579e,0x579f,0x1a8d},
{0x57a0,0x57a0,0x6e9},
{0x57a1,0x57a1,0x4864},
{0x57a2,0x57a2,0x6eb},
{0x57a3,0x57a3,0x6ea},
{0x57a4,0x57a4,0x1a8f},
{0x57a5,0x57a5,0x1a98},
{0x57a7,0x57a7,0x4914},
{0x57aa,0x57aa,0x4905},
{0x57ae,0x57ae,0x6ed},
{0x57b4,0x57b4,0x4741},
{0x57b5,0x57b5,0x1a8c},
{0x57b6,0x57b6,0x1c66},
{0x57b8,0x57b8,0x1c65},
{0x57b9,0x57b9,0x1c6a},
{0x57ba,0x57ba,0x1c61},
{0x57bb,0x57bb,0x3c79},
{0x57bc,0x57bc,0x1c64},
{0x57bd,0x57bd,0x1c63},
{0x57be,0x57be,0x372c},
{0x57bf,0x57bf,0x1c67},
{0x57c1,0x57c1,0x1c6b},
{0x57c2,0x57c2,0x87b},
{0x57c3,0x57c3,0x87e},
{0x57c4,0x57c4,0x3b5b},
{0x57c6,0x57c6,0x1c62},
{0x57c7,0x57c7,0x1c68},
{0x57c8,0x57c8,0x3d0b},
{0x57cb,0x57cb,0x87d},
{0x57cc,0x57cc,0x1c5d},
{0x57ce,0x57ce,0x6ec},
{0x57cf,0x57cf,0x1e82},
{0x57d0,0x57d0,0x1c69},
{0x57d2,0x57d2,0x1c60},
{0x57d4,0x57d4,0x87c},
{0x57d5,0x57d5,0x1c5f},
{0x57d7,0x57d7,0x3c7d},
{0x57dc,0x57dc,0x1e79},
{0x57dd,0x57dd,0x3a05},
{0x57de,0x57de,0x3f01},
{0x57df,0x57df,0xa39},
{0x57e0,0x57e0,0xa3d},
{0x57e1,0x57e1,0x1e89},
{0x57e2,0x57e2,0x1e77},
{0x57e3,0x57e3,0x1e85},
{0x57e4,0x57e4,0xa3e},
{0x57e5,0x57e5,0x1e87},
{0x57e6,0x57e6,0x40cf},
{0x57e7,0x57e7,0x1e8d},
{0x57e9,0x57e9,0x1e91},
{0x57ec,0x57ec,0x1e88},
{0x57ed,0x57ed,0x1e7c},
{0x57ee,0x57ee,0x1e84},
{0x57ef,0x57ef,0x4754},
{0x57f0,0x57f0,0x1e92},
{0x57f1,0x57f1,0x1e90},
{0x57f2,0x57f2,0x1e86},
{0x57f3,0x57f3,0x1e81},
{0x57f4,0x57f4,0x1e7a},
{0x57f5,0x57f5,0x20e1},
{0x57f6,0x57f6,0x1e78},
{0x57f7,0x57f7,0xa42},
{0x57f8,0x57f8,0x1e7f},
{0x57f9,0x57f9,0xa43},
{0x57fa,0x57fa,0xa3f},
{0x57fb,0x57fb,0x1e75},
{0x57fc,0x57fc,0x1e8b},
{0x57fd,0x57fd,0x1e7d},
{0x57fe,0x57fe,0x408f},
{0x5800,0x5800,0x1e7b},
{0x5801,0x5801,0x1e8e},
{0x5802,0x5802,0xa40},
{0x5803,0x5803,0x40d1},
{0x5804,0x5804,0x1e94},
{0x5805,0x5805,0xa3a},
{0x5806,0x5806,0xa3c},
{0x5807,0x5807,0x1e83},
{0x5808,0x5808,0x1e7e},
{0x5809,0x5809,0x87f},
{0x580a,0x580a,0xa3b},
{0x580b,0x580b,0x1e80},
{0x580c,0x580c,0x1e8f},
{0x580d,0x580d,0x1e93},
{0x580e,0x580e,0x1e8a},
{0x5810,0x5810,0x1e8c},
{0x5812,0x5812,0x3d0a},
{0x5814,0x5814,0x1e76},
{0x5819,0x5819,0x20dc},
{0x581b,0x581b,0x20e5},
{0x581c,0x581c,0x20e4},
{0x581d,0x581d,0xc1e},
{0x581e,0x581e,0x20dd},
{0x5820,0x5820,0xc1f},
{0x5821,0x5821,0xc1d},
{0x5822,0x5822,0x3c28},
{0x5823,0x5823,0x20df},
{0x5824,0x5824,0xc1a},
{0x5825,0x5825,0x20e3},
{0x5826,0x5826,0x40d4},
{0x5827,0x5827,0x20de},
{0x5828,0x5828,0x20e0},
{0x5829,0x5829,0x20da},
{0x582a,0x582a,0xc18},
{0x582c,0x582c,0x20ed},
{0x582d,0x582d,0x20ec},
{0x582e,0x582e,0x20e9},
{0x582f,0x582f,0xc17},
{0x5830,0x5831,0xc1b},
{0x5832,0x5832,0x1c5e},
{0x5833,0x5833,0x20e6},
{0x5834,0x5834,0xc19},
{0x5835,0x5835,0xa41},
{0x5836,0x5836,0x20e8},
{0x5837,0x5837,0x20db},
{0x5838,0x5838,0x20eb},
{0x5839,0x5839,0x20ea},
{0x583a,0x583a,0x3d72},
{0x583b,0x583b,0x20ee},
{0x583d,0x583d,0x239f},
{0x583f,0x583f,0x20e7},
{0x5840,0x5840,0x3d82},
{0x5844,0x5844,0x47bb},
{0x5847,0x5847,0x3ac2},
{0x5848,0x5848,0x20e2},
{0x5849,0x5849,0x2397},
{0x584a,0x584a,0xdef},
{0x584b,0x584b,0xdf2},
{0x584c,0x584c,0xded},
{0x584d,0x584d,0x2396},
{0x584e,0x584e,0x239a},
{0x584f,0x584f,0x2395},
{0x5851,0x5851,0xde7},
{0x5852,0x5852,0xdf1},
{0x5853,0x5853,0x2392},
{0x5854,0x5854,0xdeb},
{0x5855,0x5855,0x2399},
{0x5857,0x5857,0xde9},
{0x5858,0x5858,0xde8},
{0x5859,0x5859,0x239c},
{0x585a,0x585a,0xdea},
{0x585b,0x585b,0x239e},
{0x585c,0x585c,0x4949},
{0x585d,0x585d,0x239b},
{0x585e,0x585e,0xde6},
{0x585f,0x585f,0x43df},
{0x5862,0x5862,0xdf0},
{0x5863,0x5863,0x23a0},
{0x5864,0x5864,0x2394},
{0x5865,0x5865,0x239d},
{0x5868,0x5868,0x2393},
{0x5869,0x5869,0x3d65},
{0x586b,0x586b,0xdec},
{0x586c,0x586c,0x399a},
{0x586d,0x586d,0xdee},
{0x586f,0x586f,0x2398},
{0x5871,0x5871,0x23a1},
{0x5872,0x5872,0x3c26},
{0x5873,0x5873,0x4355},
{0x5874,0x5874,0x263f},
{0x5875,0x5875,0xfa2},
{0x5876,0x5876,0x2645},
{0x5879,0x5879,0xfa7},
{0x587a,0x587a,0x2641},
{0x587b,0x587b,0x2648},
{0x587c,0x587c,0x2639},
{0x587d,0x587d,0xfa9},
{0x587e,0x587e,0xfa3},
{0x587f,0x587f,0x263e},
{0x5880,0x5880,0x1121},
{0x5881,0x5881,0x263d},
{0x5882,0x5882,0x2646},
{0x5883,0x5883,0xfa4},
{0x5885,0x5885,0xfa8},
{0x5886,0x5886,0x263c},
{0x5887,0x5887,0x2642},
{0x5888,0x5888,0x2647},
{0x5889,0x5889,0x2638},
{0x588a,0x588a,0xfa6},
{0x588b,0x588b,0x2640},
{0x588e,0x588e,0x2644},
{0x588f,0x588f,0x264a},
{0x5890,0x5890,0x263a},
{0x5891,0x5891,0x2643},
{0x5893,0x5893,0xfa5},
{0x5894,0x5894,0x2649},
{0x5898,0x5898,0x263b},
{0x5899,0x5899,0x4618},
{0x589a,0x589a,0x4903},
{0x589c,0x589c,0x1125},
{0x589d,0x589d,0x28a1},
{0x589e,0x589e,0x1123},
{0x589f,0x589f,0x1122},
{0x58a0,0x58a0,0x28a3},
{0x58a1,0x58a1,0x28a8},
{0x58a3,0x58a3,0x28a4},
{0x58a5,0x58a5,0x28a7},
{0x58a6,0x58a6,0x1128},
{0x58a7,0x58a7,0x3eeb},
{0x58a8,0x58a8,0x1288},
{0x58a9,0x58a9,0x1127},
{0x58aa,0x58aa,0x40d7},
{0x58ab,0x58ab,0x28a0},
{0x58ac,0x58ac,0x28a6},
{0x58ae,0x58ae,0x1126},
{0x58af,0x58af,0x28a5},
{0x58b0,0x58b0,0x37a4},
{0x58b1,0x58b1,0x28a2},
{0x58b3,0x58b3,0x1124},
{0x58b5,0x58b5,0x4840},
{0x58b6,0x58b6,0x3dfd},
{0x58ba,0x58ba,0x2b18},
{0x58bb,0x58bb,0x36eb},
{0x58bc,0x58bc,0x2b1a},
{0x58bd,0x58bd,0x2b15},
{0x58be,0x58be,0x12a4},
{0x58bf,0x58bf,0x2b17},
{0x58c1,0x58c1,0x12a3},
{0x58c2,0x58c2,0x2b19},
{0x58c5,0x58c5,0x12a6},
{0x58c6,0x58c6,0x2b1b},
{0x58c7,0x58c7,0x12a5},
{0x58c8,0x58c8,0x2b14},
{0x58c9,0x58c9,0x2b16},
{0x58cb,0x58cb,0x3a09},
{0x58ce,0x58ce,0x13c2},
{0x58cf,0x58cf,0x2d4d},
{0x58d1,0x58d1,0x13c1},
{0x58d2,0x58d2,0x2d4e},
{0x58d3,0x58d3,0x13c0},
{0x58d4,0x58d4,0x2d4c},
{0x58d5,0x58d5,0x13bf},
{0x58d6,0x58d6,0x2d4b},
{0x58d8,0x58d8,0x14cd},
{0x58d9,0x58d9,0x14cc},
{0x58da,0x58da,0x3085},
{0x58db,0x58db,0x3087},
{0x58dc,0x58dc,0x40da},
{0x58dd,0x58dd,0x3086},
{0x58de,0x58df,0x1578},
{0x58e0,0x58e0,0x40d9},
{0x58e2,0x58e2,0x157a},
{0x58e3,0x58e3,0x31cb},
{0x58e4,0x58e4,0x160e},
{0x58e7,0x58e7,0x3411},
{0x58e8,0x58e8,0x3410},
{0x58e9,0x58e9,0x1720},
{0x58eb,0x58eb,0x27b},
{0x58ec,0x58ec,0x2c1},
{0x58ef,0x58ef,0x46f},
{0x58f0,0x58f0,0x4549},
{0x58f2,0x58f2,0x3d68},
{0x58f3,0x58f3,0x3c7a},
{0x58f4,0x58f4,0x1a9b},
{0x58f9,0x58fa,0xc20},
{0x58fb,0x58fb,0x40dc},
{0x58fc,0x58fc,0x23a2},
{0x58fd,0x58fd,0xfaa},
{0x58fe,0x58fe,0x264b},
{0x58ff,0x58ff,0x28a9},
{0x5903,0x5903,0x1785},
{0x5904,0x5904,0x454a},
{0x5905,0x5905,0x4599},
{0x5906,0x5906,0x1857},
{0x5907,0x5907,0x454b},
{0x590a,0x590a,0x224},
{0x590c,0x590c,0x1944},
{0x590d,0x590d,0x1a9c},
{0x590e,0x590e,0x1c6c},
{0x590f,0x590f,0x880},
{0x5911,0x5911,0x4274},
{0x5912,0x5912,0x3088},
{0x5914,0x5914,0x166d},
{0x5915,0x5915,0x27c},
{0x5916,0x5916,0x32e},
{0x5917,0x5917,0x179c},
{0x5919,0x591a,0x3b4},
{0x591c,0x591c,0x581},
{0x591f,0x591f,0x40e3},
{0x5920,0x5920,0xa44},
{0x5922,0x5922,0xfac},
{0x5924,0x5924,0xfad},
{0x5925,0x5925,0xfab},
{0x5927,0x5927,0x27d},
{0x5929,0x5929,0x2c2},
{0x592a,0x592a,0x2c4},
{0x592b,0x592b,0x2c3},
{0x592c,0x592c,0x1786},
{0x592d,0x592d,0x2c5},
{0x592e,0x592e,0x32f},
{0x592f,0x592f,0x179d},
{0x5931,0x5931,0x330},
{0x5932,0x5932,0x454c},
{0x5934,0x5934,0x454d},
{0x5937,0x5938,0x3b6},
{0x593c,0x593c,0x17d4},
{0x593e,0x593e,0x470},
{0x5940,0x5940,0x1858},
{0x5944,0x5944,0x585},
{0x5945,0x5945,0x1945},
{0x5947,0x5948,0x583},
{0x5949,0x5949,0x582},
{0x594a,0x594a,0x1c6d},
{0x594e,0x594e,0x6f2},
{0x594f,0x594f,0x6f1},
{0x5950,0x5950,0x6f3},
{0x5951,0x5951,0x6f0},
{0x5953,0x5953,0x1a9d},
{0x5954,0x5954,0x586},
{0x5955,0x5955,0x6ef},
{0x5957,0x5958,0x881},
{0x595a,0x595a,0x883},
{0x595c,0x595c,0x1e95},
{0x5960,0x5960,0xc22},
{0x5961,0x5961,0x20ef},
{0x5962,0x5962,0xa45},
{0x5965,0x5965,0x4852},
{0x5967,0x5967,0xdf3},
{0x5969,0x5969,0xfaf},
{0x596a,0x596a,0xfae},
{0x596b,0x596b,0x264c},
{0x596d,0x596d,0x1129},
{0x596e,0x596e,0x12a7},
{0x5970,0x5970,0x2f17},
{0x5971,0x5971,0x337b},
{0x5972,0x5972,0x3412},
{0x5973,0x5973,0x27e},
{0x5974,0x5974,0x331},
{0x5975,0x5975,0x3e6a},
{0x5976,0x5976,0x332},
{0x5977,0x5977,0x17da},
{0x5978,0x5978,0x3b9},
{0x5979,0x5979,0x3bc},
{0x597b,0x597b,0x17d8},
{0x597c,0x597c,0x17d6},
{0x597d,0x597d,0x3bb},
{0x597e,0x597e,0x17d9},
{0x597f,0x597f,0x17db},
{0x5980,0x5980,0x17d5},
{0x5981,0x5981,0x3be},
{0x5982,0x5982,0x3bd},
{0x5983,0x5983,0x3ba},
{0x5984,0x5984,0x3b8},
{0x5985,0x5985,0x17d7},
{0x5989,0x5989,0x3d30},
{0x598a,0x598a,0x47b},
{0x598d,0x598d,0x478},
{0x598e,0x598e,0x185d},
{0x598f,0x598f,0x1860},
{0x5990,0x5990,0x185f},
{0x5992,0x5992,0x472},
{0x5993,0x5993,0x47a},
{0x5994,0x5994,0x3c99},
{0x5996,0x5996,0x477},
{0x5997,0x5997,0x185c},
{0x5998,0x5998,0x185a},
{0x5999,0x5999,0x476},
{0x599a,0x599a,0x3bb0},
{0x599d,0x599d,0x471},
{0x599e,0x599e,0x474},
{0x599f,0x599f,0x3daf},
{0x59a0,0x59a0,0x185b},
{0x59a1,0x59a1,0x1862},
{0x59a2,0x59a2,0x185e},
{0x59a3,0x59a3,0x475},
{0x59a4,0x59a4,0x479},
{0x59a5,0x59a5,0x47c},
{0x59a6,0x59a6,0x1859},
{0x59a7,0x59a7,0x1861},
{0x59a8,0x59a8,0x473},
{0x59ac,0x59ac,0x3d81},
{0x59ae,0x59ae,0x58b},
{0x59af,0x59af,0x593},
{0x59b0,0x59b0,0x3cd8},
{0x59b1,0x59b1,0x1951},
{0x59b2,0x59b2,0x194a},
{0x59b3,0x59b3,0x594},
{0x59b4,0x59b4,0x1955},
{0x59b5,0x59b5,0x1946},
{0x59b6,0x59b6,0x194d},
{0x59b7,0x59b7,0x3f2d},
{0x59b8,0x59b8,0x3a10},
{0x59b9,0x59b9,0x58a},
{0x59ba,0x59ba,0x1947},
{0x59bb,0x59bb,0x588},
{0x59bc,0x59bc,0x194e},
{0x59bd,0x59bd,0x1952},
{0x59be,0x59be,0x587},
{0x59c0,0x59c0,0x1953},
{0x59c1,0x59c1,0x194c},
{0x59c3,0x59c3,0x194f},
{0x59c4,0x59c4,0x3d04},
{0x59c5,0x59c5,0x596},
{0x59c6,0x59c6,0x58d},
{0x59c7,0x59c7,0x1956},
{0x59c8,0x59c8,0x1954},
{0x59c9,0x59c9,0x40ec},
{0x59ca,0x59ca,0x592},
{0x59cb,0x59cb,0x590},
{0x59cc,0x59cc,0x194b},
{0x59cd,0x59cd,0x58f},
{0x59ce,0x59ce,0x1949},
{0x59cf,0x59cf,0x1948},
{0x59d0,0x59d0,0x58e},
{0x59d1,0x59d1,0x58c},
{0x59d2,0x59d2,0x595},
{0x59d3,0x59d3,0x591},
{0x59d4,0x59d4,0x589},
{0x59d6,0x59d6,0x1950},
{0x59d8,0x59d8,0x6f5},
{0x59d9,0x59d9,0x40f1},
{0x59da,0x59da,0x6fc},
{0x59db,0x59db,0x1aab},
{0x59dc,0x59dc,0x6f4},
{0x59dd,0x59dd,0x1aa3},
{0x59de,0x59de,0x1a9f},
{0x59e0,0x59e0,0x1aaf},
{0x59e1,0x59e1,0x1a9e},
{0x59e3,0x59e3,0x6f7},
{0x59e4,0x59e4,0x1aa8},
{0x59e5,0x59e5,0x6fa},
{0x59e6,0x59e6,0x6fd},
{0x59e8,0x59e8,0x6f8},
{0x59e9,0x59e9,0x1aac},
{0x59ea,0x59ea,0x6fb},
{0x59eb,0x59eb,0x3d59},
{0x59ec,0x59ec,0x88a},
{0x59ed,0x59ed,0x1ab2},
{0x59ee,0x59ee,0x1aa0},
{0x59ef,0x59ef,0x3d38},
{0x59f0,0x59f0,0x3bb2},
{0x59f1,0x59f1,0x1aa2},
{0x59f2,0x59f2,0x1aa9},
{0x59f3,0x59f3,0x1aad},
{0x59f4,0x59f4,0x1ab1},
{0x59f5,0x59f5,0x1aae},
{0x59f6,0x59f6,0x1aa7},
{0x59f7,0x59f7,0x1aaa},
{0x59f8,0x59f8,0x3e4a},
{0x59f9,0x59f9,0x40f8},
{0x59fa,0x59fa,0x1aa4},
{0x59fb,0x59fb,0x6ff},
{0x59fc,0x59fc,0x1aa6},
{0x59fd,0x59fd,0x1aa5},
{0x59fe,0x59fe,0x1ab0},
{0x59ff,0x59ff,0x6f6},
{0x5a00,0x5a00,0x1aa1},
{0x5a01,0x5a01,0x6fe},
{0x5a02,0x5a02,0x3b8d},
{0x5a03,0x5a03,0x6f9},
{0x5a09,0x5a09,0x890},
{0x5a0a,0x5a0a,0x1c75},
{0x5a0b,0x5a0b,0x3c89},
{0x5a0c,0x5a0c,0x88f},
{0x5a0d,0x5a0d,0x3b38},
{0x5a0f,0x5a0f,0x1c73},
{0x5a11,0x5a11,0x884},
{0x5a12,0x5a12,0x3a13},
{0x5a13,0x5a13,0x889},
{0x5a15,0x5a15,0x1c72},
{0x5a16,0x5a16,0x1c6f},
{0x5a17,0x5a17,0x1c74},
{0x5a18,0x5a18,0x885},
{0x5a19,0x5a19,0x1c6e},
{0x5a1b,0x5a1b,0x888},
{0x5a1c,0x5a1c,0x886},
{0x5a1e,0x5a1e,0x1c76},
{0x5a1f,0x5a1f,0x887},
{0x5a20,0x5a20,0x88b},
{0x5a21,0x5a21,0x3a1b},
{0x5a23,0x5a23,0x88c},
{0x5a24,0x5a24,0x40e8},
{0x5a25,0x5a25,0x88e},
{0x5a27,0x5a27,0x3de1},
{0x5a29,0x5a29,0x88d},
{0x5a2a,0x5a2a,0x3b3b},
{0x5a2b,0x5a2b,0x3d40},
{0x5a2c,0x5a2c,0x3a0f},
{0x5a2d,0x5a2e,0x1c70},
{0x5a33,0x5a33,0x1c77},
{0x5a35,0x5a35,0x1e9c},
{0x5a36,0x5a36,0xa46},
{0x5a37,0x5a37,0x20fd},
{0x5a38,0x5a38,0x1e9b},
{0x5a39,0x5a39,0x1eae},
{0x5a3c,0x5a3c,0xa4c},
{0x5a3d,0x5a3d,0x3ac0},
{0x5a3e,0x5a3e,0x1eac},
{0x5a40,0x5a40,0xa4b},
{0x5a41,0x5a41,0xa47},
{0x5a42,0x5a42,0x1eb5},
{0x5a43,0x5a43,0x1ea5},
{0x5a44,0x5a44,0x1ea8},
{0x5a45,0x5a45,0x3917},
{0x5a46,0x5a46,0xa4f},
{0x5a47,0x5a47,0x1eb2},
{0x5a48,0x5a48,0x1eaa},
{0x5a49,0x5a49,0xa48},
{0x5a4a,0x5a4a,0xa50},
{0x5a4c,0x5a4c,0x1eaf},
{0x5a4d,0x5a4d,0x1ead},
{0x5a50,0x5a50,0x1e9e},
{0x5a51,0x5a51,0x1eb3},
{0x5a52,0x5a52,0x1ea7},
{0x5a53,0x5a53,0x1ea2},
{0x5a54,0x5a54,0x4603},
{0x5a55,0x5a55,0x1e98},
{0x5a56,0x5a56,0x1eb4},
{0x5a57,0x5a57,0x1ea4},
{0x5a58,0x5a58,0x1e97},
{0x5a59,0x5a59,0x3b34},
{0x5a5a,0x5a5a,0xa4e},
{0x5a5b,0x5a5b,0x1ea9},
{0x5a5c,0x5a5c,0x1eb6},
{0x5a5d,0x5a5d,0x1ea6},
{0x5a5e,0x5a5e,0x1e9a},
{0x5a5f,0x5a5f,0x1e9f},
{0x5a60,0x5a60,0x1e96},
{0x5a61,0x5a61,0x3d33},
{0x5a62,0x5a62,0xa4d},
{0x5a63,0x5a63,0x40fb},
{0x5a64,0x5a64,0x1ea3},
{0x5a65,0x5a65,0x1ea0},
{0x5a66,0x5a66,0xa49},
{0x5a67,0x5a67,0x1e99},
{0x5a68,0x5a68,0x39b7},
{0x5a69,0x5a69,0x1eb1},
{0x5a6a,0x5a6a,0xa4a},
{0x5a6b,0x5a6b,0x3a42},
{0x5a6c,0x5a6c,0x1ea1},
{0x5a6d,0x5a6d,0x1e9d},
{0x5a6e,0x5a6e,0x3d3f},
{0x5a70,0x5a70,0x1eb0},
{0x5a71,0x5a71,0x3d34},
{0x5a77,0x5a77,0xc23},
{0x5a78,0x5a78,0x20f6},
{0x5a79,0x5a79,0x3ce1},
{0x5a7a,0x5a7a,0x20f3},
{0x5a7b,0x5a7b,0x2104},
{0x5a7c,0x5a7c,0x20f8},
{0x5a7d,0x5a7d,0x2105},
{0x5a7e,0x5a7e,0x3a11},
{0x5a7f,0x5a7f,0xc25},
{0x5a81,0x5a81,0x3a1d},
{0x5a82,0x5a82,0x3d31},
{0x5a83,0x5a83,0x2101},
{0x5a84,0x5a84,0x20fe},
{0x5a86,0x5a86,0x3b81},
{0x5a88,0x5a88,0x4263},
{0x5a8a,0x5a8a,0x20ff},
{0x5a8b,0x5a8b,0x2102},
{0x5a8c,0x5a8c,0x2106},
{0x5a8e,0x5a8e,0x1eab},
{0x5a8f,0x5a8f,0x2108},
{0x5a90,0x5a90,0x23b6},
{0x5a91,0x5a91,0x4235},
{0x5a92,0x5a92,0xc26},
{0x5a93,0x5a93,0x2109},
{0x5a94,0x5a94,0x20f1},
{0x5a95,0x5a95,0x20fb},
{0x5a96,0x5a96,0x4100},
{0x5a97,0x5a97,0x2100},
{0x5a99,0x5a99,0x3a0a},
{0x5a9a,0x5a9a,0xc24},
{0x5a9b,0x5a9b,0xc27},
{0x5a9c,0x5a9c,0x2107},
{0x5a9d,0x5a9d,0x210a},
{0x5a9e,0x5a9e,0x20f5},
{0x5a9f,0x5a9f,0x20f2},
{0x5aa0,0x5aa0,0x4172},
{0x5aa1,0x5aa1,0x3cdc},
{0x5aa2,0x5aa2,0x20f4},
{0x5aa5,0x5aa5,0x20f9},
{0x5aa6,0x5aa6,0x20f7},
{0x5aa7,0x5aa7,0xc28},
{0x5aa9,0x5aa9,0x2103},
{0x5aab,0x5aab,0x40fa},
{0x5aac,0x5aac,0x20fa},
{0x5aae,0x5aae,0x20fc},
{0x5aaf,0x5aaf,0x20f0},
{0x5ab0,0x5ab0,0x23aa},
{0x5ab1,0x5ab1,0x23a8},
{0x5ab2,0x5ab2,0xdfc},
{0x5ab3,0x5ab3,0xdfa},
{0x5ab4,0x5ab4,0x23b2},
{0x5ab5,0x5ab5,0x23a9},
{0x5ab6,0x5ab6,0x23b3},
{0x5ab7,0x5ab7,0x23af},
{0x5ab8,0x5ab8,0x23a7},
{0x5ab9,0x5ab9,0x23b5},
{0x5aba,0x5aba,0x23a6},
{0x5abb,0x5abb,0x23ad},
{0x5abc,0x5abc,0xdf9},
{0x5abd,0x5abd,0xdf8},
{0x5abe,0x5abe,0xdf7},
{0x5abf,0x5abf,0x23ab},
{0x5ac0,0x5ac0,0x23b0},
{0x5ac1,0x5ac1,0xdf4},
{0x5ac2,0x5ac2,0xdfb},
{0x5ac3,0x5ac3,0x3896},
{0x5ac4,0x5ac4,0x23a4},
{0x5ac6,0x5ac6,0x23ae},
{0x5ac7,0x5ac7,0x23a3},
{0x5ac8,0x5ac8,0x23ac},
{0x5ac9,0x5ac9,0xdf5},
{0x5aca,0x5aca,0x23b1},
{0x5acb,0x5acb,0x23a5},
{0x5acc,0x5acc,0xdf6},
{0x5acd,0x5acd,0x23b4},
{0x5ace,0x5ace,0x3c88},
{0x5ad3,0x5ad3,0x4102},
{0x5ad5,0x5ad5,0x2650},
{0x5ad6,0x5ad6,0xfb4},
{0x5ad7,0x5ad7,0xfb3},
{0x5ad8,0x5ad8,0xfb5},
{0x5ad9,0x5ad9,0x265c},
{0x5ada,0x5ada,0x2652},
{0x5adb,0x5adb,0x2658},
{0x5adc,0x5adc,0x264d},
{0x5add,0x5add,0x265b},
{0x5ade,0x5ade,0x265a},
{0x5adf,0x5adf,0x265e},
{0x5ae0,0x5ae0,0x2657},
{0x5ae1,0x5ae1,0xfb0},
{0x5ae2,0x5ae2,0x2656},
{0x5ae3,0x5ae3,0xfb6},
{0x5ae4,0x5ae4,0x3b86},
{0x5ae5,0x5ae5,0x264f},
{0x5ae6,0x5ae6,0xfb1},
{0x5ae8,0x5ae8,0x265d},
{0x5ae9,0x5ae9,0xfb2},
{0x5aea,0x5aea,0x2651},
{0x5aeb,0x5aeb,0x2654},
{0x5aec,0x5aec,0x2659},
{0x5aed,0x5aed,0x2653},
{0x5aee,0x5aee,0x264e},
{0x5af0,0x5af0,0x3ee7},
{0x5af2,0x5af2,0x37f5},
{0x5af3,0x5af3,0x2655},
{0x5af4,0x5af4,0x28ab},
{0x5af5,0x5af5,0x112d},
{0x5af6,0x5af6,0x28ae},
{0x5af7,0x5af7,0x28ad},
{0x5af8,0x5af8,0x28b0},
{0x5af9,0x5af9,0x28b2},
{0x5afa,0x5afa,0x36ee},
{0x5afb,0x5afb,0x112b},
{0x5afd,0x5afd,0x28ac},
{0x5afe,0x5afe,0x3c1d},
{0x5aff,0x5aff,0x28aa},
{0x5b01,0x5b01,0x28b3},
{0x5b02,0x5b02,0x28b1},
{0x5b03,0x5b03,0x28af},
{0x5b05,0x5b05,0x28b5},
{0x5b07,0x5b07,0x28b4},
{0x5b08,0x5b08,0x112f},
{0x5b09,0x5b09,0x112a},
{0x5b0b,0x5b0b,0x112c},
{0x5b0c,0x5b0c,0x112e},
{0x5b0d,0x5b0d,0x48ff},
{0x5b0f,0x5b0f,0x28b6},
{0x5b10,0x5b10,0x2b22},
{0x5b11,0x5b11,0x3bf6},
{0x5b13,0x5b13,0x2b21},
{0x5b14,0x5b14,0x2b20},
{0x5b16,0x5b16,0x2b23},
{0x5b17,0x5b17,0x2b1c},
{0x5b19,0x5b19,0x2b1d},
{0x5b1a,0x5b1a,0x2b25},
{0x5b1b,0x5b1b,0x2b1e},
{0x5b1d,0x5b1d,0x12a8},
{0x5b1e,0x5b1e,0x2b27},
{0x5b1f,0x5b1f,0x4941},
{0x5b20,0x5b20,0x2b26},
{0x5b21,0x5b21,0x2b1f},
{0x5b23,0x5b23,0x2d52},
{0x5b24,0x5b24,0x13c5},
{0x5b25,0x5b25,0x2d50},
{0x5b26,0x5b26,0x2d55},
{0x5b27,0x5b27,0x2d54},
{0x5b28,0x5b28,0x2b24},
{0x5b2a,0x5b2a,0x13c4},
{0x5b2b,0x5b2b,0x3b84},
{0x5b2c,0x5b2c,0x2d53},
{0x5b2d,0x5b2d,0x2d4f},
{0x5b2e,0x5b2e,0x2d57},
{0x5b2f,0x5b2f,0x2d56},
{0x5b30,0x5b30,0x13c3},
{0x5b32,0x5b32,0x2d51},
{0x5b34,0x5b34,0x12a9},
{0x5b38,0x5b38,0x14ce},
{0x5b3c,0x5b3c,0x2f18},
{0x5b3d,0x5b3f,0x3089},
{0x5b40,0x5b40,0x160f},
{0x5b41,0x5b41,0x38c8},
{0x5b43,0x5b43,0x1610},
{0x5b44,0x5b44,0x3a44},
{0x5b45,0x5b45,0x31cc},
{0x5b46,0x5b46,0x42b2},
{0x5b47,0x5b47,0x32cd},
{0x5b48,0x5b48,0x32cc},
{0x5b4a,0x5b4a,0x38cd},
{0x5b4b,0x5b4c,0x337c},
{0x5b4d,0x5b4d,0x3413},
{0x5b4e,0x5b4e,0x3482},
{0x5b4f,0x5b4f,0x3a31},
{0x5b50,0x5b51,0x27f},
{0x5b53,0x5b53,0x281},
{0x5b54,0x5b54,0x2c6},
{0x5b55,0x5b55,0x333},
{0x5b56,0x5b56,0x17dc},
{0x5b57,0x5b58,0x3bf},
{0x5b5a,0x5b5b,0x47f},
{0x5b5c,0x5b5c,0x47e},
{0x5b5d,0x5b5d,0x47d},
{0x5b5f,0x5b5f,0x597},
{0x5b62,0x5b62,0x1957},
{0x5b63,0x5b63,0x599},
{0x5b64,0x5b64,0x598},
{0x5b65,0x5b65,0x1958},
{0x5b66,0x5b66,0x454e},
{0x5b68,0x5b68,0x461d},
{0x5b69,0x5b69,0x700},
{0x5b6b,0x5b6b,0x891},
{0x5b6c,0x5b6c,0x1c78},
{0x5b6d,0x5b6d,0x3e5f},
{0x5b6e,0x5b6e,0x1eb8},
{0x5b70,0x5b70,0xa51},
{0x5b71,0x5b71,0xc2a},
{0x5b72,0x5b72,0x1eb7},
{0x5b73,0x5b73,0xc29},
{0x5b74,0x5b74,0x3732},
{0x5b75,0x5b75,0xfb7},
{0x5b76,0x5b76,0x410a},
{0x5b77,0x5b77,0x265f},
{0x5b78,0x5b78,0x12aa},
{0x5b7a,0x5b7a,0x13c6},
{0x5b7b,0x5b7b,0x2d58},
{0x5b7c,0x5b7c,0x410c},
{0x5b7d,0x5b7d,0x1611},
{0x5b7f,0x5b7f,0x16c0},
{0x5b80,0x5b80,0x225},
{0x5b81,0x5b81,0x179e},
{0x5b82,0x5b82,0x4044},
{0x5b83,0x5b83,0x334},
{0x5b84,0x5b84,0x179f},
{0x5b85,0x5b85,0x3c3},
{0x5b87,0x5b88,0x3c1},
{0x5b89,0x5b89,0x3c4},
{0x5b8b,0x5b8b,0x482},
{0x5b8c,0x5b8c,0x481},
{0x5b8e,0x5b8e,0x1863},
{0x5b8f,0x5b8f,0x483},
{0x5b90,0x5b90,0x48e9},
{0x5b92,0x5b92,0x1864},
{0x5b93,0x5b93,0x1959},
{0x5b95,0x5b95,0x195a},
{0x5b97,0x5b97,0x59a},
{0x5b98,0x5b98,0x59c},
{0x5b99,0x5b99,0x59e},
{0x5b9a,0x5b9a,0x59b},
{0x5b9b,0x5b9b,0x59f},
{0x5b9c,0x5b9c,0x59d},
{0x5b9d,0x5b9d,0x4116},
{0x5b9e,0x5b9f,0x454f},
{0x5ba2,0x5ba2,0x704},
{0x5ba3,0x5ba3,0x701},
{0x5ba4,0x5ba4,0x703},
{0x5ba5,0x5ba5,0x705},
{0x5ba6,0x5ba6,0x702},
{0x5ba7,0x5ba7,0x1c79},
{0x5ba8,0x5ba8,0x1ab3},
{0x5baa,0x5baa,0x417b},
{0x5bac,0x5bac,0x1c7b},
{0x5bad,0x5bad,0x1c7a},
{0x5bae,0x5bae,0x897},
{0x5bb0,0x5bb0,0x893},
{0x5bb3,0x5bb3,0x894},
{0x5bb4,0x5bb4,0x896},
{0x5bb5,0x5bb5,0x898},
{0x5bb6,0x5bb6,0x895},
{0x5bb8,0x5bb8,0x89a},
{0x5bb9,0x5bb9,0x899},
{0x5bbf,0x5bbf,0xa56},
{0x5bc0,0x5bc0,0x1eba},
{0x5bc1,0x5bc1,0x1eb9},
{0x5bc2,0x5bc2,0xa55},
{0x5bc3,0x5bc3,0x3f25},
{0x5bc4,0x5bc4,0xa54},
{0x5bc5,0x5bc5,0xa53},
{0x5bc6,0x5bc6,0xa57},
{0x5bc7,0x5bc7,0xa52},
{0x5bca,0x5bca,0x2110},
{0x5bcb,0x5bcb,0x210d},
{0x5bcc,0x5bcc,0xc2c},
{0x5bcd,0x5bcd,0x210c},
{0x5bce,0x5bce,0x2111},
{0x5bd0,0x5bd0,0xc2e},
{0x5bd1,0x5bd1,0x210f},
{0x5bd2,0x5bd2,0xc2b},
{0x5bd3,0x5bd3,0xc2d},
{0x5bd4,0x5bd4,0x210e},
{0x5bd5,0x5bd5,0x4111},
{0x5bd6,0x5bd6,0x23b7},
{0x5bd7,0x5bd7,0x42bb},
{0x5bd8,0x5bd9,0x23b8},
{0x5bde,0x5bde,0xfb8},
{0x5bdf,0x5bdf,0xfc0},
{0x5be0,0x5be0,0x2660},
{0x5be1,0x5be1,0xfba},
{0x5be2,0x5be2,0xfbe},
{0x5be3,0x5be3,0x2661},
{0x5be4,0x5be4,0xfbf},
{0x5be5,0x5be6,0xfbb},
{0x5be7,0x5be7,0xfb9},
{0x5be8,0x5be8,0xfbd},
{0x5be9,0x5be9,0x1132},
{0x5bea,0x5bea,0x210b},
{0x5beb,0x5beb,0x1133},
{0x5bec,0x5bec,0x1131},
{0x5bee,0x5bee,0x1130},
{0x5bef,0x5bef,0x2b28},
{0x5bf0,0x5bf0,0x12ab},
{0x5bf1,0x5bf2,0x2d59},
{0x5bf3,0x5bf3,0x4115},
{0x5bf5,0x5bf5,0x157b},
{0x5bf6,0x5bf6,0x1612},
{0x5bf8,0x5bf8,0x282},
{0x5bfa,0x5bfa,0x3c5},
{0x5bff,0x5bff,0x40dd},
{0x5c01,0x5c01,0x706},
{0x5c03,0x5c03,0x1c7c},
{0x5c04,0x5c04,0x89b},
{0x5c05,0x5c05,0x4118},
{0x5c07,0x5c07,0xa5a},
{0x5c08,0x5c08,0xa59},
{0x5c09,0x5c09,0xa58},
{0x5c0a,0x5c0b,0xc2f},
{0x5c0c,0x5c0c,0x2112},
{0x5c0d,0x5c0d,0xfc1},
{0x5c0e,0x5c0e,0x12ac},
{0x5c0f,0x5c0f,0x283},
{0x5c10,0x5c10,0x1787},
{0x5c11,0x5c11,0x2c7},
{0x5c12,0x5c12,0x17a0},
{0x5c13,0x5c13,0x411a},
{0x5c14,0x5c14,0x411c},
{0x5c15,0x5c15,0x17dd},
{0x5c16,0x5c16,0x3c6},
{0x5c1a,0x5c1a,0x5a0},
{0x5c1c,0x5c1c,0x45ea},
{0x5c1e,0x5c1e,0x3a29},
{0x5c1f,0x5c1f,0x23ba},
{0x5c20,0x5c20,0x3d89},
{0x5c22,0x5c22,0x284},
{0x5c23,0x5c23,0x44e8},
{0x5c24,0x5c24,0x2c8},
{0x5c25,0x5c25,0x17de},
{0x5c28,0x5c28,0x1865},
{0x5c2a,0x5c2a,0x1866},
{0x5c2c,0x5c2c,0x484},
{0x5c30,0x5c30,0x2113},
{0x5c31,0x5c31,0xc31},
{0x5c33,0x5c33,0x23bb},
{0x5c37,0x5c37,0x13c7},
{0x5c38,0x5c38,0x285},
{0x5c39,0x5c39,0x299},
{0x5c3a,0x5c3a,0x2c9},
{0x5c3b,0x5c3b,0x17a1},
{0x5c3c,0x5c3c,0x335},
{0x5c3e,0x5c3e,0x488},
{0x5c3f,0x5c3f,0x487},
{0x5c40,0x5c41,0x485},
{0x5c44,0x5c44,0x195b},
{0x5c45,0x5c46,0x5a2},
{0x5c47,0x5c47,0x195c},
{0x5c48,0x5c48,0x5a1},
{0x5c49,0x5c49,0x411f},
{0x5c4a,0x5c4a,0x3f5c},
{0x5c4b,0x5c4b,0x70a},
{0x5c4c,0x5c4c,0x1ab4},
{0x5c4d,0x5c4d,0x709},
{0x5c4e,0x5c4f,0x707},
{0x5c50,0x5c50,0x89e},
{0x5c51,0x5c51,0x89c},
{0x5c53,0x5c53,0x3f02},
{0x5c54,0x5c54,0x1c7e},
{0x5c55,0x5c55,0x89d},
{0x5c56,0x5c56,0x1c7d},
{0x5c58,0x5c58,0x892},
{0x5c59,0x5c59,0x1ebb},
{0x5c5c,0x5c5d,0xa5c},
{0x5c5e,0x5c5e,0x3d67},
{0x5c60,0x5c60,0xa5b},
{0x5c62,0x5c62,0xfc2},
{0x5c63,0x5c63,0x2662},
{0x5c64,0x5c65,0x1134},
{0x5c67,0x5c67,0x28b7},
{0x5c68,0x5c68,0x13c8},
{0x5c69,0x5c6a,0x2f19},
{0x5c6c,0x5c6c,0x166e},
{0x5c6d,0x5c6d,0x3483},
{0x5c6e,0x5c6e,0x1776},
{0x5c6f,0x5c6f,0x2ca},
{0x5c71,0x5c71,0x286},
{0x5c73,0x5c73,0x17a3},
{0x5c74,0x5c74,0x17a2},
{0x5c79,0x5c79,0x3c7},
{0x5c7a,0x5c7b,0x17e0},
{0x5c7c,0x5c7c,0x17df},
{0x5c7e,0x5c7e,0x17e2},
{0x5c85,0x5c85,0x4121},
{0x5c86,0x5c86,0x186e},
{0x5c88,0x5c88,0x1869},
{0x5c89,0x5c89,0x186b},
{0x5c8a,0x5c8a,0x186d},
{0x5c8b,0x5c8b,0x186a},
{0x5c8c,0x5c8c,0x48c},
{0x5c8d,0x5c8d,0x1867},
{0x5c8f,0x5c8f,0x1868},
{0x5c90,0x5c91,0x489},
{0x5c92,0x5c92,0x186c},
{0x5c93,0x5c93,0x186f},
{0x5c94,0x5c94,0x48b},
{0x5c95,0x5c95,0x1870},
{0x5c99,0x5c99,0x468c},
{0x5c9a,0x5c9a,0x4551},
{0x5c9c,0x5c9c,0x495a},
{0x5c9d,0x5c9d,0x196a},
{0x5c9e,0x5c9e,0x3a2a},
{0x5c9f,0x5c9f,0x1964},
{0x5ca0,0x5ca0,0x195f},
{0x5ca1,0x5ca1,0x5a5},
{0x5ca2,0x5ca2,0x1967},
{0x5ca3,0x5ca3,0x1965},
{0x5ca4,0x5ca4,0x195e},
{0x5ca5,0x5ca5,0x196b},
{0x5ca6,0x5ca6,0x196e},
{0x5ca7,0x5ca7,0x1969},
{0x5ca8,0x5ca8,0x1962},
{0x5ca9,0x5ca9,0x5a7},
{0x5caa,0x5caa,0x1968},
{0x5cab,0x5cab,0x5a8},
{0x5cac,0x5cac,0x1963},
{0x5cad,0x5cad,0x1966},
{0x5cae,0x5cae,0x195d},
{0x5caf,0x5caf,0x1961},
{0x5cb0,0x5cb0,0x196d},
{0x5cb1,0x5cb1,0x5a9},
{0x5cb3,0x5cb3,0x5aa},
{0x5cb5,0x5cb5,0x1960},
{0x5cb6,0x5cb6,0x196c},
{0x5cb7,0x5cb7,0x5a4},
{0x5cb8,0x5cb8,0x5a6},
{0x5cba,0x5cba,0x412b},
{0x5cc1,0x5cc1,0x43b8},
{0x5cc2,0x5cc2,0x3d4c},
{0x5cc6,0x5cc6,0x1ac5},
{0x5cc7,0x5cc7,0x1abe},
{0x5cc8,0x5cc8,0x1ac4},
{0x5cc9,0x5cc9,0x1abd},
{0x5cca,0x5cca,0x1abf},
{0x5ccb,0x5ccb,0x1ab9},
{0x5ccc,0x5ccc,0x1ab7},
{0x5cce,0x5cce,0x1ac6},
{0x5ccf,0x5ccf,0x1ac3},
{0x5cd0,0x5cd0,0x1ab5},
{0x5cd1,0x5cd1,0x3f13},
{0x5cd2,0x5cd2,0x70c},
{0x5cd3,0x5cd4,0x1ac1},
{0x5cd6,0x5cd6,0x1ac0},
{0x5cd7,0x5cd7,0x1ab8},
{0x5cd8,0x5cd8,0x1ab6},
{0x5cd9,0x5cd9,0x70b},
{0x5cda,0x5cda,0x1abc},
{0x5cdb,0x5cdb,0x1aba},
{0x5cde,0x5cde,0x1abb},
{0x5cdf,0x5cdf,0x1ac7},
{0x5ce5,0x5ce5,0x4637},
{0x5ce8,0x5ce8,0x8a3},
{0x5ce9,0x5ce9,0x4122},
{0x5cea,0x5cea,0x8a2},
{0x5cec,0x5cec,0x1c7f},
{0x5ced,0x5ced,0x89f},
{0x5cee,0x5cee,0x1c81},
{0x5cef,0x5cef,0x4123},
{0x5cf0,0x5cf0,0x8a4},
{0x5cf1,0x5cf1,0x1c82},
{0x5cf4,0x5cf4,0x8a7},
{0x5cf6,0x5cf6,0x8a5},
{0x5cf7,0x5cf7,0x1c83},
{0x5cf8,0x5cf8,0x1ac8},
{0x5cf9,0x5cf9,0x1c85},
{0x5cfb,0x5cfb,0x8a1},
{0x5cfd,0x5cfd,0x8a0},
{0x5cff,0x5cff,0x1c80},
{0x5d00,0x5d00,0x1c84},
{0x5d01,0x5d01,0x8a6},
{0x5d06,0x5d06,0xa5f},
{0x5d07,0x5d07,0xa5e},
{0x5d0b,0x5d0b,0x1ebd},
{0x5d0c,0x5d0c,0x1ec1},
{0x5d0d,0x5d0d,0x1ec3},
{0x5d0e,0x5d0e,0xa60},
{0x5d0f,0x5d0f,0x1ec6},
{0x5d10,0x5d10,0x4127},
{0x5d11,0x5d11,0xa64},
{0x5d12,0x5d12,0x1ec8},
{0x5d14,0x5d14,0xa66},
{0x5d15,0x5d15,0x43b9},
{0x5d16,0x5d16,0xa62},
{0x5d17,0x5d17,0xa6a},
{0x5d18,0x5d18,0x4128},
{0x5d19,0x5d19,0xa67},
{0x5d1a,0x5d1a,0x1ebf},
{0x5d1b,0x5d1b,0xa61},
{0x5d1d,0x5d1d,0x1ebe},
{0x5d1e,0x5d1e,0x1ebc},
{0x5d1f,0x5d1f,0x1eca},
{0x5d20,0x5d20,0x1ec0},
{0x5d22,0x5d22,0xa63},
{0x5d23,0x5d23,0x1ec9},
{0x5d24,0x5d24,0xa68},
{0x5d25,0x5d25,0x1ec5},
{0x5d26,0x5d26,0x1ec4},
{0x5d27,0x5d27,0xa69},
{0x5d28,0x5d28,0x1ec2},
{0x5d29,0x5d29,0xa65},
{0x5d2c,0x5d2c,0x3df7},
{0x5d2e,0x5d2e,0x1ecb},
{0x5d2f,0x5d2f,0x46d3},
{0x5d30,0x5d30,0x1ec7},
{0x5d31,0x5d31,0x2122},
{0x5d32,0x5d32,0x2129},
{0x5d33,0x5d33,0x211e},
{0x5d34,0x5d34,0xc34},
{0x5d35,0x5d35,0x211a},
{0x5d36,0x5d36,0x212a},
{0x5d37,0x5d37,0x2114},
{0x5d38,0x5d38,0x2127},
{0x5d39,0x5d39,0x2125},
{0x5d3a,0x5d3a,0x211f},
{0x5d3c,0x5d3c,0x2128},
{0x5d3d,0x5d3d,0x2121},
{0x5d3e,0x5d3e,0x4629},
{0x5d3f,0x5d3f,0x2119},
{0x5d40,0x5d40,0x212b},
{0x5d41,0x5d41,0x2117},
{0x5d42,0x5d42,0x2124},
{0x5d43,0x5d43,0x2115},
{0x5d45,0x5d45,0x212c},
{0x5d46,0x5d46,0x4129},
{0x5d47,0x5d47,0xc35},
{0x5d48,0x5d48,0x462a},
{0x5d49,0x5d49,0x2126},
{0x5d4a,0x5d4a,0x23be},
{0x5d4b,0x5d4b,0x2118},
{0x5d4c,0x5d4c,0xc32},
{0x5d4e,0x5d4e,0x211c},
{0x5d50,0x5d50,0xc33},
{0x5d51,0x5d51,0x211b},
{0x5d52,0x5d52,0x2120},
{0x5d55,0x5d55,0x211d},
{0x5d56,0x5d56,0x43ba},
{0x5d57,0x5d57,0x3fca},
{0x5d59,0x5d59,0x2123},
{0x5d5b,0x5d5b,0x3dd5},
{0x5d5e,0x5d5e,0x23c2},
{0x5d62,0x5d62,0x23c5},
{0x5d63,0x5d63,0x23bd},
{0x5d65,0x5d65,0x23bf},
{0x5d67,0x5d67,0x23c4},
{0x5d68,0x5d68,0x23c3},
{0x5d69,0x5d69,0xdfd},
{0x5d6b,0x5d6b,0x2116},
{0x5d6c,0x5d6c,0x23c1},
{0x5d6f,0x5d6f,0xdfe},
{0x5d70,0x5d70,0x46e4},
{0x5d71,0x5d71,0x23bc},
{0x5d72,0x5d72,0x23c0},
{0x5d74,0x5d74,0x3eef},
{0x5d77,0x5d77,0x2669},
{0x5d79,0x5d79,0x2670},
{0x5d7a,0x5d7a,0x2667},
{0x5d7c,0x5d7c,0x266e},
{0x5d7d,0x5d7d,0x2665},
{0x5d7e,0x5d7e,0x266d},
{0x5d7f,0x5d7f,0x2671},
{0x5d80,0x5d80,0x2664},
{0x5d81,0x5d81,0x2668},
{0x5d82,0x5d82,0x2663},
{0x5d84,0x5d84,0xfc3},
{0x5d85,0x5d85,0x3e34},
{0x5d86,0x5d86,0x2666},
{0x5d87,0x5d87,0xfc4},
{0x5d88,0x5d88,0x266c},
{0x5d89,0x5d89,0x266b},
{0x5d8a,0x5d8a,0x266a},
{0x5d8b,0x5d8b,0x4124},
{0x5d8d,0x5d8d,0x266f},
{0x5d8e,0x5d8e,0x3f0c},
{0x5d92,0x5d92,0x28bb},
{0x5d93,0x5d93,0x28bd},
{0x5d94,0x5d94,0x1137},
{0x5d95,0x5d95,0x28be},
{0x5d97,0x5d97,0x28b9},
{0x5d99,0x5d99,0x28b8},
{0x5d9a,0x5d9a,0x28c2},
{0x5d9c,0x5d9c,0x28c0},
{0x5d9d,0x5d9d,0x1136},
{0x5d9e,0x5d9e,0x28c3},
{0x5d9f,0x5d9f,0x28ba},
{0x5da0,0x5da0,0x28bf},
{0x5da1,0x5da1,0x28c1},
{0x5da2,0x5da2,0x28bc},
{0x5da4,0x5da4,0x462d},
{0x5da7,0x5da7,0x2b2c},
{0x5da8,0x5da8,0x2b31},
{0x5da9,0x5da9,0x2b2b},
{0x5daa,0x5daa,0x2b30},
{0x5dab,0x5dab,0x3e39},
{0x5dac,0x5dac,0x2b29},
{0x5dad,0x5dad,0x2b33},
{0x5dae,0x5dae,0x2b2f},
{0x5daf,0x5daf,0x2b34},
{0x5db0,0x5db0,0x2b2e},
{0x5db1,0x5db1,0x2b2a},
{0x5db2,0x5db2,0x2b32},
{0x5db4,0x5db4,0x2b35},
{0x5db5,0x5db5,0x2b2d},
{0x5db6,0x5db6,0x4158},
{0x5db7,0x5db7,0x2d5b},
{0x5db8,0x5db8,0x13cc},
{0x5db9,0x5db9,0x462e},
{0x5dba,0x5dba,0x13ca},
{0x5dbc,0x5dbc,0x13c9},
{0x5dbd,0x5dbd,0x13cb},
{0x5dc0,0x5dc0,0x2f1b},
{0x5dc1,0x5dc1,0x3bff},
{0x5dc2,0x5dc2,0x3023},
{0x5dc3,0x5dc3,0x308c},
{0x5dc6,0x5dc7,0x31cd},
{0x5dc9,0x5dc9,0x1613},
{0x5dcb,0x5dcb,0x32ce},
{0x5dcd,0x5dcd,0x166f},
{0x5dcf,0x5dcf,0x32cf},
{0x5dd1,0x5dd1,0x337f},
{0x5dd2,0x5dd2,0x16c2},
{0x5dd4,0x5dd4,0x16c1},
{0x5dd5,0x5dd5,0x337e},
{0x5dd6,0x5dd6,0x16f7},
{0x5dd7,0x5dd7,0x412c},
{0x5dd8,0x5dd8,0x3414},
{0x5ddb,0x5ddb,0x226},
{0x5ddd,0x5ddd,0x287},
{0x5dde,0x5dde,0x3c8},
{0x5ddf,0x5ddf,0x17e3},
{0x5de0,0x5de0,0x1871},
{0x5de1,0x5de1,0x51a},
{0x5de2,0x5de2,0xa6b},
{0x5de5,0x5de5,0x288},
{0x5de6,0x5de6,0x338},
{0x5de7,0x5de7,0x337},
{0x5de8,0x5de8,0x336},
{0x5deb,0x5deb,0x48d},
{0x5dee,0x5dee,0x8a8},
{0x5df0,0x5df0,0x23c6},
{0x5df1,0x5df3,0x289},
{0x5df4,0x5df4,0x2cb},
{0x5df5,0x5df5,0x3f61},
{0x5df7,0x5df7,0x70d},
{0x5df9,0x5df9,0x1ac9},
{0x5dfd,0x5dfd,0xc36},
{0x5dfe,0x5dfe,0x28c},
{0x5dff,0x5dff,0x1788},
{0x5e02,0x5e03,0x339},
{0x5e04,0x5e04,0x17a4},
{0x5e06,0x5e06,0x3c9},
{0x5e09,0x5e09,0x4140},
{0x5e0a,0x5e0a,0x1872},
{0x5e0b,0x5e0b,0x3d8a},
{0x5e0c,0x5e0c,0x48e},
{0x5e0e,0x5e0e,0x1873},
{0x5e11,0x5e11,0x5b0},
{0x5e12,0x5e12,0x3f2e},
{0x5e14,0x5e14,0x1970},
{0x5e15,0x5e15,0x5ae},
{0x5e16,0x5e16,0x5ad},
{0x5e17,0x5e17,0x196f},
{0x5e18,0x5e18,0x5ab},
{0x5e19,0x5e19,0x1971},
{0x5e1a,0x5e1a,0x5ac},
{0x5e1b,0x5e1b,0x5af},
{0x5e1d,0x5e1d,0x70e},
{0x5e1f,0x5e1f,0x710},
{0x5e20,0x5e20,0x1acd},
{0x5e21,0x5e23,0x1aca},
{0x5e24,0x5e24,0x1ace},
{0x5e25,0x5e25,0x70f},
{0x5e28,0x5e28,0x1c87},
{0x5e29,0x5e29,0x1c86},
{0x5e2b,0x5e2b,0x8aa},
{0x5e2d,0x5e2d,0x8a9},
{0x5e2e,0x5e2e,0x4135},
{0x5e33,0x5e33,0xa6e},
{0x5e34,0x5e34,0x1ecd},
{0x5e36,0x5e36,0xa6d},
{0x5e37,0x5e37,0xa6f},
{0x5e38,0x5e38,0xa6c},
{0x5e3d,0x5e3d,0xc38},
{0x5e3e,0x5e3e,0x1ecc},
{0x5e40,0x5e40,0xc39},
{0x5e41,0x5e41,0x212e},
{0x5e42,0x5e42,0x43a0},
{0x5e43,0x5e43,0xc3a},
{0x5e44,0x5e44,0x212d},
{0x5e45,0x5e45,0xc37},
{0x5e48,0x5e48,0x3a2c},
{0x5e4a,0x5e4a,0x23c9},
{0x5e4b,0x5e4b,0x23cb},
{0x5e4c,0x5e4c,0xdff},
{0x5e4d,0x5e4d,0x23ca},
{0x5e4e,0x5e4e,0x23c8},
{0x5e4f,0x5e4f,0x23c7},
{0x5e53,0x5e53,0x2674},
{0x5e54,0x5e54,0xfc9},
{0x5e55,0x5e55,0xfc7},
{0x5e57,0x5e57,0xfc8},
{0x5e58,0x5e59,0x2672},
{0x5e5b,0x5e5b,0xfc5},
{0x5e5c,0x5e5c,0x28c7},
{0x5e5d,0x5e5d,0x28c5},
{0x5e5e,0x5e5e,0x3a2b},
{0x5e5f,0x5e5f,0x1139},
{0x5e60,0x5e60,0x28c6},
{0x5e61,0x5e61,0x113a},
{0x5e62,0x5e62,0x1138},
{0x5e63,0x5e63,0xfc6},
{0x5e66,0x5e66,0x2b38},
{0x5e67,0x5e68,0x2b36},
{0x5e69,0x5e69,0x28c4},
{0x5e6a,0x5e6a,0x2d5d},
{0x5e6b,0x5e6b,0x13cd},
{0x5e6c,0x5e6c,0x2d5c},
{0x5e6d,0x5e6e,0x2f1c},
{0x5e6f,0x5e6f,0x2b39},
{0x5e70,0x5e70,0x308d},
{0x5e72,0x5e72,0x28d},
{0x5e73,0x5e73,0x33b},
{0x5e74,0x5e74,0x3cb},
{0x5e75,0x5e75,0x17e4},
{0x5e76,0x5e76,0x3ca},
{0x5e78,0x5e78,0x5b1},
{0x5e79,0x5e79,0xe00},
{0x5e7a,0x5e7a,0x227},
{0x5e7b,0x5e7b,0x2cc},
{0x5e7c,0x5e7c,0x33c},
{0x5e7d,0x5e7d,0x711},
{0x5e7e,0x5e7e,0xc3b},
{0x5e7f,0x5e7f,0x228},
{0x5e80,0x5e80,0x17a5},
{0x5e82,0x5e82,0x17a6},
{0x5e83,0x5e83,0x4108},
{0x5e84,0x5e84,0x17e5},
{0x5e86,0x5e86,0x4552},
{0x5e87,0x5e87,0x490},
{0x5e88,0x5e88,0x1877},
{0x5e89,0x5e89,0x1875},
{0x5e8a,0x5e8a,0x491},
{0x5e8b,0x5e8b,0x1874},
{0x5e8c,0x5e8c,0x1876},
{0x5e8d,0x5e8d,0x1878},
{0x5e8f,0x5e8f,0x48f},
{0x5e95,0x5e96,0x5b5},
{0x5e97,0x5e97,0x5b3},
{0x5e9a,0x5e9a,0x5b2},
{0x5e9b,0x5e9b,0x1ad2},
{0x5e9c,0x5e9c,0x5b4},
{0x5ea0,0x5ea0,0x712},
{0x5ea2,0x5ea2,0x1ad1},
{0x5ea3,0x5ea3,0x1ad3},
{0x5ea4,0x5ea4,0x1ad0},
{0x5ea5,0x5ea5,0x1ad4},
{0x5ea6,0x5ea6,0x713},
{0x5ea7,0x5ea7,0x8ad},
{0x5ea8,0x5ea8,0x1c88},
{0x5eaa,0x5eaa,0x1c8a},
{0x5eab,0x5eab,0x8ab},
{0x5eac,0x5eac,0x1c8b},
{0x5ead,0x5ead,0x8ac},
{0x5eae,0x5eae,0x1c89},
{0x5eb0,0x5eb0,0x1acf},
{0x5eb1,0x5eb1,0x1ece},
{0x5eb2,0x5eb3,0x1ed1},
{0x5eb4,0x5eb4,0x1ecf},
{0x5eb5,0x5eb5,0xa73},
{0x5eb6,0x5eb6,0xa72},
{0x5eb7,0x5eb8,0xa70},
{0x5eb9,0x5eb9,0x1ed0},
{0x5ebd,0x5ebd,0x43bd},
{0x5ebe,0x5ebe,0xa74},
{0x5ec1,0x5ec2,0xc3d},
{0x5ec4,0x5ec4,0xc3f},
{0x5ec5,0x5ec5,0x23cc},
{0x5ec6,0x5ec6,0x23ce},
{0x5ec7,0x5ec7,0x23d0},
{0x5ec8,0x5ec8,0xe02},
{0x5ec9,0x5ec9,0xe01},
{0x5eca,0x5eca,0xc3c},
{0x5ecb,0x5ecb,0x23cf},
{0x5ecc,0x5ecc,0x23cd},
{0x5ecd,0x5ecd,0x3a30},
{0x5ece,0x5ece,0x2678},
{0x5ed1,0x5ed1,0x2676},
{0x5ed2,0x5ed2,0x267c},
{0x5ed3,0x5ed3,0xfca},
{0x5ed4,0x5ed4,0x267d},
{0x5ed5,0x5ed5,0x267a},
{0x5ed6,0x5ed6,0xfcb},
{0x5ed7,0x5ed7,0x2677},
{0x5ed8,0x5ed8,0x2675},
{0x5ed9,0x5ed9,0x267b},
{0x5eda,0x5eda,0x113c},
{0x5edb,0x5edb,0x28c9},
{0x5edc,0x5edc,0x2679},
{0x5edd,0x5edd,0x113e},
{0x5ede,0x5ede,0x28ca},
{0x5edf,0x5edf,0x113d},
{0x5ee0,0x5ee0,0x1140},
{0x5ee1,0x5ee1,0x28cb},
{0x5ee2,0x5ee2,0x113b},
{0x5ee3,0x5ee3,0x113f},
{0x5ee5,0x5ee5,0x2b3e},
{0x5ee6,0x5ee6,0x2b3c},
{0x5ee7,0x5ee7,0x2b3b},
{0x5ee8,0x5ee8,0x2b3d},
{0x5ee9,0x5ee9,0x2b3a},
{0x5eec,0x5eec,0x157d},
{0x5eee,0x5eef,0x31cf},
{0x5ef1,0x5ef1,0x32d0},
{0x5ef2,0x5ef2,0x3380},
{0x5ef3,0x5ef3,0x1742},
{0x5ef4,0x5ef4,0x229},
{0x5ef6,0x5ef6,0x5b7},
{0x5ef7,0x5ef7,0x492},
{0x5ef8,0x5ef8,0x386f},
{0x5ef9,0x5ef9,0x4143},
{0x5efa,0x5efa,0x714},
{0x5efb,0x5efb,0x4144},
{0x5efc,0x5efc,0x4146},
{0x5efe,0x5efe,0x28e},
{0x5eff,0x5eff,0x2cd},
{0x5f01,0x5f01,0x33d},
{0x5f02,0x5f02,0x17e6},
{0x5f04,0x5f04,0x493},
{0x5f05,0x5f05,0x1879},
{0x5f07,0x5f07,0x1ad5},
{0x5f08,0x5f08,0x715},
{0x5f0a,0x5f0a,0xfcc},
{0x5f0b,0x5f0b,0x28f},
{0x5f0c,0x5f0c,0x3a3e},
{0x5f0d,0x5f0d,0x4149},
{0x5f0e,0x5f0e,0x3a3f},
{0x5f0f,0x5f0f,0x3cc},
{0x5f12,0x5f12,0xe03},
{0x5f13,0x5f13,0x290},
{0x5f14,0x5f15,0x2ce},
{0x5f17,0x5f17,0x33f},
{0x5f18,0x5f18,0x33e},
{0x5f1a,0x5f1a,0x17e7},
{0x5f1b,0x5f1b,0x3cd},
{0x5f1d,0x5f1d,0x187a},
{0x5f1f,0x5f1f,0x494},
{0x5f22,0x5f24,0x1973},
{0x5f25,0x5f25,0x4630},
{0x5f26,0x5f27,0x5b8},
{0x5f28,0x5f28,0x1972},
{0x5f29,0x5f29,0x5ba},
{0x5f2d,0x5f2d,0x716},
{0x5f2e,0x5f2e,0x1ad6},
{0x5f30,0x5f30,0x1c8d},
{0x5f31,0x5f31,0x8ae},
{0x5f33,0x5f33,0x1c8c},
{0x5f35,0x5f35,0xa75},
{0x5f36,0x5f36,0x1ed3},
{0x5f37,0x5f37,0xa76},
{0x5f38,0x5f38,0x1ed4},
{0x5f3a,0x5f3a,0x414e},
{0x5f3c,0x5f3c,0xc40},
{0x5f40,0x5f40,0x23d1},
{0x5f43,0x5f43,0x267f},
{0x5f44,0x5f44,0x267e},
{0x5f46,0x5f46,0xfcd},
{0x5f48,0x5f48,0x1141},
{0x5f49,0x5f49,0x28cc},
{0x5f4a,0x5f4a,0x12ad},
{0x5f4b,0x5f4b,0x2b3f},
{0x5f4c,0x5f4c,0x13ce},
{0x5f4d,0x5f4d,0x3ba5},
{0x5f4e,0x5f4e,0x16c3},
{0x5f4f,0x5f4f,0x3416},
{0x5f50,0x5f50,0x22a},
{0x5f51,0x5f51,0x44e9},
{0x5f54,0x5f54,0x1976},
{0x5f56,0x5f56,0x1ad7},
{0x5f57,0x5f57,0xa77},
{0x5f58,0x5f58,0x212f},
{0x5f59,0x5f59,0xe04},
{0x5f5c,0x5f5c,0x3d5a},
{0x5f5d,0x5f5d,0x14cf},
{0x5f61,0x5f61,0x22b},
{0x5f62,0x5f62,0x496},
{0x5f63,0x5f63,0x4152},
{0x5f64,0x5f64,0x495},
{0x5f65,0x5f65,0x717},
{0x5f67,0x5f67,0x1c8e},
{0x5f69,0x5f69,0xa79},
{0x5f6a,0x5f6a,0xb89},
{0x5f6b,0x5f6b,0xa7a},
{0x5f6c,0x5f6c,0xa78},
{0x5f6d,0x5f6d,0xc41},
{0x5f6f,0x5f6f,0x2680},
{0x5f70,0x5f70,0xfce},
{0x5f71,0x5f71,0x1142},
{0x5f72,0x5f72,0x4154},
{0x5f73,0x5f73,0x1777},
{0x5f74,0x5f74,0x17e8},
{0x5f76,0x5f76,0x187c},
{0x5f77,0x5f77,0x497},
{0x5f78,0x5f78,0x187b},
{0x5f79,0x5f79,0x498},
{0x5f7b,0x5f7b,0x4058},
{0x5f7c,0x5f7c,0x5be},
{0x5f7d,0x5f7d,0x1979},
{0x5f7e,0x5f7e,0x1978},
{0x5f7f,0x5f7f,0x5bd},
{0x5f80,0x5f81,0x5bb},
{0x5f82,0x5f82,0x1977},
{0x5f83,0x5f83,0x4631},
{0x5f85,0x5f85,0x719},
{0x5f86,0x5f86,0x1ad8},
{0x5f87,0x5f87,0x71c},
{0x5f88,0x5f88,0x718},
{0x5f89,0x5f89,0x71e},
{0x5f8a,0x5f8b,0x71a},
{0x5f8c,0x5f8c,0x71d},
{0x5f90,0x5f90,0x8b1},
{0x5f91,0x5f91,0x8b0},
{0x5f92,0x5f92,0x8af},
{0x5f96,0x5f96,0x1ed6},
{0x5f97,0x5f97,0xa7b},
{0x5f98,0x5f98,0xa7e},
{0x5f99,0x5f99,0xa7c},
{0x5f9b,0x5f9b,0x1ed5},
{0x5f9c,0x5f9c,0xa81},
{0x5f9e,0x5f9e,0xa7d},
{0x5f9f,0x5f9f,0x1ed7},
{0x5fa0,0x5fa0,0xa80},
{0x5fa1,0x5fa1,0xa7f},
{0x5fa4,0x5fa4,0x402c},
{0x5fa5,0x5fa5,0x2131},
{0x5fa6,0x5fa6,0x2130},
{0x5fa7,0x5fa7,0x4157},
{0x5fa8,0x5fa8,0xc44},
{0x5fa9,0x5faa,0xc42},
{0x5fab,0x5fab,0x2132},
{0x5fac,0x5fac,0xe05},
{0x5fad,0x5fad,0x23d3},
{0x5fae,0x5fae,0xe06},
{0x5faf,0x5faf,0x23d2},
{0x5fb1,0x5fb1,0x3d98},
{0x5fb2,0x5fb2,0x28cd},
{0x5fb5,0x5fb5,0x1144},
{0x5fb6,0x5fb6,0x2681},
{0x5fb7,0x5fb7,0x1143},
{0x5fb9,0x5fb9,0xfcf},
{0x5fba,0x5fba,0x3f9f},
{0x5fbb,0x5fbb,0x2b41},
{0x5fbc,0x5fbc,0x2b40},
{0x5fbd,0x5fbd,0x13cf},
{0x5fbe,0x5fbe,0x2d5e},
{0x5fbf,0x5fbf,0x308e},
{0x5fc0,0x5fc1,0x31d1},
{0x5fc3,0x5fc3,0x2d0},
{0x5fc4,0x5fc4,0x44ea},
{0x5fc5,0x5fc5,0x340},
{0x5fc9,0x5fc9,0x17a7},
{0x5fcc,0x5fcc,0x49a},
{0x5fcd,0x5fcd,0x49c},
{0x5fcf,0x5fcf,0x17eb},
{0x5fd0,0x5fd0,0x187f},
{0x5fd1,0x5fd1,0x187e},
{0x5fd2,0x5fd2,0x187d},
{0x5fd4,0x5fd4,0x17ea},
{0x5fd5,0x5fd5,0x17e9},
{0x5fd6,0x5fd6,0x3cf},
{0x5fd7,0x5fd7,0x49b},
{0x5fd8,0x5fd8,0x499},
{0x5fd9,0x5fd9,0x3ce},
{0x5fdb,0x5fdb,0x3a4a},
{0x5fdd,0x5fdd,0x5bf},
{0x5fde,0x5fde,0x197a},
{0x5fdf,0x5fdf,0x41af},
{0x5fe0,0x5fe0,0x5c0},
{0x5fe1,0x5fe1,0x1884},
{0x5fe3,0x5fe3,0x1886},
{0x5fe4,0x5fe4,0x1885},
{0x5fe5,0x5fe5,0x197b},
{0x5fe8,0x5fe8,0x1881},
{0x5fea,0x5fea,0x4a0},
{0x5feb,0x5feb,0x49e},
{0x5fed,0x5fed,0x1880},
{0x5fee,0x5fee,0x1882},
{0x5fef,0x5fef,0x1888},
{0x5ff1,0x5ff1,0x49d},
{0x5ff3,0x5ff3,0x1883},
{0x5ff4,0x5ff4,0x188c},
{0x5ff5,0x5ff5,0x5c2},
{0x5ff7,0x5ff7,0x1889},
{0x5ff8,0x5ff8,0x49f},
{0x5ffa,0x5ffa,0x1887},
{0x5ffb,0x5ffb,0x188a},
{0x5ffd,0x5ffd,0x5c1},
{0x5fff,0x5fff,0x5c3},
{0x6000,0x6000,0x188b},
{0x6009,0x6009,0x198f},
{0x600a,0x600a,0x1982},
{0x600b,0x600b,0x1980},
{0x600c,0x600c,0x198e},
{0x600d,0x600d,0x1989},
{0x600e,0x600e,0x723},
{0x600f,0x600f,0x5c4},
{0x6010,0x6010,0x198a},
{0x6011,0x6011,0x198d},
{0x6012,0x6012,0x71f},
{0x6013,0x6013,0x198c},
{0x6014,0x6014,0x5c5},
{0x6015,0x6015,0x5ca},
{0x6016,0x6016,0x5c8},
{0x6017,0x6017,0x1983},
{0x6019,0x6019,0x197e},
{0x601a,0x601a,0x1985},
{0x601b,0x601b,0x5cf},
{0x601c,0x601c,0x1990},
{0x601d,0x601d,0x720},
{0x601e,0x601e,0x1986},
{0x6020,0x6020,0x721},
{0x6021,0x6021,0x5cb},
{0x6022,0x6022,0x1988},
{0x6023,0x6023,0x4185},
{0x6024,0x6024,0x1ae7},
{0x6025,0x6025,0x722},
{0x6026,0x6026,0x197d},
{0x6027,0x6027,0x5cc},
{0x6028,0x6028,0x724},
{0x6029,0x6029,0x5cd},
{0x602a,0x602a,0x5c9},
{0x602b,0x602b,0x5ce},
{0x602c,0x602c,0x1987},
{0x602d,0x602d,0x197c},
{0x602e,0x602e,0x198b},
{0x602f,0x602f,0x5c6},
{0x6031,0x6031,0x4161},
{0x6032,0x6032,0x197f},
{0x6033,0x6033,0x1984},
{0x6034,0x6034,0x1981},
{0x6035,0x6035,0x5c7},
{0x6037,0x6037,0x1ad9},
{0x6039,0x6039,0x1ada},
{0x603b,0x603b,0x4553},
{0x6040,0x6040,0x1ae4},
{0x6041,0x6041,0x1c92},
{0x6042,0x6042,0x1ae5},
{0x6043,0x6043,0x72a},
{0x6044,0x6044,0x1ae8},
{0x6045,0x6045,0x1ade},
{0x6046,0x6046,0x729},
{0x6047,0x6047,0x1ae0},
{0x6049,0x6049,0x1ae1},
{0x604a,0x604a,0x4074},
{0x604c,0x604c,0x1ae3},
{0x604d,0x604d,0x725},
{0x6050,0x6050,0x8b5},
{0x6052,0x6052,0x36ec},
{0x6053,0x6053,0x1adf},
{0x6054,0x6054,0x1adb},
{0x6055,0x6055,0x8b6},
{0x6058,0x6058,0x1ae9},
{0x6059,0x6059,0x8b2},
{0x605a,0x605a,0x1c90},
{0x605b,0x605b,0x1ae2},
{0x605d,0x605d,0x1c8f},
{0x605e,0x605e,0x1add},
{0x605f,0x605f,0x1ae6},
{0x6062,0x6062,0x728},
{0x6063,0x6063,0x8b3},
{0x6064,0x6064,0x72e},
{0x6065,0x6065,0x8b4},
{0x6066,0x6066,0x1aea},
{0x6067,0x6067,0x1c91},
{0x6068,0x6068,0x727},
{0x6069,0x6069,0x8b8},
{0x606a,0x606a,0x72d},
{0x606b,0x606b,0x72c},
{0x606c,0x606c,0x72b},
{0x606d,0x606d,0x8b7},
{0x606e,0x606e,0x1aeb},
{0x606f,0x606f,0x8b9},
{0x6070,0x6070,0x726},
{0x6072,0x6072,0x1adc},
{0x6075,0x6075,0x3a56},
{0x6077,0x6077,0x4005},
{0x607e,0x607e,0x3a47},
{0x607f,0x607f,0xa82},
{0x6080,0x6080,0x1c95},
{0x6081,0x6081,0x1c97},
{0x6083,0x6083,0x1c99},
{0x6084,0x6084,0x8ba},
{0x6085,0x6085,0x8c0},
{0x6086,0x6086,0x1eda},
{0x6087,0x6087,0x1c9d},
{0x6088,0x6088,0x1c94},
{0x6089,0x6089,0xa84},
{0x608a,0x608a,0x1ed8},
{0x608c,0x608c,0x8bf},
{0x608d,0x608d,0x8bd},
{0x608e,0x608e,0x1c9f},
{0x6090,0x6090,0x1ed9},
{0x6092,0x6092,0x1c96},
{0x6094,0x6094,0x8be},
{0x6095,0x6095,0x1c9a},
{0x6096,0x6096,0x8c1},
{0x6097,0x6097,0x1c9c},
{0x609a,0x609a,0x8bc},
{0x609b,0x609b,0x1c9b},
{0x609c,0x609c,0x1c9e},
{0x609d,0x609d,0x1c98},
{0x609e,0x609e,0x416a},
{0x609f,0x609f,0x8bb},
{0x60a0,0x60a0,0xa85},
{0x60a2,0x60a2,0x1c93},
{0x60a3,0x60a3,0xa83},
{0x60a4,0x60a4,0x4001},
{0x60a7,0x60a7,0x3adc},
{0x60a8,0x60a8,0xa86},
{0x60b0,0x60b0,0x1edc},
{0x60b1,0x60b1,0x1ee5},
{0x60b2,0x60b2,0xc47},
{0x60b4,0x60b4,0xa88},
{0x60b5,0x60b5,0xa8d},
{0x60b6,0x60b6,0xc48},
{0x60b7,0x60b7,0x1ee7},
{0x60b8,0x60b8,0xa94},
{0x60b9,0x60b9,0x2134},
{0x60ba,0x60ba,0x1edd},
{0x60bb,0x60bb,0xa8c},
{0x60bc,0x60bc,0xa8f},
{0x60bd,0x60bd,0xa8a},
{0x60be,0x60be,0x1edb},
{0x60bf,0x60bf,0x1ee9},
{0x60c0,0x60c0,0x1eec},
{0x60c1,0x60c1,0x2143},
{0x60c3,0x60c3,0x1eea},
{0x60c4,0x60c4,0x2138},
{0x60c5,0x60c5,0xa8b},
{0x60c6,0x60c6,0xa92},
{0x60c7,0x60c7,0xa96},
{0x60c8,0x60c8,0x1ee4},
{0x60c9,0x60c9,0x2133},
{0x60ca,0x60ca,0x1ee8},
{0x60cb,0x60cb,0xa87},
{0x60cc,0x60cc,0x2135},
{0x60cd,0x60cd,0x1eeb},
{0x60ce,0x60ce,0x2137},
{0x60cf,0x60cf,0x1ee0},
{0x60d1,0x60d1,0xc45},
{0x60d3,0x60d4,0x1ede},
{0x60d5,0x60d5,0xa91},
{0x60d7,0x60d7,0x4635},
{0x60d8,0x60d8,0xa90},
{0x60d9,0x60d9,0x1ee2},
{0x60da,0x60da,0xa95},
{0x60db,0x60db,0x1ee6},
{0x60dc,0x60dc,0xa8e},
{0x60dd,0x60dd,0x1ee3},
{0x60df,0x60df,0xa93},
{0x60e0,0x60e0,0xc49},
{0x60e1,0x60e1,0xc46},
{0x60e2,0x60e2,0x2136},
{0x60e3,0x60e3,0x3d75},
{0x60e4,0x60e4,0x1ee1},
{0x60e6,0x60e6,0xa89},
{0x60e7,0x60e7,0x3d84},
{0x60e8,0x60e8,0x3d7b},
{0x60e9,0x60e9,0x4009},
{0x60f0,0x60f0,0xc4e},
{0x60f1,0x60f1,0xc52},
{0x60f2,0x60f2,0x213a},
{0x60f3,0x60f3,0xe0b},
{0x60f4,0x60f4,0xc50},
{0x60f5,0x60f5,0x213e},
{0x60f6,0x60f6,0xc54},
{0x60f7,0x60f7,0x23d4},
{0x60f8,0x60f8,0x2140},
{0x60f9,0x60f9,0xe0d},
{0x60fa,0x60fa,0xc4c},
{0x60fb,0x60fb,0xc4f},
{0x60fc,0x60fc,0x2141},
{0x60fd,0x60fd,0x3fa8},
{0x60fe,0x60fe,0x2142},
{0x60ff,0x60ff,0x2148},
{0x6100,0x6100,0xc56},
{0x6101,0x6101,0xe0e},
{0x6103,0x6103,0x2144},
{0x6104,0x6104,0x2149},
{0x6105,0x6105,0x213d},
{0x6106,0x6106,0xe18},
{0x6107,0x6107,0x3c35},
{0x6108,0x6108,0xe0f},
{0x6109,0x6109,0xc55},
{0x610a,0x610a,0x213b},
{0x610b,0x610b,0x214a},
{0x610c,0x610c,0x3c87},
{0x610d,0x610d,0xe17},
{0x610e,0x610e,0xc53},
{0x610f,0x610f,0xe08},
{0x6110,0x6110,0x2147},
{0x6112,0x6112,0xc57},
{0x6113,0x6113,0x213f},
{0x6114,0x6114,0x2139},
{0x6115,0x6115,0xc4d},
{0x6116,0x6116,0x213c},
{0x6118,0x6118,0x2145},
{0x6119,0x6119,0x3ef6},
{0x611a,0x611a,0xe07},
{0x611b,0x611b,0xe0c},
{0x611c,0x611c,0xc4a},
{0x611d,0x611d,0x2146},
{0x611f,0x611f,0xe0a},
{0x6123,0x6123,0xc4b},
{0x6127,0x6127,0xe16},
{0x6128,0x6128,0x2683},
{0x6129,0x6129,0x23df},
{0x612b,0x612b,0x23d7},
{0x612c,0x612c,0x2682},
{0x612e,0x612e,0x23db},
{0x612f,0x612f,0x23dd},
{0x6130,0x6130,0x3f37},
{0x6132,0x6132,0x23da},
{0x6134,0x6134,0xe15},
{0x6136,0x6136,0x23d9},
{0x6137,0x6137,0xe19},
{0x613b,0x613b,0x2692},
{0x613d,0x613d,0x4636},
{0x613e,0x613e,0xe14},
{0x613f,0x613f,0xfd1},
{0x6140,0x6140,0x23e0},
{0x6141,0x6141,0x2684},
{0x6142,0x6142,0x4174},
{0x6144,0x6144,0xe12},
{0x6145,0x6145,0x23d8},
{0x6146,0x6146,0x23dc},
{0x6147,0x6147,0xfd0},
{0x6148,0x6148,0xe09},
{0x6149,0x614a,0x23d5},
{0x614b,0x614b,0xfd2},
{0x614c,0x614c,0xe11},
{0x614d,0x614d,0xe13},
{0x614e,0x614e,0xe10},
{0x614f,0x614f,0x23de},
{0x6150,0x6150,0x3c32},
{0x6152,0x6153,0x2688},
{0x6154,0x6154,0x268e},
{0x6155,0x6155,0x1149},
{0x6156,0x6156,0x2695},
{0x6158,0x6158,0xfd8},
{0x6159,0x6159,0x3fba},
{0x615a,0x615a,0xfd7},
{0x615b,0x615b,0x2690},
{0x615c,0x615c,0x4186},
{0x615d,0x615d,0x1148},
{0x615e,0x615e,0x2685},
{0x615f,0x615f,0xfd6},
{0x6160,0x6160,0x494c},
{0x6161,0x6161,0x2694},
{0x6162,0x6163,0xfd4},
{0x6164,0x6164,0x4173},
{0x6165,0x6165,0x2691},
{0x6166,0x6166,0x28de},
{0x6167,0x6167,0x1146},
{0x6168,0x6168,0xc51},
{0x616a,0x616a,0x2693},
{0x616b,0x616b,0x114d},
{0x616c,0x616c,0x268b},
{0x616e,0x616e,0x1147},
{0x616f,0x616f,0x3fc0},
{0x6170,0x6170,0x114c},
{0x6171,0x6171,0x2686},
{0x6172,0x6172,0x268a},
{0x6173,0x6173,0x2687},
{0x6174,0x6174,0x268d},
{0x6175,0x6175,0xfd9},
{0x6176,0x6176,0x1145},
{0x6177,0x6177,0xfd3},
{0x6179,0x6179,0x28d0},
{0x617a,0x617a,0x268f},
{0x617c,0x617c,0x114b},
{0x617d,0x617d,0x3fbd},
{0x617e,0x617e,0x114e},
{0x6180,0x6180,0x268c},
{0x6181,0x6181,0x4177},
{0x6182,0x6182,0x114a},
{0x6183,0x6183,0x28cf},
{0x6187,0x6187,0x417a},
{0x6189,0x6189,0x28d4},
{0x618a,0x618a,0x12b1},
{0x618b,0x618b,0x28ce},
{0x618c,0x618c,0x2b4d},
{0x618d,0x618d,0x28dd},
{0x618e,0x618e,0x1152},
{0x6190,0x6190,0x1150},
{0x6191,0x6191,0x12af},
{0x6192,0x6192,0x28da},
{0x6193,0x6193,0x28d6},
{0x6194,0x6194,0x1156},
{0x6195,0x6195,0x3de0},
{0x6196,0x6196,0x2b44},
{0x6198,0x6198,0x3a55},
{0x6199,0x6199,0x3a54},
{0x619a,0x619a,0x1154},
{0x619b,0x619b,0x28d5},
{0x619c,0x619c,0x4002},
{0x619d,0x619d,0x2b42},
{0x619f,0x619f,0x28d9},
{0x61a1,0x61a1,0x28dc},
{0x61a2,0x61a2,0x28d3},
{0x61a4,0x61a4,0x1155},
{0x61a7,0x61a7,0x114f},
{0x61a8,0x61a8,0x2b43},
{0x61a9,0x61a9,0x12b0},
{0x61aa,0x61aa,0x28db},
{0x61ab,0x61ab,0x1151},
{0x61ac,0x61ac,0x1153},
{0x61ad,0x61ad,0x28d8},
{0x61ae,0x61ae,0x1157},
{0x61af,0x61af,0x28d7},
{0x61b0,0x61b0,0x28d2},
{0x61b1,0x61b1,0x28d1},
{0x61b2,0x61b2,0x12ae},
{0x61b3,0x61b3,0x28df},
{0x61b4,0x61b4,0x2b46},
{0x61b5,0x61b5,0x2d60},
{0x61b6,0x61b6,0x12b3},
{0x61b7,0x61b7,0x4639},
{0x61b8,0x61b8,0x2b4c},
{0x61b9,0x61b9,0x43bf},
{0x61ba,0x61ba,0x2b4a},
{0x61bc,0x61bc,0x2d61},
{0x61be,0x61be,0x12b4},
{0x61bf,0x61bf,0x2b4b},
{0x61c0,0x61c0,0x3a50},
{0x61c1,0x61c1,0x2b48},
{0x61c2,0x61c2,0x13d1},
{0x61c3,0x61c3,0x2d5f},
{0x61c5,0x61c5,0x2b45},
{0x61c6,0x61c6,0x2b47},
{0x61c7,0x61c7,0x13d2},
{0x61c8,0x61c8,0x12b6},
{0x61c9,0x61c9,0x13d0},
{0x61ca,0x61ca,0x12b5},
{0x61cb,0x61cb,0x13d4},
{0x61cc,0x61cc,0x2b49},
{0x61cd,0x61cd,0x12b2},
{0x61cf,0x61cf,0x463a},
{0x61d0,0x61d0,0x4181},
{0x61d3,0x61d3,0x417e},
{0x61d6,0x61d6,0x2f26},
{0x61d8,0x61d8,0x2f1e},
{0x61da,0x61da,0x38b1},
{0x61de,0x61de,0x2d67},
{0x61df,0x61df,0x2f1f},
{0x61e0,0x61e0,0x2d63},
{0x61e2,0x61e2,0x3fc5},
{0x61e3,0x61e3,0x14d0},
{0x61e4,0x61e4,0x2d65},
{0x61e5,0x61e5,0x2d64},
{0x61e6,0x61e6,0x13d3},
{0x61e7,0x61e7,0x2d62},
{0x61e8,0x61e8,0x2d66},
{0x61e9,0x61e9,0x2f27},
{0x61ea,0x61ea,0x2f23},
{0x61eb,0x61eb,0x2f25},
{0x61ed,0x61ee,0x2f20},
{0x61f0,0x61f0,0x2f24},
{0x61f1,0x61f1,0x2f22},
{0x61f2,0x61f2,0x157e},
{0x61f5,0x61f5,0x1581},
{0x61f6,0x61f6,0x1580},
{0x61f7,0x61f7,0x157f},
{0x61f8,0x61f8,0x1614},
{0x61f9,0x61f9,0x31d3},
{0x61fa,0x61fa,0x1615},
{0x61fb,0x61fb,0x308f},
{0x61fc,0x61fc,0x1670},
{0x61fd,0x61fd,0x32d1},
{0x61fe,0x61fe,0x1671},
{0x61ff,0x61ff,0x16c4},
{0x6200,0x6200,0x16f8},
{0x6201,0x6201,0x3417},
{0x6203,0x6204,0x3418},
{0x6207,0x6207,0x3533},
{0x6208,0x6208,0x2d1},
{0x6209,0x6209,0x17a8},
{0x620a,0x620a,0x341},
{0x620c,0x620d,0x3d1},
{0x620e,0x620e,0x3d0},
{0x6210,0x6210,0x3d3},
{0x6211,0x6211,0x4a2},
{0x6212,0x6212,0x4a1},
{0x6214,0x6214,0x1991},
{0x6215,0x6215,0x5d1},
{0x6216,0x6216,0x5d0},
{0x6219,0x6219,0x1ca0},
{0x621a,0x621b,0xa97},
{0x621f,0x621f,0xc58},
{0x6220,0x6220,0x23e1},
{0x6221,0x6222,0xe1a},
{0x6223,0x6223,0x23e3},
{0x6224,0x6224,0x23e5},
{0x6225,0x6225,0x23e4},
{0x6227,0x6227,0x2697},
{0x6229,0x6229,0x2696},
{0x622a,0x622a,0xfda},
{0x622b,0x622b,0x2698},
{0x622c,0x622c,0x463c},
{0x622d,0x622d,0x28e0},
{0x622e,0x622e,0x1158},
{0x6230,0x6230,0x12b7},
{0x6232,0x6232,0x13d5},
{0x6233,0x6233,0x14d1},
{0x6234,0x6234,0x13d6},
{0x6236,0x6236,0x2d2},
{0x6237,0x6237,0x451a},
{0x6239,0x6239,0x3fc2},
{0x623a,0x623a,0x188d},
{0x623d,0x623d,0x1992},
{0x623e,0x623e,0x5d3},
{0x623f,0x623f,0x5d2},
{0x6240,0x6240,0x5d4},
{0x6241,0x6241,0x72f},
{0x6242,0x6243,0x1aec},
{0x6246,0x6246,0x1ca1},
{0x6247,0x6247,0x8c2},
{0x6248,0x6248,0xa99},
{0x6249,0x6249,0xc59},
{0x624a,0x624a,0x214b},
{0x624b,0x624b,0x2d3},
{0x624c,0x624c,0x44ec},
{0x624d,0x624d,0x291},
{0x624e,0x624e,0x2d4},
{0x6250,0x6250,0x17a9},
{0x6251,0x6251,0x345},
{0x6252,0x6252,0x344},
{0x6253,0x6254,0x342},
{0x6258,0x6258,0x3d6},
{0x6259,0x6259,0x17f2},
{0x625a,0x625a,0x17f4},
{0x625b,0x625b,0x3d5},
{0x625c,0x625c,0x17ec},
{0x625e,0x625e,0x17ed},
{0x6260,0x6260,0x17f3},
{0x6261,0x6261,0x17ef},
{0x6262,0x6262,0x17f1},
{0x6263,0x6263,0x3d4},
{0x6264,0x6264,0x17ee},
{0x6265,0x6265,0x17f5},
{0x6266,0x6266,0x17f0},
{0x6268,0x6268,0x3f15},
{0x626d,0x626d,0x4a9},
{0x626e,0x626e,0x4b2},
{0x626f,0x626f,0x4b0},
{0x6270,0x6270,0x1897},
{0x6271,0x6271,0x1894},
{0x6272,0x6272,0x189c},
{0x6273,0x6273,0x4ae},
{0x6274,0x6274,0x189d},
{0x6276,0x6276,0x4a7},
{0x6277,0x6277,0x189a},
{0x6279,0x6279,0x4ad},
{0x627a,0x627a,0x1896},
{0x627b,0x627b,0x1895},
{0x627c,0x627c,0x4ab},
{0x627d,0x627d,0x189b},
{0x627e,0x627e,0x4ac},
{0x627f,0x627f,0x5d5},
{0x6280,0x6280,0x4a6},
{0x6281,0x6281,0x1898},
{0x6282,0x6282,0x3f86},
{0x6283,0x6283,0x188e},
{0x6284,0x6284,0x4a3},
{0x6285,0x6285,0x3f50},
{0x6286,0x6286,0x4b6},
{0x6287,0x6287,0x1893},
{0x6288,0x6288,0x1899},
{0x6289,0x6289,0x4a8},
{0x628a,0x628a,0x4aa},
{0x628c,0x628c,0x188f},
{0x628e,0x628f,0x1890},
{0x6290,0x6290,0x43c0},
{0x6291,0x6291,0x4b5},
{0x6292,0x6292,0x4af},
{0x6293,0x6293,0x4b4},
{0x6294,0x6294,0x1892},
{0x6295,0x6295,0x4b3},
{0x6296,0x6296,0x4a5},
{0x6297,0x6297,0x4a4},
{0x6298,0x6298,0x4b1},
{0x629d,0x629d,0x3e96},
{0x62a4,0x62a4,0x3a69},
{0x62a6,0x62a6,0x3fc1},
{0x62a8,0x62a8,0x5e3},
{0x62a9,0x62a9,0x199e},
{0x62aa,0x62aa,0x1997},
{0x62ab,0x62ab,0x5de},
{0x62ac,0x62ac,0x5f1},
{0x62ad,0x62ad,0x1993},
{0x62ae,0x62ae,0x199a},
{0x62af,0x62af,0x199c},
{0x62b0,0x62b0,0x199f},
{0x62b1,0x62b1,0x5ec},
{0x62b3,0x62b3,0x199b},
{0x62b4,0x62b4,0x1994},
{0x62b5,0x62b5,0x5ea},
{0x62b6,0x62b6,0x1998},
{0x62b8,0x62b8,0x19a0},
{0x62b9,0x62b9,0x5db},
{0x62bb,0x62bb,0x199d},
{0x62bc,0x62bc,0x5e5},
{0x62bd,0x62bd,0x5e4},
{0x62be,0x62be,0x1996},
{0x62bf,0x62bf,0x5d9},
{0x62c2,0x62c2,0x5da},
{0x62c3,0x62c3,0x3d8f},
{0x62c4,0x62c4,0x5d8},
{0x62c5,0x62c5,0x418a},
{0x62c6,0x62c6,0x5f0},
{0x62c7,0x62c7,0x5e8},
{0x62c8,0x62c8,0x5e2},
{0x62c9,0x62c9,0x5d6},
{0x62ca,0x62ca,0x1999},
{0x62cb,0x62cb,0x5e1},
{0x62cc,0x62cc,0x5d7},
{0x62cd,0x62cd,0x5e9},
{0x62ce,0x62ce,0x5f2},
{0x62cf,0x62cf,0x1aee},
{0x62d0,0x62d0,0x5e6},
{0x62d1,0x62d1,0x1995},
{0x62d2,0x62d2,0x5dc},
{0x62d3,0x62d4,0x5df},
{0x62d5,0x62d5,0x418c},
{0x62d6,0x62d7,0x5ee},
{0x62d8,0x62d8,0x5ed},
{0x62d9,0x62d9,0x5e7},
{0x62da,0x62da,0x5eb},
{0x62db,0x62db,0x5dd},
{0x62dc,0x62dc,0x730},
{0x62df,0x62df,0x401c},
{0x62e5,0x62e5,0x463d},
{0x62eb,0x62eb,0x1af4},
{0x62ec,0x62ec,0x73c},
{0x62ed,0x62ed,0x734},
{0x62ee,0x62ee,0x736},
{0x62ef,0x62ef,0x73b},
{0x62f0,0x62f0,0x1b00},
{0x62f1,0x62f1,0x739},
{0x62f2,0x62f2,0x1ca2},
{0x62f3,0x62f3,0x8c3},
{0x62f4,0x62f4,0x73e},
{0x62f5,0x62f5,0x1af1},
{0x62f6,0x62f6,0x1af9},
{0x62f7,0x62f7,0x73a},
{0x62f8,0x62f8,0x1af8},
{0x62f9,0x62f9,0x1af5},
{0x62fa,0x62fa,0x1afd},
{0x62fb,0x62fb,0x1aff},
{0x62fc,0x62fc,0x733},
{0x62fd,0x62fd,0x737},
{0x62fe,0x62fe,0x73d},
{0x62ff,0x62ff,0x8c5},
{0x6300,0x6300,0x1afa},
{0x6301,0x6301,0x735},
{0x6302,0x6302,0x740},
{0x6303,0x6303,0x1af3},
{0x6307,0x6307,0x738},
{0x6308,0x6308,0x8c4},
{0x6309,0x6309,0x732},
{0x630b,0x630b,0x1af0},
{0x630c,0x630c,0x1af7},
{0x630d,0x630d,0x1aef},
{0x630e,0x630e,0x1af2},
{0x630f,0x630f,0x1af6},
{0x6310,0x6310,0x1ca3},
{0x6311,0x6311,0x73f},
{0x6313,0x6314,0x1afb},
{0x6315,0x6315,0x1afe},
{0x6316,0x6316,0x731},
{0x6318,0x6318,0x43c1},
{0x6328,0x6328,0x8d3},
{0x6329,0x6329,0x1caf},
{0x632a,0x632b,0x8d1},
{0x632c,0x632c,0x1ca5},
{0x632d,0x632d,0x1cb5},
{0x632e,0x632e,0x3edd},
{0x632f,0x632f,0x8c8},
{0x6331,0x6331,0x3a65},
{0x6332,0x6332,0x1eed},
{0x6333,0x6333,0x1cb7},
{0x6334,0x6334,0x1cb1},
{0x6335,0x6335,0x3f16},
{0x6336,0x6336,0x1ca8},
{0x6337,0x6337,0x3a63},
{0x6338,0x6338,0x1cba},
{0x6339,0x6339,0x1cab},
{0x633a,0x633a,0x8ce},
{0x633b,0x633b,0x1f04},
{0x633c,0x633c,0x1cae},
{0x633d,0x633d,0x8d0},
{0x633e,0x633e,0x8c7},
{0x6340,0x6340,0x1cbc},
{0x6341,0x6341,0x1cb0},
{0x6342,0x6342,0x8ca},
{0x6343,0x6343,0x1ca9},
{0x6344,0x6345,0x1ca6},
{0x6346,0x6346,0x8cb},
{0x6347,0x6347,0x1cb6},
{0x6348,0x6348,0x1cbd},
{0x6349,0x6349,0x8cd},
{0x634a,0x634a,0x1cad},
{0x634b,0x634b,0x1cac},
{0x634c,0x634c,0x8d5},
{0x634d,0x634d,0x8d4},
{0x634e,0x634e,0x8c6},
{0x634f,0x634f,0x8cc},
{0x6350,0x6350,0x8cf},
{0x6351,0x6351,0x1cb9},
{0x6354,0x6354,0x1cb3},
{0x6355,0x6355,0x8c9},
{0x6356,0x6356,0x1ca4},
{0x6357,0x6357,0x1cbb},
{0x6358,0x6358,0x1cb2},
{0x6359,0x6359,0x1cb4},
{0x635a,0x635a,0x1cb8},
{0x6364,0x6364,0x3fc7},
{0x6365,0x6365,0x1eee},
{0x6367,0x6367,0xaa1},
{0x6368,0x6368,0xab5},
{0x6369,0x6369,0xab4},
{0x636b,0x636b,0xaa9},
{0x636c,0x636c,0x418e},
{0x636d,0x636d,0x1f00},
{0x636e,0x636e,0x1efc},
{0x636f,0x636f,0x1ef9},
{0x6370,0x6370,0x1f0b},
{0x6371,0x6371,0xaa4},
{0x6372,0x6372,0xa9c},
{0x6375,0x6375,0x1efe},
{0x6376,0x6376,0xc69},
{0x6377,0x6377,0xaa0},
{0x6378,0x6378,0x1f06},
{0x6379,0x6379,0x4367},
{0x637a,0x637a,0xab6},
{0x637b,0x637b,0xab3},
{0x637c,0x637c,0x1f02},
{0x637d,0x637d,0x1ef1},
{0x637f,0x637f,0x3f4b},
{0x6380,0x6380,0xab2},
{0x6381,0x6381,0x1f08},
{0x6382,0x6382,0x1ef0},
{0x6383,0x6383,0xaa7},
{0x6384,0x6384,0xaab},
{0x6385,0x6385,0x1f07},
{0x6387,0x6387,0x1efa},
{0x6388,0x6388,0xaac},
{0x6389,0x6389,0xaa6},
{0x638a,0x638a,0x1eef},
{0x638b,0x638b,0x4188},
{0x638c,0x638c,0xc5b},
{0x638d,0x638d,0x1f0a},
{0x638e,0x638e,0x1ef8},
{0x638f,0x638f,0xab1},
{0x6390,0x6390,0x1efb},
{0x6391,0x6391,0x1f09},
{0x6392,0x6392,0xab0},
{0x6394,0x6394,0x214c},
{0x6396,0x6396,0xa9d},
{0x6397,0x6397,0x1ef6},
{0x6398,0x6398,0xaa2},
{0x6399,0x6399,0xaad},
{0x639b,0x639b,0xaa8},
{0x639c,0x639c,0x1eff},
{0x639d,0x639d,0x1ef5},
{0x639e,0x639e,0x1ef3},
{0x639f,0x639f,0x1f05},
{0x63a0,0x63a0,0xa9a},
{0x63a1,0x63a1,0xaae},
{0x63a2,0x63a2,0xa9e},
{0x63a3,0x63a3,0xc5a},
{0x63a4,0x63a4,0x1f03},
{0x63a5,0x63a5,0xa9f},
{0x63a7,0x63a7,0xa9b},
{0x63a8,0x63a8,0xaaa},
{0x63a9,0x63a9,0xaa5},
{0x63aa,0x63aa,0xaa3},
{0x63ab,0x63ab,0x1ef7},
{0x63ac,0x63ac,0xaaf},
{0x63ad,0x63ad,0x1ef4},
{0x63ae,0x63ae,0x1f01},
{0x63af,0x63af,0x1efd},
{0x63b0,0x63b0,0x214e},
{0x63b1,0x63b1,0x214d},
{0x63b9,0x63b9,0x3e9d},
{0x63bd,0x63bd,0x1ef2},
{0x63be,0x63be,0x215e},
{0x63c0,0x63c0,0xc5d},
{0x63c1,0x63c1,0x46a8},
{0x63c2,0x63c2,0x2164},
{0x63c3,0x63c3,0x2153},
{0x63c4,0x63c4,0x2161},
{0x63c5,0x63c5,0x23e6},
{0x63c6,0x63c6,0xc60},
{0x63c7,0x63c7,0x2165},
{0x63c8,0x63c8,0x2168},
{0x63c9,0x63c9,0xc5f},
{0x63ca,0x63ca,0x2156},
{0x63cb,0x63cb,0x2167},
{0x63cc,0x63cc,0x2166},
{0x63cd,0x63cd,0xc61},
{0x63ce,0x63ce,0x214f},
{0x63cf,0x63cf,0xc5c},
{0x63d0,0x63d0,0xc64},
{0x63d1,0x63d1,0x3a66},
{0x63d2,0x63d2,0xc62},
{0x63d3,0x63d3,0x2163},
{0x63d5,0x63d5,0x2159},
{0x63d6,0x63d6,0xc66},
{0x63d7,0x63d7,0x216a},
{0x63d8,0x63d8,0x2162},
{0x63d9,0x63d9,0x216b},
{0x63da,0x63da,0xc6e},
{0x63db,0x63db,0xc6c},
{0x63dc,0x63dc,0x2160},
{0x63dd,0x63dd,0x215f},
{0x63de,0x63de,0x3e60},
{0x63df,0x63df,0x215d},
{0x63e0,0x63e0,0x2157},
{0x63e1,0x63e1,0xc65},
{0x63e2,0x63e2,0x4641},
{0x63e3,0x63e3,0xc63},
{0x63e4,0x63e4,0x1caa},
{0x63e5,0x63e5,0x2150},
{0x63e6,0x63e6,0x489f},
{0x63e7,0x63e7,0x2404},
{0x63e8,0x63e8,0x2151},
{0x63e9,0x63e9,0xc5e},
{0x63ea,0x63ea,0xc6b},
{0x63eb,0x63eb,0x23e8},
{0x63ed,0x63ee,0xc67},
{0x63ef,0x63ef,0x2152},
{0x63f0,0x63f0,0x2169},
{0x63f1,0x63f1,0x23e7},
{0x63f2,0x63f2,0x215a},
{0x63f3,0x63f3,0x2155},
{0x63f4,0x63f4,0xc6a},
{0x63f5,0x63f5,0x215b},
{0x63f6,0x63f6,0x2158},
{0x63f8,0x63f8,0x4192},
{0x63f9,0x63f9,0xc6f},
{0x63fb,0x63fb,0x4642},
{0x63fc,0x63fc,0x3e9c},
{0x63fe,0x63fe,0x3e9e},
{0x6406,0x6406,0xe2a},
{0x6407,0x6407,0x4643},
{0x6409,0x6409,0x23eb},
{0x640a,0x640a,0x23fe},
{0x640b,0x640b,0x2403},
{0x640c,0x640c,0x23f7},
{0x640d,0x640d,0xe26},
{0x640e,0x640e,0x2408},
{0x640f,0x640f,0xe23},
{0x6410,0x6410,0x23e9},
{0x6412,0x6412,0x23ea},
{0x6413,0x6413,0xe1c},
{0x6414,0x6414,0xe25},
{0x6415,0x6415,0x23f1},
{0x6416,0x6417,0xe28},
{0x6418,0x6418,0x23f2},
{0x641a,0x641a,0x23ff},
{0x641b,0x641b,0x2405},
{0x641c,0x641c,0xe24},
{0x641e,0x641e,0xe1e},
{0x641f,0x641f,0x23f0},
{0x6420,0x6420,0x23ec},
{0x6421,0x6421,0x2407},
{0x6422,0x6423,0x23f5},
{0x6424,0x6424,0x23ed},
{0x6425,0x6425,0x2401},
{0x6426,0x6426,0x23f8},
{0x6427,0x6427,0x2402},
{0x6428,0x6428,0x23fa},
{0x642a,0x642a,0xe1f},
{0x642b,0x642b,0x2699},
{0x642c,0x642c,0xe22},
{0x642d,0x642d,0xe20},
{0x642e,0x642e,0x2406},
{0x642f,0x642f,0x23fd},
{0x6430,0x6430,0x23f9},
{0x6432,0x6432,0x45d9},
{0x6433,0x6433,0x23ee},
{0x6434,0x6434,0xfe4},
{0x6435,0x6435,0x23fc},
{0x6436,0x6436,0xe27},
{0x6437,0x6437,0x23f4},
{0x6438,0x6438,0x4191},
{0x6439,0x6439,0x23f3},
{0x643a,0x643a,0x419d},
{0x643b,0x643b,0x3a6b},
{0x643d,0x643d,0xe21},
{0x643e,0x643e,0xe1d},
{0x643f,0x643f,0x26ae},
{0x6440,0x6440,0x2400},
{0x6441,0x6441,0x23fb},
{0x6443,0x6443,0x23ef},
{0x644b,0x644b,0x26a9},
{0x644d,0x644d,0x269a},
{0x644e,0x644e,0x26a5},
{0x6450,0x6450,0x26ac},
{0x6451,0x6451,0xfe2},
{0x6452,0x6452,0xc6d},
{0x6453,0x6453,0x26aa},
{0x6454,0x6454,0xfdd},
{0x6458,0x6458,0xfdc},
{0x6459,0x6459,0x26b1},
{0x645a,0x645a,0x43c2},
{0x645b,0x645b,0x269b},
{0x645c,0x645c,0x26a8},
{0x645d,0x645d,0x269c},
{0x645e,0x645e,0x26a7},
{0x645f,0x645f,0xfe0},
{0x6460,0x6460,0x26ab},
{0x6461,0x6461,0x215c},
{0x6465,0x6465,0x26b2},
{0x6466,0x6466,0x26a3},
{0x6467,0x6467,0xfe3},
{0x6468,0x6468,0x28ee},
{0x6469,0x6469,0x1159},
{0x646b,0x646b,0x26b0},
{0x646c,0x646c,0x26af},
{0x646d,0x646d,0xfe5},
{0x646e,0x646e,0x28e1},
{0x646f,0x646f,0x115a},
{0x6470,0x6470,0x28e2},
{0x6471,0x6471,0x3a5b},
{0x6472,0x6473,0x269f},
{0x6474,0x6474,0x269d},
{0x6475,0x6475,0x26a2},
{0x6476,0x6476,0x269e},
{0x6477,0x6477,0x26b3},
{0x6478,0x6478,0xfdf},
{0x6479,0x6479,0x115b},
{0x647a,0x647a,0xfe1},
{0x647b,0x647b,0xfe6},
{0x647c,0x647c,0x436c},
{0x647d,0x647d,0x26a1},
{0x647f,0x647f,0x26ad},
{0x6482,0x6482,0x26a6},
{0x6485,0x6485,0x28e5},
{0x6487,0x6487,0xfdb},
{0x6488,0x6488,0x115e},
{0x6489,0x6489,0x2b54},
{0x648a,0x648a,0x28ea},
{0x648b,0x648b,0x28e9},
{0x648c,0x648c,0x28eb},
{0x648d,0x648d,0x4323},
{0x648f,0x648f,0x28e8},
{0x6490,0x6490,0x115f},
{0x6491,0x6491,0x43c3},
{0x6492,0x6492,0x1165},
{0x6493,0x6493,0x1162},
{0x6495,0x6495,0x1163},
{0x6496,0x6496,0x28e3},
{0x6497,0x6497,0x28e6},
{0x6498,0x6498,0x28f0},
{0x6499,0x6499,0x116b},
{0x649a,0x649a,0x1169},
{0x649c,0x649c,0x28e7},
{0x649d,0x649d,0x2154},
{0x649e,0x649e,0x115c},
{0x649f,0x649f,0x28ed},
{0x64a0,0x64a0,0x28e4},
{0x64a2,0x64a2,0x116c},
{0x64a3,0x64a3,0x28ec},
{0x64a4,0x64a4,0xfde},
{0x64a5,0x64a5,0x1161},
{0x64a6,0x64a6,0x26a4},
{0x64a9,0x64a9,0x1164},
{0x64ab,0x64ab,0x1168},
{0x64ac,0x64ac,0x116a},
{0x64ad,0x64ad,0x1167},
{0x64ae,0x64ae,0x1166},
{0x64af,0x64af,0x42e1},
{0x64b0,0x64b0,0x1160},
{0x64b1,0x64b1,0x28ef},
{0x64b2,0x64b2,0x115d},
{0x64b3,0x64b3,0x116d},
{0x64b4,0x64b4,0x4340},
{0x64b6,0x64b6,0x3a64},
{0x64bb,0x64bc,0x12bb},
{0x64bd,0x64bd,0x2b53},
{0x64be,0x64be,0x12c5},
{0x64bf,0x64bf,0x12c2},
{0x64c0,0x64c0,0x4645},
{0x64c1,0x64c1,0x12b9},
{0x64c2,0x64c2,0x12c0},
{0x64c3,0x64c3,0x2b55},
{0x64c4,0x64c4,0x12be},
{0x64c5,0x64c5,0x12b8},
{0x64c7,0x64c7,0x12bf},
{0x64c9,0x64c9,0x2b52},
{0x64ca,0x64ca,0x13d8},
{0x64cb,0x64cb,0x12ba},
{0x64cd,0x64cd,0x12c1},
{0x64ce,0x64ce,0x13d7},
{0x64cf,0x64cf,0x2b51},
{0x64d0,0x64d0,0x2b50},
{0x64d2,0x64d2,0x12c3},
{0x64d3,0x64d3,0x3f03},
{0x64d4,0x64d4,0x12c4},
{0x64d6,0x64d6,0x2b4f},
{0x64d7,0x64d7,0x2b4e},
{0x64d8,0x64d8,0x13d9},
{0x64d9,0x64d9,0x2b58},
{0x64da,0x64da,0x12bd},
{0x64db,0x64db,0x2b56},
{0x64dd,0x64dd,0x431e},
{0x64e0,0x64e0,0x13da},
{0x64e1,0x64e1,0x4199},
{0x64e2,0x64e2,0x13df},
{0x64e3,0x64e3,0x2d6a},
{0x64e4,0x64e4,0x2d6c},
{0x64e5,0x64e5,0x419a},
{0x64e6,0x64e6,0x13dc},
{0x64e7,0x64e7,0x37a8},
{0x64e8,0x64e8,0x2d6d},
{0x64e9,0x64e9,0x2d69},
{0x64ea,0x64ea,0x434a},
{0x64eb,0x64eb,0x2d6b},
{0x64ec,0x64ec,0x13dd},
{0x64ed,0x64ed,0x13e0},
{0x64ef,0x64ef,0x2d68},
{0x64f0,0x64f0,0x13db},
{0x64f1,0x64f1,0x13de},
{0x64f2,0x64f2,0x14d3},
{0x64f3,0x64f3,0x2b57},
{0x64f4,0x64f4,0x14d2},
{0x64f7,0x64f7,0x14d8},
{0x64f8,0x64f8,0x2f2b},
{0x64fa,0x64fb,0x14d6},
{0x64fc,0x64fc,0x2f2e},
{0x64fd,0x64fd,0x2f2a},
{0x64fe,0x64fe,0x14d4},
{0x64ff,0x64ff,0x2f28},
{0x6500,0x6500,0x1582},
{0x6501,0x6501,0x2f2c},
{0x6503,0x6503,0x2f2d},
{0x6504,0x6504,0x2f29},
{0x6506,0x6506,0x14d5},
{0x6507,0x6507,0x3090},
{0x6509,0x6509,0x3093},
{0x650a,0x650a,0x4536},
{0x650c,0x650c,0x3094},
{0x650d,0x650d,0x3092},
{0x650e,0x650e,0x3095},
{0x650f,0x650f,0x1583},
{0x6510,0x6510,0x3091},
{0x6511,0x6511,0x3aa9},
{0x6513,0x6513,0x31d7},
{0x6514,0x6514,0x1617},
{0x6515,0x6515,0x31d6},
{0x6516,0x6516,0x31d5},
{0x6517,0x6517,0x31d4},
{0x6518,0x6518,0x1616},
{0x6519,0x6519,0x1618},
{0x651b,0x651b,0x32d2},
{0x651c,0x651c,0x1673},
{0x651d,0x651d,0x1672},
{0x651e,0x651e,0x4187},
{0x651f,0x651f,0x3ec0},
{0x6520,0x6520,0x3382},
{0x6521,0x6521,0x3381},
{0x6522,0x6522,0x3384},
{0x6523,0x6523,0x16f9},
{0x6524,0x6524,0x16c5},
{0x6525,0x6525,0x341b},
{0x6526,0x6526,0x3383},
{0x6529,0x6529,0x341a},
{0x652a,0x652a,0x16fb},
{0x652b,0x652b,0x16fa},
{0x652c,0x652c,0x1721},
{0x652d,0x652d,0x3484},
{0x652e,0x652e,0x34ca},
{0x652f,0x652f,0x2d5},
{0x6530,0x6530,0x3e62},
{0x6532,0x6532,0x216c},
{0x6533,0x6533,0x2b59},
{0x6534,0x6534,0x22c},
{0x6535,0x6535,0x44ed},
{0x6536,0x6536,0x3d7},
{0x6537,0x6537,0x17f6},
{0x6538,0x6538,0x4b9},
{0x6539,0x6539,0x4b7},
{0x653b,0x653b,0x4b8},
{0x653d,0x653d,0x19a1},
{0x653e,0x653e,0x5f3},
{0x653f,0x653f,0x741},
{0x6541,0x6541,0x1b01},
{0x6543,0x6543,0x1b02},
{0x6545,0x6545,0x742},
{0x6546,0x6546,0x1cbf},
{0x6548,0x6549,0x8d6},
{0x654a,0x654a,0x1cbe},
{0x654d,0x654d,0x419f},
{0x654f,0x654f,0xabd},
{0x6551,0x6551,0xab9},
{0x6553,0x6553,0x1f0c},
{0x6554,0x6554,0xac0},
{0x6555,0x6555,0xabf},
{0x6556,0x6556,0xab8},
{0x6557,0x6557,0xabb},
{0x6558,0x6558,0xabe},
{0x6559,0x6559,0xaba},
{0x655c,0x655c,0x2170},
{0x655d,0x655d,0xab7},
{0x655e,0x655e,0xc70},
{0x655f,0x655f,0x41a5},
{0x6562,0x6563,0xc72},
{0x6564,0x6564,0x216f},
{0x6565,0x6565,0x2172},
{0x6566,0x6566,0xc71},
{0x6567,0x6567,0x216d},
{0x6568,0x6568,0x2171},
{0x656a,0x656a,0x216e},
{0x656b,0x656b,0x3a6c},
{0x656c,0x656c,0xe2b},
{0x656d,0x656d,0x41a4},
{0x656f,0x656f,0x2409},
{0x6572,0x6572,0xfe7},
{0x6573,0x6573,0x26b4},
{0x6574,0x6574,0x12c6},
{0x6575,0x6575,0x116e},
{0x6576,0x6576,0x28f1},
{0x6577,0x6578,0x116f},
{0x6579,0x6579,0x28f3},
{0x657a,0x657a,0x28f2},
{0x657b,0x657b,0x28f4},
{0x657c,0x657c,0x2b5b},
{0x657f,0x657f,0x2b5a},
{0x6580,0x6580,0x2d6f},
{0x6581,0x6581,0x2d6e},
{0x6582,0x6583,0x13e1},
{0x6584,0x6584,0x3096},
{0x6585,0x6585,0x41a3},
{0x6586,0x6586,0x4648},
{0x6587,0x6587,0x2d6},
{0x6588,0x6588,0x410b},
{0x6589,0x6589,0x4554},
{0x658c,0x658c,0x2173},
{0x6590,0x6590,0xc75},
{0x6591,0x6591,0xc74},
{0x6592,0x6592,0x240a},
{0x6594,0x6594,0x2f2f},
{0x6595,0x6595,0x1674},
{0x6596,0x6596,0x341c},
{0x6597,0x6597,0x2d7},
{0x6599,0x6599,0x8d8},
{0x659b,0x659b,0xac2},
{0x659c,0x659c,0xac1},
{0x659d,0x659e,0x2174},
{0x659f,0x659f,0xe2c},
{0x65a0,0x65a0,0x26b5},
{0x65a1,0x65a1,0xfe8},
{0x65a2,0x65a2,0x2b5c},
{0x65a4,0x65a4,0x2d8},
{0x65a5,0x65a5,0x346},
{0x65a7,0x65a7,0x5f4},
{0x65a8,0x65a8,0x19a2},
{0x65aa,0x65aa,0x1b03},
{0x65ab,0x65ab,0x743},
{0x65ac,0x65ac,0xac3},
{0x65ae,0x65ae,0x2176},
{0x65af,0x65af,0xc76},
{0x65b0,0x65b0,0xe2d},
{0x65b2,0x65b3,0x28f5},
{0x65b5,0x65b5,0x41a7},
{0x65b6,0x65b6,0x2d70},
{0x65b7,0x65b7,0x14d9},
{0x65b8,0x65b8,0x34cb},
{0x65b9,0x65b9,0x2d9},
{0x65bb,0x65bb,0x19a3},
{0x65bc,0x65bc,0x5f5},
{0x65bd,0x65bd,0x744},
{0x65be,0x65be,0x3e3b},
{0x65bf,0x65bf,0x1b04},
{0x65c1,0x65c1,0x8d9},
{0x65c2,0x65c2,0x1cc3},
{0x65c3,0x65c4,0x1cc1},
{0x65c5,0x65c5,0x8da},
{0x65c6,0x65c6,0x1cc0},
{0x65cb,0x65cc,0xac5},
{0x65cd,0x65cd,0x1f0d},
{0x65ce,0x65ce,0xac7},
{0x65cf,0x65cf,0xac4},
{0x65d0,0x65d0,0x2177},
{0x65d1,0x65d1,0x41aa},
{0x65d2,0x65d2,0x2178},
{0x65d3,0x65d3,0x240b},
{0x65d4,0x65d4,0x3ba8},
{0x65d6,0x65d6,0xfea},
{0x65d7,0x65d7,0xfe9},
{0x65da,0x65da,0x2d71},
{0x65db,0x65db,0x2f30},
{0x65dd,0x65dd,0x3098},
{0x65de,0x65de,0x3097},
{0x65df,0x65df,0x31d8},
{0x65e0,0x65e0,0x22d},
{0x65e1,0x65e1,0x1789},
{0x65e2,0x65e2,0x745},
{0x65e3,0x65e3,0x41ae},
{0x65e5,0x65e5,0x2da},
{0x65e6,0x65e6,0x347},
{0x65e8,0x65e8,0x3d9},
{0x65e9,0x65e9,0x3d8},
{0x65ec,0x65ed,0x3da},
{0x65ee,0x65ee,0x17f8},
{0x65ef,0x65ef,0x17f7},
{0x65f0,0x65f0,0x189e},
{0x65f1,0x65f1,0x4ba},
{0x65f2,0x65f2,0x18a1},
{0x65f3,0x65f3,0x18a0},
{0x65f4,0x65f4,0x189f},
{0x65f5,0x65f5,0x18a2},
{0x65fa,0x65fa,0x5f6},
{0x65fb,0x65fb,0x19a9},
{0x65fc,0x65fc,0x19a5},
{0x65fd,0x65fd,0x19ae},
{0x65ff,0x65ff,0x464a},
{0x6600,0x6600,0x5fd},
{0x6602,0x6602,0x5fb},
{0x6603,0x6603,0x19aa},
{0x6604,0x6604,0x19a6},
{0x6605,0x6605,0x19ad},
{0x6606,0x6606,0x5fa},
{0x6607,0x6607,0x601},
{0x6608,0x6608,0x19a8},
{0x6609,0x6609,0x19a4},
{0x660a,0x660a,0x600},
{0x660b,0x660b,0x19ab},
{0x660c,0x660c,0x5f9},
{0x660d,0x660d,0x19ac},
{0x660e,0x660e,0x5fc},
{0x660f,0x660f,0x5fe},
{0x6610,0x6610,0x19b0},
{0x6611,0x6611,0x19af},
{0x6612,0x6612,0x19a7},
{0x6613,0x6613,0x5f8},
{0x6614,0x6614,0x5f7},
{0x6615,0x6615,0x5ff},
{0x6618,0x6618,0x41b1},
{0x661c,0x661c,0x1b09},
{0x661d,0x661d,0x1b0f},
{0x661e,0x661e,0x3a93},
{0x661f,0x661f,0x74b},
{0x6620,0x6620,0x748},
{0x6621,0x6621,0x1b06},
{0x6622,0x6622,0x1b0b},
{0x6623,0x6623,0x4295},
{0x6624,0x6624,0x74e},
{0x6625,0x6625,0x746},
{0x6626,0x6626,0x1b0a},
{0x6627,0x6627,0x749},
{0x6628,0x6628,0x74c},
{0x662b,0x662b,0x1b0d},
{0x662d,0x662d,0x747},
{0x662e,0x662e,0x1b12},
{0x662f,0x662f,0x74a},
{0x6630,0x6630,0x3a8d},
{0x6631,0x6631,0x74d},
{0x6632,0x6632,0x1b07},
{0x6633,0x6633,0x1b0c},
{0x6634,0x6634,0x1b10},
{0x6635,0x6635,0x1b08},
{0x6636,0x6636,0x1b05},
{0x6639,0x6639,0x1b11},
{0x663a,0x663a,0x1b0e},
{0x6641,0x6641,0x8e2},
{0x6642,0x6642,0x8db},
{0x6643,0x6643,0x8de},
{0x6644,0x6644,0x41b4},
{0x6645,0x6645,0x8e1},
{0x6647,0x6647,0x1cc6},
{0x6648,0x6648,0x3d99},
{0x6649,0x6649,0x8dc},
{0x664a,0x664a,0x1cc4},
{0x664b,0x664b,0x41b7},
{0x664c,0x664c,0x8e0},
{0x664f,0x664f,0x8dd},
{0x6651,0x6651,0x1cc7},
{0x6652,0x6652,0x8df},
{0x6653,0x6653,0x464b},
{0x6657,0x6657,0x409b},
{0x6659,0x6659,0x1f11},
{0x665a,0x665a,0xac9},
{0x665b,0x665b,0x1f10},
{0x665c,0x665c,0x1f12},
{0x665d,0x665d,0xac8},
{0x665e,0x665e,0xacd},
{0x665f,0x665f,0x1cc5},
{0x6661,0x6661,0x1f0f},
{0x6662,0x6662,0x1f13},
{0x6663,0x6663,0x3a90},
{0x6664,0x6664,0xaca},
{0x6665,0x6665,0x1f0e},
{0x6666,0x6666,0xacc},
{0x6667,0x6667,0x41b9},
{0x6668,0x6668,0xacb},
{0x666a,0x666a,0x217f},
{0x666b,0x666b,0x3a8b},
{0x666c,0x666c,0x217a},
{0x666e,0x666e,0xc77},
{0x666f,0x666f,0xc7b},
{0x6670,0x6670,0xc78},
{0x6671,0x6671,0x217d},
{0x6672,0x6672,0x2180},
{0x6673,0x6673,0x41bb},
{0x6674,0x6674,0xc79},
{0x6676,0x6676,0xc7a},
{0x6677,0x6677,0xc7f},
{0x6678,0x6678,0x2414},
{0x6679,0x6679,0x217e},
{0x667a,0x667a,0xc7d},
{0x667b,0x667b,0x217b},
{0x667c,0x667c,0x2179},
{0x667d,0x667d,0x469b},
{0x667e,0x667e,0xc7e},
{0x6680,0x6680,0x217c},
{0x6684,0x6684,0xe33},
{0x6685,0x6685,0x4162},
{0x6686,0x6686,0x240c},
{0x6687,0x6688,0xe30},
{0x6689,0x6689,0xe2f},
{0x668a,0x668a,0x2411},
{0x668b,0x668b,0x2410},
{0x668c,0x668c,0x240d},
{0x668d,0x668d,0xe35},
{0x668e,0x668e,0x3a89},
{0x6690,0x6690,0x240f},
{0x6691,0x6691,0xc7c},
{0x6692,0x6692,0x464d},
{0x6694,0x6694,0x2413},
{0x6695,0x6695,0x240e},
{0x6696,0x6696,0xe32},
{0x6697,0x6697,0xe2e},
{0x6698,0x6698,0xe34},
{0x6699,0x6699,0x2412},
{0x669a,0x669a,0x3d3d},
{0x669d,0x669d,0xfed},
{0x669f,0x669f,0x26b8},
{0x66a0,0x66a0,0x26b7},
{0x66a1,0x66a1,0x26b6},
{0x66a2,0x66a2,0xfeb},
{0x66a4,0x66a4,0x40ba},
{0x66a8,0x66a8,0xfec},
{0x66a9,0x66a9,0x28f9},
{0x66aa,0x66aa,0x28fc},
{0x66ab,0x66ab,0x1172},
{0x66ad,0x66ad,0x4948},
{0x66ae,0x66ae,0x1171},
{0x66af,0x66af,0x28fd},
{0x66b0,0x66b0,0x28f8},
{0x66b1,0x66b1,0x1174},
{0x66b2,0x66b2,0x28fa},
{0x66b3,0x66b3,0x40b5},
{0x66b4,0x66b4,0x1173},
{0x66b5,0x66b5,0x28f7},
{0x66b6,0x66b6,0x3e13},
{0x66b7,0x66b7,0x28fb},
{0x66b8,0x66b8,0x12cc},
{0x66b9,0x66b9,0x12c9},
{0x66ba,0x66ba,0x2b65},
{0x66bb,0x66bb,0x2b64},
{0x66bd,0x66bd,0x2b63},
{0x66be,0x66be,0x2b5e},
{0x66bf,0x66bf,0x3d9a},
{0x66c0,0x66c0,0x2b5f},
{0x66c4,0x66c4,0x12ca},
{0x66c6,0x66c6,0x12c7},
{0x66c7,0x66c7,0x12cb},
{0x66c8,0x66c8,0x2b5d},
{0x66c9,0x66c9,0x12c8},
{0x66ca,0x66cb,0x2b60},
{0x66cc,0x66cc,0x2b66},
{0x66cd,0x66cd,0x40b8},
{0x66ce,0x66ce,0x3a77},
{0x66cf,0x66cf,0x2b62},
{0x66d2,0x66d2,0x2d72},
{0x66d6,0x66d6,0x13e4},
{0x66d8,0x66d8,0x2f33},
{0x66d9,0x66d9,0x13e3},
{0x66da,0x66db,0x2f31},
{0x66dc,0x66dc,0x14da},
{0x66dd,0x66dd,0x1585},
{0x66de,0x66de,0x3099},
{0x66e0,0x66e0,0x1584},
{0x66e3,0x66e4,0x31da},
{0x66e6,0x66e6,0x1619},
{0x66e8,0x66e8,0x31d9},
{0x66e9,0x66e9,0x1675},
{0x66eb,0x66eb,0x341d},
{0x66ec,0x66ec,0x16fc},
{0x66ed,0x66ee,0x3485},
{0x66f0,0x66f0,0x2db},
{0x66f1,0x66f1,0x3e84},
{0x66f2,0x66f3,0x3dc},
{0x66f4,0x66f4,0x4bb},
{0x66f6,0x66f6,0x19b1},
{0x66f7,0x66f7,0x74f},
{0x66f8,0x66f8,0x8e3},
{0x66f9,0x66f9,0xace},
{0x66fc,0x66fc,0xa1f},
{0x66fe,0x66ff,0xc80},
{0x6700,0x6700,0xbf0},
{0x6701,0x6701,0x2181},
{0x6702,0x6702,0x41c4},
{0x6703,0x6703,0xe36},
{0x6704,0x6704,0x26ba},
{0x6705,0x6705,0x26b9},
{0x6708,0x6708,0x2dc},
{0x6709,0x6709,0x3de},
{0x670a,0x670a,0x19b2},
{0x670b,0x670b,0x603},
{0x670c,0x670c,0x3fe2},
{0x670d,0x670d,0x602},
{0x670e,0x670e,0x3da1},
{0x670f,0x6710,0x1b13},
{0x6712,0x6713,0x1cc8},
{0x6714,0x6715,0x8e4},
{0x6716,0x6716,0x464f},
{0x6717,0x6717,0x8e6},
{0x6718,0x6718,0x1f14},
{0x671b,0x671b,0xad0},
{0x671d,0x671d,0xc83},
{0x671e,0x671e,0x3d8b},
{0x671f,0x671f,0xc82},
{0x6720,0x6720,0x2415},
{0x6721,0x6721,0x2522},
{0x6722,0x6722,0x26bb},
{0x6723,0x6723,0x2b67},
{0x6725,0x6725,0x379f},
{0x6726,0x6726,0x14db},
{0x6727,0x6727,0x161a},
{0x6728,0x6728,0x2dd},
{0x672a,0x672b,0x34a},
{0x672c,0x672c,0x349},
{0x672d,0x672d,0x34c},
{0x672e,0x672e,0x348},
{0x6731,0x6731,0x3e1},
{0x6733,0x6733,0x1800},
{0x6734,0x6734,0x3e0},
{0x6735,0x6735,0x3e2},
{0x6736,0x6736,0x3e41},
{0x6738,0x6738,0x17fb},
{0x6739,0x6739,0x17fa},
{0x673a,0x673a,0x17fd},
{0x673b,0x673b,0x17fc},
{0x673c,0x673c,0x17ff},
{0x673d,0x673d,0x3df},
{0x673e,0x673e,0x17f9},
{0x673f,0x673f,0x17fe},
{0x6744,0x6744,0x401b},
{0x6745,0x6745,0x18a3},
{0x6746,0x6746,0x4c5},
{0x6747,0x6747,0x18a4},
{0x6748,0x6748,0x18a8},
{0x6749,0x6749,0x4c4},
{0x674b,0x674b,0x18ac},
{0x674c,0x674c,0x18a7},
{0x674d,0x674d,0x18aa},
{0x674e,0x6751,0x4bd},
{0x6753,0x6753,0x4c7},
{0x6755,0x6755,0x18a6},
{0x6756,0x6756,0x4c2},
{0x6757,0x6757,0x4c8},
{0x6759,0x6759,0x18a5},
{0x675a,0x675a,0x18ab},
{0x675c,0x675c,0x4c1},
{0x675d,0x675d,0x18a9},
{0x675e,0x675e,0x4c3},
{0x675f,0x675f,0x4bc},
{0x6760,0x6760,0x4c6},
{0x6761,0x6761,0x3d66},
{0x6762,0x6762,0x41cb},
{0x6767,0x6767,0x41ca},
{0x676a,0x676a,0x618},
{0x676c,0x676c,0x19b4},
{0x676d,0x676d,0x604},
{0x676f,0x6770,0x60e},
{0x6771,0x6771,0x607},
{0x6772,0x6772,0x619},
{0x6773,0x6773,0x609},
{0x6774,0x6774,0x19bc},
{0x6775,0x6775,0x614},
{0x6776,0x6776,0x19b7},
{0x6777,0x6777,0x60a},
{0x6778,0x6779,0x19c6},
{0x677a,0x677a,0x19bf},
{0x677b,0x677b,0x19b8},
{0x677c,0x677c,0x617},
{0x677d,0x677d,0x19c4},
{0x677e,0x677e,0x612},
{0x677f,0x677f,0x610},
{0x6781,0x6781,0x19c5},
{0x6783,0x6783,0x19c3},
{0x6784,0x6784,0x19bb},
{0x6785,0x6785,0x19b3},
{0x6786,0x6786,0x19ba},
{0x6787,0x6787,0x60b},
{0x6789,0x6789,0x611},
{0x678b,0x678b,0x605},
{0x678c,0x678c,0x19be},
{0x678d,0x678d,0x19bd},
{0x678e,0x678e,0x19b5},
{0x678f,0x678f,0x46c1},
{0x6790,0x6790,0x613},
{0x6791,0x6791,0x19c1},
{0x6792,0x6792,0x19b6},
{0x6793,0x6793,0x616},
{0x6794,0x6794,0x19c8},
{0x6795,0x6795,0x606},
{0x6797,0x6797,0x60d},
{0x6798,0x6798,0x19b9},
{0x6799,0x6799,0x19c2},
{0x679a,0x679a,0x615},
{0x679c,0x679c,0x608},
{0x679d,0x679d,0x60c},
{0x679f,0x679f,0x19c0},
{0x67a0,0x67a0,0x3d7a},
{0x67a4,0x67a4,0x4651},
{0x67ac,0x67ac,0x41e3},
{0x67ae,0x67ae,0x1b34},
{0x67af,0x67af,0x757},
{0x67b0,0x67b0,0x764},
{0x67b1,0x67b1,0x41f4},
{0x67b2,0x67b2,0x1b2f},
{0x67b3,0x67b3,0x1b25},
{0x67b4,0x67b4,0x75d},
{0x67b5,0x67b5,0x1b23},
{0x67b6,0x67b6,0x756},
{0x67b7,0x67b7,0x1b1e},
{0x67b8,0x67b8,0x760},
{0x67b9,0x67b9,0x1b2b},
{0x67ba,0x67ba,0x1b18},
{0x67bb,0x67bb,0x1b1a},
{0x67bf,0x67bf,0x37fa},
{0x67c0,0x67c0,0x1b1d},
{0x67c1,0x67c1,0x1b15},
{0x67c2,0x67c2,0x1b2a},
{0x67c3,0x67c3,0x1b3a},
{0x67c4,0x67c4,0x75b},
{0x67c5,0x67c5,0x1b1f},
{0x67c6,0x67c6,0x1b31},
{0x67c8,0x67c8,0x1b17},
{0x67c9,0x67ca,0x1b38},
{0x67cb,0x67cb,0x1b3c},
{0x67cc,0x67cc,0x1b33},
{0x67cd,0x67cd,0x1b24},
{0x67ce,0x67ce,0x1b2c},
{0x67cf,0x67cf,0x761},
{0x67d0,0x67d0,0x754},
{0x67d1,0x67d1,0x75c},
{0x67d2,0x67d2,0x768},
{0x67d3,0x67d3,0x751},
{0x67d4,0x67d4,0x753},
{0x67d6,0x67d6,0x3b58},
{0x67d7,0x67d7,0x41ce},
{0x67d8,0x67d8,0x1b1c},
{0x67d9,0x67d9,0x765},
{0x67da,0x67da,0x75e},
{0x67db,0x67db,0x1b36},
{0x67dc,0x67dc,0x1b19},
{0x67dd,0x67dd,0x767},
{0x67de,0x67de,0x762},
{0x67df,0x67df,0x1b22},
{0x67e2,0x67e2,0x766},
{0x67e3,0x67e3,0x1b29},
{0x67e4,0x67e4,0x1b21},
{0x67e5,0x67e5,0x75f},
{0x67e6,0x67e6,0x1b35},
{0x67e7,0x67e7,0x1b2d},
{0x67e9,0x67e9,0x759},
{0x67ea,0x67ea,0x1b3b},
{0x67eb,0x67eb,0x1b20},
{0x67ec,0x67ec,0x755},
{0x67ed,0x67ed,0x1b32},
{0x67ee,0x67ee,0x1b28},
{0x67ef,0x67ef,0x75a},
{0x67f0,0x67f0,0x1b2e},
{0x67f1,0x67f1,0x752},
{0x67f2,0x67f2,0x1b16},
{0x67f3,0x67f3,0x763},
{0x67f4,0x67f4,0x8f5},
{0x67f5,0x67f5,0x758},
{0x67f6,0x67f6,0x1b27},
{0x67f7,0x67f7,0x1b26},
{0x67f8,0x67f8,0x1b1b},
{0x67f9,0x67f9,0x3996},
{0x67fa,0x67fa,0x1b37},
{0x67fc,0x67fc,0x1b30},
{0x67fe,0x67fe,0x4555},
{0x67ff,0x67ff,0x750},
{0x6800,0x6800,0x43c7},
{0x6801,0x6801,0x41d3},
{0x6802,0x6802,0x3fc8},
{0x6803,0x6803,0x3d6a},
{0x6804,0x6804,0x4556},
{0x680d,0x680d,0x4281},
{0x6810,0x6810,0x399c},
{0x6812,0x6812,0x1cdd},
{0x6813,0x6813,0x8fc},
{0x6814,0x6814,0x1cde},
{0x6816,0x6816,0x1cd2},
{0x6817,0x6817,0x8f1},
{0x6818,0x6818,0x8fd},
{0x681a,0x681a,0x1ccb},
{0x681b,0x681b,0x4072},
{0x681c,0x681c,0x1cd4},
{0x681d,0x681d,0x1cdc},
{0x681e,0x681e,0x46c2},
{0x681f,0x681f,0x1cca},
{0x6820,0x6820,0x1ce5},
{0x6821,0x6821,0x8e7},
{0x6822,0x6822,0x41d0},
{0x6825,0x6825,0x1ce4},
{0x6826,0x6826,0x1cdf},
{0x6828,0x6828,0x1ce0},
{0x6829,0x6829,0x8ef},
{0x682a,0x682a,0x8fa},
{0x682b,0x682b,0x1cd6},
{0x682d,0x682d,0x1cd7},
{0x682e,0x682e,0x1ce1},
{0x682f,0x682f,0x1cd8},
{0x6831,0x6831,0x1cd3},
{0x6832,0x6833,0x1ccd},
{0x6834,0x6834,0x1cdb},
{0x6835,0x6835,0x1cd5},
{0x6836,0x6836,0x3e2e},
{0x6837,0x6837,0x421d},
{0x6838,0x6838,0x8e8},
{0x6839,0x6839,0x8ec},
{0x683a,0x683a,0x1ce3},
{0x683b,0x683b,0x1ccf},
{0x683c,0x683c,0x8f8},
{0x683d,0x683d,0x8f4},
{0x683e,0x683e,0x4147},
{0x6840,0x6840,0x8f7},
{0x6841,0x6841,0x8fe},
{0x6842,0x6842,0x8ed},
{0x6843,0x6843,0x8f9},
{0x6844,0x6844,0x1cda},
{0x6845,0x6845,0x8fb},
{0x6846,0x6846,0x8ea},
{0x6847,0x6847,0x3beb},
{0x6848,0x6848,0x8e9},
{0x6849,0x6849,0x1ccc},
{0x684a,0x684a,0x4653},
{0x684b,0x684b,0x1cd0},
{0x684c,0x684c,0x8f2},
{0x684d,0x684d,0x1ce2},
{0x684e,0x684e,0x1cd9},
{0x684f,0x684f,0x1cd1},
{0x6850,0x6850,0x8f6},
{0x6851,0x6851,0x8f3},
{0x6853,0x6853,0x8eb},
{0x6854,0x6854,0x8ee},
{0x6855,0x6855,0x3d90},
{0x6856,0x6856,0x3814},
{0x685d,0x685d,0x41d6},
{0x6865,0x6865,0x4557},
{0x686b,0x686b,0x1f29},
{0x686d,0x686e,0x1f19},
{0x686f,0x686f,0x1f1e},
{0x6871,0x6871,0x1f2d},
{0x6872,0x6872,0x1f2a},
{0x6874,0x6874,0x1f23},
{0x6875,0x6875,0x1f22},
{0x6876,0x6876,0xad7},
{0x6877,0x6877,0x1f26},
{0x6878,0x6878,0x1f35},
{0x6879,0x6879,0x1f15},
{0x687b,0x687b,0x1f36},
{0x687c,0x687c,0x1f28},
{0x687d,0x687d,0x1f3a},
{0x687e,0x687e,0x1f2e},
{0x687f,0x687f,0xad6},
{0x6880,0x6880,0x1f2c},
{0x6881,0x6881,0xad1},
{0x6882,0x6882,0xae6},
{0x6883,0x6883,0xadc},
{0x6884,0x6884,0x4654},
{0x6885,0x6885,0xae0},
{0x6886,0x6886,0xadf},
{0x6887,0x6887,0x1f16},
{0x6888,0x6888,0x46b7},
{0x6889,0x6889,0x1f33},
{0x688a,0x688a,0x1f39},
{0x688b,0x688b,0x1f31},
{0x688c,0x688c,0x1f38},
{0x688f,0x688f,0x1f25},
{0x6890,0x6890,0x1f17},
{0x6891,0x6891,0x1f37},
{0x6892,0x6892,0x1f27},
{0x6893,0x6893,0xad4},
{0x6894,0x6894,0xae1},
{0x6896,0x6896,0x1f30},
{0x6897,0x6897,0xada},
{0x6898,0x6898,0x3aaa},
{0x689b,0x689b,0x1f2f},
{0x689c,0x689c,0x1f18},
{0x689d,0x689d,0xae2},
{0x689f,0x689f,0xae4},
{0x68a0,0x68a0,0x1f32},
{0x68a1,0x68a1,0xae5},
{0x68a2,0x68a2,0xad3},
{0x68a3,0x68a3,0x1f1f},
{0x68a4,0x68a4,0x1f34},
{0x68a6,0x68a6,0x40e4},
{0x68a7,0x68a7,0xad9},
{0x68a8,0x68a8,0xae3},
{0x68a9,0x68a9,0x1f21},
{0x68aa,0x68aa,0x1f2b},
{0x68ab,0x68ab,0x1f1c},
{0x68ac,0x68ac,0x1f20},
{0x68ad,0x68ad,0xade},
{0x68ae,0x68ae,0x1f1b},
{0x68af,0x68af,0xad2},
{0x68b0,0x68b0,0xadb},
{0x68b1,0x68b1,0xad8},
{0x68b2,0x68b2,0x1f24},
{0x68b3,0x68b3,0x8f0},
{0x68b4,0x68b4,0x2198},
{0x68b5,0x68b5,0xad5},
{0x68b6,0x68b6,0x3a9e},
{0x68b9,0x68b9,0x3d14},
{0x68bd,0x68bd,0x427c},
{0x68c3,0x68c3,0x41dc},
{0x68c4,0x68c4,0xadd},
{0x68c5,0x68c5,0x3c2e},
{0x68c6,0x68c6,0x219b},
{0x68c7,0x68c7,0x21b1},
{0x68c8,0x68c8,0x21a6},
{0x68c9,0x68c9,0xc97},
{0x68ca,0x68ca,0x42eb},
{0x68cb,0x68cb,0xc92},
{0x68cc,0x68cc,0x2195},
{0x68cd,0x68cd,0xc93},
{0x68ce,0x68ce,0x21a5},
{0x68d0,0x68d0,0x219e},
{0x68d1,0x68d1,0x21ab},
{0x68d2,0x68d2,0xc8f},
{0x68d3,0x68d3,0x2183},
{0x68d4,0x68d4,0x21ad},
{0x68d5,0x68d5,0xc85},
{0x68d6,0x68d6,0x218b},
{0x68d7,0x68d7,0xc88},
{0x68d8,0x68d8,0xc87},
{0x68da,0x68da,0xc98},
{0x68dc,0x68dc,0x2185},
{0x68dd,0x68de,0x21a7},
{0x68df,0x68df,0xc8a},
{0x68e0,0x68e0,0xc86},
{0x68e1,0x68e1,0x2193},
{0x68e3,0x68e3,0xc91},
{0x68e4,0x68e4,0x218e},
{0x68e6,0x68e6,0x21a9},
{0x68e7,0x68e7,0xc8d},
{0x68e8,0x68e8,0x21a1},
{0x68e9,0x68e9,0x21ae},
{0x68ea,0x68ea,0x2188},
{0x68eb,0x68eb,0x218d},
{0x68ec,0x68ec,0x2187},
{0x68ee,0x68ee,0xc8c},
{0x68ef,0x68ef,0x219a},
{0x68f0,0x68f0,0x242b},
{0x68f1,0x68f1,0x2189},
{0x68f2,0x68f2,0xc90},
{0x68f3,0x68f3,0x2192},
{0x68f4,0x68f4,0x21aa},
{0x68f5,0x68f5,0xc8b},
{0x68f6,0x68f6,0x218f},
{0x68f7,0x68f7,0x218c},
{0x68f8,0x68f8,0x219d},
{0x68f9,0x68f9,0xc8e},
{0x68fa,0x68fa,0xc84},
{0x68fb,0x68fb,0xc9a},
{0x68fc,0x68fc,0x21a0},
{0x68fd,0x68fd,0x219f},
{0x6900,0x6900,0x41df},
{0x6901,0x6901,0x41de},
{0x6902,0x6902,0x42fb},
{0x6903,0x6903,0x429c},
{0x6904,0x6904,0x2184},
{0x6905,0x6905,0xc89},
{0x6906,0x6906,0x21ac},
{0x6907,0x6907,0x2194},
{0x6908,0x6908,0x2196},
{0x6909,0x6909,0x4658},
{0x690a,0x690a,0x21a3},
{0x690b,0x690b,0x21a2},
{0x690c,0x690c,0x2182},
{0x690d,0x690d,0xc94},
{0x690e,0x690e,0xc96},
{0x690f,0x690f,0x218a},
{0x6910,0x6910,0x2191},
{0x6911,0x6911,0x2199},
{0x6912,0x6912,0xc95},
{0x6913,0x6913,0x2190},
{0x6914,0x6914,0x219c},
{0x6915,0x6915,0x21af},
{0x6917,0x6917,0x21a4},
{0x6918,0x6918,0x3da2},
{0x691a,0x691a,0x3d80},
{0x691b,0x691b,0x37bd},
{0x6925,0x6925,0x21b0},
{0x692a,0x692a,0x2186},
{0x692c,0x692c,0x3f38},
{0x692f,0x692f,0x243f},
{0x6930,0x6930,0xe3e},
{0x6932,0x6932,0x243d},
{0x6933,0x6933,0x2428},
{0x6934,0x6934,0x242d},
{0x6935,0x6935,0x2426},
{0x6936,0x6936,0x41e7},
{0x6937,0x6937,0x2439},
{0x6938,0x6938,0x2418},
{0x6939,0x6939,0x241f},
{0x693b,0x693b,0x2437},
{0x693c,0x693c,0x2441},
{0x693d,0x693d,0x2429},
{0x693e,0x693e,0x4056},
{0x693f,0x693f,0x241c},
{0x6940,0x6940,0x242f},
{0x6941,0x6941,0x2434},
{0x6942,0x6942,0x2420},
{0x6943,0x6943,0x4659},
{0x6944,0x6944,0x2431},
{0x6945,0x6945,0x241d},
{0x6946,0x6946,0x456f},
{0x6948,0x6949,0x2424},
{0x694a,0x694a,0xe40},
{0x694b,0x694b,0x2438},
{0x694c,0x694c,0x2436},
{0x694e,0x694e,0x2419},
{0x694f,0x694f,0x243b},
{0x6951,0x6951,0x243c},
{0x6952,0x6952,0x243e},
{0x6953,0x6953,0xe44},
{0x6954,0x6954,0xe3c},
{0x6955,0x6955,0x3d86},
{0x6956,0x6956,0x1f1d},
{0x6957,0x6957,0x2421},
{0x6958,0x6958,0x2433},
{0x6959,0x6959,0x2422},
{0x695a,0x695a,0xe39},
{0x695b,0x695b,0xe49},
{0x695c,0x695c,0x243a},
{0x695d,0x695d,0xe47},
{0x695e,0x695e,0xe43},
{0x695f,0x695f,0x2417},
{0x6960,0x6960,0xe3b},
{0x6961,0x6961,0x41e4},
{0x6962,0x6962,0x241a},
{0x6963,0x6963,0xe48},
{0x6964,0x6964,0x465b},
{0x6965,0x6965,0x242a},
{0x6966,0x6966,0x2416},
{0x6967,0x6967,0x3d15},
{0x6968,0x6968,0xe41},
{0x6969,0x6969,0x242e},
{0x696a,0x696a,0x241e},
{0x696b,0x696b,0xe42},
{0x696c,0x696c,0x2427},
{0x696d,0x696d,0xe38},
{0x696e,0x696e,0xc99},
{0x696f,0x696f,0x2430},
{0x6970,0x6970,0x2197},
{0x6971,0x6971,0x241b},
{0x6972,0x6972,0x3a6d},
{0x6973,0x6973,0x41db},
{0x6974,0x6974,0x2435},
{0x6975,0x6975,0xe3d},
{0x6976,0x6976,0x2432},
{0x6977,0x6977,0xe3a},
{0x6978,0x6978,0x242c},
{0x6979,0x6979,0xe45},
{0x697a,0x697a,0x2423},
{0x697b,0x697b,0x2440},
{0x6980,0x6980,0x3f3e},
{0x6982,0x6982,0xe3f},
{0x6983,0x6983,0x24af},
{0x6985,0x6985,0x465d},
{0x6986,0x6986,0xe46},
{0x698a,0x698a,0x3d74},
{0x698d,0x698d,0x26c9},
{0x698e,0x698e,0x26c7},
{0x6990,0x6990,0x26dd},
{0x6991,0x6991,0x26c5},
{0x6993,0x6993,0x26d7},
{0x6994,0x6994,0xe37},
{0x6995,0x6995,0xff0},
{0x6996,0x6996,0x26c1},
{0x6997,0x6997,0x26dc},
{0x6998,0x6998,0x41e8},
{0x6999,0x6999,0x26c6},
{0x699a,0x699a,0x26d4},
{0x699b,0x699b,0xff5},
{0x699c,0x699c,0xfee},
{0x699e,0x699e,0x26da},
{0x699f,0x699f,0x465c},
{0x69a0,0x69a0,0x26bf},
{0x69a1,0x69a1,0x26d9},
{0x69a2,0x69a2,0x3730},
{0x69a3,0x69a3,0x1000},
{0x69a4,0x69a4,0x26d0},
{0x69a5,0x69a5,0x26e0},
{0x69a6,0x69a6,0xffe},
{0x69a7,0x69a7,0x26c8},
{0x69a8,0x69a8,0xfef},
{0x69a9,0x69a9,0x26ca},
{0x69aa,0x69aa,0x26d8},
{0x69ab,0x69ab,0xff8},
{0x69ac,0x69ac,0x26c3},
{0x69ad,0x69ad,0xffc},
{0x69ae,0x69ae,0xff2},
{0x69af,0x69af,0x26cc},
{0x69b0,0x69b0,0x26c2},
{0x69b1,0x69b1,0x26bc},
{0x69b2,0x69b2,0x3ff6},
{0x69b3,0x69b3,0x26d6},
{0x69b4,0x69b4,0xff9},
{0x69b5,0x69b5,0x26df},
{0x69b6,0x69b6,0x26bd},
{0x69b7,0x69b7,0xff6},
{0x69b9,0x69b9,0x26d2},
{0x69bb,0x69bb,0xff7},
{0x69bc,0x69bc,0x26c4},
{0x69bd,0x69bd,0x26cf},
{0x69be,0x69be,0x26cb},
{0x69bf,0x69bf,0x26cd},
{0x69c0,0x69c0,0x3f5d},
{0x69c1,0x69c1,0xff1},
{0x69c2,0x69c2,0x26de},
{0x69c3,0x69c3,0xfff},
{0x69c4,0x69c4,0x26ce},
{0x69c6,0x69c6,0x26e1},
{0x69c9,0x69c9,0x26be},
{0x69ca,0x69ca,0x26d3},
{0x69cb,0x69cb,0xff4},
{0x69cc,0x69cc,0xffd},
{0x69cd,0x69cd,0xffb},
{0x69ce,0x69ce,0x26c0},
{0x69cf,0x69cf,0x26d5},
{0x69d0,0x69d0,0xffa},
{0x69d1,0x69d1,0x39ef},
{0x69d3,0x69d3,0xff3},
{0x69d4,0x69d4,0x26d1},
{0x69d5,0x69d5,0x3e3c},
{0x69d6,0x69d6,0x465e},
{0x69d9,0x69d9,0x26db},
{0x69e1,0x69e1,0x41d8},
{0x69e2,0x69e2,0x2909},
{0x69e4,0x69e4,0x2905},
{0x69e5,0x69e5,0x2901},
{0x69e6,0x69e6,0x2915},
{0x69e7,0x69e7,0x2912},
{0x69e8,0x69e8,0x1177},
{0x69eb,0x69eb,0x2919},
{0x69ec,0x69ec,0x2908},
{0x69ed,0x69ed,0x1182},
{0x69ee,0x69ee,0x290f},
{0x69f1,0x69f1,0x2904},
{0x69f2,0x69f2,0x290e},
{0x69f3,0x69f3,0x117f},
{0x69f4,0x69f4,0x2922},
{0x69f6,0x69f6,0x291f},
{0x69f7,0x69f7,0x2911},
{0x69f8,0x69f8,0x2902},
{0x69f9,0x69f9,0x41ed},
{0x69fa,0x69fa,0x41c7},
{0x69fb,0x69fb,0x2916},
{0x69fc,0x69fc,0x2918},
{0x69fd,0x69fd,0x117b},
{0x69fe,0x69fe,0x290c},
{0x69ff,0x69ff,0x2907},
{0x6a00,0x6a00,0x28fe},
{0x6a01,0x6a01,0x1178},
{0x6a02,0x6a02,0x1180},
{0x6a03,0x6a03,0x3e29},
{0x6a04,0x6a04,0x291b},
{0x6a05,0x6a05,0x1181},
{0x6a06,0x6a06,0x28ff},
{0x6a07,0x6a07,0x2921},
{0x6a08,0x6a08,0x2914},
{0x6a09,0x6a09,0x291a},
{0x6a0a,0x6a0a,0x117e},
{0x6a0b,0x6a0b,0x41d9},
{0x6a0c,0x6a0c,0x3d50},
{0x6a0d,0x6a0d,0x2917},
{0x6a0f,0x6a0f,0x291e},
{0x6a11,0x6a11,0x1183},
{0x6a13,0x6a13,0x117d},
{0x6a14,0x6a14,0x2910},
{0x6a15,0x6a15,0x2903},
{0x6a16,0x6a16,0x2923},
{0x6a17,0x6a17,0x2900},
{0x6a18,0x6a18,0x291c},
{0x6a19,0x6a19,0x117a},
{0x6a1a,0x6a1a,0x3d1e},
{0x6a1b,0x6a1b,0x290a},
{0x6a1c,0x6a1c,0x3b68},
{0x6a1d,0x6a1d,0x290b},
{0x6a1e,0x6a1e,0x1179},
{0x6a1f,0x6a1f,0x1176},
{0x6a20,0x6a20,0x2906},
{0x6a21,0x6a21,0x117c},
{0x6a23,0x6a23,0x1175},
{0x6a25,0x6a25,0x291d},
{0x6a26,0x6a26,0x2920},
{0x6a27,0x6a27,0x290d},
{0x6a28,0x6a28,0x2b74},
{0x6a2b,0x6a2b,0x3d42},
{0x6a2c,0x6a2c,0x42ab},
{0x6a2d,0x6a2d,0x3b1f},
{0x6a32,0x6a32,0x2b6c},
{0x6a33,0x6a33,0x3aa4},
{0x6a34,0x6a34,0x2b68},
{0x6a35,0x6a35,0x12d9},
{0x6a38,0x6a38,0x12ce},
{0x6a39,0x6a39,0x12d3},
{0x6a3a,0x6a3a,0x12cf},
{0x6a3b,0x6a3b,0x2b76},
{0x6a3c,0x6a3c,0x2b81},
{0x6a3d,0x6a3d,0x12cd},
{0x6a3e,0x6a3e,0x2b6e},
{0x6a3f,0x6a3f,0x2b77},
{0x6a40,0x6a40,0x2913},
{0x6a41,0x6a41,0x2b78},
{0x6a44,0x6a44,0x12d4},
{0x6a45,0x6a45,0x41f0},
{0x6a46,0x6a46,0x2b87},
{0x6a47,0x6a47,0x12d8},
{0x6a48,0x6a48,0x12db},
{0x6a49,0x6a49,0x2b6a},
{0x6a4b,0x6a4b,0x12d7},
{0x6a4c,0x6a4c,0x3aab},
{0x6a4d,0x6a4e,0x2b85},
{0x6a4f,0x6a4f,0x2b7c},
{0x6a50,0x6a50,0x2b7b},
{0x6a51,0x6a51,0x2b73},
{0x6a52,0x6a52,0x3aa5},
{0x6a53,0x6a53,0x37ac},
{0x6a54,0x6a54,0x2b7d},
{0x6a55,0x6a55,0x2b84},
{0x6a56,0x6a56,0x2b83},
{0x6a57,0x6a57,0x37da},
{0x6a58,0x6a58,0x12d2},
{0x6a59,0x6a59,0x12d0},
{0x6a5a,0x6a5a,0x2b75},
{0x6a5b,0x6a5b,0x2b72},
{0x6a5d,0x6a5d,0x2b6f},
{0x6a5e,0x6a5e,0x2b82},
{0x6a5f,0x6a5f,0x12da},
{0x6a60,0x6a60,0x2b80},
{0x6a61,0x6a61,0x12d6},
{0x6a62,0x6a62,0x12d5},
{0x6a64,0x6a64,0x2b7a},
{0x6a65,0x6a65,0x4660},
{0x6a66,0x6a66,0x2b69},
{0x6a67,0x6a67,0x2b6b},
{0x6a68,0x6a68,0x2b6d},
{0x6a69,0x6a69,0x2b7f},
{0x6a6a,0x6a6a,0x2b79},
{0x6a6b,0x6a6b,0x12d1},
{0x6a6d,0x6a6d,0x2b70},
{0x6a6f,0x6a6f,0x2b7e},
{0x6a71,0x6a71,0x4662},
{0x6a74,0x6a74,0x4661},
{0x6a76,0x6a76,0x2b71},
{0x6a7a,0x6a7a,0x3aad},
{0x6a7e,0x6a7e,0x13ec},
{0x6a7f,0x6a7f,0x2d84},
{0x6a80,0x6a80,0x13e5},
{0x6a81,0x6a81,0x2d75},
{0x6a82,0x6a82,0x4663},
{0x6a83,0x6a83,0x2d80},
{0x6a84,0x6a84,0x13e7},
{0x6a85,0x6a85,0x2d87},
{0x6a87,0x6a87,0x2d7c},
{0x6a89,0x6a89,0x2d77},
{0x6a8a,0x6a8a,0x4136},
{0x6a8c,0x6a8c,0x2d88},
{0x6a8d,0x6a8d,0x2d73},
{0x6a8e,0x6a8e,0x2d7e},
{0x6a8f,0x6a8f,0x3c1b},
{0x6a90,0x6a90,0x13ee},
{0x6a91,0x6a91,0x2d83},
{0x6a92,0x6a92,0x2d89},
{0x6a93,0x6a93,0x2d7d},
{0x6a94,0x6a94,0x13e6},
{0x6a95,0x6a95,0x2d7f},
{0x6a96,0x6a96,0x2d74},
{0x6a97,0x6a97,0x13ed},
{0x6a99,0x6a99,0x4665},
{0x6a9a,0x6a9a,0x2d86},
{0x6a9b,0x6a9b,0x2d79},
{0x6a9c,0x6a9c,0x13e9},
{0x6a9d,0x6a9d,0x41f2},
{0x6a9e,0x6a9e,0x2d7b},
{0x6a9f,0x6a9f,0x2d78},
{0x6aa0,0x6aa0,0x13ef},
{0x6aa1,0x6aa1,0x2d7a},
{0x6aa2,0x6aa2,0x13e8},
{0x6aa3,0x6aa3,0x13eb},
{0x6aa4,0x6aa4,0x2d82},
{0x6aa5,0x6aa5,0x2d76},
{0x6aa6,0x6aa6,0x2d85},
{0x6aa7,0x6aa7,0x37e3},
{0x6aa8,0x6aa8,0x2d81},
{0x6aab,0x6aab,0x4667},
{0x6aac,0x6aac,0x14dd},
{0x6aad,0x6aad,0x2f3e},
{0x6aae,0x6aaf,0x14e2},
{0x6ab1,0x6ab1,0x3e53},
{0x6ab2,0x6ab2,0x3b4b},
{0x6ab3,0x6ab3,0x14dc},
{0x6ab4,0x6ab4,0x2f3d},
{0x6ab5,0x6ab5,0x4668},
{0x6ab6,0x6ab7,0x2f3a},
{0x6ab8,0x6ab8,0x14e0},
{0x6ab9,0x6ab9,0x2f35},
{0x6aba,0x6aba,0x2f39},
{0x6abb,0x6abb,0x14df},
{0x6abd,0x6abd,0x2f36},
{0x6abe,0x6abe,0x3aa1},
{0x6ac2,0x6ac2,0x14e1},
{0x6ac3,0x6ac3,0x14de},
{0x6ac5,0x6ac5,0x2f34},
{0x6ac6,0x6ac6,0x2f38},
{0x6ac7,0x6ac7,0x2f3c},
{0x6ac8,0x6ac8,0x41f5},
{0x6ac9,0x6ac9,0x3aa6},
{0x6aca,0x6aca,0x3f4c},
{0x6acb,0x6acb,0x309f},
{0x6acc,0x6acc,0x309c},
{0x6acd,0x6acd,0x30a5},
{0x6acf,0x6acf,0x30a4},
{0x6ad0,0x6ad0,0x30a2},
{0x6ad1,0x6ad1,0x309d},
{0x6ad3,0x6ad3,0x1589},
{0x6ad4,0x6ad4,0x4669},
{0x6ad8,0x6ad8,0x3caa},
{0x6ad9,0x6ad9,0x309e},
{0x6ada,0x6ada,0x1588},
{0x6adb,0x6adb,0x13ea},
{0x6adc,0x6adc,0x30a1},
{0x6add,0x6add,0x1587},
{0x6ade,0x6ade,0x30a6},
{0x6adf,0x6adf,0x30a0},
{0x6ae0,0x6ae0,0x309b},
{0x6ae1,0x6ae1,0x2f37},
{0x6ae5,0x6ae5,0x1586},
{0x6ae7,0x6ae7,0x309a},
{0x6ae8,0x6ae8,0x31df},
{0x6aea,0x6aea,0x31de},
{0x6aeb,0x6aeb,0x30a3},
{0x6aec,0x6aec,0x161b},
{0x6aee,0x6aef,0x31e2},
{0x6af0,0x6af0,0x31dd},
{0x6af1,0x6af1,0x31e1},
{0x6af3,0x6af3,0x31dc},
{0x6af6,0x6af6,0x466a},
{0x6af8,0x6af8,0x32d6},
{0x6af9,0x6af9,0x31e0},
{0x6afa,0x6afa,0x1678},
{0x6afb,0x6afb,0x1676},
{0x6afc,0x6afc,0x32d4},
{0x6b00,0x6b00,0x32d7},
{0x6b02,0x6b02,0x32d3},
{0x6b03,0x6b03,0x32d5},
{0x6b04,0x6b04,0x1677},
{0x6b05,0x6b05,0x3aa7},
{0x6b08,0x6b09,0x3386},
{0x6b0a,0x6b0a,0x16c6},
{0x6b0b,0x6b0b,0x3385},
{0x6b0f,0x6b0f,0x3420},
{0x6b10,0x6b10,0x16fd},
{0x6b11,0x6b12,0x341e},
{0x6b13,0x6b13,0x3487},
{0x6b16,0x6b16,0x1743},
{0x6b17,0x6b17,0x34ce},
{0x6b18,0x6b19,0x34cc},
{0x6b1a,0x6b1a,0x34cf},
{0x6b1d,0x6b1d,0x41f8},
{0x6b1e,0x6b1e,0x3534},
{0x6b20,0x6b20,0x2de},
{0x6b21,0x6b21,0x3e3},
{0x6b23,0x6b23,0x61a},
{0x6b25,0x6b25,0x19c9},
{0x6b28,0x6b28,0x1b3d},
{0x6b2c,0x6b2c,0x1ce6},
{0x6b2d,0x6b2d,0x1ce8},
{0x6b2f,0x6b2f,0x1ce7},
{0x6b31,0x6b31,0x1ce9},
{0x6b32,0x6b32,0xae7},
{0x6b33,0x6b33,0x1f3c},
{0x6b34,0x6b34,0x1cea},
{0x6b35,0x6b35,0x41fb},
{0x6b36,0x6b36,0x1f3b},
{0x6b37,0x6b38,0x1f3d},
{0x6b39,0x6b39,0x21b2},
{0x6b3a,0x6b3a,0xc9c},
{0x6b3b,0x6b3b,0x21b3},
{0x6b3c,0x6b3c,0x21b5},
{0x6b3d,0x6b3d,0xc9d},
{0x6b3e,0x6b3e,0xc9b},
{0x6b3f,0x6b3f,0x21b4},
{0x6b41,0x6b41,0x2447},
{0x6b42,0x6b42,0x2445},
{0x6b43,0x6b43,0x2444},
{0x6b45,0x6b45,0x2443},
{0x6b46,0x6b46,0x2442},
{0x6b47,0x6b47,0xe4a},
{0x6b48,0x6b48,0x2446},
{0x6b49,0x6b49,0x1001},
{0x6b4a,0x6b4a,0x26e2},
{0x6b4b,0x6b4b,0x26e4},
{0x6b4c,0x6b4c,0x1002},
{0x6b4d,0x6b4d,0x26e3},
{0x6b4e,0x6b4e,0x1185},
{0x6b50,0x6b50,0x1184},
{0x6b51,0x6b51,0x2924},
{0x6b52,0x6b52,0x3f6e},
{0x6b54,0x6b54,0x2b89},
{0x6b55,0x6b55,0x2b88},
{0x6b56,0x6b56,0x2b8a},
{0x6b57,0x6b57,0x3aae},
{0x6b59,0x6b59,0x12dc},
{0x6b5b,0x6b5b,0x2d8a},
{0x6b5c,0x6b5c,0x13f0},
{0x6b5e,0x6b5e,0x2f3f},
{0x6b5f,0x6b5f,0x14e4},
{0x6b60,0x6b60,0x30a7},
{0x6b61,0x6b61,0x16c7},
{0x6b62,0x6b62,0x2df},
{0x6b63,0x6b63,0x34d},
{0x6b64,0x6b64,0x3e4},
{0x6b65,0x6b65,0x4c9},
{0x6b66,0x6b67,0x61b},
{0x6b6a,0x6b6a,0x769},
{0x6b6d,0x6b6d,0x1ceb},
{0x6b6f,0x6b6f,0x4515},
{0x6b72,0x6b72,0xe4b},
{0x6b74,0x6b74,0x41fc},
{0x6b76,0x6b76,0x29a2},
{0x6b77,0x6b77,0x12dd},
{0x6b78,0x6b78,0x14e5},
{0x6b79,0x6b79,0x2e0},
{0x6b7a,0x6b7a,0x44ee},
{0x6b7b,0x6b7b,0x3e5},
{0x6b7e,0x6b7e,0x19cb},
{0x6b7f,0x6b7f,0x61d},
{0x6b80,0x6b80,0x19ca},
{0x6b81,0x6b81,0x466b},
{0x6b82,0x6b82,0x1b3e},
{0x6b83,0x6b83,0x76a},
{0x6b84,0x6b84,0x1b3f},
{0x6b86,0x6b86,0x76b},
{0x6b88,0x6b88,0x1ced},
{0x6b89,0x6b89,0x900},
{0x6b8a,0x6b8a,0x8ff},
{0x6b8c,0x6b8c,0x1f43},
{0x6b8d,0x6b8e,0x1f41},
{0x6b8f,0x6b8f,0x1f40},
{0x6b91,0x6b91,0x1f3f},
{0x6b94,0x6b94,0x21b6},
{0x6b95,0x6b95,0x21b9},
{0x6b96,0x6b96,0xc9f},
{0x6b97,0x6b97,0x21b7},
{0x6b98,0x6b98,0xc9e},
{0x6b99,0x6b99,0x21b8},
{0x6b9b,0x6b9b,0x2448},
{0x6b9e,0x6ba0,0x26e5},
{0x6ba2,0x6ba2,0x2927},
{0x6ba3,0x6ba3,0x2926},
{0x6ba4,0x6ba4,0x1186},
{0x6ba5,0x6ba5,0x2925},
{0x6ba6,0x6ba6,0x2928},
{0x6ba7,0x6ba7,0x2b8b},
{0x6baa,0x6bab,0x2b8c},
{0x6bad,0x6bad,0x2d8b},
{0x6bae,0x6bae,0x13f1},
{0x6baf,0x6baf,0x14e6},
{0x6bb0,0x6bb0,0x30a8},
{0x6bb2,0x6bb2,0x1679},
{0x6bb3,0x6bb3,0x178a},
{0x6bb5,0x6bb5,0x76c},
{0x6bb6,0x6bb6,0x1b40},
{0x6bb7,0x6bb7,0x901},
{0x6bba,0x6bba,0xae8},
{0x6bbc,0x6bbc,0xca0},
{0x6bbd,0x6bbd,0x21ba},
{0x6bbf,0x6bbf,0xe4d},
{0x6bc0,0x6bc0,0xe4c},
{0x6bc1,0x6bc1,0x466c},
{0x6bc3,0x6bc4,0x26e8},
{0x6bc5,0x6bc6,0x1187},
{0x6bc7,0x6bc7,0x2b8f},
{0x6bc8,0x6bc8,0x2b8e},
{0x6bc9,0x6bc9,0x2f40},
{0x6bca,0x6bca,0x3421},
{0x6bcb,0x6bcb,0x2e1},
{0x6bcc,0x6bcc,0x178b},
{0x6bcd,0x6bcd,0x34e},
{0x6bcf,0x6bcf,0x4ca},
{0x6bd0,0x6bd0,0x18ad},
{0x6bd2,0x6bd2,0x76d},
{0x6bd3,0x6bd3,0xe4e},
{0x6bd4,0x6bd4,0x2e2},
{0x6bd6,0x6bd6,0x1b41},
{0x6bd7,0x6bd7,0x76e},
{0x6bd8,0x6bd8,0x1b42},
{0x6bda,0x6bda,0x13f2},
{0x6bdb,0x6bdb,0x2e3},
{0x6bdc,0x6bdc,0x4207},
{0x6bde,0x6bde,0x19cc},
{0x6be0,0x6be0,0x1b43},
{0x6be1,0x6be1,0x4205},
{0x6be2,0x6be2,0x1cf2},
{0x6be3,0x6be3,0x1cf1},
{0x6be4,0x6be4,0x1cef},
{0x6be6,0x6be6,0x1cee},
{0x6be7,0x6be7,0x1cf3},
{0x6be8,0x6be8,0x1cf0},
{0x6bea,0x6bea,0x466d},
{0x6beb,0x6bec,0xae9},
{0x6bef,0x6bef,0xca1},
{0x6bf0,0x6bf0,0x21bb},
{0x6bf2,0x6bf3,0x21bc},
{0x6bf7,0x6bf8,0x244c},
{0x6bf9,0x6bf9,0x244b},
{0x6bfa,0x6bfa,0x3f79},
{0x6bfb,0x6bfc,0x2449},
{0x6bfd,0x6bfd,0xe4f},
{0x6bfe,0x6bfe,0x26ea},
{0x6bff,0x6bff,0x292b},
{0x6c00,0x6c00,0x292a},
{0x6c01,0x6c01,0x2929},
{0x6c02,0x6c02,0x292c},
{0x6c03,0x6c03,0x2b91},
{0x6c04,0x6c04,0x2b90},
{0x6c05,0x6c05,0x12de},
{0x6c06,0x6c06,0x2b92},
{0x6c08,0x6c08,0x13f3},
{0x6c09,0x6c09,0x2d8c},
{0x6c0b,0x6c0b,0x2f41},
{0x6c0c,0x6c0c,0x30a9},
{0x6c0d,0x6c0d,0x3388},
{0x6c0f,0x6c0f,0x2e4},
{0x6c10,0x6c10,0x350},
{0x6c11,0x6c11,0x34f},
{0x6c13,0x6c13,0x61e},
{0x6c14,0x6c14,0x178c},
{0x6c15,0x6c15,0x17aa},
{0x6c16,0x6c16,0x3e6},
{0x6c18,0x6c18,0x1801},
{0x6c19,0x6c1a,0x18ae},
{0x6c1b,0x6c1b,0x61f},
{0x6c1c,0x6c1c,0x38ab},
{0x6c1d,0x6c1d,0x19cd},
{0x6c1f,0x6c1f,0x76f},
{0x6c20,0x6c21,0x1b44},
{0x6c23,0x6c23,0x902},
{0x6c24,0x6c24,0x906},
{0x6c25,0x6c25,0x1cf4},
{0x6c26,0x6c26,0x905},
{0x6c27,0x6c28,0x903},
{0x6c2a,0x6c2a,0x1f44},
{0x6c2b,0x6c2b,0xaeb},
{0x6c2c,0x6c2c,0xca4},
{0x6c2e,0x6c2f,0xca2},
{0x6c30,0x6c30,0x21be},
{0x6c31,0x6c31,0x3f63},
{0x6c33,0x6c33,0x1003},
{0x6c34,0x6c34,0x2e5},
{0x6c35,0x6c35,0x44ef},
{0x6c36,0x6c36,0x17ab},
{0x6c37,0x6c37,0x4208},
{0x6c38,0x6c38,0x351},
{0x6c39,0x6c39,0x3e05},
{0x6c3a,0x6c3a,0x44f0},
{0x6c3b,0x6c3b,0x17ae},
{0x6c3d,0x6c3d,0x4381},
{0x6c3e,0x6c3e,0x354},
{0x6c3f,0x6c3f,0x17ad},
{0x6c40,0x6c40,0x353},
{0x6c41,0x6c41,0x352},
{0x6c42,0x6c42,0x4cb},
{0x6c43,0x6c43,0x17ac},
{0x6c46,0x6c46,0x1802},
{0x6c49,0x6c49,0x408d},
{0x6c4a,0x6c4a,0x1806},
{0x6c4b,0x6c4c,0x1808},
{0x6c4d,0x6c4e,0x3f0},
{0x6c4f,0x6c4f,0x1805},
{0x6c50,0x6c50,0x3ec},
{0x6c52,0x6c52,0x1803},
{0x6c54,0x6c54,0x1807},
{0x6c55,0x6c55,0x3ed},
{0x6c57,0x6c57,0x3e8},
{0x6c58,0x6c58,0x3bba},
{0x6c59,0x6c59,0x3e9},
{0x6c5a,0x6c5a,0x420c},
{0x6c5b,0x6c5b,0x3ef},
{0x6c5c,0x6c5c,0x1804},
{0x6c5d,0x6c5d,0x3e7},
{0x6c5e,0x6c5e,0x4cc},
{0x6c5f,0x6c60,0x3ea},
{0x6c61,0x6c61,0x3ee},
{0x6c65,0x6c65,0x18c0},
{0x6c66,0x6c66,0x18be},
{0x6c67,0x6c67,0x18b1},
{0x6c68,0x6c68,0x4d8},
{0x6c69,0x6c69,0x18b8},
{0x6c6a,0x6c6a,0x4d3},
{0x6c6b,0x6c6b,0x18b2},
{0x6c6d,0x6c6d,0x18ba},
{0x6c6e,0x6c6e,0x40ca},
{0x6c6f,0x6c6f,0x18b7},
{0x6c70,0x6c70,0x4d6},
{0x6c71,0x6c71,0x18b6},
{0x6c72,0x6c72,0x4dd},
{0x6c73,0x6c73,0x18bf},
{0x6c74,0x6c74,0x4df},
{0x6c75,0x6c75,0x466e},
{0x6c76,0x6c76,0x4e1},
{0x6c78,0x6c78,0x18b0},
{0x6c79,0x6c79,0x420e},
{0x6c7a,0x6c7a,0x4d4},
{0x6c7b,0x6c7b,0x18c1},
{0x6c7d,0x6c7d,0x4db},
{0x6c7e,0x6c7e,0x4de},
{0x6c7f,0x6c7f,0x37a6},
{0x6c80,0x6c80,0x19e4},
{0x6c81,0x6c81,0x4ce},
{0x6c82,0x6c82,0x4e5},
{0x6c83,0x6c83,0x4dc},
{0x6c84,0x6c84,0x18b3},
{0x6c85,0x6c85,0x4d1},
{0x6c86,0x6c86,0x4e0},
{0x6c87,0x6c87,0x18bb},
{0x6c88,0x6c89,0x4cf},
{0x6c8a,0x6c8a,0x19e2},
{0x6c8b,0x6c8b,0x18b4},
{0x6c8c,0x6c8c,0x4d7},
{0x6c8d,0x6c8d,0x4e2},
{0x6c8e,0x6c8e,0x18c2},
{0x6c8f,0x6c8f,0x18b5},
{0x6c90,0x6c90,0x4d5},
{0x6c92,0x6c92,0x4da},
{0x6c93,0x6c93,0x19ce},
{0x6c94,0x6c94,0x4e3},
{0x6c95,0x6c95,0x18bc},
{0x6c96,0x6c96,0x4d9},
{0x6c98,0x6c98,0x4e4},
{0x6c99,0x6c99,0x4cd},
{0x6c9a,0x6c9a,0x18b9},
{0x6c9b,0x6c9b,0x4d2},
{0x6c9c,0x6c9c,0x18bd},
{0x6c9d,0x6c9d,0x19e3},
{0x6c9f,0x6c9f,0x3889},
{0x6ca2,0x6ca2,0x3d63},
{0x6caa,0x6caa,0x466f},
{0x6cab,0x6cab,0x62b},
{0x6cac,0x6cac,0x63b},
{0x6cad,0x6cad,0x19d5},
{0x6cae,0x6cae,0x632},
{0x6caf,0x6caf,0x3cf6},
{0x6cb0,0x6cb0,0x19ea},
{0x6cb1,0x6cb1,0x623},
{0x6cb2,0x6cb2,0x3ecc},
{0x6cb3,0x6cb3,0x626},
{0x6cb4,0x6cb4,0x19e1},
{0x6cb6,0x6cb6,0x19d3},
{0x6cb7,0x6cb7,0x19d7},
{0x6cb8,0x6cb8,0x62e},
{0x6cb9,0x6cb9,0x630},
{0x6cba,0x6cba,0x19da},
{0x6cbb,0x6cbb,0x637},
{0x6cbc,0x6cbc,0x629},
{0x6cbd,0x6cbe,0x627},
{0x6cbf,0x6cbf,0x636},
{0x6cc0,0x6cc0,0x19e6},
{0x6cc1,0x6cc1,0x631},
{0x6cc2,0x6cc2,0x19d9},
{0x6cc3,0x6cc3,0x19db},
{0x6cc4,0x6cc4,0x62f},
{0x6cc5,0x6cc5,0x634},
{0x6cc6,0x6cc6,0x19dc},
{0x6cc7,0x6cc7,0x19e9},
{0x6cc9,0x6cc9,0x770},
{0x6cca,0x6cca,0x63a},
{0x6ccb,0x6ccb,0x4070},
{0x6ccc,0x6ccc,0x624},
{0x6ccd,0x6ccd,0x19e8},
{0x6cce,0x6cce,0x3f91},
{0x6ccf,0x6ccf,0x19ec},
{0x6cd0,0x6cd0,0x19d8},
{0x6cd1,0x6cd1,0x19ee},
{0x6cd2,0x6cd2,0x19df},
{0x6cd3,0x6cd3,0x62d},
{0x6cd4,0x6cd4,0x19d4},
{0x6cd5,0x6cd5,0x62c},
{0x6cd6,0x6cd6,0x63e},
{0x6cd7,0x6cd7,0x633},
{0x6cd9,0x6cd9,0x19d2},
{0x6cda,0x6cda,0x1b4e},
{0x6cdb,0x6cdb,0x639},
{0x6cdc,0x6cdc,0x63d},
{0x6cdd,0x6cdd,0x19e0},
{0x6cde,0x6cde,0x19e5},
{0x6cdf,0x6cdf,0x3d57},
{0x6ce0,0x6ce0,0x63f},
{0x6ce1,0x6ce1,0x638},
{0x6ce2,0x6ce2,0x62a},
{0x6ce3,0x6ce3,0x620},
{0x6ce5,0x6ce5,0x625},
{0x6ce7,0x6ce7,0x19d6},
{0x6ce8,0x6ce8,0x621},
{0x6ce9,0x6ce9,0x19ed},
{0x6cea,0x6cea,0x3f8d},
{0x6ceb,0x6ceb,0x19d0},
{0x6cec,0x6cec,0x19cf},
{0x6ced,0x6ced,0x19dd},
{0x6cee,0x6cee,0x19d1},
{0x6cef,0x6cef,0x63c},
{0x6cf0,0x6cf0,0x907},
{0x6cf1,0x6cf1,0x635},
{0x6cf2,0x6cf2,0x19de},
{0x6cf3,0x6cf3,0x622},
{0x6cf5,0x6cf5,0x77f},
{0x6cf9,0x6cf9,0x19eb},
{0x6d00,0x6d00,0x1b55},
{0x6d01,0x6d01,0x1b58},
{0x6d02,0x6d02,0x4671},
{0x6d03,0x6d03,0x1b5b},
{0x6d04,0x6d04,0x1b50},
{0x6d05,0x6d05,0x3a19},
{0x6d06,0x6d06,0x4672},
{0x6d07,0x6d07,0x1b5e},
{0x6d08,0x6d08,0x1b61},
{0x6d09,0x6d09,0x1b63},
{0x6d0a,0x6d0a,0x1b4d},
{0x6d0b,0x6d0b,0x771},
{0x6d0c,0x6d0c,0x776},
{0x6d0d,0x6d0d,0x1cf9},
{0x6d0e,0x6d0e,0x786},
{0x6d0f,0x6d0f,0x1b5c},
{0x6d10,0x6d10,0x1b64},
{0x6d11,0x6d11,0x1b54},
{0x6d12,0x6d12,0x1b4c},
{0x6d16,0x6d16,0x1d14},
{0x6d17,0x6d17,0x779},
{0x6d18,0x6d18,0x1b59},
{0x6d19,0x6d19,0x1b51},
{0x6d1a,0x6d1a,0x1b53},
{0x6d1b,0x6d1b,0x77e},
{0x6d1d,0x6d1d,0x1b56},
{0x6d1e,0x6d1e,0x778},
{0x6d1f,0x6d1f,0x1b49},
{0x6d20,0x6d20,0x1b5f},
{0x6d22,0x6d22,0x1b62},
{0x6d24,0x6d24,0x3ac3},
{0x6d25,0x6d25,0x775},
{0x6d26,0x6d26,0x4673},
{0x6d27,0x6d27,0x781},
{0x6d28,0x6d28,0x1b46},
{0x6d29,0x6d29,0x783},
{0x6d2a,0x6d2a,0x773},
{0x6d2b,0x6d2b,0x787},
{0x6d2c,0x6d2c,0x1b60},
{0x6d2d,0x6d2d,0x1b48},
{0x6d2e,0x6d2e,0x784},
{0x6d2f,0x6d2f,0x1d0e},
{0x6d30,0x6d30,0x19e7},
{0x6d31,0x6d31,0x777},
{0x6d32,0x6d32,0x772},
{0x6d33,0x6d33,0x1b4f},
{0x6d34,0x6d34,0x1b47},
{0x6d35,0x6d35,0x785},
{0x6d36,0x6d36,0x77d},
{0x6d37,0x6d37,0x1b5a},
{0x6d38,0x6d38,0x782},
{0x6d39,0x6d39,0x780},
{0x6d3a,0x6d3a,0x1b52},
{0x6d3b,0x6d3b,0x77a},
{0x6d3c,0x6d3c,0x1b4a},
{0x6d3d,0x6d3e,0x77b},
{0x6d3f,0x6d3f,0x1b4b},
{0x6d40,0x6d40,0x1b5d},
{0x6d41,0x6d41,0x774},
{0x6d42,0x6d42,0x1b57},
{0x6d4e,0x6d4e,0x4558},
{0x6d57,0x6d57,0x3cf3},
{0x6d58,0x6d58,0x1cfc},
{0x6d59,0x6d59,0x90f},
{0x6d5a,0x6d5a,0x914},
{0x6d5b,0x6d5b,0x409c},
{0x6d5c,0x6d5c,0x4222},
{0x6d5e,0x6d5e,0x1d05},
{0x6d5f,0x6d5f,0x1d0b},
{0x6d60,0x6d60,0x1d07},
{0x6d61,0x6d61,0x1cfa},
{0x6d62,0x6d62,0x1cfd},
{0x6d63,0x6d64,0x1cf6},
{0x6d65,0x6d65,0x91b},
{0x6d66,0x6d66,0x90c},
{0x6d67,0x6d67,0x1d06},
{0x6d68,0x6d68,0x1d0f},
{0x6d69,0x6d69,0x916},
{0x6d6a,0x6d6a,0x908},
{0x6d6c,0x6d6c,0x911},
{0x6d6d,0x6d6d,0x1cfe},
{0x6d6e,0x6d6e,0x913},
{0x6d6f,0x6d6f,0x1cff},
{0x6d70,0x6d70,0x1d09},
{0x6d71,0x6d71,0x40f5},
{0x6d72,0x6d72,0x39d7},
{0x6d74,0x6d74,0x915},
{0x6d75,0x6d75,0x1d18},
{0x6d76,0x6d76,0x1cf8},
{0x6d77,0x6d77,0x90e},
{0x6d78,0x6d78,0x90d},
{0x6d79,0x6d79,0x919},
{0x6d7a,0x6d7a,0x1cf5},
{0x6d7b,0x6d7b,0x1d16},
{0x6d7c,0x6d7c,0x1d0a},
{0x6d7d,0x6d7d,0x1d17},
{0x6d7e,0x6d7e,0x1d11},
{0x6d7f,0x6d7f,0x1d03},
{0x6d80,0x6d80,0x1d12},
{0x6d81,0x6d81,0x4674},
{0x6d82,0x6d82,0x1d0c},
{0x6d83,0x6d83,0x1d15},
{0x6d84,0x6d84,0x1d13},
{0x6d85,0x6d85,0x91a},
{0x6d86,0x6d86,0x1d04},
{0x6d87,0x6d87,0x90b},
{0x6d88,0x6d88,0x90a},
{0x6d89,0x6d89,0x912},
{0x6d8a,0x6d8a,0x918},
{0x6d8b,0x6d8b,0x1d10},
{0x6d8c,0x6d8c,0x917},
{0x6d8d,0x6d8d,0x1d01},
{0x6d8e,0x6d8e,0xaec},
{0x6d8f,0x6d8f,0x3de8},
{0x6d90,0x6d90,0x1d19},
{0x6d91,0x6d91,0x1d00},
{0x6d92,0x6d92,0x1cfb},
{0x6d93,0x6d93,0x910},
{0x6d94,0x6d94,0x91c},
{0x6d95,0x6d95,0x909},
{0x6d96,0x6d96,0x4221},
{0x6d97,0x6d97,0x1d08},
{0x6d98,0x6d98,0x1d0d},
{0x6da4,0x6da4,0x4676},
{0x6da5,0x6da5,0x3ab9},
{0x6daa,0x6daa,0xb0e},
{0x6dab,0x6dab,0x1f46},
{0x6dac,0x6dac,0x1f4a},
{0x6dae,0x6dae,0xafb},
{0x6daf,0x6daf,0xaf9},
{0x6db1,0x6db1,0x4677},
{0x6db2,0x6db2,0xaf0},
{0x6db3,0x6db3,0x1f48},
{0x6db4,0x6db4,0x1f47},
{0x6db5,0x6db5,0xb04},
{0x6db7,0x6db7,0x1f4d},
{0x6db8,0x6db8,0xafe},
{0x6db9,0x6db9,0x3ffb},
{0x6dba,0x6dba,0x1f5f},
{0x6dbb,0x6dbb,0x1f6b},
{0x6dbc,0x6dbc,0xaed},
{0x6dbd,0x6dbd,0x1f5c},
{0x6dbe,0x6dbe,0x1f55},
{0x6dbf,0x6dbf,0xb10},
{0x6dc0,0x6dc0,0x1f45},
{0x6dc2,0x6dc2,0x1f61},
{0x6dc4,0x6dc4,0xb0d},
{0x6dc5,0x6dc5,0xb01},
{0x6dc6,0x6dc6,0xb0c},
{0x6dc7,0x6dc7,0xaf7},
{0x6dc8,0x6dc8,0x1f51},
{0x6dc9,0x6dc9,0x1f63},
{0x6dca,0x6dca,0x1f5b},
{0x6dcb,0x6dcb,0xaf8},
{0x6dcc,0x6dcc,0xaf2},
{0x6dcd,0x6dcd,0x1f69},
{0x6dcf,0x6dcf,0x1f62},
{0x6dd0,0x6dd0,0x1f64},
{0x6dd1,0x6dd1,0xafa},
{0x6dd2,0x6dd2,0xb02},
{0x6dd3,0x6dd3,0x1f66},
{0x6dd4,0x6dd4,0x1f4f},
{0x6dd5,0x6dd5,0x1f60},
{0x6dd6,0x6dd6,0x1f54},
{0x6dd7,0x6dd7,0x1f68},
{0x6dd8,0x6dd8,0xb07},
{0x6dd9,0x6dd9,0xaef},
{0x6dda,0x6dda,0xb05},
{0x6ddb,0x6ddb,0x1f59},
{0x6ddc,0x6ddd,0x1f57},
{0x6dde,0x6dde,0xafc},
{0x6ddf,0x6ddf,0x1f53},
{0x6de0,0x6de0,0x1f52},
{0x6de1,0x6de1,0xaf1},
{0x6de2,0x6de2,0x1f4c},
{0x6de3,0x6de3,0x1f6a},
{0x6de4,0x6de4,0xaf3},
{0x6de5,0x6de5,0x1f56},
{0x6de6,0x6de6,0xb11},
{0x6de8,0x6de8,0xb0b},
{0x6de9,0x6de9,0x1f4b},
{0x6dea,0x6dea,0xb08},
{0x6deb,0x6deb,0xb06},
{0x6dec,0x6dec,0xb0f},
{0x6ded,0x6ded,0x1f5d},
{0x6dee,0x6dee,0xb0a},
{0x6def,0x6def,0x1d02},
{0x6df0,0x6df0,0x1f5e},
{0x6df1,0x6df1,0xb09},
{0x6df2,0x6df2,0x1f65},
{0x6df3,0x6df3,0xaee},
{0x6df4,0x6df4,0x1f5a},
{0x6df5,0x6df5,0xb00},
{0x6df6,0x6df6,0x1f4e},
{0x6df7,0x6df7,0xaff},
{0x6df9,0x6df9,0xafd},
{0x6dfa,0x6dfa,0xaf5},
{0x6dfb,0x6dfb,0xaf4},
{0x6dfc,0x6dfc,0x21bf},
{0x6dfd,0x6dfd,0x1f67},
{0x6e00,0x6e00,0x1f50},
{0x6e02,0x6e02,0x3cfa},
{0x6e03,0x6e03,0x21d4},
{0x6e04,0x6e04,0x3abe},
{0x6e05,0x6e05,0xaf6},
{0x6e0a,0x6e0a,0x3ac1},
{0x6e0f,0x6e0f,0x3cf0},
{0x6e15,0x6e15,0x4678},
{0x6e18,0x6e18,0x4679},
{0x6e19,0x6e19,0xcc1},
{0x6e1a,0x6e1a,0xb03},
{0x6e1b,0x6e1b,0xcaf},
{0x6e1c,0x6e1c,0x21ce},
{0x6e1d,0x6e1d,0xcbd},
{0x6e1f,0x6e1f,0x21c2},
{0x6e20,0x6e20,0xcac},
{0x6e21,0x6e21,0xca8},
{0x6e22,0x6e22,0x21e0},
{0x6e23,0x6e23,0xcae},
{0x6e24,0x6e24,0xcb2},
{0x6e25,0x6e25,0xcad},
{0x6e26,0x6e26,0xcb6},
{0x6e27,0x6e27,0x21e4},
{0x6e28,0x6e28,0x21db},
{0x6e29,0x6e29,0x467a},
{0x6e2a,0x6e2a,0x4302},
{0x6e2b,0x6e2b,0x21c9},
{0x6e2c,0x6e2c,0xcbb},
{0x6e2d,0x6e2d,0xcb5},
{0x6e2e,0x6e2e,0x21d5},
{0x6e2f,0x6e2f,0xca5},
{0x6e30,0x6e30,0x21e1},
{0x6e31,0x6e31,0x21da},
{0x6e32,0x6e32,0xca9},
{0x6e33,0x6e33,0x21cf},
{0x6e34,0x6e34,0xcb8},
{0x6e35,0x6e36,0x21ec},
{0x6e38,0x6e38,0xca6},
{0x6e39,0x6e39,0x21df},
{0x6e3a,0x6e3a,0xcba},
{0x6e3b,0x6e3b,0x21d3},
{0x6e3c,0x6e3d,0x21c5},
{0x6e3e,0x6e3e,0xcbe},
{0x6e3f,0x6e3f,0x21ca},
{0x6e40,0x6e40,0x21d1},
{0x6e41,0x6e41,0x21cb},
{0x6e43,0x6e43,0xcbc},
{0x6e44,0x6e44,0xcc4},
{0x6e45,0x6e45,0x21c7},
{0x6e46,0x6e47,0x21c0},
{0x6e49,0x6e49,0x21c3},
{0x6e4a,0x6e4a,0xcab},
{0x6e4b,0x6e4b,0x21d0},
{0x6e4d,0x6e4d,0xcb9},
{0x6e4e,0x6e4e,0xcc2},
{0x6e4f,0x6e4f,0x4234},
{0x6e50,0x6e50,0x41d1},
{0x6e51,0x6e51,0x21d2},
{0x6e52,0x6e52,0x21ea},
{0x6e53,0x6e53,0x21e2},
{0x6e54,0x6e54,0xca7},
{0x6e55,0x6e55,0x21e8},
{0x6e56,0x6e56,0xcb3},
{0x6e58,0x6e58,0xcb1},
{0x6e59,0x6e59,0x40e6},
{0x6e5a,0x6e5a,0x21ee},
{0x6e5b,0x6e5b,0xcb0},
{0x6e5c,0x6e5c,0x21d8},
{0x6e5d,0x6e5d,0x21cc},
{0x6e5e,0x6e5e,0x21d6},
{0x6e5f,0x6e5f,0xcc7},
{0x6e60,0x6e60,0x21dc},
{0x6e61,0x6e61,0x21d9},
{0x6e62,0x6e62,0x21c8},
{0x6e63,0x6e63,0xcc3},
{0x6e64,0x6e64,0x21e6},
{0x6e65,0x6e65,0x21e3},
{0x6e66,0x6e66,0x21eb},
{0x6e67,0x6e67,0xcaa},
{0x6e68,0x6e68,0x21d7},
{0x6e69,0x6e69,0xcc6},
{0x6e6b,0x6e6b,0x21de},
{0x6e6e,0x6e6e,0xcb4},
{0x6e6f,0x6e6f,0xcb7},
{0x6e71,0x6e71,0x21dd},
{0x6e72,0x6e72,0xcc5},
{0x6e73,0x6e73,0x21cd},
{0x6e74,0x6e74,0x1f49},
{0x6e76,0x6e76,0x39d3},
{0x6e77,0x6e77,0x21e7},
{0x6e78,0x6e78,0x21e5},
{0x6e79,0x6e79,0x21e9},
{0x6e7c,0x6e7c,0x4223},
{0x6e86,0x6e86,0x467b},
{0x6e88,0x6e88,0x21c4},
{0x6e89,0x6e89,0xcc0},
{0x6e8b,0x6e8b,0x3bbf},
{0x6e8d,0x6e8d,0x246c},
{0x6e8e,0x6e8e,0x246b},
{0x6e8f,0x6e8f,0x2451},
{0x6e90,0x6e90,0xe55},
{0x6e92,0x6e92,0x246a},
{0x6e93,0x6e94,0x2454},
{0x6e96,0x6e96,0xe5f},
{0x6e97,0x6e97,0x2473},
{0x6e98,0x6e98,0xe5a},
{0x6e99,0x6e99,0x2469},
{0x6e9a,0x6e9a,0x4581},
{0x6e9b,0x6e9b,0x244e},
{0x6e9c,0x6e9c,0xe60},
{0x6e9d,0x6e9d,0xe56},
{0x6e9e,0x6e9e,0x245d},
{0x6e9f,0x6e9f,0x2453},
{0x6ea0,0x6ea0,0x2456},
{0x6ea1,0x6ea1,0x246e},
{0x6ea2,0x6ea2,0xe50},
{0x6ea3,0x6ea3,0x2475},
{0x6ea4,0x6ea4,0x246d},
{0x6ea5,0x6ea5,0xe59},
{0x6ea6,0x6ea6,0x2462},
{0x6ea7,0x6ea7,0xe64},
{0x6eaa,0x6eaa,0xe63},
{0x6eab,0x6eab,0xe5d},
{0x6eae,0x6eae,0x2474},
{0x6eaf,0x6eaf,0xe51},
{0x6eb0,0x6eb0,0x2460},
{0x6eb1,0x6eb1,0x2457},
{0x6eb2,0x6eb2,0x2464},
{0x6eb3,0x6eb3,0x2470},
{0x6eb4,0x6eb4,0xe65},
{0x6eb5,0x6eb5,0x41fe},
{0x6eb6,0x6eb6,0xe53},
{0x6eb7,0x6eb7,0x245f},
{0x6eb8,0x6eb8,0x3e0c},
{0x6eb9,0x6eb9,0x2458},
{0x6eba,0x6eba,0xe5c},
{0x6ebb,0x6ebb,0x467d},
{0x6ebc,0x6ebc,0xe5b},
{0x6ebd,0x6ebd,0x245b},
{0x6ebe,0x6ebe,0x2465},
{0x6ebf,0x6ebf,0x246f},
{0x6ec0,0x6ec0,0x2452},
{0x6ec1,0x6ec1,0x245c},
{0x6ec2,0x6ec2,0xe54},
{0x6ec3,0x6ec3,0x2466},
{0x6ec4,0x6ec4,0xe61},
{0x6ec5,0x6ec5,0xe58},
{0x6ec6,0x6ec6,0x2459},
{0x6ec7,0x6ec7,0xe57},
{0x6ec8,0x6ec8,0x2450},
{0x6ec9,0x6ec9,0x245e},
{0x6eca,0x6eca,0x2472},
{0x6ecb,0x6ecb,0xcbf},
{0x6ecc,0x6ecc,0x101f},
{0x6ecd,0x6ecd,0x2461},
{0x6ece,0x6ece,0x26eb},
{0x6ecf,0x6ecf,0x2463},
{0x6ed0,0x6ed0,0x2471},
{0x6ed1,0x6ed1,0xe5e},
{0x6ed2,0x6ed2,0x245a},
{0x6ed3,0x6ed3,0xe52},
{0x6ed4,0x6ed4,0xe62},
{0x6ed5,0x6ed5,0x119a},
{0x6ed6,0x6ed6,0x244f},
{0x6ed8,0x6ed8,0x2468},
{0x6ed9,0x6ed9,0x4069},
{0x6eda,0x6eda,0x467f},
{0x6edb,0x6edb,0x402f},
{0x6edc,0x6edc,0x2467},
{0x6edd,0x6edd,0x4538},
{0x6ee2,0x6ee2,0x467e},
{0x6ee8,0x6ee9,0x4681},
{0x6eeb,0x6eeb,0x2707},
{0x6eec,0x6eec,0x101c},
{0x6eed,0x6eed,0x26fc},
{0x6eee,0x6eee,0x2701},
{0x6eef,0x6eef,0x1011},
{0x6ef1,0x6ef1,0x26ed},
{0x6ef2,0x6ef2,0x101e},
{0x6ef4,0x6ef4,0x1008},
{0x6ef5,0x6ef5,0x26ec},
{0x6ef6,0x6ef6,0x270d},
{0x6ef7,0x6ef7,0x1020},
{0x6ef8,0x6ef8,0x26f0},
{0x6ef9,0x6ef9,0x2700},
{0x6efa,0x6efa,0x423b},
{0x6efb,0x6efb,0x26f2},
{0x6efc,0x6efc,0x2710},
{0x6efd,0x6efd,0x270c},
{0x6efe,0x6efe,0x1006},
{0x6eff,0x6eff,0x1010},
{0x6f00,0x6f00,0x2946},
{0x6f01,0x6f01,0x101d},
{0x6f02,0x6f02,0x100e},
{0x6f03,0x6f03,0x26ee},
{0x6f04,0x6f04,0x4224},
{0x6f05,0x6f05,0x270b},
{0x6f06,0x6f06,0x1012},
{0x6f07,0x6f07,0x2708},
{0x6f08,0x6f08,0x2715},
{0x6f09,0x6f09,0x26f4},
{0x6f0a,0x6f0a,0x26fd},
{0x6f0b,0x6f0b,0x3aba},
{0x6f0c,0x6f0c,0x3cfb},
{0x6f0d,0x6f0d,0x2713},
{0x6f0e,0x6f0e,0x2709},
{0x6f0f,0x6f0f,0x100d},
{0x6f12,0x6f12,0x26fb},
{0x6f13,0x6f13,0x1007},
{0x6f14,0x6f14,0x1005},
{0x6f15,0x6f15,0x1017},
{0x6f16,0x6f16,0x41a0},
{0x6f17,0x6f17,0x46ce},
{0x6f18,0x6f18,0x26f9},
{0x6f19,0x6f1a,0x26f6},
{0x6f1c,0x6f1c,0x270f},
{0x6f1e,0x6f1e,0x2714},
{0x6f1f,0x6f1f,0x2712},
{0x6f20,0x6f20,0x100b},
{0x6f21,0x6f21,0x2716},
{0x6f22,0x6f22,0x100f},
{0x6f23,0x6f23,0x1016},
{0x6f24,0x6f24,0x4683},
{0x6f25,0x6f25,0x26ef},
{0x6f26,0x6f26,0x292e},
{0x6f27,0x6f27,0x26f8},
{0x6f29,0x6f29,0x1009},
{0x6f2a,0x6f2a,0x101b},
{0x6f2b,0x6f2b,0x1018},
{0x6f2c,0x6f2c,0x100c},
{0x6f2d,0x6f2d,0x2702},
{0x6f2e,0x6f2e,0x26f3},
{0x6f2f,0x6f2f,0x1019},
{0x6f30,0x6f30,0x2704},
{0x6f31,0x6f31,0x1013},
{0x6f32,0x6f32,0x1015},
{0x6f33,0x6f33,0x1004},
{0x6f34,0x6f34,0x4684},
{0x6f35,0x6f35,0x2706},
{0x6f36,0x6f36,0x26fe},
{0x6f37,0x6f37,0x26f1},
{0x6f38,0x6f38,0x1014},
{0x6f39,0x6f39,0x270e},
{0x6f3a,0x6f3a,0x2711},
{0x6f3b,0x6f3b,0x26fa},
{0x6f3c,0x6f3c,0x2705},
{0x6f3d,0x6f3d,0x4282},
{0x6f3e,0x6f3e,0x100a},
{0x6f3f,0x6f3f,0x1189},
{0x6f40,0x6f40,0x2703},
{0x6f41,0x6f41,0x292d},
{0x6f43,0x6f43,0x270a},
{0x6f44,0x6f44,0x4240},
{0x6f4e,0x6f4e,0x26f5},
{0x6f4f,0x6f4f,0x2937},
{0x6f50,0x6f50,0x2941},
{0x6f51,0x6f51,0x118c},
{0x6f52,0x6f52,0x2940},
{0x6f53,0x6f53,0x294c},
{0x6f54,0x6f54,0x118e},
{0x6f55,0x6f55,0x293e},
{0x6f56,0x6f56,0x3e52},
{0x6f57,0x6f57,0x2942},
{0x6f58,0x6f58,0x1199},
{0x6f5a,0x6f5a,0x2939},
{0x6f5b,0x6f5b,0x1191},
{0x6f5c,0x6f5c,0x4241},
{0x6f5d,0x6f5d,0x2945},
{0x6f5e,0x6f5e,0x2b9a},
{0x6f5f,0x6f5f,0x119d},
{0x6f60,0x6f60,0x119c},
{0x6f61,0x6f61,0x2947},
{0x6f62,0x6f62,0x2936},
{0x6f63,0x6f63,0x2951},
{0x6f64,0x6f64,0x1197},
{0x6f66,0x6f66,0x118d},
{0x6f67,0x6f67,0x294a},
{0x6f69,0x6f69,0x294e},
{0x6f6a,0x6f6a,0x2953},
{0x6f6b,0x6f6b,0x2948},
{0x6f6c,0x6f6c,0x293c},
{0x6f6d,0x6f6d,0x1190},
{0x6f6e,0x6f6e,0x1193},
{0x6f6f,0x6f6f,0x119b},
{0x6f70,0x6f70,0x1196},
{0x6f72,0x6f72,0x293f},
{0x6f73,0x6f73,0x26ff},
{0x6f74,0x6f74,0x4243},
{0x6f76,0x6f76,0x293b},
{0x6f77,0x6f77,0x2952},
{0x6f78,0x6f78,0x1192},
{0x6f79,0x6f79,0x3bbe},
{0x6f7a,0x6f7a,0x1195},
{0x6f7b,0x6f7b,0x2954},
{0x6f7c,0x6f7c,0x118a},
{0x6f7d,0x6f7d,0x2949},
{0x6f7e,0x6f7e,0x292f},
{0x6f7f,0x6f7f,0x294f},
{0x6f80,0x6f80,0x13fc},
{0x6f81,0x6f81,0x4687},
{0x6f82,0x6f82,0x293d},
{0x6f84,0x6f84,0x118b},
{0x6f85,0x6f85,0x2938},
{0x6f86,0x6f86,0x118f},
{0x6f87,0x6f87,0x2930},
{0x6f88,0x6f88,0x101a},
{0x6f89,0x6f89,0x2934},
{0x6f8a,0x6f8a,0x4195},
{0x6f8b,0x6f8b,0x294d},
{0x6f8c,0x6f8c,0x2935},
{0x6f8d,0x6f8d,0x2933},
{0x6f8e,0x6f8e,0x1194},
{0x6f90,0x6f90,0x294b},
{0x6f92,0x6f92,0x2932},
{0x6f93,0x6f93,0x2944},
{0x6f94,0x6f94,0x2943},
{0x6f95,0x6f95,0x2950},
{0x6f96,0x6f96,0x293a},
{0x6f97,0x6f97,0x1198},
{0x6f9d,0x6f9d,0x4239},
{0x6f9e,0x6f9e,0x2b9d},
{0x6f9f,0x6f9f,0x4246},
{0x6fa0,0x6fa0,0x12eb},
{0x6fa1,0x6fa1,0x12e1},
{0x6fa2,0x6fa2,0x2ba9},
{0x6fa3,0x6fa3,0x2b95},
{0x6fa4,0x6fa4,0x12e3},
{0x6fa5,0x6fa5,0x2ba1},
{0x6fa6,0x6fa6,0x12ea},
{0x6fa7,0x6fa7,0x12e5},
{0x6fa8,0x6fa8,0x2b9f},
{0x6fa9,0x6fa9,0x2d8e},
{0x6faa,0x6faa,0x2ba5},
{0x6fab,0x6fab,0x2bab},
{0x6fac,0x6fac,0x2ba4},
{0x6fad,0x6fad,0x2b93},
{0x6fae,0x6fae,0x2ba2},
{0x6faf,0x6faf,0x2bad},
{0x6fb0,0x6fb0,0x2baf},
{0x6fb1,0x6fb1,0x12e0},
{0x6fb2,0x6fb2,0x2bae},
{0x6fb3,0x6fb3,0x12e6},
{0x6fb4,0x6fb4,0x12ec},
{0x6fb5,0x6fb5,0x3e27},
{0x6fb6,0x6fb6,0x12e9},
{0x6fb8,0x6fb8,0x2ba8},
{0x6fb9,0x6fb9,0x12e8},
{0x6fba,0x6fba,0x2ba3},
{0x6fbb,0x6fbb,0x3bbb},
{0x6fbc,0x6fbc,0x2b97},
{0x6fbd,0x6fbd,0x2b9c},
{0x6fbe,0x6fbe,0x4688},
{0x6fbf,0x6fbf,0x2ba7},
{0x6fc0,0x6fc0,0x12e7},
{0x6fc1,0x6fc1,0x12e4},
{0x6fc2,0x6fc2,0x12df},
{0x6fc3,0x6fc3,0x12e2},
{0x6fc4,0x6fc4,0x2b9b},
{0x6fc6,0x6fc6,0x2931},
{0x6fc7,0x6fc7,0x2b96},
{0x6fc8,0x6fc8,0x2b99},
{0x6fc9,0x6fc9,0x2baa},
{0x6fca,0x6fca,0x2b9e},
{0x6fcb,0x6fcb,0x2b94},
{0x6fcc,0x6fcc,0x2d8d},
{0x6fcd,0x6fcd,0x2bac},
{0x6fce,0x6fce,0x2b98},
{0x6fcf,0x6fcf,0x2ba6},
{0x6fd3,0x6fd3,0x4248},
{0x6fd4,0x6fd4,0x2d90},
{0x6fd5,0x6fd5,0x1400},
{0x6fd8,0x6fd8,0x13f4},
{0x6fd9,0x6fd9,0x3c74},
{0x6fda,0x6fda,0x3c73},
{0x6fdb,0x6fdb,0x13f8},
{0x6fdc,0x6fdc,0x2d92},
{0x6fdd,0x6fdd,0x2d98},
{0x6fde,0x6fde,0x2d96},
{0x6fdf,0x6fe0,0x13f6},
{0x6fe1,0x6fe1,0x13fe},
{0x6fe2,0x6fe2,0x2d99},
{0x6fe3,0x6fe3,0x2d91},
{0x6fe4,0x6fe4,0x13f9},
{0x6fe6,0x6fe6,0x2d95},
{0x6fe7,0x6fe7,0x2d94},
{0x6fe8,0x6fe8,0x2d9a},
{0x6fe9,0x6fe9,0x13ff},
{0x6feb,0x6feb,0x13fa},
{0x6fec,0x6fec,0x13fd},
{0x6fed,0x6fed,0x2d93},
{0x6fee,0x6fee,0x1401},
{0x6fef,0x6fef,0x13fb},
{0x6ff0,0x6ff0,0x1402},
{0x6ff1,0x6ff1,0x13f5},
{0x6ff2,0x6ff2,0x2d97},
{0x6ff4,0x6ff4,0x2d8f},
{0x6ff6,0x6ff6,0x38a5},
{0x6ff7,0x6ff7,0x2f4e},
{0x6ff8,0x6ff8,0x4237},
{0x6ffa,0x6ffa,0x14eb},
{0x6ffb,0x6ffb,0x2f4b},
{0x6ffc,0x6ffc,0x2f4d},
{0x6ffe,0x6ffe,0x14e9},
{0x6fff,0x7000,0x2f49},
{0x7001,0x7001,0x2f45},
{0x7003,0x7003,0x46e2},
{0x7004,0x7004,0x2ba0},
{0x7005,0x7005,0x2f46},
{0x7006,0x7006,0x14ea},
{0x7007,0x7007,0x2f42},
{0x7009,0x7009,0x14e7},
{0x700a,0x700a,0x2f4f},
{0x700b,0x700b,0x14e8},
{0x700c,0x700d,0x2f43},
{0x700e,0x700e,0x2f48},
{0x700f,0x700f,0x14ed},
{0x7011,0x7011,0x14ec},
{0x7014,0x7014,0x2f47},
{0x7015,0x7015,0x158f},
{0x7016,0x7016,0x30ad},
{0x7017,0x7017,0x30b3},
{0x7018,0x7018,0x1590},
{0x7019,0x7019,0x30aa},
{0x701a,0x701a,0x158d},
{0x701b,0x701b,0x158a},
{0x701c,0x701c,0x30b5},
{0x701d,0x701d,0x158e},
{0x701e,0x701e,0x3c76},
{0x701f,0x701f,0x158b},
{0x7020,0x7020,0x30ac},
{0x7021,0x7023,0x30af},
{0x7024,0x7024,0x30b4},
{0x7026,0x7026,0x2f4c},
{0x7027,0x7027,0x30ab},
{0x7028,0x7028,0x158c},
{0x7029,0x7029,0x30b2},
{0x702a,0x702a,0x31ef},
{0x702b,0x702b,0x30ae},
{0x702c,0x702c,0x468e},
{0x702f,0x702f,0x31e6},
{0x7030,0x7030,0x161d},
{0x7031,0x7031,0x31e9},
{0x7032,0x7032,0x161e},
{0x7033,0x7033,0x31f2},
{0x7034,0x7034,0x31e8},
{0x7035,0x7035,0x31e5},
{0x7037,0x7037,0x31e7},
{0x7038,0x7038,0x31eb},
{0x7039,0x7039,0x31ee},
{0x703a,0x703a,0x31ed},
{0x703b,0x703b,0x31f1},
{0x703c,0x703c,0x31e4},
{0x703e,0x703e,0x161c},
{0x703f,0x703f,0x31ec},
{0x7040,0x7040,0x31f0},
{0x7041,0x7041,0x31f3},
{0x7042,0x7042,0x31ea},
{0x7043,0x7044,0x32d8},
{0x7045,0x7046,0x32dd},
{0x7048,0x7049,0x32db},
{0x704a,0x704a,0x32da},
{0x704b,0x704b,0x4254},
{0x704c,0x704c,0x167a},
{0x7050,0x7050,0x4690},
{0x7051,0x7051,0x16c8},
{0x7052,0x7052,0x338c},
{0x7054,0x7054,0x4691},
{0x7055,0x7057,0x3389},
{0x7058,0x7058,0x16c9},
{0x705a,0x705a,0x3423},
{0x705b,0x705b,0x3422},
{0x705c,0x705c,0x3852},
{0x705d,0x705d,0x348a},
{0x705e,0x705e,0x1722},
{0x705f,0x705f,0x3488},
{0x7060,0x7060,0x348b},
{0x7061,0x7061,0x3489},
{0x7062,0x7062,0x34d0},
{0x7063,0x7063,0x1744},
{0x7064,0x7064,0x1752},
{0x7065,0x7065,0x351c},
{0x7066,0x7066,0x3506},
{0x7067,0x7067,0x3cf5},
{0x7068,0x7068,0x351b},
{0x7069,0x706a,0x3547},
{0x706b,0x706b,0x2e6},
{0x706c,0x706c,0x44f1},
{0x706f,0x706f,0x4692},
{0x7070,0x7070,0x3f2},
{0x7071,0x7071,0x180a},
{0x7074,0x7074,0x18c3},
{0x7075,0x7075,0x38c7},
{0x7076,0x7076,0x4e6},
{0x7078,0x7078,0x4e9},
{0x7079,0x7079,0x4099},
{0x707a,0x707a,0x18c4},
{0x707c,0x707d,0x4e7},
{0x707e,0x707e,0x4255},
{0x707f,0x707f,0x4693},
{0x7081,0x7081,0x4959},
{0x7082,0x7082,0x19f7},
{0x7083,0x7083,0x19f9},
{0x7084,0x7084,0x19f4},
{0x7085,0x7085,0x19f1},
{0x7086,0x7086,0x19f3},
{0x7089,0x7089,0x43f9},
{0x708a,0x708a,0x643},
{0x708b,0x708b,0x40cb},
{0x708e,0x708e,0x641},
{0x708f,0x708f,0x46b3},
{0x7091,0x7091,0x19f5},
{0x7092,0x7092,0x642},
{0x7093,0x7093,0x19f2},
{0x7094,0x7094,0x19ef},
{0x7095,0x7095,0x640},
{0x7096,0x7096,0x19f6},
{0x7098,0x7098,0x19f0},
{0x7099,0x7099,0x644},
{0x709a,0x709a,0x19f8},
{0x709f,0x709f,0x1b66},
{0x70a0,0x70a0,0x4742},
{0x70a1,0x70a1,0x1b6a},
{0x70a3,0x70a3,0x3972},
{0x70a4,0x70a4,0x790},
{0x70a5,0x70a5,0x3cb7},
{0x70a7,0x70a7,0x4256},
{0x70a9,0x70a9,0x1b6d},
{0x70ab,0x70ab,0x788},
{0x70ac,0x70ac,0x78b},
{0x70ad,0x70ad,0x78d},
{0x70ae,0x70ae,0x78f},
{0x70af,0x70af,0x78c},
{0x70b0,0x70b0,0x1b69},
{0x70b1,0x70b1,0x1b68},
{0x70b3,0x70b3,0x78a},
{0x70b4,0x70b5,0x1b6b},
{0x70b7,0x70b7,0x1b65},
{0x70b8,0x70b8,0x78e},
{0x70b9,0x70b9,0x3d69},
{0x70ba,0x70ba,0x789},
{0x70bb,0x70bb,0x4812},
{0x70bc,0x70bc,0x4559},
{0x70bd,0x70bd,0x4270},
{0x70be,0x70be,0x1b67},
{0x70c0,0x70c0,0x4733},
{0x70c4,0x70c4,0x3baa},
{0x70c5,0x70c7,0x1d27},
{0x70c8,0x70c8,0x921},
{0x70ca,0x70ca,0x91d},
{0x70cb,0x70cb,0x1d1e},
{0x70cc,0x70cc,0x4258},
{0x70cd,0x70cd,0x1d26},
{0x70ce,0x70ce,0x1d2b},
{0x70cf,0x70cf,0x922},
{0x70d0,0x70d0,0x396d},
{0x70d1,0x70d1,0x1d1c},
{0x70d2,0x70d2,0x1d22},
{0x70d3,0x70d3,0x1d1b},
{0x70d4,0x70d4,0x1d25},
{0x70d5,0x70d6,0x4259},
{0x70d7,0x70d7,0x1d21},
{0x70d8,0x70d8,0x91e},
{0x70d9,0x70d9,0x920},
{0x70da,0x70da,0x1d2a},
{0x70dc,0x70dc,0x1d1a},
{0x70dd,0x70dd,0x1d1d},
{0x70de,0x70de,0x1d23},
{0x70df,0x70df,0x425b},
{0x70e0,0x70e0,0x1d24},
{0x70e1,0x70e1,0x1d2c},
{0x70e2,0x70e2,0x1d20},
{0x70e4,0x70e4,0x91f},
{0x70ef,0x70ef,0xb16},
{0x70f0,0x70f0,0x1f72},
{0x70f1,0x70f1,0x3ac8},
{0x70f3,0x70f3,0x1f74},
{0x70f4,0x70f4,0x1f70},
{0x70f5,0x70f5,0x4760},
{0x70f6,0x70f6,0x1f7c},
{0x70f7,0x70f7,0x1f6e},
{0x70f8,0x70f8,0x1f7b},
{0x70f9,0x70f9,0xb12},
{0x70fa,0x70fa,0x1f6c},
{0x70fb,0x70fb,0x21f2},
{0x70fc,0x70fc,0x1f76},
{0x70fd,0x70fd,0xb15},
{0x70fe,0x70fe,0x3d0e},
{0x70ff,0x70ff,0x1f77},
{0x7100,0x7100,0x1f7a},
{0x7102,0x7102,0x1f7e},
{0x7104,0x7104,0x1f73},
{0x7105,0x7105,0x3a23},
{0x7106,0x7106,0x1f78},
{0x7109,0x710a,0xb13},
{0x710b,0x710b,0x1f7d},
{0x710c,0x710c,0x1f71},
{0x710d,0x710d,0x1f6d},
{0x710e,0x710e,0x1f7f},
{0x7110,0x7110,0x1f75},
{0x7113,0x7113,0x1f79},
{0x7117,0x7117,0x1f6f},
{0x7119,0x711a,0xcc8},
{0x711b,0x711b,0x21fc},
{0x711c,0x711c,0xccf},
{0x711d,0x711d,0x3d47},
{0x711e,0x711e,0x21f0},
{0x711f,0x711f,0x21f9},
{0x7120,0x7120,0x21ef},
{0x7121,0x7121,0xccc},
{0x7122,0x7122,0x21f7},
{0x7123,0x7123,0x21f5},
{0x7125,0x7125,0x21f6},
{0x7126,0x7126,0xcca},
{0x7128,0x7128,0x21fa},
{0x7129,0x7129,0x3ba9},
{0x712b,0x712b,0x4261},
{0x712c,0x712c,0x426a},
{0x712e,0x712e,0x21f3},
{0x712f,0x712f,0x21f1},
{0x7130,0x7130,0xccb},
{0x7131,0x7131,0x21f4},
{0x7132,0x7132,0x21f8},
{0x7133,0x7133,0x3d54},
{0x7134,0x7134,0x3c7e},
{0x7135,0x7135,0x376d},
{0x7136,0x7136,0xccd},
{0x713a,0x713a,0x21fb},
{0x713b,0x713b,0x3ac5},
{0x713e,0x713e,0x372e},
{0x7140,0x7140,0x4398},
{0x7141,0x7141,0x247b},
{0x7142,0x7142,0x2482},
{0x7143,0x7143,0x2484},
{0x7144,0x7144,0x248a},
{0x7145,0x7145,0x4262},
{0x7146,0x7146,0xe72},
{0x7147,0x7147,0x2476},
{0x7149,0x7149,0xe6a},
{0x714a,0x714a,0x4264},
{0x714b,0x714b,0x2485},
{0x714c,0x714c,0xe6f},
{0x714d,0x714d,0x248b},
{0x714e,0x714e,0xe66},
{0x714f,0x714f,0x4267},
{0x7150,0x7150,0x2488},
{0x7151,0x7151,0x4865},
{0x7152,0x7152,0x2478},
{0x7153,0x7153,0x2489},
{0x7154,0x7154,0x2477},
{0x7156,0x7156,0xe74},
{0x7158,0x7158,0x2483},
{0x7159,0x7159,0xe67},
{0x715a,0x715a,0x248c},
{0x715c,0x715c,0xe6c},
{0x715d,0x715d,0x247c},
{0x715e,0x715e,0xe71},
{0x715f,0x715f,0x2487},
{0x7160,0x7160,0x247a},
{0x7161,0x7161,0x2481},
{0x7162,0x7162,0x247d},
{0x7163,0x7163,0x2479},
{0x7164,0x7164,0xe69},
{0x7165,0x7165,0xe70},
{0x7166,0x7166,0xe6e},
{0x7167,0x7167,0xe6b},
{0x7168,0x7168,0xe73},
{0x7169,0x7169,0xe68},
{0x716a,0x716a,0x2480},
{0x716b,0x716b,0x3cc5},
{0x716c,0x716c,0xe6d},
{0x716e,0x716e,0xcce},
{0x7170,0x7170,0x2486},
{0x7171,0x7171,0x3dc0},
{0x7172,0x7172,0x247e},
{0x7173,0x7173,0x47e7},
{0x7175,0x7175,0x3ced},
{0x7176,0x7176,0x4000},
{0x7177,0x7177,0x3957},
{0x7178,0x7178,0x247f},
{0x717a,0x717a,0x4924},
{0x717b,0x717b,0x271e},
{0x717c,0x717c,0x3835},
{0x717d,0x717d,0x1023},
{0x717e,0x717e,0x3970},
{0x7180,0x7180,0x271a},
{0x7181,0x7181,0x2720},
{0x7182,0x7182,0x271c},
{0x7184,0x7184,0x1025},
{0x7185,0x7185,0x271b},
{0x7186,0x7186,0x271f},
{0x7187,0x7187,0x2717},
{0x7188,0x7188,0x4706},
{0x7189,0x7189,0x2719},
{0x718a,0x718a,0x1024},
{0x718c,0x718c,0x39ea},
{0x718e,0x718e,0x3d3b},
{0x718f,0x718f,0x271d},
{0x7190,0x7190,0x2718},
{0x7192,0x7192,0x1026},
{0x7194,0x7194,0x1021},
{0x7196,0x7196,0x425f},
{0x7197,0x7197,0x2721},
{0x7198,0x7198,0x481d},
{0x7199,0x7199,0x1022},
{0x719a,0x719a,0x295a},
{0x719b,0x719b,0x2957},
{0x719c,0x719c,0x2963},
{0x719d,0x719d,0x295d},
{0x719e,0x719e,0x295f},
{0x719f,0x719f,0x119e},
{0x71a0,0x71a0,0x2959},
{0x71a1,0x71a1,0x2961},
{0x71a2,0x71a2,0x48c1},
{0x71a3,0x71a3,0x3ec8},
{0x71a4,0x71a4,0x2960},
{0x71a5,0x71a5,0x295e},
{0x71a7,0x71a7,0x2964},
{0x71a8,0x71a8,0x11a1},
{0x71a9,0x71a9,0x295b},
{0x71aa,0x71aa,0x2962},
{0x71ac,0x71ac,0x119f},
{0x71ad,0x71ad,0x3e1d},
{0x71af,0x71af,0x2956},
{0x71b0,0x71b0,0x2958},
{0x71b1,0x71b1,0x11a0},
{0x71b2,0x71b2,0x2955},
{0x71b3,0x71b3,0x2965},
{0x71b4,0x71b4,0x425e},
{0x71b5,0x71b5,0x295c},
{0x71b7,0x71b7,0x468b},
{0x71b8,0x71b8,0x2bb3},
{0x71b9,0x71b9,0x12f3},
{0x71ba,0x71ba,0x426e},
{0x71bc,0x71bc,0x2bbe},
{0x71bd,0x71bd,0x2bbc},
{0x71be,0x71be,0x12ed},
{0x71bf,0x71bf,0x2bb2},
{0x71c0,0x71c1,0x2bb5},
{0x71c2,0x71c2,0x2bb1},
{0x71c3,0x71c4,0x12f7},
{0x71c5,0x71c5,0x2bb0},
{0x71c6,0x71c6,0x2bbf},
{0x71c7,0x71c7,0x2bba},
{0x71c8,0x71c8,0x12f1},
{0x71c9,0x71c9,0x12ee},
{0x71ca,0x71ca,0x2bb9},
{0x71cb,0x71cb,0x2bb7},
{0x71ce,0x71ce,0x12f4},
{0x71cf,0x71cf,0x2bbb},
{0x71d0,0x71d0,0x12ef},
{0x71d1,0x71d1,0x373d},
{0x71d2,0x71d2,0x12f0},
{0x71d4,0x71d4,0x2bb8},
{0x71d5,0x71d5,0x12f2},
{0x71d6,0x71d6,0x2bb4},
{0x71d8,0x71d8,0x2bbd},
{0x71d9,0x71d9,0x12f5},
{0x71da,0x71db,0x2bc0},
{0x71dc,0x71dc,0x12f6},
{0x71dd,0x71dd,0x4078},
{0x71df,0x71df,0x1404},
{0x71e0,0x71e0,0x140b},
{0x71e1,0x71e1,0x2d9b},
{0x71e2,0x71e2,0x2da1},
{0x71e4,0x71e4,0x2d9f},
{0x71e5,0x71e5,0x1407},
{0x71e6,0x71e6,0x1406},
{0x71e7,0x71e7,0x1403},
{0x71e8,0x71e8,0x2d9d},
{0x71eb,0x71eb,0x3c81},
{0x71ec,0x71ec,0x1409},
{0x71ed,0x71ed,0x1408},
{0x71ee,0x71ee,0x1405},
{0x71f0,0x71f0,0x2da0},
{0x71f1,0x71f1,0x2d9c},
{0x71f2,0x71f2,0x2d9e},
{0x71f4,0x71f4,0x140a},
{0x71f5,0x71f5,0x46ee},
{0x71f6,0x71f6,0x3e9a},
{0x71f8,0x71f8,0x14f1},
{0x71f9,0x71f9,0x2f52},
{0x71fb,0x71fc,0x14ee},
{0x71fd,0x71fd,0x2f54},
{0x71fe,0x71fe,0x14f0},
{0x71ff,0x71ff,0x2f51},
{0x7201,0x7201,0x2f50},
{0x7202,0x7202,0x30b9},
{0x7203,0x7203,0x2f53},
{0x7205,0x7205,0x30ba},
{0x7206,0x7206,0x1591},
{0x7207,0x7207,0x30b8},
{0x7209,0x7209,0x3dbb},
{0x720a,0x720a,0x30b7},
{0x720c,0x720c,0x30b6},
{0x720d,0x720d,0x1592},
{0x720e,0x720e,0x4271},
{0x720f,0x720f,0x39fc},
{0x7210,0x7210,0x161f},
{0x7213,0x7214,0x31f4},
{0x7215,0x7215,0x4273},
{0x7216,0x7216,0x3953},
{0x7217,0x7217,0x3969},
{0x7219,0x7219,0x32e1},
{0x721a,0x721a,0x32e0},
{0x721b,0x721b,0x167b},
{0x721d,0x721d,0x32df},
{0x721e,0x721f,0x338d},
{0x7222,0x7222,0x3424},
{0x7223,0x7223,0x348c},
{0x7224,0x7224,0x4276},
{0x7226,0x7226,0x34d1},
{0x7227,0x7227,0x3535},
{0x7228,0x7228,0x1766},
{0x7229,0x7229,0x3549},
{0x722a,0x722a,0x2e7},
{0x722b,0x722b,0x44f2},
{0x722c,0x722d,0x645},
{0x722e,0x722e,0x4279},
{0x7230,0x7230,0x791},
{0x7235,0x7235,0x140c},
{0x7236,0x7236,0x2e8},
{0x7238,0x7238,0x647},
{0x7239,0x7239,0x923},
{0x723a,0x723a,0xe75},
{0x723b,0x723b,0x2e9},
{0x723d,0x723d,0xb17},
{0x723e,0x723e,0x1027},
{0x723f,0x723f,0x178d},
{0x7240,0x7240,0x427a},
{0x7241,0x7241,0x1b6e},
{0x7242,0x7242,0x1d2d},
{0x7244,0x7244,0x2722},
{0x7246,0x7246,0x140d},
{0x7247,0x7247,0x2ea},
{0x7248,0x7248,0x648},
{0x7249,0x724a,0x1b6f},
{0x724b,0x724b,0x21fd},
{0x724c,0x724c,0xcd0},
{0x724f,0x724f,0x248d},
{0x7250,0x7250,0x3ac9},
{0x7252,0x7252,0xe76},
{0x7253,0x7253,0x2723},
{0x7255,0x7255,0x427d},
{0x7256,0x7256,0x11a2},
{0x7257,0x7257,0x427e},
{0x7258,0x7258,0x1593},
{0x7259,0x7259,0x2eb},
{0x725a,0x725a,0x21fe},
{0x725b,0x725b,0x2ec},
{0x725c,0x725c,0x465a},
{0x725d,0x725d,0x3f4},
{0x725e,0x725e,0x180b},
{0x725f,0x725f,0x3f3},
{0x7260,0x7260,0x4ec},
{0x7261,0x7261,0x4eb},
{0x7262,0x7262,0x4ea},
{0x7263,0x7263,0x18c5},
{0x7266,0x7266,0x3ebb},
{0x7267,0x7267,0x649},
{0x7269,0x7269,0x64a},
{0x726a,0x726a,0x19fa},
{0x726c,0x726c,0x1b71},
{0x726e,0x726e,0x1b74},
{0x726f,0x726f,0x793},
{0x7270,0x7270,0x1b72},
{0x7272,0x7272,0x792},
{0x7273,0x7273,0x1b73},
{0x7274,0x7274,0x794},
{0x7276,0x7276,0x1d30},
{0x7277,0x7277,0x1d2f},
{0x7278,0x7278,0x1d2e},
{0x7279,0x7279,0x924},
{0x727b,0x727c,0x1f81},
{0x727d,0x727d,0xb18},
{0x727e,0x727e,0x1f80},
{0x727f,0x727f,0x1f83},
{0x7280,0x7280,0xcd2},
{0x7281,0x7281,0xb19},
{0x7282,0x7282,0x4283},
{0x7284,0x7284,0xcd1},
{0x7285,0x7285,0x2202},
{0x7286,0x7286,0x2201},
{0x7287,0x7287,0x3aca},
{0x7288,0x7289,0x21ff},
{0x728b,0x728b,0x2203},
{0x728c,0x728c,0x248f},
{0x728d,0x728d,0x248e},
{0x728e,0x728e,0x2492},
{0x728f,0x728f,0x43c9},
{0x7290,0x7290,0x2491},
{0x7291,0x7291,0x2490},
{0x7292,0x7292,0x1028},
{0x7293,0x7293,0x2726},
{0x7294,0x7294,0x3acb},
{0x7295,0x7295,0x2725},
{0x7296,0x7296,0x1029},
{0x7297,0x7297,0x2724},
{0x7298,0x7298,0x2966},
{0x729a,0x729a,0x2967},
{0x729b,0x729b,0x11a3},
{0x729d,0x729e,0x2bc2},
{0x729f,0x729f,0x473a},
{0x72a1,0x72a1,0x30bf},
{0x72a2,0x72a2,0x1594},
{0x72a3,0x72a3,0x30be},
{0x72a4,0x72a4,0x30bd},
{0x72a5,0x72a6,0x30bb},
{0x72a7,0x72a7,0x167c},
{0x72a8,0x72a8,0x31f6},
{0x72a9,0x72a9,0x338f},
{0x72aa,0x72aa,0x34d2},
{0x72ac,0x72ac,0x2ed},
{0x72ad,0x72ad,0x44f4},
{0x72ae,0x72ae,0x17af},
{0x72af,0x72af,0x355},
{0x72b0,0x72b0,0x17b0},
{0x72b2,0x72b2,0x3fb6},
{0x72b4,0x72b5,0x180c},
{0x72ba,0x72ba,0x18cb},
{0x72bd,0x72bd,0x18c7},
{0x72bf,0x72bf,0x18c6},
{0x72c0,0x72c0,0x64b},
{0x72c1,0x72c1,0x18ca},
{0x72c2,0x72c2,0x4ee},
{0x72c3,0x72c3,0x18c8},
{0x72c4,0x72c4,0x4ed},
{0x72c5,0x72c5,0x18cc},
{0x72c6,0x72c6,0x18c9},
{0x72c9,0x72c9,0x19fe},
{0x72ca,0x72ca,0x1b75},
{0x72cb,0x72cb,0x19fc},
{0x72cc,0x72cc,0x1a03},
{0x72cd,0x72cd,0x43ca},
{0x72ce,0x72ce,0x64c},
{0x72d0,0x72d0,0x64f},
{0x72d1,0x72d1,0x1a04},
{0x72d2,0x72d2,0x1a00},
{0x72d4,0x72d4,0x1a01},
{0x72d6,0x72d6,0x19fb},
{0x72d7,0x72d7,0x64e},
{0x72d8,0x72d8,0x19fd},
{0x72d9,0x72d9,0x64d},
{0x72da,0x72da,0x1a02},
{0x72dc,0x72dc,0x19ff},
{0x72df,0x72df,0x1b79},
{0x72e0,0x72e1,0x796},
{0x72e2,0x72e2,0x3f97},
{0x72e3,0x72e3,0x1b7c},
{0x72e4,0x72e4,0x1b76},
{0x72e6,0x72e6,0x1b7b},
{0x72e8,0x72e8,0x1b77},
{0x72e9,0x72e9,0x795},
{0x72ea,0x72ea,0x1b7a},
{0x72eb,0x72eb,0x1b78},
{0x72f3,0x72f3,0x1d36},
{0x72f4,0x72f4,0x1d33},
{0x72f6,0x72f6,0x1d35},
{0x72f7,0x72f7,0x929},
{0x72f8,0x72f8,0x928},
{0x72f9,0x72f9,0x926},
{0x72fa,0x72fa,0x1d32},
{0x72fb,0x72fb,0x1d37},
{0x72fc,0x72fc,0x925},
{0x72fd,0x72fd,0x927},
{0x72fe,0x72fe,0x1d34},
{0x72ff,0x72ff,0x1f8b},
{0x7300,0x7300,0x1d31},
{0x7301,0x7301,0x1d38},
{0x7302,0x7302,0x3f9a},
{0x7304,0x7304,0x3eb5},
{0x7307,0x7307,0x1f86},
{0x7308,0x7308,0x1f8a},
{0x730a,0x730a,0x1f89},
{0x730b,0x730b,0x2205},
{0x730c,0x730c,0x2210},
{0x730f,0x730f,0x1f8c},
{0x7310,0x7310,0x46ea},
{0x7311,0x7311,0x1f87},
{0x7312,0x7312,0x2204},
{0x7313,0x7313,0xb1d},
{0x7316,0x7316,0xb1c},
{0x7317,0x7317,0x1f85},
{0x7318,0x7318,0x1f88},
{0x7319,0x7319,0xb1e},
{0x731b,0x731b,0xb1b},
{0x731c,0x731c,0xb1a},
{0x731d,0x731d,0x1f84},
{0x731e,0x731e,0x1f8d},
{0x7322,0x7322,0x2207},
{0x7323,0x7323,0x220e},
{0x7325,0x7325,0xcd4},
{0x7326,0x7326,0x220d},
{0x7327,0x7327,0x220a},
{0x7328,0x7328,0x428a},
{0x7329,0x7329,0xcd6},
{0x732a,0x732a,0x3d70},
{0x732b,0x732b,0x4285},
{0x732c,0x732c,0x3fb2},
{0x732d,0x732d,0x220c},
{0x732e,0x732e,0x428b},
{0x7330,0x7330,0x2206},
{0x7331,0x7331,0x2208},
{0x7332,0x7332,0x220b},
{0x7333,0x7333,0x2209},
{0x7334,0x7334,0xcd5},
{0x7335,0x7335,0x220f},
{0x7336,0x7336,0xcd3},
{0x7337,0x7337,0xe77},
{0x7338,0x7338,0x46eb},
{0x7339,0x7339,0x43cc},
{0x733a,0x733a,0x2496},
{0x733b,0x733b,0x2495},
{0x733c,0x733c,0x2493},
{0x733e,0x733e,0xe7a},
{0x733f,0x733f,0xe79},
{0x7340,0x7340,0x2497},
{0x7341,0x7341,0x46ec},
{0x7342,0x7342,0x2494},
{0x7343,0x7343,0x2727},
{0x7344,0x7344,0x102a},
{0x7345,0x7345,0xe78},
{0x7348,0x7348,0x43fa},
{0x7349,0x7349,0x2499},
{0x734a,0x734a,0x2498},
{0x734c,0x734c,0x272a},
{0x734d,0x734d,0x2728},
{0x734e,0x734e,0x11a4},
{0x734f,0x734f,0x3f51},
{0x7350,0x7350,0x102b},
{0x7351,0x7351,0x2729},
{0x7352,0x7352,0x2969},
{0x7357,0x7357,0x11a5},
{0x7358,0x7358,0x2968},
{0x7359,0x7359,0x2971},
{0x735a,0x735a,0x2970},
{0x735b,0x735b,0x296e},
{0x735d,0x735d,0x296d},
{0x735e,0x7360,0x296a},
{0x7361,0x7361,0x296f},
{0x7362,0x7362,0x2972},
{0x7365,0x7365,0x2bc8},
{0x7366,0x7367,0x2bc5},
{0x7368,0x7368,0x12f9},
{0x7369,0x7369,0x2bc4},
{0x736a,0x736a,0x2bca},
{0x736b,0x736b,0x2bc9},
{0x736c,0x736c,0x2bc7},
{0x736e,0x736f,0x2da3},
{0x7370,0x7370,0x140e},
{0x7371,0x7371,0x3f98},
{0x7372,0x7372,0x140f},
{0x7373,0x7373,0x2da2},
{0x7374,0x7374,0x3ed4},
{0x7375,0x7375,0x14f3},
{0x7376,0x7376,0x2f55},
{0x7377,0x7377,0x14f2},
{0x7378,0x7378,0x1595},
{0x737a,0x737a,0x1596},
{0x737b,0x737b,0x1620},
{0x737c,0x737c,0x31f8},
{0x737d,0x737d,0x31f7},
{0x737e,0x737e,0x32e2},
{0x737f,0x737f,0x3390},
{0x7380,0x7380,0x16ca},
{0x7381,0x7381,0x3426},
{0x7382,0x7382,0x3425},
{0x7383,0x7383,0x3427},
{0x7384,0x7384,0x356},
{0x7385,0x7385,0x1b7d},
{0x7386,0x7386,0x92a},
{0x7387,0x7387,0xb1f},
{0x7388,0x7388,0x1f8e},
{0x7389,0x7389,0x357},
{0x738a,0x738a,0x17b1},
{0x738b,0x738b,0x2ee},
{0x738e,0x738e,0x180e},
{0x738f,0x738f,0x46e5},
{0x7392,0x7392,0x18d1},
{0x7393,0x7394,0x18cf},
{0x7395,0x7395,0x18cd},
{0x7396,0x7396,0x4ef},
{0x7397,0x7397,0x18ce},
{0x7398,0x7398,0x39f6},
{0x739c,0x739c,0x408a},
{0x739d,0x739d,0x1a0c},
{0x739e,0x739e,0x395b},
{0x739f,0x739f,0x652},
{0x73a0,0x73a0,0x1a0a},
{0x73a1,0x73a1,0x1a06},
{0x73a2,0x73a2,0x1a09},
{0x73a4,0x73a4,0x1a05},
{0x73a5,0x73a5,0x654},
{0x73a6,0x73a6,0x1a08},
{0x73a7,0x73a7,0x4087},
{0x73a8,0x73a8,0x651},
{0x73a9,0x73a9,0x650},
{0x73aa,0x73aa,0x428d},
{0x73ab,0x73ab,0x653},
{0x73ac,0x73ac,0x1a0b},
{0x73ad,0x73ad,0x1a07},
{0x73b2,0x73b2,0x79b},
{0x73b3,0x73b3,0x79e},
{0x73b4,0x73b4,0x1b85},
{0x73b5,0x73b5,0x1b84},
{0x73b6,0x73b6,0x1b83},
{0x73b7,0x73b7,0x798},
{0x73b8,0x73b8,0x1b8c},
{0x73b9,0x73b9,0x1b82},
{0x73bb,0x73bb,0x79a},
{0x73bc,0x73bc,0x1d3d},
{0x73be,0x73be,0x1b89},
{0x73bf,0x73bf,0x1b87},
{0x73c0,0x73c0,0x79d},
{0x73c2,0x73c2,0x1b7f},
{0x73c3,0x73c3,0x1b8a},
{0x73c5,0x73c5,0x1b81},
{0x73c6,0x73c6,0x1b8b},
{0x73c7,0x73c7,0x1b88},
{0x73c8,0x73c8,0x1b80},
{0x73c9,0x73c9,0x4290},
{0x73ca,0x73ca,0x799},
{0x73cb,0x73cb,0x1b8d},
{0x73cc,0x73cc,0x1b7e},
{0x73cd,0x73cd,0x79c},
{0x73ce,0x73ce,0x411b},
{0x73cf,0x73cf,0x428c},
{0x73d0,0x73d0,0x3b17},
{0x73d2,0x73d2,0x1d42},
{0x73d3,0x73d3,0x1d39},
{0x73d4,0x73d4,0x1d44},
{0x73d5,0x73d5,0x4073},
{0x73d6,0x73d6,0x1d3c},
{0x73d7,0x73d8,0x1d47},
{0x73d9,0x73d9,0x1d3a},
{0x73da,0x73da,0x1d46},
{0x73db,0x73db,0x1d43},
{0x73dc,0x73dc,0x1d41},
{0x73dd,0x73dd,0x1d45},
{0x73de,0x73de,0x930},
{0x73e0,0x73e0,0x92e},
{0x73e1,0x73e1,0x46f0},
{0x73e2,0x73e2,0x3ca3},
{0x73e3,0x73e3,0x1d3f},
{0x73e4,0x73e4,0x42a5},
{0x73e5,0x73e5,0x1d3b},
{0x73e6,0x73e6,0x3cba},
{0x73e7,0x73e7,0x1d3e},
{0x73e8,0x73e8,0x1d49},
{0x73e9,0x73e9,0x1d40},
{0x73ea,0x73ea,0x92f},
{0x73eb,0x73eb,0x1b86},
{0x73ed,0x73ed,0x92b},
{0x73ee,0x73ee,0x92d},
{0x73ef,0x73ef,0x4011},
{0x73f3,0x73f3,0x3cab},
{0x73f4,0x73f4,0x1f9c},
{0x73f5,0x73f5,0x1f91},
{0x73f6,0x73f6,0x1f8f},
{0x73f7,0x73f7,0x429a},
{0x73f8,0x73f8,0x1f90},
{0x73f9,0x73f9,0x3bf9},
{0x73fa,0x73fa,0x1f97},
{0x73fb,0x73fb,0x3cac},
{0x73fc,0x73fc,0x1f98},
{0x73fd,0x73fd,0x1f94},
{0x73fe,0x73fe,0xb24},
{0x73ff,0x73ff,0x1f99},
{0x7400,0x7400,0x1f96},
{0x7401,0x7401,0x1f93},
{0x7403,0x7403,0xb22},
{0x7404,0x7404,0x1f92},
{0x7405,0x7405,0xb20},
{0x7406,0x7406,0xb23},
{0x7407,0x7407,0x1f95},
{0x7408,0x7408,0x1f9d},
{0x7409,0x7409,0x92c},
{0x740a,0x740a,0xb21},
{0x740b,0x740b,0x1f9b},
{0x740c,0x740c,0x1f9a},
{0x740d,0x740d,0xb25},
{0x7411,0x7411,0x456c},
{0x7412,0x7412,0x3be6},
{0x7414,0x7414,0x39f4},
{0x7415,0x7415,0x429b},
{0x7416,0x7416,0x2215},
{0x7417,0x7417,0x40ad},
{0x7419,0x7419,0x46f3},
{0x741a,0x741a,0x2216},
{0x741b,0x741b,0xce0},
{0x741c,0x741c,0x38dd},
{0x741d,0x741d,0x221c},
{0x741e,0x741f,0x46f4},
{0x7420,0x7420,0x221e},
{0x7421,0x7421,0x2217},
{0x7422,0x7422,0xcda},
{0x7423,0x7423,0x221b},
{0x7424,0x7424,0x221a},
{0x7425,0x7425,0xcdb},
{0x7426,0x7426,0xce1},
{0x7428,0x7428,0xce2},
{0x7429,0x7429,0x221d},
{0x742a,0x742a,0xcd8},
{0x742b,0x742b,0x2214},
{0x742c,0x742c,0x2212},
{0x742d,0x742d,0x2218},
{0x742e,0x742e,0x2211},
{0x742f,0x742f,0xcdf},
{0x7430,0x7430,0x2213},
{0x7431,0x7431,0x2219},
{0x7432,0x7432,0x221f},
{0x7433,0x7433,0xcd9},
{0x7434,0x7434,0xcde},
{0x7435,0x7436,0xcdc},
{0x7437,0x7437,0x3ae1},
{0x7438,0x7438,0x3b09},
{0x7439,0x7439,0x429e},
{0x743a,0x743a,0xcd7},
{0x743c,0x743c,0x3adf},
{0x743f,0x743f,0xe81},
{0x7440,0x7440,0x24a0},
{0x7441,0x7441,0xe80},
{0x7442,0x7442,0x24a4},
{0x7443,0x7443,0x3a1a},
{0x7444,0x7444,0x249a},
{0x7445,0x7445,0x3bd7},
{0x7446,0x7446,0x24a5},
{0x7447,0x7447,0x42a4},
{0x7448,0x7448,0x3cb5},
{0x7449,0x7449,0x4291},
{0x744a,0x744b,0x249b},
{0x744d,0x744d,0x24a6},
{0x744e,0x744e,0x24a3},
{0x744f,0x7450,0x24a1},
{0x7451,0x7451,0x249e},
{0x7452,0x7452,0x249d},
{0x7453,0x7453,0x4231},
{0x7454,0x7454,0x24a7},
{0x7455,0x7455,0xe7d},
{0x7456,0x7456,0x39f5},
{0x7457,0x7457,0x249f},
{0x7459,0x7459,0xe82},
{0x745a,0x745a,0xe7c},
{0x745b,0x745c,0xe83},
{0x745d,0x745d,0x42c6},
{0x745e,0x745e,0xe7f},
{0x745f,0x745f,0xe7e},
{0x7460,0x7460,0x42a2},
{0x7462,0x7462,0x272b},
{0x7463,0x7463,0x102d},
{0x7464,0x7464,0x102c},
{0x7465,0x7465,0x4101},
{0x7467,0x7467,0x2730},
{0x7468,0x7468,0x3aee},
{0x7469,0x7469,0x11a6},
{0x746a,0x746a,0x102e},
{0x746b,0x746b,0x3be7},
{0x746c,0x746c,0x42a8},
{0x746d,0x746d,0x1030},
{0x746e,0x746e,0x2731},
{0x746f,0x746f,0xe7b},
{0x7470,0x7470,0x102f},
{0x7471,0x7471,0x272d},
{0x7472,0x7472,0x272f},
{0x7473,0x7473,0x272c},
{0x7474,0x7474,0x42a9},
{0x7475,0x7475,0x272e},
{0x7476,0x7476,0x42a6},
{0x7479,0x7479,0x297c},
{0x747a,0x747a,0x3ad1},
{0x747c,0x747c,0x297b},
{0x747d,0x747d,0x2978},
{0x747e,0x747e,0x11a9},
{0x747f,0x747f,0x2bcb},
{0x7480,0x7480,0x11aa},
{0x7481,0x7481,0x2977},
{0x7482,0x7482,0x42ac},
{0x7483,0x7483,0x11a8},
{0x7485,0x7485,0x2979},
{0x7486,0x7486,0x2976},
{0x7487,0x7487,0x2973},
{0x7488,0x7488,0x297a},
{0x7489,0x748a,0x2974},
{0x748b,0x748b,0x11a7},
{0x748c,0x748c,0x3a21},
{0x748d,0x748d,0x469d},
{0x7490,0x7490,0x2da8},
{0x7492,0x7492,0x2bcf},
{0x7494,0x7494,0x2bce},
{0x7495,0x7495,0x2bd0},
{0x7497,0x7497,0x2da5},
{0x7498,0x7498,0x12fc},
{0x7499,0x7499,0x39f3},
{0x749a,0x749a,0x2bcc},
{0x749b,0x749b,0x3b0a},
{0x749c,0x749c,0x12fa},
{0x749e,0x749e,0x12fe},
{0x749f,0x749f,0x12fd},
{0x74a0,0x74a0,0x2bcd},
{0x74a1,0x74a1,0x2bd1},
{0x74a3,0x74a3,0x12fb},
{0x74a4,0x74a4,0x4170},
{0x74a5,0x74a5,0x2dac},
{0x74a6,0x74a6,0x1412},
{0x74a7,0x74a7,0x14f4},
{0x74a8,0x74a8,0x1413},
{0x74a9,0x74a9,0x1410},
{0x74aa,0x74aa,0x2da9},
{0x74ab,0x74ab,0x2da7},
{0x74ad,0x74ad,0x2daa},
{0x74af,0x74af,0x2dad},
{0x74b0,0x74b0,0x1411},
{0x74b1,0x74b1,0x2dab},
{0x74b2,0x74b2,0x2da6},
{0x74b4,0x74b4,0x3ca5},
{0x74b5,0x74b5,0x2f58},
{0x74b6,0x74b6,0x2f5b},
{0x74b7,0x74b7,0x30c2},
{0x74b8,0x74b8,0x2f56},
{0x74ba,0x74ba,0x31f9},
{0x74bb,0x74bb,0x2f5c},
{0x74bd,0x74bd,0x1597},
{0x74be,0x74be,0x2f5a},
{0x74bf,0x74bf,0x14f5},
{0x74c0,0x74c0,0x2f57},
{0x74c1,0x74c1,0x2f59},
{0x74c2,0x74c2,0x2f5d},
{0x74c3,0x74c3,0x30c3},
{0x74c5,0x74c5,0x30c1},
{0x74c8,0x74c8,0x42b5},
{0x74ca,0x74ca,0x1598},
{0x74cb,0x74cb,0x30c0},
{0x74cc,0x74cc,0x3ade},
{0x74cf,0x74cf,0x1621},
{0x74d0,0x74d0,0x3a9b},
{0x74d3,0x74d3,0x46fa},
{0x74d4,0x74d4,0x167e},
{0x74d5,0x74d5,0x3392},
{0x74d6,0x74d6,0x167d},
{0x74d7,0x74d7,0x3394},
{0x74d8,0x74d8,0x3391},
{0x74d9,0x74d9,0x3393},
{0x74da,0x74da,0x16fe},
{0x74db,0x74db,0x348d},
{0x74dc,0x74dc,0x358},
{0x74dd,0x74dd,0x1a0d},
{0x74de,0x74df,0x1d4a},
{0x74e0,0x74e0,0xb26},
{0x74e1,0x74e1,0x24a8},
{0x74e2,0x74e2,0x12ff},
{0x74e3,0x74e3,0x1599},
{0x74e4,0x74e4,0x16cb},
{0x74e5,0x74e5,0x348e},
{0x74e6,0x74e6,0x359},
{0x74e7,0x74e7,0x3ffe},
{0x74e8,0x74e8,0x1a0e},
{0x74e9,0x74e9,0x120},
{0x74ec,0x74ec,0x1b8e},
{0x74ee,0x74ee,0x1b8f},
{0x74f0,0x74f0,0x3fe6},
{0x74f1,0x74f1,0x3f9b},
{0x74f2,0x74f2,0x3fdf},
{0x74f4,0x74f5,0x1d4c},
{0x74f6,0x74f7,0xb27},
{0x74f8,0x74f8,0x3ff4},
{0x74fb,0x74fb,0x2220},
{0x74fd,0x74fd,0x24ab},
{0x74fe,0x74fe,0x24aa},
{0x74ff,0x74ff,0x24a9},
{0x7500,0x7500,0x2732},
{0x7502,0x7503,0x2733},
{0x7504,0x7504,0x1031},
{0x7505,0x7505,0x3fde},
{0x7507,0x7507,0x297e},
{0x7508,0x7508,0x297d},
{0x750b,0x750b,0x2bd2},
{0x750c,0x750d,0x1300},
{0x750e,0x750e,0x42b7},
{0x750f,0x750f,0x2db1},
{0x7510,0x7512,0x2dae},
{0x7513,0x7513,0x2f5f},
{0x7514,0x7514,0x2f5e},
{0x7515,0x7515,0x14f6},
{0x7516,0x7516,0x30c4},
{0x7517,0x7517,0x32e3},
{0x7518,0x7518,0x35a},
{0x7519,0x7519,0x4583},
{0x751a,0x751a,0x79f},
{0x751c,0x751c,0xb29},
{0x751d,0x751d,0x24ac},
{0x751e,0x751e,0x42b8},
{0x751f,0x751f,0x35b},
{0x7521,0x7521,0x1d4e},
{0x7522,0x7522,0xb2a},
{0x7525,0x7526,0xce3},
{0x7528,0x7529,0x35c},
{0x752a,0x752a,0x180f},
{0x752b,0x752b,0x4f1},
{0x752c,0x752c,0x4f0},
{0x752d,0x752d,0x7a0},
{0x752e,0x752e,0x1b90},
{0x752f,0x752f,0x2221},
{0x7530,0x7533,0x35e},
{0x7534,0x7534,0x3e87},
{0x7535,0x7535,0x455a},
{0x7537,0x7538,0x4f2},
{0x7539,0x7539,0x18d3},
{0x753a,0x753a,0x18d2},
{0x753b,0x753b,0x3dfb},
{0x753d,0x753d,0x655},
{0x753e,0x753e,0x1a11},
{0x753f,0x7540,0x1a0f},
{0x7542,0x7542,0x43cd},
{0x7546,0x7546,0x3f40},
{0x7547,0x7548,0x1b91},
{0x754a,0x754a,0x42bf},
{0x754b,0x754b,0x7a4},
{0x754c,0x754c,0x7a2},
{0x754d,0x754d,0x42be},
{0x754e,0x754e,0x7a3},
{0x754f,0x754f,0x7a1},
{0x7551,0x7551,0x3cfe},
{0x7553,0x7553,0x3feb},
{0x7554,0x7554,0x931},
{0x7555,0x7555,0x3aea},
{0x7559,0x7559,0x935},
{0x755a,0x755a,0x934},
{0x755b,0x755b,0x1d4f},
{0x755c,0x755c,0x933},
{0x755d,0x755d,0x932},
{0x755f,0x755f,0x1d50},
{0x7560,0x7560,0x3d71},
{0x7562,0x7562,0xb2d},
{0x7563,0x7563,0x1f9f},
{0x7564,0x7564,0x1f9e},
{0x7565,0x7566,0xb2b},
{0x7567,0x7567,0x42c0},
{0x756a,0x756a,0xce6},
{0x756b,0x756b,0xce5},
{0x756c,0x756c,0x2223},
{0x756d,0x756d,0x46fd},
{0x756e,0x756e,0x42c1},
{0x756f,0x756f,0x2222},
{0x7570,0x7570,0xb2e},
{0x7572,0x7572,0x46fe},
{0x7576,0x7576,0xe85},
{0x7577,0x7577,0x24ae},
{0x7578,0x7578,0xe86},
{0x7579,0x7579,0x24ad},
{0x757a,0x757a,0x3f4f},
{0x757d,0x757d,0x2735},
{0x757e,0x757e,0x297f},
{0x757f,0x757f,0x11ab},
{0x7580,0x7580,0x2bd3},
{0x7583,0x7583,0x3ae5},
{0x7584,0x7584,0x2db2},
{0x7586,0x7586,0x159b},
{0x7587,0x7587,0x159a},
{0x758a,0x758a,0x16cc},
{0x758b,0x758b,0x362},
{0x758c,0x758c,0x1a12},
{0x758d,0x758d,0x46ff},
{0x758e,0x758e,0x42c5},
{0x758f,0x758f,0xb2f},
{0x7590,0x7590,0x2736},
{0x7591,0x7591,0x1032},
{0x7592,0x7592,0x22e},
{0x7594,0x7595,0x18d4},
{0x7598,0x7598,0x1a13},
{0x7599,0x759a,0x657},
{0x759d,0x759d,0x656},
{0x759e,0x759e,0x42c7},
{0x75a2,0x75a3,0x7a8},
{0x75a4,0x75a5,0x7a6},
{0x75a7,0x75a7,0x1b93},
{0x75aa,0x75aa,0x1b94},
{0x75ab,0x75ab,0x7a5},
{0x75b0,0x75b0,0x1d51},
{0x75b1,0x75b1,0x3ead},
{0x75b2,0x75b3,0x939},
{0x75b4,0x75b4,0x42c8},
{0x75b5,0x75b5,0xb32},
{0x75b6,0x75b6,0x1d57},
{0x75b8,0x75b8,0x93f},
{0x75b9,0x75b9,0x93d},
{0x75ba,0x75ba,0x1d58},
{0x75bb,0x75bb,0x1d53},
{0x75bc,0x75bc,0x93c},
{0x75bd,0x75bd,0x93b},
{0x75be,0x75be,0x936},
{0x75bf,0x75bf,0x1d56},
{0x75c0,0x75c0,0x1d55},
{0x75c1,0x75c1,0x1d52},
{0x75c2,0x75c2,0x93e},
{0x75c3,0x75c3,0x3f93},
{0x75c4,0x75c4,0x1d54},
{0x75c5,0x75c5,0x937},
{0x75c7,0x75c7,0x938},
{0x75c8,0x75c8,0x4701},
{0x75ca,0x75ca,0xb33},
{0x75cb,0x75cc,0x1fa3},
{0x75cd,0x75cd,0xb34},
{0x75ce,0x75ce,0x1fa0},
{0x75cf,0x75cf,0x1fa2},
{0x75d0,0x75d0,0x1fa6},
{0x75d1,0x75d1,0x1fa5},
{0x75d2,0x75d2,0x1fa1},
{0x75d4,0x75d5,0xb30},
{0x75d7,0x75d7,0x222b},
{0x75d8,0x75d8,0xceb},
{0x75d9,0x75d9,0xcea},
{0x75da,0x75da,0x2225},
{0x75db,0x75db,0xce8},
{0x75dc,0x75dc,0x43ce},
{0x75dd,0x75dd,0x2228},
{0x75de,0x75de,0xcec},
{0x75df,0x75df,0x2229},
{0x75e0,0x75e0,0xced},
{0x75e1,0x75e1,0x2226},
{0x75e2,0x75e2,0xce7},
{0x75e3,0x75e3,0xce9},
{0x75e4,0x75e4,0x222a},
{0x75e6,0x75e6,0x2227},
{0x75e7,0x75e7,0x2224},
{0x75ed,0x75ed,0x24bb},
{0x75ef,0x75ef,0x24b0},
{0x75f0,0x75f0,0xe88},
{0x75f1,0x75f1,0xe8b},
{0x75f2,0x75f2,0xe8a},
{0x75f3,0x75f3,0xe8f},
{0x75f4,0x75f4,0xe8e},
{0x75f5,0x75f5,0x24bc},
{0x75f6,0x75f6,0x24ba},
{0x75f7,0x75f7,0x24b3},
{0x75f8,0x75f8,0x24b7},
{0x75f9,0x75f9,0x24b6},
{0x75fa,0x75fa,0xe8c},
{0x75fb,0x75fb,0x24b9},
{0x75fc,0x75fc,0x24b5},
{0x75fd,0x75fd,0x24bd},
{0x75fe,0x75fe,0x24b4},
{0x75ff,0x75ff,0xe8d},
{0x7600,0x7600,0xe87},
{0x7601,0x7601,0xe89},
{0x7602,0x7602,0x42c9},
{0x7603,0x7603,0x24b2},
{0x7607,0x7607,0x3f8f},
{0x7608,0x7608,0x2738},
{0x7609,0x7609,0x1036},
{0x760a,0x760a,0x273c},
{0x760b,0x760b,0x1035},
{0x760c,0x760c,0x2739},
{0x760d,0x760d,0x1034},
{0x760f,0x760f,0x24b1},
{0x7610,0x7610,0x24b8},
{0x7611,0x7611,0x273b},
{0x7613,0x7613,0x1037},
{0x7614,0x7614,0x273d},
{0x7615,0x7615,0x273a},
{0x7616,0x7616,0x2737},
{0x7619,0x7619,0x2982},
{0x761a,0x761a,0x2986},
{0x761b,0x761b,0x2988},
{0x761c,0x761c,0x2984},
{0x761d,0x761d,0x2983},
{0x761e,0x761e,0x2981},
{0x761f,0x761f,0x11ae},
{0x7620,0x7620,0x11ac},
{0x7621,0x7622,0x11b1},
{0x7623,0x7623,0x2985},
{0x7624,0x7624,0x11af},
{0x7625,0x7625,0x2980},
{0x7626,0x7626,0x11b0},
{0x7627,0x7627,0x1033},
{0x7628,0x7628,0x2987},
{0x7629,0x7629,0x11ad},
{0x762c,0x762c,0x42ca},
{0x762d,0x762d,0x2bd5},
{0x762f,0x762f,0x2bd4},
{0x7630,0x7630,0x2bdc},
{0x7631,0x7631,0x2bd6},
{0x7632,0x7632,0x2bdb},
{0x7633,0x7633,0x2bd8},
{0x7634,0x7634,0x1302},
{0x7635,0x7635,0x2bda},
{0x7638,0x7638,0x1303},
{0x763a,0x763a,0x1304},
{0x763b,0x763b,0x3e70},
{0x763c,0x763c,0x2bd9},
{0x763d,0x763d,0x2bd7},
{0x7640,0x7640,0x3ee6},
{0x7642,0x7642,0x1415},
{0x7643,0x7643,0x2db3},
{0x7646,0x7646,0x1414},
{0x7647,0x7647,0x2db6},
{0x7648,0x7649,0x2db4},
{0x764c,0x764c,0x1416},
{0x764d,0x764d,0x4702},
{0x764e,0x764e,0x3e72},
{0x764f,0x764f,0x42cc},
{0x7650,0x7650,0x2f63},
{0x7651,0x7651,0x42cb},
{0x7652,0x7652,0x14f9},
{0x7653,0x7653,0x2f64},
{0x7654,0x7654,0x3ef0},
{0x7656,0x7656,0x14f7},
{0x7657,0x7657,0x2f65},
{0x7658,0x7658,0x14f8},
{0x7659,0x7659,0x2f62},
{0x765a,0x765a,0x2f66},
{0x765c,0x765c,0x2f60},
{0x765f,0x765f,0x159c},
{0x7660,0x7660,0x30c5},
{0x7661,0x7661,0x159d},
{0x7662,0x7662,0x1622},
{0x7664,0x7664,0x2f61},
{0x7665,0x7665,0x1623},
{0x7666,0x7666,0x3eaa},
{0x7667,0x7667,0x3fa9},
{0x7669,0x7669,0x167f},
{0x766a,0x766a,0x32e4},
{0x766c,0x766c,0x16ce},
{0x766d,0x766d,0x3395},
{0x766e,0x766e,0x16cd},
{0x766f,0x766f,0x42cd},
{0x7670,0x7670,0x3428},
{0x7671,0x7672,0x1723},
{0x7673,0x7673,0x3aeb},
{0x7674,0x7674,0x4703},
{0x7675,0x7675,0x3543},
{0x7676,0x7676,0x22f},
{0x7678,0x7678,0x7aa},
{0x7679,0x7679,0x1b95},
{0x767a,0x767a,0x4705},
{0x767b,0x767c,0xcee},
{0x767d,0x767d,0x363},
{0x767e,0x767e,0x3f5},
{0x767f,0x767f,0x1810},
{0x7680,0x7680,0x43d3},
{0x7681,0x7681,0x18d6},
{0x7682,0x7682,0x4f4},
{0x7684,0x7684,0x659},
{0x7686,0x7688,0x7ab},
{0x7689,0x7689,0x1fa8},
{0x768a,0x768a,0x1d59},
{0x768b,0x768b,0x940},
{0x768c,0x768c,0x43d4},
{0x768e,0x768e,0xb35},
{0x768f,0x768f,0x1fa7},
{0x7690,0x7690,0x42d0},
{0x7692,0x7692,0x222d},
{0x7693,0x7693,0xcf1},
{0x7695,0x7695,0x222c},
{0x7696,0x7696,0xcf0},
{0x7699,0x7699,0x24be},
{0x769a,0x769a,0x11b3},
{0x769b,0x769b,0x298c},
{0x769c,0x769e,0x2989},
{0x76a1,0x76a1,0x42d5},
{0x76a4,0x76a4,0x2db7},
{0x76a5,0x76a5,0x42d6},
{0x76a6,0x76a6,0x2f67},
{0x76aa,0x76aa,0x31fb},
{0x76ab,0x76ab,0x31fa},
{0x76ad,0x76ad,0x3396},
{0x76ae,0x76ae,0x364},
{0x76af,0x76af,0x1a14},
{0x76b0,0x76b0,0x941},
{0x76b4,0x76b4,0xcf2},
{0x76b5,0x76b5,0x24bf},
{0x76b7,0x76b7,0x42d7},
{0x76b8,0x76b8,0x273e},
{0x76ba,0x76ba,0x11b4},
{0x76bb,0x76bb,0x2bdd},
{0x76bd,0x76bd,0x2f68},
{0x76be,0x76be,0x31fc},
{0x76bf,0x76bf,0x365},
{0x76c2,0x76c2,0x65a},
{0x76c3,0x76c3,0x7b0},
{0x76c4,0x76c4,0x1b96},
{0x76c5,0x76c5,0x7b1},
{0x76c6,0x76c6,0x7af},
{0x76c8,0x76c8,0x7ae},
{0x76c9,0x76c9,0x1d5a},
{0x76ca,0x76ca,0x942},
{0x76cc,0x76cc,0x42d8},
{0x76cd,0x76ce,0x943},
{0x76d2,0x76d2,0xb37},
{0x76d3,0x76d3,0x1fa9},
{0x76d4,0x76d4,0xb36},
{0x76d6,0x76d6,0x3f55},
{0x76da,0x76da,0x222e},
{0x76db,0x76db,0xb38},
{0x76dc,0x76dc,0xcf3},
{0x76dd,0x76dd,0x24c0},
{0x76de,0x76df,0xe90},
{0x76e1,0x76e1,0x1038},
{0x76e3,0x76e3,0x1039},
{0x76e4,0x76e4,0x11b5},
{0x76e5,0x76e5,0x1306},
{0x76e6,0x76e6,0x2bde},
{0x76e7,0x76e7,0x1305},
{0x76e9,0x76e9,0x2db8},
{0x76ea,0x76ea,0x1417},
{0x76ec,0x76ec,0x2f69},
{0x76ed,0x76ed,0x31fd},
{0x76ee,0x76ee,0x366},
{0x76ef,0x76ef,0x4f5},
{0x76f0,0x76f0,0x1a17},
{0x76f1,0x76f1,0x1a16},
{0x76f2,0x76f2,0x65b},
{0x76f3,0x76f3,0x1a15},
{0x76f4,0x76f4,0x65c},
{0x76f5,0x76f5,0x1a18},
{0x76f7,0x76f7,0x1b9c},
{0x76f8,0x76f8,0x7b4},
{0x76f9,0x76f9,0x7b3},
{0x76fa,0x76fa,0x1b9e},
{0x76fb,0x76fb,0x1b9d},
{0x76fc,0x76fc,0x7b8},
{0x76fe,0x76fe,0x7b7},
{0x7701,0x7701,0x7b2},
{0x7703,0x7705,0x1b98},
{0x7707,0x7707,0x7b9},
{0x7708,0x7708,0x1b97},
{0x7709,0x7709,0x7b5},
{0x770a,0x770a,0x1b9b},
{0x770b,0x770b,0x7b6},
{0x770c,0x770c,0x3d62},
{0x770e,0x770f,0x3b02},
{0x7710,0x7710,0x1d5d},
{0x7711,0x7711,0x1d61},
{0x7712,0x7712,0x1d5f},
{0x7713,0x7713,0x1d5e},
{0x7715,0x7715,0x1d62},
{0x7719,0x771a,0x1d63},
{0x771b,0x771b,0x1d5c},
{0x771d,0x771d,0x1d5b},
{0x771e,0x771e,0x42de},
{0x771f,0x7720,0x946},
{0x7722,0x7722,0x1d65},
{0x7723,0x7723,0x1d60},
{0x7724,0x7724,0x3fe8},
{0x7725,0x7725,0x1fb2},
{0x7726,0x7726,0x42df},
{0x7727,0x7727,0x1d66},
{0x7728,0x7728,0x948},
{0x7729,0x7729,0x945},
{0x772b,0x772b,0x3ff3},
{0x772d,0x772d,0x1fac},
{0x772f,0x772f,0x1fab},
{0x7731,0x7732,0x1fad},
{0x7733,0x7733,0x1fb0},
{0x7734,0x7734,0x1faf},
{0x7735,0x7735,0x1fb4},
{0x7736,0x7736,0xb3c},
{0x7737,0x7737,0xb39},
{0x7738,0x7738,0xb3d},
{0x7739,0x7739,0x1faa},
{0x773a,0x773a,0xb3e},
{0x773b,0x773b,0x1fb3},
{0x773c,0x773c,0xb3b},
{0x773d,0x773d,0x1fb1},
{0x773e,0x773e,0xb3a},
{0x7740,0x7740,0x42e0},
{0x7743,0x7743,0x470a},
{0x7744,0x7744,0x2231},
{0x7745,0x7745,0x2233},
{0x7746,0x7747,0x222f},
{0x774a,0x774a,0x2234},
{0x774b,0x774c,0x2236},
{0x774d,0x774d,0x2232},
{0x774e,0x774e,0x2235},
{0x774f,0x774f,0xcf4},
{0x7752,0x7752,0x24c4},
{0x7754,0x7754,0x24c9},
{0x7755,0x7755,0x24c1},
{0x7756,0x7756,0x24c5},
{0x7758,0x7758,0x42e3},
{0x7759,0x7759,0x24ca},
{0x775a,0x775a,0x24c6},
{0x775b,0x775b,0xe92},
{0x775c,0x775c,0xe9a},
{0x775e,0x775e,0xe95},
{0x775f,0x7760,0x24c2},
{0x7761,0x7761,0x103d},
{0x7762,0x7762,0xe9d},
{0x7763,0x7763,0xe96},
{0x7765,0x7765,0xe9b},
{0x7766,0x7766,0xe94},
{0x7767,0x7767,0x24c8},
{0x7768,0x7768,0xe9c},
{0x7769,0x7769,0x24c7},
{0x776a,0x776a,0xe98},
{0x776b,0x776b,0xe93},
{0x776c,0x776c,0xe99},
{0x776d,0x776d,0x24cb},
{0x776e,0x776e,0x2743},
{0x776f,0x776f,0x2745},
{0x7772,0x7772,0x396b},
{0x7777,0x7777,0x46a9},
{0x7778,0x7778,0x3b00},
{0x7779,0x7779,0xe97},
{0x777a,0x777a,0x4317},
{0x777b,0x777b,0x3b04},
{0x777c,0x777c,0x2740},
{0x777d,0x777d,0x103b},
{0x777e,0x777e,0x2746},
{0x777f,0x777f,0x103c},
{0x7780,0x7780,0x2744},
{0x7781,0x7781,0x273f},
{0x7782,0x7782,0x2742},
{0x7783,0x7783,0x2747},
{0x7784,0x7784,0x103a},
{0x7785,0x7785,0x2741},
{0x7787,0x7787,0x11b7},
{0x7788,0x7788,0x2990},
{0x7789,0x7789,0x298f},
{0x778b,0x778b,0x11ba},
{0x778c,0x778c,0x11b8},
{0x778d,0x778d,0x298d},
{0x778e,0x778e,0x11b6},
{0x778f,0x778f,0x298e},
{0x7791,0x7791,0x11b9},
{0x7793,0x7793,0x382d},
{0x7795,0x7795,0x2be6},
{0x7797,0x7797,0x2be8},
{0x7798,0x7798,0x470e},
{0x7799,0x7799,0x2be7},
{0x779a,0x779a,0x2bdf},
{0x779b,0x779b,0x2be3},
{0x779c,0x779c,0x2be2},
{0x779d,0x779d,0x2be0},
{0x779e,0x779f,0x1308},
{0x77a0,0x77a0,0x1307},
{0x77a1,0x77a1,0x2be1},
{0x77a2,0x77a3,0x2be4},
{0x77a5,0x77a5,0x130a},
{0x77a7,0x77a7,0x141c},
{0x77a8,0x77a8,0x2dc0},
{0x77aa,0x77aa,0x1419},
{0x77ab,0x77ab,0x2dba},
{0x77ac,0x77ac,0x141b},
{0x77ad,0x77ad,0x141d},
{0x77af,0x77af,0x42e5},
{0x77b0,0x77b0,0x141a},
{0x77b1,0x77b1,0x2dbf},
{0x77b2,0x77b2,0x2dbb},
{0x77b3,0x77b3,0x1418},
{0x77b4,0x77b4,0x2dbe},
{0x77b5,0x77b5,0x2db9},
{0x77b6,0x77b6,0x2dbd},
{0x77b7,0x77b7,0x2dbc},
{0x77b9,0x77b9,0x3e73},
{0x77ba,0x77ba,0x2f6b},
{0x77bb,0x77bc,0x14fc},
{0x77bd,0x77bd,0x14fa},
{0x77be,0x77be,0x4711},
{0x77bf,0x77bf,0x14fb},
{0x77c2,0x77c2,0x2f6a},
{0x77c3,0x77c3,0x3bb1},
{0x77c4,0x77c4,0x30c8},
{0x77c5,0x77c5,0x41c1},
{0x77c7,0x77c7,0x159e},
{0x77c9,0x77ca,0x30c6},
{0x77cb,0x77cb,0x4712},
{0x77cc,0x77cc,0x31fe},
{0x77cd,0x77cd,0x3201},
{0x77ce,0x77cf,0x31ff},
{0x77d0,0x77d0,0x32e5},
{0x77d3,0x77d3,0x1680},
{0x77d4,0x77d4,0x3429},
{0x77d5,0x77d5,0x348f},
{0x77d7,0x77d7,0x1725},
{0x77d8,0x77d9,0x34d3},
{0x77da,0x77da,0x1753},
{0x77db,0x77db,0x367},
{0x77dc,0x77dc,0x7ba},
{0x77de,0x77de,0x2238},
{0x77e0,0x77e0,0x24cc},
{0x77e2,0x77e2,0x368},
{0x77e3,0x77e3,0x4f6},
{0x77e5,0x77e5,0x65d},
{0x77e6,0x77e6,0x4081},
{0x77e7,0x77e8,0x1b9f},
{0x77e9,0x77e9,0x949},
{0x77ec,0x77ec,0x2239},
{0x77ed,0x77ed,0xcf5},
{0x77ee,0x77ee,0xe9e},
{0x77ef,0x77ef,0x141e},
{0x77f0,0x77f0,0x2dc1},
{0x77f1,0x77f1,0x30c9},
{0x77f2,0x77f2,0x3202},
{0x77f3,0x77f3,0x369},
{0x77f4,0x77f4,0x42e9},
{0x77f7,0x77f7,0x1a1e},
{0x77f8,0x77f8,0x1a19},
{0x77f9,0x77f9,0x1a1b},
{0x77fa,0x77fa,0x1a1d},
{0x77fb,0x77fb,0x1a1c},
{0x77fc,0x77fc,0x1a1a},
{0x77fd,0x77fd,0x65e},
{0x77fe,0x77fe,0x3fd5},
{0x7802,0x7802,0x7bb},
{0x7803,0x7803,0x1ba9},
{0x7805,0x7805,0x1ba4},
{0x7806,0x7806,0x1ba1},
{0x7808,0x7808,0x3e46},
{0x7809,0x7809,0x1ba8},
{0x780c,0x780d,0x7bd},
{0x780e,0x780e,0x1ba7},
{0x780f,0x780f,0x1ba6},
{0x7810,0x7810,0x1ba5},
{0x7811,0x7812,0x1ba2},
{0x7813,0x7813,0x1baa},
{0x7814,0x7814,0x7bc},
{0x7818,0x7818,0x4713},
{0x781c,0x781c,0x4714},
{0x781d,0x781d,0x94d},
{0x781e,0x781e,0x3b13},
{0x781f,0x781f,0x953},
{0x7820,0x7820,0x952},
{0x7821,0x7821,0x1d6f},
{0x7822,0x7822,0x1d69},
{0x7823,0x7823,0x1d67},
{0x7825,0x7825,0x950},
{0x7826,0x7826,0x1fbb},
{0x7827,0x7827,0x94b},
{0x7828,0x7828,0x1d6c},
{0x7829,0x7829,0x1d70},
{0x782a,0x782a,0x1d72},
{0x782b,0x782b,0x1d6e},
{0x782c,0x782c,0x1d68},
{0x782d,0x782d,0x951},
{0x782e,0x782e,0x1d6d},
{0x782f,0x782f,0x1d6b},
{0x7830,0x7830,0x94a},
{0x7831,0x7831,0x1d73},
{0x7832,0x7832,0x954},
{0x7833,0x7833,0x1d71},
{0x7834,0x7834,0x94e},
{0x7835,0x7835,0x1d6a},
{0x7837,0x7837,0x94f},
{0x7838,0x7838,0x94c},
{0x7839,0x7839,0x43d6},
{0x783c,0x783c,0x401a},
{0x783d,0x783d,0x3c6a},
{0x7842,0x7842,0x3ac4},
{0x7843,0x7843,0xb40},
{0x7844,0x7844,0x3c2b},
{0x7845,0x7845,0x1fbc},
{0x7847,0x7847,0x4715},
{0x7848,0x7848,0x1fb5},
{0x7849,0x7849,0x1fb7},
{0x784a,0x784a,0x1fb9},
{0x784b,0x784b,0x3c6d},
{0x784c,0x784c,0x1fba},
{0x784d,0x784d,0x1fb8},
{0x784e,0x784e,0xb41},
{0x7850,0x7850,0x1fbd},
{0x7851,0x7851,0x4716},
{0x7852,0x7852,0x1fb6},
{0x7853,0x7853,0x3f95},
{0x7854,0x7854,0x3c6b},
{0x785c,0x785c,0x223d},
{0x785d,0x785d,0xcf6},
{0x785e,0x785e,0x2245},
{0x7860,0x7860,0x223a},
{0x7862,0x7862,0x2246},
{0x7864,0x7865,0x223b},
{0x7866,0x7866,0x4717},
{0x7868,0x7868,0x2244},
{0x7869,0x7869,0x2243},
{0x786a,0x786a,0x2240},
{0x786b,0x786b,0xb3f},
{0x786c,0x786c,0xcf7},
{0x786d,0x786d,0x223e},
{0x786e,0x786e,0x2241},
{0x786f,0x786f,0xcf8},
{0x7870,0x7870,0x2242},
{0x7871,0x7871,0x223f},
{0x7879,0x7879,0x24d7},
{0x787a,0x787a,0x3ee9},
{0x787b,0x787b,0x24db},
{0x787c,0x787c,0xea5},
{0x787e,0x787e,0x274d},
{0x787f,0x787f,0xea8},
{0x7880,0x7880,0x24d9},
{0x7881,0x7881,0x36e8},
{0x7883,0x7883,0x24d6},
{0x7884,0x7884,0x24d1},
{0x7885,0x7886,0x24d3},
{0x7887,0x7887,0x24cd},
{0x7888,0x7888,0x3b15},
{0x7889,0x7889,0xea4},
{0x788c,0x788c,0xea3},
{0x788d,0x788d,0x3b14},
{0x788e,0x788e,0xe9f},
{0x788f,0x788f,0x24d0},
{0x7891,0x7891,0xea6},
{0x7893,0x7893,0xea7},
{0x7894,0x7894,0x24cf},
{0x7895,0x7895,0x24d2},
{0x7896,0x7896,0x24da},
{0x7897,0x7898,0xea1},
{0x7899,0x7899,0x24d8},
{0x789a,0x789a,0x24ce},
{0x789e,0x789e,0x274f},
{0x789f,0x789f,0x103f},
{0x78a0,0x78a0,0x2751},
{0x78a1,0x78a1,0x24d5},
{0x78a2,0x78a2,0x2753},
{0x78a3,0x78a3,0x1043},
{0x78a4,0x78a4,0x2754},
{0x78a5,0x78a5,0x2750},
{0x78a7,0x78a7,0x1040},
{0x78a8,0x78a8,0x274c},
{0x78a9,0x78a9,0x1042},
{0x78aa,0x78aa,0x2749},
{0x78ab,0x78ab,0x274e},
{0x78ac,0x78ac,0x2752},
{0x78ad,0x78ad,0x274b},
{0x78af,0x78af,0x42ec},
{0x78b0,0x78b0,0xea0},
{0x78b1,0x78b1,0x42f4},
{0x78b2,0x78b2,0x2748},
{0x78b3,0x78b3,0x1041},
{0x78b4,0x78b4,0x274a},
{0x78b6,0x78b6,0x3c6c},
{0x78b8,0x78b8,0x4571},
{0x78b9,0x78b9,0x3c63},
{0x78ba,0x78ba,0x11bd},
{0x78bb,0x78bb,0x2992},
{0x78bc,0x78bc,0x11c1},
{0x78be,0x78be,0x11bf},
{0x78c1,0x78c1,0x103e},
{0x78c3,0x78c4,0x2999},
{0x78c5,0x78c5,0x11bc},
{0x78c7,0x78c7,0x42ed},
{0x78c8,0x78c8,0x2998},
{0x78c9,0x78c9,0x299b},
{0x78ca,0x78ca,0x11be},
{0x78cb,0x78cb,0x11bb},
{0x78cc,0x78cc,0x2994},
{0x78cd,0x78cd,0x2991},
{0x78ce,0x78ce,0x2996},
{0x78cf,0x78cf,0x2993},
{0x78d0,0x78d0,0x11c2},
{0x78d1,0x78d1,0x2995},
{0x78d2,0x78d2,0x3b16},
{0x78d3,0x78d3,0x42ee},
{0x78d4,0x78d4,0x2997},
{0x78d5,0x78d5,0x11c0},
{0x78d7,0x78d7,0x42f2},
{0x78d8,0x78d8,0x3f8c},
{0x78da,0x78da,0x130c},
{0x78db,0x78db,0x2bef},
{0x78dd,0x78dd,0x2be9},
{0x78de,0x78de,0x2bed},
{0x78df,0x78e0,0x2bf3},
{0x78e1,0x78e2,0x2bf0},
{0x78e3,0x78e3,0x2bee},
{0x78e5,0x78e5,0x2beb},
{0x78e7,0x78e7,0x130e},
{0x78e8,0x78e8,0x130b},
{0x78e9,0x78e9,0x2bea},
{0x78ea,0x78ea,0x2bec},
{0x78ec,0x78ec,0x130d},
{0x78ed,0x78ed,0x2bf2},
{0x78ee,0x78ee,0x3a81},
{0x78ef,0x78ef,0x1422},
{0x78f0,0x78f0,0x3b3a},
{0x78f1,0x78f1,0x40be},
{0x78f2,0x78f2,0x2dc8},
{0x78f3,0x78f3,0x2dc2},
{0x78f4,0x78f4,0x1421},
{0x78f5,0x78f5,0x38b3},
{0x78f7,0x78f7,0x141f},
{0x78f9,0x78f9,0x2dca},
{0x78fa,0x78fa,0x1420},
{0x78fb,0x78fc,0x2dc5},
{0x78fd,0x78fd,0x2dc3},
{0x78fe,0x78fe,0x2dcb},
{0x78ff,0x78ff,0x2dc7},
{0x7901,0x7901,0x1423},
{0x7902,0x7902,0x2dc4},
{0x7904,0x7904,0x2dcc},
{0x7905,0x7905,0x2dc9},
{0x7906,0x7906,0x3fcf},
{0x7909,0x7909,0x2f6f},
{0x790c,0x790c,0x2f6c},
{0x790e,0x790e,0x14fe},
{0x7910,0x7910,0x2f70},
{0x7911,0x7911,0x2f72},
{0x7912,0x7912,0x2f71},
{0x7913,0x7914,0x2f6d},
{0x7917,0x7917,0x30ce},
{0x7919,0x7919,0x159f},
{0x791b,0x791b,0x30cb},
{0x791c,0x791c,0x30cd},
{0x791d,0x791d,0x30ca},
{0x791e,0x791e,0x30cf},
{0x7921,0x7921,0x30cc},
{0x7923,0x7923,0x3204},
{0x7924,0x7924,0x3207},
{0x7925,0x7925,0x3203},
{0x7926,0x7926,0x1624},
{0x7927,0x7928,0x3205},
{0x7929,0x7929,0x3208},
{0x792a,0x792a,0x1625},
{0x792b,0x792b,0x1627},
{0x792c,0x792c,0x1626},
{0x792d,0x792d,0x32e6},
{0x792e,0x792e,0x42f0},
{0x792f,0x792f,0x32e8},
{0x7931,0x7931,0x32e7},
{0x7932,0x7932,0x471b},
{0x7933,0x7933,0x471a},
{0x7934,0x7934,0x42f3},
{0x7935,0x7935,0x3397},
{0x7936,0x7936,0x3783},
{0x7938,0x7938,0x3490},
{0x7939,0x7939,0x34d5},
{0x793a,0x793a,0x36a},
{0x793b,0x793b,0x44f7},
{0x793c,0x793c,0x4300},
{0x793d,0x793d,0x18d7},
{0x793e,0x793e,0x65f},
{0x793f,0x793f,0x1a20},
{0x7940,0x7941,0x660},
{0x7942,0x7942,0x1a1f},
{0x7944,0x7944,0x1baf},
{0x7945,0x7945,0x1bae},
{0x7946,0x7946,0x7bf},
{0x7947,0x7947,0x7c2},
{0x7948,0x7948,0x7c1},
{0x7949,0x7949,0x7c0},
{0x794a,0x794a,0x1bab},
{0x794b,0x794b,0x1bad},
{0x794c,0x794c,0x1bac},
{0x794f,0x794f,0x1d76},
{0x7950,0x7950,0x956},
{0x7951,0x7951,0x1d7a},
{0x7952,0x7952,0x1d79},
{0x7953,0x7953,0x1d78},
{0x7954,0x7954,0x1d74},
{0x7955,0x7955,0x955},
{0x7956,0x7956,0x959},
{0x7957,0x7957,0x95c},
{0x7958,0x7958,0x37e5},
{0x7959,0x7959,0x3b18},
{0x795a,0x795a,0x95d},
{0x795b,0x795b,0x1d75},
{0x795c,0x795c,0x1d77},
{0x795d,0x795d,0x95b},
{0x795e,0x795e,0x95a},
{0x795f,0x795f,0x958},
{0x7960,0x7960,0x957},
{0x7961,0x7961,0x1fc4},
{0x7962,0x7962,0x3e7d},
{0x7963,0x7963,0x1fc2},
{0x7964,0x7964,0x1fbe},
{0x7965,0x7965,0xb42},
{0x7967,0x7967,0x1fbf},
{0x7968,0x7968,0xb43},
{0x7969,0x796a,0x1fc0},
{0x796b,0x796b,0x1fc3},
{0x796d,0x796d,0xb44},
{0x7970,0x7970,0x224a},
{0x7971,0x7971,0x4168},
{0x7972,0x7972,0x2249},
{0x7973,0x7973,0x2248},
{0x7974,0x7974,0x2247},
{0x7979,0x7979,0x24df},
{0x797a,0x797a,0xea9},
{0x797c,0x797c,0x24dc},
{0x797d,0x797d,0x24de},
{0x797e,0x797e,0x3e26},
{0x797f,0x797f,0xeaa},
{0x7980,0x7980,0x42fc},
{0x7981,0x7981,0xeab},
{0x7982,0x7982,0x24dd},
{0x7983,0x7983,0x3df6},
{0x7986,0x7986,0x42f9},
{0x7987,0x7987,0x4588},
{0x7988,0x7988,0x275d},
{0x798a,0x798b,0x2756},
{0x798d,0x798d,0x1046},
{0x798e,0x798f,0x1044},
{0x7990,0x7990,0x275f},
{0x7991,0x7991,0x471d},
{0x7992,0x7992,0x275e},
{0x7993,0x7993,0x275b},
{0x7994,0x7994,0x275a},
{0x7995,0x7995,0x2759},
{0x7996,0x7996,0x2758},
{0x7997,0x7997,0x275c},
{0x7998,0x7998,0x2755},
{0x7999,0x7999,0x43fc},
{0x799a,0x799a,0x299c},
{0x799b,0x799b,0x29a1},
{0x799c,0x799c,0x299f},
{0x799d,0x799d,0x42fe},
{0x799f,0x799f,0x395e},
{0x79a0,0x79a0,0x299e},
{0x79a1,0x79a1,0x299d},
{0x79a2,0x79a2,0x29a0},
{0x79a4,0x79a4,0x2bf5},
{0x79a5,0x79a5,0x3b1e},
{0x79a6,0x79a6,0x130f},
{0x79a7,0x79a7,0x1424},
{0x79a8,0x79a8,0x2dce},
{0x79a9,0x79a9,0x4301},
{0x79aa,0x79aa,0x1425},
{0x79ab,0x79ab,0x2dcd},
{0x79ac,0x79ac,0x2f74},
{0x79ad,0x79ad,0x2f73},
{0x79ae,0x79ae,0x14ff},
{0x79b0,0x79b0,0x30d0},
{0x79b1,0x79b1,0x15a0},
{0x79b2,0x79b2,0x3209},
{0x79b3,0x79b3,0x16cf},
{0x79b4,0x79b4,0x3398},
{0x79b6,0x79b6,0x3492},
{0x79b7,0x79b7,0x3491},
{0x79b8,0x79b8,0x17b2},
{0x79b9,0x79ba,0x7c3},
{0x79bb,0x79bb,0x1fc5},
{0x79bd,0x79bd,0xead},
{0x79be,0x79be,0x36b},
{0x79bf,0x79bf,0x4f9},
{0x79c0,0x79c0,0x4f8},
{0x79c1,0x79c1,0x4f7},
{0x79c4,0x79c4,0x3ccd},
{0x79c5,0x79c5,0x1a21},
{0x79c6,0x79c6,0x4305},
{0x79c8,0x79c8,0x663},
{0x79c9,0x79c9,0x662},
{0x79cb,0x79cb,0x7c7},
{0x79cc,0x79cc,0x4233},
{0x79cd,0x79cd,0x1bb1},
{0x79ce,0x79ce,0x1bb4},
{0x79cf,0x79cf,0x1bb2},
{0x79d1,0x79d2,0x7c5},
{0x79d4,0x79d4,0x4307},
{0x79d5,0x79d5,0x1bb0},
{0x79d6,0x79d6,0x1bb3},
{0x79d8,0x79d8,0x964},
{0x79dc,0x79dc,0x1d81},
{0x79dd,0x79dd,0x1d83},
{0x79de,0x79de,0x1d82},
{0x79df,0x79df,0x961},
{0x79e0,0x79e0,0x1d7d},
{0x79e2,0x79e2,0x3c5a},
{0x79e3,0x79e3,0x95f},
{0x79e4,0x79e4,0x95e},
{0x79e6,0x79e6,0x962},
{0x79e7,0x79e7,0x960},
{0x79e9,0x79e9,0x963},
{0x79ea,0x79ea,0x1d80},
{0x79eb,0x79ec,0x1d7b},
{0x79ed,0x79ed,0x1d7f},
{0x79ee,0x79ee,0x1d7e},
{0x79f1,0x79f1,0x3b27},
{0x79f4,0x79f4,0x3b22},
{0x79f6,0x79f7,0x1fc8},
{0x79f8,0x79f8,0x1fc7},
{0x79fa,0x79fa,0x1fc6},
{0x79fb,0x79fb,0xb45},
{0x7a00,0x7a00,0xcfd},
{0x7a02,0x7a02,0x224b},
{0x7a03,0x7a03,0x224d},
{0x7a04,0x7a04,0x224f},
{0x7a05,0x7a05,0xcfc},
{0x7a06,0x7a06,0x471e},
{0x7a08,0x7a08,0xcfa},
{0x7a0a,0x7a0a,0x224c},
{0x7a0b,0x7a0b,0xcfb},
{0x7a0c,0x7a0c,0x224e},
{0x7a0d,0x7a0d,0xcf9},
{0x7a10,0x7a10,0x24e9},
{0x7a11,0x7a11,0x24e0},
{0x7a12,0x7a12,0x24e3},
{0x7a13,0x7a13,0x24e7},
{0x7a14,0x7a14,0xeb1},
{0x7a15,0x7a15,0x24e5},
{0x7a17,0x7a17,0x24e4},
{0x7a18,0x7a19,0x24e1},
{0x7a1a,0x7a1a,0xeaf},
{0x7a1b,0x7a1b,0x24e8},
{0x7a1c,0x7a1c,0xeae},
{0x7a1e,0x7a1e,0xeb3},
{0x7a1f,0x7a1f,0xeb2},
{0x7a20,0x7a20,0xeb0},
{0x7a22,0x7a22,0x24e6},
{0x7a26,0x7a26,0x2765},
{0x7a28,0x7a28,0x2764},
{0x7a2b,0x7a2b,0x2760},
{0x7a2d,0x7a2d,0x3fda},
{0x7a2e,0x7a2e,0x1047},
{0x7a2f,0x7a2f,0x2763},
{0x7a30,0x7a30,0x2762},
{0x7a31,0x7a31,0x1048},
{0x7a37,0x7a37,0x11c7},
{0x7a39,0x7a39,0x29a3},
{0x7a3a,0x7a3a,0x3b21},
{0x7a3b,0x7a3b,0x11c8},
{0x7a3c,0x7a3c,0x11c4},
{0x7a3d,0x7a3d,0x11c6},
{0x7a3e,0x7a3e,0x3f8b},
{0x7a3f,0x7a3f,0x11c3},
{0x7a40,0x7a40,0x11c5},
{0x7a43,0x7a43,0x396c},
{0x7a44,0x7a44,0x2bf6},
{0x7a45,0x7a45,0x3df1},
{0x7a46,0x7a46,0x1312},
{0x7a47,0x7a47,0x2bf8},
{0x7a48,0x7a48,0x2bf7},
{0x7a49,0x7a49,0x3733},
{0x7a4a,0x7a4a,0x2761},
{0x7a4b,0x7a4b,0x1314},
{0x7a4c,0x7a4c,0x1313},
{0x7a4d,0x7a4e,0x1310},
{0x7a54,0x7a54,0x2dd3},
{0x7a56,0x7a56,0x2dd1},
{0x7a57,0x7a57,0x1426},
{0x7a58,0x7a58,0x2dd2},
{0x7a5a,0x7a5a,0x2dd4},
{0x7a5b,0x7a5b,0x2dd0},
{0x7a5c,0x7a5c,0x2dcf},
{0x7a5f,0x7a5f,0x2f75},
{0x7a60,0x7a60,0x1502},
{0x7a61,0x7a62,0x1500},
{0x7a65,0x7a65,0x3736},
{0x7a67,0x7a68,0x30d1},
{0x7a69,0x7a69,0x15a2},
{0x7a6b,0x7a6b,0x15a1},
{0x7a6c,0x7a6d,0x320b},
{0x7a6e,0x7a6e,0x320a},
{0x7a70,0x7a71,0x3399},
{0x7a74,0x7a74,0x36c},
{0x7a75,0x7a75,0x1811},
{0x7a76,0x7a76,0x4fa},
{0x7a78,0x7a78,0x1a22},
{0x7a79,0x7a79,0x665},
{0x7a7a,0x7a7a,0x664},
{0x7a7b,0x7a7b,0x1a23},
{0x7a7d,0x7a7d,0x3737},
{0x7a7e,0x7a7e,0x1bb6},
{0x7a7f,0x7a7f,0x7c8},
{0x7a80,0x7a80,0x1bb5},
{0x7a81,0x7a81,0x7c9},
{0x7a83,0x7a83,0x3d7d},
{0x7a84,0x7a84,0x965},
{0x7a85,0x7a85,0x1d86},
{0x7a86,0x7a86,0x1d84},
{0x7a87,0x7a87,0x1d8a},
{0x7a88,0x7a88,0x966},
{0x7a89,0x7a89,0x1d85},
{0x7a8a,0x7a8a,0x1d89},
{0x7a8b,0x7a8c,0x1d87},
{0x7a8f,0x7a8f,0x1fca},
{0x7a90,0x7a90,0x1fcc},
{0x7a91,0x7a91,0x43d8},
{0x7a92,0x7a92,0xb46},
{0x7a94,0x7a94,0x1fcb},
{0x7a95,0x7a95,0xb47},
{0x7a96,0x7a96,0xd00},
{0x7a97,0x7a97,0xcff},
{0x7a98,0x7a98,0xcfe},
{0x7a99,0x7a99,0x2250},
{0x7a9e,0x7a9e,0x24ec},
{0x7a9f,0x7aa0,0xeb4},
{0x7aa2,0x7aa2,0x24eb},
{0x7aa3,0x7aa3,0x24ea},
{0x7aa8,0x7aa8,0x2766},
{0x7aa9,0x7aa9,0x104a},
{0x7aaa,0x7aaa,0x1049},
{0x7aab,0x7aac,0x2767},
{0x7aae,0x7aae,0x11ca},
{0x7aaf,0x7aaf,0x11c9},
{0x7ab0,0x7ab0,0x373a},
{0x7ab1,0x7ab1,0x2bfc},
{0x7ab2,0x7ab2,0x29a4},
{0x7ab3,0x7ab3,0x29a6},
{0x7ab4,0x7ab4,0x29a5},
{0x7ab5,0x7ab5,0x2bfb},
{0x7ab6,0x7ab6,0x2bf9},
{0x7ab7,0x7ab7,0x2bfd},
{0x7ab8,0x7ab8,0x2bfa},
{0x7aba,0x7aba,0x1315},
{0x7abb,0x7abb,0x3739},
{0x7abc,0x7abc,0x4721},
{0x7abe,0x7abe,0x2dd5},
{0x7abf,0x7abf,0x1427},
{0x7ac0,0x7ac1,0x2dd6},
{0x7ac2,0x7ac3,0x373b},
{0x7ac4,0x7ac5,0x1503},
{0x7ac7,0x7ac7,0x1628},
{0x7ac8,0x7ac8,0x3d7f},
{0x7ac9,0x7ac9,0x4570},
{0x7aca,0x7aca,0x16ff},
{0x7acb,0x7acb,0x36d},
{0x7acf,0x7acf,0x4724},
{0x7ad1,0x7ad1,0x1bb7},
{0x7ad3,0x7ad3,0x3f8a},
{0x7ad8,0x7ad8,0x1d8b},
{0x7ad9,0x7ad9,0x967},
{0x7ada,0x7ada,0x3740},
{0x7adb,0x7adb,0x4725},
{0x7adc,0x7adc,0x3951},
{0x7add,0x7add,0x3741},
{0x7adf,0x7adf,0xbde},
{0x7ae0,0x7ae0,0xbdd},
{0x7ae2,0x7ae2,0x3b33},
{0x7ae3,0x7ae3,0xd02},
{0x7ae4,0x7ae4,0x2252},
{0x7ae5,0x7ae5,0xd01},
{0x7ae6,0x7ae6,0x2251},
{0x7ae7,0x7ae7,0x385d},
{0x7ae9,0x7ae9,0x3831},
{0x7aea,0x7aea,0x3742},
{0x7aeb,0x7aeb,0x24ed},
{0x7aed,0x7aed,0x104b},
{0x7aee,0x7aee,0x2769},
{0x7aef,0x7aef,0x104c},
{0x7af6,0x7af6,0x1629},
{0x7af7,0x7af7,0x320d},
{0x7af9,0x7af9,0x3f6},
{0x7afa,0x7afa,0x666},
{0x7afb,0x7afb,0x1a24},
{0x7afd,0x7afd,0x7cb},
{0x7afe,0x7afe,0x3b3d},
{0x7aff,0x7aff,0x7ca},
{0x7b00,0x7b01,0x1bb8},
{0x7b04,0x7b04,0x1d8d},
{0x7b05,0x7b05,0x1d8f},
{0x7b06,0x7b06,0x968},
{0x7b08,0x7b08,0x1d91},
{0x7b09,0x7b09,0x1d94},
{0x7b0a,0x7b0a,0x1d92},
{0x7b0b,0x7b0b,0x3746},
{0x7b0c,0x7b0c,0x3b63},
{0x7b0e,0x7b0e,0x1d93},
{0x7b0f,0x7b0f,0x1d90},
{0x7b10,0x7b10,0x1d8c},
{0x7b11,0x7b11,0x969},
{0x7b12,0x7b12,0x1d95},
{0x7b13,0x7b13,0x1d8e},
{0x7b14,0x7b14,0x3f77},
{0x7b18,0x7b18,0x1fd5},
{0x7b19,0x7b19,0xb4d},
{0x7b1a,0x7b1a,0x1fde},
{0x7b1b,0x7b1b,0xb4a},
{0x7b1d,0x7b1d,0x1fd7},
{0x7b1e,0x7b1e,0xb4e},
{0x7b1f,0x7b1f,0x3f11},
{0x7b20,0x7b20,0xb48},
{0x7b22,0x7b22,0x1fd2},
{0x7b23,0x7b23,0x1fdf},
{0x7b24,0x7b24,0x1fd3},
{0x7b25,0x7b25,0x1fd0},
{0x7b26,0x7b26,0xb4c},
{0x7b27,0x7b27,0x3b5f},
{0x7b28,0x7b28,0xb49},
{0x7b29,0x7b29,0x3748},
{0x7b2a,0x7b2a,0x1fd6},
{0x7b2b,0x7b2b,0x1fd9},
{0x7b2c,0x7b2c,0xb4b},
{0x7b2d,0x7b2d,0x1fda},
{0x7b2e,0x7b2e,0xb4f},
{0x7b2f,0x7b2f,0x1fdb},
{0x7b30,0x7b30,0x1fd1},
{0x7b31,0x7b31,0x1fd8},
{0x7b32,0x7b32,0x1fdc},
{0x7b33,0x7b33,0x1fd4},
{0x7b34,0x7b34,0x1fcf},
{0x7b35,0x7b35,0x1fcd},
{0x7b38,0x7b38,0x1fdd},
{0x7b39,0x7b39,0x3d6e},
{0x7b3b,0x7b3b,0x1fce},
{0x7b40,0x7b40,0x2259},
{0x7b42,0x7b42,0x3ded},
{0x7b43,0x7b43,0x3e25},
{0x7b44,0x7b44,0x2255},
{0x7b45,0x7b45,0x225b},
{0x7b46,0x7b46,0xd05},
{0x7b47,0x7b47,0x2254},
{0x7b48,0x7b48,0x2256},
{0x7b49,0x7b49,0xd03},
{0x7b4a,0x7b4a,0x2253},
{0x7b4b,0x7b4b,0xd0a},
{0x7b4c,0x7b4c,0x2257},
{0x7b4d,0x7b4d,0xd09},
{0x7b4e,0x7b4e,0x2258},
{0x7b4f,0x7b4f,0xd0b},
{0x7b50,0x7b50,0xd06},
{0x7b51,0x7b51,0xd0c},
{0x7b52,0x7b52,0xd07},
{0x7b54,0x7b54,0xd08},
{0x7b55,0x7b55,0x3747},
{0x7b56,0x7b56,0xd04},
{0x7b58,0x7b58,0x225a},
{0x7b60,0x7b60,0xeb8},
{0x7b61,0x7b61,0x24f8},
{0x7b62,0x7b62,0x4727},
{0x7b63,0x7b63,0x24fb},
{0x7b64,0x7b64,0x24ef},
{0x7b65,0x7b65,0x24f4},
{0x7b66,0x7b66,0x24ee},
{0x7b67,0x7b67,0xeba},
{0x7b69,0x7b69,0x24f2},
{0x7b6c,0x7b6c,0x4728},
{0x7b6d,0x7b6d,0x24f0},
{0x7b6e,0x7b6e,0xeb9},
{0x7b6f,0x7b6f,0x374c},
{0x7b70,0x7b70,0x24f7},
{0x7b71,0x7b71,0x24f6},
{0x7b72,0x7b72,0x24f3},
{0x7b73,0x7b73,0x24f5},
{0x7b74,0x7b74,0x24f1},
{0x7b75,0x7b75,0x1050},
{0x7b76,0x7b76,0x24fa},
{0x7b77,0x7b77,0xeb6},
{0x7b78,0x7b78,0x24f9},
{0x7b7b,0x7b7b,0x4729},
{0x7b82,0x7b82,0x2779},
{0x7b84,0x7b84,0x1057},
{0x7b85,0x7b85,0x2774},
{0x7b87,0x7b87,0x1056},
{0x7b88,0x7b88,0x276a},
{0x7b8a,0x7b8a,0x276c},
{0x7b8b,0x7b8b,0x104f},
{0x7b8c,0x7b8c,0x2771},
{0x7b8d,0x7b8d,0x2770},
{0x7b8e,0x7b8e,0x2773},
{0x7b8f,0x7b8f,0x1054},
{0x7b90,0x7b90,0x276e},
{0x7b91,0x7b91,0x276d},
{0x7b92,0x7b92,0x3752},
{0x7b94,0x7b94,0x1053},
{0x7b95,0x7b95,0x104e},
{0x7b96,0x7b96,0x276f},
{0x7b97,0x7b97,0x1051},
{0x7b98,0x7b98,0x2775},
{0x7b99,0x7b99,0x2777},
{0x7b9b,0x7b9b,0x2772},
{0x7b9c,0x7b9c,0x276b},
{0x7b9d,0x7b9d,0x1052},
{0x7ba0,0x7ba0,0x11d2},
{0x7ba1,0x7ba1,0x104d},
{0x7ba2,0x7ba2,0x374b},
{0x7ba3,0x7ba3,0x3f14},
{0x7ba4,0x7ba4,0x2778},
{0x7bac,0x7bac,0x29aa},
{0x7bad,0x7bad,0x11cb},
{0x7baf,0x7baf,0x29ac},
{0x7bb1,0x7bb1,0x11cc},
{0x7bb2,0x7bb2,0x461c},
{0x7bb4,0x7bb4,0x11ce},
{0x7bb5,0x7bb5,0x29af},
{0x7bb7,0x7bb7,0x29a7},
{0x7bb8,0x7bb8,0x1055},
{0x7bb9,0x7bb9,0x29ad},
{0x7bbe,0x7bbe,0x29a9},
{0x7bc0,0x7bc0,0xeb7},
{0x7bc1,0x7bc1,0x11d1},
{0x7bc4,0x7bc4,0x11cd},
{0x7bc6,0x7bc7,0x11cf},
{0x7bc9,0x7bc9,0x1318},
{0x7bca,0x7bca,0x29ae},
{0x7bcb,0x7bcb,0x29a8},
{0x7bcc,0x7bcc,0x11d3},
{0x7bce,0x7bce,0x29ab},
{0x7bcf,0x7bcf,0x3f18},
{0x7bd0,0x7bd0,0x3750},
{0x7bd4,0x7bd4,0x2c07},
{0x7bd5,0x7bd5,0x2c02},
{0x7bd8,0x7bd8,0x2c0c},
{0x7bd9,0x7bd9,0x1316},
{0x7bda,0x7bda,0x2c04},
{0x7bdb,0x7bdb,0x131a},
{0x7bdc,0x7bdc,0x2c0a},
{0x7bdd,0x7bdd,0x2c01},
{0x7bde,0x7bde,0x2bfe},
{0x7bdf,0x7bdf,0x2c0d},
{0x7be0,0x7be0,0x142d},
{0x7be1,0x7be1,0x131b},
{0x7be2,0x7be2,0x2c09},
{0x7be3,0x7be3,0x2bff},
{0x7be4,0x7be4,0x1319},
{0x7be5,0x7be5,0x2c03},
{0x7be6,0x7be6,0x131d},
{0x7be7,0x7be7,0x2c00},
{0x7be8,0x7be8,0x2c05},
{0x7be9,0x7be9,0x131c},
{0x7bea,0x7bea,0x2c08},
{0x7beb,0x7beb,0x2c0b},
{0x7bf0,0x7bf1,0x2de9},
{0x7bf2,0x7bf2,0x2dda},
{0x7bf3,0x7bf3,0x2de1},
{0x7bf4,0x7bf4,0x2ddf},
{0x7bf7,0x7bf7,0x142b},
{0x7bf8,0x7bf8,0x2de6},
{0x7bf9,0x7bf9,0x2c06},
{0x7bfa,0x7bfa,0x3757},
{0x7bfb,0x7bfb,0x2ddd},
{0x7bfc,0x7bfc,0x3f1f},
{0x7bfd,0x7bfd,0x2de7},
{0x7bfe,0x7bfe,0x142a},
{0x7bff,0x7bff,0x2ddc},
{0x7c00,0x7c00,0x2ddb},
{0x7c01,0x7c01,0x2de5},
{0x7c02,0x7c02,0x2de2},
{0x7c03,0x7c03,0x2de4},
{0x7c05,0x7c05,0x2dd8},
{0x7c06,0x7c06,0x2de8},
{0x7c07,0x7c07,0x1428},
{0x7c09,0x7c09,0x2de3},
{0x7c0a,0x7c0a,0x2dec},
{0x7c0b,0x7c0b,0x2de0},
{0x7c0c,0x7c0c,0x142c},
{0x7c0d,0x7c0d,0x1429},
{0x7c0e,0x7c0e,0x2dde},
{0x7c0f,0x7c0f,0x2dd9},
{0x7c10,0x7c10,0x2deb},
{0x7c11,0x7c11,0x1317},
{0x7c12,0x7c12,0x472a},
{0x7c15,0x7c15,0x4061},
{0x7c19,0x7c19,0x2f78},
{0x7c1b,0x7c1b,0x43d9},
{0x7c1c,0x7c1c,0x2f76},
{0x7c1d,0x7c1d,0x2f7c},
{0x7c1e,0x7c1e,0x1508},
{0x7c1f,0x7c1f,0x2f7a},
{0x7c20,0x7c20,0x2f79},
{0x7c21,0x7c21,0x150a},
{0x7c22,0x7c22,0x2f7f},
{0x7c23,0x7c23,0x1509},
{0x7c25,0x7c25,0x2f80},
{0x7c26,0x7c26,0x2f7d},
{0x7c27,0x7c27,0x1506},
{0x7c28,0x7c28,0x2f7e},
{0x7c29,0x7c29,0x2f77},
{0x7c2a,0x7c2a,0x1507},
{0x7c2b,0x7c2b,0x1505},
{0x7c2c,0x7c2c,0x30d6},
{0x7c2d,0x7c2d,0x2f7b},
{0x7c30,0x7c30,0x2f81},
{0x7c33,0x7c33,0x30d3},
{0x7c35,0x7c35,0x3759},
{0x7c37,0x7c37,0x15a7},
{0x7c38,0x7c38,0x15a5},
{0x7c39,0x7c39,0x30d5},
{0x7c3b,0x7c3b,0x30d7},
{0x7c3c,0x7c3c,0x30d4},
{0x7c3d,0x7c3d,0x15a6},
{0x7c3e,0x7c3f,0x15a3},
{0x7c40,0x7c40,0x15a8},
{0x7c42,0x7c42,0x3f1c},
{0x7c43,0x7c43,0x162b},
{0x7c44,0x7c44,0x375b},
{0x7c45,0x7c45,0x3212},
{0x7c47,0x7c47,0x3211},
{0x7c48,0x7c48,0x320f},
{0x7c49,0x7c49,0x320e},
{0x7c4a,0x7c4a,0x3210},
{0x7c4c,0x7c4c,0x162a},
{0x7c4d,0x7c4d,0x162c},
{0x7c50,0x7c50,0x1681},
{0x7c51,0x7c51,0x3fb8},
{0x7c53,0x7c53,0x32ea},
{0x7c54,0x7c54,0x32e9},
{0x7c56,0x7c56,0x3eff},
{0x7c57,0x7c57,0x339b},
{0x7c59,0x7c59,0x339d},
{0x7c5a,0x7c5a,0x339f},
{0x7c5b,0x7c5b,0x339e},
{0x7c5c,0x7c5c,0x339c},
{0x7c5d,0x7c5d,0x3b3f},
{0x7c5f,0x7c5f,0x16d1},
{0x7c60,0x7c60,0x16d0},
{0x7c63,0x7c63,0x1701},
{0x7c64,0x7c64,0x1700},
{0x7c65,0x7c65,0x1702},
{0x7c66,0x7c66,0x342b},
{0x7c67,0x7c67,0x342a},
{0x7c69,0x7c69,0x34d6},
{0x7c6a,0x7c6a,0x3493},
{0x7c6b,0x7c6b,0x34d7},
{0x7c6c,0x7c6c,0x1745},
{0x7c6d,0x7c6d,0x3b40},
{0x7c6e,0x7c6e,0x1746},
{0x7c6f,0x7c6f,0x3507},
{0x7c70,0x7c70,0x3f52},
{0x7c72,0x7c72,0x176b},
{0x7c73,0x7c73,0x3f7},
{0x7c74,0x7c74,0x469c},
{0x7c75,0x7c75,0x1a25},
{0x7c78,0x7c79,0x1bbb},
{0x7c7a,0x7c7a,0x1bba},
{0x7c7b,0x7c7b,0x472d},
{0x7c7c,0x7c7c,0x3b49},
{0x7c7d,0x7c7d,0x7cc},
{0x7c7e,0x7c7e,0x3f1a},
{0x7c7f,0x7c81,0x1bbd},
{0x7c83,0x7c83,0x375c},
{0x7c84,0x7c84,0x1d96},
{0x7c85,0x7c85,0x1d9c},
{0x7c86,0x7c86,0x3f1d},
{0x7c88,0x7c88,0x1d9a},
{0x7c89,0x7c89,0x96a},
{0x7c8a,0x7c8a,0x1d98},
{0x7c8c,0x7c8c,0x1d99},
{0x7c8d,0x7c8d,0x1d9b},
{0x7c8e,0x7c8e,0x3b48},
{0x7c91,0x7c91,0x1d97},
{0x7c92,0x7c92,0xb50},
{0x7c94,0x7c94,0x1fe0},
{0x7c95,0x7c95,0xb52},
{0x7c96,0x7c96,0x1fe2},
{0x7c97,0x7c97,0xb51},
{0x7c98,0x7c98,0x1fe1},
{0x7c9c,0x7c9c,0x472e},
{0x7c9e,0x7c9e,0x225d},
{0x7c9f,0x7c9f,0xd0d},
{0x7ca1,0x7ca1,0x225f},
{0x7ca2,0x7ca2,0x225c},
{0x7ca3,0x7ca3,0x1fe3},
{0x7ca5,0x7ca5,0xd0e},
{0x7ca6,0x7ca6,0x375e},
{0x7ca7,0x7ca7,0x36ed},
{0x7ca8,0x7ca8,0x225e},
{0x7cac,0x7cac,0x3885},
{0x7cae,0x7cae,0x3b4a},
{0x7caf,0x7caf,0x24fe},
{0x7cb1,0x7cb1,0xebb},
{0x7cb2,0x7cb2,0x24fc},
{0x7cb3,0x7cb3,0xebc},
{0x7cb4,0x7cb4,0x24fd},
{0x7cb5,0x7cb5,0xebd},
{0x7cb8,0x7cb8,0x4730},
{0x7cb9,0x7cb9,0x1058},
{0x7cba,0x7cba,0x277d},
{0x7cbb,0x7cbb,0x277a},
{0x7cbc,0x7cbc,0x277c},
{0x7cbd,0x7cbe,0x1059},
{0x7cbf,0x7cbf,0x277b},
{0x7cc2,0x7cc2,0x3fd7},
{0x7cc5,0x7cc5,0x29b0},
{0x7cc7,0x7cc7,0x3761},
{0x7cc8,0x7cc8,0x29b1},
{0x7cc9,0x7cc9,0x3760},
{0x7cca,0x7cca,0x11d4},
{0x7ccb,0x7ccb,0x29b3},
{0x7ccc,0x7ccc,0x29b2},
{0x7ccd,0x7ccd,0x3b45},
{0x7cce,0x7cce,0x121},
{0x7cd0,0x7cd1,0x2c11},
{0x7cd2,0x7cd2,0x2c0e},
{0x7cd3,0x7cd3,0x3d5c},
{0x7cd4,0x7cd4,0x2c0f},
{0x7cd5,0x7cd6,0x131e},
{0x7cd7,0x7cd7,0x2c10},
{0x7cd9,0x7cd9,0x1433},
{0x7cda,0x7cda,0x3fd9},
{0x7cdc,0x7cdc,0x142f},
{0x7cdd,0x7cdd,0x1434},
{0x7cde,0x7cde,0x1430},
{0x7cdf,0x7cdf,0x1432},
{0x7ce0,0x7ce0,0x142e},
{0x7ce2,0x7ce2,0x1431},
{0x7ce6,0x7ce6,0x3762},
{0x7ce7,0x7ce7,0x150b},
{0x7ce8,0x7ce8,0x2ded},
{0x7cea,0x7cea,0x30d9},
{0x7cec,0x7cec,0x30d8},
{0x7ced,0x7ced,0x43da},
{0x7cee,0x7cee,0x3213},
{0x7cef,0x7cf0,0x162d},
{0x7cf1,0x7cf1,0x33a1},
{0x7cf2,0x7cf2,0x32eb},
{0x7cf3,0x7cf3,0x3764},
{0x7cf4,0x7cf4,0x33a0},
{0x7cf5,0x7cf5,0x3765},
{0x7cf6,0x7cf6,0x34d8},
{0x7cf7,0x7cf7,0x351d},
{0x7cf8,0x7cf8,0x3f8},
{0x7cf9,0x7cf9,0x44f8},
{0x7cfb,0x7cfb,0x4fb},
{0x7cfc,0x7cfc,0x456d},
{0x7cfd,0x7cfd,0x1a26},
{0x7cfe,0x7cfe,0x667},
{0x7d00,0x7d00,0x7cf},
{0x7d01,0x7d01,0x1bc2},
{0x7d02,0x7d02,0x7cd},
{0x7d03,0x7d03,0x1bc0},
{0x7d04,0x7d04,0x7d2},
{0x7d05,0x7d05,0x7ce},
{0x7d06,0x7d06,0x7d3},
{0x7d07,0x7d07,0x7d1},
{0x7d08,0x7d08,0x1bc1},
{0x7d09,0x7d09,0x7d0},
{0x7d0a,0x7d0a,0x96e},
{0x7d0b,0x7d0b,0x96d},
{0x7d0c,0x7d0c,0x1da7},
{0x7d0d,0x7d0d,0x976},
{0x7d0e,0x7d0e,0x1da0},
{0x7d0f,0x7d0f,0x1da6},
{0x7d10,0x7d10,0x972},
{0x7d11,0x7d11,0x1d9f},
{0x7d12,0x7d12,0x1da5},
{0x7d13,0x7d13,0x1da3},
{0x7d14,0x7d14,0x971},
{0x7d15,0x7d15,0x973},
{0x7d16,0x7d16,0x1da2},
{0x7d17,0x7d17,0x96c},
{0x7d18,0x7d18,0x1da1},
{0x7d19,0x7d19,0x977},
{0x7d1a,0x7d1a,0x974},
{0x7d1b,0x7d1b,0x978},
{0x7d1c,0x7d1c,0x975},
{0x7d1d,0x7d1d,0x1d9e},
{0x7d1e,0x7d1e,0x1d9d},
{0x7d1f,0x7d1f,0x1da4},
{0x7d20,0x7d20,0x96f},
{0x7d21,0x7d21,0x96b},
{0x7d22,0x7d22,0x970},
{0x7d25,0x7d25,0x3ede},
{0x7d28,0x7d28,0x1ff2},
{0x7d29,0x7d29,0x1feb},
{0x7d2b,0x7d2b,0xd13},
{0x7d2c,0x7d2c,0x1fea},
{0x7d2e,0x7d2e,0xb56},
{0x7d2f,0x7d2f,0xb5d},
{0x7d30,0x7d30,0xb5a},
{0x7d31,0x7d31,0xb60},
{0x7d32,0x7d32,0xb5f},
{0x7d33,0x7d33,0xb5b},
{0x7d35,0x7d35,0x1fe4},
{0x7d36,0x7d36,0x1fe7},
{0x7d38,0x7d38,0x1fe6},
{0x7d39,0x7d39,0xb57},
{0x7d3a,0x7d3a,0x1fe8},
{0x7d3b,0x7d3b,0x1ff1},
{0x7d3c,0x7d3c,0xb58},
{0x7d3d,0x7d3d,0x1fe5},
{0x7d3e,0x7d3f,0x1fee},
{0x7d40,0x7d40,0xb59},
{0x7d41,0x7d41,0x1fec},
{0x7d42,0x7d42,0xb5e},
{0x7d43,0x7d43,0xb54},
{0x7d44,0x7d44,0xb5c},
{0x7d45,0x7d45,0x1fe9},
{0x7d46,0x7d46,0xb53},
{0x7d47,0x7d47,0x1fed},
{0x7d4a,0x7d4a,0x1ff0},
{0x7d4d,0x7d4d,0x3fdd},
{0x7d4e,0x7d4e,0x2270},
{0x7d4f,0x7d4f,0x2267},
{0x7d50,0x7d50,0xd10},
{0x7d51,0x7d51,0x226e},
{0x7d52,0x7d52,0x226b},
{0x7d53,0x7d53,0x2263},
{0x7d54,0x7d54,0x226c},
{0x7d55,0x7d55,0xd12},
{0x7d56,0x7d56,0x2264},
{0x7d58,0x7d58,0x2260},
{0x7d5a,0x7d5a,0x3e93},
{0x7d5b,0x7d5b,0xec3},
{0x7d5c,0x7d5c,0x2269},
{0x7d5d,0x7d5d,0x3769},
{0x7d5e,0x7d5e,0xd0f},
{0x7d5f,0x7d5f,0x226f},
{0x7d61,0x7d61,0xd16},
{0x7d62,0x7d62,0xd18},
{0x7d63,0x7d63,0x2262},
{0x7d66,0x7d66,0xd17},
{0x7d67,0x7d67,0x2265},
{0x7d68,0x7d68,0xd11},
{0x7d69,0x7d69,0x226d},
{0x7d6a,0x7d6a,0x2266},
{0x7d6b,0x7d6b,0x226a},
{0x7d6d,0x7d6d,0x2268},
{0x7d6e,0x7d6e,0xd14},
{0x7d6f,0x7d6f,0x2261},
{0x7d70,0x7d70,0xd19},
{0x7d71,0x7d71,0xb55},
{0x7d72,0x7d72,0xd15},
{0x7d73,0x7d73,0xd1a},
{0x7d79,0x7d79,0xebf},
{0x7d7a,0x7d7a,0x2505},
{0x7d7b,0x7d7b,0x2507},
{0x7d7c,0x7d7c,0x2509},
{0x7d7d,0x7d7d,0x250d},
{0x7d7f,0x7d7f,0x2503},
{0x7d80,0x7d80,0x2501},
{0x7d81,0x7d81,0xec1},
{0x7d83,0x7d83,0x2508},
{0x7d84,0x7d84,0x250c},
{0x7d85,0x7d85,0x2504},
{0x7d86,0x7d86,0x2500},
{0x7d88,0x7d88,0x24ff},
{0x7d89,0x7d89,0x376b},
{0x7d8c,0x7d8c,0x250a},
{0x7d8d,0x7d8d,0x2502},
{0x7d8e,0x7d8e,0x2506},
{0x7d8f,0x7d8f,0xec2},
{0x7d91,0x7d91,0xec0},
{0x7d92,0x7d92,0x250e},
{0x7d93,0x7d93,0xebe},
{0x7d94,0x7d94,0x250b},
{0x7d96,0x7d96,0x278e},
{0x7d97,0x7d97,0x3b53},
{0x7d9c,0x7d9c,0x105d},
{0x7d9d,0x7d9d,0x2786},
{0x7d9e,0x7d9e,0x11e1},
{0x7d9f,0x7d9f,0x2790},
{0x7da0,0x7da0,0x1060},
{0x7da1,0x7da1,0x2794},
{0x7da2,0x7da2,0x1066},
{0x7da3,0x7da3,0x2781},
{0x7da4,0x7da4,0x46d7},
{0x7da6,0x7da6,0x2791},
{0x7da7,0x7da7,0x277e},
{0x7da8,0x7da8,0x3c9c},
{0x7da9,0x7da9,0x2793},
{0x7daa,0x7daa,0x2782},
{0x7dab,0x7dab,0x376c},
{0x7dac,0x7dac,0x106d},
{0x7dad,0x7dad,0x106a},
{0x7dae,0x7dae,0x2792},
{0x7daf,0x7daf,0x278c},
{0x7db0,0x7db0,0x105c},
{0x7db1,0x7db1,0x1064},
{0x7db2,0x7db2,0x1063},
{0x7db3,0x7db3,0x376e},
{0x7db4,0x7db4,0x1062},
{0x7db5,0x7db5,0x1068},
{0x7db7,0x7db7,0x277f},
{0x7db8,0x7db8,0x1069},
{0x7db9,0x7db9,0x278d},
{0x7dba,0x7dba,0x1065},
{0x7dbb,0x7dbb,0x105b},
{0x7dbc,0x7dbc,0x278f},
{0x7dbd,0x7dbe,0x105e},
{0x7dbf,0x7dbf,0x1067},
{0x7dc0,0x7dc0,0x2784},
{0x7dc1,0x7dc1,0x2783},
{0x7dc2,0x7dc2,0x2780},
{0x7dc4,0x7dc4,0x2788},
{0x7dc5,0x7dc5,0x2785},
{0x7dc6,0x7dc6,0x2789},
{0x7dc7,0x7dc7,0x106c},
{0x7dc9,0x7dc9,0x2795},
{0x7dca,0x7dca,0x1061},
{0x7dcb,0x7dcc,0x278a},
{0x7dcd,0x7dcd,0x456e},
{0x7dce,0x7dce,0x2787},
{0x7dcf,0x7dcf,0x4735},
{0x7dd0,0x7dd0,0x4737},
{0x7dd2,0x7dd2,0x106b},
{0x7dd3,0x7dd3,0x3b4e},
{0x7dd4,0x7dd4,0x4736},
{0x7dd6,0x7dd6,0x376f},
{0x7dd7,0x7dd7,0x29b8},
{0x7dd8,0x7dd8,0x11d9},
{0x7dd9,0x7dd9,0x11e2},
{0x7dda,0x7dda,0x11de},
{0x7ddb,0x7ddb,0x29b5},
{0x7ddc,0x7ddc,0x3b4c},
{0x7ddd,0x7ddd,0x11db},
{0x7dde,0x7dde,0x11df},
{0x7ddf,0x7ddf,0x29c1},
{0x7de0,0x7de0,0x11d5},
{0x7de1,0x7de1,0x29b9},
{0x7de3,0x7de3,0x11dd},
{0x7de4,0x7de4,0x3772},
{0x7de5,0x7de5,0x3776},
{0x7de6,0x7de6,0x29bc},
{0x7de7,0x7de7,0x29b7},
{0x7de8,0x7de8,0x11dc},
{0x7de9,0x7de9,0x11e0},
{0x7dea,0x7dea,0x29b6},
{0x7dec,0x7dec,0x11da},
{0x7dee,0x7dee,0x29c0},
{0x7def,0x7def,0x11d7},
{0x7df0,0x7df0,0x29bf},
{0x7df1,0x7df1,0x29be},
{0x7df2,0x7df2,0x11e3},
{0x7df3,0x7df3,0x28c8},
{0x7df4,0x7df4,0x11d6},
{0x7df5,0x7df5,0x3774},
{0x7df6,0x7df6,0x29bd},
{0x7df7,0x7df7,0x29b4},
{0x7df9,0x7df9,0x11e4},
{0x7dfa,0x7dfa,0x29bb},
{0x7dfb,0x7dfb,0x11d8},
{0x7dfd,0x7dfd,0x4738},
{0x7dfe,0x7dfe,0x3ccf},
{0x7e03,0x7e03,0x29ba},
{0x7e07,0x7e07,0x3b4d},
{0x7e08,0x7e08,0x1322},
{0x7e09,0x7e09,0x1327},
{0x7e0a,0x7e0a,0x1320},
{0x7e0b,0x7e0b,0x2c1f},
{0x7e0c,0x7e0c,0x2c16},
{0x7e0d,0x7e0d,0x2c22},
{0x7e0e,0x7e0e,0x2c1a},
{0x7e0f,0x7e0f,0x2c20},
{0x7e10,0x7e10,0x1328},
{0x7e11,0x7e11,0x1321},
{0x7e12,0x7e12,0x2c13},
{0x7e13,0x7e13,0x2c19},
{0x7e14,0x7e14,0x2c23},
{0x7e15,0x7e15,0x2c1c},
{0x7e16,0x7e16,0x2c21},
{0x7e17,0x7e17,0x2c15},
{0x7e1a,0x7e1a,0x2c1d},
{0x7e1b,0x7e1b,0x1323},
{0x7e1c,0x7e1c,0x2c1b},
{0x7e1d,0x7e1d,0x1326},
{0x7e1e,0x7e1e,0x1325},
{0x7e1f,0x7e20,0x2c17},
{0x7e21,0x7e21,0x2c14},
{0x7e22,0x7e22,0x2c1e},
{0x7e23,0x7e23,0x1324},
{0x7e24,0x7e24,0x2c25},
{0x7e25,0x7e25,0x2c24},
{0x7e27,0x7e27,0x377f},
{0x7e29,0x7e29,0x2df8},
{0x7e2a,0x7e2a,0x2df4},
{0x7e2b,0x7e2b,0x143b},
{0x7e2d,0x7e2d,0x2dee},
{0x7e2e,0x7e2e,0x1435},
{0x7e2f,0x7e2f,0x1445},
{0x7e30,0x7e30,0x2dfa},
{0x7e31,0x7e31,0x143d},
{0x7e32,0x7e32,0x1439},
{0x7e33,0x7e33,0x2df1},
{0x7e34,0x7e34,0x1440},
{0x7e35,0x7e35,0x1443},
{0x7e36,0x7e36,0x2dfc},
{0x7e37,0x7e37,0x1438},
{0x7e38,0x7e38,0x2df3},
{0x7e39,0x7e39,0x1441},
{0x7e3a,0x7e3a,0x2dfe},
{0x7e3b,0x7e3b,0x2dfb},
{0x7e3c,0x7e3c,0x2def},
{0x7e3d,0x7e3d,0x143c},
{0x7e3e,0x7e3e,0x1436},
{0x7e3f,0x7e3f,0x1444},
{0x7e40,0x7e40,0x2df6},
{0x7e41,0x7e41,0x143f},
{0x7e42,0x7e42,0x2df0},
{0x7e43,0x7e43,0x143a},
{0x7e44,0x7e44,0x2dfd},
{0x7e45,0x7e45,0x143e},
{0x7e46,0x7e46,0x1437},
{0x7e47,0x7e47,0x2df7},
{0x7e48,0x7e48,0x1442},
{0x7e49,0x7e49,0x2df5},
{0x7e4c,0x7e4c,0x2df9},
{0x7e50,0x7e50,0x2f83},
{0x7e51,0x7e51,0x2f89},
{0x7e52,0x7e52,0x1511},
{0x7e53,0x7e53,0x2f8c},
{0x7e54,0x7e55,0x150c},
{0x7e56,0x7e56,0x2f84},
{0x7e57,0x7e57,0x2f8b},
{0x7e58,0x7e58,0x2f86},
{0x7e59,0x7e59,0x1512},
{0x7e5a,0x7e5a,0x150f},
{0x7e5c,0x7e5c,0x2f82},
{0x7e5e,0x7e5e,0x150e},
{0x7e5f,0x7e5f,0x2f88},
{0x7e60,0x7e60,0x2f8a},
{0x7e61,0x7e61,0x1510},
{0x7e62,0x7e62,0x2f87},
{0x7e63,0x7e63,0x2f85},
{0x7e65,0x7e65,0x46d2},
{0x7e67,0x7e67,0x3766},
{0x7e68,0x7e68,0x30e3},
{0x7e69,0x7e6a,0x15ac},
{0x7e6b,0x7e6b,0x15a9},
{0x7e6d,0x7e6d,0x15aa},
{0x7e6e,0x7e6e,0x377b},
{0x7e6f,0x7e6f,0x30df},
{0x7e70,0x7e70,0x30dd},
{0x7e72,0x7e72,0x30e1},
{0x7e73,0x7e73,0x15ae},
{0x7e74,0x7e74,0x30e2},
{0x7e75,0x7e75,0x30db},
{0x7e76,0x7e76,0x30da},
{0x7e77,0x7e77,0x30de},
{0x7e78,0x7e78,0x30dc},
{0x7e79,0x7e79,0x15ab},
{0x7e7a,0x7e7a,0x30e0},
{0x7e7b,0x7e7b,0x3214},
{0x7e7c,0x7e7c,0x1631},
{0x7e7d,0x7e7d,0x1630},
{0x7e7e,0x7e7e,0x3215},
{0x7e7f,0x7e7f,0x3e51},
{0x7e80,0x7e80,0x3217},
{0x7e81,0x7e81,0x3216},
{0x7e82,0x7e82,0x1632},
{0x7e86,0x7e86,0x32f0},
{0x7e87,0x7e88,0x32ed},
{0x7e8a,0x7e8a,0x32ec},
{0x7e8b,0x7e8b,0x32ef},
{0x7e8c,0x7e8c,0x1683},
{0x7e8d,0x7e8d,0x32f1},
{0x7e8e,0x7e8e,0x3ec9},
{0x7e8f,0x7e8f,0x1682},
{0x7e91,0x7e91,0x33a2},
{0x7e92,0x7e92,0x469e},
{0x7e93,0x7e93,0x1703},
{0x7e94,0x7e94,0x1705},
{0x7e95,0x7e95,0x342c},
{0x7e96,0x7e96,0x1704},
{0x7e97,0x7e97,0x3494},
{0x7e98,0x7e98,0x34da},
{0x7e99,0x7e99,0x34dc},
{0x7e9a,0x7e9a,0x34d9},
{0x7e9b,0x7e9b,0x34db},
{0x7e9c,0x7e9c,0x1759},
{0x7e9f,0x7e9f,0x48bb},
{0x7ea4,0x7ea4,0x455b},
{0x7eac,0x7eac,0x455c},
{0x7eba,0x7eba,0x455d},
{0x7ec7,0x7ec7,0x455e},
{0x7ecf,0x7ecf,0x455f},
{0x7edf,0x7edf,0x4560},
{0x7f06,0x7f06,0x4561},
{0x7f36,0x7f36,0x3f9},
{0x7f37,0x7f37,0x4562},
{0x7f38,0x7f38,0x7d4},
{0x7f39,0x7f39,0x1d1f},
{0x7f3a,0x7f3a,0x979},
{0x7f3d,0x7f3d,0xb61},
{0x7f3e,0x7f3f,0x2271},
{0x7f40,0x7f41,0x3780},
{0x7f43,0x7f43,0x2c26},
{0x7f44,0x7f44,0x1446},
{0x7f45,0x7f45,0x2dff},
{0x7f47,0x7f47,0x3782},
{0x7f48,0x7f48,0x1513},
{0x7f49,0x7f49,0x3e69},
{0x7f4a,0x7f4a,0x30e5},
{0x7f4b,0x7f4b,0x30e4},
{0x7f4c,0x7f4c,0x1633},
{0x7f4d,0x7f4d,0x32f2},
{0x7f4e,0x7f4e,0x3b4f},
{0x7f4f,0x7f4f,0x33a3},
{0x7f50,0x7f50,0x1726},
{0x7f51,0x7f51,0x1812},
{0x7f52,0x7f52,0x44f6},
{0x7f53,0x7f53,0x44f9},
{0x7f54,0x7f54,0x668},
{0x7f55,0x7f55,0x4fc},
{0x7f58,0x7f58,0x1bc3},
{0x7f5b,0x7f5b,0x1dad},
{0x7f5c,0x7f5c,0x1da8},
{0x7f5d,0x7f5d,0x1dac},
{0x7f5e,0x7f5e,0x1daa},
{0x7f5f,0x7f5f,0x97a},
{0x7f60,0x7f60,0x1dab},
{0x7f61,0x7f61,0x1da9},
{0x7f63,0x7f63,0x1ff3},
{0x7f65,0x7f66,0x2273},
{0x7f67,0x7f68,0x2511},
{0x7f69,0x7f6a,0xec5},
{0x7f6b,0x7f6b,0x2510},
{0x7f6c,0x7f6c,0x2513},
{0x7f6d,0x7f6d,0x250f},
{0x7f6e,0x7f6e,0xec4},
{0x7f70,0x7f70,0x106e},
{0x7f71,0x7f71,0x4007},
{0x7f72,0x7f72,0xec7},
{0x7f73,0x7f73,0x2796},
{0x7f75,0x7f75,0x11e5},
{0x7f76,0x7f76,0x29c2},
{0x7f77,0x7f77,0x11e6},
{0x7f78,0x7f78,0x3f7b},
{0x7f79,0x7f79,0x1329},
{0x7f7a,0x7f7a,0x2c29},
{0x7f7b,0x7f7c,0x2c27},
{0x7f7d,0x7f7d,0x2e02},
{0x7f7e,0x7f7e,0x2e01},
{0x7f7f,0x7f7f,0x2e00},
{0x7f83,0x7f83,0x30e6},
{0x7f85,0x7f85,0x15af},
{0x7f86,0x7f86,0x30e7},
{0x7f87,0x7f87,0x33a4},
{0x7f88,0x7f88,0x1727},
{0x7f89,0x7f89,0x3495},
{0x7f8a,0x7f8a,0x3fa},
{0x7f8b,0x7f8b,0x66a},
{0x7f8c,0x7f8c,0x669},
{0x7f8d,0x7f8d,0x1bc5},
{0x7f8e,0x7f8e,0x7d5},
{0x7f8f,0x7f8f,0x421c},
{0x7f91,0x7f91,0x1bc4},
{0x7f92,0x7f92,0x1daf},
{0x7f93,0x7f93,0x43db},
{0x7f94,0x7f94,0x97b},
{0x7f95,0x7f95,0x1ff4},
{0x7f96,0x7f96,0x1dae},
{0x7f97,0x7f97,0x3786},
{0x7f9a,0x7f9a,0xb63},
{0x7f9b,0x7f9b,0x1ff7},
{0x7f9c,0x7f9d,0x1ff5},
{0x7f9e,0x7f9e,0xb62},
{0x7fa0,0x7fa1,0x2276},
{0x7fa2,0x7fa2,0x2275},
{0x7fa3,0x7fa3,0x3788},
{0x7fa4,0x7fa4,0xeca},
{0x7fa5,0x7fa5,0x2515},
{0x7fa6,0x7fa6,0x2514},
{0x7fa7,0x7fa7,0x2516},
{0x7fa8,0x7fa8,0xec9},
{0x7fa9,0x7fa9,0xec8},
{0x7fac,0x7fac,0x29c3},
{0x7fad,0x7fad,0x29c5},
{0x7fae,0x7fae,0x43dc},
{0x7faf,0x7faf,0x11e7},
{0x7fb0,0x7fb0,0x29c4},
{0x7fb1,0x7fb1,0x2c2a},
{0x7fb2,0x7fb2,0x132a},
{0x7fb3,0x7fb3,0x2f8e},
{0x7fb4,0x7fb4,0x4739},
{0x7fb5,0x7fb5,0x2f8d},
{0x7fb6,0x7fb6,0x15b0},
{0x7fb7,0x7fb7,0x30e8},
{0x7fb8,0x7fb8,0x15b2},
{0x7fb9,0x7fb9,0x15b1},
{0x7fba,0x7fba,0x3218},
{0x7fbb,0x7fbb,0x32f3},
{0x7fbc,0x7fbc,0x1684},
{0x7fbd,0x7fbd,0x3fb},
{0x7fbe,0x7fbe,0x1bc6},
{0x7fbf,0x7fbf,0x7d6},
{0x7fc0,0x7fc0,0x1db2},
{0x7fc1,0x7fc1,0x97d},
{0x7fc2,0x7fc2,0x1db1},
{0x7fc3,0x7fc3,0x1db0},
{0x7fc5,0x7fc5,0x97c},
{0x7fc7,0x7fc7,0x1ffd},
{0x7fc9,0x7fc9,0x1fff},
{0x7fca,0x7fcb,0x1ff8},
{0x7fcc,0x7fcc,0xb64},
{0x7fcd,0x7fcd,0x1ffa},
{0x7fce,0x7fce,0xb65},
{0x7fcf,0x7fcf,0x1ffe},
{0x7fd0,0x7fd1,0x1ffb},
{0x7fd2,0x7fd2,0xb66},
{0x7fd4,0x7fd5,0xd1c},
{0x7fd7,0x7fd7,0x2278},
{0x7fdb,0x7fdc,0x2517},
{0x7fdd,0x7fdd,0x3b5e},
{0x7fde,0x7fde,0x279a},
{0x7fdf,0x7fdf,0x1071},
{0x7fe0,0x7fe1,0x106f},
{0x7fe2,0x7fe3,0x2797},
{0x7fe5,0x7fe5,0x2799},
{0x7fe6,0x7fe6,0x29ca},
{0x7fe7,0x7fe7,0x46e9},
{0x7fe8,0x7fe8,0x29cb},
{0x7fe9,0x7fe9,0x11e8},
{0x7fea,0x7fea,0x29c8},
{0x7feb,0x7feb,0x29c7},
{0x7fec,0x7fec,0x29c9},
{0x7fed,0x7fed,0x29c6},
{0x7fee,0x7fee,0x132d},
{0x7fef,0x7fef,0x2c2b},
{0x7ff0,0x7ff1,0x132b},
{0x7ff2,0x7ff2,0x2e04},
{0x7ff3,0x7ff3,0x1447},
{0x7ff4,0x7ff4,0x2e03},
{0x7ff5,0x7ff5,0x2eff},
{0x7ff7,0x7ff8,0x2f8f},
{0x7ff9,0x7ff9,0x1514},
{0x7ffa,0x7ffa,0x378e},
{0x7ffb,0x7ffb,0x1515},
{0x7ffc,0x7ffc,0x1448},
{0x7ffd,0x7ffe,0x30e9},
{0x7fff,0x7fff,0x3219},
{0x8000,0x8000,0x1634},
{0x8001,0x8001,0x3fc},
{0x8002,0x8002,0x44fd},
{0x8003,0x8003,0x3fd},
{0x8004,0x8004,0x97f},
{0x8005,0x8005,0x66b},
{0x8006,0x8006,0x97e},
{0x8007,0x8007,0x1bc7},
{0x8008,0x8008,0x3791},
{0x800b,0x800b,0xd1e},
{0x800c,0x800c,0x3fe},
{0x800d,0x800d,0x7d8},
{0x800e,0x800f,0x1bc8},
{0x8010,0x8010,0x7d7},
{0x8011,0x8011,0x7d9},
{0x8012,0x8012,0x3ff},
{0x8014,0x8014,0x1bca},
{0x8015,0x8015,0x981},
{0x8016,0x8016,0x1db3},
{0x8017,0x8017,0x983},
{0x8018,0x8018,0x980},
{0x8019,0x8019,0x982},
{0x801b,0x801b,0x2002},
{0x801c,0x801c,0xb67},
{0x801d,0x801d,0x3792},
{0x801e,0x801e,0x2001},
{0x801f,0x801f,0x2000},
{0x8020,0x8020,0x473c},
{0x8021,0x8021,0x2519},
{0x8024,0x8024,0x279b},
{0x8025,0x8025,0x473d},
{0x8026,0x8026,0x11e9},
{0x8028,0x8028,0x132e},
{0x8029,0x8029,0x2c2d},
{0x802a,0x802a,0x2c2c},
{0x802c,0x802c,0x2e05},
{0x802e,0x802e,0x473e},
{0x802f,0x802f,0x3794},
{0x8030,0x8030,0x32f4},
{0x8031,0x8031,0x473f},
{0x8033,0x8033,0x400},
{0x8034,0x8034,0x18d8},
{0x8035,0x8035,0x1a27},
{0x8036,0x8036,0x7da},
{0x8037,0x8037,0x1bcb},
{0x8039,0x8039,0x1db5},
{0x803b,0x803c,0x3797},
{0x803d,0x803d,0x984},
{0x803e,0x803e,0x1db4},
{0x803f,0x803f,0x985},
{0x8043,0x8043,0x2004},
{0x8046,0x8046,0xb69},
{0x8047,0x8047,0x2003},
{0x8048,0x8048,0x2005},
{0x804a,0x804a,0xb68},
{0x804f,0x8050,0x227a},
{0x8051,0x8051,0x2279},
{0x8052,0x8052,0xd1f},
{0x8054,0x8054,0x4740},
{0x8056,0x8056,0xecb},
{0x8058,0x8058,0xecc},
{0x805a,0x805a,0x1073},
{0x805b,0x805b,0x3fe5},
{0x805c,0x805c,0x279d},
{0x805d,0x805d,0x279c},
{0x805e,0x805e,0x1072},
{0x8061,0x8061,0x3799},
{0x8062,0x8062,0x3fe3},
{0x8063,0x8063,0x3fdc},
{0x8064,0x8064,0x29cc},
{0x8066,0x8066,0x3fdb},
{0x8067,0x8067,0x29cd},
{0x806c,0x806c,0x2c2e},
{0x806f,0x806f,0x144c},
{0x8070,0x8070,0x144b},
{0x8071,0x8072,0x1449},
{0x8073,0x8073,0x144d},
{0x8075,0x8075,0x2f91},
{0x8076,0x8076,0x1517},
{0x8077,0x8077,0x1516},
{0x8078,0x8078,0x30eb},
{0x8079,0x8079,0x321a},
{0x807d,0x807d,0x16d3},
{0x807e,0x807e,0x16d2},
{0x807f,0x807f,0x401},
{0x8080,0x8080,0x44fe},
{0x8082,0x8082,0x1cec},
{0x8084,0x8084,0xece},
{0x8085,0x8085,0xd20},
{0x8086,0x8086,0xecd},
{0x8087,0x8087,0x1074},
{0x8089,0x8089,0x402},
{0x808a,0x808a,0x17b3},
{0x808b,0x808c,0x403},
{0x808f,0x808f,0x1a28},
{0x8090,0x8090,0x18db},
{0x8092,0x8092,0x18dc},
{0x8093,0x8093,0x4fe},
{0x8095,0x8095,0x18d9},
{0x8096,0x8096,0x4fd},
{0x8098,0x8098,0x500},
{0x8099,0x8099,0x18da},
{0x809a,0x809a,0x502},
{0x809b,0x809b,0x501},
{0x809c,0x809c,0x18dd},
{0x809d,0x809d,0x4ff},
{0x809f,0x809f,0x4576},
{0x80a1,0x80a1,0x670},
{0x80a2,0x80a2,0x66e},
{0x80a3,0x80a3,0x1a2a},
{0x80a5,0x80a5,0x66d},
{0x80a7,0x80a7,0x37a0},
{0x80a9,0x80a9,0x672},
{0x80aa,0x80aa,0x674},
{0x80ab,0x80ab,0x671},
{0x80ad,0x80ad,0x1a2d},
{0x80ae,0x80ae,0x1a29},
{0x80af,0x80af,0x675},
{0x80b1,0x80b1,0x66f},
{0x80b2,0x80b2,0x503},
{0x80b4,0x80b4,0x673},
{0x80b5,0x80b5,0x1a2c},
{0x80b6,0x80b6,0x3eae},
{0x80b7,0x80b7,0x4743},
{0x80b8,0x80b8,0x1a2b},
{0x80ba,0x80ba,0x66c},
{0x80bc,0x80bc,0x4572},
{0x80bd,0x80bd,0x3e7b},
{0x80c2,0x80c2,0x1bd1},
{0x80c3,0x80c4,0x7de},
{0x80c5,0x80c5,0x1bd3},
{0x80c6,0x80c6,0x3b74},
{0x80c7,0x80c7,0x1bcd},
{0x80c8,0x80c8,0x1bd0},
{0x80c9,0x80c9,0x1bd9},
{0x80ca,0x80ca,0x1bd7},
{0x80cc,0x80cc,0x7e0},
{0x80cd,0x80cd,0x1bdd},
{0x80ce,0x80ce,0x7e3},
{0x80cf,0x80cf,0x1bda},
{0x80d0,0x80d0,0x1bd2},
{0x80d1,0x80d1,0x1bcf},
{0x80d4,0x80d4,0x227d},
{0x80d5,0x80d5,0x1bd8},
{0x80d6,0x80d6,0x7db},
{0x80d7,0x80d7,0x1bdb},
{0x80d8,0x80d8,0x1bcc},
{0x80d9,0x80d9,0x1bd5},
{0x80da,0x80da,0x7dd},
{0x80db,0x80db,0x7e2},
{0x80dc,0x80dc,0x1bd6},
{0x80dd,0x80dd,0x7e6},
{0x80de,0x80de,0x7e4},
{0x80e0,0x80e0,0x1bce},
{0x80e1,0x80e1,0x7e1},
{0x80e3,0x80e3,0x1bd4},
{0x80e4,0x80e4,0x7e5},
{0x80e5,0x80e5,0x7dc},
{0x80e6,0x80e6,0x1bdc},
{0x80e9,0x80e9,0x4744},
{0x80ec,0x80ec,0x45e9},
{0x80ed,0x80ed,0x98a},
{0x80ef,0x80ef,0x993},
{0x80f0,0x80f0,0x988},
{0x80f1,0x80f1,0x986},
{0x80f2,0x80f2,0x1db7},
{0x80f3,0x80f3,0x98e},
{0x80f4,0x80f4,0x98b},
{0x80f5,0x80f5,0x1db9},
{0x80f6,0x80f6,0x4574},
{0x80f8,0x80f8,0x98d},
{0x80f9,0x80f9,0x1db8},
{0x80fa,0x80fa,0x1db6},
{0x80fb,0x80fb,0x1dbb},
{0x80fc,0x80fc,0x992},
{0x80fd,0x80fd,0x990},
{0x80fe,0x80fe,0x227c},
{0x8100,0x8100,0x1dbc},
{0x8101,0x8101,0x1dba},
{0x8102,0x8102,0x987},
{0x8103,0x8103,0x3fe7},
{0x8105,0x8105,0x989},
{0x8106,0x8106,0x98c},
{0x8107,0x8107,0x37a2},
{0x8108,0x8108,0x98f},
{0x8109,0x8109,0x3b75},
{0x810a,0x810a,0x991},
{0x810c,0x810c,0x4746},
{0x810e,0x810e,0x4747},
{0x8112,0x8112,0x4748},
{0x8114,0x8114,0x4749},
{0x8115,0x8115,0x200f},
{0x8116,0x8116,0xb6b},
{0x8117,0x8117,0x3e6d},
{0x8118,0x8118,0x2006},
{0x8119,0x8119,0x2008},
{0x811a,0x811a,0x37a3},
{0x811b,0x811b,0x2009},
{0x811d,0x811d,0x2011},
{0x811e,0x811e,0x200d},
{0x811f,0x811f,0x200b},
{0x8121,0x8121,0x200e},
{0x8122,0x8122,0x2012},
{0x8123,0x8123,0xb6c},
{0x8124,0x8124,0xb70},
{0x8125,0x8125,0x2007},
{0x8127,0x8127,0x2010},
{0x8129,0x8129,0xb6e},
{0x812a,0x812a,0x3a39},
{0x812b,0x812b,0xb6d},
{0x812c,0x812c,0x200c},
{0x812d,0x812d,0x200a},
{0x812f,0x812f,0xb6a},
{0x8130,0x8130,0xb6f},
{0x8132,0x8132,0x3e79},
{0x8134,0x8134,0x45ec},
{0x8137,0x8137,0x3b72},
{0x8139,0x8139,0xd26},
{0x813a,0x813a,0x2285},
{0x813d,0x813d,0x2283},
{0x813e,0x813e,0xd28},
{0x8142,0x8142,0x3b76},
{0x8143,0x8143,0x227e},
{0x8144,0x8144,0x2527},
{0x8146,0x8146,0xd27},
{0x8147,0x8147,0x2282},
{0x8148,0x8148,0x3e76},
{0x814a,0x814a,0x227f},
{0x814b,0x814b,0xd23},
{0x814c,0x814c,0xd29},
{0x814d,0x814d,0x2284},
{0x814e,0x814e,0xd25},
{0x814f,0x814f,0x2281},
{0x8150,0x8150,0x1075},
{0x8151,0x8151,0xd24},
{0x8152,0x8152,0x2280},
{0x8153,0x8153,0xd2a},
{0x8154,0x8154,0xd22},
{0x8155,0x8155,0xd21},
{0x8156,0x8156,0x474c},
{0x8159,0x815a,0x474d},
{0x815b,0x815b,0x251f},
{0x815c,0x815c,0x251d},
{0x815e,0x815e,0x2523},
{0x8160,0x8160,0x251b},
{0x8161,0x8161,0x2528},
{0x8162,0x8162,0x2520},
{0x8164,0x8164,0x251a},
{0x8165,0x8165,0xed2},
{0x8166,0x8166,0xed8},
{0x8167,0x8167,0x2525},
{0x8169,0x8169,0x251e},
{0x816b,0x816b,0xed5},
{0x816d,0x816d,0x43c4},
{0x816e,0x816e,0xed3},
{0x816f,0x816f,0x2526},
{0x8170,0x8170,0xed0},
{0x8171,0x8171,0xecf},
{0x8172,0x8172,0x2521},
{0x8173,0x8173,0xed4},
{0x8174,0x8174,0xd2b},
{0x8176,0x8176,0x2524},
{0x8177,0x8177,0x251c},
{0x8178,0x8178,0xed1},
{0x8179,0x817a,0xed6},
{0x817c,0x817c,0x4750},
{0x817f,0x817f,0x107a},
{0x8180,0x8180,0x1076},
{0x8182,0x8182,0x107b},
{0x8183,0x8183,0x27a0},
{0x8184,0x8184,0x43c5},
{0x8186,0x8186,0x279f},
{0x8187,0x8187,0x27a1},
{0x8188,0x8188,0x1078},
{0x8189,0x8189,0x279e},
{0x818a,0x818a,0x1079},
{0x818b,0x818b,0x27a4},
{0x818c,0x818c,0x27a3},
{0x818d,0x818d,0x27a2},
{0x818f,0x818f,0x1077},
{0x8193,0x8193,0x43c6},
{0x8195,0x8195,0x29d1},
{0x8197,0x8197,0x29d4},
{0x8198,0x8198,0x11ef},
{0x8199,0x8199,0x29d3},
{0x819a,0x819a,0x11ee},
{0x819b,0x819d,0x11ea},
{0x819e,0x819e,0x29d0},
{0x819f,0x819f,0x29cf},
{0x81a0,0x81a0,0x11ed},
{0x81a2,0x81a2,0x29d2},
{0x81a3,0x81a3,0x29ce},
{0x81a5,0x81a5,0x4753},
{0x81a6,0x81a6,0x2c30},
{0x81a7,0x81a7,0x2c3a},
{0x81a8,0x81a8,0x1331},
{0x81a9,0x81a9,0x1330},
{0x81aa,0x81aa,0x4364},
{0x81ab,0x81ab,0x2c34},
{0x81ac,0x81ac,0x2c36},
{0x81ae,0x81ae,0x2c31},
{0x81b0,0x81b0,0x2c35},
{0x81b1,0x81b1,0x2c2f},
{0x81b2,0x81b2,0x2c38},
{0x81b3,0x81b3,0x132f},
{0x81b4,0x81b4,0x2c37},
{0x81b5,0x81b5,0x2c33},
{0x81b6,0x81b6,0x3eb2},
{0x81b7,0x81b7,0x2c39},
{0x81b9,0x81b9,0x2c32},
{0x81ba,0x81ba,0x1450},
{0x81bb,0x81bb,0x2e06},
{0x81bc,0x81bc,0x2e0c},
{0x81bd,0x81bd,0x1454},
{0x81be,0x81be,0x1456},
{0x81bf,0x81bf,0x1453},
{0x81c0,0x81c0,0x1452},
{0x81c1,0x81c1,0x4755},
{0x81c2,0x81c2,0x1451},
{0x81c3,0x81c3,0x144f},
{0x81c4,0x81c4,0x2e07},
{0x81c5,0x81c5,0x2e0a},
{0x81c6,0x81c6,0x144e},
{0x81c7,0x81c7,0x2e0b},
{0x81c8,0x81c8,0x3fee},
{0x81c9,0x81c9,0x1455},
{0x81ca,0x81ca,0x2e09},
{0x81cc,0x81cc,0x2e08},
{0x81cd,0x81cd,0x1518},
{0x81cf,0x81cf,0x1519},
{0x81d0,0x81d0,0x2f94},
{0x81d1,0x81d2,0x2f92},
{0x81d5,0x81d5,0x30ed},
{0x81d7,0x81d7,0x30ec},
{0x81d8,0x81d8,0x15b3},
{0x81d9,0x81d9,0x321c},
{0x81da,0x81da,0x1635},
{0x81db,0x81db,0x321b},
{0x81dd,0x81dd,0x32f5},
{0x81de,0x81de,0x33a5},
{0x81df,0x81df,0x16d4},
{0x81e0,0x81e1,0x34dd},
{0x81e2,0x81e2,0x1706},
{0x81e3,0x81e3,0x405},
{0x81e4,0x81e4,0x4756},
{0x81e5,0x81e5,0x676},
{0x81e6,0x81e6,0x2286},
{0x81e7,0x81e7,0x107c},
{0x81e8,0x81e8,0x1457},
{0x81e9,0x81e9,0x2e0d},
{0x81ea,0x81ea,0x406},
{0x81ec,0x81ec,0x995},
{0x81ed,0x81ed,0x994},
{0x81ee,0x81ee,0x2287},
{0x81ef,0x81ef,0x42d1},
{0x81f2,0x81f2,0x2c3b},
{0x81f3,0x81f3,0x407},
{0x81f4,0x81f4,0x7e7},
{0x81f6,0x81f6,0x3d61},
{0x81f7,0x81f9,0x2288},
{0x81fa,0x81fa,0x107d},
{0x81fb,0x81fb,0x1332},
{0x81fc,0x81fc,0x408},
{0x81fe,0x81fe,0x677},
{0x81ff,0x81ff,0x1bde},
{0x8200,0x8200,0x996},
{0x8201,0x8201,0x1dbd},
{0x8202,0x8202,0xb71},
{0x8204,0x8204,0x228b},
{0x8205,0x8205,0xed9},
{0x8207,0x8207,0x107e},
{0x8208,0x8208,0x1333},
{0x8209,0x8209,0x1458},
{0x820a,0x820a,0x151a},
{0x820b,0x820b,0x30ee},
{0x820c,0x820c,0x409},
{0x820d,0x820d,0x678},
{0x8210,0x8210,0x997},
{0x8211,0x8211,0x2013},
{0x8212,0x8212,0xd2c},
{0x8214,0x8214,0x107f},
{0x8215,0x8215,0x27a5},
{0x8216,0x8216,0x29d5},
{0x8218,0x8218,0x37aa},
{0x821a,0x821a,0x3fe1},
{0x821b,0x821b,0x40a},
{0x821c,0x821c,0xd2d},
{0x821d,0x821d,0x2529},
{0x821e,0x821e,0x1080},
{0x821f,0x821f,0x40b},
{0x8220,0x8220,0x1a2e},
{0x8221,0x8221,0x1bdf},
{0x8222,0x8222,0x7e8},
{0x8225,0x8225,0x1dbf},
{0x8226,0x8226,0x420d},
{0x8228,0x8228,0x99a},
{0x8229,0x8229,0x37b0},
{0x822a,0x822b,0x998},
{0x822c,0x822c,0x99b},
{0x822d,0x822d,0x3ed0},
{0x822f,0x822f,0x1dbe},
{0x8232,0x8232,0x2018},
{0x8233,0x8233,0x2015},
{0x8234,0x8234,0x2017},
{0x8235,0x8235,0xb72},
{0x8236,0x8236,0xb74},
{0x8237,0x8237,0xb73},
{0x8238,0x8238,0x2014},
{0x8239,0x8239,0xb75},
{0x823a,0x823a,0x2016},
{0x823c,0x823d,0x228c},
{0x823e,0x823e,0x4582},
{0x823f,0x823f,0x228e},
{0x8240,0x8240,0x252c},
{0x8242,0x8242,0x252d},
{0x8244,0x8244,0x252b},
{0x8245,0x8245,0x252e},
{0x8247,0x8247,0xeda},
{0x8249,0x8249,0x252a},
{0x824b,0x824b,0x1081},
{0x824e,0x824e,0x29da},
{0x824f,0x824f,0x29d6},
{0x8250,0x8250,0x29d9},
{0x8251,0x8251,0x29db},
{0x8252,0x8252,0x29d8},
{0x8253,0x8253,0x29d7},
{0x8254,0x8254,0x4757},
{0x8255,0x8257,0x2c3c},
{0x8258,0x8259,0x1334},
{0x825a,0x825a,0x2e0f},
{0x825b,0x825b,0x2e0e},
{0x825c,0x825c,0x2e10},
{0x825e,0x825e,0x2f96},
{0x825f,0x825f,0x2f95},
{0x8261,0x8261,0x30f0},
{0x8262,0x8262,0x3b7a},
{0x8263,0x8263,0x30f1},
{0x8264,0x8264,0x30ef},
{0x8265,0x8265,0x3b7b},
{0x8266,0x8266,0x1636},
{0x8268,0x8269,0x321d},
{0x826b,0x826b,0x33a6},
{0x826c,0x826c,0x342d},
{0x826d,0x826d,0x3496},
{0x826e,0x826e,0x40c},
{0x826f,0x826f,0x504},
{0x8271,0x8271,0x1459},
{0x8272,0x8272,0x40d},
{0x8274,0x8274,0x2019},
{0x8275,0x8275,0x228f},
{0x8276,0x8276,0x4759},
{0x8277,0x8277,0x172e},
{0x8278,0x8278,0x1813},
{0x8279,0x8279,0x4722},
{0x827a,0x827a,0x4563},
{0x827b,0x827b,0x37c8},
{0x827c,0x827c,0x1814},
{0x827d,0x827d,0x1816},
{0x827e,0x827e,0x40e},
{0x827f,0x827f,0x1817},
{0x8280,0x8280,0x1815},
{0x8283,0x8284,0x18e5},
{0x8285,0x8285,0x18e0},
{0x8287,0x8287,0x4132},
{0x828a,0x828a,0x18e4},
{0x828b,0x828b,0x506},
{0x828d,0x828d,0x507},
{0x828e,0x828e,0x18e1},
{0x828f,0x828f,0x18df},
{0x8290,0x8290,0x18de},
{0x8291,0x8291,0x18e2},
{0x8292,0x8292,0x505},
{0x8293,0x8293,0x18e3},
{0x8294,0x8294,0x1be0},
{0x8298,0x8298,0x1a33},
{0x8299,0x8299,0x67b},
{0x829a,0x829a,0x1a32},
{0x829b,0x829b,0x1a34},
{0x829d,0x829d,0x67a},
{0x829e,0x829e,0x1a39},
{0x829f,0x829f,0x67e},
{0x82a0,0x82a0,0x1a2f},
{0x82a1,0x82a1,0x1a3d},
{0x82a2,0x82a2,0x1a43},
{0x82a3,0x82a3,0x685},
{0x82a4,0x82a4,0x1a40},
{0x82a5,0x82a5,0x682},
{0x82a6,0x82a6,0x3d73},
{0x82a7,0x82a7,0x1a36},
{0x82a8,0x82a8,0x1a3c},
{0x82a9,0x82a9,0x1a3e},
{0x82aa,0x82aa,0x37bc},
{0x82ab,0x82ab,0x1a31},
{0x82ac,0x82ac,0x681},
{0x82ad,0x82ad,0x67c},
{0x82ae,0x82ae,0x1a37},
{0x82af,0x82af,0x683},
{0x82b0,0x82b0,0x686},
{0x82b1,0x82b1,0x680},
{0x82b3,0x82b3,0x679},
{0x82b4,0x82b4,0x1a3b},
{0x82b5,0x82b5,0x1a35},
{0x82b6,0x82b6,0x1a42},
{0x82b7,0x82b7,0x688},
{0x82b8,0x82b8,0x684},
{0x82b9,0x82b9,0x67f},
{0x82ba,0x82ba,0x1a3a},
{0x82bb,0x82bb,0x99c},
{0x82bc,0x82bc,0x1a38},
{0x82bd,0x82bd,0x67d},
{0x82be,0x82be,0x687},
{0x82c0,0x82c0,0x1a30},
{0x82c2,0x82c2,0x1a3f},
{0x82c3,0x82c3,0x1a41},
{0x82c4,0x82c4,0x45e3},
{0x82ca,0x82ca,0x475a},
{0x82cf,0x82cf,0x4564},
{0x82d0,0x82d0,0x37c1},
{0x82d1,0x82d1,0x7f9},
{0x82d2,0x82d2,0x7f3},
{0x82d3,0x82d3,0x7fb},
{0x82d4,0x82d4,0x7f8},
{0x82d5,0x82d5,0x1be7},
{0x82d6,0x82d6,0x1bea},
{0x82d7,0x82d7,0x7f4},
{0x82d8,0x82d8,0x475b},
{0x82d9,0x82d9,0x1be1},
{0x82db,0x82db,0x7ed},
{0x82dc,0x82dc,0x7f7},
{0x82de,0x82de,0x7fa},
{0x82df,0x82df,0x7fc},
{0x82e0,0x82e0,0x1bf6},
{0x82e1,0x82e1,0x1bed},
{0x82e2,0x82e2,0x37c9},
{0x82e3,0x82e3,0x7ec},
{0x82e4,0x82e4,0x1bf5},
{0x82e5,0x82e5,0x7f0},
{0x82e6,0x82e6,0x7ee},
{0x82e7,0x82e7,0x7e9},
{0x82e8,0x82e8,0x1be5},
{0x82ea,0x82ea,0x1bf4},
{0x82eb,0x82eb,0x1be9},
{0x82ec,0x82ec,0x1bec},
{0x82ed,0x82ed,0x1bf9},
{0x82ee,0x82ee,0x4018},
{0x82ef,0x82ef,0x7fd},
{0x82f0,0x82f0,0x1bf3},
{0x82f1,0x82f1,0x7f5},
{0x82f2,0x82f2,0x1bee},
{0x82f3,0x82f3,0x1bf8},
{0x82f4,0x82f4,0x1beb},
{0x82f5,0x82f5,0x1bef},
{0x82f6,0x82f6,0x1bf2},
{0x82f7,0x82f7,0x3e77},
{0x82f9,0x82f9,0x1be3},
{0x82fa,0x82fa,0x1bf7},
{0x82fb,0x82fb,0x1bf1},
{0x82fc,0x82fc,0x412d},
{0x82fd,0x82fd,0x3d92},
{0x82fe,0x82fe,0x1be2},
{0x82ff,0x82ff,0x43de},
{0x8300,0x8300,0x1be6},
{0x8301,0x8301,0x7f6},
{0x8302,0x8302,0x7f1},
{0x8303,0x8303,0x7ea},
{0x8304,0x8304,0x7ef},
{0x8305,0x8305,0x7eb},
{0x8306,0x8306,0x7fe},
{0x8307,0x8307,0x1be4},
{0x8308,0x8308,0x1dd0},
{0x8309,0x8309,0x7f2},
{0x830b,0x830b,0x3eb6},
{0x830c,0x830c,0x1bf0},
{0x830d,0x830d,0x1a7d},
{0x8316,0x8316,0x1dd3},
{0x8317,0x8317,0x9aa},
{0x8318,0x8318,0x37ca},
{0x8319,0x8319,0x1dc3},
{0x831a,0x831a,0x37c2},
{0x831b,0x831b,0x1dce},
{0x831c,0x831c,0x1dca},
{0x831d,0x831d,0x37d1},
{0x831e,0x831e,0x1ddd},
{0x8320,0x8320,0x1dd5},
{0x8322,0x8322,0x1dcb},
{0x8324,0x8324,0x1dd4},
{0x8325,0x8325,0x1dc5},
{0x8326,0x8326,0x1dc9},
{0x8327,0x8327,0x1de0},
{0x8328,0x8328,0x9ad},
{0x8329,0x8329,0x1dd8},
{0x832a,0x832a,0x1dcf},
{0x832b,0x832b,0x99d},
{0x832c,0x832c,0x1dde},
{0x832d,0x832d,0x1dc1},
{0x832f,0x832f,0x1dd7},
{0x8331,0x8331,0x9ac},
{0x8332,0x8332,0x9a7},
{0x8333,0x8333,0x1dc0},
{0x8334,0x8334,0x9a5},
{0x8335,0x8335,0x9a4},
{0x8336,0x8336,0x9a9},
{0x8337,0x8337,0x1dd6},
{0x8338,0x8338,0x9a1},
{0x8339,0x8339,0x9a8},
{0x833a,0x833a,0x1be8},
{0x833b,0x833b,0x2290},
{0x833c,0x833c,0x1dd1},
{0x833d,0x833d,0x3d2a},
{0x833f,0x833f,0x1dc7},
{0x8340,0x8340,0x9ab},
{0x8341,0x8341,0x1dc8},
{0x8342,0x8342,0x1dcc},
{0x8343,0x8343,0x9ae},
{0x8344,0x8344,0x1dc2},
{0x8345,0x8345,0x1dda},
{0x8347,0x8347,0x1dd9},
{0x8348,0x8348,0x1de1},
{0x8349,0x8349,0x9a3},
{0x834a,0x834a,0x9a0},
{0x834b,0x834b,0x1ddf},
{0x834c,0x834c,0x1ddb},
{0x834d,0x834d,0x1dd2},
{0x834e,0x834e,0x1dcd},
{0x834f,0x834f,0x9a6},
{0x8350,0x8350,0x9a2},
{0x8351,0x8351,0x1dc4},
{0x8352,0x8352,0x99e},
{0x8353,0x8353,0x1ddc},
{0x8354,0x8354,0x99f},
{0x8356,0x8356,0x1dc6},
{0x8357,0x8357,0x475d},
{0x8362,0x8362,0x37b6},
{0x8363,0x8363,0x41eb},
{0x8366,0x8366,0x3e1e},
{0x836f,0x836f,0x4565},
{0x8373,0x8373,0x201f},
{0x8374,0x8374,0x2021},
{0x8375,0x8375,0x2026},
{0x8376,0x8376,0x203a},
{0x8377,0x8377,0xb83},
{0x8378,0x8378,0xb79},
{0x837a,0x837a,0x201e},
{0x837b,0x837c,0xb84},
{0x837d,0x837d,0x2029},
{0x837e,0x837e,0x2030},
{0x837f,0x837f,0x2036},
{0x8381,0x8381,0x2023},
{0x8383,0x8383,0x202a},
{0x8385,0x8385,0x37d3},
{0x8386,0x8386,0xb86},
{0x8387,0x8387,0x2038},
{0x8388,0x8388,0x2033},
{0x8389,0x8389,0xb81},
{0x838a,0x838a,0xb7f},
{0x838b,0x838b,0x202f},
{0x838c,0x838c,0x202b},
{0x838d,0x838d,0x201d},
{0x838e,0x838e,0xb76},
{0x838f,0x838f,0x2022},
{0x8390,0x8390,0x201a},
{0x8391,0x8391,0x37ec},
{0x8392,0x8392,0xb7e},
{0x8393,0x8393,0xb80},
{0x8394,0x8394,0x2027},
{0x8395,0x8395,0x2024},
{0x8396,0x8396,0xb7b},
{0x8397,0x8397,0x2034},
{0x8398,0x8398,0xb78},
{0x8399,0x8399,0x2025},
{0x839a,0x839a,0x22b9},
{0x839b,0x839b,0x202d},
{0x839c,0x839c,0x374d},
{0x839d,0x839d,0x202c},
{0x839e,0x839e,0xb77},
{0x83a0,0x83a0,0xb82},
{0x83a2,0x83a2,0xb7a},
{0x83a3,0x83a3,0x201b},
{0x83a4,0x83a4,0x2020},
{0x83a5,0x83a5,0x2031},
{0x83a6,0x83a6,0x2037},
{0x83a7,0x83a7,0xb87},
{0x83a8,0x83a8,0x201c},
{0x83a9,0x83a9,0x2028},
{0x83aa,0x83aa,0x202e},
{0x83ab,0x83ab,0xb7d},
{0x83ac,0x83ac,0x37d6},
{0x83ae,0x83ae,0x2039},
{0x83af,0x83af,0x2032},
{0x83b0,0x83b0,0x2035},
{0x83b9,0x83b9,0x42a7},
{0x83bd,0x83bd,0xb7c},
{0x83be,0x83be,0x3f59},
{0x83bf,0x83bf,0x22a0},
{0x83c0,0x83c0,0x2294},
{0x83c1,0x83c1,0xd35},
{0x83c2,0x83c2,0x22b1},
{0x83c3,0x83c3,0x22ba},
{0x83c4,0x83c4,0x22bd},
{0x83c5,0x83c5,0xd33},
{0x83c6,0x83c6,0x229c},
{0x83c7,0x83c7,0x22b5},
{0x83c8,0x83c8,0x229d},
{0x83c9,0x83c9,0x22ab},
{0x83ca,0x83ca,0xd40},
{0x83cb,0x83cb,0x22a7},
{0x83cc,0x83cc,0xd3d},
{0x83cd,0x83cd,0x4055},
{0x83ce,0x83ce,0x22a8},
{0x83cf,0x83cf,0x2291},
{0x83d1,0x83d1,0x22b6},
{0x83d3,0x83d3,0x37d8},
{0x83d4,0x83d4,0xd46},
{0x83d5,0x83d5,0x22b3},
{0x83d6,0x83d6,0x22a9},
{0x83d7,0x83d7,0x22bf},
{0x83d8,0x83d8,0x22a4},
{0x83d9,0x83d9,0x254e},
{0x83db,0x83db,0x22c2},
{0x83dc,0x83dc,0xd44},
{0x83dd,0x83dd,0x22a2},
{0x83de,0x83de,0x22ae},
{0x83df,0x83df,0xd47},
{0x83e0,0x83e0,0xd32},
{0x83e1,0x83e1,0x22a6},
{0x83e2,0x83e2,0x22c0},
{0x83e3,0x83e3,0x229f},
{0x83e4,0x83e4,0x2298},
{0x83e5,0x83e5,0x22a3},
{0x83e7,0x83e7,0x2297},
{0x83e8,0x83e8,0x2295},
{0x83e9,0x83e9,0xd2e},
{0x83ea,0x83ea,0x22b7},
{0x83eb,0x83eb,0x229e},
{0x83ec,0x83ec,0x22bb},
{0x83ed,0x83ed,0x3d2c},
{0x83ee,0x83ee,0x22bc},
{0x83ef,0x83ef,0xd36},
{0x83f0,0x83f0,0xd3b},
{0x83f1,0x83f1,0xd37},
{0x83f2,0x83f2,0xd3f},
{0x83f3,0x83f3,0x22b2},
{0x83f4,0x83f4,0xd38},
{0x83f5,0x83f5,0x22aa},
{0x83f6,0x83f6,0x229a},
{0x83f8,0x83f8,0xd30},
{0x83f9,0x83f9,0x2292},
{0x83fa,0x83fa,0x22b4},
{0x83fb,0x83fb,0x22be},
{0x83fc,0x83fc,0x2299},
{0x83fd,0x83fd,0xd3e},
{0x83fe,0x83fe,0x22c3},
{0x83ff,0x83ff,0x22a5},
{0x8401,0x8401,0x22a1},
{0x8403,0x8403,0xd2f},
{0x8404,0x8404,0xd43},
{0x8405,0x8405,0x475f},
{0x8406,0x8406,0x22b0},
{0x8407,0x8407,0xd45},
{0x8409,0x8409,0x22ac},
{0x840a,0x840a,0xd3a},
{0x840b,0x840b,0xd34},
{0x840c,0x840c,0xd3c},
{0x840d,0x840d,0xd31},
{0x840e,0x840e,0xd42},
{0x840f,0x840f,0x22ad},
{0x8410,0x8410,0x229b},
{0x8411,0x8411,0x22af},
{0x8412,0x8412,0x2296},
{0x8413,0x8413,0x22b8},
{0x8414,0x8414,0x3c10},
{0x8416,0x8416,0x4037},
{0x8418,0x8418,0x3e7a},
{0x841b,0x841b,0x22c1},
{0x841c,0x841c,0x3eb3},
{0x8420,0x8420,0x3c55},
{0x8421,0x8421,0x3751},
{0x8423,0x8423,0x2293},
{0x8424,0x8424,0x4951},
{0x8426,0x8426,0x46d8},
{0x8429,0x8429,0x254d},
{0x842b,0x842b,0x2563},
{0x842c,0x842c,0xeac},
{0x842d,0x842d,0x2552},
{0x842e,0x842e,0x422d},
{0x842f,0x842f,0x2550},
{0x8430,0x8430,0x253b},
{0x8431,0x8431,0xede},
{0x8432,0x8432,0x254b},
{0x8433,0x8433,0x255f},
{0x8434,0x8434,0x2547},
{0x8435,0x8435,0xee6},
{0x8436,0x8436,0x255e},
{0x8437,0x8437,0x2545},
{0x8438,0x8438,0xd41},
{0x8439,0x8439,0x2555},
{0x843a,0x843a,0x2546},
{0x843b,0x843b,0x255c},
{0x843c,0x843c,0xee5},
{0x843d,0x843d,0xedd},
{0x843e,0x843e,0x3d21},
{0x843f,0x843f,0x2530},
{0x8440,0x8440,0x2538},
{0x8442,0x8442,0x2551},
{0x8443,0x8443,0x2549},
{0x8444,0x8444,0x2562},
{0x8445,0x8445,0x254c},
{0x8446,0x8446,0xeeb},
{0x8447,0x8447,0x255d},
{0x8448,0x8448,0x4718},
{0x8449,0x8449,0xee2},
{0x844a,0x844a,0x37e4},
{0x844b,0x844b,0x254f},
{0x844c,0x844c,0x2557},
{0x844d,0x844d,0x253c},
{0x844e,0x844e,0x2556},
{0x8450,0x8450,0x2567},
{0x8451,0x8451,0x2537},
{0x8452,0x8452,0x2558},
{0x8453,0x8453,0x3b7d},
{0x8454,0x8454,0x2565},
{0x8455,0x8455,0x398b},
{0x8456,0x8456,0x2531},
{0x8457,0x8457,0xd39},
{0x8458,0x8458,0x37de},
{0x8459,0x8459,0x253f},
{0x845a,0x845a,0x253e},
{0x845b,0x845b,0xee4},
{0x845c,0x845c,0x3edb},
{0x845d,0x845d,0x2542},
{0x845e,0x845e,0x2544},
{0x845f,0x845f,0x2553},
{0x8460,0x8460,0x2564},
{0x8461,0x8461,0xee7},
{0x8462,0x8462,0x42da},
{0x8463,0x8463,0xee8},
{0x8464,0x8464,0x4761},
{0x8465,0x8465,0x2536},
{0x8466,0x8466,0xee0},
{0x8467,0x8467,0x253a},
{0x8468,0x8468,0x2560},
{0x8469,0x8469,0xee9},
{0x846b,0x846b,0xee1},
{0x846c,0x846c,0xee3},
{0x846d,0x846d,0xeea},
{0x846e,0x846e,0x2566},
{0x846f,0x846f,0x2559},
{0x8470,0x8470,0x2554},
{0x8471,0x8471,0x37e0},
{0x8472,0x8472,0x3c93},
{0x8473,0x8473,0x2541},
{0x8474,0x8474,0x2540},
{0x8475,0x8475,0xedf},
{0x8476,0x8476,0x2532},
{0x8477,0x8477,0xedc},
{0x8478,0x8478,0x254a},
{0x8479,0x8479,0x2533},
{0x847a,0x847a,0x2548},
{0x847d,0x847d,0x253d},
{0x847e,0x847e,0x2561},
{0x847f,0x847f,0x394e},
{0x8482,0x8482,0xedb},
{0x8486,0x8486,0x2539},
{0x8488,0x8488,0x4762},
{0x848d,0x848d,0x2535},
{0x848e,0x848e,0x255b},
{0x848f,0x848f,0x2534},
{0x8490,0x8490,0x108e},
{0x8491,0x8491,0x27cd},
{0x8492,0x8492,0x3756},
{0x8493,0x8493,0x37ee},
{0x8494,0x8494,0x27bc},
{0x8497,0x8497,0x27a6},
{0x8498,0x8498,0x27c4},
{0x8499,0x8499,0x1086},
{0x849a,0x849a,0x27b5},
{0x849b,0x849b,0x27bf},
{0x849c,0x849c,0x1089},
{0x849d,0x849d,0x27b8},
{0x849e,0x849e,0x1087},
{0x849f,0x849f,0x27a9},
{0x84a0,0x84a0,0x27c7},
{0x84a1,0x84a1,0x27a8},
{0x84a2,0x84a2,0x27bb},
{0x84a3,0x84a3,0x3b9e},
{0x84a4,0x84a4,0x27a7},
{0x84a7,0x84a7,0x27b9},
{0x84a8,0x84a8,0x27c2},
{0x84a9,0x84a9,0x27c0},
{0x84aa,0x84aa,0x27b4},
{0x84ab,0x84ab,0x27af},
{0x84ac,0x84ac,0x27ad},
{0x84ad,0x84ad,0x3d8c},
{0x84ae,0x84ae,0x27ae},
{0x84af,0x84af,0x27c1},
{0x84b0,0x84b0,0x27cc},
{0x84b1,0x84b1,0x27b6},
{0x84b2,0x84b2,0x1088},
{0x84b4,0x84b4,0x27b1},
{0x84b6,0x84b6,0x27c5},
{0x84b8,0x84b8,0x108b},
{0x84b9,0x84b9,0x27b0},
{0x84ba,0x84ba,0x27aa},
{0x84bb,0x84bb,0x27ba},
{0x84bc,0x84bc,0x108f},
{0x84bd,0x84bd,0x3c4c},
{0x84be,0x84be,0x4764},
{0x84bf,0x84bf,0x1083},
{0x84c0,0x84c0,0x108c},
{0x84c1,0x84c1,0x27b2},
{0x84c2,0x84c2,0x27ac},
{0x84c4,0x84c4,0x1085},
{0x84c5,0x84c5,0x255a},
{0x84c6,0x84c6,0x1084},
{0x84c7,0x84c7,0x27bd},
{0x84c9,0x84c9,0x1082},
{0x84ca,0x84ca,0x1091},
{0x84cb,0x84cb,0x108a},
{0x84cc,0x84cc,0x27be},
{0x84cd,0x84cd,0x27b3},
{0x84ce,0x84ce,0x27ab},
{0x84cf,0x84cf,0x27c6},
{0x84d0,0x84d0,0x27b7},
{0x84d1,0x84d1,0x1090},
{0x84d2,0x84d2,0x27ca},
{0x84d3,0x84d3,0x108d},
{0x84d4,0x84d4,0x27c9},
{0x84d6,0x84d6,0x27c3},
{0x84d7,0x84d7,0x27c8},
{0x84da,0x84da,0x3ff5},
{0x84db,0x84db,0x27cb},
{0x84de,0x84de,0x37ea},
{0x84e1,0x84e1,0x4765},
{0x84e2,0x84e2,0x37b5},
{0x84e4,0x84e4,0x37ef},
{0x84e5,0x84e5,0x3bd0},
{0x84e7,0x84e8,0x2a03},
{0x84e9,0x84e9,0x29f7},
{0x84ea,0x84ea,0x29f6},
{0x84eb,0x84eb,0x29f2},
{0x84ec,0x84ec,0x11fb},
{0x84ee,0x84ee,0x11f3},
{0x84ef,0x84ef,0x2a06},
{0x84f0,0x84f0,0x2a05},
{0x84f1,0x84f1,0x252f},
{0x84f2,0x84f2,0x29ef},
{0x84f3,0x84f3,0x29f3},
{0x84f4,0x84f4,0x29ed},
{0x84f6,0x84f6,0x2a00},
{0x84f7,0x84f7,0x29f1},
{0x84f8,0x84f8,0x4766},
{0x84f9,0x84f9,0x2a07},
{0x84fa,0x84fa,0x29ea},
{0x84fb,0x84fb,0x29e8},
{0x84fc,0x84fc,0x29f4},
{0x84fd,0x84fd,0x29fe},
{0x84fe,0x84fe,0x29f9},
{0x84ff,0x84ff,0x11fd},
{0x8500,0x8500,0x29df},
{0x8502,0x8502,0x29fd},
{0x8505,0x8505,0x40eb},
{0x8506,0x8506,0x11fe},
{0x8507,0x8507,0x2543},
{0x8508,0x8508,0x29eb},
{0x8509,0x8509,0x29e2},
{0x850a,0x850a,0x29e5},
{0x850b,0x850b,0x2a0b},
{0x850c,0x850c,0x29ec},
{0x850d,0x850d,0x29e3},
{0x850e,0x850e,0x29e1},
{0x850f,0x850f,0x29de},
{0x8510,0x8510,0x4767},
{0x8511,0x8511,0x11f7},
{0x8512,0x8512,0x29f5},
{0x8513,0x8513,0x11f6},
{0x8514,0x8514,0x11fa},
{0x8515,0x8515,0x29f0},
{0x8516,0x8516,0x29f8},
{0x8517,0x8517,0x11f0},
{0x8518,0x8518,0x2a08},
{0x8519,0x8519,0x2a0c},
{0x851a,0x851a,0x11f2},
{0x851c,0x851c,0x29e7},
{0x851d,0x851d,0x29fb},
{0x851e,0x851e,0x29ff},
{0x851f,0x851f,0x29e4},
{0x8520,0x8520,0x2a09},
{0x8521,0x8521,0x11f9},
{0x8523,0x8523,0x11f8},
{0x8524,0x8524,0x29dc},
{0x8525,0x8525,0x11fc},
{0x8526,0x8526,0x2a02},
{0x8527,0x8527,0x29e6},
{0x8528,0x8528,0x29fa},
{0x8529,0x8529,0x29e0},
{0x852a,0x852a,0x29ee},
{0x852b,0x852b,0x29e9},
{0x852c,0x852d,0x11f4},
{0x852e,0x852e,0x29fc},
{0x852f,0x852f,0x2a0d},
{0x8530,0x8530,0x2a0a},
{0x8531,0x8531,0x2a01},
{0x8533,0x8533,0x4228},
{0x8534,0x8534,0x37f4},
{0x8538,0x8538,0x4768},
{0x853b,0x853b,0x29dd},
{0x853d,0x853d,0x11f1},
{0x853e,0x853e,0x2c4f},
{0x8540,0x8540,0x2c46},
{0x8541,0x8541,0x2c49},
{0x8542,0x8542,0x4062},
{0x8543,0x8543,0x133b},
{0x8544,0x8544,0x2c4b},
{0x8545,0x8545,0x2c40},
{0x8546,0x8546,0x2c47},
{0x8547,0x8547,0x2c4d},
{0x8548,0x8548,0x1338},
{0x8549,0x8549,0x133c},
{0x854a,0x854a,0x1336},
{0x854b,0x854b,0x37c0},
{0x854c,0x854c,0x400f},
{0x854d,0x854d,0x2c42},
{0x854e,0x854e,0x2c52},
{0x8551,0x8551,0x2c4c},
{0x8552,0x8552,0x4769},
{0x8553,0x8553,0x2c43},
{0x8554,0x8554,0x2c5b},
{0x8555,0x8555,0x2c55},
{0x8556,0x8556,0x2c3f},
{0x8557,0x8557,0x2e23},
{0x8558,0x8558,0x2c45},
{0x8559,0x8559,0x1337},
{0x855a,0x855a,0x37db},
{0x855b,0x855b,0x2c50},
{0x855d,0x855d,0x2c5a},
{0x855e,0x855e,0x133f},
{0x8560,0x8560,0x2c57},
{0x8561,0x8561,0x2c44},
{0x8562,0x8562,0x2c4a},
{0x8563,0x8563,0x2c4e},
{0x8564,0x8564,0x2c48},
{0x8565,0x8565,0x2c5c},
{0x8566,0x8566,0x2c59},
{0x8567,0x8567,0x2c56},
{0x8568,0x8569,0x1339},
{0x856a,0x856a,0x133e},
{0x856b,0x856b,0x2c41},
{0x856c,0x856c,0x2c5d},
{0x856d,0x856d,0x133d},
{0x856e,0x856e,0x2c53},
{0x856f,0x8570,0x476b},
{0x8571,0x8571,0x2c51},
{0x8573,0x8573,0x37f8},
{0x8575,0x8575,0x2c54},
{0x8576,0x8576,0x2e30},
{0x8577,0x8577,0x2e1d},
{0x8578,0x8578,0x2e22},
{0x8579,0x8579,0x2e2f},
{0x857a,0x857a,0x2e21},
{0x857b,0x857b,0x2e19},
{0x857c,0x857c,0x2e1e},
{0x857e,0x857e,0x145c},
{0x8580,0x8580,0x2e12},
{0x8581,0x8581,0x2e2a},
{0x8582,0x8582,0x2e2c},
{0x8583,0x8583,0x2e11},
{0x8584,0x8584,0x145b},
{0x8585,0x8585,0x2e2e},
{0x8586,0x8586,0x2e26},
{0x8587,0x8587,0x1462},
{0x8588,0x8588,0x2e2d},
{0x8589,0x8589,0x2e1f},
{0x858a,0x858a,0x1464},
{0x858b,0x858b,0x2e17},
{0x858c,0x858c,0x2c58},
{0x858d,0x858d,0x2e27},
{0x858e,0x858e,0x2e24},
{0x858f,0x858f,0x2e13},
{0x8590,0x8590,0x2e32},
{0x8591,0x8591,0x145e},
{0x8594,0x8594,0x145f},
{0x8595,0x8595,0x2e15},
{0x8596,0x8596,0x2e25},
{0x8598,0x8598,0x2e31},
{0x8599,0x8599,0x2e28},
{0x859a,0x859a,0x2e1b},
{0x859b,0x859b,0x1461},
{0x859c,0x859c,0x145d},
{0x859d,0x859d,0x2e29},
{0x859e,0x859e,0x2e1c},
{0x859f,0x859f,0x2e33},
{0x85a0,0x85a0,0x2e16},
{0x85a1,0x85a1,0x2e20},
{0x85a2,0x85a2,0x2e2b},
{0x85a3,0x85a3,0x2e18},
{0x85a4,0x85a4,0x2e1a},
{0x85a6,0x85a6,0x1465},
{0x85a7,0x85a7,0x2e14},
{0x85a8,0x85a8,0x1463},
{0x85a9,0x85a9,0x151c},
{0x85aa,0x85aa,0x145a},
{0x85af,0x85af,0x1460},
{0x85b0,0x85b0,0x1520},
{0x85b1,0x85b1,0x2fa6},
{0x85b3,0x85b3,0x2f9c},
{0x85b4,0x85b4,0x2f97},
{0x85b5,0x85b5,0x2f9d},
{0x85b6,0x85b6,0x2fa7},
{0x85b7,0x85b7,0x2fab},
{0x85b8,0x85b8,0x2faa},
{0x85b9,0x85b9,0x1522},
{0x85ba,0x85ba,0x1521},
{0x85bd,0x85bd,0x2f9e},
{0x85be,0x85be,0x2fac},
{0x85bf,0x85bf,0x2fa1},
{0x85c0,0x85c0,0x2f99},
{0x85c1,0x85c1,0x37fe},
{0x85c2,0x85c2,0x2f9b},
{0x85c3,0x85c3,0x2f9a},
{0x85c4,0x85c4,0x2fa0},
{0x85c5,0x85c5,0x2fa5},
{0x85c6,0x85c6,0x2f98},
{0x85c7,0x85c7,0x2f9f},
{0x85c8,0x85c8,0x2fa4},
{0x85c9,0x85c9,0x151f},
{0x85cb,0x85cb,0x2fa2},
{0x85cd,0x85cd,0x151d},
{0x85ce,0x85ce,0x2fa3},
{0x85cf,0x85cf,0x151b},
{0x85d0,0x85d0,0x151e},
{0x85d1,0x85d1,0x3101},
{0x85d2,0x85d2,0x2fa8},
{0x85d5,0x85d5,0x15b7},
{0x85d7,0x85d7,0x30f9},
{0x85d8,0x85d8,0x30fd},
{0x85d9,0x85d9,0x30f5},
{0x85da,0x85da,0x30f8},
{0x85dc,0x85dc,0x3100},
{0x85dd,0x85dd,0x15b5},
{0x85de,0x85de,0x3105},
{0x85df,0x85df,0x30fe},
{0x85e0,0x85e0,0x476d},
{0x85e1,0x85e1,0x30f6},
{0x85e2,0x85e2,0x3106},
{0x85e3,0x85e3,0x30ff},
{0x85e4,0x85e5,0x15b8},
{0x85e6,0x85e6,0x3103},
{0x85e8,0x85e8,0x30f7},
{0x85e9,0x85e9,0x15b4},
{0x85ea,0x85ea,0x15b6},
{0x85eb,0x85eb,0x30f2},
{0x85ec,0x85ec,0x30fa},
{0x85ed,0x85ed,0x30f4},
{0x85ee,0x85ee,0x3c56},
{0x85ef,0x85ef,0x3104},
{0x85f0,0x85f0,0x3102},
{0x85f1,0x85f1,0x30f3},
{0x85f2,0x85f2,0x30fb},
{0x85f6,0x85f6,0x3225},
{0x85f7,0x85f7,0x15ba},
{0x85f8,0x85f8,0x30fc},
{0x85f9,0x85f9,0x1638},
{0x85fa,0x85fa,0x163a},
{0x85fb,0x85fb,0x1637},
{0x85fc,0x85fc,0x3c50},
{0x85fd,0x85fd,0x322a},
{0x85fe,0x85fe,0x3222},
{0x85ff,0x85ff,0x3220},
{0x8600,0x8600,0x3224},
{0x8601,0x8601,0x3221},
{0x8602,0x8602,0x3800},
{0x8604,0x8604,0x3226},
{0x8605,0x8605,0x3228},
{0x8606,0x8606,0x163b},
{0x8607,0x8607,0x163d},
{0x8609,0x8609,0x3227},
{0x860a,0x860a,0x163e},
{0x860b,0x860b,0x163c},
{0x860c,0x860c,0x3229},
{0x860d,0x860d,0x4064},
{0x8610,0x8610,0x3b7f},
{0x8611,0x8611,0x1639},
{0x8614,0x8614,0x46be},
{0x8616,0x8616,0x37fb},
{0x8617,0x8617,0x1685},
{0x8618,0x8618,0x32f6},
{0x8619,0x8619,0x32fc},
{0x861a,0x861a,0x1687},
{0x861b,0x861b,0x3223},
{0x861c,0x861c,0x32fb},
{0x861e,0x861e,0x3302},
{0x861f,0x861f,0x32f9},
{0x8620,0x8620,0x3300},
{0x8621,0x8621,0x32ff},
{0x8622,0x8622,0x321f},
{0x8623,0x8623,0x32fa},
{0x8624,0x8624,0x2fa9},
{0x8625,0x8625,0x3303},
{0x8626,0x8626,0x32f8},
{0x8627,0x8627,0x32fd},
{0x8628,0x8628,0x3805},
{0x8629,0x8629,0x3301},
{0x862a,0x862a,0x32f7},
{0x862c,0x862c,0x33aa},
{0x862d,0x862d,0x1686},
{0x862e,0x862e,0x32fe},
{0x862f,0x862f,0x3f9e},
{0x8631,0x8631,0x3432},
{0x8632,0x8632,0x33ab},
{0x8633,0x8633,0x33a9},
{0x8634,0x8635,0x33a7},
{0x8636,0x8636,0x33ac},
{0x8638,0x8638,0x1707},
{0x8639,0x8639,0x3430},
{0x863a,0x863a,0x342e},
{0x863b,0x863b,0x3433},
{0x863c,0x863c,0x3431},
{0x863e,0x863e,0x3434},
{0x863f,0x863f,0x1708},
{0x8640,0x8640,0x342f},
{0x8642,0x8642,0x38c1},
{0x8643,0x8643,0x3497},
{0x8645,0x8645,0x3b66},
{0x8646,0x8648,0x34df},
{0x864b,0x864b,0x353e},
{0x864c,0x864c,0x3536},
{0x864d,0x864d,0x1818},
{0x864e,0x864e,0x689},
{0x8650,0x8650,0x7ff},
{0x8652,0x8652,0x1de3},
{0x8653,0x8653,0x1de2},
{0x8654,0x8654,0x9af},
{0x8655,0x8655,0xb88},
{0x8656,0x8656,0x203c},
{0x8659,0x8659,0x203b},
{0x865b,0x865b,0xd48},
{0x865c,0x865c,0xeed},
{0x865e,0x865e,0xeec},
{0x865f,0x865f,0xeee},
{0x8661,0x8661,0x27ce},
{0x8662,0x8662,0x2a0e},
{0x8663,0x8663,0x2c5e},
{0x8664,0x8664,0x2c60},
{0x8665,0x8665,0x2c5f},
{0x8667,0x8667,0x1466},
{0x8668,0x8668,0x2e34},
{0x8669,0x8669,0x2fad},
{0x866a,0x866a,0x351e},
{0x866b,0x866b,0x40f},
{0x866c,0x866c,0x3813},
{0x866d,0x866e,0x1a46},
{0x866f,0x866f,0x1a45},
{0x8670,0x8670,0x1a44},
{0x8671,0x8671,0x68a},
{0x8672,0x8672,0x476f},
{0x8673,0x8673,0x1bfd},
{0x8674,0x8674,0x1bfb},
{0x8677,0x8677,0x1bfa},
{0x8679,0x8679,0x800},
{0x867a,0x867a,0x802},
{0x867b,0x867b,0x801},
{0x867c,0x867c,0x1bfc},
{0x867e,0x867e,0x380a},
{0x8685,0x8685,0x1def},
{0x8686,0x8686,0x1dec},
{0x8687,0x8687,0x1dea},
{0x868a,0x868a,0x9b0},
{0x868b,0x868b,0x1ded},
{0x868c,0x868c,0x9b5},
{0x868d,0x868d,0x1de7},
{0x868e,0x868e,0x1df6},
{0x8690,0x8690,0x1df8},
{0x8691,0x8691,0x1de8},
{0x8692,0x8692,0x4770},
{0x8693,0x8693,0x9b2},
{0x8694,0x8694,0x1df9},
{0x8695,0x8695,0x1df4},
{0x8696,0x8696,0x1de6},
{0x8697,0x8697,0x1deb},
{0x8698,0x8698,0x1df5},
{0x8699,0x8699,0x1df1},
{0x869a,0x869a,0x1dee},
{0x869c,0x869c,0x9b7},
{0x869d,0x869d,0x1df7},
{0x869e,0x869e,0x1de9},
{0x86a0,0x86a0,0x3b88},
{0x86a1,0x86a1,0x1df2},
{0x86a2,0x86a2,0x1de4},
{0x86a3,0x86a3,0x9b6},
{0x86a4,0x86a4,0x9b3},
{0x86a5,0x86a5,0x1df0},
{0x86a7,0x86a7,0x1df3},
{0x86a8,0x86a8,0x1de5},
{0x86a9,0x86a9,0x9b4},
{0x86aa,0x86aa,0x9b1},
{0x86ad,0x86ad,0x3f06},
{0x86af,0x86af,0xb92},
{0x86b0,0x86b0,0x2043},
{0x86b1,0x86b1,0xb91},
{0x86b2,0x86b2,0x43e0},
{0x86b3,0x86b3,0x2046},
{0x86b4,0x86b4,0x2049},
{0x86b5,0x86b5,0xb8e},
{0x86b6,0x86b6,0xb8c},
{0x86b7,0x86b7,0x203e},
{0x86b8,0x86b8,0x2047},
{0x86b9,0x86b9,0x2045},
{0x86ba,0x86ba,0x2042},
{0x86bb,0x86bc,0x204a},
{0x86bd,0x86be,0x204d},
{0x86bf,0x86bf,0x203d},
{0x86c0,0x86c0,0xb8b},
{0x86c1,0x86c1,0x2040},
{0x86c2,0x86c2,0x203f},
{0x86c3,0x86c3,0x204c},
{0x86c4,0x86c4,0xb8d},
{0x86c5,0x86c5,0x2041},
{0x86c6,0x86c6,0xb8f},
{0x86c7,0x86c7,0xb8a},
{0x86c8,0x86c8,0x2044},
{0x86c9,0x86c9,0xb93},
{0x86cb,0x86cb,0xb90},
{0x86cc,0x86cc,0x2048},
{0x86d0,0x86d0,0xd4f},
{0x86d1,0x86d1,0x22d2},
{0x86d3,0x86d3,0x22c7},
{0x86d4,0x86d4,0xd4c},
{0x86d6,0x86d6,0x256d},
{0x86d7,0x86d7,0x22d0},
{0x86d8,0x86d8,0x22c4},
{0x86d9,0x86d9,0xd4a},
{0x86da,0x86da,0x22c9},
{0x86db,0x86db,0xd4d},
{0x86dc,0x86dc,0x22cd},
{0x86dd,0x86dd,0x22cb},
{0x86de,0x86de,0xd50},
{0x86df,0x86df,0xd49},
{0x86e2,0x86e2,0x22c5},
{0x86e3,0x86e3,0x22c8},
{0x86e4,0x86e4,0xd4e},
{0x86e6,0x86e6,0x22c6},
{0x86e8,0x86e8,0x22d1},
{0x86e9,0x86e9,0x22cf},
{0x86ea,0x86ea,0x22ca},
{0x86eb,0x86eb,0x22cc},
{0x86ec,0x86ec,0x22ce},
{0x86ed,0x86ed,0xd4b},
{0x86ef,0x86ef,0x4771},
{0x86f5,0x86f5,0x256e},
{0x86f6,0x86f6,0x2574},
{0x86f7,0x86f7,0x256a},
{0x86f8,0x86f8,0x2570},
{0x86f9,0x86f9,0xeef},
{0x86fa,0x86fa,0x256c},
{0x86fb,0x86fb,0xef5},
{0x86fe,0x86fe,0xef4},
{0x8700,0x8700,0xef3},
{0x8701,0x8701,0x2573},
{0x8702,0x8703,0xef6},
{0x8704,0x8704,0x2569},
{0x8705,0x8705,0x2576},
{0x8706,0x8706,0xef8},
{0x8707,0x8707,0xef2},
{0x8708,0x8708,0xef1},
{0x8709,0x8709,0x2572},
{0x870a,0x870a,0xef9},
{0x870b,0x870b,0x2568},
{0x870c,0x870c,0x256b},
{0x870d,0x870d,0x2575},
{0x870e,0x870e,0x2571},
{0x8711,0x8711,0x27ee},
{0x8712,0x8712,0x27e3},
{0x8713,0x8713,0xef0},
{0x8718,0x8718,0x1098},
{0x8719,0x8719,0x27d7},
{0x871a,0x871a,0x27ec},
{0x871b,0x871b,0x27d8},
{0x871c,0x871c,0x1093},
{0x871e,0x871e,0x27d5},
{0x8720,0x8720,0x27de},
{0x8721,0x8721,0x27d6},
{0x8722,0x8722,0x1095},
{0x8723,0x8723,0x27d0},
{0x8724,0x8724,0x27eb},
{0x8725,0x8725,0x1096},
{0x8726,0x8727,0x27e8},
{0x8728,0x8728,0x27d1},
{0x8729,0x8729,0x109b},
{0x872a,0x872a,0x27e0},
{0x872c,0x872c,0x27da},
{0x872d,0x872d,0x27e1},
{0x872e,0x872e,0x27d4},
{0x8730,0x8730,0x27ed},
{0x8731,0x8731,0x27e5},
{0x8732,0x8732,0x27df},
{0x8733,0x8733,0x27cf},
{0x8734,0x8734,0x1097},
{0x8735,0x8735,0x27e6},
{0x8737,0x8737,0x109a},
{0x8738,0x8738,0x27ea},
{0x873a,0x873a,0x27e4},
{0x873b,0x873b,0x1094},
{0x873c,0x873c,0x27e2},
{0x873e,0x873e,0x27dc},
{0x873f,0x873f,0x1092},
{0x8740,0x8740,0x27d3},
{0x8741,0x8741,0x27db},
{0x8742,0x8742,0x27e7},
{0x8743,0x8743,0x27d9},
{0x8746,0x8746,0x27dd},
{0x874c,0x874c,0x1208},
{0x874d,0x874d,0x256f},
{0x874e,0x874e,0x2a20},
{0x874f,0x874f,0x2a29},
{0x8750,0x8750,0x2a1f},
{0x8751,0x8751,0x2a1b},
{0x8752,0x8752,0x2a18},
{0x8753,0x8753,0x1209},
{0x8754,0x8754,0x2a16},
{0x8755,0x8755,0x1099},
{0x8756,0x8756,0x2a0f},
{0x8757,0x8757,0x1207},
{0x8758,0x8758,0x2a15},
{0x8759,0x8759,0x1206},
{0x875a,0x875a,0x2a1a},
{0x875b,0x875b,0x2a17},
{0x875c,0x875c,0x2a27},
{0x875d,0x875d,0x2a22},
{0x875e,0x875e,0x2a1c},
{0x875f,0x875f,0x2a21},
{0x8760,0x8760,0x1202},
{0x8761,0x8761,0x2a19},
{0x8762,0x8762,0x2a2c},
{0x8763,0x8764,0x2a10},
{0x8765,0x8765,0x2a28},
{0x8766,0x8766,0x1203},
{0x8767,0x8767,0x2a2d},
{0x8768,0x8768,0x1205},
{0x8769,0x8769,0x2a2e},
{0x876a,0x876a,0x2a1e},
{0x876b,0x876b,0x27d2},
{0x876c,0x876c,0x2a24},
{0x876d,0x876d,0x2a1d},
{0x876e,0x876e,0x2a26},
{0x876f,0x876f,0x2a23},
{0x8770,0x8770,0x3e6c},
{0x8773,0x8773,0x2a14},
{0x8774,0x8774,0x1200},
{0x8775,0x8775,0x2a2b},
{0x8776,0x8776,0x1201},
{0x8777,0x8777,0x2a12},
{0x8778,0x8778,0x1204},
{0x8779,0x8779,0x2c6b},
{0x877a,0x877a,0x2a25},
{0x877b,0x877b,0x2a2a},
{0x877d,0x877d,0x4045},
{0x8781,0x8781,0x2c68},
{0x8782,0x8782,0x11ff},
{0x8783,0x8783,0x1340},
{0x8784,0x8784,0x2c72},
{0x8785,0x8785,0x2c6e},
{0x8787,0x8787,0x2c6c},
{0x8788,0x8788,0x2c67},
{0x8789,0x8789,0x2c76},
{0x878b,0x878b,0x43e2},
{0x878c,0x878c,0x3fef},
{0x878d,0x878d,0x1344},
{0x878f,0x878f,0x2c63},
{0x8790,0x8791,0x2c6f},
{0x8792,0x8792,0x2c66},
{0x8793,0x8793,0x2c65},
{0x8794,0x8794,0x2c73},
{0x8796,0x8796,0x2c69},
{0x8797,0x8797,0x2c64},
{0x8798,0x8798,0x2c6a},
{0x879a,0x879a,0x2c75},
{0x879b,0x879b,0x2c62},
{0x879c,0x879c,0x2c74},
{0x879d,0x879d,0x2c71},
{0x879e,0x879e,0x1342},
{0x879f,0x879f,0x1341},
{0x87a2,0x87a2,0x1343},
{0x87a3,0x87a3,0x2c6d},
{0x87a4,0x87a4,0x2c61},
{0x87a5,0x87a5,0x3d88},
{0x87a9,0x87a9,0x3fec},
{0x87aa,0x87aa,0x2e36},
{0x87ab,0x87ab,0x146c},
{0x87ac,0x87ac,0x2e3a},
{0x87ad,0x87ad,0x2e37},
{0x87ae,0x87ae,0x2e3e},
{0x87af,0x87af,0x2e44},
{0x87b0,0x87b0,0x2e39},
{0x87b1,0x87b1,0x380f},
{0x87b2,0x87b2,0x2e4d},
{0x87b3,0x87b3,0x1469},
{0x87b4,0x87b4,0x2e47},
{0x87b5,0x87b5,0x2e3c},
{0x87b6,0x87b6,0x2e48},
{0x87b7,0x87b7,0x2e43},
{0x87b8,0x87b8,0x2e4a},
{0x87b9,0x87b9,0x2e3b},
{0x87ba,0x87ba,0x146e},
{0x87bb,0x87bb,0x146d},
{0x87bc,0x87bc,0x2e3d},
{0x87bd,0x87bd,0x2e4b},
{0x87be,0x87be,0x2e35},
{0x87bf,0x87bf,0x2e49},
{0x87c0,0x87c0,0x1467},
{0x87c1,0x87c1,0x3ffc},
{0x87c2,0x87c2,0x2e41},
{0x87c3,0x87c3,0x2e40},
{0x87c4,0x87c4,0x2e45},
{0x87c5,0x87c5,0x2e38},
{0x87c6,0x87c6,0x146b},
{0x87c8,0x87c8,0x146f},
{0x87c9,0x87c9,0x2e3f},
{0x87ca,0x87ca,0x2e46},
{0x87cb,0x87cb,0x1470},
{0x87cc,0x87cc,0x2e42},
{0x87ce,0x87ce,0x3fed},
{0x87d1,0x87d1,0x1468},
{0x87d2,0x87d2,0x146a},
{0x87d3,0x87d3,0x2fba},
{0x87d4,0x87d4,0x2fb8},
{0x87d6,0x87d6,0x3f92},
{0x87d7,0x87d7,0x2fbe},
{0x87d8,0x87d8,0x2fbc},
{0x87d9,0x87d9,0x2fbf},
{0x87da,0x87da,0x3810},
{0x87db,0x87db,0x2fb1},
{0x87dc,0x87dc,0x2fb9},
{0x87dd,0x87dd,0x2fc3},
{0x87de,0x87de,0x2e4c},
{0x87df,0x87df,0x2fb5},
{0x87e0,0x87e0,0x1526},
{0x87e1,0x87e1,0x2a13},
{0x87e2,0x87e2,0x2fb0},
{0x87e3,0x87e3,0x2fbd},
{0x87e4,0x87e4,0x2fb7},
{0x87e5,0x87e5,0x2fb4},
{0x87e6,0x87e6,0x2faf},
{0x87e7,0x87e7,0x2fae},
{0x87e8,0x87e8,0x2fc2},
{0x87ea,0x87ea,0x2fb3},
{0x87eb,0x87eb,0x2fb2},
{0x87ec,0x87ec,0x1524},
{0x87ed,0x87ed,0x2fbb},
{0x87ee,0x87ee,0x380d},
{0x87ef,0x87ef,0x1523},
{0x87f2,0x87f2,0x1525},
{0x87f3,0x87f3,0x2fb6},
{0x87f4,0x87f4,0x2fc1},
{0x87f5,0x87f5,0x3fae},
{0x87f6,0x87f7,0x310a},
{0x87f9,0x87f9,0x15be},
{0x87fa,0x87fa,0x3108},
{0x87fb,0x87fb,0x15bb},
{0x87fc,0x87fc,0x3110},
{0x87fe,0x87fe,0x15bf},
{0x87ff,0x87ff,0x3112},
{0x8800,0x8800,0x3107},
{0x8801,0x8801,0x2fc0},
{0x8802,0x8802,0x3114},
{0x8803,0x8803,0x3109},
{0x8804,0x8804,0x3eaf},
{0x8805,0x8805,0x15bc},
{0x8806,0x8806,0x310f},
{0x8808,0x8808,0x3111},
{0x8809,0x8809,0x310c},
{0x880a,0x880a,0x3113},
{0x880b,0x880b,0x310e},
{0x880c,0x880c,0x310d},
{0x880d,0x880d,0x15bd},
{0x880f,0x880f,0x3811},
{0x8810,0x8811,0x322c},
{0x8813,0x8813,0x322f},
{0x8814,0x8815,0x163f},
{0x8816,0x8816,0x3230},
{0x8817,0x8817,0x322e},
{0x8818,0x8818,0x3f7a},
{0x8819,0x8819,0x322b},
{0x881b,0x881b,0x3306},
{0x881c,0x881c,0x3309},
{0x881d,0x881d,0x3305},
{0x881f,0x881f,0x168b},
{0x8820,0x8820,0x3307},
{0x8821,0x8821,0x168a},
{0x8822,0x8822,0x1689},
{0x8823,0x8823,0x1688},
{0x8824,0x8824,0x3308},
{0x8825,0x8825,0x33b1},
{0x8826,0x8826,0x33af},
{0x8827,0x8827,0x3e07},
{0x8828,0x8828,0x33ae},
{0x8829,0x8829,0x3304},
{0x882a,0x882a,0x33b0},
{0x882b,0x882b,0x330a},
{0x882c,0x882c,0x33ad},
{0x882d,0x882d,0x3b8b},
{0x882e,0x882e,0x3437},
{0x882f,0x882f,0x3415},
{0x8830,0x8830,0x3435},
{0x8831,0x8831,0x1709},
{0x8832,0x8832,0x3436},
{0x8833,0x8833,0x3438},
{0x8835,0x8835,0x349a},
{0x8836,0x8836,0x1728},
{0x8837,0x8837,0x3499},
{0x8838,0x8838,0x3498},
{0x8839,0x8839,0x1729},
{0x883b,0x883b,0x1747},
{0x883c,0x883c,0x3508},
{0x883d,0x883d,0x3520},
{0x883e,0x883e,0x351f},
{0x883f,0x883f,0x3521},
{0x8840,0x8840,0x410},
{0x8841,0x8841,0x1bfe},
{0x8842,0x8842,0x3f5a},
{0x8843,0x8844,0x1dfa},
{0x8845,0x8846,0x3816},
{0x8848,0x8848,0x22d3},
{0x884a,0x884a,0x330b},
{0x884b,0x884b,0x349b},
{0x884c,0x884c,0x411},
{0x884d,0x884d,0x803},
{0x884e,0x884e,0x1bff},
{0x884f,0x884f,0x3844},
{0x8852,0x8852,0x204f},
{0x8853,0x8853,0xb94},
{0x8855,0x8855,0x22d5},
{0x8856,0x8856,0x22d4},
{0x8857,0x8857,0xd51},
{0x8859,0x8859,0xefa},
{0x885a,0x885a,0x2a2f},
{0x885b,0x885b,0x120a},
{0x885d,0x885d,0x120b},
{0x885e,0x885e,0x381b},
{0x8860,0x8860,0x3a46},
{0x8861,0x8861,0x1345},
{0x8862,0x8862,0x172a},
{0x8863,0x8863,0x412},
{0x8864,0x8864,0x4502},
{0x8865,0x8865,0x3dfc},
{0x8867,0x8867,0x1c00},
{0x8868,0x8868,0x68c},
{0x8869,0x8869,0x1c02},
{0x886a,0x886a,0x1c01},
{0x886b,0x886b,0x804},
{0x886d,0x886d,0x1dfc},
{0x886e,0x886e,0x3b8e},
{0x886f,0x886f,0x1e03},
{0x8870,0x8870,0x9b8},
{0x8871,0x8871,0x1e01},
{0x8872,0x8872,0x1dff},
{0x8874,0x8874,0x1e06},
{0x8875,0x8876,0x1dfd},
{0x8877,0x8877,0x9b9},
{0x8879,0x8879,0x9bd},
{0x887c,0x887c,0x1e07},
{0x887d,0x887d,0x9bc},
{0x887e,0x887e,0x1e05},
{0x887f,0x887f,0x1e02},
{0x8880,0x8880,0x1e00},
{0x8881,0x8882,0x9ba},
{0x8883,0x8883,0x1e04},
{0x8884,0x8884,0x42f7},
{0x8887,0x8887,0x3b90},
{0x8888,0x8888,0xb96},
{0x8889,0x8889,0x2050},
{0x888b,0x888b,0xb9b},
{0x888c,0x888c,0x2060},
{0x888d,0x888d,0xb9a},
{0x888e,0x888e,0x2062},
{0x8891,0x8891,0x2056},
{0x8892,0x8892,0xb98},
{0x8893,0x8893,0x2061},
{0x8895,0x8895,0x2051},
{0x8896,0x8896,0xb99},
{0x8897,0x8897,0x205d},
{0x8898,0x8898,0x2059},
{0x8899,0x8899,0x205b},
{0x889a,0x889a,0x2055},
{0x889b,0x889b,0x205c},
{0x889c,0x889c,0x381c},
{0x889e,0x889e,0xb95},
{0x889f,0x889f,0x2058},
{0x88a0,0x88a0,0x3fa0},
{0x88a1,0x88a1,0x2057},
{0x88a2,0x88a2,0x2053},
{0x88a4,0x88a4,0x205e},
{0x88a7,0x88a7,0x205a},
{0x88a8,0x88a8,0x2052},
{0x88aa,0x88aa,0x2054},
{0x88ab,0x88ab,0xb97},
{0x88ac,0x88ac,0x205f},
{0x88ae,0x88ae,0x4775},
{0x88b1,0x88b1,0xd54},
{0x88b2,0x88b2,0x22e0},
{0x88b4,0x88b5,0x381e},
{0x88b6,0x88b6,0x22dc},
{0x88b7,0x88b7,0x22de},
{0x88b8,0x88b8,0x22d9},
{0x88b9,0x88b9,0x22d8},
{0x88ba,0x88ba,0x22d6},
{0x88bc,0x88bc,0x22dd},
{0x88bd,0x88bd,0x22df},
{0x88be,0x88be,0x22db},
{0x88bf,0x88bf,0x3b91},
{0x88c0,0x88c0,0x22da},
{0x88c1,0x88c2,0xd52},
{0x88c5,0x88c5,0x3820},
{0x88c7,0x88c7,0x3eb1},
{0x88c9,0x88c9,0x22e2},
{0x88ca,0x88ca,0xf02},
{0x88cb,0x88cb,0x2578},
{0x88cc,0x88cc,0x257e},
{0x88cd,0x88ce,0x2579},
{0x88cf,0x88cf,0x36ea},
{0x88d0,0x88d0,0x257f},
{0x88d2,0x88d2,0xf04},
{0x88d4,0x88d4,0xefc},
{0x88d5,0x88d5,0xf03},
{0x88d6,0x88d6,0x2577},
{0x88d7,0x88d7,0x22d7},
{0x88d8,0x88d8,0xeff},
{0x88d9,0x88d9,0xefd},
{0x88da,0x88da,0x257d},
{0x88db,0x88db,0x257c},
{0x88dc,0x88dc,0xefe},
{0x88dd,0x88dd,0xf00},
{0x88de,0x88de,0x257b},
{0x88df,0x88df,0xefb},
{0x88e1,0x88e1,0xf01},
{0x88e6,0x88e6,0x3b92},
{0x88e7,0x88e7,0x27f0},
{0x88e8,0x88e8,0x10a2},
{0x88eb,0x88eb,0x27fb},
{0x88ec,0x88ec,0x27fa},
{0x88ee,0x88ee,0x27f5},
{0x88ef,0x88ef,0x10a4},
{0x88f0,0x88f0,0x27f9},
{0x88f1,0x88f2,0x27f1},
{0x88f3,0x88f3,0x109c},
{0x88f4,0x88f4,0x109e},
{0x88f5,0x88f5,0x3e08},
{0x88f6,0x88f6,0x27f7},
{0x88f7,0x88f7,0x27ef},
{0x88f8,0x88f8,0x10a0},
{0x88f9,0x88f9,0x109f},
{0x88fa,0x88fa,0x27f3},
{0x88fb,0x88fb,0x27f8},
{0x88fc,0x88fc,0x27f6},
{0x88fd,0x88fd,0x10a1},
{0x88fe,0x88fe,0x27f4},
{0x88ff,0x88ff,0x4776},
{0x8900,0x8900,0x42fa},
{0x8901,0x8901,0x22e1},
{0x8902,0x8902,0x109d},
{0x8905,0x8905,0x2a30},
{0x8906,0x8906,0x2a37},
{0x8907,0x8907,0x120d},
{0x8909,0x8909,0x2a3b},
{0x890a,0x890a,0x1211},
{0x890b,0x890b,0x2a33},
{0x890c,0x890c,0x2a31},
{0x890e,0x890e,0x2a3a},
{0x8910,0x8910,0x120c},
{0x8911,0x8911,0x2a39},
{0x8912,0x8913,0x120e},
{0x8914,0x8914,0x2a32},
{0x8915,0x8915,0x1210},
{0x8916,0x8916,0x2a38},
{0x8917,0x8919,0x2a34},
{0x891a,0x891a,0x10a3},
{0x891e,0x891e,0x2c77},
{0x891f,0x891f,0x2c83},
{0x8921,0x8921,0x134a},
{0x8922,0x8922,0x2c7e},
{0x8923,0x8923,0x2c80},
{0x8924,0x8924,0x4777},
{0x8925,0x8925,0x1348},
{0x8926,0x8926,0x2c78},
{0x8927,0x8927,0x2c7c},
{0x8929,0x8929,0x2c7f},
{0x892a,0x892a,0x1346},
{0x892b,0x892b,0x1349},
{0x892c,0x892c,0x2c82},
{0x892d,0x892e,0x2c7a},
{0x892f,0x892f,0x2c81},
{0x8930,0x8930,0x2c79},
{0x8931,0x8931,0x2c7d},
{0x8932,0x8932,0x1347},
{0x8933,0x8933,0x2e4f},
{0x8935,0x8935,0x2e4e},
{0x8936,0x8936,0x1472},
{0x8937,0x8937,0x2e54},
{0x8938,0x8938,0x1474},
{0x893b,0x893b,0x1471},
{0x893c,0x893c,0x2e50},
{0x893d,0x893d,0x1475},
{0x893e,0x893e,0x2e51},
{0x8941,0x8941,0x2e52},
{0x8942,0x8942,0x2e55},
{0x8943,0x8943,0x3e16},
{0x8944,0x8944,0x1473},
{0x8946,0x8946,0x2fc8},
{0x8947,0x8947,0x43e3},
{0x8949,0x8949,0x2fcb},
{0x894b,0x894b,0x2fc5},
{0x894c,0x894c,0x2fc7},
{0x894d,0x894d,0x3b94},
{0x894f,0x894f,0x2fc6},
{0x8950,0x8951,0x2fc9},
{0x8952,0x8952,0x2e53},
{0x8953,0x8953,0x2fc4},
{0x8954,0x8954,0x3b96},
{0x8956,0x8956,0x15c2},
{0x8957,0x8957,0x3118},
{0x8958,0x8958,0x311b},
{0x8959,0x8959,0x311d},
{0x895a,0x895b,0x3116},
{0x895c,0x895c,0x311a},
{0x895d,0x895d,0x311c},
{0x895e,0x895e,0x15c3},
{0x895f,0x895f,0x15c1},
{0x8960,0x8960,0x15c0},
{0x8961,0x8961,0x3119},
{0x8962,0x8962,0x3115},
{0x8963,0x8963,0x3231},
{0x8964,0x8964,0x1641},
{0x8965,0x8965,0x3b93},
{0x8966,0x8966,0x3232},
{0x8969,0x8969,0x330d},
{0x896a,0x896a,0x168c},
{0x896b,0x896b,0x330f},
{0x896c,0x896c,0x168d},
{0x896d,0x896d,0x330c},
{0x896e,0x896e,0x330e},
{0x896f,0x896f,0x16d6},
{0x8971,0x8971,0x33b2},
{0x8972,0x8972,0x16d5},
{0x8973,0x8973,0x343b},
{0x8974,0x8974,0x343a},
{0x8976,0x8976,0x3439},
{0x8977,0x8977,0x3dea},
{0x8979,0x897a,0x34e2},
{0x897b,0x897b,0x34e5},
{0x897c,0x897c,0x34e4},
{0x897e,0x897e,0x1819},
{0x897f,0x897f,0x413},
{0x8980,0x8980,0x4708},
{0x8981,0x8981,0x805},
{0x8982,0x8982,0x2063},
{0x8983,0x8983,0xd55},
{0x8985,0x8985,0x2580},
{0x8986,0x8986,0x1527},
{0x8987,0x8987,0x3822},
{0x8988,0x8988,0x311e},
{0x8989,0x8989,0x3fe9},
{0x898b,0x898b,0x508},
{0x898f,0x898f,0xb9d},
{0x8991,0x8991,0x4778},
{0x8993,0x8993,0xb9c},
{0x8994,0x8994,0x3fe0},
{0x8995,0x8995,0x22e3},
{0x8996,0x8996,0xd56},
{0x8997,0x8997,0x22e5},
{0x8998,0x8998,0x22e4},
{0x899b,0x899b,0x2581},
{0x899c,0x899c,0xf05},
{0x899d,0x899d,0x27fc},
{0x899e,0x899e,0x27ff},
{0x899f,0x899f,0x27fe},
{0x89a1,0x89a1,0x27fd},
{0x89a2,0x89a2,0x2a3c},
{0x89a3,0x89a3,0x2a3e},
{0x89a4,0x89a4,0x2a3d},
{0x89a5,0x89a5,0x3d93},
{0x89a6,0x89a6,0x134c},
{0x89a7,0x89a7,0x3826},
{0x89aa,0x89aa,0x134b},
{0x89ac,0x89ac,0x1476},
{0x89ad,0x89ad,0x2e56},
{0x89ae,0x89ae,0x2e58},
{0x89af,0x89af,0x2e57},
{0x89b2,0x89b2,0x1528},
{0x89b6,0x89b6,0x3120},
{0x89b7,0x89b7,0x311f},
{0x89b9,0x89b9,0x3233},
{0x89ba,0x89ba,0x1642},
{0x89bc,0x89bc,0x3827},
{0x89bd,0x89bd,0x168e},
{0x89be,0x89be,0x33b4},
{0x89bf,0x89bf,0x33b3},
{0x89c0,0x89c0,0x1748},
{0x89c1,0x89c1,0x4503},
{0x89c6,0x89c6,0x4566},
{0x89d2,0x89d2,0x509},
{0x89d3,0x89d3,0x1c03},
{0x89d4,0x89d4,0x806},
{0x89d5,0x89d5,0x2066},
{0x89d6,0x89d6,0x2064},
{0x89d9,0x89d9,0x2065},
{0x89da,0x89db,0x22e7},
{0x89dc,0x89dc,0x2588},
{0x89dd,0x89dd,0x22e6},
{0x89df,0x89df,0x2582},
{0x89e0,0x89e0,0x2586},
{0x89e1,0x89e1,0x2585},
{0x89e2,0x89e2,0x2587},
{0x89e3,0x89e3,0xf06},
{0x89e4,0x89e4,0x2584},
{0x89e5,0x89e5,0x2583},
{0x89e6,0x89e6,0x2589},
{0x89e7,0x89e7,0x3829},
{0x89e8,0x89e8,0x2802},
{0x89e9,0x89e9,0x2800},
{0x89eb,0x89eb,0x2801},
{0x89ec,0x89ec,0x2a41},
{0x89ed,0x89ed,0x2a3f},
{0x89f0,0x89f0,0x2a40},
{0x89f1,0x89f1,0x2c84},
{0x89f2,0x89f3,0x2e59},
{0x89f4,0x89f4,0x1529},
{0x89f6,0x89f6,0x3121},
{0x89f7,0x89f7,0x3234},
{0x89f8,0x89f8,0x1643},
{0x89fa,0x89fa,0x3310},
{0x89fb,0x89fb,0x33b5},
{0x89fc,0x89fc,0x16d7},
{0x89fe,0x89fe,0x343c},
{0x89ff,0x89ff,0x34e6},
{0x8a00,0x8a00,0x50a},
{0x8a02,0x8a03,0x808},
{0x8a04,0x8a04,0x1c04},
{0x8a07,0x8a07,0x1c05},
{0x8a08,0x8a08,0x807},
{0x8a0a,0x8a0a,0x9c3},
{0x8a0c,0x8a0c,0x9c1},
{0x8a0e,0x8a0e,0x9c0},
{0x8a0f,0x8a0f,0x9c7},
{0x8a10,0x8a10,0x9bf},
{0x8a11,0x8a11,0x9c8},
{0x8a12,0x8a12,0x1e08},
{0x8a13,0x8a13,0x9c5},
{0x8a15,0x8a15,0x9c2},
{0x8a16,0x8a16,0x9c6},
{0x8a17,0x8a17,0x9c4},
{0x8a18,0x8a18,0x9be},
{0x8a1b,0x8a1b,0xba5},
{0x8a1c,0x8a1c,0x3ba3},
{0x8a1d,0x8a1d,0xb9f},
{0x8a1e,0x8a1e,0x206a},
{0x8a1f,0x8a1f,0xba4},
{0x8a22,0x8a22,0xba6},
{0x8a23,0x8a23,0xba0},
{0x8a25,0x8a25,0xba1},
{0x8a27,0x8a27,0x2068},
{0x8a29,0x8a29,0x43e5},
{0x8a2a,0x8a2a,0xb9e},
{0x8a2b,0x8a2b,0x3bd1},
{0x8a2c,0x8a2c,0x2069},
{0x8a2d,0x8a2d,0xba3},
{0x8a30,0x8a30,0x2067},
{0x8a31,0x8a31,0xba2},
{0x8a34,0x8a34,0xd61},
{0x8a36,0x8a36,0xd63},
{0x8a38,0x8a38,0x477a},
{0x8a39,0x8a39,0x22eb},
{0x8a3a,0x8a3a,0xd62},
{0x8a3b,0x8a3b,0xd57},
{0x8a3c,0x8a3c,0xd5b},
{0x8a3d,0x8a3d,0x3ba2},
{0x8a3e,0x8a3e,0xf19},
{0x8a3f,0x8a3f,0x258e},
{0x8a40,0x8a40,0x22ed},
{0x8a41,0x8a41,0xd5c},
{0x8a44,0x8a45,0x22f0},
{0x8a46,0x8a46,0xd60},
{0x8a48,0x8a48,0x22f3},
{0x8a49,0x8a49,0x3ff0},
{0x8a4a,0x8a4a,0x22f5},
{0x8a4c,0x8a4c,0x22f6},
{0x8a4d,0x8a4d,0x22ea},
{0x8a4e,0x8a4e,0x22e9},
{0x8a4f,0x8a4f,0x22f7},
{0x8a50,0x8a50,0xd5f},
{0x8a51,0x8a51,0x22f4},
{0x8a52,0x8a52,0x22f2},
{0x8a54,0x8a54,0xd5d},
{0x8a55,0x8a55,0xd59},
{0x8a56,0x8a56,0xd64},
{0x8a57,0x8a58,0x22ee},
{0x8a59,0x8a59,0x22ec},
{0x8a5b,0x8a5b,0xd5e},
{0x8a5e,0x8a5e,0xd5a},
{0x8a60,0x8a60,0xd58},
{0x8a61,0x8a61,0x258d},
{0x8a62,0x8a62,0xf14},
{0x8a63,0x8a63,0xf0f},
{0x8a66,0x8a66,0xf0a},
{0x8a67,0x8a67,0x3f8e},
{0x8a68,0x8a68,0xf1a},
{0x8a69,0x8a69,0xf0b},
{0x8a6b,0x8a6b,0xf07},
{0x8a6c,0x8a6c,0xf16},
{0x8a6d,0x8a6d,0xf13},
{0x8a6e,0x8a6e,0xf15},
{0x8a70,0x8a70,0xf0c},
{0x8a71,0x8a71,0xf11},
{0x8a72,0x8a73,0xf08},
{0x8a74,0x8a74,0x2595},
{0x8a75,0x8a75,0x2592},
{0x8a76,0x8a76,0x258a},
{0x8a77,0x8a77,0x258f},
{0x8a79,0x8a79,0xf17},
{0x8a7a,0x8a7a,0x2596},
{0x8a7b,0x8a7b,0xf18},
{0x8a7c,0x8a7c,0xf0e},
{0x8a7e,0x8a7e,0x3fa5},
{0x8a7f,0x8a7f,0x258c},
{0x8a81,0x8a81,0x2594},
{0x8a82,0x8a82,0x2590},
{0x8a83,0x8a83,0x2593},
{0x8a84,0x8a84,0x2591},
{0x8a85,0x8a85,0xf12},
{0x8a86,0x8a86,0x258b},
{0x8a87,0x8a87,0xf0d},
{0x8a8b,0x8a8b,0x2805},
{0x8a8c,0x8a8c,0x10a6},
{0x8a8d,0x8a8d,0x10a9},
{0x8a8f,0x8a8f,0x2807},
{0x8a90,0x8a90,0x382f},
{0x8a91,0x8a91,0x10b1},
{0x8a92,0x8a92,0x2806},
{0x8a93,0x8a93,0x10ab},
{0x8a94,0x8a94,0x477b},
{0x8a95,0x8a95,0x1216},
{0x8a96,0x8a96,0x2808},
{0x8a98,0x8a98,0x10b0},
{0x8a99,0x8a99,0x2804},
{0x8a9a,0x8a9a,0x10b2},
{0x8a9c,0x8a9c,0x382c},
{0x8a9e,0x8a9e,0x10a7},
{0x8aa0,0x8aa0,0xf10},
{0x8aa1,0x8aa1,0x10aa},
{0x8aa3,0x8aa3,0x10a8},
{0x8aa4,0x8aa4,0x10ac},
{0x8aa5,0x8aa5,0x10ae},
{0x8aa6,0x8aa6,0x10a5},
{0x8aa7,0x8aa7,0x10b3},
{0x8aa8,0x8aa8,0x10af},
{0x8aa9,0x8aa9,0x383d},
{0x8aaa,0x8aaa,0x10ad},
{0x8aab,0x8aab,0x2803},
{0x8aaf,0x8aaf,0x3841},
{0x8ab0,0x8ab0,0x121d},
{0x8ab2,0x8ab2,0x1219},
{0x8ab4,0x8ab4,0x477c},
{0x8ab6,0x8ab6,0x1220},
{0x8ab8,0x8ab8,0x2a44},
{0x8ab9,0x8ab9,0x1221},
{0x8aba,0x8aba,0x2a50},
{0x8abb,0x8abb,0x2a49},
{0x8abc,0x8abc,0x1212},
{0x8abd,0x8abd,0x2a51},
{0x8abe,0x8abe,0x2a4b},
{0x8abf,0x8abf,0x121c},
{0x8ac0,0x8ac0,0x2a4c},
{0x8ac2,0x8ac2,0x121b},
{0x8ac3,0x8ac3,0x2a4f},
{0x8ac4,0x8ac4,0x1215},
{0x8ac5,0x8ac5,0x2a4d},
{0x8ac6,0x8ac6,0x2a43},
{0x8ac7,0x8ac7,0x1214},
{0x8ac8,0x8ac8,0x2c8f},
{0x8ac9,0x8ac9,0x121a},
{0x8acb,0x8acb,0x1217},
{0x8acd,0x8acd,0x121f},
{0x8acf,0x8acf,0x2a42},
{0x8ad1,0x8ad1,0x2a46},
{0x8ad2,0x8ad2,0x1213},
{0x8ad3,0x8ad3,0x2a45},
{0x8ad4,0x8ad5,0x2a47},
{0x8ad6,0x8ad6,0x121e},
{0x8ad7,0x8ad7,0x2a4a},
{0x8ad8,0x8ad8,0x2a4e},
{0x8ad9,0x8ad9,0x2a52},
{0x8ada,0x8ada,0x39c2},
{0x8adb,0x8adb,0x1222},
{0x8adc,0x8adc,0x1352},
{0x8add,0x8add,0x2c8a},
{0x8ade,0x8ade,0x2c90},
{0x8adf,0x8adf,0x2c8d},
{0x8ae0,0x8ae0,0x2c85},
{0x8ae1,0x8ae1,0x2c91},
{0x8ae2,0x8ae2,0x2c86},
{0x8ae4,0x8ae4,0x2c8c},
{0x8ae6,0x8ae6,0x134d},
{0x8ae7,0x8ae7,0x1353},
{0x8ae8,0x8ae8,0x2c92},
{0x8aea,0x8aea,0x3dd9},
{0x8aeb,0x8aeb,0x134f},
{0x8aed,0x8aed,0x1359},
{0x8aee,0x8aee,0x1354},
{0x8aef,0x8aef,0x2c94},
{0x8af0,0x8af0,0x2c8e},
{0x8af1,0x8af1,0x1350},
{0x8af2,0x8af2,0x2c87},
{0x8af3,0x8af3,0x135a},
{0x8af4,0x8af5,0x2c88},
{0x8af6,0x8af6,0x135b},
{0x8af7,0x8af7,0x1358},
{0x8af8,0x8af8,0x1218},
{0x8afa,0x8afa,0x134e},
{0x8afb,0x8afb,0x2c95},
{0x8afc,0x8afc,0x135c},
{0x8afe,0x8afe,0x1355},
{0x8aff,0x8aff,0x2c93},
{0x8b00,0x8b00,0x1351},
{0x8b01,0x8b02,0x1356},
{0x8b04,0x8b04,0x147e},
{0x8b05,0x8b05,0x2e5f},
{0x8b06,0x8b06,0x2e68},
{0x8b07,0x8b07,0x2e65},
{0x8b08,0x8b08,0x2e67},
{0x8b0a,0x8b0a,0x147b},
{0x8b0b,0x8b0b,0x2e60},
{0x8b0c,0x8b0c,0x3836},
{0x8b0d,0x8b0d,0x2e66},
{0x8b0e,0x8b0e,0x1477},
{0x8b0f,0x8b0f,0x2e62},
{0x8b10,0x8b10,0x147f},
{0x8b11,0x8b11,0x2e5e},
{0x8b12,0x8b12,0x2e63},
{0x8b13,0x8b13,0x2e6a},
{0x8b14,0x8b14,0x2c8b},
{0x8b15,0x8b15,0x2e64},
{0x8b16,0x8b16,0x2e5d},
{0x8b17,0x8b17,0x1478},
{0x8b18,0x8b18,0x2e5c},
{0x8b19,0x8b19,0x1479},
{0x8b1a,0x8b1a,0x2e6b},
{0x8b1b,0x8b1b,0x147a},
{0x8b1c,0x8b1c,0x2e69},
{0x8b1d,0x8b1d,0x147d},
{0x8b1e,0x8b1e,0x2e5b},
{0x8b1f,0x8b1f,0x3837},
{0x8b20,0x8b20,0x147c},
{0x8b22,0x8b22,0x2e61},
{0x8b23,0x8b23,0x2fce},
{0x8b24,0x8b24,0x2fdc},
{0x8b25,0x8b25,0x2fd7},
{0x8b26,0x8b26,0x2fd9},
{0x8b27,0x8b27,0x2fcd},
{0x8b28,0x8b28,0x152a},
{0x8b2a,0x8b2a,0x2fcc},
{0x8b2b,0x8b2b,0x152d},
{0x8b2c,0x8b2c,0x152c},
{0x8b2d,0x8b2d,0x3f99},
{0x8b2e,0x8b2e,0x2fdb},
{0x8b2f,0x8b2f,0x2fd3},
{0x8b30,0x8b30,0x2fd0},
{0x8b31,0x8b31,0x2fd6},
{0x8b33,0x8b33,0x2fcf},
{0x8b35,0x8b35,0x2fd1},
{0x8b36,0x8b36,0x2fda},
{0x8b37,0x8b37,0x2fd8},
{0x8b39,0x8b39,0x152b},
{0x8b3a,0x8b3a,0x2fdf},
{0x8b3b,0x8b3b,0x2fdd},
{0x8b3c,0x8b3c,0x2fd4},
{0x8b3d,0x8b3d,0x2fde},
{0x8b3e,0x8b3e,0x2fd5},
{0x8b3f,0x8b3f,0x383a},
{0x8b40,0x8b40,0x3125},
{0x8b41,0x8b41,0x15c4},
{0x8b42,0x8b42,0x312c},
{0x8b43,0x8b43,0x3ff2},
{0x8b45,0x8b45,0x3313},
{0x8b46,0x8b46,0x15cb},
{0x8b47,0x8b47,0x2fd2},
{0x8b48,0x8b48,0x3123},
{0x8b49,0x8b49,0x15c7},
{0x8b4a,0x8b4a,0x3124},
{0x8b4b,0x8b4b,0x3129},
{0x8b4d,0x8b4d,0x383c},
{0x8b4e,0x8b4f,0x15c9},
{0x8b50,0x8b50,0x3122},
{0x8b51,0x8b51,0x312b},
{0x8b52,0x8b52,0x312d},
{0x8b53,0x8b53,0x3126},
{0x8b54,0x8b54,0x3128},
{0x8b55,0x8b55,0x312a},
{0x8b56,0x8b56,0x3127},
{0x8b57,0x8b57,0x312e},
{0x8b58,0x8b58,0x15c6},
{0x8b59,0x8b59,0x15cc},
{0x8b5a,0x8b5a,0x15c8},
{0x8b5c,0x8b5c,0x15c5},
{0x8b5d,0x8b5d,0x3237},
{0x8b5e,0x8b5e,0x3db8},
{0x8b5f,0x8b5f,0x1648},
{0x8b60,0x8b60,0x3235},
{0x8b62,0x8b62,0x3dd4},
{0x8b63,0x8b63,0x3239},
{0x8b65,0x8b65,0x323a},
{0x8b66,0x8b66,0x1646},
{0x8b67,0x8b67,0x323b},
{0x8b68,0x8b68,0x3238},
{0x8b69,0x8b69,0x3bf7},
{0x8b6a,0x8b6a,0x3236},
{0x8b6b,0x8b6b,0x1649},
{0x8b6c,0x8b6c,0x1645},
{0x8b6d,0x8b6d,0x323c},
{0x8b6f,0x8b6f,0x1647},
{0x8b70,0x8b70,0x1644},
{0x8b74,0x8b74,0x168f},
{0x8b77,0x8b77,0x1690},
{0x8b78,0x8b78,0x3312},
{0x8b79,0x8b79,0x3311},
{0x8b7a,0x8b7b,0x3314},
{0x8b7d,0x8b7d,0x1691},
{0x8b7e,0x8b7e,0x33b6},
{0x8b7f,0x8b7f,0x33bb},
{0x8b80,0x8b80,0x16d8},
{0x8b81,0x8b81,0x3d8d},
{0x8b82,0x8b82,0x33b8},
{0x8b84,0x8b84,0x33b7},
{0x8b85,0x8b85,0x33ba},
{0x8b86,0x8b86,0x33b9},
{0x8b88,0x8b88,0x3440},
{0x8b8a,0x8b8a,0x170a},
{0x8b8b,0x8b8b,0x343f},
{0x8b8c,0x8b8c,0x343d},
{0x8b8e,0x8b8e,0x343e},
{0x8b90,0x8b90,0x383f},
{0x8b92,0x8b92,0x172c},
{0x8b93,0x8b93,0x172b},
{0x8b94,0x8b95,0x349c},
{0x8b96,0x8b96,0x172d},
{0x8b98,0x8b99,0x34e7},
{0x8b9a,0x8b9a,0x1754},
{0x8b9b,0x8b9b,0x3840},
{0x8b9c,0x8b9c,0x175a},
{0x8b9e,0x8b9e,0x3522},
{0x8b9f,0x8b9f,0x353f},
{0x8ba0,0x8ba0,0x4505},
{0x8bbe,0x8bbe,0x4567},
{0x8be2,0x8be2,0x4568},
{0x8c37,0x8c37,0x50b},
{0x8c39,0x8c39,0x206b},
{0x8c3b,0x8c3b,0x206c},
{0x8c3c,0x8c3c,0x2597},
{0x8c3d,0x8c3d,0x2809},
{0x8c3e,0x8c3e,0x2a53},
{0x8c3f,0x8c3f,0x1481},
{0x8c41,0x8c41,0x1480},
{0x8c42,0x8c42,0x2fe0},
{0x8c43,0x8c43,0x312f},
{0x8c45,0x8c45,0x3441},
{0x8c46,0x8c46,0x50c},
{0x8c47,0x8c47,0x1e09},
{0x8c48,0x8c48,0x9c9},
{0x8c49,0x8c49,0xba7},
{0x8c4a,0x8c4a,0x2599},
{0x8c4b,0x8c4b,0x2598},
{0x8c4c,0x8c4c,0x1223},
{0x8c4d,0x8c4d,0x2a54},
{0x8c4e,0x8c4e,0x1224},
{0x8c4f,0x8c4f,0x2e6c},
{0x8c50,0x8c50,0x152e},
{0x8c51,0x8c51,0x477d},
{0x8c54,0x8c54,0x1763},
{0x8c55,0x8c55,0x50d},
{0x8c56,0x8c56,0x1a48},
{0x8c57,0x8c57,0x1e0a},
{0x8c5a,0x8c5a,0xba8},
{0x8c5c,0x8c5d,0x206d},
{0x8c5f,0x8c5f,0x22f8},
{0x8c61,0x8c61,0xd65},
{0x8c62,0x8c62,0xf1b},
{0x8c64,0x8c64,0x259b},
{0x8c65,0x8c65,0x259a},
{0x8c66,0x8c66,0x259c},
{0x8c68,0x8c69,0x280a},
{0x8c6a,0x8c6a,0x10b4},
{0x8c6b,0x8c6b,0x135d},
{0x8c6c,0x8c6c,0x1225},
{0x8c6d,0x8c6d,0x135e},
{0x8c6f,0x8c6f,0x2e70},
{0x8c70,0x8c70,0x2e6d},
{0x8c71,0x8c71,0x2e6f},
{0x8c72,0x8c72,0x2e6e},
{0x8c73,0x8c73,0x1482},
{0x8c75,0x8c75,0x2fe1},
{0x8c76,0x8c76,0x3131},
{0x8c77,0x8c77,0x3130},
{0x8c78,0x8c78,0x18e7},
{0x8c79,0x8c79,0x9cb},
{0x8c7a,0x8c7a,0x9ca},
{0x8c7b,0x8c7b,0x1e0b},
{0x8c7d,0x8c7d,0x206f},
{0x8c80,0x8c80,0x22fa},
{0x8c81,0x8c81,0x22f9},
{0x8c82,0x8c82,0xd66},
{0x8c84,0x8c85,0x259e},
{0x8c86,0x8c86,0x259d},
{0x8c89,0x8c89,0xf1d},
{0x8c8a,0x8c8a,0xf1c},
{0x8c8c,0x8c8c,0x10b6},
{0x8c8d,0x8c8d,0x10b5},
{0x8c8f,0x8c8f,0x2a55},
{0x8c90,0x8c90,0x2c98},
{0x8c91,0x8c92,0x2c96},
{0x8c93,0x8c93,0x135f},
{0x8c94,0x8c94,0x2e72},
{0x8c95,0x8c95,0x2e71},
{0x8c97,0x8c97,0x2fe4},
{0x8c98,0x8c98,0x2fe3},
{0x8c99,0x8c99,0x2fe2},
{0x8c9a,0x8c9a,0x3132},
{0x8c9b,0x8c9b,0x3845},
{0x8c9c,0x8c9c,0x3523},
{0x8c9d,0x8c9d,0x50e},
{0x8c9e,0x8c9e,0x80a},
{0x8c9f,0x8c9f,0x3bad},
{0x8ca0,0x8ca0,0x80b},
{0x8ca1,0x8ca2,0x9cc},
{0x8ca3,0x8ca3,0x1e0d},
{0x8ca4,0x8ca4,0x1e0c},
{0x8ca5,0x8ca5,0x2070},
{0x8ca7,0x8ca7,0xbae},
{0x8ca8,0x8ca8,0xbac},
{0x8ca9,0x8ca9,0xba9},
{0x8caa,0x8caa,0xbad},
{0x8cab,0x8cab,0xbab},
{0x8cac,0x8cac,0xbaa},
{0x8cad,0x8cad,0x3ff1},
{0x8caf,0x8caf,0xd67},
{0x8cb0,0x8cb0,0x22fd},
{0x8cb2,0x8cb2,0xf22},
{0x8cb3,0x8cb3,0xd69},
{0x8cb4,0x8cb4,0xd6e},
{0x8cb5,0x8cb5,0x22ff},
{0x8cb6,0x8cb6,0xd70},
{0x8cb7,0x8cb7,0xd6f},
{0x8cb8,0x8cb8,0xd72},
{0x8cb9,0x8cb9,0x22fe},
{0x8cba,0x8cba,0x22fb},
{0x8cbb,0x8cbb,0xd6c},
{0x8cbc,0x8cbc,0xd68},
{0x8cbd,0x8cbd,0xd6a},
{0x8cbe,0x8cbe,0x22fc},
{0x8cbf,0x8cbf,0xd71},
{0x8cc0,0x8cc0,0xd6d},
{0x8cc1,0x8cc1,0xd6b},
{0x8cc2,0x8cc2,0xf24},
{0x8cc3,0x8cc3,0xf23},
{0x8cc4,0x8cc4,0xf21},
{0x8cc5,0x8cc5,0xf25},
{0x8cc7,0x8cc8,0xf1f},
{0x8cca,0x8cca,0xf1e},
{0x8ccc,0x8ccc,0x25a0},
{0x8ccd,0x8ccd,0x3e99},
{0x8ccf,0x8ccf,0x280d},
{0x8cd1,0x8cd2,0x10b8},
{0x8cd3,0x8cd3,0x10b7},
{0x8cd4,0x8cd4,0x477e},
{0x8cd5,0x8cd5,0x280c},
{0x8cd6,0x8cd6,0x384b},
{0x8cd7,0x8cd7,0x280e},
{0x8cd9,0x8cd9,0x2a58},
{0x8cda,0x8cda,0x2a5a},
{0x8cdb,0x8cdb,0x3851},
{0x8cdc,0x8cdc,0x122e},
{0x8cdd,0x8cdd,0x2a5b},
{0x8cde,0x8cde,0x1227},
{0x8cdf,0x8cdf,0x2a57},
{0x8ce0,0x8ce0,0x1226},
{0x8ce1,0x8ce1,0x1230},
{0x8ce2,0x8ce3,0x122c},
{0x8ce4,0x8ce4,0x1229},
{0x8ce5,0x8ce5,0x2a56},
{0x8ce6,0x8ce6,0x1228},
{0x8ce7,0x8ce7,0x2a5c},
{0x8ce8,0x8ce8,0x2a59},
{0x8ce9,0x8ce9,0x3bae},
{0x8cea,0x8cea,0x122f},
{0x8ceb,0x8ceb,0x3f9c},
{0x8cec,0x8ced,0x122a},
{0x8cee,0x8cee,0x2c9a},
{0x8cf0,0x8cf0,0x2c9c},
{0x8cf1,0x8cf1,0x2c9b},
{0x8cf2,0x8cf2,0x477f},
{0x8cf3,0x8cf3,0x2c9d},
{0x8cf4,0x8cf4,0x1360},
{0x8cf5,0x8cf5,0x2c99},
{0x8cf7,0x8cf7,0x3fb1},
{0x8cf8,0x8cf8,0x1486},
{0x8cf9,0x8cf9,0x2e73},
{0x8cfa,0x8cfa,0x1483},
{0x8cfb,0x8cfb,0x1487},
{0x8cfc,0x8cfc,0x1485},
{0x8cfd,0x8cfd,0x1484},
{0x8cfe,0x8cfe,0x2fe5},
{0x8d00,0x8d00,0x2fe8},
{0x8d02,0x8d02,0x2fe7},
{0x8d03,0x8d03,0x384f},
{0x8d04,0x8d04,0x2fe6},
{0x8d05,0x8d05,0x152f},
{0x8d06,0x8d07,0x3133},
{0x8d08,0x8d08,0x15cd},
{0x8d09,0x8d09,0x3135},
{0x8d0a,0x8d0a,0x15ce},
{0x8d0b,0x8d0b,0x4573},
{0x8d0c,0x8d0c,0x3f96},
{0x8d0d,0x8d0d,0x164b},
{0x8d0f,0x8d0f,0x164a},
{0x8d10,0x8d10,0x3316},
{0x8d11,0x8d11,0x3853},
{0x8d12,0x8d12,0x384e},
{0x8d13,0x8d13,0x1692},
{0x8d14,0x8d14,0x3317},
{0x8d15,0x8d15,0x33bc},
{0x8d16,0x8d17,0x16d9},
{0x8d18,0x8d18,0x3e6f},
{0x8d19,0x8d19,0x3442},
{0x8d1b,0x8d1b,0x172f},
{0x8d1c,0x8d1c,0x4780},
{0x8d1d,0x8d1d,0x4506},
{0x8d64,0x8d64,0x50f},
{0x8d66,0x8d66,0xbb0},
{0x8d67,0x8d67,0xbaf},
{0x8d68,0x8d69,0x25a1},
{0x8d6b,0x8d6b,0x10ba},
{0x8d6c,0x8d6c,0x2c9e},
{0x8d6d,0x8d6d,0x1231},
{0x8d6e,0x8d6e,0x2c9f},
{0x8d6f,0x8d6f,0x2e74},
{0x8d70,0x8d70,0x510},
{0x8d72,0x8d72,0x1c06},
{0x8d73,0x8d73,0x80d},
{0x8d74,0x8d74,0x80c},
{0x8d76,0x8d76,0x1e0e},
{0x8d77,0x8d77,0x9ce},
{0x8d78,0x8d78,0x1e0f},
{0x8d79,0x8d79,0x2073},
{0x8d7a,0x8d7a,0x39e1},
{0x8d7b,0x8d7b,0x2072},
{0x8d7d,0x8d7d,0x2071},
{0x8d80,0x8d80,0x2301},
{0x8d81,0x8d81,0xd75},
{0x8d82,0x8d82,0x3f69},
{0x8d84,0x8d84,0x2300},
{0x8d85,0x8d85,0xd74},
{0x8d89,0x8d89,0x2302},
{0x8d8a,0x8d8a,0xd73},
{0x8d8c,0x8d8c,0x25a4},
{0x8d8d,0x8d8d,0x25a7},
{0x8d8e,0x8d8f,0x25a5},
{0x8d90,0x8d90,0x25aa},
{0x8d91,0x8d91,0x25a3},
{0x8d92,0x8d92,0x25ab},
{0x8d93,0x8d94,0x25a8},
{0x8d95,0x8d95,0x10bc},
{0x8d96,0x8d96,0x280f},
{0x8d99,0x8d99,0x10bb},
{0x8d9b,0x8d9b,0x2a60},
{0x8d9c,0x8d9c,0x2a5e},
{0x8d9f,0x8d9f,0x1232},
{0x8da0,0x8da0,0x2a5d},
{0x8da1,0x8da1,0x2a5f},
{0x8da3,0x8da3,0x1233},
{0x8da5,0x8da5,0x2ca0},
{0x8da6,0x8da6,0x3ff7},
{0x8da7,0x8da7,0x2ca1},
{0x8da8,0x8da8,0x1488},
{0x8da9,0x8da9,0x3856},
{0x8daa,0x8daa,0x3137},
{0x8dab,0x8dab,0x3139},
{0x8dac,0x8dac,0x3136},
{0x8dad,0x8dad,0x3138},
{0x8dae,0x8dae,0x323d},
{0x8daf,0x8daf,0x3318},
{0x8db2,0x8db2,0x3509},
{0x8db3,0x8db3,0x511},
{0x8db4,0x8db4,0x80e},
{0x8db5,0x8db5,0x1e10},
{0x8db6,0x8db6,0x1e12},
{0x8db7,0x8db7,0x1e11},
{0x8db9,0x8db9,0x2076},
{0x8dba,0x8dba,0xbb2},
{0x8dbc,0x8dbc,0x2074},
{0x8dbe,0x8dbe,0xbb1},
{0x8dbf,0x8dbf,0x2077},
{0x8dc0,0x8dc0,0x436a},
{0x8dc1,0x8dc1,0x2078},
{0x8dc2,0x8dc2,0x2075},
{0x8dc3,0x8dc3,0x4782},
{0x8dc5,0x8dc5,0x230e},
{0x8dc6,0x8dc6,0xd7d},
{0x8dc7,0x8dc7,0x2306},
{0x8dc8,0x8dc8,0x230c},
{0x8dcb,0x8dcb,0xd78},
{0x8dcc,0x8dcc,0xd7b},
{0x8dcd,0x8dcd,0x2305},
{0x8dce,0x8dce,0xd76},
{0x8dcf,0x8dcf,0x2309},
{0x8dd0,0x8dd0,0x25b1},
{0x8dd1,0x8dd1,0xd7a},
{0x8dd3,0x8dd3,0x2304},
{0x8dd4,0x8dd4,0x3f82},
{0x8dd5,0x8dd5,0x230a},
{0x8dd6,0x8dd6,0x2307},
{0x8dd7,0x8dd7,0x230d},
{0x8dd8,0x8dd8,0x2303},
{0x8dd9,0x8dd9,0x230b},
{0x8dda,0x8dda,0xd79},
{0x8ddb,0x8ddb,0xd7c},
{0x8ddc,0x8ddc,0x2308},
{0x8ddd,0x8ddd,0xd77},
{0x8ddf,0x8ddf,0xf27},
{0x8de0,0x8de0,0x25ad},
{0x8de1,0x8de1,0xf26},
{0x8de2,0x8de2,0x25b4},
{0x8de3,0x8de3,0x25b3},
{0x8de4,0x8de4,0xf2d},
{0x8de6,0x8de6,0xf2e},
{0x8de7,0x8de7,0x25b5},
{0x8de8,0x8de8,0xf28},
{0x8de9,0x8de9,0x25b2},
{0x8dea,0x8dea,0xf2c},
{0x8deb,0x8deb,0x25b7},
{0x8dec,0x8dec,0x25ae},
{0x8dee,0x8dee,0x25b0},
{0x8def,0x8def,0xf29},
{0x8df0,0x8df0,0x25ac},
{0x8df1,0x8df1,0x25af},
{0x8df2,0x8df2,0x25b6},
{0x8df3,0x8df3,0xf2a},
{0x8df4,0x8df4,0x25b8},
{0x8dfa,0x8dfa,0xf2b},
{0x8dfc,0x8dfc,0x10bd},
{0x8dfd,0x8dfd,0x2814},
{0x8dfe,0x8dfe,0x281a},
{0x8dff,0x8dff,0x2812},
{0x8e00,0x8e00,0x281b},
{0x8e01,0x8e01,0x3f85},
{0x8e02,0x8e02,0x2811},
{0x8e03,0x8e03,0x2816},
{0x8e04,0x8e04,0x281c},
{0x8e05,0x8e05,0x2819},
{0x8e06,0x8e06,0x2818},
{0x8e07,0x8e07,0x2817},
{0x8e09,0x8e09,0x2810},
{0x8e0a,0x8e0a,0x2815},
{0x8e0d,0x8e0d,0x2813},
{0x8e0e,0x8e0e,0x3e5e},
{0x8e0f,0x8e0f,0x1238},
{0x8e10,0x8e10,0x1235},
{0x8e11,0x8e11,0x2a69},
{0x8e12,0x8e12,0x2a6e},
{0x8e13,0x8e13,0x2a70},
{0x8e14,0x8e14,0x2a6d},
{0x8e15,0x8e15,0x2a66},
{0x8e16,0x8e16,0x2a68},
{0x8e17,0x8e17,0x2a72},
{0x8e18,0x8e18,0x2a6f},
{0x8e19,0x8e19,0x2a6a},
{0x8e1a,0x8e1a,0x2a73},
{0x8e1b,0x8e1b,0x2a67},
{0x8e1c,0x8e1c,0x2a71},
{0x8e1d,0x8e1d,0x1236},
{0x8e1e,0x8e1e,0x123c},
{0x8e1f,0x8e1f,0x123a},
{0x8e20,0x8e20,0x2a61},
{0x8e21,0x8e21,0x123b},
{0x8e22,0x8e22,0x1237},
{0x8e23,0x8e23,0x2a62},
{0x8e24,0x8e24,0x2a64},
{0x8e25,0x8e25,0x2a63},
{0x8e26,0x8e27,0x2a6b},
{0x8e28,0x8e28,0x3f88},
{0x8e29,0x8e29,0x1239},
{0x8e2a,0x8e2a,0x3bb4},
{0x8e2b,0x8e2b,0x1234},
{0x8e2d,0x8e2d,0x3eab},
{0x8e2e,0x8e2e,0x2a65},
{0x8e30,0x8e30,0x2cab},
{0x8e31,0x8e31,0x1362},
{0x8e33,0x8e33,0x2ca2},
{0x8e34,0x8e34,0x1363},
{0x8e35,0x8e35,0x1366},
{0x8e36,0x8e36,0x2ca7},
{0x8e38,0x8e38,0x2ca4},
{0x8e39,0x8e39,0x1365},
{0x8e3a,0x8e3a,0x4784},
{0x8e3c,0x8e3d,0x2ca8},
{0x8e3e,0x8e3e,0x2ca3},
{0x8e3f,0x8e3f,0x2cac},
{0x8e40,0x8e40,0x2ca5},
{0x8e41,0x8e41,0x2caa},
{0x8e42,0x8e42,0x1364},
{0x8e44,0x8e44,0x1361},
{0x8e45,0x8e45,0x2ca6},
{0x8e46,0x8e46,0x4312},
{0x8e47,0x8e47,0x2e7a},
{0x8e48,0x8e48,0x148b},
{0x8e49,0x8e49,0x1489},
{0x8e4a,0x8e4a,0x148c},
{0x8e4b,0x8e4b,0x148a},
{0x8e4c,0x8e4c,0x2e79},
{0x8e4d,0x8e4d,0x2e76},
{0x8e4e,0x8e4e,0x2e75},
{0x8e4f,0x8e4f,0x3f83},
{0x8e50,0x8e50,0x2e78},
{0x8e53,0x8e53,0x2e77},
{0x8e54,0x8e54,0x2ff6},
{0x8e55,0x8e55,0x1535},
{0x8e56,0x8e56,0x2fed},
{0x8e57,0x8e57,0x2fec},
{0x8e59,0x8e59,0x1530},
{0x8e5a,0x8e5a,0x2ff2},
{0x8e5b,0x8e5b,0x2ff1},
{0x8e5c,0x8e5c,0x2fe9},
{0x8e5d,0x8e5d,0x2ff4},
{0x8e5e,0x8e5e,0x2fee},
{0x8e5f,0x8e5f,0x1534},
{0x8e60,0x8e60,0x2feb},
{0x8e61,0x8e61,0x2ff3},
{0x8e62,0x8e62,0x2fea},
{0x8e63,0x8e63,0x1531},
{0x8e64,0x8e64,0x1533},
{0x8e65,0x8e65,0x2fef},
{0x8e66,0x8e66,0x1532},
{0x8e67,0x8e67,0x2ff0},
{0x8e68,0x8e68,0x434d},
{0x8e69,0x8e69,0x2ff5},
{0x8e6a,0x8e6a,0x313d},
{0x8e6c,0x8e6c,0x15d3},
{0x8e6d,0x8e6d,0x313a},
{0x8e6f,0x8e6f,0x313e},
{0x8e71,0x8e71,0x43e7},
{0x8e72,0x8e72,0x15d0},
{0x8e73,0x8e73,0x313c},
{0x8e74,0x8e74,0x15d5},
{0x8e75,0x8e75,0x3f89},
{0x8e76,0x8e76,0x15d2},
{0x8e77,0x8e77,0x3f7f},
{0x8e78,0x8e78,0x313b},
{0x8e7a,0x8e7a,0x15d4},
{0x8e7b,0x8e7b,0x313f},
{0x8e7c,0x8e7c,0x15cf},
{0x8e7e,0x8e7e,0x4324},
{0x8e81,0x8e81,0x164d},
{0x8e82,0x8e82,0x164f},
{0x8e84,0x8e84,0x3240},
{0x8e85,0x8e85,0x164e},
{0x8e86,0x8e86,0x323e},
{0x8e87,0x8e87,0x15d1},
{0x8e88,0x8e88,0x323f},
{0x8e89,0x8e89,0x164c},
{0x8e8a,0x8e8a,0x1693},
{0x8e8b,0x8e8b,0x1695},
{0x8e8c,0x8e8c,0x331a},
{0x8e8d,0x8e8d,0x1694},
{0x8e8e,0x8e8e,0x3319},
{0x8e90,0x8e90,0x33c1},
{0x8e91,0x8e91,0x16db},
{0x8e92,0x8e92,0x33c0},
{0x8e93,0x8e93,0x16dc},
{0x8e94,0x8e94,0x33be},
{0x8e95,0x8e95,0x33bd},
{0x8e96,0x8e97,0x33c2},
{0x8e98,0x8e98,0x3443},
{0x8e9a,0x8e9a,0x33bf},
{0x8e9d,0x8e9d,0x34a1},
{0x8e9e,0x8ea0,0x349e},
{0x8ea1,0x8ea1,0x1749},
{0x8ea3,0x8ea3,0x34eb},
{0x8ea4,0x8ea4,0x34ea},
{0x8ea5,0x8ea5,0x34e9},
{0x8ea6,0x8ea6,0x350a},
{0x8ea7,0x8ea7,0x3bb5},
{0x8ea8,0x8ea8,0x3537},
{0x8ea9,0x8ea9,0x3524},
{0x8eaa,0x8eaa,0x175b},
{0x8eab,0x8eab,0x512},
{0x8eac,0x8eac,0x9cf},
{0x8ead,0x8ead,0x385e},
{0x8eb0,0x8eb0,0x43e9},
{0x8eb2,0x8eb2,0xf2f},
{0x8eb6,0x8eb6,0x385f},
{0x8eba,0x8eba,0x123d},
{0x8ebc,0x8ebc,0x38a0},
{0x8ebd,0x8ebd,0x2cad},
{0x8ec0,0x8ec0,0x1536},
{0x8ec2,0x8ec2,0x3140},
{0x8ec3,0x8ec3,0x3860},
{0x8ec9,0x8ec9,0x3525},
{0x8eca,0x8eca,0x513},
{0x8ecb,0x8ecb,0x68d},
{0x8ecc,0x8ecc,0x810},
{0x8ecd,0x8ecd,0x80f},
{0x8ece,0x8ece,0x4788},
{0x8ecf,0x8ecf,0x9d2},
{0x8ed1,0x8ed1,0x1e13},
{0x8ed2,0x8ed2,0x9d0},
{0x8ed3,0x8ed3,0x1e14},
{0x8ed4,0x8ed4,0x9d1},
{0x8ed7,0x8ed7,0x207d},
{0x8ed8,0x8ed8,0x2079},
{0x8eda,0x8eda,0x3dbe},
{0x8edb,0x8edb,0xbb3},
{0x8edc,0x8edc,0x207c},
{0x8edd,0x8edd,0x207b},
{0x8ede,0x8ede,0x207a},
{0x8edf,0x8edf,0xbb4},
{0x8ee0,0x8ee1,0x207e},
{0x8ee2,0x8ee2,0x4789},
{0x8ee4,0x8ee4,0x478a},
{0x8ee5,0x8ee5,0x2315},
{0x8ee6,0x8ee6,0x2313},
{0x8ee7,0x8ee8,0x2317},
{0x8ee9,0x8ee9,0x231e},
{0x8eeb,0x8eeb,0x231a},
{0x8eec,0x8eec,0x231c},
{0x8eed,0x8eed,0x478b},
{0x8eee,0x8eee,0x2314},
{0x8eef,0x8eef,0x230f},
{0x8ef1,0x8ef1,0x231b},
{0x8ef2,0x8ef2,0x478c},
{0x8ef4,0x8ef4,0x231d},
{0x8ef5,0x8ef5,0x2316},
{0x8ef6,0x8ef6,0x2319},
{0x8ef7,0x8ef7,0x2310},
{0x8ef8,0x8ef8,0xd7f},
{0x8ef9,0x8ef9,0x2312},
{0x8efa,0x8efa,0x2311},
{0x8efb,0x8efb,0xd7e},
{0x8efc,0x8efc,0xd80},
{0x8efe,0x8efe,0xf32},
{0x8eff,0x8eff,0x25ba},
{0x8f00,0x8f00,0x25bc},
{0x8f01,0x8f01,0x25bb},
{0x8f02,0x8f02,0x25c0},
{0x8f03,0x8f03,0xf30},
{0x8f05,0x8f05,0x25bd},
{0x8f06,0x8f06,0x25b9},
{0x8f07,0x8f08,0x25be},
{0x8f09,0x8f09,0xf31},
{0x8f0a,0x8f0a,0xf33},
{0x8f0b,0x8f0b,0x25c1},
{0x8f0d,0x8f0d,0x2820},
{0x8f0e,0x8f0e,0x281f},
{0x8f10,0x8f11,0x281d},
{0x8f12,0x8f12,0x10bf},
{0x8f13,0x8f13,0x10c1},
{0x8f14,0x8f14,0x10be},
{0x8f15,0x8f15,0x10c0},
{0x8f16,0x8f17,0x2a7a},
{0x8f18,0x8f18,0x2a76},
{0x8f19,0x8f19,0x3862},
{0x8f1a,0x8f1a,0x2a77},
{0x8f1b,0x8f1b,0x123f},
{0x8f1c,0x8f1c,0x1244},
{0x8f1d,0x8f1d,0x123e},
{0x8f1e,0x8f1e,0x1245},
{0x8f1f,0x8f1f,0x1240},
{0x8f20,0x8f20,0x2a78},
{0x8f23,0x8f23,0x2a79},
{0x8f24,0x8f24,0x2a75},
{0x8f25,0x8f25,0x1246},
{0x8f26,0x8f26,0x1242},
{0x8f29,0x8f29,0x1241},
{0x8f2a,0x8f2a,0x1243},
{0x8f2c,0x8f2c,0x2a74},
{0x8f2d,0x8f2d,0x3863},
{0x8f2e,0x8f2e,0x2caf},
{0x8f2f,0x8f2f,0x1368},
{0x8f30,0x8f30,0x3bb7},
{0x8f32,0x8f32,0x2cb1},
{0x8f33,0x8f33,0x136a},
{0x8f34,0x8f34,0x2cb4},
{0x8f35,0x8f35,0x2cb0},
{0x8f36,0x8f36,0x2cae},
{0x8f37,0x8f37,0x2cb3},
{0x8f38,0x8f38,0x1369},
{0x8f39,0x8f39,0x2cb2},
{0x8f3b,0x8f3b,0x1367},
{0x8f3e,0x8f3e,0x148e},
{0x8f3f,0x8f3f,0x1491},
{0x8f40,0x8f40,0x2e7c},
{0x8f41,0x8f41,0x3d07},
{0x8f42,0x8f42,0x148f},
{0x8f43,0x8f43,0x2e7b},
{0x8f44,0x8f44,0x148d},
{0x8f45,0x8f45,0x1490},
{0x8f46,0x8f48,0x2ff7},
{0x8f49,0x8f49,0x1537},
{0x8f4a,0x8f4a,0x3bb8},
{0x8f4b,0x8f4b,0x2ffa},
{0x8f4d,0x8f4d,0x1538},
{0x8f4e,0x8f4e,0x15d7},
{0x8f4f,0x8f50,0x3143},
{0x8f51,0x8f51,0x3142},
{0x8f52,0x8f52,0x3141},
{0x8f53,0x8f53,0x3145},
{0x8f54,0x8f54,0x15d6},
{0x8f55,0x8f55,0x3244},
{0x8f56,0x8f57,0x3242},
{0x8f58,0x8f58,0x3245},
{0x8f59,0x8f59,0x3241},
{0x8f5a,0x8f5a,0x3246},
{0x8f5b,0x8f5b,0x331c},
{0x8f5c,0x8f5c,0x3f31},
{0x8f5d,0x8f5d,0x331d},
{0x8f5e,0x8f5e,0x331b},
{0x8f5f,0x8f5f,0x1696},
{0x8f60,0x8f60,0x33c4},
{0x8f61,0x8f61,0x16dd},
{0x8f62,0x8f62,0x33c5},
{0x8f63,0x8f63,0x3445},
{0x8f64,0x8f64,0x3444},
{0x8f66,0x8f67,0x4569},
{0x8f6e,0x8f6e,0x456b},
{0x8f93,0x8f93,0x4732},
{0x8f9b,0x8f9b,0x514},
{0x8f9c,0x8f9c,0xd81},
{0x8f9f,0x8f9f,0xf34},
{0x8fa0,0x8fa0,0x3d94},
{0x8fa3,0x8fa3,0x10c2},
{0x8fa5,0x8fa5,0x3866},
{0x8fa6,0x8fa6,0x136c},
{0x8fa8,0x8fa8,0x136b},
{0x8fad,0x8fad,0x15d8},
{0x8fae,0x8fae,0x162f},
{0x8faf,0x8faf,0x1697},
{0x8fb0,0x8fb0,0x515},
{0x8fb1,0x8fb1,0x9d3},
{0x8fb2,0x8fb2,0xf35},
{0x8fb3,0x8fb3,0x386a},
{0x8fb4,0x8fb4,0x3146},
{0x8fb5,0x8fb5,0x230},
{0x8fb6,0x8fb6,0x47d3},
{0x8fb7,0x8fb7,0x43fd},
{0x8fb8,0x8fb8,0x42f6},
{0x8fb9,0x8fb9,0x48c9},
{0x8fba,0x8fba,0x48c7},
{0x8fbb,0x8fbc,0x3d6b},
{0x8fbe,0x8fbe,0x48c6},
{0x8fbf,0x8fbf,0x18e9},
{0x8fc1,0x8fc1,0x478d},
{0x8fc2,0x8fc2,0x516},
{0x8fc4,0x8fc4,0x519},
{0x8fc5,0x8fc5,0x518},
{0x8fc6,0x8fc6,0x517},
{0x8fc9,0x8fc9,0x18e8},
{0x8fca,0x8fca,0x478e},
{0x8fcb,0x8fcb,0x1a4a},
{0x8fcc,0x8fcc,0x478f},
{0x8fcd,0x8fcd,0x1a4c},
{0x8fce,0x8fce,0x68e},
{0x8fd0,0x8fd0,0x48ae},
{0x8fd1,0x8fd1,0x690},
{0x8fd2,0x8fd2,0x1a49},
{0x8fd3,0x8fd3,0x1a4b},
{0x8fd4,0x8fd4,0x68f},
{0x8fd5,0x8fd5,0x1a4e},
{0x8fd6,0x8fd6,0x1a4d},
{0x8fd7,0x8fd7,0x1a4f},
{0x8fda,0x8fda,0x3fcb},
{0x8fe0,0x8fe0,0x1c0a},
{0x8fe1,0x8fe1,0x1c08},
{0x8fe2,0x8fe2,0x813},
{0x8fe3,0x8fe3,0x1c07},
{0x8fe4,0x8fe4,0x818},
{0x8fe5,0x8fe5,0x815},
{0x8fe6,0x8fe6,0x812},
{0x8fe8,0x8fe8,0x819},
{0x8fea,0x8fea,0x814},
{0x8feb,0x8feb,0x817},
{0x8fed,0x8fed,0x816},
{0x8fee,0x8fee,0x1c09},
{0x8ff0,0x8ff0,0x811},
{0x8ff4,0x8ff4,0x9d9},
{0x8ff5,0x8ff5,0x1e16},
{0x8ff6,0x8ff6,0x1e1c},
{0x8ff7,0x8ff7,0x9d6},
{0x8ff8,0x8ff8,0x9dd},
{0x8ff9,0x8ff9,0x3871},
{0x8ffa,0x8ffa,0x9d8},
{0x8ffb,0x8ffb,0x1e19},
{0x8ffc,0x8ffc,0x1e1b},
{0x8ffd,0x8ffd,0x9db},
{0x8ffe,0x8ffe,0x1e15},
{0x8fff,0x8fff,0x1e18},
{0x9000,0x9000,0x9d7},
{0x9001,0x9001,0x9d4},
{0x9002,0x9002,0x1e17},
{0x9003,0x9003,0x9da},
{0x9004,0x9004,0x1e1a},
{0x9005,0x9005,0x9dc},
{0x9006,0x9006,0x9d5},
{0x9008,0x9008,0x3ee0},
{0x900b,0x900b,0x2081},
{0x900c,0x900c,0x2084},
{0x900d,0x900d,0xbb6},
{0x900f,0x900f,0xbc0},
{0x9010,0x9010,0xbbc},
{0x9011,0x9011,0x2082},
{0x9012,0x9012,0x3e4e},
{0x9014,0x9014,0xbc4},
{0x9015,0x9015,0xbbd},
{0x9016,0x9016,0xbc2},
{0x9017,0x9017,0xbb8},
{0x9019,0x9019,0xbb5},
{0x901a,0x901a,0xbb7},
{0x901b,0x901b,0xbc3},
{0x901c,0x901c,0x2083},
{0x901d,0x901d,0xbbb},
{0x901e,0x901e,0xbbe},
{0x901f,0x901f,0xbba},
{0x9020,0x9020,0xbbf},
{0x9021,0x9021,0x2085},
{0x9022,0x9022,0xbc1},
{0x9023,0x9023,0xbb9},
{0x9024,0x9024,0x2080},
{0x902d,0x902d,0x231f},
{0x902e,0x902e,0xd82},
{0x902f,0x902f,0x2321},
{0x9031,0x9031,0xd84},
{0x9032,0x9032,0xd86},
{0x9033,0x9033,0x4790},
{0x9034,0x9034,0x2320},
{0x9035,0x9035,0xd83},
{0x9036,0x9036,0xd87},
{0x9037,0x9037,0x3876},
{0x9038,0x9038,0xd85},
{0x903c,0x903c,0xf3b},
{0x903d,0x903d,0x25c6},
{0x903e,0x903e,0xf43},
{0x903f,0x903f,0x25c3},
{0x9041,0x9041,0xf44},
{0x9042,0x9042,0xf39},
{0x9044,0x9044,0x25c4},
{0x9047,0x9047,0xf3e},
{0x9049,0x9049,0x25c5},
{0x904a,0x904a,0xf37},
{0x904b,0x904b,0xf36},
{0x904c,0x904c,0x3f10},
{0x904d,0x904d,0xf41},
{0x904e,0x904e,0xf40},
{0x904f,0x904f,0xf3f},
{0x9050,0x9050,0xf3d},
{0x9051,0x9051,0xf42},
{0x9052,0x9052,0x25c2},
{0x9053,0x9053,0xf38},
{0x9054,0x9054,0xf3a},
{0x9055,0x9055,0xf3c},
{0x9056,0x9056,0x3dd2},
{0x9058,0x9058,0x10c4},
{0x9059,0x9059,0x10c7},
{0x905b,0x905b,0x10cb},
{0x905c,0x905c,0x10c5},
{0x905d,0x905d,0x10ca},
{0x905e,0x905e,0x10c8},
{0x9060,0x9060,0x10c3},
{0x9061,0x9061,0x3879},
{0x9062,0x9062,0x10c9},
{0x9063,0x9063,0x10c6},
{0x9064,0x9064,0x3c68},
{0x9067,0x9067,0x2a7f},
{0x9068,0x9068,0x1249},
{0x9069,0x9069,0x1247},
{0x906b,0x906b,0x2a80},
{0x906c,0x906c,0x3d60},
{0x906d,0x906d,0x124a},
{0x906e,0x906e,0x1248},
{0x906f,0x906f,0x2a7e},
{0x9070,0x9070,0x2a7d},
{0x9072,0x9072,0x1370},
{0x9073,0x9073,0x2a7c},
{0x9074,0x9074,0x136e},
{0x9075,0x9075,0x136d},
{0x9076,0x9076,0x2cb5},
{0x9077,0x9077,0x124b},
{0x9078,0x9078,0x136f},
{0x9079,0x9079,0x2cb6},
{0x907a,0x907a,0x1372},
{0x907b,0x907b,0x2cb7},
{0x907c,0x907c,0x1371},
{0x907d,0x907d,0x1493},
{0x907e,0x907e,0x2e7e},
{0x907f,0x907f,0x1492},
{0x9080,0x9080,0x1497},
{0x9081,0x9082,0x1495},
{0x9083,0x9083,0x153a},
{0x9084,0x9084,0x1494},
{0x9085,0x9085,0x2e7d},
{0x9086,0x9086,0x2cb8},
{0x9087,0x9087,0x1539},
{0x9088,0x9088,0x153b},
{0x908a,0x908b,0x15d9},
{0x908d,0x908d,0x3247},
{0x908f,0x908f,0x170c},
{0x9090,0x9090,0x170b},
{0x9091,0x9091,0x51b},
{0x9094,0x9094,0x181e},
{0x9095,0x9095,0x9de},
{0x9097,0x9098,0x181b},
{0x9099,0x9099,0x181a},
{0x909b,0x909b,0x181d},
{0x909e,0x909e,0x18ed},
{0x909f,0x909f,0x18ea},
{0x90a0,0x90a0,0x18ef},
{0x90a1,0x90a1,0x18eb},
{0x90a2,0x90a2,0x51c},
{0x90a3,0x90a3,0x51f},
{0x90a5,0x90a5,0x18ec},
{0x90a6,0x90a6,0x51e},
{0x90a7,0x90a7,0x18ee},
{0x90a8,0x90a8,0x387c},
{0x90aa,0x90aa,0x51d},
{0x90ae,0x90ae,0x3880},
{0x90af,0x90af,0x1a52},
{0x90b0,0x90b0,0x1a54},
{0x90b1,0x90b1,0x693},
{0x90b2,0x90b2,0x1a50},
{0x90b3,0x90b3,0x1a53},
{0x90b4,0x90b4,0x1a51},
{0x90b5,0x90b5,0x691},
{0x90b6,0x90b6,0x694},
{0x90b8,0x90b8,0x692},
{0x90bb,0x90bb,0x3bc4},
{0x90bd,0x90bd,0x1c0c},
{0x90be,0x90be,0x1c10},
{0x90bf,0x90bf,0x1c0d},
{0x90c1,0x90c1,0x81c},
{0x90c3,0x90c3,0x81d},
{0x90c4,0x90c4,0x387e},
{0x90c5,0x90c5,0x1c0f},
{0x90c7,0x90c7,0x1c11},
{0x90c8,0x90c8,0x1c13},
{0x90ca,0x90ca,0x81a},
{0x90cb,0x90cb,0x1c12},
{0x90ce,0x90ce,0x81b},
{0x90d4,0x90d4,0x208c},
{0x90d5,0x90d5,0x1c0e},
{0x90d6,0x90d6,0x1e1d},
{0x90d7,0x90d7,0x1e26},
{0x90d8,0x90d8,0x1e24},
{0x90d9,0x90da,0x1e1f},
{0x90db,0x90db,0x1e25},
{0x90dc,0x90dc,0x1e27},
{0x90dd,0x90dd,0x9e0},
{0x90df,0x90df,0x1e22},
{0x90e0,0x90e0,0x1e1e},
{0x90e1,0x90e1,0x9df},
{0x90e2,0x90e2,0x9e1},
{0x90e3,0x90e3,0x1e21},
{0x90e4,0x90e4,0x1e28},
{0x90e5,0x90e5,0x1e23},
{0x90e8,0x90e8,0xbc5},
{0x90e9,0x90e9,0x208f},
{0x90ea,0x90ea,0x2087},
{0x90eb,0x90ec,0x208d},
{0x90ed,0x90ed,0xbc6},
{0x90ef,0x90ef,0x2086},
{0x90f0,0x90f0,0x2088},
{0x90f1,0x90f1,0x1c0b},
{0x90f2,0x90f3,0x208a},
{0x90f4,0x90f4,0x2089},
{0x90f5,0x90f5,0xd89},
{0x90f9,0x90f9,0x2328},
{0x90fa,0x90fa,0x2cb9},
{0x90fb,0x90fb,0x2329},
{0x90fc,0x90fc,0x2326},
{0x90fd,0x90fd,0xbc7},
{0x90fe,0x90fe,0xd8b},
{0x90ff,0x90ff,0x2325},
{0x9100,0x9100,0x232b},
{0x9101,0x9101,0x232a},
{0x9102,0x9102,0xd88},
{0x9103,0x9103,0x232e},
{0x9104,0x9104,0x2324},
{0x9105,0x9105,0x232d},
{0x9106,0x9106,0x2322},
{0x9107,0x9107,0x232c},
{0x9108,0x9108,0x2327},
{0x9109,0x9109,0xd8a},
{0x910b,0x910b,0x25cd},
{0x910d,0x910d,0x25c8},
{0x910e,0x910e,0x25ce},
{0x910f,0x910f,0x25c9},
{0x9110,0x9110,0x25c7},
{0x9111,0x9111,0x25ca},
{0x9112,0x9112,0xf45},
{0x9114,0x9114,0x25cc},
{0x9116,0x9116,0x25cb},
{0x9117,0x9117,0xf46},
{0x9118,0x9118,0x10cd},
{0x9119,0x9119,0x10cc},
{0x911a,0x911a,0x2827},
{0x911b,0x911b,0x282a},
{0x911c,0x911c,0x2822},
{0x911d,0x911d,0x2826},
{0x911e,0x911e,0x10ce},
{0x911f,0x911f,0x2825},
{0x9120,0x9120,0x2823},
{0x9121,0x9121,0x2829},
{0x9122,0x9122,0x2824},
{0x9123,0x9123,0x2821},
{0x9124,0x9124,0x2828},
{0x9126,0x9126,0x2a86},
{0x9127,0x9127,0x124e},
{0x9128,0x9128,0x2ffb},
{0x9129,0x912a,0x2a83},
{0x912b,0x912b,0x2a82},
{0x912c,0x912c,0x2323},
{0x912d,0x912d,0x124d},
{0x912e,0x912e,0x2a87},
{0x912f,0x912f,0x2a81},
{0x9130,0x9130,0x124c},
{0x9131,0x9131,0x124f},
{0x9132,0x9132,0x2a85},
{0x9133,0x9133,0x2cba},
{0x9134,0x9134,0x1373},
{0x9135,0x9136,0x2cbb},
{0x9138,0x9138,0x2e7f},
{0x9139,0x9139,0x1498},
{0x913a,0x913b,0x2ffc},
{0x913e,0x913e,0x2ffe},
{0x913f,0x913f,0x3148},
{0x9140,0x9140,0x3147},
{0x9141,0x9141,0x3249},
{0x9143,0x9143,0x3248},
{0x9144,0x9145,0x331f},
{0x9146,0x9146,0x331e},
{0x9147,0x9147,0x33c6},
{0x9148,0x9148,0x16de},
{0x9149,0x9149,0x520},
{0x914a,0x914a,0x81f},
{0x914b,0x914b,0x81e},
{0x914c,0x914c,0x9e4},
{0x914d,0x914d,0x9e3},
{0x914e,0x914f,0x1e2a},
{0x9150,0x9150,0x1e29},
{0x9151,0x9151,0x3f49},
{0x9152,0x9152,0x9e2},
{0x9153,0x9153,0x2093},
{0x9155,0x9155,0x2094},
{0x9156,0x9156,0x2090},
{0x9157,0x9157,0xbc8},
{0x9158,0x9158,0x2091},
{0x9159,0x9159,0x3f45},
{0x915a,0x915a,0x2092},
{0x915c,0x915c,0x3f47},
{0x915e,0x915e,0x43eb},
{0x915f,0x915f,0x2331},
{0x9160,0x9160,0x2333},
{0x9161,0x9161,0x232f},
{0x9162,0x9162,0x2332},
{0x9163,0x9163,0xd8c},
{0x9164,0x9164,0x2330},
{0x9165,0x9165,0xd8d},
{0x9167,0x9167,0x3882},
{0x9168,0x9168,0x23e2},
{0x9169,0x9169,0xf49},
{0x916a,0x916a,0xf48},
{0x916c,0x916c,0xf47},
{0x916e,0x916f,0x25cf},
{0x9170,0x9170,0x3eb8},
{0x9172,0x9172,0x282c},
{0x9173,0x9173,0x282e},
{0x9174,0x9174,0x10d2},
{0x9175,0x9175,0x10cf},
{0x9176,0x9176,0x3e9b},
{0x9177,0x9177,0x10d1},
{0x9178,0x9178,0x10d0},
{0x9179,0x9179,0x282d},
{0x917a,0x917a,0x282b},
{0x917c,0x917c,0x3f6f},
{0x9180,0x9180,0x2a8e},
{0x9181,0x9182,0x2a8b},
{0x9183,0x9183,0x1253},
{0x9184,0x9184,0x2a8d},
{0x9185,0x9186,0x2a88},
{0x9187,0x9187,0x1250},
{0x9189,0x9189,0x1251},
{0x918a,0x918a,0x2a8a},
{0x918b,0x918b,0x1252},
{0x918c,0x918c,0x43ec},
{0x918d,0x918d,0x2cc0},
{0x918e,0x918e,0x3f4e},
{0x918f,0x918f,0x2cc1},
{0x9190,0x9191,0x2cbe},
{0x9192,0x9192,0x1374},
{0x9193,0x9193,0x2cbd},
{0x9199,0x9199,0x2e83},
{0x919a,0x919a,0x2e80},
{0x919b,0x919b,0x2e82},
{0x919c,0x919c,0x149b},
{0x919d,0x919d,0x2e86},
{0x919e,0x919e,0x149a},
{0x919f,0x919f,0x2e84},
{0x91a0,0x91a0,0x2e87},
{0x91a1,0x91a1,0x2e85},
{0x91a2,0x91a2,0x2e81},
{0x91a3,0x91a3,0x1499},
{0x91a5,0x91a5,0x3000},
{0x91a7,0x91a7,0x3001},
{0x91a8,0x91a8,0x2fff},
{0x91a9,0x91a9,0x3883},
{0x91aa,0x91aa,0x3003},
{0x91ab,0x91ac,0x153c},
{0x91ad,0x91ad,0x314a},
{0x91ae,0x91ae,0x15dc},
{0x91af,0x91af,0x3002},
{0x91b0,0x91b0,0x3149},
{0x91b1,0x91b1,0x15db},
{0x91b2,0x91b3,0x324c},
{0x91b4,0x91b4,0x1650},
{0x91b5,0x91b5,0x324b},
{0x91b6,0x91b6,0x45ef},
{0x91b7,0x91b7,0x324a},
{0x91b9,0x91b9,0x3321},
{0x91ba,0x91ba,0x1698},
{0x91bb,0x91bb,0x3bc8},
{0x91bc,0x91bc,0x3446},
{0x91bd,0x91bd,0x34a3},
{0x91be,0x91be,0x34a2},
{0x91c0,0x91c0,0x1730},
{0x91c1,0x91c1,0x174a},
{0x91c2,0x91c2,0x34a4},
{0x91c3,0x91c3,0x350b},
{0x91c4,0x91c4,0x3884},
{0x91c5,0x91c5,0x175c},
{0x91c6,0x91c6,0x521},
{0x91c7,0x91c7,0x695},
{0x91c9,0x91c9,0xf4a},
{0x91cb,0x91cb,0x1651},
{0x91cc,0x91cc,0x522},
{0x91cd,0x91cd,0x820},
{0x91ce,0x91ce,0xbc9},
{0x91cf,0x91cf,0xd8e},
{0x91d0,0x91d0,0x153e},
{0x91d1,0x91d1,0x696},
{0x91d3,0x91d3,0x1c15},
{0x91d4,0x91d4,0x1c14},
{0x91d5,0x91d5,0x1e2c},
{0x91d6,0x91d6,0x415e},
{0x91d7,0x91d7,0x9e7},
{0x91d8,0x91d8,0x9e5},
{0x91d9,0x91d9,0x9e9},
{0x91da,0x91da,0x1e2e},
{0x91dc,0x91dc,0x9e8},
{0x91dd,0x91dd,0x9e6},
{0x91df,0x91df,0x453a},
{0x91e2,0x91e2,0x1e2d},
{0x91e3,0x91e3,0xbcc},
{0x91e4,0x91e4,0x209a},
{0x91e5,0x91e5,0x3c06},
{0x91e6,0x91e6,0xbcb},
{0x91e7,0x91e7,0xbcd},
{0x91e8,0x91e8,0x209f},
{0x91e9,0x91e9,0xbcf},
{0x91ea,0x91eb,0x209c},
{0x91ec,0x91ec,0x2095},
{0x91ed,0x91ed,0xbce},
{0x91ee,0x91ee,0x20a0},
{0x91f1,0x91f1,0x2097},
{0x91f3,0x91f3,0x2098},
{0x91f4,0x91f4,0x2096},
{0x91f5,0x91f5,0xbca},
{0x91f7,0x91f7,0x209e},
{0x91f8,0x91f8,0x2099},
{0x91f9,0x91f9,0x209b},
{0x91fa,0x91fa,0x3af8},
{0x91fd,0x91fd,0x233f},
{0x91fe,0x91fe,0x382e},
{0x91ff,0x91ff,0x233e},
{0x9200,0x9200,0x233c},
{0x9201,0x9201,0x2334},
{0x9202,0x9202,0x2343},
{0x9203,0x9203,0x2337},
{0x9204,0x9204,0x2341},
{0x9205,0x9205,0x2348},
{0x9206,0x9206,0x2340},
{0x9207,0x9207,0xd96},
{0x9208,0x9208,0x3eb9},
{0x9209,0x9209,0xd92},
{0x920a,0x920a,0x2335},
{0x920c,0x920c,0x233b},
{0x920d,0x920d,0xd94},
{0x920e,0x920e,0x3888},
{0x920f,0x920f,0x233a},
{0x9210,0x9210,0xd95},
{0x9211,0x9211,0xd97},
{0x9212,0x9212,0x233d},
{0x9213,0x9213,0x4793},
{0x9214,0x9215,0xd8f},
{0x9216,0x9216,0x2349},
{0x9217,0x9217,0x2347},
{0x9219,0x9219,0x2346},
{0x921a,0x921a,0x2338},
{0x921c,0x921c,0x2344},
{0x921e,0x921e,0xd93},
{0x9223,0x9223,0xd91},
{0x9224,0x9224,0x2345},
{0x9225,0x9225,0x2336},
{0x9226,0x9226,0x2339},
{0x9227,0x9227,0x2342},
{0x9228,0x9228,0x4795},
{0x922a,0x922a,0x3c0e},
{0x922b,0x922b,0x3ba6},
{0x922d,0x922d,0x2851},
{0x922e,0x922e,0x25da},
{0x9230,0x9230,0x25d3},
{0x9231,0x9231,0x25e6},
{0x9232,0x9232,0x25ef},
{0x9233,0x9233,0x25d6},
{0x9234,0x9234,0xf55},
{0x9235,0x9235,0x3b26},
{0x9236,0x9236,0x25e3},
{0x9237,0x9237,0xf4b},
{0x9238,0x9238,0xf4d},
{0x9239,0x9239,0xf59},
{0x923a,0x923a,0x25d4},
{0x923c,0x923c,0x41d2},
{0x923d,0x923d,0xf4e},
{0x923e,0x923e,0xf50},
{0x923f,0x923f,0xf5a},
{0x9240,0x9240,0xf4f},
{0x9241,0x9241,0x388a},
{0x9244,0x9244,0x389a},
{0x9245,0x9245,0xf58},
{0x9246,0x9246,0x25dc},
{0x9248,0x9248,0x25d1},
{0x9249,0x9249,0xf56},
{0x924a,0x924a,0x25db},
{0x924b,0x924b,0xf52},
{0x924c,0x924c,0x25ed},
{0x924d,0x924d,0xf57},
{0x924e,0x924e,0x25eb},
{0x924f,0x924f,0x25df},
{0x9250,0x9250,0x25e9},
{0x9251,0x9251,0xf54},
{0x9252,0x9252,0x25d2},
{0x9253,0x9253,0x25ec},
{0x9254,0x9254,0x25e7},
{0x9255,0x9255,0x3e47},
{0x9256,0x9256,0x25ee},
{0x9257,0x9257,0xf4c},
{0x9258,0x9258,0x4796},
{0x925a,0x925a,0xf5b},
{0x925b,0x925b,0xf51},
{0x925d,0x925d,0x3a03},
{0x925e,0x925e,0x25d8},
{0x925f,0x925f,0x3ce0},
{0x9260,0x9260,0x25e0},
{0x9261,0x9261,0x25e4},
{0x9262,0x9262,0x388b},
{0x9263,0x9263,0x25e8},
{0x9264,0x9264,0xf53},
{0x9265,0x9265,0x25d7},
{0x9266,0x9266,0x25d5},
{0x9267,0x9267,0x25e1},
{0x926b,0x926b,0x4797},
{0x926c,0x926c,0x25de},
{0x926d,0x926d,0x25dd},
{0x926e,0x926e,0x3d05},
{0x926f,0x926f,0x25e2},
{0x9270,0x9270,0x25e5},
{0x9272,0x9272,0x25ea},
{0x9276,0x9276,0x2831},
{0x9277,0x9277,0x3c64},
{0x9278,0x9278,0x10d3},
{0x9279,0x9279,0x283b},
{0x927a,0x927a,0x2833},
{0x927b,0x927b,0x10d9},
{0x927c,0x927c,0x10dd},
{0x927d,0x927d,0x2844},
{0x927e,0x927e,0x284c},
{0x927f,0x927f,0x283d},
{0x9280,0x9280,0x10d5},
{0x9281,0x9281,0x3e01},
{0x9282,0x9282,0x2841},
{0x9283,0x9283,0x25d9},
{0x9284,0x9284,0x3cb9},
{0x9285,0x9285,0x10d6},
{0x9286,0x9286,0x2848},
{0x9287,0x9287,0x284d},
{0x9288,0x9288,0x2845},
{0x9289,0x9289,0x3ab5},
{0x928a,0x928a,0x2847},
{0x928b,0x928b,0x2850},
{0x928c,0x928c,0x2849},
{0x928d,0x928d,0x2837},
{0x928e,0x928e,0x2840},
{0x928f,0x928f,0x46bd},
{0x9291,0x9291,0x10de},
{0x9293,0x9293,0x10da},
{0x9294,0x9294,0x2835},
{0x9295,0x9295,0x2842},
{0x9296,0x9296,0x10d8},
{0x9297,0x9297,0x283c},
{0x9298,0x9298,0x10d7},
{0x9299,0x9299,0x284a},
{0x929a,0x929a,0x2839},
{0x929b,0x929b,0x2832},
{0x929c,0x929c,0x10db},
{0x929d,0x929d,0x284f},
{0x92a0,0x92a0,0x2834},
{0x92a1,0x92a1,0x2846},
{0x92a2,0x92a2,0x2843},
{0x92a3,0x92a3,0x283e},
{0x92a4,0x92a4,0x2830},
{0x92a5,0x92a5,0x282f},
{0x92a6,0x92a6,0x2838},
{0x92a7,0x92a7,0x284b},
{0x92a8,0x92a8,0x10dc},
{0x92a9,0x92a9,0x284e},
{0x92aa,0x92aa,0x2836},
{0x92ab,0x92ab,0x283a},
{0x92ac,0x92ac,0x10d4},
{0x92ae,0x92ae,0x4799},
{0x92b1,0x92b1,0x4798},
{0x92b2,0x92b2,0x125f},
{0x92b3,0x92b3,0x125a},
{0x92b4,0x92b4,0x2ab0},
{0x92b5,0x92b5,0x2aac},
{0x92b6,0x92b6,0x2a94},
{0x92b7,0x92b7,0x1256},
{0x92b9,0x92b9,0x36e9},
{0x92ba,0x92ba,0x3bf5},
{0x92bb,0x92bb,0x1255},
{0x92bc,0x92bc,0x125b},
{0x92be,0x92be,0x3c60},
{0x92bf,0x92bf,0x479a},
{0x92c0,0x92c0,0x2a92},
{0x92c1,0x92c1,0x1259},
{0x92c2,0x92c2,0x2a9e},
{0x92c3,0x92c4,0x2a90},
{0x92c5,0x92c5,0x1254},
{0x92c6,0x92c6,0x2aaf},
{0x92c7,0x92c7,0x125d},
{0x92c8,0x92c8,0x2aa1},
{0x92c9,0x92c9,0x2aa6},
{0x92ca,0x92ca,0x2aa0},
{0x92cb,0x92cb,0x2cd3},
{0x92cc,0x92cc,0x2a9c},
{0x92cd,0x92cd,0x2aa4},
{0x92ce,0x92ce,0x2aa2},
{0x92cf,0x92cf,0x2a95},
{0x92d0,0x92d0,0x2a8f},
{0x92d1,0x92d1,0x2aaa},
{0x92d2,0x92d2,0x125c},
{0x92d3,0x92d3,0x2aab},
{0x92d4,0x92d4,0x3861},
{0x92d5,0x92d5,0x2aa5},
{0x92d7,0x92d7,0x2a9a},
{0x92d8,0x92d8,0x2a98},
{0x92d9,0x92d9,0x2a93},
{0x92db,0x92db,0x400c},
{0x92dd,0x92dd,0x2a9b},
{0x92de,0x92de,0x2aa8},
{0x92df,0x92df,0x2a97},
{0x92e0,0x92e0,0x2aa7},
{0x92e1,0x92e1,0x2aad},
{0x92e3,0x92e3,0x479b},
{0x92e4,0x92e4,0x1258},
{0x92e5,0x92e5,0x3ca2},
{0x92e6,0x92e6,0x2aa3},
{0x92e7,0x92e7,0x2aa9},
{0x92e8,0x92e8,0x2a9f},
{0x92e9,0x92e9,0x2a99},
{0x92ea,0x92ea,0x1257},
{0x92eb,0x92eb,0x479c},
{0x92ec,0x92ec,0x3964},
{0x92ee,0x92ee,0x283f},
{0x92ef,0x92ef,0x2a9d},
{0x92f0,0x92f0,0x125e},
{0x92f1,0x92f1,0x2a96},
{0x92f2,0x92f2,0x3ab2},
{0x92f3,0x92f4,0x479d},
{0x92f6,0x92f6,0x3c04},
{0x92f7,0x92f7,0x2cd8},
{0x92f8,0x92f8,0x1377},
{0x92f9,0x92f9,0x2cd7},
{0x92fa,0x92fa,0x2cd5},
{0x92fb,0x92fb,0x2ce7},
{0x92fc,0x92fc,0x137b},
{0x92fd,0x92fd,0x479f},
{0x92fe,0x92fe,0x2ce4},
{0x92ff,0x92ff,0x2cdc},
{0x9300,0x9300,0x2ce6},
{0x9301,0x9301,0x2cce},
{0x9302,0x9302,0x2cda},
{0x9303,0x9303,0x3867},
{0x9304,0x9304,0x137d},
{0x9306,0x9306,0x2cc6},
{0x9307,0x9307,0x3b11},
{0x9308,0x9308,0x2cc4},
{0x9309,0x9309,0x2ce5},
{0x930b,0x930b,0x2ce3},
{0x930c,0x930c,0x2ce2},
{0x930d,0x930d,0x2cd2},
{0x930e,0x930e,0x2cd1},
{0x930f,0x930f,0x2cc7},
{0x9310,0x9310,0x137f},
{0x9312,0x9312,0x2ccd},
{0x9313,0x9313,0x2cd6},
{0x9314,0x9314,0x2ce1},
{0x9315,0x9315,0x1382},
{0x9316,0x9316,0x2ce8},
{0x9318,0x9318,0x14a3},
{0x9319,0x9319,0x1384},
{0x931a,0x931a,0x137e},
{0x931b,0x931b,0x2ccb},
{0x931d,0x931d,0x2cd4},
{0x931e,0x931e,0x2cc3},
{0x931f,0x931f,0x2cc5},
{0x9320,0x9320,0x1375},
{0x9321,0x9321,0x1381},
{0x9322,0x9322,0x137a},
{0x9323,0x9323,0x2ccc},
{0x9324,0x9324,0x2cdb},
{0x9325,0x9325,0x2aae},
{0x9326,0x9326,0x1380},
{0x9327,0x9327,0x2cc2},
{0x9328,0x9328,0x149e},
{0x9329,0x9329,0x2cdd},
{0x932a,0x932a,0x2ce0},
{0x932b,0x932b,0x137c},
{0x932c,0x932c,0x3892},
{0x932d,0x932d,0x2cd0},
{0x932e,0x932e,0x1383},
{0x932f,0x932f,0x1379},
{0x9330,0x9330,0x3e03},
{0x9331,0x9331,0x3cbd},
{0x9333,0x9333,0x1378},
{0x9334,0x9334,0x2cd9},
{0x9335,0x9335,0x2cdf},
{0x9336,0x9336,0x1376},
{0x9338,0x9338,0x2cc9},
{0x9339,0x9339,0x2cde},
{0x933c,0x933c,0x2cca},
{0x9340,0x9340,0x4155},
{0x9341,0x9341,0x4277},
{0x9342,0x9342,0x3afa},
{0x9343,0x9343,0x47a0},
{0x9344,0x9344,0x4016},
{0x9345,0x9345,0x3e74},
{0x9346,0x9346,0x2ccf},
{0x9347,0x9347,0x2e8d},
{0x9348,0x9348,0x3c61},
{0x9349,0x9349,0x2e92},
{0x934a,0x934a,0x14a0},
{0x934b,0x934b,0x14a2},
{0x934c,0x934c,0x2e98},
{0x934d,0x934d,0x149c},
{0x934e,0x934e,0x2ea6},
{0x934f,0x934f,0x2e9e},
{0x9350,0x9351,0x2e93},
{0x9352,0x9352,0x2e9d},
{0x9354,0x9354,0x14a9},
{0x9355,0x9355,0x2e9c},
{0x9356,0x9356,0x2e8c},
{0x9357,0x9357,0x2e9b},
{0x9358,0x9358,0x2e8f},
{0x9359,0x9359,0x2ea7},
{0x935a,0x935a,0x14a8},
{0x935b,0x935b,0x14a6},
{0x935c,0x935c,0x2e90},
{0x935e,0x935e,0x2ea3},
{0x935f,0x935f,0x3cbb},
{0x9360,0x9360,0x2e95},
{0x9361,0x9361,0x2ea2},
{0x9362,0x9362,0x4268},
{0x9363,0x9363,0x2ea4},
{0x9364,0x9364,0x2e8b},
{0x9365,0x9365,0x14a1},
{0x9366,0x9366,0x46bc},
{0x9367,0x9367,0x2ea5},
{0x9368,0x9368,0x3bf0},
{0x9369,0x9369,0x3961},
{0x936a,0x936a,0x2e99},
{0x936b,0x936b,0x3893},
{0x936c,0x936c,0x14a5},
{0x936d,0x936d,0x2e96},
{0x9370,0x9370,0x14a7},
{0x9371,0x9371,0x2e9f},
{0x9373,0x9373,0x389e},
{0x9374,0x9374,0x3ce4},
{0x9375,0x9375,0x149f},
{0x9376,0x9376,0x2e91},
{0x9377,0x9377,0x2ea0},
{0x9378,0x9378,0x38f9},
{0x9379,0x9379,0x2e9a},
{0x937a,0x937a,0x2cc8},
{0x937b,0x937b,0x2ea1},
{0x937c,0x937c,0x2e8e},
{0x937d,0x937d,0x3c25},
{0x937e,0x937e,0x14a4},
{0x9380,0x9380,0x3014},
{0x9381,0x9381,0x3965},
{0x9382,0x9382,0x149d},
{0x9383,0x9383,0x2e89},
{0x9384,0x9384,0x47a1},
{0x9385,0x9385,0x42bd},
{0x9386,0x9386,0x3cc0},
{0x9387,0x9387,0x3ae0},
{0x9388,0x9388,0x3011},
{0x9389,0x9389,0x300a},
{0x938a,0x938a,0x1540},
{0x938c,0x938c,0x3005},
{0x938d,0x938d,0x3015},
{0x938e,0x938e,0x300c},
{0x938f,0x938f,0x2e97},
{0x9390,0x9390,0x3d3c},
{0x9391,0x9391,0x3017},
{0x9392,0x9392,0x3006},
{0x9394,0x9394,0x153f},
{0x9395,0x9395,0x3010},
{0x9396,0x9396,0x1541},
{0x9397,0x9397,0x1549},
{0x9398,0x9398,0x1547},
{0x9399,0x9399,0x3012},
{0x939a,0x939a,0x1548},
{0x939b,0x939b,0x3008},
{0x939c,0x939c,0x39ac},
{0x939d,0x939d,0x3009},
{0x939e,0x939e,0x300e},
{0x939f,0x939f,0x3013},
{0x93a0,0x93a0,0x3ab1},
{0x93a1,0x93a1,0x2e88},
{0x93a2,0x93a2,0x1542},
{0x93a3,0x93a3,0x301c},
{0x93a4,0x93a4,0x3019},
{0x93a5,0x93a5,0x3160},
{0x93a6,0x93a6,0x300f},
{0x93a7,0x93a7,0x300b},
{0x93a8,0x93a8,0x301a},
{0x93a9,0x93a9,0x3155},
{0x93aa,0x93aa,0x300d},
{0x93ac,0x93ac,0x1545},
{0x93ad,0x93ad,0x47a2},
{0x93ae,0x93ae,0x1544},
{0x93af,0x93af,0x2e8a},
{0x93b0,0x93b0,0x1546},
{0x93b1,0x93b1,0x3016},
{0x93b2,0x93b2,0x3018},
{0x93b3,0x93b3,0x1543},
{0x93b4,0x93b4,0x301b},
{0x93b5,0x93b5,0x3004},
{0x93b7,0x93b7,0x3007},
{0x93b8,0x93b8,0x3c90},
{0x93ba,0x93ba,0x43f7},
{0x93bb,0x93bb,0x3c8f},
{0x93bd,0x93bd,0x3a27},
{0x93bf,0x93bf,0x3e2d},
{0x93c0,0x93c0,0x315e},
{0x93c2,0x93c2,0x314e},
{0x93c3,0x93c3,0x15e0},
{0x93c4,0x93c4,0x315c},
{0x93c7,0x93c7,0x314c},
{0x93c8,0x93c8,0x15e1},
{0x93ca,0x93ca,0x3157},
{0x93cb,0x93cb,0x3966},
{0x93cc,0x93cc,0x3153},
{0x93cd,0x93cd,0x15e6},
{0x93ce,0x93ce,0x315d},
{0x93cf,0x93cf,0x314d},
{0x93d0,0x93d0,0x3150},
{0x93d1,0x93d1,0x15de},
{0x93d2,0x93d2,0x315f},
{0x93d3,0x93d3,0x40e1},
{0x93d4,0x93d4,0x3158},
{0x93d5,0x93d5,0x315b},
{0x93d6,0x93d6,0x15e4},
{0x93d7,0x93d7,0x15e9},
{0x93d8,0x93d8,0x15e7},
{0x93d9,0x93d9,0x3154},
{0x93da,0x93da,0x314f},
{0x93db,0x93db,0x3c5f},
{0x93dc,0x93dd,0x15e2},
{0x93de,0x93de,0x314b},
{0x93df,0x93df,0x15df},
{0x93e0,0x93e0,0x3a20},
{0x93e1,0x93e1,0x15dd},
{0x93e2,0x93e2,0x15e5},
{0x93e3,0x93e3,0x315a},
{0x93e4,0x93e4,0x15e8},
{0x93e6,0x93e6,0x3156},
{0x93e7,0x93e7,0x3161},
{0x93e8,0x93e8,0x15ea},
{0x93ec,0x93ec,0x3152},
{0x93ee,0x93ee,0x3159},
{0x93f0,0x93f0,0x423f},
{0x93f1,0x93f1,0x42aa},
{0x93f3,0x93f3,0x3962},
{0x93f5,0x93f5,0x325a},
{0x93f6,0x93f6,0x3269},
{0x93f7,0x93f7,0x325c},
{0x93f8,0x93f8,0x3263},
{0x93f9,0x93f9,0x3151},
{0x93fa,0x93fa,0x3261},
{0x93fb,0x93fb,0x3250},
{0x93fc,0x93fc,0x3267},
{0x93fd,0x93fd,0x1654},
{0x93fe,0x93fe,0x3254},
{0x93ff,0x93ff,0x3266},
{0x9400,0x9400,0x325b},
{0x9401,0x9401,0x3cd4},
{0x9403,0x9403,0x1653},
{0x9404,0x9404,0x3bc9},
{0x9406,0x9406,0x326b},
{0x9407,0x9407,0x325d},
{0x9408,0x9408,0x3a25},
{0x9409,0x9409,0x3262},
{0x940a,0x940a,0x3265},
{0x940b,0x940b,0x324e},
{0x940c,0x940c,0x3268},
{0x940d,0x940d,0x3259},
{0x940e,0x940e,0x325e},
{0x940f,0x940f,0x3252},
{0x9410,0x9410,0x3256},
{0x9411,0x9411,0x326a},
{0x9412,0x9412,0x3260},
{0x9413,0x9413,0x324f},
{0x9414,0x9414,0x3253},
{0x9415,0x9415,0x3255},
{0x9416,0x9416,0x325f},
{0x9417,0x9417,0x47a5},
{0x9418,0x9418,0x1652},
{0x9419,0x9419,0x3258},
{0x941b,0x941b,0x4148},
{0x941d,0x941d,0x47a6},
{0x9420,0x9420,0x3251},
{0x9424,0x9424,0x3943},
{0x9425,0x9425,0x38d0},
{0x9426,0x9426,0x38a3},
{0x9427,0x9427,0x3bcd},
{0x9428,0x9428,0x3257},
{0x9429,0x9429,0x3325},
{0x942a,0x942a,0x3329},
{0x942b,0x942b,0x169f},
{0x942c,0x942c,0x332b},
{0x942d,0x942d,0x47a7},
{0x942e,0x942e,0x1699},
{0x9430,0x9430,0x3327},
{0x9431,0x9431,0x332d},
{0x9432,0x9432,0x169e},
{0x9433,0x9433,0x169a},
{0x9435,0x9435,0x169b},
{0x9436,0x9436,0x3324},
{0x9437,0x9437,0x332a},
{0x9438,0x9438,0x169d},
{0x9439,0x9439,0x3328},
{0x943a,0x943a,0x169c},
{0x943b,0x943b,0x3323},
{0x943c,0x943c,0x3264},
{0x943d,0x943d,0x3326},
{0x943e,0x943e,0x47a8},
{0x943f,0x943f,0x3322},
{0x9440,0x9440,0x332c},
{0x9442,0x9442,0x4272},
{0x9443,0x9443,0x4275},
{0x9444,0x9444,0x16df},
{0x9445,0x9445,0x33cd},
{0x9446,0x9446,0x33d0},
{0x9447,0x9447,0x33cc},
{0x9448,0x9449,0x33ce},
{0x944a,0x944b,0x33c9},
{0x944c,0x944c,0x33c7},
{0x944d,0x944d,0x3c65},
{0x944f,0x944f,0x33cb},
{0x9450,0x9450,0x33c8},
{0x9451,0x9452,0x16e0},
{0x9454,0x9454,0x47aa},
{0x9455,0x9455,0x3448},
{0x9457,0x9457,0x344a},
{0x9458,0x9458,0x3c30},
{0x945b,0x945b,0x389f},
{0x945d,0x945d,0x3449},
{0x945e,0x945e,0x344b},
{0x9460,0x9460,0x170e},
{0x9462,0x9462,0x3447},
{0x9463,0x9463,0x170d},
{0x9464,0x9464,0x170f},
{0x9465,0x9465,0x3dde},
{0x9467,0x9467,0x3ab8},
{0x9468,0x9469,0x34a6},
{0x946a,0x946a,0x1731},
{0x946b,0x946b,0x34a5},
{0x946c,0x946c,0x377e},
{0x946d,0x946d,0x34ed},
{0x946e,0x946e,0x34ec},
{0x946f,0x946f,0x34ee},
{0x9470,0x9470,0x174c},
{0x9471,0x9471,0x34ef},
{0x9472,0x9472,0x174b},
{0x9473,0x9473,0x34f0},
{0x9474,0x9474,0x350c},
{0x9475,0x9475,0x350f},
{0x9476,0x9476,0x350e},
{0x9477,0x9477,0x1755},
{0x9478,0x9478,0x350d},
{0x9479,0x9479,0x47ab},
{0x947b,0x947b,0x419b},
{0x947c,0x947c,0x175f},
{0x947d,0x947e,0x175d},
{0x947f,0x947f,0x1764},
{0x9480,0x9481,0x3539},
{0x9482,0x9482,0x3538},
{0x9483,0x9483,0x3540},
{0x9485,0x9485,0x4507},
{0x949f,0x949f,0x4885},
{0x94a2,0x94a2,0x451b},
{0x94c1,0x94c1,0x47e1},
{0x94c3,0x94c3,0x47df},
{0x94dc,0x94dc,0x47d7},
{0x94f6,0x94f6,0x47d2},
{0x952d,0x952d,0x47ac},
{0x9547,0x9547,0x48bf},
{0x9577,0x9577,0x697},
{0x9578,0x9578,0x4508},
{0x957a,0x957a,0x20a1},
{0x957b,0x957b,0x234a},
{0x957c,0x957c,0x2ab1},
{0x957d,0x957d,0x3162},
{0x957f,0x957f,0x4509},
{0x9580,0x9580,0x698},
{0x9582,0x9582,0x821},
{0x9583,0x9583,0x9ea},
{0x9585,0x9585,0x38a1},
{0x9586,0x9586,0x20a2},
{0x9588,0x9588,0x20a3},
{0x9589,0x9589,0xbd0},
{0x958b,0x958b,0xd9a},
{0x958c,0x958c,0x234c},
{0x958d,0x958d,0x234b},
{0x958e,0x958e,0xd9e},
{0x958f,0x958f,0xd99},
{0x9590,0x9590,0x234d},
{0x9591,0x9591,0xd9b},
{0x9592,0x9592,0xd9d},
{0x9593,0x9593,0xd9c},
{0x9594,0x9594,0xd98},
{0x9596,0x9596,0x3bd5},
{0x9597,0x9597,0x3bd4},
{0x9598,0x9598,0xf5c},
{0x9599,0x9599,0x3bd2},
{0x959b,0x959b,0x25f3},
{0x959c,0x959c,0x25f1},
{0x959e,0x959e,0x25f2},
{0x959f,0x959f,0x25f0},
{0x95a0,0x95a0,0x38a4},
{0x95a1,0x95a1,0x10df},
{0x95a2,0x95a2,0x47ad},
{0x95a3,0x95a3,0x10e2},
{0x95a4,0x95a4,0x10e4},
{0x95a5,0x95a5,0x10e3},
{0x95a6,0x95a6,0x38a2},
{0x95a7,0x95a7,0x3bd3},
{0x95a8,0x95a9,0x10e0},
{0x95aa,0x95aa,0x4395},
{0x95ab,0x95ab,0x2ab3},
{0x95ac,0x95ac,0x2ab2},
{0x95ad,0x95ad,0x1260},
{0x95ae,0x95ae,0x2ab4},
{0x95b0,0x95b0,0x2ab5},
{0x95b1,0x95b1,0x1261},
{0x95b5,0x95b5,0x2cf0},
{0x95b6,0x95b6,0x2cee},
{0x95b7,0x95b7,0x2ead},
{0x95b9,0x95ba,0x2cec},
{0x95bb,0x95bb,0x1385},
{0x95bc,0x95bc,0x2ce9},
{0x95bd,0x95bd,0x2cf1},
{0x95be,0x95be,0x2ceb},
{0x95bf,0x95bf,0x2cef},
{0x95c0,0x95c0,0x2ea9},
{0x95c3,0x95c3,0x2eab},
{0x95c5,0x95c5,0x2eac},
{0x95c6,0x95c6,0x14ae},
{0x95c7,0x95c7,0x2ea8},
{0x95c8,0x95c8,0x14ad},
{0x95c9,0x95c9,0x2eaa},
{0x95ca,0x95cc,0x14aa},
{0x95cd,0x95cd,0x2cea},
{0x95d0,0x95d0,0x154c},
{0x95d1,0x95d1,0x301f},
{0x95d2,0x95d3,0x301d},
{0x95d4,0x95d4,0x154a},
{0x95d5,0x95d5,0x154d},
{0x95d6,0x95d6,0x154b},
{0x95da,0x95db,0x3163},
{0x95dc,0x95dc,0x15eb},
{0x95de,0x95de,0x326c},
{0x95df,0x95df,0x326e},
{0x95e0,0x95e0,0x326d},
{0x95e1,0x95e1,0x1655},
{0x95e2,0x95e2,0x16a0},
{0x95e3,0x95e3,0x3330},
{0x95e4,0x95e4,0x332f},
{0x95e5,0x95e5,0x332e},
{0x95e8,0x95e8,0x450a},
{0x95f4,0x95f4,0x47af},
{0x961c,0x961c,0x699},
{0x961d,0x961d,0x4519},
{0x961e,0x961e,0x17b4},
{0x9620,0x9620,0x1821},
{0x9621,0x9621,0x414},
{0x9622,0x9622,0x181f},
{0x9623,0x9623,0x1822},
{0x9624,0x9624,0x1820},
{0x9628,0x9628,0x18f1},
{0x962a,0x962a,0x526},
{0x962c,0x962c,0x527},
{0x962d,0x962d,0x18f3},
{0x962e,0x962e,0x524},
{0x962f,0x962f,0x18f2},
{0x9630,0x9630,0x18f0},
{0x9631,0x9631,0x525},
{0x9632,0x9632,0x523},
{0x9633,0x9633,0x47b0},
{0x9638,0x9638,0x3fd1},
{0x9639,0x9639,0x1a55},
{0x963a,0x963a,0x1a58},
{0x963b,0x963b,0x69c},
{0x963c,0x963c,0x1a57},
{0x963d,0x963d,0x1a56},
{0x963f,0x963f,0x69b},
{0x9640,0x9640,0x69a},
{0x9641,0x9641,0x3f3d},
{0x9642,0x9642,0x69e},
{0x9643,0x9643,0x1a59},
{0x9644,0x9644,0x69d},
{0x9645,0x9645,0x4772},
{0x964a,0x964a,0x1c1a},
{0x964b,0x964d,0x823},
{0x964e,0x964e,0x1c1b},
{0x964f,0x964f,0x1c17},
{0x9650,0x9650,0x822},
{0x9651,0x9651,0x1c18},
{0x9653,0x9653,0x1c19},
{0x9654,0x9654,0x1c16},
{0x9656,0x9656,0x3de7},
{0x9658,0x9658,0x9f1},
{0x965b,0x965b,0x9ee},
{0x965c,0x965c,0x1e2f},
{0x965d,0x965d,0x9ef},
{0x965e,0x965e,0x9f2},
{0x965f,0x965f,0x1e30},
{0x9661,0x9661,0x9ed},
{0x9662,0x9663,0x9eb},
{0x9664,0x9664,0x9f0},
{0x9669,0x9669,0x475e},
{0x966a,0x966a,0xbd1},
{0x966b,0x966b,0x20a6},
{0x966c,0x966c,0xbd9},
{0x966d,0x966d,0x20a5},
{0x966f,0x966f,0x20a8},
{0x9670,0x9670,0xbd5},
{0x9671,0x9671,0x20a7},
{0x9672,0x9672,0xda6},
{0x9673,0x9673,0xbd3},
{0x9674,0x9674,0xbd6},
{0x9675,0x9675,0xbd2},
{0x9676,0x9677,0xbd7},
{0x9678,0x9678,0xbd4},
{0x967b,0x967b,0x38ac},
{0x967c,0x967c,0x20a4},
{0x967d,0x967d,0xda2},
{0x967e,0x967e,0x234f},
{0x9680,0x9680,0x2353},
{0x9681,0x9681,0x3f46},
{0x9683,0x9683,0x2352},
{0x9684,0x9684,0xda7},
{0x9685,0x9686,0xda3},
{0x9687,0x9687,0x234e},
{0x9688,0x9689,0x2350},
{0x968a,0x968a,0xd9f},
{0x968b,0x968b,0xda1},
{0x968d,0x968d,0xda5},
{0x968e,0x968e,0xda0},
{0x968f,0x968f,0x3bde},
{0x9691,0x9691,0x25f6},
{0x9692,0x9693,0x25f4},
{0x9694,0x9695,0xf5e},
{0x9696,0x9696,0x38ad},
{0x9697,0x9697,0x25f7},
{0x9698,0x9698,0xf5d},
{0x9699,0x9699,0x10e5},
{0x969b,0x969b,0x10e7},
{0x969c,0x969c,0x10e6},
{0x969e,0x969e,0x2852},
{0x96a1,0x96a1,0x2853},
{0x96a2,0x96a2,0x2ab7},
{0x96a3,0x96a3,0x38af},
{0x96a4,0x96a4,0x2ab6},
{0x96a5,0x96a5,0x42ef},
{0x96a7,0x96a8,0x1386},
{0x96a9,0x96a9,0x2cf2},
{0x96aa,0x96aa,0x1388},
{0x96ac,0x96ac,0x2eb0},
{0x96ae,0x96ae,0x2eae},
{0x96b0,0x96b0,0x2eaf},
{0x96b1,0x96b1,0x14af},
{0x96b3,0x96b3,0x3020},
{0x96b4,0x96b4,0x15ec},
{0x96b6,0x96b6,0x231},
{0x96b8,0x96b8,0x14b0},
{0x96b9,0x96b9,0x69f},
{0x96bb,0x96bb,0x9f3},
{0x96bc,0x96bc,0x1e31},
{0x96bd,0x96bd,0x38b5},
{0x96bf,0x96bf,0x20a9},
{0x96c0,0x96c0,0xbda},
{0x96c1,0x96c1,0xda8},
{0x96c2,0x96c2,0x2354},
{0x96c3,0x96c3,0x2356},
{0x96c4,0x96c4,0xdaa},
{0x96c5,0x96c5,0xda9},
{0x96c6,0x96c7,0xdab},
{0x96c8,0x96c8,0x2355},
{0x96c9,0x96ca,0xf62},
{0x96cb,0x96cb,0xf61},
{0x96cc,0x96cc,0x10e8},
{0x96cd,0x96cd,0xf60},
{0x96ce,0x96ce,0x25f8},
{0x96d2,0x96d2,0x10e9},
{0x96d3,0x96d3,0x2ab8},
{0x96d4,0x96d4,0x2cf3},
{0x96d5,0x96d5,0x1389},
{0x96d6,0x96d6,0x14b1},
{0x96d7,0x96d7,0x3021},
{0x96d8,0x96d8,0x3025},
{0x96d9,0x96d9,0x1550},
{0x96da,0x96da,0x3022},
{0x96db,0x96db,0x1551},
{0x96dc,0x96dc,0x154f},
{0x96dd,0x96dd,0x3026},
{0x96de,0x96de,0x1552},
{0x96df,0x96df,0x3024},
{0x96e1,0x96e1,0x3165},
{0x96e2,0x96e2,0x154e},
{0x96e3,0x96e3,0x15ed},
{0x96e5,0x96e5,0x34a8},
{0x96e8,0x96e8,0x6a0},
{0x96e9,0x96e9,0xbdc},
{0x96ea,0x96ea,0xbdb},
{0x96ef,0x96ef,0xdad},
{0x96f0,0x96f0,0x2358},
{0x96f1,0x96f1,0x2357},
{0x96f2,0x96f2,0xdae},
{0x96f4,0x96f4,0x3a04},
{0x96f5,0x96f5,0x25fc},
{0x96f6,0x96f6,0xf67},
{0x96f7,0x96f7,0xf64},
{0x96f8,0x96f8,0x25fb},
{0x96f9,0x96f9,0xf66},
{0x96fa,0x96fa,0x25f9},
{0x96fb,0x96fb,0xf65},
{0x96fd,0x96fd,0x25fa},
{0x96ff,0x96ff,0x2854},
{0x9700,0x9700,0x10ea},
{0x9702,0x9702,0x2abb},
{0x9703,0x9703,0x3f0d},
{0x9704,0x9704,0x1262},
{0x9705,0x9705,0x2ab9},
{0x9706,0x9707,0x1263},
{0x9708,0x9708,0x2aba},
{0x9709,0x9709,0x1265},
{0x970b,0x970b,0x2cf4},
{0x970d,0x970d,0x138d},
{0x970e,0x970e,0x138a},
{0x970f,0x970f,0x138f},
{0x9710,0x9710,0x2cf6},
{0x9711,0x9711,0x138b},
{0x9712,0x9712,0x2cf5},
{0x9713,0x9713,0x138e},
{0x9716,0x9716,0x138c},
{0x9718,0x9718,0x2eb3},
{0x9719,0x9719,0x2eb5},
{0x971b,0x971b,0x38c9},
{0x971c,0x971c,0x14b2},
{0x971d,0x971d,0x2eb4},
{0x971e,0x971e,0x14b3},
{0x971f,0x971f,0x2eb2},
{0x9720,0x9720,0x2eb1},
{0x9721,0x9721,0x3fb3},
{0x9722,0x9722,0x3028},
{0x9723,0x9723,0x3027},
{0x9724,0x9724,0x1553},
{0x9725,0x9725,0x3029},
{0x9726,0x9726,0x316a},
{0x9727,0x9727,0x15ef},
{0x9728,0x9728,0x3169},
{0x9729,0x9729,0x3166},
{0x972a,0x972a,0x15ee},
{0x972b,0x972c,0x3167},
{0x972e,0x972f,0x326f},
{0x9730,0x9730,0x1656},
{0x9731,0x9731,0x38c0},
{0x9732,0x9732,0x16a3},
{0x9735,0x9735,0x3331},
{0x9736,0x9736,0x38c2},
{0x9738,0x9739,0x16a1},
{0x973a,0x973a,0x3332},
{0x973d,0x973e,0x16e2},
{0x973f,0x973f,0x33d1},
{0x9740,0x9740,0x47b3},
{0x9741,0x9741,0x3be9},
{0x9742,0x9742,0x1732},
{0x9743,0x9743,0x34aa},
{0x9744,0x9744,0x1734},
{0x9746,0x9746,0x34a9},
{0x9747,0x9747,0x34ab},
{0x9748,0x9748,0x1733},
{0x9749,0x9749,0x34f1},
{0x974b,0x974b,0x3526},
{0x9751,0x9751,0x44e3},
{0x9752,0x9752,0x6a1},
{0x9756,0x9756,0xf68},
{0x9757,0x9757,0x38cc},
{0x9758,0x9758,0x2855},
{0x975a,0x975a,0x2abc},
{0x975b,0x975c,0x1390},
{0x975e,0x975e,0x6a2},
{0x975f,0x975f,0x38cf},
{0x9760,0x9760,0x1266},
{0x9761,0x9761,0x15f0},
{0x9762,0x9762,0x826},
{0x9766,0x9766,0x1392},
{0x9768,0x9768,0x1710},
{0x9769,0x9769,0x827},
{0x976a,0x976a,0x20aa},
{0x976c,0x976c,0x2359},
{0x976d,0x976d,0x3e97},
{0x976e,0x976e,0x235b},
{0x9770,0x9770,0x235a},
{0x9772,0x9772,0x2600},
{0x9773,0x9773,0x25fd},
{0x9774,0x9774,0xf69},
{0x9776,0x9776,0xf6a},
{0x9777,0x9778,0x25fe},
{0x977a,0x977a,0x2857},
{0x977b,0x977b,0x285c},
{0x977c,0x977c,0x10eb},
{0x977d,0x977d,0x2856},
{0x977e,0x977e,0x2858},
{0x977f,0x977f,0x285f},
{0x9780,0x9780,0x285a},
{0x9781,0x9781,0x285e},
{0x9782,0x9782,0x285b},
{0x9783,0x9783,0x2859},
{0x9784,0x9784,0x285d},
{0x9785,0x9785,0x10ec},
{0x9787,0x9787,0x40c0},
{0x9788,0x9788,0x2abf},
{0x9789,0x9789,0x38d4},
{0x978a,0x978a,0x2abd},
{0x978b,0x978b,0x1268},
{0x978d,0x978d,0x1267},
{0x978e,0x978e,0x2abe},
{0x978f,0x978f,0x1269},
{0x9794,0x9794,0x2cf9},
{0x9797,0x9797,0x2cf8},
{0x9798,0x9798,0x1393},
{0x9799,0x9799,0x2cf7},
{0x979a,0x979a,0x2eb6},
{0x979b,0x979b,0x3bfb},
{0x979c,0x979c,0x2eb8},
{0x979d,0x979d,0x2eba},
{0x979e,0x979e,0x2eb9},
{0x979f,0x979f,0x38d5},
{0x97a0,0x97a0,0x14b4},
{0x97a1,0x97a1,0x2eb7},
{0x97a2,0x97a2,0x3030},
{0x97a3,0x97a3,0x1554},
{0x97a4,0x97a4,0x302e},
{0x97a5,0x97a5,0x3031},
{0x97a6,0x97a6,0x1555},
{0x97a8,0x97a8,0x302c},
{0x97aa,0x97aa,0x302f},
{0x97ab,0x97ab,0x302d},
{0x97ac,0x97ac,0x302a},
{0x97ad,0x97ad,0x1556},
{0x97ae,0x97ae,0x302b},
{0x97b1,0x97b1,0x38d6},
{0x97b2,0x97b2,0x47b5},
{0x97b3,0x97b3,0x316b},
{0x97b4,0x97b4,0x3f33},
{0x97b6,0x97b6,0x316d},
{0x97b7,0x97b7,0x316c},
{0x97b8,0x97b8,0x3d95},
{0x97b9,0x97b9,0x3271},
{0x97ba,0x97ba,0x3f35},
{0x97bb,0x97bb,0x3272},
{0x97bd,0x97bd,0x494e},
{0x97be,0x97be,0x38d7},
{0x97bf,0x97bf,0x3333},
{0x97c0,0x97c0,0x38d8},
{0x97c1,0x97c1,0x16e5},
{0x97c2,0x97c2,0x47b6},
{0x97c3,0x97c3,0x16e4},
{0x97c4,0x97c5,0x344c},
{0x97c6,0x97c6,0x1735},
{0x97c7,0x97c7,0x34ac},
{0x97c8,0x97c8,0x3f2f},
{0x97c9,0x97c9,0x1756},
{0x97cb,0x97cb,0x828},
{0x97cc,0x97cc,0xdaf},
{0x97cd,0x97cd,0x2861},
{0x97ce,0x97ce,0x2860},
{0x97cf,0x97cf,0x2ac1},
{0x97d0,0x97d0,0x2ac0},
{0x97d2,0x97d2,0x38d9},
{0x97d3,0x97d3,0x14b5},
{0x97d4,0x97d4,0x2ebc},
{0x97d5,0x97d5,0x2ebb},
{0x97d6,0x97d6,0x3034},
{0x97d7,0x97d7,0x3032},
{0x97d8,0x97d8,0x3035},
{0x97d9,0x97d9,0x3033},
{0x97dc,0x97dc,0x15f1},
{0x97dd,0x97df,0x316e},
{0x97e0,0x97e0,0x38da},
{0x97e1,0x97e1,0x3334},
{0x97e3,0x97e3,0x33d2},
{0x97e5,0x97e5,0x34ad},
{0x97e6,0x97e6,0x450c},
{0x97ed,0x97ed,0x829},
{0x97ee,0x97ee,0x38dc},
{0x97f0,0x97f0,0x2cfa},
{0x97f1,0x97f1,0x2ebd},
{0x97f2,0x97f2,0x394d},
{0x97f3,0x97f3,0x82a},
{0x97f5,0x97f5,0x38e0},
{0x97f6,0x97f6,0x10ed},
{0x97f8,0x97f8,0x2cfb},
{0x97f9,0x97f9,0x1557},
{0x97fa,0x97fa,0x3036},
{0x97fb,0x97fb,0x15f2},
{0x97fd,0x97fe,0x3273},
{0x97ff,0x97ff,0x16a4},
{0x9800,0x9800,0x344e},
{0x9801,0x9801,0x82b},
{0x9802,0x9803,0xbdf},
{0x9804,0x9804,0x20ab},
{0x9805,0x9806,0xdb0},
{0x9807,0x9807,0x235c},
{0x9808,0x9808,0xdb2},
{0x980a,0x980a,0xf6e},
{0x980c,0x980c,0xf70},
{0x980d,0x980e,0x2602},
{0x980f,0x980f,0x2601},
{0x9810,0x9811,0xf6b},
{0x9812,0x9812,0xf6f},
{0x9813,0x9813,0xf6d},
{0x9814,0x9814,0x4952},
{0x9815,0x9815,0x433c},
{0x9816,0x9816,0x2862},
{0x9817,0x9818,0x10ee},
{0x981b,0x981b,0x2ac8},
{0x981c,0x981c,0x126c},
{0x981d,0x981d,0x2ac3},
{0x981e,0x981e,0x2ac2},
{0x981f,0x981f,0x3c02},
{0x9820,0x9820,0x2ac7},
{0x9821,0x9821,0x126a},
{0x9823,0x9823,0x3e37},
{0x9824,0x9824,0x139a},
{0x9826,0x9826,0x2ac4},
{0x9827,0x9827,0x2ac9},
{0x9828,0x9828,0x2ac6},
{0x9829,0x9829,0x2ac5},
{0x982b,0x982b,0x126b},
{0x982d,0x982d,0x1398},
{0x982e,0x982e,0x3f72},
{0x982f,0x982f,0x2cfd},
{0x9830,0x9830,0x1394},
{0x9832,0x9832,0x2cfe},
{0x9833,0x9833,0x38e5},
{0x9834,0x9834,0x38e4},
{0x9835,0x9835,0x2cfc},
{0x9837,0x9837,0x1397},
{0x9838,0x9838,0x1395},
{0x9839,0x9839,0x1399},
{0x983b,0x983b,0x1396},
{0x9841,0x9841,0x2ebe},
{0x9843,0x9843,0x2ec3},
{0x9844,0x9844,0x2ebf},
{0x9845,0x9845,0x2ec2},
{0x9846,0x9846,0x14b6},
{0x9847,0x9847,0x3d96},
{0x9848,0x9848,0x2df2},
{0x9849,0x9849,0x2ec1},
{0x984a,0x984a,0x2ec0},
{0x984b,0x984b,0x38e6},
{0x984c,0x984c,0x155a},
{0x984d,0x984d,0x1558},
{0x984e,0x984e,0x155b},
{0x984f,0x984f,0x1559},
{0x9850,0x9852,0x3037},
{0x9853,0x9853,0x155c},
{0x9857,0x9857,0x3174},
{0x9858,0x9858,0x15f4},
{0x9859,0x9859,0x3172},
{0x985b,0x985b,0x15f5},
{0x985c,0x985c,0x3171},
{0x985d,0x985d,0x3173},
{0x985e,0x985e,0x15f3},
{0x985f,0x985f,0x3278},
{0x9860,0x9860,0x3275},
{0x9862,0x9863,0x3276},
{0x9864,0x9864,0x3335},
{0x9865,0x9865,0x16a6},
{0x9866,0x9866,0x38e7},
{0x9867,0x9867,0x16a5},
{0x9869,0x9869,0x33d4},
{0x986a,0x986a,0x33d3},
{0x986b,0x986b,0x16e6},
{0x986c,0x986c,0x4035},
{0x986f,0x986f,0x1711},
{0x9870,0x9870,0x1736},
{0x9871,0x9871,0x174d},
{0x9872,0x9872,0x34f2},
{0x9873,0x9874,0x3527},
{0x9875,0x9875,0x450d},
{0x98a8,0x98a8,0x82c},
{0x98a9,0x98a9,0x235d},
{0x98ac,0x98ac,0x2604},
{0x98ad,0x98ae,0x2863},
{0x98af,0x98af,0x10f0},
{0x98b1,0x98b1,0x10f1},
{0x98b2,0x98b2,0x2aca},
{0x98b3,0x98b3,0x126d},
{0x98b4,0x98b4,0x3f73},
{0x98b6,0x98b6,0x14b7},
{0x98b7,0x98b7,0x38ef},
{0x98b8,0x98b8,0x303a},
{0x98b9,0x98b9,0x47b8},
{0x98ba,0x98ba,0x155d},
{0x98bb,0x98bb,0x3177},
{0x98bc,0x98bc,0x15f6},
{0x98bd,0x98bd,0x3176},
{0x98be,0x98be,0x3178},
{0x98bf,0x98bf,0x3175},
{0x98c0,0x98c0,0x3338},
{0x98c1,0x98c2,0x3279},
{0x98c3,0x98c3,0x3b78},
{0x98c4,0x98c4,0x1657},
{0x98c6,0x98c6,0x3337},
{0x98c7,0x98c7,0x38f1},
{0x98c8,0x98c8,0x38f0},
{0x98c9,0x98c9,0x3336},
{0x98ca,0x98ca,0x38ee},
{0x98cb,0x98cb,0x33d5},
{0x98cc,0x98cc,0x3529},
{0x98ce,0x98ce,0x450e},
{0x98db,0x98db,0x82d},
{0x98dc,0x98dc,0x3c0b},
{0x98de,0x98de,0x450f},
{0x98df,0x98df,0x82e},
{0x98e0,0x98e0,0x4792},
{0x98e1,0x98e1,0x38f6},
{0x98e2,0x98e2,0x9f4},
{0x98e3,0x98e3,0x1e32},
{0x98e5,0x98e5,0x20ac},
{0x98e6,0x98e6,0x38f7},
{0x98e7,0x98e7,0xdb3},
{0x98e9,0x98e9,0xdb6},
{0x98ea,0x98ea,0xdb4},
{0x98eb,0x98eb,0x235e},
{0x98ec,0x98ec,0x38f8},
{0x98ed,0x98ed,0xdb8},
{0x98ef,0x98ef,0xdb5},
{0x98f1,0x98f1,0x47ba},
{0x98f2,0x98f2,0xdb7},
{0x98f4,0x98f4,0xf72},
{0x98f5,0x98f5,0x4365},
{0x98f6,0x98f6,0x2605},
{0x98f9,0x98f9,0x2606},
{0x98fa,0x98fa,0x2acc},
{0x98fc,0x98fc,0xf71},
{0x98fd,0x98fe,0xf73},
{0x9900,0x9900,0x2866},
{0x9902,0x9902,0x2865},
{0x9903,0x9903,0x10f2},
{0x9905,0x9905,0x10f3},
{0x9907,0x9907,0x2867},
{0x9908,0x9908,0x2acb},
{0x9909,0x9909,0x10f5},
{0x990a,0x990a,0x126e},
{0x990c,0x990c,0x10f4},
{0x990e,0x990e,0x43ed},
{0x9910,0x9910,0x139b},
{0x9911,0x9911,0x2acd},
{0x9912,0x9912,0x1270},
{0x9913,0x9913,0x126f},
{0x9914,0x9914,0x2ace},
{0x9915,0x9915,0x2ad1},
{0x9916,0x9917,0x2acf},
{0x9918,0x9918,0x1271},
{0x9919,0x9919,0x47bc},
{0x991a,0x991a,0x13a0},
{0x991b,0x991b,0x139e},
{0x991c,0x991c,0x43ee},
{0x991e,0x991e,0x139d},
{0x991f,0x991f,0x2d00},
{0x9921,0x9921,0x139f},
{0x9924,0x9924,0x2cff},
{0x9925,0x9925,0x2ec4},
{0x9927,0x9927,0x2d01},
{0x9928,0x9928,0x139c},
{0x9929,0x9929,0x2d02},
{0x992a,0x992a,0x2ec7},
{0x992b,0x992c,0x2ec5},
{0x992d,0x992d,0x2ecb},
{0x992e,0x992e,0x1561},
{0x992f,0x992f,0x2eca},
{0x9930,0x9930,0x2ecd},
{0x9931,0x9931,0x2ecc},
{0x9932,0x9932,0x2ec9},
{0x9933,0x9933,0x2ec8},
{0x9935,0x9935,0x14b8},
{0x9937,0x9937,0x47bd},
{0x9938,0x9938,0x3bfd},
{0x9939,0x9939,0x38fa},
{0x993a,0x993a,0x303d},
{0x993b,0x993b,0x3c11},
{0x993c,0x993c,0x303c},
{0x993d,0x993d,0x1560},
{0x993e,0x993f,0x155e},
{0x9940,0x9940,0x3f34},
{0x9941,0x9941,0x303b},
{0x9942,0x9942,0x43ff},
{0x9943,0x9943,0x317b},
{0x9945,0x9945,0x15f7},
{0x9947,0x9947,0x317a},
{0x9948,0x9948,0x3179},
{0x9949,0x9949,0x15f8},
{0x994a,0x994a,0x3fb9},
{0x994b,0x994b,0x327f},
{0x994c,0x994c,0x327e},
{0x994d,0x994d,0x3c12},
{0x994e,0x994e,0x327c},
{0x9950,0x9950,0x327b},
{0x9951,0x9951,0x1659},
{0x9952,0x9952,0x1658},
{0x9953,0x9953,0x3280},
{0x9954,0x9954,0x33d6},
{0x9955,0x9955,0x16e7},
{0x9956,0x9956,0x333a},
{0x9957,0x9957,0x16a7},
{0x9958,0x9958,0x3339},
{0x9959,0x9959,0x327d},
{0x995b,0x995b,0x33d7},
{0x995c,0x995c,0x1712},
{0x995d,0x995d,0x47be},
{0x995e,0x995e,0x174e},
{0x995f,0x995f,0x34f3},
{0x9961,0x9961,0x352a},
{0x9963,0x9963,0x4510},
{0x9996,0x9996,0x82f},
{0x9997,0x9997,0x20ad},
{0x9998,0x9998,0x2ece},
{0x9999,0x9999,0x830},
{0x999c,0x999c,0x2869},
{0x999d,0x999d,0x2868},
{0x999e,0x999e,0x2d03},
{0x99a1,0x99a1,0x2ed0},
{0x99a3,0x99a3,0x2ecf},
{0x99a4,0x99a4,0x41c3},
{0x99a5,0x99a5,0x1562},
{0x99a6,0x99a7,0x317c},
{0x99a8,0x99a8,0x165a},
{0x99aa,0x99aa,0x3c17},
{0x99ab,0x99ab,0x352b},
{0x99ac,0x99ac,0x9f5},
{0x99ad,0x99ad,0xdba},
{0x99ae,0x99ae,0xdb9},
{0x99af,0x99af,0x2607},
{0x99b0,0x99b0,0x2609},
{0x99b1,0x99b1,0xf76},
{0x99b2,0x99b2,0x2608},
{0x99b3,0x99b3,0xf75},
{0x99b4,0x99b4,0xf77},
{0x99b5,0x99b5,0x260a},
{0x99b8,0x99b8,0x394b},
{0x99b9,0x99b9,0x286b},
{0x99ba,0x99ba,0x286d},
{0x99bb,0x99bb,0x286c},
{0x99bc,0x99bc,0x3c22},
{0x99bd,0x99bd,0x286f},
{0x99c1,0x99c1,0x10f6},
{0x99c2,0x99c2,0x286e},
{0x99c3,0x99c3,0x286a},
{0x99c4,0x99c4,0x3d77},
{0x99c5,0x99c5,0x47c1},
{0x99c7,0x99c7,0x2870},
{0x99c9,0x99c9,0x2ad8},
{0x99cb,0x99cb,0x2adb},
{0x99cc,0x99cc,0x2add},
{0x99cd,0x99cd,0x2ad3},
{0x99ce,0x99ce,0x2ad7},
{0x99cf,0x99cf,0x2ad4},
{0x99d0,0x99d0,0x1273},
{0x99d1,0x99d1,0x1276},
{0x99d2,0x99d2,0x1278},
{0x99d3,0x99d4,0x2ad5},
{0x99d5,0x99d5,0x1277},
{0x99d6,0x99d6,0x2ad9},
{0x99d7,0x99d7,0x2adc},
{0x99d8,0x99d8,0x2ada},
{0x99d9,0x99d9,0x1279},
{0x99da,0x99da,0x3f42},
{0x99db,0x99db,0x1275},
{0x99dc,0x99dc,0x2ad2},
{0x99dd,0x99dd,0x1272},
{0x99df,0x99df,0x1274},
{0x99e1,0x99e1,0x3785},
{0x99e2,0x99e2,0x13a2},
{0x99e3,0x99e3,0x2d09},
{0x99e4,0x99e4,0x2d07},
{0x99e5,0x99e5,0x2d06},
{0x99e6,0x99e6,0x3b65},
{0x99e7,0x99e7,0x2d0c},
{0x99e9,0x99e9,0x2d0b},
{0x99ea,0x99ea,0x2d0a},
{0x99ec,0x99ec,0x2d05},
{0x99ed,0x99ed,0x13a1},
{0x99ee,0x99ee,0x2d04},
{0x99f0,0x99f0,0x2d08},
{0x99f1,0x99f1,0x13a3},
{0x99f4,0x99f4,0x2ed3},
{0x99f5,0x99f5,0x38ff},
{0x99f6,0x99f6,0x2ed7},
{0x99f7,0x99f7,0x2ed4},
{0x99f8,0x99f8,0x2ed6},
{0x99f9,0x99f9,0x2ed5},
{0x99fa,0x99fa,0x2ed2},
{0x99fb,0x99fb,0x2ed8},
{0x99fc,0x99fc,0x2edb},
{0x99fd,0x99fe,0x2ed9},
{0x99ff,0x99ff,0x14ba},
{0x9a01,0x9a01,0x14b9},
{0x9a02,0x9a02,0x2ed1},
{0x9a03,0x9a03,0x2edc},
{0x9a04,0x9a04,0x3042},
{0x9a05,0x9a05,0x3045},
{0x9a06,0x9a06,0x3047},
{0x9a07,0x9a07,0x3046},
{0x9a09,0x9a09,0x3040},
{0x9a0a,0x9a0a,0x3044},
{0x9a0b,0x9a0b,0x303f},
{0x9a0c,0x9a0c,0x3900},
{0x9a0d,0x9a0d,0x3041},
{0x9a0e,0x9a0e,0x1563},
{0x9a0f,0x9a0f,0x303e},
{0x9a10,0x9a10,0x3902},
{0x9a11,0x9a11,0x3043},
{0x9a14,0x9a14,0x318a},
{0x9a15,0x9a15,0x317f},
{0x9a16,0x9a16,0x15f9},
{0x9a19,0x9a19,0x15fa},
{0x9a1a,0x9a1a,0x317e},
{0x9a1b,0x9a1b,0x3183},
{0x9a1c,0x9a1c,0x3189},
{0x9a1d,0x9a1d,0x3181},
{0x9a1e,0x9a1e,0x3188},
{0x9a1f,0x9a1f,0x3b6c},
{0x9a20,0x9a20,0x3185},
{0x9a21,0x9a21,0x3c1c},
{0x9a22,0x9a22,0x3184},
{0x9a23,0x9a23,0x3187},
{0x9a24,0x9a24,0x3182},
{0x9a25,0x9a25,0x3180},
{0x9a26,0x9a26,0x3df2},
{0x9a27,0x9a27,0x3186},
{0x9a29,0x9a29,0x3287},
{0x9a2a,0x9a2a,0x3285},
{0x9a2b,0x9a2b,0x165b},
{0x9a2c,0x9a2c,0x3284},
{0x9a2d,0x9a2d,0x328a},
{0x9a2e,0x9a2e,0x3288},
{0x9a2f,0x9a2f,0x3c1e},
{0x9a30,0x9a30,0x165c},
{0x9a31,0x9a31,0x3283},
{0x9a32,0x9a32,0x3281},
{0x9a34,0x9a34,0x3282},
{0x9a35,0x9a35,0x165e},
{0x9a36,0x9a36,0x3286},
{0x9a37,0x9a37,0x165d},
{0x9a38,0x9a38,0x3289},
{0x9a39,0x9a39,0x333b},
{0x9a3a,0x9a3a,0x3341},
{0x9a3b,0x9a3b,0x3901},
{0x9a3c,0x9a3c,0x47c3},
{0x9a3d,0x9a3d,0x333c},
{0x9a3e,0x9a3e,0x16ab},
{0x9a3f,0x9a3f,0x3342},
{0x9a40,0x9a40,0x16aa},
{0x9a41,0x9a41,0x3340},
{0x9a42,0x9a42,0x333f},
{0x9a43,0x9a43,0x16a9},
{0x9a44,0x9a44,0x333e},
{0x9a45,0x9a45,0x16a8},
{0x9a46,0x9a46,0x333d},
{0x9a48,0x9a48,0x33dd},
{0x9a49,0x9a49,0x33df},
{0x9a4a,0x9a4a,0x33de},
{0x9a4c,0x9a4c,0x33db},
{0x9a4d,0x9a4d,0x16e9},
{0x9a4e,0x9a4e,0x33d8},
{0x9a4f,0x9a4f,0x33dc},
{0x9a50,0x9a50,0x33e1},
{0x9a52,0x9a52,0x33e0},
{0x9a53,0x9a54,0x33d9},
{0x9a55,0x9a55,0x16e8},
{0x9a56,0x9a56,0x344f},
{0x9a57,0x9a57,0x1715},
{0x9a58,0x9a58,0x3903},
{0x9a59,0x9a59,0x3450},
{0x9a5a,0x9a5b,0x1713},
{0x9a5c,0x9a5c,0x3c18},
{0x9a5e,0x9a5e,0x34ae},
{0x9a5f,0x9a5f,0x1737},
{0x9a60,0x9a60,0x3510},
{0x9a62,0x9a62,0x1757},
{0x9a63,0x9a63,0x3b67},
{0x9a64,0x9a64,0x352c},
{0x9a65,0x9a65,0x1758},
{0x9a66,0x9a67,0x352d},
{0x9a68,0x9a68,0x353c},
{0x9a69,0x9a69,0x353b},
{0x9a6a,0x9a6a,0x1767},
{0x9a6b,0x9a6b,0x3544},
{0x9a6c,0x9a6c,0x4585},
{0x9a8f,0x9a8f,0x4586},
{0x9aa8,0x9aa8,0x9f6},
{0x9aab,0x9aab,0x260c},
{0x9aad,0x9aad,0x260b},
{0x9aaf,0x9ab0,0x10f7},
{0x9ab1,0x9ab1,0x2871},
{0x9ab2,0x9ab2,0x4332},
{0x9ab3,0x9ab3,0x2ade},
{0x9ab4,0x9ab4,0x2d0f},
{0x9ab7,0x9ab7,0x127a},
{0x9ab8,0x9ab8,0x13a4},
{0x9ab9,0x9ab9,0x2d0d},
{0x9aba,0x9aba,0x3f74},
{0x9abb,0x9abb,0x2d10},
{0x9abc,0x9abc,0x13a5},
{0x9abd,0x9abd,0x3d97},
{0x9abe,0x9abe,0x2edd},
{0x9abf,0x9abf,0x2d0e},
{0x9ac0,0x9ac0,0x3048},
{0x9ac1,0x9ac1,0x1564},
{0x9ac2,0x9ac2,0x318b},
{0x9ac6,0x9ac6,0x328d},
{0x9ac7,0x9ac7,0x328b},
{0x9aca,0x9aca,0x328c},
{0x9acd,0x9acd,0x3343},
{0x9acf,0x9acf,0x16ac},
{0x9ad0,0x9ad0,0x33e2},
{0x9ad1,0x9ad1,0x1718},
{0x9ad2,0x9ad2,0x16ea},
{0x9ad3,0x9ad4,0x1716},
{0x9ad5,0x9ad5,0x34af},
{0x9ad6,0x9ad6,0x174f},
{0x9ad7,0x9ad7,0x3faa},
{0x9ad8,0x9ad8,0x9f7},
{0x9adc,0x9adc,0x3049},
{0x9adf,0x9adf,0x1e33},
{0x9ae0,0x9ae0,0x3908},
{0x9ae1,0x9ae1,0xf78},
{0x9ae2,0x9ae2,0x3909},
{0x9ae3,0x9ae3,0x2872},
{0x9ae6,0x9ae6,0x10f9},
{0x9ae7,0x9ae7,0x2873},
{0x9aeb,0x9aeb,0x2ae0},
{0x9aec,0x9aec,0x2adf},
{0x9aed,0x9aed,0x13a7},
{0x9aee,0x9aef,0x127b},
{0x9af1,0x9af1,0x2ae3},
{0x9af2,0x9af2,0x2ae2},
{0x9af3,0x9af3,0x2ae1},
{0x9af4,0x9af4,0x390b},
{0x9af6,0x9af6,0x2d11},
{0x9af7,0x9af7,0x2d14},
{0x9af9,0x9af9,0x2d13},
{0x9afa,0x9afa,0x2d12},
{0x9afb,0x9afb,0x13a6},
{0x9afc,0x9afc,0x2ee1},
{0x9afd,0x9afd,0x2edf},
{0x9afe,0x9afe,0x2ede},
{0x9aff,0x9aff,0x3f17},
{0x9b01,0x9b01,0x2ee0},
{0x9b02,0x9b02,0x3f12},
{0x9b03,0x9b03,0x1565},
{0x9b04,0x9b05,0x304b},
{0x9b06,0x9b06,0x1566},
{0x9b08,0x9b08,0x304a},
{0x9b09,0x9b09,0x3f20},
{0x9b0a,0x9b0a,0x318d},
{0x9b0b,0x9b0b,0x318c},
{0x9b0c,0x9b0c,0x318f},
{0x9b0d,0x9b0d,0x15fb},
{0x9b0e,0x9b0e,0x318e},
{0x9b0f,0x9b0f,0x47c4},
{0x9b10,0x9b10,0x328e},
{0x9b11,0x9b11,0x3290},
{0x9b12,0x9b12,0x328f},
{0x9b14,0x9b14,0x390d},
{0x9b15,0x9b15,0x3344},
{0x9b16,0x9b16,0x3347},
{0x9b17,0x9b18,0x3345},
{0x9b19,0x9b19,0x33e3},
{0x9b1a,0x9b1a,0x16eb},
{0x9b1e,0x9b20,0x3451},
{0x9b22,0x9b22,0x1738},
{0x9b23,0x9b23,0x1750},
{0x9b24,0x9b24,0x352f},
{0x9b25,0x9b25,0x9f8},
{0x9b27,0x9b27,0x127d},
{0x9b28,0x9b28,0x13a8},
{0x9b29,0x9b29,0x304d},
{0x9b2a,0x9b2a,0x3f19},
{0x9b2b,0x9b2b,0x33e4},
{0x9b2d,0x9b2d,0x390e},
{0x9b2e,0x9b2e,0x3511},
{0x9b2f,0x9b2f,0x1e34},
{0x9b31,0x9b31,0x1768},
{0x9b32,0x9b32,0x9f9},
{0x9b33,0x9b33,0x2d15},
{0x9b34,0x9b34,0x3911},
{0x9b35,0x9b35,0x304e},
{0x9b37,0x9b37,0x3190},
{0x9b39,0x9b39,0x3f00},
{0x9b3a,0x9b3a,0x3348},
{0x9b3b,0x9b3b,0x33e5},
{0x9b3c,0x9b3c,0x9fa},
{0x9b3e,0x9b3f,0x2874},
{0x9b40,0x9b40,0x3915},
{0x9b41,0x9b42,0x10fa},
{0x9b43,0x9b43,0x2ae5},
{0x9b44,0x9b44,0x127f},
{0x9b45,0x9b45,0x127e},
{0x9b46,0x9b46,0x2ae4},
{0x9b48,0x9b48,0x2ee2},
{0x9b4a,0x9b4a,0x304f},
{0x9b4b,0x9b4b,0x3051},
{0x9b4c,0x9b4c,0x3050},
{0x9b4d,0x9b4d,0x1569},
{0x9b4e,0x9b4e,0x1568},
{0x9b4f,0x9b4f,0x1567},
{0x9b50,0x9b50,0x3914},
{0x9b51,0x9b51,0x16ae},
{0x9b52,0x9b52,0x3349},
{0x9b54,0x9b54,0x16ad},
{0x9b55,0x9b55,0x33e7},
{0x9b56,0x9b56,0x33e6},
{0x9b58,0x9b58,0x1739},
{0x9b59,0x9b59,0x34b0},
{0x9b5a,0x9b5a,0xbe1},
{0x9b5b,0x9b5b,0x260d},
{0x9b5f,0x9b5f,0x2878},
{0x9b60,0x9b61,0x2876},
{0x9b64,0x9b64,0x2aee},
{0x9b66,0x9b66,0x2ae9},
{0x9b67,0x9b67,0x2ae6},
{0x9b68,0x9b68,0x2aed},
{0x9b69,0x9b69,0x47c5},
{0x9b6c,0x9b6c,0x2aef},
{0x9b6f,0x9b6f,0x1281},
{0x9b70,0x9b70,0x2aec},
{0x9b71,0x9b71,0x2ae8},
{0x9b74,0x9b74,0x2ae7},
{0x9b75,0x9b75,0x2aeb},
{0x9b76,0x9b76,0x2aea},
{0x9b77,0x9b77,0x1280},
{0x9b7a,0x9b7a,0x2d20},
{0x9b7b,0x9b7b,0x2d1b},
{0x9b7c,0x9b7c,0x2d19},
{0x9b7d,0x9b7d,0x2d22},
{0x9b7e,0x9b7e,0x2d1a},
{0x9b7f,0x9b7f,0x3c3b},
{0x9b80,0x9b80,0x2d16},
{0x9b81,0x9b81,0x43f1},
{0x9b82,0x9b82,0x2d1c},
{0x9b83,0x9b83,0x4219},
{0x9b85,0x9b85,0x2d17},
{0x9b86,0x9b86,0x2eeb},
{0x9b87,0x9b87,0x2d18},
{0x9b88,0x9b88,0x2d23},
{0x9b8b,0x9b8b,0x3eee},
{0x9b8d,0x9b8d,0x4623},
{0x9b8e,0x9b8e,0x3919},
{0x9b8f,0x9b8f,0x3fad},
{0x9b90,0x9b90,0x2d1f},
{0x9b91,0x9b91,0x13a9},
{0x9b92,0x9b92,0x2d1e},
{0x9b93,0x9b93,0x2d1d},
{0x9b95,0x9b95,0x2d21},
{0x9b97,0x9b97,0x3f71},
{0x9b9a,0x9b9a,0x2ee3},
{0x9b9b,0x9b9b,0x2ee6},
{0x9b9d,0x9b9d,0x3f56},
{0x9b9e,0x9b9e,0x2ee5},
{0x9b9f,0x9b9f,0x3c3e},
{0x9ba0,0x9ba0,0x2eed},
{0x9ba1,0x9ba1,0x2ee8},
{0x9ba2,0x9ba2,0x2eec},
{0x9ba4,0x9ba4,0x2eea},
{0x9ba5,0x9ba5,0x2ee9},
{0x9ba6,0x9ba6,0x2ee7},
{0x9ba8,0x9ba8,0x2ee4},
{0x9baa,0x9baa,0x14bd},
{0x9bab,0x9bab,0x14bc},
{0x9bad,0x9bad,0x14be},
{0x9bae,0x9bae,0x14bb},
{0x9baf,0x9baf,0x2eee},
{0x9bb0,0x9bb0,0x3fb4},
{0x9bb5,0x9bb5,0x3057},
{0x9bb6,0x9bb6,0x305a},
{0x9bb8,0x9bb8,0x3058},
{0x9bb9,0x9bb9,0x305c},
{0x9bbd,0x9bbd,0x305d},
{0x9bbf,0x9bbf,0x3055},
{0x9bc0,0x9bc0,0x156e},
{0x9bc1,0x9bc1,0x3056},
{0x9bc3,0x9bc3,0x3054},
{0x9bc4,0x9bc4,0x305b},
{0x9bc6,0x9bc6,0x3053},
{0x9bc7,0x9bc7,0x3052},
{0x9bc8,0x9bc8,0x156d},
{0x9bc9,0x9bc9,0x156b},
{0x9bca,0x9bca,0x156a},
{0x9bcf,0x9bcf,0x3c3c},
{0x9bd3,0x9bd3,0x3059},
{0x9bd4,0x9bd4,0x3199},
{0x9bd5,0x9bd5,0x319f},
{0x9bd6,0x9bd6,0x15fe},
{0x9bd7,0x9bd7,0x319a},
{0x9bd9,0x9bd9,0x319d},
{0x9bda,0x9bda,0x31a1},
{0x9bdb,0x9bdb,0x15ff},
{0x9bdc,0x9bdc,0x319c},
{0x9bdd,0x9bdd,0x47c6},
{0x9bde,0x9bde,0x3194},
{0x9be0,0x9be0,0x3193},
{0x9be1,0x9be1,0x31a0},
{0x9be2,0x9be2,0x3197},
{0x9be4,0x9be4,0x3195},
{0x9be5,0x9be5,0x319e},
{0x9be6,0x9be6,0x3196},
{0x9be7,0x9be7,0x15fd},
{0x9be8,0x9be8,0x15fc},
{0x9be9,0x9be9,0x3bc1},
{0x9bea,0x9beb,0x3191},
{0x9bec,0x9bec,0x319b},
{0x9bed,0x9bed,0x3ed7},
{0x9bf0,0x9bf0,0x3198},
{0x9bf1,0x9bf1,0x47c7},
{0x9bf4,0x9bf4,0x47c8},
{0x9bf7,0x9bf7,0x3293},
{0x9bf8,0x9bf8,0x3296},
{0x9bfd,0x9bfd,0x156c},
{0x9bff,0x9bff,0x391b},
{0x9c02,0x9c02,0x391a},
{0x9c05,0x9c05,0x3294},
{0x9c06,0x9c06,0x329a},
{0x9c07,0x9c07,0x3298},
{0x9c08,0x9c08,0x3292},
{0x9c09,0x9c09,0x329d},
{0x9c0a,0x9c0a,0x3f3a},
{0x9c0b,0x9c0b,0x3291},
{0x9c0c,0x9c0c,0x391c},
{0x9c0d,0x9c0d,0x1660},
{0x9c0e,0x9c0e,0x3299},
{0x9c10,0x9c10,0x3c3a},
{0x9c12,0x9c12,0x3295},
{0x9c13,0x9c13,0x165f},
{0x9c14,0x9c14,0x329c},
{0x9c15,0x9c15,0x3f1e},
{0x9c17,0x9c17,0x329b},
{0x9c1b,0x9c1b,0x491b},
{0x9c1c,0x9c1c,0x334c},
{0x9c1d,0x9c1d,0x334b},
{0x9c1f,0x9c1f,0x4622},
{0x9c20,0x9c20,0x47ca},
{0x9c21,0x9c21,0x3352},
{0x9c23,0x9c23,0x334e},
{0x9c24,0x9c24,0x3351},
{0x9c25,0x9c25,0x16b0},
{0x9c26,0x9c26,0x45e6},
{0x9c28,0x9c29,0x334f},
{0x9c2b,0x9c2b,0x334a},
{0x9c2c,0x9c2c,0x334d},
{0x9c2d,0x9c2d,0x16af},
{0x9c2e,0x9c2e,0x3f22},
{0x9c2f,0x9c2f,0x3d87},
{0x9c31,0x9c31,0x16ed},
{0x9c32,0x9c32,0x33f2},
{0x9c33,0x9c33,0x33ed},
{0x9c34,0x9c34,0x33f1},
{0x9c35,0x9c35,0x3c39},
{0x9c36,0x9c36,0x33f4},
{0x9c37,0x9c37,0x33f0},
{0x9c39,0x9c39,0x33ec},
{0x9c3a,0x9c3a,0x3d7e},
{0x9c3b,0x9c3b,0x16ef},
{0x9c3c,0x9c3c,0x33ef},
{0x9c3d,0x9c3d,0x33f3},
{0x9c3e,0x9c3e,0x16ee},
{0x9c3f,0x9c3f,0x33ea},
{0x9c40,0x9c40,0x3297},
{0x9c41,0x9c41,0x33ee},
{0x9c44,0x9c44,0x33eb},
{0x9c45,0x9c45,0x3e71},
{0x9c46,0x9c46,0x33e8},
{0x9c48,0x9c48,0x33e9},
{0x9c49,0x9c49,0x16ec},
{0x9c4a,0x9c4a,0x3457},
{0x9c4b,0x9c4b,0x3459},
{0x9c4c,0x9c4c,0x345c},
{0x9c4d,0x9c4d,0x3458},
{0x9c4e,0x9c4e,0x345d},
{0x9c4f,0x9c4f,0x3c36},
{0x9c50,0x9c50,0x3456},
{0x9c52,0x9c52,0x3454},
{0x9c53,0x9c53,0x3c37},
{0x9c54,0x9c54,0x1719},
{0x9c55,0x9c55,0x345a},
{0x9c56,0x9c56,0x171b},
{0x9c57,0x9c57,0x171a},
{0x9c58,0x9c58,0x3455},
{0x9c59,0x9c59,0x345b},
{0x9c5d,0x9c5d,0x3ebf},
{0x9c5e,0x9c5e,0x34b5},
{0x9c5f,0x9c5f,0x173a},
{0x9c60,0x9c60,0x34b6},
{0x9c62,0x9c62,0x34b4},
{0x9c63,0x9c63,0x34b1},
{0x9c66,0x9c66,0x34b3},
{0x9c67,0x9c67,0x34b2},
{0x9c68,0x9c68,0x34f4},
{0x9c6d,0x9c6d,0x34f6},
{0x9c6e,0x9c6e,0x34f5},
{0x9c71,0x9c71,0x3514},
{0x9c72,0x9c72,0x3ea1},
{0x9c73,0x9c73,0x3513},
{0x9c74,0x9c74,0x3512},
{0x9c75,0x9c75,0x3515},
{0x9c77,0x9c78,0x1760},
{0x9c79,0x9c79,0x3541},
{0x9c7a,0x9c7a,0x3545},
{0x9c7b,0x9c7b,0x3c38},
{0x9c7c,0x9c7c,0x4512},
{0x9ce5,0x9ce5,0xbe2},
{0x9ce6,0x9ce6,0x235f},
{0x9ce7,0x9ce7,0x2610},
{0x9ce9,0x9ce9,0xf79},
{0x9cea,0x9cea,0x260e},
{0x9ced,0x9ced,0x260f},
{0x9cf1,0x9cf2,0x2879},
{0x9cf3,0x9cf3,0x10fe},
{0x9cf4,0x9cf4,0x10fc},
{0x9cf5,0x9cf5,0x287b},
{0x9cf6,0x9cf6,0x10fd},
{0x9cf7,0x9cf7,0x2af4},
{0x9cf9,0x9cf9,0x2af7},
{0x9cfa,0x9cfa,0x2af1},
{0x9cfb,0x9cfb,0x2af8},
{0x9cfc,0x9cfc,0x2af0},
{0x9cfd,0x9cfd,0x2af2},
{0x9cff,0x9cff,0x2af3},
{0x9d00,0x9d00,0x2af6},
{0x9d02,0x9d02,0x3f3b},
{0x9d03,0x9d03,0x1284},
{0x9d04,0x9d04,0x2afb},
{0x9d05,0x9d05,0x2afa},
{0x9d06,0x9d06,0x1282},
{0x9d07,0x9d07,0x2af5},
{0x9d08,0x9d08,0x2af9},
{0x9d09,0x9d09,0x1283},
{0x9d0c,0x9d0c,0x3c46},
{0x9d10,0x9d10,0x2d2d},
{0x9d12,0x9d12,0x13ae},
{0x9d14,0x9d14,0x2d28},
{0x9d15,0x9d15,0x13aa},
{0x9d16,0x9d16,0x3c7c},
{0x9d17,0x9d17,0x2d25},
{0x9d18,0x9d18,0x2d2b},
{0x9d19,0x9d19,0x2d2e},
{0x9d1b,0x9d1b,0x13af},
{0x9d1d,0x9d1d,0x2d2a},
{0x9d1e,0x9d1e,0x2d27},
{0x9d1f,0x9d1f,0x2d2f},
{0x9d20,0x9d20,0x2d26},
{0x9d21,0x9d21,0x3c41},
{0x9d22,0x9d22,0x2d2c},
{0x9d23,0x9d23,0x13ab},
{0x9d25,0x9d25,0x2d24},
{0x9d26,0x9d26,0x13ac},
{0x9d28,0x9d28,0x13ad},
{0x9d29,0x9d29,0x2d29},
{0x9d2d,0x9d2d,0x2f00},
{0x9d2e,0x9d2f,0x2ef3},
{0x9d30,0x9d30,0x2ef7},
{0x9d31,0x9d31,0x2ef5},
{0x9d33,0x9d33,0x2eef},
{0x9d34,0x9d34,0x404a},
{0x9d36,0x9d36,0x2ef2},
{0x9d37,0x9d37,0x2efc},
{0x9d38,0x9d38,0x2ef6},
{0x9d39,0x9d39,0x392e},
{0x9d3b,0x9d3b,0x14bf},
{0x9d3d,0x9d3d,0x2efe},
{0x9d3e,0x9d3e,0x2efb},
{0x9d3f,0x9d3f,0x14c0},
{0x9d40,0x9d40,0x2efd},
{0x9d41,0x9d41,0x2ef0},
{0x9d42,0x9d43,0x2ef9},
{0x9d44,0x9d44,0x3fab},
{0x9d45,0x9d45,0x2ef8},
{0x9d49,0x9d49,0x47cd},
{0x9d4a,0x9d4a,0x3061},
{0x9d4b,0x9d4b,0x3063},
{0x9d4c,0x9d4c,0x3066},
{0x9d4e,0x9d4e,0x4539},
{0x9d4f,0x9d4f,0x3060},
{0x9d50,0x9d50,0x3eca},
{0x9d51,0x9d51,0x156f},
{0x9d52,0x9d52,0x3068},
{0x9d53,0x9d53,0x305f},
{0x9d54,0x9d54,0x3069},
{0x9d56,0x9d56,0x3065},
{0x9d57,0x9d57,0x3067},
{0x9d58,0x9d58,0x306b},
{0x9d59,0x9d59,0x3064},
{0x9d5a,0x9d5a,0x306c},
{0x9d5b,0x9d5b,0x3062},
{0x9d5c,0x9d5c,0x305e},
{0x9d5d,0x9d5d,0x1570},
{0x9d5e,0x9d5e,0x3e6e},
{0x9d5f,0x9d5f,0x306a},
{0x9d60,0x9d60,0x1571},
{0x9d61,0x9d61,0x1601},
{0x9d67,0x9d67,0x2ef1},
{0x9d68,0x9d68,0x31bb},
{0x9d69,0x9d69,0x31b2},
{0x9d6a,0x9d6a,0x1603},
{0x9d6b,0x9d6b,0x31ae},
{0x9d6c,0x9d6c,0x1604},
{0x9d6d,0x9d6d,0x3bac},
{0x9d6e,0x9d6e,0x433b},
{0x9d6f,0x9d6f,0x31b7},
{0x9d70,0x9d70,0x31b1},
{0x9d71,0x9d71,0x31a7},
{0x9d72,0x9d72,0x1602},
{0x9d73,0x9d73,0x31b4},
{0x9d74,0x9d75,0x31af},
{0x9d77,0x9d77,0x31a2},
{0x9d78,0x9d78,0x31a9},
{0x9d79,0x9d79,0x31b8},
{0x9d7b,0x9d7b,0x31b5},
{0x9d7c,0x9d7c,0x3efe},
{0x9d7d,0x9d7d,0x31ad},
{0x9d7e,0x9d7e,0x3925},
{0x9d7f,0x9d7f,0x31b9},
{0x9d80,0x9d80,0x31a8},
{0x9d81,0x9d81,0x31a3},
{0x9d82,0x9d82,0x31b6},
{0x9d83,0x9d83,0x3926},
{0x9d84,0x9d84,0x31a5},
{0x9d85,0x9d85,0x31b3},
{0x9d86,0x9d86,0x31aa},
{0x9d87,0x9d87,0x31ba},
{0x9d88,0x9d88,0x31a6},
{0x9d89,0x9d89,0x1600},
{0x9d8a,0x9d8a,0x31a4},
{0x9d8b,0x9d8c,0x31ab},
{0x9d90,0x9d90,0x32a4},
{0x9d92,0x9d92,0x32a2},
{0x9d93,0x9d93,0x43f3},
{0x9d94,0x9d94,0x32a7},
{0x9d96,0x9d96,0x32b3},
{0x9d97,0x9d97,0x32aa},
{0x9d98,0x9d98,0x32a3},
{0x9d99,0x9d99,0x329f},
{0x9d9a,0x9d9a,0x32ac},
{0x9d9b,0x9d9b,0x32a5},
{0x9d9c,0x9d9c,0x32a8},
{0x9d9d,0x9d9d,0x32a1},
{0x9d9e,0x9d9e,0x32af},
{0x9d9f,0x9d9f,0x329e},
{0x9da0,0x9da0,0x32a6},
{0x9da1,0x9da1,0x32ab},
{0x9da2,0x9da2,0x32ad},
{0x9da3,0x9da3,0x32b0},
{0x9da4,0x9da4,0x32a0},
{0x9da5,0x9da5,0x3c4b},
{0x9da6,0x9da7,0x32b4},
{0x9da8,0x9da8,0x32ae},
{0x9da9,0x9da9,0x32b2},
{0x9daa,0x9daa,0x32a9},
{0x9dab,0x9dab,0x3f30},
{0x9dac,0x9dac,0x3362},
{0x9dad,0x9dad,0x3365},
{0x9daf,0x9daf,0x16b1},
{0x9db1,0x9db1,0x3364},
{0x9db2,0x9db2,0x3369},
{0x9db3,0x9db3,0x3367},
{0x9db4,0x9db4,0x16b2},
{0x9db5,0x9db5,0x335e},
{0x9db6,0x9db6,0x3354},
{0x9db7,0x9db7,0x3353},
{0x9db8,0x9db8,0x16b4},
{0x9db9,0x9dba,0x3360},
{0x9dbb,0x9dbb,0x335d},
{0x9dbc,0x9dbc,0x3355},
{0x9dbd,0x9dbd,0x47d0},
{0x9dbe,0x9dbe,0x335a},
{0x9dbf,0x9dbf,0x32b1},
{0x9dc0,0x9dc0,0x43f2},
{0x9dc1,0x9dc1,0x3356},
{0x9dc2,0x9dc2,0x16b3},
{0x9dc3,0x9dc3,0x335c},
{0x9dc4,0x9dc4,0x3929},
{0x9dc5,0x9dc5,0x335b},
{0x9dc7,0x9dc7,0x3357},
{0x9dc8,0x9dc8,0x3363},
{0x9dc9,0x9dc9,0x4579},
{0x9dca,0x9dca,0x3358},
{0x9dcb,0x9dcb,0x33f9},
{0x9dcc,0x9dcc,0x3366},
{0x9dcd,0x9dcd,0x3368},
{0x9dce,0x9dce,0x335f},
{0x9dcf,0x9dcf,0x3359},
{0x9dd0,0x9dd0,0x33fa},
{0x9dd1,0x9dd1,0x33fc},
{0x9dd2,0x9dd2,0x33f6},
{0x9dd3,0x9dd3,0x16f0},
{0x9dd4,0x9dd4,0x391e},
{0x9dd5,0x9dd5,0x3403},
{0x9dd6,0x9dd6,0x3401},
{0x9dd7,0x9dd7,0x16f1},
{0x9dd8,0x9dd8,0x3400},
{0x9dd9,0x9dd9,0x33ff},
{0x9dda,0x9dda,0x33f8},
{0x9ddb,0x9ddb,0x33f5},
{0x9ddc,0x9ddc,0x33fb},
{0x9ddd,0x9ddd,0x3404},
{0x9dde,0x9dde,0x33f7},
{0x9ddf,0x9ddf,0x33fd},
{0x9de1,0x9de1,0x3466},
{0x9de2,0x9de2,0x346b},
{0x9de3,0x9de3,0x3461},
{0x9de4,0x9de4,0x3464},
{0x9de5,0x9de5,0x171c},
{0x9de6,0x9de6,0x3468},
{0x9de8,0x9de8,0x346f},
{0x9de9,0x9de9,0x33fe},
{0x9deb,0x9deb,0x3462},
{0x9dec,0x9dec,0x346c},
{0x9ded,0x9ded,0x3470},
{0x9dee,0x9dee,0x3467},
{0x9def,0x9def,0x3460},
{0x9df0,0x9df0,0x346a},
{0x9df2,0x9df2,0x3469},
{0x9df3,0x9df3,0x346e},
{0x9df4,0x9df4,0x346d},
{0x9df5,0x9df5,0x3402},
{0x9df6,0x9df6,0x3465},
{0x9df7,0x9df7,0x345f},
{0x9df8,0x9df8,0x3463},
{0x9df9,0x9dfa,0x173b},
{0x9dfb,0x9dfb,0x345e},
{0x9dfc,0x9dfc,0x47d1},
{0x9dfd,0x9dfd,0x34c1},
{0x9dfe,0x9dfe,0x34b8},
{0x9dff,0x9dff,0x34c0},
{0x9e00,0x9e01,0x34bd},
{0x9e02,0x9e02,0x34b7},
{0x9e03,0x9e03,0x34ba},
{0x9e04,0x9e04,0x34c2},
{0x9e05,0x9e05,0x34bc},
{0x9e06,0x9e06,0x34bb},
{0x9e07,0x9e07,0x34b9},
{0x9e09,0x9e09,0x34bf},
{0x9e0a,0x9e0a,0x457e},
{0x9e0b,0x9e0b,0x34f7},
{0x9e0c,0x9e0c,0x457a},
{0x9e0d,0x9e0d,0x34f8},
{0x9e0e,0x9e0e,0x3928},
{0x9e0f,0x9e0f,0x34fa},
{0x9e10,0x9e10,0x34f9},
{0x9e11,0x9e11,0x34fc},
{0x9e12,0x9e12,0x34fb},
{0x9e13,0x9e13,0x3517},
{0x9e14,0x9e14,0x3516},
{0x9e15,0x9e15,0x3530},
{0x9e17,0x9e17,0x3531},
{0x9e18,0x9e18,0x3c44},
{0x9e19,0x9e19,0x353d},
{0x9e1a,0x9e1a,0x1765},
{0x9e1b,0x9e1b,0x1769},
{0x9e1c,0x9e1c,0x3f84},
{0x9e1d,0x9e1d,0x3546},
{0x9e1e,0x9e1e,0x176a},
{0x9e1f,0x9e1f,0x4513},
{0x9e75,0x9e75,0xbe3},
{0x9e79,0x9e79,0x1661},
{0x9e7a,0x9e7a,0x336a},
{0x9e7b,0x9e7b,0x43f8},
{0x9e7c,0x9e7d,0x173d},
{0x9e7f,0x9e7f,0xbe4},
{0x9e80,0x9e80,0x2611},
{0x9e81,0x9e81,0x3f0f},
{0x9e82,0x9e82,0xf7a},
{0x9e83,0x9e83,0x2afc},
{0x9e84,0x9e84,0x3f76},
{0x9e85,0x9e85,0x3ef2},
{0x9e86,0x9e87,0x2d31},
{0x9e88,0x9e88,0x2d30},
{0x9e89,0x9e89,0x2f02},
{0x9e8a,0x9e8a,0x2f01},
{0x9e8b,0x9e8b,0x14c1},
{0x9e8c,0x9e8c,0x306e},
{0x9e8d,0x9e8d,0x2f03},
{0x9e8e,0x9e8e,0x306d},
{0x9e90,0x9e90,0x3931},
{0x9e91,0x9e91,0x31bd},
{0x9e92,0x9e92,0x1605},
{0x9e93,0x9e93,0x1607},
{0x9e94,0x9e94,0x31bc},
{0x9e95,0x9e95,0x3932},
{0x9e96,0x9e96,0x3fbc},
{0x9e97,0x9e97,0x1606},
{0x9e98,0x9e98,0x3f27},
{0x9e99,0x9e99,0x32b6},
{0x9e9a,0x9e9a,0x32b8},
{0x9e9b,0x9e9b,0x32b7},
{0x9e9c,0x9e9c,0x336b},
{0x9e9d,0x9e9d,0x16b5},
{0x9e9e,0x9e9e,0x3933},
{0x9e9f,0x9e9f,0x171d},
{0x9ea0,0x9ea0,0x34c3},
{0x9ea1,0x9ea1,0x34fd},
{0x9ea2,0x9ea2,0x3934},
{0x9ea4,0x9ea4,0x354a},
{0x9ea5,0x9ea5,0xbe5},
{0x9ea6,0x9ea6,0x4944},
{0x9ea7,0x9ea7,0x287c},
{0x9ea8,0x9ea8,0x3f75},
{0x9ea9,0x9ea9,0x1285},
{0x9eaa,0x9eaa,0x3936},
{0x9eab,0x9eab,0x3e92},
{0x9eac,0x9eac,0x43f4},
{0x9ead,0x9ead,0x2d34},
{0x9eae,0x9eae,0x2d33},
{0x9eaf,0x9eaf,0x3937},
{0x9eb0,0x9eb0,0x2f04},
{0x9eb1,0x9eb1,0x47d4},
{0x9eb4,0x9eb4,0x1608},
{0x9eb5,0x9eb5,0x1662},
{0x9eb6,0x9eb6,0x3405},
{0x9eb7,0x9eb7,0x3542},
{0x9ebb,0x9ebb,0xbe6},
{0x9ebc,0x9ebc,0x10ff},
{0x9ebd,0x9ebd,0x47d5},
{0x9ebe,0x9ebe,0x1286},
{0x9ebf,0x9ebf,0x3d78},
{0x9ec0,0x9ec0,0x31be},
{0x9ec1,0x9ec1,0x3939},
{0x9ec2,0x9ec2,0x3471},
{0x9ec3,0x9ec3,0xdbb},
{0x9ec4,0x9ec4,0x4514},
{0x9ec6,0x9ec6,0x47d6},
{0x9ec7,0x9ec7,0x4577},
{0x9ec8,0x9ec8,0x2f05},
{0x9ecc,0x9ecc,0x1751},
{0x9ecd,0x9ecd,0xdbc},
{0x9ece,0x9ece,0x1287},
{0x9ecf,0x9ecf,0x14c2},
{0x9ed0,0x9ed0,0x3472},
{0x9ed1,0x9ed1,0xdbd},
{0x9ed3,0x9ed3,0x2afd},
{0x9ed4,0x9ed4,0x13b1},
{0x9ed5,0x9ed6,0x2d35},
{0x9ed8,0x9ed8,0x13b0},
{0x9eda,0x9eda,0x2f06},
{0x9edb,0x9edb,0x14c6},
{0x9edc,0x9edd,0x14c4},
{0x9ede,0x9ede,0x14c3},
{0x9edf,0x9edf,0x306f},
{0x9ee0,0x9ee0,0x1572},
{0x9ee2,0x9ee2,0x47d8},
{0x9ee4,0x9ee4,0x32ba},
{0x9ee5,0x9ee5,0x32b9},
{0x9ee6,0x9ee6,0x32bc},
{0x9ee7,0x9ee7,0x32bb},
{0x9ee8,0x9ee8,0x1663},
{0x9eeb,0x9eeb,0x336c},
{0x9eed,0x9eed,0x336e},
{0x9eee,0x9eee,0x336d},
{0x9eef,0x9eef,0x16b6},
{0x9ef0,0x9ef0,0x3406},
{0x9ef1,0x9ef1,0x47d9},
{0x9ef2,0x9ef3,0x3473},
{0x9ef4,0x9ef4,0x171e},
{0x9ef5,0x9ef5,0x34fe},
{0x9ef6,0x9ef6,0x3518},
{0x9ef7,0x9ef7,0x1762},
{0x9ef8,0x9ef8,0x47da},
{0x9ef9,0x9ef9,0x2360},
{0x9efa,0x9efa,0x2d37},
{0x9efb,0x9efb,0x2f07},
{0x9efc,0x9efc,0x31bf},
{0x9efd,0x9efd,0x2612},
{0x9efe,0x9efe,0x47ce},
{0x9eff,0x9eff,0x2f08},
{0x9f00,0x9f00,0x3071},
{0x9f01,0x9f01,0x3070},
{0x9f02,0x9f02,0x3940},
{0x9f06,0x9f06,0x3475},
{0x9f07,0x9f07,0x173f},
{0x9f08,0x9f08,0x3941},
{0x9f09,0x9f09,0x34ff},
{0x9f0a,0x9f0a,0x3519},
{0x9f0e,0x9f0e,0xf7b},
{0x9f0f,0x9f10,0x2afe},
{0x9f12,0x9f12,0x2d38},
{0x9f13,0x9f13,0xf7c},
{0x9f15,0x9f15,0x1573},
{0x9f16,0x9f16,0x3072},
{0x9f17,0x9f17,0x3945},
{0x9f18,0x9f18,0x3370},
{0x9f19,0x9f19,0x16b7},
{0x9f1a,0x9f1a,0x3371},
{0x9f1b,0x9f1b,0x336f},
{0x9f1c,0x9f1c,0x3476},
{0x9f1e,0x9f1e,0x34c4},
{0x9f20,0x9f20,0xf7d},
{0x9f22,0x9f22,0x2f0b},
{0x9f23,0x9f23,0x2f0a},
{0x9f24,0x9f24,0x2f09},
{0x9f25,0x9f25,0x3073},
{0x9f26,0x9f26,0x3f90},
{0x9f27,0x9f27,0x4620},
{0x9f28,0x9f28,0x3077},
{0x9f29,0x9f29,0x3076},
{0x9f2a,0x9f2a,0x3075},
{0x9f2b,0x9f2b,0x3074},
{0x9f2c,0x9f2c,0x1574},
{0x9f2d,0x9f2d,0x31c0},
{0x9f2e,0x9f2e,0x32be},
{0x9f2f,0x9f2f,0x1664},
{0x9f30,0x9f30,0x32bd},
{0x9f31,0x9f31,0x3372},
{0x9f32,0x9f32,0x3409},
{0x9f33,0x9f33,0x3408},
{0x9f34,0x9f34,0x16f2},
{0x9f35,0x9f35,0x3407},
{0x9f36,0x9f36,0x3479},
{0x9f37,0x9f37,0x3478},
{0x9f38,0x9f38,0x3477},
{0x9f39,0x9f39,0x3947},
{0x9f3b,0x9f3b,0x1100},
{0x9f3d,0x9f3d,0x2d39},
{0x9f3e,0x9f3e,0x14c7},
{0x9f40,0x9f41,0x31c1},
{0x9f42,0x9f42,0x340a},
{0x9f43,0x9f43,0x347a},
{0x9f44,0x9f44,0x47db},
{0x9f45,0x9f45,0x394a},
{0x9f46,0x9f46,0x34c5},
{0x9f47,0x9f47,0x3500},
{0x9f48,0x9f48,0x3532},
{0x9f49,0x9f49,0x354c},
{0x9f4a,0x9f4a,0x1101},
{0x9f4b,0x9f4b,0x14c8},
{0x9f4c,0x9f4c,0x3078},
{0x9f4d,0x9f4d,0x31c3},
{0x9f4e,0x9f4e,0x3373},
{0x9f4f,0x9f4f,0x347b},
{0x9f50,0x9f50,0x4943},
{0x9f52,0x9f52,0x1289},
{0x9f53,0x9f53,0x3f80},
{0x9f54,0x9f54,0x2f0c},
{0x9f55,0x9f55,0x3079},
{0x9f56,0x9f58,0x31c4},
{0x9f59,0x9f59,0x32c3},
{0x9f5a,0x9f5a,0x3f23},
{0x9f5b,0x9f5b,0x32bf},
{0x9f5c,0x9f5c,0x16b8},
{0x9f5d,0x9f5d,0x32c2},
{0x9f5e,0x9f5e,0x32c1},
{0x9f5f,0x9f5f,0x1665},
{0x9f60,0x9f60,0x32c0},
{0x9f61,0x9f61,0x1667},
{0x9f63,0x9f63,0x1666},
{0x9f64,0x9f64,0x3375},
{0x9f65,0x9f65,0x3374},
{0x9f66,0x9f67,0x16b9},
{0x9f69,0x9f69,0x3950},
{0x9f6a,0x9f6a,0x16f4},
{0x9f6b,0x9f6b,0x340b},
{0x9f6c,0x9f6c,0x16f3},
{0x9f6e,0x9f6f,0x347e},
{0x9f70,0x9f70,0x347d},
{0x9f71,0x9f71,0x347c},
{0x9f72,0x9f72,0x1741},
{0x9f74,0x9f76,0x34c6},
{0x9f77,0x9f77,0x1740},
{0x9f78,0x9f78,0x3501},
{0x9f79,0x9f79,0x3504},
{0x9f7a,0x9f7a,0x3503},
{0x9f7b,0x9f7b,0x3502},
{0x9f7e,0x9f7e,0x354b},
{0x9f7f,0x9f7f,0x4680},
{0x9f8d,0x9f8d,0x13b2},
{0x9f8e,0x9f8e,0x3952},
{0x9f90,0x9f90,0x157c},
{0x9f91,0x9f91,0x32c4},
{0x9f92,0x9f92,0x3376},
{0x9f94,0x9f94,0x16f5},
{0x9f95,0x9f95,0x340c},
{0x9f98,0x9f98,0x354d},
{0x9f99,0x9f99,0x4587},
{0x9f9c,0x9f9c,0x13b3},
{0x9f9f,0x9f9f,0x4646},
{0x9fa0,0x9fa0,0x2f0d},
{0x9fa2,0x9fa2,0x340d},
{0x9fa4,0x9fa4,0x351a},
{0x9fa5,0x9fa5,0x3f70},
{0xe000,0xe000,0x400b},
{0xe002,0xe003,0x400d},
{0xe009,0xe00a,0x4014},
{0xe00c,0xe00c,0x4017},
{0xe00e,0xe00e,0x4019},
{0xe012,0xe016,0x401d},
{0xe018,0xe018,0x4023},
{0xe01a,0xe01e,0x4025},
{0xe020,0xe020,0x402b},
{0xe022,0xe023,0x402d},
{0xe025,0xe025,0x4030},
{0xe02d,0xe02e,0x4038},
{0xe030,0xe030,0x403b},
{0xe032,0xe038,0x403d},
{0xe03b,0xe03c,0x4046},
{0xe045,0xe045,0x4050},
{0xe04c,0xe04c,0x4057},
{0xe05a,0xe05a,0x4065},
{0xe05c,0xe05c,0x4067},
{0xe05f,0xe061,0x406a},
{0xe064,0xe064,0x406f},
{0xe066,0xe066,0x4071},
{0xe06c,0xe06c,0x4077},
{0xe071,0xe071,0x407c},
{0xe074,0xe075,0x407f},
{0xe078,0xe078,0x4083},
{0xe07d,0xe07d,0x4088},
{0xe087,0xe087,0x4092},
{0xe089,0xe08a,0x4094},
{0xe08c,0xe08c,0x4097},
{0xe093,0xe094,0x409e},
{0xe099,0xe09a,0x40a4},
{0xe09c,0xe09e,0x40a7},
{0xe0a6,0xe0a7,0x40b1},
{0xe0a9,0xe0a9,0x40b4},
{0xe0ab,0xe0ab,0x40b6},
{0xe0ae,0xe0ae,0x40b9},
{0xe0ba,0xe0ba,0x40c5},
{0xe0bc,0xe0bc,0x40c7},
{0xe0be,0xe0be,0x40c9},
{0xe0c3,0xe0c3,0x40ce},
{0xe0c5,0xe0c5,0x40d0},
{0xe0c7,0xe0c8,0x40d2},
{0xe0ca,0xe0ca,0x40d5},
{0xe0d0,0xe0d0,0x40db},
{0xe0d4,0xe0d5,0x40df},
{0xe0dc,0xe0dc,0x40e7},
{0xe0df,0xe0df,0x40ea},
{0xe0e2,0xe0e5,0x40ed},
{0xe0e7,0xe0e9,0x40f2},
{0xe0eb,0xe0ec,0x40f6},
{0xe0ee,0xe0ee,0x40f9},
{0xe0f2,0xe0f2,0x40fd},
{0xe0f8,0xe0f9,0x4103},
{0xe0fb,0xe0fb,0x4105},
{0xe0fd,0xe0fd,0x4107},
{0xe103,0xe103,0x410d},
{0xe106,0xe106,0x4110},
{0xe109,0xe10b,0x4112},
{0xe110,0xe110,0x4119},
{0xe114,0xe115,0x411d},
{0xe11c,0xe11d,0x4125},
{0xe121,0xe121,0x412a},
{0xe126,0xe128,0x412f},
{0xe130,0xe131,0x4139},
{0xe136,0xe136,0x413f},
{0xe139,0xe139,0x4142},
{0xe141,0xe142,0x414a},
{0xe145,0xe145,0x414d},
{0xe147,0xe148,0x414f},
{0xe14a,0xe14a,0x4151},
{0xe14d,0xe14d,0x4153},
{0xe150,0xe150,0x4156},
{0xe154,0xe157,0x415a},
{0xe159,0xe15a,0x415f},
{0xe15d,0xe15d,0x494f},
{0xe162,0xe162,0x4167},
{0xe168,0xe16a,0x416d},
{0xe171,0xe172,0x4175},
{0xe175,0xe175,0x4179},
{0xe178,0xe179,0x417c},
{0xe17b,0xe17c,0x417f},
{0xe17f,0xe180,0x4183},
{0xe185,0xe185,0x4189},
{0xe187,0xe187,0x418b},
{0xe189,0xe189,0x418d},
{0xe18b,0xe18b,0x418f},
{0xe18f,0xe190,0x4193},
{0xe193,0xe193,0x4196},
{0xe195,0xe195,0x4953},
{0xe196,0xe196,0x4198},
{0xe19f,0xe1a0,0x41a1},
{0xe1a4,0xe1a4,0x4954},
{0xe1a6,0xe1a6,0x41a8},
{0xe1aa,0xe1aa,0x41ac},
{0xe1ae,0xe1ae,0x41b0},
{0xe1b0,0xe1b1,0x41b2},
{0xe1b3,0xe1b4,0x41b5},
{0xe1b6,0xe1b6,0x41b8},
{0xe1b8,0xe1b8,0x41ba},
{0xe1bb,0xe1bb,0x4955},
{0xe1bc,0xe1bf,0x41bd},
{0xe1c1,0xe1c1,0x41c2},
{0xe1c4,0xe1c5,0x41c5},
{0xe1c8,0xe1c8,0x41c8},
{0xe1cc,0xe1cd,0x41cc},
{0xe1d4,0xe1d5,0x41d4},
{0xe1d7,0xe1d7,0x41d7},
{0xe1da,0xe1da,0x41da},
{0xe1dd,0xe1dd,0x41dd},
{0xe1e2,0xe1e2,0x41e1},
{0xe1e6,0xe1e6,0x41e5},
{0xe1eb,0xe1eb,0x41ea},
{0xe1ef,0xe1f0,0x41ee},
{0xe1f2,0xe1f2,0x41f1},
{0xe1f7,0xe1f7,0x41f6},
{0xe1fa,0xe1fa,0x41f9},
{0xe1fe,0xe1fe,0x41fd},
{0xe200,0xe200,0x41ff},
{0xe201,0xe201,0x4958},
{0xe202,0xe202,0x4201},
{0xe205,0xe205,0x4204},
{0xe207,0xe207,0x4206},
{0xe20a,0xe20c,0x4209},
{0xe210,0xe210,0x420f},
{0xe212,0xe216,0x4211},
{0xe219,0xe219,0x4218},
{0xe21b,0xe21c,0x421a},
{0xe21f,0xe221,0x421e},
{0xe226,0xe228,0x4225},
{0xe22a,0xe22a,0x4229},
{0xe22f,0xe22f,0x422e},
{0xe231,0xe231,0x4230},
{0xe233,0xe233,0x4232},
{0xe237,0xe237,0x4236},
{0xe23b,0xe23b,0x423a},
{0xe23d,0xe23e,0x423c},
{0xe245,0xe245,0x4244},
{0xe248,0xe248,0x4247},
{0xe24a,0xe24f,0x4249},
{0xe251,0xe254,0x4250},
{0xe261,0xe261,0x4260},
{0xe268,0xe268,0x4266},
{0xe26b,0xe26b,0x4269},
{0xe26d,0xe26f,0x426b},
{0xe271,0xe271,0x426f},
{0xe27a,0xe27a,0x4278},
{0xe27d,0xe27d,0x427b},
{0xe282,0xe282,0x4280},
{0xe288,0xe289,0x4286},
{0xe28b,0xe28b,0x4289},
{0xe290,0xe291,0x428e},
{0xe294,0xe296,0x4292},
{0xe299,0xe29b,0x4297},
{0xe29f,0xe29f,0x429d},
{0xe2a1,0xe2a1,0x495b},
{0xe2a4,0xe2a4,0x42a1},
{0xe2a6,0xe2a6,0x42a3},
{0xe2b2,0xe2b5,0x42ae},
{0xe2b7,0xe2b8,0x42b3},
{0xe2ba,0xe2ba,0x42b6},
{0xe2be,0xe2bf,0x42b9},
{0xe2c1,0xe2c1,0x42bc},
{0xe2c7,0xe2c7,0x42c2},
{0xe2c9,0xe2c9,0x42c4},
{0xe2d4,0xe2d4,0x42cf},
{0xe2d8,0xe2d9,0x42d3},
{0xe2de,0xe2de,0x42d9},
{0xe2e0,0xe2e2,0x42db},
{0xe2e7,0xe2e7,0x42e2},
{0xe2e9,0xe2e9,0x42e4},
{0xe2eb,0xe2ed,0x42e6},
{0xe2f0,0xe2f1,0x495d},
{0xe2f8,0xe2f8,0x42f1},
{0xe2fc,0xe2fc,0x42f5},
{0xe2ff,0xe300,0x495f},
{0xe301,0xe301,0x42f8},
{0xe306,0xe306,0x42fd},
{0xe308,0xe308,0x42ff},
{0xe30c,0xe30c,0x4303},
{0xe30f,0xe30f,0x4306},
{0xe311,0xe311,0x372b},
{0xe313,0xe313,0x372d},
{0xe315,0xe315,0x372f},
{0xe318,0xe318,0x3731},
{0xe31b,0xe31c,0x3734},
{0xe31f,0xe31f,0x3738},
{0xe325,0xe325,0x373e},
{0xe32c,0xe32c,0x3745},
{0xe330,0xe331,0x3749},
{0xe335,0xe336,0x374e},
{0xe33b,0xe33b,0x3754},
{0xe33d,0xe33d,0x469a},
{0xe342,0xe342,0x375a},
{0xe345,0xe345,0x375d},
{0xe348,0xe348,0x375f},
{0xe352,0xe352,0x3768},
{0xe354,0xe354,0x376a},
{0xe35b,0xe35c,0x3770},
{0xe360,0xe360,0x3775},
{0xe362,0xe362,0x3777},
{0xe364,0xe365,0x3779},
{0xe36f,0xe36f,0x3784},
{0xe372,0xe372,0x3787},
{0xe374,0xe375,0x3789},
{0xe377,0xe377,0x378c},
{0xe379,0xe379,0x378d},
{0xe37b,0xe37b,0x378f},
{0xe381,0xe382,0x3795},
{0xe386,0xe386,0x379a},
{0xe388,0xe38a,0x379c},
{0xe38d,0xe38d,0x37a1},
{0xe391,0xe391,0x37a5},
{0xe393,0xe393,0x37a7},
{0xe394,0xe394,0x46a0},
{0xe396,0xe396,0x37a9},
{0xe398,0xe398,0x37ab},
{0xe39a,0xe39b,0x37ad},
{0xe39e,0xe39e,0x46a1},
{0xe39f,0xe3a0,0x37b1},
{0xe3a2,0xe3a2,0x37b4},
{0xe3a5,0xe3a5,0x46a2},
{0xe3a6,0xe3a9,0x37b8},
{0xe3ac,0xe3ac,0x37be},
{0xe3b1,0xe3b2,0x37c3},
{0xe3b4,0xe3b5,0x37c6},
{0xe3b9,0xe3bd,0x37cb},
{0xe3c3,0xe3c3,0x37d5},
{0xe3c8,0xe3c8,0x37d9},
{0xe3cc,0xe3cc,0x37dd},
{0xe3cd,0xe3cd,0x46a4},
{0xe3cf,0xe3cf,0x37df},
{0xe3d5,0xe3d5,0x46a5},
{0xe3d8,0xe3da,0x37e7},
{0xe3e1,0xe3e1,0x37f0},
{0xe3e3,0xe3e3,0x37f2},
{0xe3e7,0xe3e7,0x46a6},
{0xe3ed,0xe3ee,0x37fc},
{0xe3f0,0xe3f0,0x37ff},
{0xe3f2,0xe3f4,0x3801},
{0xe3f8,0xe3f8,0x3807},
{0xe3fa,0xe3fa,0x3809},
{0xe3fd,0xe3fd,0x380c},
{0xe3ff,0xe3ff,0x380e},
{0xe409,0xe40b,0x3818},
{0xe414,0xe414,0x3821},
{0xe41b,0xe41b,0x3828},
{0xe41d,0xe41e,0x382a},
{0xe423,0xe423,0x3830},
{0xe425,0xe425,0x3832},
{0xe426,0xe426,0x46ab},
{0xe428,0xe428,0x3834},
{0xe42c,0xe42d,0x3838},
{0xe432,0xe432,0x383e},
{0xe436,0xe436,0x3842},
{0xe43a,0xe43c,0x3846},
{0xe43e,0xe43e,0x384a},
{0xe440,0xe441,0x384c},
{0xe444,0xe444,0x3850},
{0xe448,0xe448,0x46ad},
{0xe44c,0xe44e,0x3857},
{0xe450,0xe451,0x385b},
{0xe459,0xe45a,0x3864},
{0xe45d,0xe45e,0x3868},
{0xe461,0xe463,0x386c},
{0xe465,0xe465,0x3870},
{0xe467,0xe469,0x46af},
{0xe46a,0xe46a,0x3873},
{0xe46c,0xe46c,0x3875},
{0xe46e,0xe46f,0x3877},
{0xe471,0xe472,0x387a},
{0xe474,0xe474,0x387d},
{0xe476,0xe476,0x387f},
{0xe47e,0xe47f,0x3886},
{0xe484,0xe484,0x388c},
{0xe486,0xe48a,0x388d},
{0xe48d,0xe48e,0x3894},
{0xe491,0xe492,0x3897},
{0xe495,0xe497,0x389b},
{0xe4a1,0xe4a1,0x46b4},
{0xe4a2,0xe4a4,0x38a7},
{0xe4ab,0xe4ab,0x38b0},
{0xe4af,0xe4af,0x38b4},
{0xe4b3,0xe4ba,0x38b8},
{0xe4c1,0xe4c1,0x38c6},
{0xe4c6,0xe4c6,0x38cb},
{0xe4c9,0xe4c9,0x38ce},
{0xe4cd,0xe4ce,0x38d2},
{0xe4d6,0xe4d6,0x38db},
{0xe4d9,0xe4d9,0x38de},
{0xe4dc,0xe4dd,0x38e1},
{0xe4e4,0xe4e4,0x38e9},
{0xe4e6,0xe4e8,0x38eb},
{0xe4ee,0xe4ef,0x38f3},
{0xe4f6,0xe4f6,0x38fb},
{0xe4f8,0xe4f9,0x38fd},
{0xe4ff,0xe4ff,0x3904},
{0xe501,0xe502,0x3906},
{0xe505,0xe505,0x390a},
{0xe50a,0xe50a,0x390f},
{0xe50d,0xe50d,0x46b5},
{0xe50f,0xe50f,0x3913},
{0xe512,0xe512,0x3916},
{0xe514,0xe514,0x3918},
{0xe516,0xe516,0x46b6},
{0xe51a,0xe51a,0x391d},
{0xe51c,0xe521,0x391f},
{0xe524,0xe524,0x3927},
{0xe528,0xe528,0x46b8},
{0xe529,0xe52c,0x392a},
{0xe52e,0xe52f,0x392f},
{0xe537,0xe537,0x3938},
{0xe53e,0xe53e,0x393f},
{0xe543,0xe543,0x3944},
{0xe54b,0xe54b,0x394c},
{0xe554,0xe555,0x3955},
{0xe557,0xe559,0x3958},
{0xe55b,0xe55c,0x395c},
{0xe55e,0xe55f,0x395f},
{0xe562,0xe562,0x3963},
{0xe566,0xe567,0x3967},
{0xe56d,0xe56e,0x396e},
{0xe570,0xe570,0x3971},
{0xe572,0xe573,0x3973},
{0xe575,0xe577,0x3976},
{0xe579,0xe57a,0x397a},
{0xe57c,0xe57e,0x397d},
{0xe580,0xe580,0x3981},
{0xe582,0xe586,0x3983},
{0xe588,0xe588,0x3989},
{0xe58b,0xe590,0x398c},
{0xe592,0xe594,0x3993},
{0xe59a,0xe59a,0x399b},
{0xe59d,0xe5a1,0x399e},
{0xe5a4,0xe5a4,0x39a5},
{0xe5a6,0xe5a8,0x39a7},
{0xe5aa,0xe5aa,0x39ab},
{0xe5af,0xe5af,0x39b0},
{0xe5b1,0xe5b1,0x39b2},
{0xe5b2,0xe5b2,0x46bb},
{0xe5b5,0xe5b5,0x39b6},
{0xe5b7,0xe5b8,0x39b8},
{0xe5ba,0xe5bb,0x39bb},
{0xe5be,0xe5c0,0x39bf},
{0xe5c2,0xe5c2,0x39c3},
{0xe5c8,0xe5c9,0x39c9},
{0xe5d7,0xe5d9,0x39d4},
{0xe5db,0xe5dc,0x39d8},
{0xe5df,0xe5df,0x39dc},
{0xe5e1,0xe5e3,0x39de},
{0xe5e5,0xe5e6,0x39e2},
{0xe5e9,0xe5e9,0x39e6},
{0xe5ee,0xe5f1,0x39eb},
{0xe5f3,0xe5f3,0x39f0},
{0xe5fb,0xe5fc,0x39f8},
{0xe600,0xe600,0x39fd},
{0xe602,0xe602,0x39ff},
{0xe604,0xe604,0x3a01},
{0xe60f,0xe612,0x3a0b},
{0xe619,0xe61c,0x3a14},
{0xe621,0xe621,0x3a1c},
{0xe623,0xe624,0x3a1e},
{0xe627,0xe627,0x3a22},
{0xe62b,0xe62b,0x3a26},
{0xe632,0xe634,0x3a2d},
{0xe637,0xe638,0x3a32},
{0xe63a,0xe63a,0x3a35},
{0xe63c,0xe63c,0x3a37},
{0xe63f,0xe642,0x3a3a},
{0xe645,0xe646,0x3a40},
{0xe648,0xe648,0x3a43},
{0xe64b,0xe64b,0x3a45},
{0xe64e,0xe64f,0x3a48},
{0xe652,0xe655,0x3a4c},
{0xe657,0xe659,0x3a51},
{0xe65d,0xe660,0x3a57},
{0xe662,0xe663,0x3a5c},
{0xe665,0xe668,0x3a5f},
{0xe66a,0xe66a,0x46bf},
{0xe66e,0xe66f,0x3a67},
{0xe671,0xe671,0x3a6a},
{0xe676,0xe679,0x3a6f},
{0xe67b,0xe67d,0x3a74},
{0xe67f,0xe680,0x3a78},
{0xe683,0xe688,0x3a7b},
{0xe68a,0xe68c,0x3a82},
{0xe68e,0xe690,0x3a86},
{0xe692,0xe692,0x3a8a},
{0xe696,0xe697,0x3a8e},
{0xe699,0xe69a,0x3a91},
{0xe69c,0xe69c,0x3a94},
{0xe69e,0xe69f,0x3a96},
{0xe6a1,0xe6a2,0x3a99},
{0xe6a6,0xe6a6,0x3a9d},
{0xe6ac,0xe6ad,0x3aa2},
{0xe6b2,0xe6b2,0x3aa8},
{0xe6b9,0xe6ba,0x3aaf},
{0xe6bd,0xe6be,0x3ab3},
{0xe6c0,0xe6c1,0x3ab6},
{0xe6c5,0xe6c5,0x46c4},
{0xe6c7,0xe6c7,0x3abc},
{0xe6ca,0xe6ca,0x3abf},
{0xe6d1,0xe6d2,0x3ac6},
{0xe6d7,0xe6d8,0x3acc},
{0xe6da,0xe6db,0x3acf},
{0xe6dd,0xe6e1,0x3ad2},
{0xe6e4,0xe6e6,0x3ad9},
{0xe6ee,0xe6ee,0x3ae3},
{0xe6f2,0xe6f3,0x3ae7},
{0xe6f7,0xe6f7,0x3aec},
{0xe6fa,0xe6fc,0x3aef},
{0xe6ff,0xe6ff,0x3af4},
{0xe701,0xe702,0x3af6},
{0xe706,0xe707,0x3afb},
{0xe709,0xe70a,0x3afe},
{0xe70c,0xe70c,0x3b01},
{0xe710,0xe711,0x3b05},
{0xe713,0xe713,0x3b08},
{0xe717,0xe718,0x3b0c},
{0xe71a,0xe71b,0x3b0f},
{0xe71d,0xe71d,0x3b12},
{0xe724,0xe725,0x3b19},
{0xe72b,0xe72b,0x3b20},
{0xe72f,0xe72f,0x3b24},
{0xe733,0xe737,0x3b28},
{0xe73c,0xe73d,0x3b31},
{0xe740,0xe742,0x3b35},
{0xe744,0xe744,0x3b39},
{0xe747,0xe747,0x3b3c},
{0xe74d,0xe74f,0x3b42},
{0xe751,0xe752,0x3b46},
{0xe75b,0xe75d,0x3b50},
{0xe75f,0xe75f,0x3b54},
{0xe761,0xe762,0x3b56},
{0xe764,0xe765,0x3b59},
{0xe767,0xe768,0x3b5c},
{0xe769,0xe769,0x46c7},
{0xe76c,0xe76e,0x3b60},
{0xe770,0xe770,0x3b64},
{0xe775,0xe775,0x3b69},
{0xe777,0xe778,0x3b6a},
{0xe77a,0xe77a,0x3b6d},
{0xe77c,0xe77d,0x3b6f},
{0xe784,0xe784,0x3b77},
{0xe786,0xe786,0x3b79},
{0xe789,0xe789,0x3b7c},
{0xe78b,0xe78b,0x3b7e},
{0xe78d,0xe78d,0x3b80},
{0xe790,0xe790,0x3b83},
{0xe792,0xe792,0x3b85},
{0xe794,0xe794,0x3b87},
{0xe796,0xe797,0x3b89},
{0xe799,0xe799,0x3b8c},
{0xe7a2,0xe7a2,0x3b95},
{0xe7a4,0xe7a9,0x3b97},
{0xe7b1,0xe7b1,0x3ba4},
{0xe7b4,0xe7b4,0x3ba7},
{0xe7b8,0xe7b8,0x3bab},
{0xe7bc,0xe7bc,0x3baf},
{0xe7c4,0xe7c4,0x3bb6},
{0xe7ca,0xe7ca,0x3bbc},
{0xe7ce,0xe7ce,0x3bc0},
{0xe7d1,0xe7d1,0x3bc3},
{0xe7d8,0xe7d8,0x3bca},
{0xe7da,0xe7da,0x3bcc},
{0xe7dc,0xe7dd,0x3bce},
{0xe7e4,0xe7e4,0x3bd6},
{0xe7e7,0xe7e9,0x3bd9},
{0xe7eb,0xe7eb,0x3bdd},
{0xe7ed,0xe7ef,0x3bdf},
{0xe7f1,0xe7f3,0x3be3},
{0xe7f8,0xe7f8,0x3bea},
{0xe7fb,0xe7fc,0x3bed},
{0xe7ff,0xe802,0x3bf1},
{0xe808,0xe808,0x3bfa},
{0xe80c,0xe80c,0x3bfe},
{0xe80e,0xe80f,0x3c00},
{0xe811,0xe811,0x3c03},
{0xe813,0xe813,0x3c05},
{0xe816,0xe818,0x3c08},
{0xe81a,0xe81a,0x3c0c},
{0xe821,0xe821,0x3c13},
{0xe827,0xe828,0x3c19},
{0xe82d,0xe82d,0x3c1f},
{0xe82f,0xe82f,0x3c21},
{0xe835,0xe835,0x3c27},
{0xe837,0xe838,0x3c29},
{0xe83a,0xe83b,0x3c2c},
{0xe841,0xe842,0x3c33},
{0xe84b,0xe84b,0x3c3d},
{0xe84d,0xe84e,0x3c3f},
{0xe851,0xe851,0x3c43},
{0xe855,0xe858,0x3c47},
{0xe85b,0xe85d,0x3c4d},
{0xe860,0xe862,0x3c52},
{0xe865,0xe867,0x3c57},
{0xe869,0xe86a,0x3c5b},
{0xe86c,0xe86c,0x3c5d},
{0xe871,0xe871,0x3c62},
{0xe878,0xe878,0x3c69},
{0xe87d,0xe87f,0x3c6e},
{0xe887,0xe887,0x3c78},
{0xe88f,0xe88f,0x3c80},
{0xe891,0xe892,0x3c82},
{0xe894,0xe895,0x3c85},
{0xe89a,0xe89a,0x3c8b},
{0xe89d,0xe89d,0x3c8e},
{0xe8a0,0xe8a1,0x3c91},
{0xe8a3,0xe8a7,0x3c94},
{0xe8a9,0xe8aa,0x3c9a},
{0xe8ac,0xe8b0,0x3c9d},
{0xe8b5,0xe8b6,0x3ca6},
{0xe8b8,0xe8b8,0x3ca9},
{0xe8bd,0xe8c3,0x3cae},
{0xe8c5,0xe8c5,0x3cb6},
{0xe8c7,0xe8c7,0x3cb8},
{0xe8cb,0xe8cb,0x3cbc},
{0xe8cd,0xe8ce,0x3cbe},
{0xe8d0,0xe8d1,0x3cc1},
{0xe8d3,0xe8d3,0x3cc4},
{0xe8d5,0xe8d6,0x3cc6},
{0xe8d8,0xe8da,0x3cc9},
{0xe8dd,0xe8dd,0x3cce},
{0xe8df,0xe8e1,0x3cd0},
{0xe8e5,0xe8e6,0x3cd6},
{0xe8e8,0xe8ea,0x3cd9},
{0xe8ed,0xe8ed,0x3cde},
{0xe8f1,0xe8f2,0x3ce2},
{0xe8f5,0xe8f5,0x3ce6},
{0xe8fb,0xe8fb,0x3cec},
{0xe8fe,0xe8fe,0x3cef},
{0xe900,0xe900,0x3cf1},
{0xe903,0xe903,0x3cf4},
{0xe907,0xe908,0x3cf8},
{0xe90c,0xe90c,0x3cfd},
{0xe917,0xe918,0x3d08},
{0xe91c,0xe91c,0x3d0d},
{0xe91e,0xe922,0x3d0f},
{0xe925,0xe92c,0x3d16},
{0xe92e,0xe92f,0x3d1f},
{0xe933,0xe936,0x3d24},
{0xe938,0xe938,0x46cf},
{0xe93a,0xe93a,0x3d2b},
{0xe93c,0xe93e,0x3d2d},
{0xe941,0xe941,0x3d32},
{0xe944,0xe945,0x3d35},
{0xe948,0xe948,0x3d39},
{0xe94d,0xe94d,0x3d3e},
{0xe950,0xe950,0x3d41},
{0xe952,0xe955,0x3d43},
{0xe957,0xe958,0x3d48},
{0xe95a,0xe95a,0x3d4b},
{0xe95c,0xe95e,0x3d4d},
{0xe960,0xe961,0x3d51},
{0xe964,0xe964,0x3d55},
{0xe967,0xe967,0x3d58},
{0xe968,0xe968,0x46d1},
{0xe96e,0xe96f,0x46d5},
{0xe973,0xe973,0x46da},
{0xe974,0xe974,0x3d5b},
{0xe975,0xe975,0x46db},
{0xe977,0xe978,0x46dd},
{0xe97a,0xe97b,0x46e0},
{0xe97d,0xe97d,0x46e3},
{0xe981,0xe982,0x46e6},
{0xe987,0xe987,0x3d5d},
{0xe98a,0xe98a,0x3d5e},
{0xe98e,0xe98e,0x3d5f},
{0xe991,0xe991,0x46ef},
{0xe9b0,0xe9b0,0x43d4},
{0xe9cb,0xe9cb,0x4719},
{0xe9ef,0xe9ef,0x4734},
{0xea43,0xea43,0x4779},
{0xeaa9,0xeaa9,0x47cc},
{0xeabf,0xeac1,0x47dc},
{0xeac5,0xeac5,0x47e0},
{0xeac9,0xeacc,0x47e2},
{0xeacf,0xead1,0x47e8},
{0xead3,0xead4,0x47eb},
{0xead6,0xeada,0x47ee},
{0xeadc,0xeadc,0x47f3},
{0xeade,0xeae3,0x47f4},
{0xeae7,0xeaec,0x47fa},
{0xeaee,0xeaee,0x4800},
{0xeaf0,0xeaf2,0x4801},
{0xeaf4,0xeaf4,0x43b0},
{0xeaf5,0xeaf6,0x4804},
{0xeaf8,0xeaf8,0x4807},
{0xeaf9,0xeaf9,0x43b2},
{0xeafa,0xeafb,0x4808},
{0xeafe,0xeb00,0x480b},
{0xeb02,0xeb05,0x480e},
{0xeb08,0xeb0f,0x4813},
{0xeb11,0xeb12,0x481b},
{0xeb15,0xeb15,0x481e},
{0xeb18,0xeb1d,0x4821},
{0xeb1f,0xeb21,0x4827},
{0xeb23,0xeb23,0x482a},
{0xeb25,0xeb25,0x482b},
{0xeb27,0xeb27,0x482c},
{0xeb2a,0xeb2a,0x482d},
{0xeb2c,0xeb39,0x482e},
{0xeb3d,0xeb3d,0x3d9b},
{0xeb3e,0xeb3f,0x483c},
{0xeb41,0xeb41,0x3d9d},
{0xeb43,0xeb44,0x483e},
{0xeb46,0xeb46,0x3da0},
{0xeb4a,0xeb4a,0x4841},
{0xeb4b,0xeb4c,0x3da3},
{0xeb4d,0xeb4e,0x4842},
{0xeb4f,0xeb50,0x3da5},
{0xeb53,0xeb53,0x4844},
{0xeb54,0xeb54,0x3da9},
{0xeb55,0xeb56,0x4845},
{0xeb57,0xeb57,0x3daa},
{0xeb58,0xeb59,0x4847},
{0xeb5a,0xeb5b,0x3dab},
{0xeb5d,0xeb5d,0x3dae},
{0xeb5f,0xeb5f,0x3db0},
{0xeb61,0xeb61,0x3db2},
{0xeb62,0xeb63,0x4849},
{0xeb65,0xeb65,0x484c},
{0xeb67,0xeb67,0x484d},
{0xeb68,0xeb69,0x3db4},
{0xeb6b,0xeb6b,0x3db7},
{0xeb6d,0xeb6d,0x3db9},
{0xeb6f,0xeb71,0x484e},
{0xeb73,0xeb73,0x4851},
{0xeb74,0xeb74,0x3dbc},
{0xeb76,0xeb76,0x43d0},
{0xeb77,0xeb77,0x4853},
{0xeb78,0xeb78,0x3dbd},
{0xeb7a,0xeb7a,0x4854},
{0xeb7e,0xeb7f,0x4856},
{0xeb80,0xeb81,0x3dc1},
{0xeb83,0xeb84,0x4858},
{0xeb85,0xeb85,0x3dc4},
{0xeb88,0xeb88,0x485a},
{0xeb89,0xeb89,0x3dc6},
{0xeb8a,0xeb8a,0x485b},
{0xeb8c,0xeb8e,0x485c},
{0xeb8f,0xeb8f,0x3dc8},
{0xeb90,0xeb90,0x485f},
{0xeb93,0xeb93,0x3dcb},
{0xeb95,0xeb95,0x4860},
{0xeb97,0xeb97,0x3dce},
{0xeb98,0xeb9a,0x4861},
{0xeb9b,0xeb9b,0x3dcf},
{0xeb9f,0xeb9f,0x4866},
{0xeba0,0xeba0,0x3dd1},
{0xeba2,0xeba2,0x4867},
{0xeba3,0xeba3,0x3dd3},
{0xeba5,0xeba6,0x4868},
{0xeba8,0xeba8,0x3dd6},
{0xebaa,0xebaa,0x486a},
{0xebab,0xebab,0x3dd8},
{0xebad,0xebad,0x3dda},
{0xebae,0xebae,0x486b},
{0xebaf,0xebaf,0x3ddb},
{0xebb0,0xebb0,0x486c},
{0xebb2,0xebb3,0x486d},
{0xebb4,0xebb4,0x3ddd},
{0xebb6,0xebb6,0x3ddf},
{0xebb9,0xebb9,0x3de2},
{0xebbc,0xebbc,0x3de4},
{0xebbd,0xebbd,0x4870},
{0xebbf,0xebbf,0x3de6},
{0xebc2,0xebc2,0x3de9},
{0xebc5,0xebc6,0x4872},
{0xebc7,0xebc7,0x3deb},
{0xebc8,0xebc8,0x4874},
{0xebca,0xebca,0x4875},
{0xebcc,0xebcd,0x3dee},
{0xebce,0xebce,0x4876},
{0xebd0,0xebd0,0x4877},
{0xebd3,0xebd3,0x4878},
{0xebd5,0xebd5,0x4879},
{0xebd7,0xebd8,0x3df4},
{0xebda,0xebdb,0x487a},
{0xebdd,0xebdd,0x3df8},
{0xebdf,0xebdf,0x487c},
{0xebe1,0xebe1,0x487d},
{0xebe4,0xebe4,0x487e},
{0xebe7,0xebe7,0x3dff},
{0xebe8,0xebe9,0x487f},
{0xebeb,0xebec,0x4881},
{0xebee,0xebee,0x3e02},
{0xebef,0xebef,0x4883},
{0xebf1,0xebf1,0x4884},
{0xebf2,0xebf2,0x3e04},
{0xebf5,0xebf5,0x4886},
{0xebf6,0xebf6,0x3e06},
{0xebf9,0xebfb,0x3e09},
{0xebfd,0xebfe,0x3e0d},
{0xec05,0xec06,0x3e14},
{0xec08,0xec09,0x4888},
{0xec0c,0xec0c,0x3e18},
{0xec0e,0xec10,0x3e1a},
{0xec13,0xec13,0x488b},
{0xec14,0xec14,0x3e1f},
{0xec17,0xec18,0x3e22},
{0xec19,0xec1c,0x488c},
{0xec1d,0xec1d,0x3e24},
{0xec20,0xec20,0x4890},
{0xec22,0xec22,0x3e28},
{0xec24,0xec24,0x3e2a},
{0xec26,0xec26,0x3e2c},
{0xec2a,0xec2a,0x3e30},
{0xec2b,0xec2b,0x4891},
{0xec2c,0xec2e,0x3e31},
{0xec2f,0xec2f,0x4892},
{0xec31,0xec31,0x3e35},
{0xec32,0xec32,0x4893},
{0xec35,0xec35,0x3e38},
{0xec37,0xec37,0x3e3a},
{0xec3b,0xec3c,0x3e3e},
{0xec3f,0xec41,0x4894},
{0xec42,0xec42,0x3e42},
{0xec43,0xec43,0x4897},
{0xec44,0xec44,0x43e8},
{0xec45,0xec49,0x4898},
{0xec4b,0xec4c,0x489d},
{0xec4f,0xec4f,0x48a0},
{0xec52,0xec52,0x48a1},
{0xec54,0xec54,0x48a2},
{0xec56,0xec58,0x48a3},
{0xec5a,0xec5a,0x48a6},
{0xec5c,0xec5d,0x48a7},
{0xec5f,0xec60,0x48a9},
{0xec62,0xec63,0x48ac},
{0xec67,0xec67,0x48af},
{0xec68,0xec68,0x3e4d},
{0xec69,0xec73,0x48b0},
{0xec75,0xec76,0x48bc},
{0xec79,0xec79,0x48c0},
{0xec7b,0xec7b,0x48c2},
{0xec7e,0xec7e,0x48c3},
{0xec80,0xec80,0x48c5},
{0xec84,0xec84,0x48c8},
{0xec86,0xec86,0x48ca},
{0xec8d,0xec8d,0x48cc},
{0xec8e,0xec8e,0x3e55},
{0xec91,0xec91,0x48ce},
{0xec92,0xec92,0x3e58},
{0xec93,0xec94,0x48cf},
{0xec95,0xec95,0x3e59},
{0xec96,0xec96,0x48d1},
{0xec98,0xec98,0x48d2},
{0xec99,0xec99,0x3e5b},
{0xec9a,0xec9b,0x48d3},
{0xec9d,0xec9d,0x48d6},
{0xec9e,0xec9e,0x3e5c},
{0xeca0,0xeca0,0x48d7},
{0xeca3,0xeca4,0x48d8},
{0xeca7,0xeca8,0x48da},
{0xecab,0xecab,0x48dc},
{0xecad,0xecad,0x48dd},
{0xecaf,0xecaf,0x3e65},
{0xecb4,0xecb4,0x48df},
{0xecb6,0xecb6,0x3e6b},
{0xecb9,0xecbb,0x48e1},
{0xeccf,0xeccf,0x3e80},
{0xecd4,0xecd4,0x3e85},
{0xecdb,0xecdc,0x3e8c},
{0xecde,0xecde,0x48e4},
{0xecdf,0xece1,0x3e8f},
{0xece5,0xece5,0x3e95},
{0xecf0,0xecf0,0x3ea0},
{0xecf2,0xecf2,0x3ea2},
{0xecf5,0xecf5,0x3ea5},
{0xecfc,0xecfc,0x3eac},
{0xed04,0xed04,0x48e7},
{0xed07,0xed07,0x48e8},
{0xed0a,0xed0a,0x48ea},
{0xed0c,0xed11,0x48eb},
{0xed13,0xed13,0x48f1},
{0xed15,0xed17,0x48f2},
{0xed18,0xed18,0x3eba},
{0xed1a,0xed1a,0x48f5},
{0xed1c,0xed1c,0x48f6},
{0xed1d,0xed1d,0x3ebd},
{0xed1e,0xed1e,0x48f7},
{0xed22,0xed22,0x48f8},
{0xed24,0xed25,0x3ec2},
{0xed26,0xed27,0x48f9},
{0xed29,0xed29,0x3ec5},
{0xed2a,0xed2a,0x48fb},
{0xed2c,0xed2c,0x3ec7},
{0xed2d,0xed2d,0x48fc},
{0xed3b,0xed3b,0x3ecf},
{0xed3d,0xed3d,0x4904},
{0xed41,0xed42,0x4906},
{0xed45,0xed45,0x4908},
{0xed47,0xed47,0x3ed5},
{0xed4a,0xed4b,0x4909},
{0xed4d,0xed4e,0x490b},
{0xed4f,0xed4f,0x3eda},
{0xed51,0xed51,0x490d},
{0xed52,0xed52,0x3edc},
{0xed53,0xed54,0x490e},
{0xed57,0xed58,0x4910},
{0xed5f,0xed5f,0x3ee5},
{0xed62,0xed62,0x3ee8},
{0xed6b,0xed6b,0x3ef1},
{0xed71,0xed71,0x4912},
{0xed72,0xed72,0x3ef7},
{0xed75,0xed75,0x4913},
{0xed77,0xed77,0x4915},
{0xed78,0xed78,0x3efa},
{0xed7a,0xed7a,0x4916},
{0xed81,0xed81,0x4917},
{0xed84,0xed85,0x3f04},
{0xed86,0xed86,0x4918},
{0xed88,0xed88,0x3f07},
{0xed89,0xed8a,0x4919},
{0xed8b,0xed8b,0x3f08},
{0xed8e,0xed8e,0x3f0b},
{0xed91,0xed91,0x3f0e},
{0xeda6,0xeda6,0x491c},
{0xedc6,0xedc6,0x3f41},
{0xedcf,0xedcf,0x3f4a},
{0xedd1,0xedd1,0x491e},
{0xedd7,0xedd7,0x491f},
{0xedda,0xedda,0x3f53},
{0xeddb,0xeddb,0x4920},
{0xeddd,0xeddd,0x4921},
{0xede1,0xede1,0x3f58},
{0xeded,0xeded,0x3f64},
{0xee08,0xee08,0x3f7e},
{0xee11,0xee11,0x3f87},
{0xee15,0xee15,0x4925},
{0xee1b,0xee1b,0x4926},
{0xee20,0xee20,0x3f94},
{0xee22,0xee22,0x4927},
{0xee2a,0xee2a,0x3f9d},
{0xee30,0xee30,0x4928},
{0xee31,0xee31,0x3fa3},
{0xee34,0xee34,0x3fa6},
{0xee48,0xee48,0x4929},
{0xee56,0xee56,0x492a},
{0xee5b,0xee5b,0x492b},
{0xee5d,0xee5d,0x3fcc},
{0xee60,0xee60,0x492c},
{0xee64,0xee64,0x3fd2},
{0xee6a,0xee6a,0x3fd8},
{0xee76,0xee76,0x3fe4},
{0xee7c,0xee7c,0x3fea},
{0xee7e,0xee7e,0x492d},
{0xee91,0xee91,0x3ffd},
{0xee9c,0xee9c,0x492f},
{0xeea1,0xeea6,0x4931},
{0xeea7,0xeea7,0x43b4},
{0xeea8,0xeeaa,0x4937},
{0xeeac,0xeeb1,0x493a},
{0xeeb4,0xeeb4,0x4942},
{0xeeb7,0xeeb7,0x4945},
{0xf303,0xf318,0x44c9},
{0xf325,0xf325,0x496d},
{0xf327,0xf327,0x496f},
{0xf344,0xf344,0x498c},
{0xf346,0xf346,0x498e},
{0xf34a,0xf34b,0x499c},
{0xf3a0,0xf3a1,0x4534},
{0xf3ac,0xf3ac,0x453b},
{0xf3f0,0xf3f0,0x4575},
{0xf3f9,0xf3fa,0x457c},
{0xf3fc,0xf3fc,0x457f},
{0xf408,0xf40b,0x4589},
{0xf43d,0xf43d,0x45b5},
{0xf440,0xf442,0x430b},
{0xf444,0xf444,0x430f},
{0xf446,0xf446,0x4311},
{0xf448,0xf449,0x4313},
{0xf44b,0xf44b,0x4316},
{0xf44d,0xf44d,0x4318},
{0xf450,0xf452,0x431b},
{0xf454,0xf456,0x431f},
{0xf457,0xf457,0x45b7},
{0xf45a,0xf45a,0x4325},
{0xf45b,0xf45b,0x45b8},
{0xf45c,0xf45f,0x4327},
{0xf461,0xf463,0x432c},
{0xf465,0xf466,0x4330},
{0xf469,0xf469,0x4334},
{0xf46c,0xf46d,0x4337},
{0xf46e,0xf46e,0x45b9},
{0xf46f,0xf46f,0x433a},
{0xf474,0xf474,0x45ba},
{0xf477,0xf477,0x45bb},
{0xf478,0xf478,0x4343},
{0xf479,0xf479,0x45bc},
{0xf47b,0xf47b,0x45bd},
{0xf47c,0xf47e,0x4347},
{0xf480,0xf481,0x434b},
{0xf483,0xf483,0x45be},
{0xf484,0xf485,0x434f},
{0xf488,0xf488,0x4353},
{0xf48b,0xf48b,0x4356},
{0xf48d,0xf490,0x4358},
{0xf491,0xf491,0x45bf},
{0xf497,0xf498,0x4362},
{0xf49b,0xf49b,0x4366},
{0xf49e,0xf49e,0x4369},
{0xf4a4,0xf4a6,0x436f},
{0xf4a7,0xf4a7,0x45c1},
{0xf4a9,0xf4aa,0x4374},
{0xf4ad,0xf4ad,0x4378},
{0xf4af,0xf4b0,0x437a},
{0xf4b2,0xf4b2,0x45c3},
{0xf4b3,0xf4b4,0x437e},
{0xf4b7,0xf4b7,0x4382},
{0xf4ba,0xf4c0,0x4385},
{0xf4c1,0xf4c1,0x45c4},
{0xf4c3,0xf4c5,0x45c5},
{0xf4c7,0xf4ca,0x45c9},
{0xf4cc,0xf4cf,0x45ce},
{0xf4d1,0xf4d5,0x45d2},
{0xf4d7,0xf4d7,0x45d8},
{0xf4d9,0xf4da,0x45da},
{0xf4db,0xf4dc,0x438c},
{0xf4dd,0xf4de,0x45dc},
{0xf4df,0xf4df,0x438e},
{0xf4e0,0xf4e0,0x45de},
{0xf4e2,0xf4e2,0x45df},
{0xf4e3,0xf4e3,0x4390},
{0xf4e4,0xf4e4,0x45e0},
{0xf4e5,0xf4e5,0x4391},
{0xf4e6,0xf4e6,0x45e1},
{0xf4e7,0xf4e9,0x4392},
{0xf4ef,0xf4ef,0x45e5},
{0xf4f2,0xf4f2,0x4397},
{0xf4f3,0xf4f3,0x45e8},
{0xf4f8,0xf4f8,0x45eb},
{0xf4fc,0xf4fc,0x45ee},
{0xf4fe,0xf501,0x45f0},
{0xf503,0xf503,0x45f4},
{0xf505,0xf505,0x45f6},
{0xf507,0xf511,0x45f8},
{0xf513,0xf516,0x4604},
{0xf518,0xf51d,0x4609},
{0xf51f,0xf526,0x4610},
{0xf52a,0xf52a,0x461b},
{0xf533,0xf534,0x4624},
{0xf53a,0xf53b,0x44e1},
{0xf53d,0xf53d,0x44e4},
{0xf54e,0xf54e,0x44f5},
{0xf553,0xf553,0x44fa},
{0xf556,0xf556,0x44fc},
{0xf559,0xf55a,0x44ff},
{0xf55e,0xf55e,0x4504},
{0xf565,0xf565,0x450b},
{0xf56b,0xf56b,0x4511},
{0xf572,0xf572,0x4518},
{0xf694,0xf694,0x467c},
{0xf69e,0xf69e,0x4686},
{0xf6ad,0xf6ad,0x4694},
{0xf6b0,0xf6b0,0x4697},
{0xf7e5,0xf7e6,0x36af},
{0xf7e8,0xf7e8,0x36b2},
{0xf7ea,0xf7eb,0x36b4},
{0xf7ed,0xf7ee,0x36b7},
{0xfa0c,0xfa0c,0x274},
{0xfa0d,0xfa0d,0x2381},
{0xfe30,0xfe30,0x6d},
{0xfe31,0xfe31,0x7a},
{0xfe33,0xfe33,0x35af},
{0xfe34,0xfe34,0x35b1},
{0xfe35,0xfe36,0x82},
{0xfe37,0xfe38,0x86},
{0xfe39,0xfe3a,0x8a},
{0xfe3b,0xfe3c,0x8e},
{0xfe3d,0xfe3e,0x92},
{0xfe3f,0xfe40,0x96},
{0xfe41,0xfe42,0x9a},
{0xfe43,0xfe44,0x9e},
{0xfe49,0xfe4a,0xc7},
{0xfe4b,0xfe4c,0xcb},
{0xfe4d,0xfe4e,0xc9},
{0xfe4f,0xfe4f,0x35b2},
{0xfe50,0xfe50,0x70},
{0xfe52,0xfe52,0x72},
{0xfe54,0xfe57,0x74},
{0xfe59,0xfe5e,0xa0},
{0xfe5f,0xfe61,0xcd},
{0xfe62,0xfe66,0xdf},
{0xfe69,0xfe6b,0x10c},
{0xff01,0xff01,0x6c},
{0xff02,0xff02,0x36e4},
{0xff03,0xff03,0xae},
{0xff04,0xff04,0x103},
{0xff05,0xff05,0x108},
{0xff06,0xff06,0xaf},
{0xff07,0xff07,0x36e3},
{0xff08,0xff09,0x80},
{0xff0a,0xff0a,0xb0},
{0xff0b,0xff0b,0xd0},
{0xff0c,0xff0c,0x64},
{0xff0d,0xff0d,0xd1},
{0xff0e,0xff0e,0x67},
{0xff0f,0xff0f,0x101},
{0xff10,0xff19,0x14d},
{0xff1a,0xff1a,0x6a},
{0xff1b,0xff1b,0x69},
{0xff1c,0xff1c,0xd6},
{0xff1d,0xff1d,0xd8},
{0xff1e,0xff1e,0xd7},
{0xff1f,0xff1f,0x6b},
{0xff20,0xff20,0x109},
{0xff21,0xff3a,0x16d},
{0xff3b,0xff3b,0x35be},
{0xff3c,0xff3c,0x102},
{0xff3d,0xff3d,0x35bf},
{0xff3e,0xff3e,0x35b4},
{0xff3f,0xff3f,0xc5},
{0xff41,0xff5a,0x187},
{0xff5b,0xff5b,0x84},
{0xff5c,0xff5c,0x78},
{0xff5d,0xff5d,0x85},
{0xff64,0xff64,0x71},
{0xffe2,0xffe2,0x36e1},
{0xffe4,0xffe4,0x36e2},
};

static pdf_cmap cmap_UniCNS_X = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "UniCNS-X",
	/* usecmap */ "", NULL,
	/* wmode */ 0,
	/* codespaces */ 0, {
		{ 0, 0, 0 },
	},
	16377, 16377, (pdf_range*)cmap_UniCNS_X_ranges,
	0, 0, NULL, /* xranges */
	0, 0, NULL, /* mranges */
	0, 0, NULL, /* table */
	0, 0, 0, NULL /* splay tree */
};
