/* This is an automatically generated file. Do not edit. */

/* GBKp-EUC-H */

static const pdf_range cmap_GBKp_EUC_H_ranges[] = {
{0x20,0x7e,0x1},
};

static pdf_cmap cmap_GBKp_EUC_H = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "GBKp-EUC-H",
	/* usecmap */ "GBK-X", NULL,
	/* wmode */ 0,
	/* codespaces */ 2, {
		{ 1, 0x00, 0x80 },
		{ 2, 0x8140, 0xfefe },
	},
	1, 1, (pdf_range*)cmap_GBKp_EUC_H_ranges,
	0, 0, NULL, /* xranges */
	0, 0, NULL, /* mranges */
	0, 0, NULL, /* table */
	0, 0, 0, NULL /* splay tree */
};
