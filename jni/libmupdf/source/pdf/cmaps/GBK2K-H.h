/* This is an automatically generated file. Do not edit. */

/* GBK2K-H */

static const pdf_range cmap_GBK2K_H_ranges[] = {
{0x20,0x7e,0x1},
{0xa2e3,0xa2e3,0x5751},
};

static const pdf_xrange cmap_GBK2K_H_xranges[] = {
{0x81308436,0x81308436,0x5752},
{0x8138fd38,0x8138fd39,0x579c},
{0x8138fe30,0x8138fe39,0x579e},
{0x81398130,0x81398137,0x57a8},
{0x81398139,0x81398139,0x57b0},
{0x81398230,0x81398239,0x57b1},
{0x81398330,0x81398339,0x57bb},
{0x81398430,0x81398439,0x57c5},
{0x81398530,0x81398539,0x57cf},
{0x81398630,0x81398639,0x57d9},
{0x81398730,0x81398739,0x57e3},
{0x81398830,0x81398839,0x57ed},
{0x81398930,0x81398939,0x57f7},
{0x81398b32,0x81398b32,0x1042},
{0x81398b33,0x81398b33,0x1263},
{0x81398b34,0x81398b34,0x1272},
{0x81398b35,0x81398b35,0x1265},
{0x81398b36,0x81398b36,0x1059},
{0x81398b37,0x81398b37,0x2793},
{0x81398b38,0x81398b38,0x63d},
{0x81398b39,0x81398b39,0x1303},
{0x81398c30,0x81398c30,0xca6},
{0x81398c31,0x81398c31,0x638},
{0x81398c32,0x81398c32,0xcc6},
{0x81398c33,0x81398c33,0x3d6},
{0x81398c34,0x81398c34,0x129d},
{0x81398c35,0x81398c35,0x1314},
{0x81398c36,0x81398c36,0x130f},
{0x81398c37,0x81398c37,0x82b},
{0x81398c38,0x81398c38,0x1399},
{0x81398c39,0x81398c39,0x597},
{0x81398d30,0x81398d30,0x9ef},
{0x81398d31,0x81398d31,0x12fc},
{0x81398d32,0x81398d32,0x1266},
{0x81398d33,0x81398d33,0x1284},
{0x81398d34,0x81398d34,0x2a1a},
{0x81398d35,0x81398d35,0xd45},
{0x81398d36,0x81398d36,0x47e},
{0x81398d37,0x81398d37,0x1352},
{0x81398d38,0x81398d38,0x4cc},
{0x81398d39,0x81398d39,0x139c},
{0x81398e30,0x81398e30,0x10bb},
{0x81398e31,0x81398e31,0x967},
{0x81398e32,0x81398e32,0x1593},
{0x81398e33,0x81398e33,0xe72},
{0x81398e34,0x81398e34,0xd56},
{0x81398e35,0x81398e35,0x161c},
{0x81398e36,0x81398e36,0x2d2d},
{0x81398e37,0x81398e37,0xf13},
{0x81398e38,0x81398e38,0x576},
{0x81398e39,0x81398e39,0xb6f},
{0x81398f30,0x81398f30,0x1230},
{0x81398f31,0x81398f31,0x172e},
{0x81398f32,0x81398f32,0x56a},
{0x81398f33,0x81398f33,0xf6c},
{0x81398f34,0x81398f34,0x14b6},
{0x81398f35,0x81398f35,0xd43},
{0x81398f36,0x81398f36,0x1774},
{0x81398f37,0x81398f37,0xcf6},
{0x81398f38,0x81398f38,0x1815},
{0x81398f39,0x81398f39,0x6fd},
{0x81399030,0x81399030,0x82d},
{0x81399031,0x81399031,0x8be},
{0x81399032,0x81399032,0x6c4},
{0x81399033,0x81399033,0x1813},
{0x81399034,0x81399034,0x73c},
{0x81399035,0x81399035,0x1398},
{0x81399036,0x81399036,0x14ad},
{0x81399037,0x81399037,0x14f6},
{0x81399038,0x81399038,0x706},
{0x81399039,0x81399039,0x1762},
{0x81399130,0x81399130,0x15ee},
{0x81399131,0x81399131,0x15e0},
{0x81399132,0x81399132,0xf8f},
{0x81399133,0x81399133,0x6e5},
{0x81399134,0x81399134,0x3292},
{0x81399135,0x81399135,0xd6d},
{0x81399136,0x81399136,0x11a6},
{0x81399137,0x81399137,0x1909},
{0x81399138,0x81399138,0xed3},
{0x81399139,0x81399139,0x5f6},
{0x81399230,0x81399230,0x8c0},
{0x81399231,0x81399231,0x65a},
{0x81399232,0x81399232,0xeed},
{0x81399233,0x81399233,0xcb0},
{0x81399234,0x81399234,0x10fe},
{0x81399235,0x81399235,0x1105},
{0x81399236,0x81399236,0xb21},
{0x81399237,0x81399237,0xc34},
{0x81399238,0x81399238,0x11b8},
{0x81399239,0x81399239,0x578},
{0x81399330,0x81399330,0x19bd},
{0x81399331,0x81399331,0xef2},
{0x81399332,0x81399332,0x431},
{0x81399333,0x81399333,0xab0},
{0x81399334,0x81399334,0xd66},
{0x81399335,0x81399335,0xc17},
{0x81399336,0x81399336,0xda3},
{0x81399337,0x81399337,0x805},
{0x81399338,0x81399338,0x1203},
{0x81399339,0x81399339,0x6b3},
{0x81399430,0x81399430,0x1269},
{0x81399431,0x81399431,0x169d},
{0x81399432,0x81399432,0xbcb},
{0x81399433,0x81399433,0xfe9},
{0x81399434,0x81399434,0xb64},
{0x81399435,0x81399435,0xc89},
{0x81399436,0x81399436,0xfc9},
{0x81399437,0x81399437,0x10d6},
{0x81399438,0x81399438,0x728},
{0x81399439,0x81399439,0xe90},
{0x81399530,0x81399530,0x6c5},
{0x81399531,0x81399531,0xd33},
{0x81399532,0x81399532,0x10a8},
{0x81399533,0x81399533,0xe42},
{0x81399534,0x81399534,0x1bc6},
{0x81399535,0x81399535,0x1b60},
{0x81399536,0x81399536,0x3ebf},
{0x81399537,0x81399537,0x3e2},
{0x81399538,0x81399538,0xbc3},
{0x81399539,0x81399539,0xaf8},
{0x81399630,0x81399630,0xb22},
{0x81399631,0x81399631,0xab1},
{0x81399632,0x81399632,0xd4f},
{0x81399633,0x81399633,0xd46},
{0x81399634,0x81399634,0xd55},
{0x81399635,0x81399635,0x40cb},
{0x81399636,0x81399636,0x783},
{0x81399637,0x81399637,0xfd1},
{0x81399638,0x81399638,0x9eb},
{0x81399639,0x81399639,0x11f0},
{0x81399730,0x81399730,0xadc},
{0x81399731,0x81399731,0x1ce7},
{0x81399732,0x81399732,0x1c5e},
{0x81399733,0x81399733,0xea9},
{0x81399734,0x81399734,0x101b},
{0x81399735,0x81399735,0x10d5},
{0x81399736,0x81399736,0x9ba},
{0x81399737,0x81399737,0x637},
{0x81399738,0x81399738,0x1bcb},
{0x81399739,0x81399739,0x639},
{0x81399830,0x81399830,0x1a1e},
{0x81399831,0x81399831,0xcbd},
{0x81399832,0x81399832,0x4e1},
{0x81399833,0x81399833,0x1231},
{0x81399834,0x81399834,0x11c0},
{0x81399835,0x81399835,0x8f9},
{0x81399836,0x81399836,0xd19},
{0x81399837,0x81399837,0x1618},
{0x81399838,0x81399838,0x11da},
{0x81399839,0x81399839,0x1cdc},
{0x81399930,0x81399930,0xce4},
{0x81399931,0x81399931,0x45a2},
{0x81399932,0x81399932,0x1bf0},
{0x81399933,0x81399933,0x50b},
{0x81399934,0x81399934,0xfd3},
{0x81399935,0x81399935,0xf9b},
{0x81399936,0x81399936,0x1049},
{0x81399937,0x81399937,0x49dc},
{0x81399938,0x81399938,0x1f96},
{0x81399939,0x81399939,0x898},
{0x81399a30,0x81399a30,0xffd},
{0x81399a31,0x81399a31,0x721},
{0x81399a32,0x81399a32,0x5f8},
{0x81399a33,0x81399a33,0x1d15},
{0x81399a34,0x81399a34,0x1d50},
{0x81399a35,0x81399a35,0x1e3b},
{0x81399a36,0x81399a36,0x505},
{0x81399a37,0x81399a37,0x123c},
{0x81399a38,0x81399a38,0x1240},
{0x81399a39,0x81399a39,0xd26},
{0x81399b30,0x81399b30,0x1e7b},
{0x81399b31,0x81399b31,0xf8c},
{0x81399b32,0x81399b32,0x4e2},
{0x81399b33,0x81399b33,0x4d13},
{0x81399b34,0x81399b34,0x105f},
{0x81399b35,0x81399b35,0x10b4},
{0x81399b36,0x81399b36,0x4e18},
{0x81399b37,0x81399b37,0x9da},
{0x81399b38,0x81399b38,0x8c1},
{0x81399b39,0x81399b39,0x1e75},
{0x81399c30,0x81399c30,0x207d},
{0x81399c31,0x81399c31,0x6b2},
{0x81399c32,0x81399c32,0x9ee},
{0x81399c33,0x81399c33,0x1d79},
{0x81399c34,0x81399c34,0x10cf},
{0x81399c35,0x81399c35,0x50ea},
{0x81399c36,0x81399c36,0x664},
{0x81399c37,0x81399c37,0xaeb},
{0x81399c38,0x81399c38,0x6ea},
{0x81399c39,0x81399c39,0x218d},
{0x81399d30,0x81399d30,0x8f1},
{0x81399d31,0x81399d31,0x107b},
{0x81399d32,0x81399d32,0x2209},
{0x81399d33,0x81399d33,0x1f00},
{0x81399d34,0x81399d34,0x1ef4},
{0x81399d35,0x81399d35,0xd4a},
{0x81399d36,0x81399d36,0xd6e},
{0x81399d37,0x81399d37,0xf50},
{0x81399d38,0x81399d38,0x206d},
{0x81399d39,0x81399d39,0x720},
{0x81399e30,0x81399e30,0x6da},
{0x81399e31,0x81399e31,0x1dec},
{0x81399e32,0x81399e32,0x1ed0},
{0x81399e33,0x81399e33,0x139b},
{0x81399e34,0x81399e34,0x1260},
{0x81399e35,0x81399e35,0x746},
{0x81399e36,0x81399e36,0x2239},
{0x81399e37,0x81399e37,0x209c},
{0x81399e38,0x81399e38,0x2688},
{0x81399e39,0x81399e39,0xa60},
{0x81399f30,0x81399f30,0x2071},
{0x81399f31,0x81399f31,0xa90},
{0x81399f32,0x81399f32,0x5586},
{0x81399f33,0x81399f33,0xd89},
{0x81399f34,0x81399f34,0x791},
{0x81399f35,0x81399f35,0x1a55},
{0x81399f36,0x81399f36,0x2618},
{0x81399f37,0x81399f37,0x5e5},
{0x81399f38,0x81399f38,0x71d},
{0x81399f39,0x81399f39,0xd8a},
{0x8139a030,0x8139a030,0x430},
{0x8139a031,0x8139a031,0x20c6},
{0x8139a032,0x8139a032,0x1e86},
{0x8139a033,0x8139a033,0x2037},
{0x8139a034,0x8139a034,0x1f34},
{0x8139a035,0x8139a035,0x12f3},
{0x8139a533,0x8139a535,0x577b},
{0x8139a538,0x8139a539,0x577e},
{0x8139a630,0x8139a630,0x5780},
{0x8139a634,0x8139a634,0x5755},
{0x8139a636,0x8139a636,0x5767},
{0x8139a735,0x8139a735,0x5776},
{0x8139a736,0x8139a736,0x5778},
{0x8139a737,0x8139a737,0x5777},
{0x8139a738,0x8139a738,0x5779},
{0x8139a836,0x8139a838,0x5781},
{0x8139b434,0x8139b439,0x5784},
{0x8139b530,0x8139b539,0x578a},
{0x8139b630,0x8139b637,0x5794},
{0x8139ee39,0x8139ee39,0x5801},
{0x8139ef30,0x8139ef39,0x5802},
{0x8139f030,0x8139f039,0x580c},
{0x8139f130,0x8139f139,0x5816},
{0x8139f230,0x8139f239,0x5820},
{0x8139f330,0x8139f339,0x582a},
{0x8139f430,0x8139f439,0x5834},
{0x8139f530,0x8139f539,0x583e},
{0x8139f630,0x8139f639,0x5848},
{0x8139f730,0x8139f739,0x5852},
{0x8139f830,0x8139f839,0x585c},
{0x8139f930,0x8139f939,0x5866},
{0x8139fa30,0x8139fa39,0x5870},
{0x8139fb30,0x8139fb39,0x587a},
{0x8139fc30,0x8139fc39,0x5884},
{0x8139fd30,0x8139fd39,0x588e},
{0x8139fe30,0x8139fe39,0x5898},
{0x82308130,0x82308139,0x58a2},
{0x82308230,0x82308239,0x58ac},
{0x82308330,0x82308339,0x58b6},
{0x82308430,0x82308439,0x58c0},
{0x82308530,0x82308539,0x58ca},
{0x82308630,0x82308639,0x58d4},
{0x82308730,0x82308739,0x58de},
{0x82308830,0x82308839,0x58e8},
{0x82308930,0x82308939,0x58f2},
{0x82308a30,0x82308a39,0x58fc},
{0x82308b30,0x82308b39,0x5906},
{0x82308c30,0x82308c39,0x5910},
{0x82308d30,0x82308d39,0x591a},
{0x82308e30,0x82308e39,0x5924},
{0x82308f30,0x82308f39,0x592e},
{0x82309030,0x82309039,0x5938},
{0x82309130,0x82309139,0x5942},
{0x82309230,0x82309239,0x594c},
{0x82309330,0x82309339,0x5956},
{0x82309430,0x82309439,0x5960},
{0x82309530,0x82309539,0x596a},
{0x82309630,0x82309639,0x5974},
{0x82309730,0x82309739,0x597e},
{0x82309830,0x82309839,0x5988},
{0x82309930,0x82309939,0x5992},
{0x82309a30,0x82309a39,0x599c},
{0x82309b30,0x82309b39,0x59a6},
{0x82309c30,0x82309c39,0x59b0},
{0x82309d30,0x82309d39,0x59ba},
{0x82309e30,0x82309e39,0x59c4},
{0x82309f30,0x82309f39,0x59ce},
{0x8230a030,0x8230a039,0x59d8},
{0x8230a130,0x8230a139,0x59e2},
{0x8230a230,0x8230a239,0x59ec},
{0x8230a330,0x8230a339,0x59f6},
{0x8230a430,0x8230a439,0x5a00},
{0x8230a530,0x8230a539,0x5a0a},
{0x8230a630,0x8230a639,0x5a14},
{0x8230a730,0x8230a739,0x5a1e},
{0x8230a830,0x8230a839,0x5a28},
{0x8230a930,0x8230a939,0x5a32},
{0x8230aa30,0x8230aa39,0x5a3c},
{0x8230ab30,0x8230ab39,0x5a46},
{0x8230ac30,0x8230ac39,0x5a50},
{0x8230ad30,0x8230ad39,0x5a5a},
{0x8230ae30,0x8230ae39,0x5a64},
{0x8230af30,0x8230af39,0x5a6e},
{0x8230b030,0x8230b039,0x5a78},
{0x8230b130,0x8230b139,0x5a82},
{0x8230b230,0x8230b239,0x5a8c},
{0x8230b330,0x8230b339,0x5a96},
{0x8230b430,0x8230b439,0x5aa0},
{0x8230b530,0x8230b539,0x5aaa},
{0x8230b630,0x8230b639,0x5ab4},
{0x8230b730,0x8230b739,0x5abe},
{0x8230b830,0x8230b839,0x5ac8},
{0x8230b930,0x8230b939,0x5ad2},
{0x8230ba30,0x8230ba39,0x5adc},
{0x8230bb30,0x8230bb39,0x5ae6},
{0x8230bc30,0x8230bc39,0x5af0},
{0x8230bd30,0x8230bd39,0x5afa},
{0x8230be30,0x8230be39,0x5b04},
{0x8230bf30,0x8230bf39,0x5b0e},
{0x8230c030,0x8230c039,0x5b18},
{0x8230c130,0x8230c139,0x5b22},
{0x8230c230,0x8230c239,0x5b2c},
{0x8230c330,0x8230c339,0x5b36},
{0x8230c430,0x8230c439,0x5b40},
{0x8230c530,0x8230c539,0x5b4a},
{0x8230c630,0x8230c639,0x5b54},
{0x8230c730,0x8230c739,0x5b5e},
{0x8230c830,0x8230c839,0x5b68},
{0x8230c930,0x8230c939,0x5b72},
{0x8230ca30,0x8230ca39,0x5b7c},
{0x8230cb30,0x8230cb39,0x5b86},
{0x8230cc30,0x8230cc39,0x5b90},
{0x8230cd30,0x8230cd39,0x5b9a},
{0x8230ce30,0x8230ce39,0x5ba4},
{0x8230cf30,0x8230cf39,0x5bae},
{0x8230d030,0x8230d039,0x5bb8},
{0x8230d130,0x8230d139,0x5bc2},
{0x8230d230,0x8230d239,0x5bcc},
{0x8230d330,0x8230d339,0x5bd6},
{0x8230d430,0x8230d439,0x5be0},
{0x8230d530,0x8230d539,0x5bea},
{0x8230d630,0x8230d639,0x5bf4},
{0x8230d730,0x8230d739,0x5bfe},
{0x8230d830,0x8230d839,0x5c08},
{0x8230d930,0x8230d939,0x5c12},
{0x8230da30,0x8230da39,0x5c1c},
{0x8230db30,0x8230db39,0x5c26},
{0x8230dc30,0x8230dc39,0x5c30},
{0x8230dd30,0x8230dd39,0x5c3a},
{0x8230de30,0x8230de39,0x5c44},
{0x8230df30,0x8230df39,0x5c4e},
{0x8230e030,0x8230e039,0x5c58},
{0x8230e130,0x8230e139,0x5c62},
{0x8230e230,0x8230e239,0x5c6c},
{0x8230e330,0x8230e339,0x5c76},
{0x8230e430,0x8230e439,0x5c80},
{0x8230e530,0x8230e539,0x5c8a},
{0x8230e630,0x8230e639,0x5c94},
{0x8230e730,0x8230e739,0x5c9e},
{0x8230e830,0x8230e839,0x5ca8},
{0x8230e930,0x8230e939,0x5cb2},
{0x8230ea30,0x8230ea39,0x5cbc},
{0x8230eb30,0x8230eb39,0x5cc6},
{0x8230ec30,0x8230ec39,0x5cd0},
{0x8230ed30,0x8230ed39,0x5cda},
{0x8230ee30,0x8230ee39,0x5ce4},
{0x8230ef30,0x8230ef39,0x5cee},
{0x8230f030,0x8230f039,0x5cf8},
{0x8230f130,0x8230f139,0x5d02},
{0x8230f230,0x8230f239,0x5d0c},
{0x8230f330,0x8230f339,0x5d16},
{0x8230f430,0x8230f439,0x5d20},
{0x8230f530,0x8230f539,0x5d2a},
{0x8230f630,0x8230f639,0x5d34},
{0x8230f730,0x8230f739,0x5d3e},
{0x8230f830,0x8230f839,0x5d48},
{0x8230f930,0x8230f939,0x5d52},
{0x8230fa30,0x8230fa39,0x5d5c},
{0x8230fb30,0x8230fb39,0x5d66},
{0x8230fc30,0x8230fc39,0x5d70},
{0x8230fd30,0x8230fd39,0x5d7a},
{0x8230fe30,0x8230fe39,0x5d84},
{0x82318130,0x82318139,0x5d8e},
{0x82318230,0x82318239,0x5d98},
{0x82318330,0x82318339,0x5da2},
{0x82318430,0x82318439,0x5dac},
{0x82318530,0x82318539,0x5db6},
{0x82318630,0x82318639,0x5dc0},
{0x82318730,0x82318739,0x5dca},
{0x82318830,0x82318839,0x5dd4},
{0x82318930,0x82318939,0x5dde},
{0x82318a30,0x82318a39,0x5de8},
{0x82318b30,0x82318b39,0x5df2},
{0x82318c30,0x82318c39,0x5dfc},
{0x82318d30,0x82318d39,0x5e06},
{0x82318e30,0x82318e39,0x5e10},
{0x82318f30,0x82318f39,0x5e1a},
{0x82319030,0x82319039,0x5e24},
{0x82319130,0x82319139,0x5e2e},
{0x82319230,0x82319239,0x5e38},
{0x82319330,0x82319339,0x5e42},
{0x82319430,0x82319439,0x5e4c},
{0x82319530,0x82319539,0x5e56},
{0x82319630,0x82319639,0x5e60},
{0x82319730,0x82319739,0x5e6a},
{0x82319830,0x82319839,0x5e74},
{0x82319930,0x82319939,0x5e7e},
{0x82319a30,0x82319a39,0x5e88},
{0x82319b30,0x82319b39,0x5e92},
{0x82319c30,0x82319c39,0x5e9c},
{0x82319d30,0x82319d39,0x5ea6},
{0x82319e30,0x82319e39,0x5eb0},
{0x82319f30,0x82319f39,0x5eba},
{0x8231a030,0x8231a039,0x5ec4},
{0x8231a130,0x8231a139,0x5ece},
{0x8231a230,0x8231a239,0x5ed8},
{0x8231a330,0x8231a339,0x5ee2},
{0x8231a430,0x8231a439,0x5eec},
{0x8231a530,0x8231a539,0x5ef6},
{0x8231a630,0x8231a639,0x5f00},
{0x8231a730,0x8231a739,0x5f0a},
{0x8231a830,0x8231a839,0x5f14},
{0x8231a930,0x8231a939,0x5f1e},
{0x8231aa30,0x8231aa39,0x5f28},
{0x8231ab30,0x8231ab39,0x5f32},
{0x8231ac30,0x8231ac39,0x5f3c},
{0x8231ad30,0x8231ad39,0x5f46},
{0x8231ae30,0x8231ae39,0x5f50},
{0x8231af30,0x8231af39,0x5f5a},
{0x8231b030,0x8231b039,0x5f64},
{0x8231b130,0x8231b139,0x5f6e},
{0x8231b230,0x8231b239,0x5f78},
{0x8231b330,0x8231b339,0x5f82},
{0x8231b430,0x8231b439,0x5f8c},
{0x8231b530,0x8231b539,0x5f96},
{0x8231b630,0x8231b639,0x5fa0},
{0x8231b730,0x8231b739,0x5faa},
{0x8231b830,0x8231b839,0x5fb4},
{0x8231b930,0x8231b939,0x5fbe},
{0x8231ba30,0x8231ba39,0x5fc8},
{0x8231bb30,0x8231bb39,0x5fd2},
{0x8231bc30,0x8231bc39,0x5fdc},
{0x8231bd30,0x8231bd39,0x5fe6},
{0x8231be30,0x8231be39,0x5ff0},
{0x8231bf30,0x8231bf39,0x5ffa},
{0x8231c030,0x8231c039,0x6004},
{0x8231c130,0x8231c139,0x600e},
{0x8231c230,0x8231c239,0x6018},
{0x8231c330,0x8231c339,0x6022},
{0x8231c430,0x8231c439,0x602c},
{0x8231c530,0x8231c539,0x6036},
{0x8231c630,0x8231c639,0x6040},
{0x8231c730,0x8231c739,0x604a},
{0x8231c830,0x8231c839,0x6054},
{0x8231c930,0x8231c939,0x605e},
{0x8231ca30,0x8231ca39,0x6068},
{0x8231cb30,0x8231cb39,0x6072},
{0x8231cc30,0x8231cc39,0x607c},
{0x8231cd30,0x8231cd39,0x6086},
{0x8231ce30,0x8231ce39,0x6090},
{0x8231cf30,0x8231cf39,0x609a},
{0x8231d030,0x8231d039,0x60a4},
{0x8231d130,0x8231d139,0x60ae},
{0x8231d230,0x8231d239,0x60b8},
{0x8231d330,0x8231d339,0x60c2},
{0x8231d430,0x8231d439,0x60cc},
{0x8231d530,0x8231d539,0x60d6},
{0x8231d630,0x8231d639,0x60e0},
{0x8231d730,0x8231d739,0x60ea},
{0x8231d830,0x8231d839,0x60f4},
{0x8231d930,0x8231d939,0x60fe},
{0x8231da30,0x8231da39,0x6108},
{0x8231db30,0x8231db39,0x6112},
{0x8231dc30,0x8231dc39,0x611c},
{0x8231dd30,0x8231dd39,0x6126},
{0x8231de30,0x8231de39,0x6130},
{0x8231df30,0x8231df39,0x613a},
{0x8231e030,0x8231e039,0x6144},
{0x8231e130,0x8231e139,0x614e},
{0x8231e230,0x8231e239,0x6158},
{0x8231e330,0x8231e339,0x6162},
{0x8231e430,0x8231e439,0x616c},
{0x8231e530,0x8231e539,0x6176},
{0x8231e630,0x8231e639,0x6180},
{0x8231e730,0x8231e739,0x618a},
{0x8231e830,0x8231e839,0x6194},
{0x8231e930,0x8231e939,0x619e},
{0x8231ea30,0x8231ea39,0x61a8},
{0x8231eb30,0x8231eb39,0x61b2},
{0x8231ec30,0x8231ec39,0x61bc},
{0x8231ed30,0x8231ed39,0x61c6},
{0x8231ee30,0x8231ee39,0x61d0},
{0x8231ef30,0x8231ef39,0x61da},
{0x8231f030,0x8231f039,0x61e4},
{0x8231f130,0x8231f139,0x61ee},
{0x8231f230,0x8231f239,0x61f8},
{0x8231f330,0x8231f339,0x6202},
{0x8231f430,0x8231f439,0x620c},
{0x8231f530,0x8231f539,0x6216},
{0x8231f630,0x8231f639,0x6220},
{0x8231f730,0x8231f739,0x622a},
{0x8231f830,0x8231f839,0x6234},
{0x8231f930,0x8231f939,0x623e},
{0x8231fa30,0x8231fa39,0x6248},
{0x8231fb30,0x8231fb39,0x6252},
{0x8231fc30,0x8231fc39,0x625c},
{0x8231fd30,0x8231fd39,0x6266},
{0x8231fe30,0x8231fe39,0x6270},
{0x82328130,0x82328139,0x627a},
{0x82328230,0x82328239,0x6284},
{0x82328330,0x82328339,0x628e},
{0x82328430,0x82328439,0x6298},
{0x82328530,0x82328539,0x62a2},
{0x82328630,0x82328639,0x62ac},
{0x82328730,0x82328739,0x62b6},
{0x82328830,0x82328839,0x62c0},
{0x82328930,0x82328939,0x62ca},
{0x82328a30,0x82328a39,0x62d4},
{0x82328b30,0x82328b39,0x62de},
{0x82328c30,0x82328c39,0x62e8},
{0x82328d30,0x82328d39,0x62f2},
{0x82328e30,0x82328e39,0x62fc},
{0x82328f30,0x82328f39,0x6306},
{0x82329030,0x82329039,0x6310},
{0x82329130,0x82329139,0x631a},
{0x82329230,0x82329239,0x6324},
{0x82329330,0x82329339,0x632e},
{0x82329430,0x82329439,0x6338},
{0x82329530,0x82329539,0x6342},
{0x82329630,0x82329639,0x634c},
{0x82329730,0x82329739,0x6356},
{0x82329830,0x82329839,0x6360},
{0x82329930,0x82329939,0x636a},
{0x82329a30,0x82329a39,0x6374},
{0x82329b30,0x82329b39,0x637e},
{0x82329c30,0x82329c39,0x6388},
{0x82329d30,0x82329d39,0x6392},
{0x82329e30,0x82329e39,0x639c},
{0x82329f30,0x82329f39,0x63a6},
{0x8232a030,0x8232a039,0x63b0},
{0x8232a130,0x8232a139,0x63ba},
{0x8232a230,0x8232a239,0x63c4},
{0x8232a330,0x8232a339,0x63ce},
{0x8232a430,0x8232a439,0x63d8},
{0x8232a530,0x8232a539,0x63e2},
{0x8232a630,0x8232a639,0x63ec},
{0x8232a730,0x8232a739,0x63f6},
{0x8232a830,0x8232a839,0x6400},
{0x8232a930,0x8232a939,0x640a},
{0x8232aa30,0x8232aa39,0x6414},
{0x8232ab30,0x8232ab39,0x641e},
{0x8232ac30,0x8232ac39,0x6428},
{0x8232ad30,0x8232ad39,0x6432},
{0x8232ae30,0x8232ae39,0x643c},
{0x8232af30,0x8232af39,0x6446},
{0x8232b030,0x8232b039,0x6450},
{0x8232b130,0x8232b139,0x645a},
{0x8232b230,0x8232b239,0x6464},
{0x8232b330,0x8232b339,0x646e},
{0x8232b430,0x8232b439,0x6478},
{0x8232b530,0x8232b539,0x6482},
{0x8232b630,0x8232b639,0x648c},
{0x8232b730,0x8232b739,0x6496},
{0x8232b830,0x8232b839,0x64a0},
{0x8232b930,0x8232b939,0x64aa},
{0x8232ba30,0x8232ba39,0x64b4},
{0x8232bb30,0x8232bb39,0x64be},
{0x8232bc30,0x8232bc39,0x64c8},
{0x8232bd30,0x8232bd39,0x64d2},
{0x8232be30,0x8232be39,0x64dc},
{0x8232bf30,0x8232bf39,0x64e6},
{0x8232c030,0x8232c039,0x64f0},
{0x8232c130,0x8232c139,0x64fa},
{0x8232c230,0x8232c239,0x6504},
{0x8232c330,0x8232c339,0x650e},
{0x8232c430,0x8232c439,0x6518},
{0x8232c530,0x8232c539,0x6522},
{0x8232c630,0x8232c639,0x652c},
{0x8232c730,0x8232c739,0x6536},
{0x8232c830,0x8232c839,0x6540},
{0x8232c930,0x8232c939,0x654a},
{0x8232ca30,0x8232ca39,0x6554},
{0x8232cb30,0x8232cb39,0x655e},
{0x8232cc30,0x8232cc39,0x6568},
{0x8232cd30,0x8232cd39,0x6572},
{0x8232ce30,0x8232ce39,0x657c},
{0x8232cf30,0x8232cf39,0x6586},
{0x8232d030,0x8232d039,0x6590},
{0x8232d130,0x8232d139,0x659a},
{0x8232d230,0x8232d239,0x65a4},
{0x8232d330,0x8232d339,0x65ae},
{0x8232d430,0x8232d439,0x65b8},
{0x8232d530,0x8232d539,0x65c2},
{0x8232d630,0x8232d639,0x65cc},
{0x8232d730,0x8232d739,0x65d6},
{0x8232d830,0x8232d839,0x65e0},
{0x8232d930,0x8232d939,0x65ea},
{0x8232da30,0x8232da39,0x65f4},
{0x8232db30,0x8232db39,0x65fe},
{0x8232dc30,0x8232dc39,0x6608},
{0x8232dd30,0x8232dd39,0x6612},
{0x8232de30,0x8232de39,0x661c},
{0x8232df30,0x8232df39,0x6626},
{0x8232e030,0x8232e039,0x6630},
{0x8232e130,0x8232e139,0x663a},
{0x8232e230,0x8232e239,0x6644},
{0x8232e330,0x8232e339,0x664e},
{0x8232e430,0x8232e439,0x6658},
{0x8232e530,0x8232e539,0x6662},
{0x8232e630,0x8232e639,0x666c},
{0x8232e730,0x8232e739,0x6676},
{0x8232e830,0x8232e839,0x6680},
{0x8232e930,0x8232e939,0x668a},
{0x8232ea30,0x8232ea39,0x6694},
{0x8232eb30,0x8232eb39,0x669e},
{0x8232ec30,0x8232ec39,0x66a8},
{0x8232ed30,0x8232ed39,0x66b2},
{0x8232ee30,0x8232ee39,0x66bc},
{0x8232ef30,0x8232ef39,0x66c6},
{0x8232f030,0x8232f039,0x66d0},
{0x8232f130,0x8232f139,0x66da},
{0x8232f230,0x8232f239,0x66e4},
{0x8232f330,0x8232f339,0x66ee},
{0x8232f430,0x8232f439,0x66f8},
{0x8232f530,0x8232f539,0x6702},
{0x8232f630,0x8232f639,0x670c},
{0x8232f730,0x8232f739,0x6716},
{0x8232f830,0x8232f839,0x6720},
{0x8232f930,0x8232f939,0x672a},
{0x8232fa30,0x8232fa39,0x6734},
{0x8232fb30,0x8232fb39,0x673e},
{0x8232fc30,0x8232fc39,0x6748},
{0x8232fd30,0x8232fd39,0x6752},
{0x8232fe30,0x8232fe39,0x675c},
{0x82338130,0x82338139,0x6766},
{0x82338230,0x82338239,0x6770},
{0x82338330,0x82338339,0x677a},
{0x82338430,0x82338439,0x6784},
{0x82338530,0x82338539,0x678e},
{0x82338630,0x82338639,0x6798},
{0x82338730,0x82338739,0x67a2},
{0x82338830,0x82338839,0x67ac},
{0x82338930,0x82338939,0x67b6},
{0x82338a30,0x82338a39,0x67c0},
{0x82338b30,0x82338b39,0x67ca},
{0x82338c30,0x82338c39,0x67d4},
{0x82338d30,0x82338d39,0x67de},
{0x82338e30,0x82338e39,0x67e8},
{0x82338f30,0x82338f39,0x67f2},
{0x82339030,0x82339039,0x67fc},
{0x82339130,0x82339139,0x6806},
{0x82339230,0x82339239,0x6810},
{0x82339330,0x82339339,0x681a},
{0x82339430,0x82339439,0x6824},
{0x82339530,0x82339539,0x682e},
{0x82339630,0x82339639,0x6838},
{0x82339730,0x82339739,0x6842},
{0x82339830,0x82339839,0x684c},
{0x82339930,0x82339939,0x6856},
{0x82339a30,0x82339a39,0x6860},
{0x82339b30,0x82339b39,0x686a},
{0x82339c30,0x82339c39,0x6874},
{0x82339d30,0x82339d39,0x687e},
{0x82339e30,0x82339e39,0x6888},
{0x82339f30,0x82339f39,0x6892},
{0x8233a030,0x8233a039,0x689c},
{0x8233a130,0x8233a139,0x68a6},
{0x8233a230,0x8233a239,0x68b0},
{0x8233a330,0x8233a339,0x68ba},
{0x8233a430,0x8233a439,0x68c4},
{0x8233a530,0x8233a539,0x68ce},
{0x8233a630,0x8233a639,0x68d8},
{0x8233a730,0x8233a739,0x68e2},
{0x8233a830,0x8233a839,0x68ec},
{0x8233a930,0x8233a939,0x68f6},
{0x8233aa30,0x8233aa39,0x6900},
{0x8233ab30,0x8233ab39,0x690a},
{0x8233ac30,0x8233ac39,0x6914},
{0x8233ad30,0x8233ad39,0x691e},
{0x8233ae30,0x8233ae39,0x6928},
{0x8233af30,0x8233af39,0x6932},
{0x8233b030,0x8233b039,0x693c},
{0x8233b130,0x8233b139,0x6946},
{0x8233b230,0x8233b239,0x6950},
{0x8233b330,0x8233b339,0x695a},
{0x8233b430,0x8233b439,0x6964},
{0x8233b530,0x8233b539,0x696e},
{0x8233b630,0x8233b639,0x6978},
{0x8233b730,0x8233b739,0x6982},
{0x8233b830,0x8233b839,0x698c},
{0x8233b930,0x8233b939,0x6996},
{0x8233ba30,0x8233ba39,0x69a0},
{0x8233bb30,0x8233bb39,0x69aa},
{0x8233bc30,0x8233bc39,0x69b4},
{0x8233bd30,0x8233bd39,0x69be},
{0x8233be30,0x8233be39,0x69c8},
{0x8233bf30,0x8233bf39,0x69d2},
{0x8233c030,0x8233c039,0x69dc},
{0x8233c130,0x8233c139,0x69e6},
{0x8233c230,0x8233c239,0x69f0},
{0x8233c330,0x8233c339,0x69fa},
{0x8233c430,0x8233c439,0x6a04},
{0x8233c530,0x8233c539,0x6a0e},
{0x8233c630,0x8233c639,0x6a18},
{0x8233c730,0x8233c739,0x6a22},
{0x8233c830,0x8233c839,0x6a2c},
{0x8233c930,0x8233c939,0x6a36},
{0x8233ca30,0x8233ca39,0x6a40},
{0x8233cb30,0x8233cb39,0x6a4a},
{0x8233cc30,0x8233cc39,0x6a54},
{0x8233cd30,0x8233cd39,0x6a5e},
{0x8233ce30,0x8233ce39,0x6a68},
{0x8233cf30,0x8233cf39,0x6a72},
{0x8233d030,0x8233d039,0x6a7c},
{0x8233d130,0x8233d139,0x6a86},
{0x8233d230,0x8233d239,0x6a90},
{0x8233d330,0x8233d339,0x6a9a},
{0x8233d430,0x8233d439,0x6aa4},
{0x8233d530,0x8233d539,0x6aae},
{0x8233d630,0x8233d639,0x6ab8},
{0x8233d730,0x8233d739,0x6ac2},
{0x8233d830,0x8233d839,0x6acc},
{0x8233d930,0x8233d939,0x6ad6},
{0x8233da30,0x8233da39,0x6ae0},
{0x8233db30,0x8233db39,0x6aea},
{0x8233dc30,0x8233dc39,0x6af4},
{0x8233dd30,0x8233dd39,0x6afe},
{0x8233de30,0x8233de39,0x6b08},
{0x8233df30,0x8233df39,0x6b12},
{0x8233e030,0x8233e039,0x6b1c},
{0x8233e130,0x8233e139,0x6b26},
{0x8233e230,0x8233e239,0x6b30},
{0x8233e330,0x8233e339,0x6b3a},
{0x8233e430,0x8233e439,0x6b44},
{0x8233e530,0x8233e539,0x6b4e},
{0x8233e630,0x8233e639,0x6b58},
{0x8233e730,0x8233e739,0x6b62},
{0x8233e830,0x8233e839,0x6b6c},
{0x8233e930,0x8233e939,0x6b76},
{0x8233ea30,0x8233ea39,0x6b80},
{0x8233eb30,0x8233eb39,0x6b8a},
{0x8233ec30,0x8233ec39,0x6b94},
{0x8233ed30,0x8233ed39,0x6b9e},
{0x8233ee30,0x8233ee39,0x6ba8},
{0x8233ef30,0x8233ef39,0x6bb2},
{0x8233f030,0x8233f039,0x6bbc},
{0x8233f130,0x8233f139,0x6bc6},
{0x8233f230,0x8233f239,0x6bd0},
{0x8233f330,0x8233f339,0x6bda},
{0x8233f430,0x8233f439,0x6be4},
{0x8233f530,0x8233f539,0x6bee},
{0x8233f630,0x8233f639,0x6bf8},
{0x8233f730,0x8233f739,0x6c02},
{0x8233f830,0x8233f839,0x6c0c},
{0x8233f930,0x8233f939,0x6c16},
{0x8233fa30,0x8233fa39,0x6c20},
{0x8233fb30,0x8233fb39,0x6c2a},
{0x8233fc30,0x8233fc39,0x6c34},
{0x8233fd30,0x8233fd39,0x6c3e},
{0x8233fe30,0x8233fe39,0x6c48},
{0x82348130,0x82348139,0x6c52},
{0x82348230,0x82348239,0x6c5c},
{0x82348330,0x82348339,0x6c66},
{0x82348430,0x82348439,0x6c70},
{0x82348530,0x82348539,0x6c7a},
{0x82348630,0x82348639,0x6c84},
{0x82348730,0x82348739,0x6c8e},
{0x82348830,0x82348839,0x6c98},
{0x82348930,0x82348939,0x6ca2},
{0x82348a30,0x82348a39,0x6cac},
{0x82348b30,0x82348b39,0x6cb6},
{0x82348c30,0x82348c39,0x6cc0},
{0x82348d30,0x82348d39,0x6cca},
{0x82348e30,0x82348e39,0x6cd4},
{0x82348f30,0x82348f39,0x6cde},
{0x82349030,0x82349039,0x6ce8},
{0x82349130,0x82349139,0x6cf2},
{0x82349230,0x82349239,0x6cfc},
{0x82349330,0x82349339,0x6d06},
{0x82349430,0x82349439,0x6d10},
{0x82349530,0x82349539,0x6d1a},
{0x82349630,0x82349639,0x6d24},
{0x82349730,0x82349739,0x6d2e},
{0x82349830,0x82349839,0x6d38},
{0x82349930,0x82349939,0x6d42},
{0x82349a30,0x82349a39,0x6d4c},
{0x82349b30,0x82349b39,0x6d56},
{0x82349c30,0x82349c39,0x6d60},
{0x82349d30,0x82349d39,0x6d6a},
{0x82349e30,0x82349e39,0x6d74},
{0x82349f30,0x82349f39,0x6d7e},
{0x8234a030,0x8234a039,0x6d88},
{0x8234a130,0x8234a139,0x6d92},
{0x8234a230,0x8234a239,0x6d9c},
{0x8234a330,0x8234a339,0x6da6},
{0x8234a430,0x8234a439,0x6db0},
{0x8234a530,0x8234a539,0x6dba},
{0x8234a630,0x8234a639,0x6dc4},
{0x8234a730,0x8234a739,0x6dce},
{0x8234a830,0x8234a839,0x6dd8},
{0x8234a930,0x8234a939,0x6de2},
{0x8234aa30,0x8234aa39,0x6dec},
{0x8234ab30,0x8234ab39,0x6df6},
{0x8234ac30,0x8234ac39,0x6e00},
{0x8234ad30,0x8234ad39,0x6e0a},
{0x8234ae30,0x8234ae39,0x6e14},
{0x8234af30,0x8234af39,0x6e1e},
{0x8234b030,0x8234b039,0x6e28},
{0x8234b130,0x8234b139,0x6e32},
{0x8234b230,0x8234b239,0x6e3c},
{0x8234b330,0x8234b339,0x6e46},
{0x8234b430,0x8234b439,0x6e50},
{0x8234b530,0x8234b539,0x6e5a},
{0x8234b630,0x8234b639,0x6e64},
{0x8234b730,0x8234b739,0x6e6e},
{0x8234b830,0x8234b839,0x6e78},
{0x8234b930,0x8234b939,0x6e82},
{0x8234ba30,0x8234ba39,0x6e8c},
{0x8234bb30,0x8234bb39,0x6e96},
{0x8234bc30,0x8234bc39,0x6ea0},
{0x8234bd30,0x8234bd39,0x6eaa},
{0x8234be30,0x8234be39,0x6eb4},
{0x8234bf30,0x8234bf39,0x6ebe},
{0x8234c030,0x8234c039,0x6ec8},
{0x8234c130,0x8234c139,0x6ed2},
{0x8234c230,0x8234c239,0x6edc},
{0x8234c330,0x8234c339,0x6ee6},
{0x8234c430,0x8234c439,0x6ef0},
{0x8234c530,0x8234c539,0x6efa},
{0x8234c630,0x8234c639,0x6f04},
{0x8234c730,0x8234c739,0x6f0e},
{0x8234c830,0x8234c839,0x6f18},
{0x8234c930,0x8234c939,0x6f22},
{0x8234ca30,0x8234ca39,0x6f2c},
{0x8234cb30,0x8234cb39,0x6f36},
{0x8234cc30,0x8234cc39,0x6f40},
{0x8234cd30,0x8234cd39,0x6f4a},
{0x8234ce30,0x8234ce39,0x6f54},
{0x8234cf30,0x8234cf39,0x6f5e},
{0x8234d030,0x8234d039,0x6f68},
{0x8234d130,0x8234d139,0x6f72},
{0x8234d230,0x8234d239,0x6f7c},
{0x8234d330,0x8234d339,0x6f86},
{0x8234d430,0x8234d439,0x6f90},
{0x8234d530,0x8234d539,0x6f9a},
{0x8234d630,0x8234d639,0x6fa4},
{0x8234d730,0x8234d739,0x6fae},
{0x8234d830,0x8234d839,0x6fb8},
{0x8234d930,0x8234d939,0x6fc2},
{0x8234da30,0x8234da39,0x6fcc},
{0x8234db30,0x8234db39,0x6fd6},
{0x8234dc30,0x8234dc39,0x6fe0},
{0x8234dd30,0x8234dd39,0x6fea},
{0x8234de30,0x8234de39,0x6ff4},
{0x8234df30,0x8234df39,0x6ffe},
{0x8234e030,0x8234e039,0x7008},
{0x8234e130,0x8234e139,0x7012},
{0x8234e230,0x8234e239,0x701c},
{0x8234e330,0x8234e339,0x7026},
{0x8234e430,0x8234e439,0x7030},
{0x8234e530,0x8234e539,0x703a},
{0x8234e630,0x8234e639,0x7044},
{0x8234e730,0x8234e739,0x704e},
{0x8234e830,0x8234e839,0x7058},
{0x8234e930,0x8234e939,0x7062},
{0x8234ea30,0x8234ea39,0x706c},
{0x8234eb30,0x8234eb39,0x7076},
{0x8234ec30,0x8234ec39,0x7080},
{0x8234ed30,0x8234ed39,0x708a},
{0x8234ee30,0x8234ee39,0x7094},
{0x8234ef30,0x8234ef39,0x709e},
{0x8234f030,0x8234f039,0x70a8},
{0x8234f130,0x8234f139,0x70b2},
{0x8234f230,0x8234f239,0x70bc},
{0x8234f330,0x8234f339,0x70c6},
{0x8234f430,0x8234f439,0x70d0},
{0x8234f530,0x8234f539,0x70da},
{0x8234f630,0x8234f639,0x70e4},
{0x8234f730,0x8234f739,0x70ee},
{0x8234f830,0x8234f839,0x70f8},
{0x8234f930,0x8234f939,0x7102},
{0x8234fa30,0x8234fa39,0x710c},
{0x8234fb30,0x8234fb39,0x7116},
{0x8234fc30,0x8234fc39,0x7120},
{0x8234fd30,0x8234fd39,0x712a},
{0x8234fe30,0x8234fe39,0x7134},
{0x82358130,0x82358139,0x713e},
{0x82358230,0x82358239,0x7148},
{0x82358330,0x82358339,0x7152},
{0x82358430,0x82358439,0x715c},
{0x82358530,0x82358539,0x7166},
{0x82358630,0x82358639,0x7170},
{0x82358730,0x82358738,0x717a},
{0x82359833,0x82359839,0x7188},
{0x82359930,0x82359939,0x718f},
{0x82359a30,0x82359a39,0x7199},
{0x82359b30,0x82359b39,0x71a3},
{0x82359c30,0x82359c39,0x71ad},
{0x82359d30,0x82359d39,0x71b7},
{0x82359e30,0x82359e39,0x71c1},
{0x82359f30,0x82359f39,0x71cb},
{0x8235a030,0x8235a039,0x71d5},
{0x8235a130,0x8235a139,0x71df},
{0x8235a230,0x8235a239,0x71e9},
{0x8235a330,0x8235a339,0x71f3},
{0x8235a430,0x8235a439,0x71fd},
{0x8235a530,0x8235a539,0x7207},
{0x8235a630,0x8235a639,0x7211},
{0x8235a730,0x8235a739,0x721b},
{0x8235a830,0x8235a839,0x7225},
{0x8235a930,0x8235a939,0x722f},
{0x8235aa30,0x8235aa39,0x7239},
{0x8235ab30,0x8235ab39,0x7243},
{0x8235ac30,0x8235ac39,0x724d},
{0x8235ad30,0x8235ad39,0x7257},
{0x8235ae30,0x8235ae39,0x7261},
{0x8235af30,0x8235af39,0x726b},
{0x8235b030,0x8235b039,0x7275},
{0x8235b130,0x8235b139,0x727f},
{0x8235b230,0x8235b239,0x7289},
{0x8235b330,0x8235b339,0x7293},
{0x8235b430,0x8235b439,0x729d},
{0x8235b530,0x8235b539,0x72a7},
{0x8235b630,0x8235b639,0x72b1},
{0x8235b730,0x8235b739,0x72bb},
{0x8235b830,0x8235b839,0x72c5},
{0x8235b930,0x8235b939,0x72cf},
{0x8235ba30,0x8235ba39,0x72d9},
{0x8235bb30,0x8235bb39,0x72e3},
{0x8235bc30,0x8235bc39,0x72ed},
{0x8235bd30,0x8235bd39,0x72f7},
{0x8235be30,0x8235be39,0x7301},
{0x8235bf30,0x8235bf39,0x730b},
{0x8235c030,0x8235c039,0x7315},
{0x8235c130,0x8235c139,0x731f},
{0x8235c230,0x8235c239,0x7329},
{0x8235c330,0x8235c339,0x7333},
{0x8235c430,0x8235c439,0x733d},
{0x8235c530,0x8235c539,0x7347},
{0x8235c630,0x8235c639,0x7351},
{0x8235c730,0x8235c739,0x735b},
{0x8235c830,0x8235c839,0x7365},
{0x8235c930,0x8235c939,0x736f},
{0x8235ca30,0x8235ca39,0x7379},
{0x8235cb30,0x8235cb39,0x7383},
{0x8235cc30,0x8235cc39,0x738d},
{0x8235cd30,0x8235cd39,0x7397},
{0x8235ce30,0x8235ce39,0x73a1},
{0x8235cf30,0x8235cf39,0x73ab},
{0x8235d030,0x8235d039,0x73b5},
{0x8235d130,0x8235d139,0x73bf},
{0x8235d230,0x8235d239,0x73c9},
{0x8235d330,0x8235d339,0x73d3},
{0x8235d430,0x8235d439,0x73dd},
{0x8235d530,0x8235d539,0x73e7},
{0x8235d630,0x8235d639,0x73f1},
{0x8235d730,0x8235d739,0x73fb},
{0x8235d830,0x8235d839,0x7405},
{0x8235d930,0x8235d939,0x740f},
{0x8235da30,0x8235da39,0x7419},
{0x8235db30,0x8235db39,0x7423},
{0x8235dc30,0x8235dc39,0x742d},
{0x8235dd30,0x8235dd39,0x7437},
{0x8235de30,0x8235de39,0x7441},
{0x8235df30,0x8235df39,0x744b},
{0x8235e030,0x8235e039,0x7455},
{0x8235e130,0x8235e139,0x745f},
{0x8235e230,0x8235e239,0x7469},
{0x8235e330,0x8235e339,0x7473},
{0x8235e430,0x8235e439,0x747d},
{0x8235e530,0x8235e539,0x7487},
{0x8235e630,0x8235e639,0x7491},
{0x8235e730,0x8235e739,0x749b},
{0x8235e830,0x8235e839,0x74a5},
{0x8235e930,0x8235e939,0x74af},
{0x8235ea30,0x8235ea39,0x74b9},
{0x8235eb30,0x8235eb39,0x74c3},
{0x8235ec30,0x8235ec39,0x74cd},
{0x8235ed30,0x8235ed39,0x74d7},
{0x8235ee30,0x8235ee39,0x74e1},
{0x8235ef30,0x8235ef39,0x74eb},
{0x8235f030,0x8235f039,0x74f5},
{0x8235f130,0x8235f139,0x74ff},
{0x8235f230,0x8235f239,0x7509},
{0x8235f330,0x8235f339,0x7513},
{0x8235f430,0x8235f439,0x751d},
{0x8235f530,0x8235f539,0x7527},
{0x8235f630,0x8235f639,0x7531},
{0x8235f730,0x8235f739,0x753b},
{0x8235f830,0x8235f839,0x7545},
{0x8235f930,0x8235f939,0x754f},
{0x8235fa30,0x8235fa39,0x7559},
{0x8235fb30,0x8235fb39,0x7563},
{0x8235fc30,0x8235fc39,0x756d},
{0x8235fd30,0x8235fd39,0x7577},
{0x8235fe30,0x8235fe39,0x7581},
{0x82368130,0x82368139,0x758b},
{0x82368230,0x82368239,0x7595},
{0x82368330,0x82368339,0x759f},
{0x82368430,0x82368439,0x75a9},
{0x82368530,0x82368539,0x75b3},
{0x82368630,0x82368639,0x75bd},
{0x82368730,0x82368739,0x75c7},
{0x82368830,0x82368839,0x75d1},
{0x82368930,0x82368939,0x75db},
{0x82368a30,0x82368a39,0x75e5},
{0x82368b30,0x82368b39,0x75ef},
{0x82368c30,0x82368c39,0x75f9},
{0x82368d30,0x82368d39,0x7603},
{0x82368e30,0x82368e37,0x760d},
{0x82368f31,0x82368f39,0x7615},
{0x82369030,0x82369039,0x761e},
{0x82369130,0x82369139,0x7628},
{0x82369230,0x82369239,0x7632},
{0x82369330,0x82369339,0x763c},
{0x82369430,0x82369435,0x7646},
};

static pdf_cmap cmap_GBK2K_H = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "GBK2K-H",
	/* usecmap */ "GBK-X", NULL,
	/* wmode */ 0,
	/* codespaces */ 3, {
		{ 1, 0x00, 0x7f },
		{ 4, 0x81308130, 0xfe39fe39 },
		{ 2, 0x8140, 0xfefe },
	},
	2, 2, (pdf_range*)cmap_GBK2K_H_ranges,
	1017, 1017, (pdf_xrange*)cmap_GBK2K_H_xranges,
	0, 0, NULL, /* mranges */
	0, 0, NULL, /* table */
	0, 0, 0, NULL /* splay tree */
};
