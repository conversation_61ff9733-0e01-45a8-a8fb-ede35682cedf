/* This is an automatically generated file. Do not edit. */

/* Ext-RKSJ-H */

static const pdf_range cmap_Ext_RKSJ_H_ranges[] = {
{0x20,0x7e,0xe7},
{0xa0,0xdf,0x146},
{0x8140,0x817e,0x279},
{0x8180,0x8188,0x2b8},
{0x8189,0x8189,0x1d36},
{0x818a,0x81ac,0x2c2},
{0x824f,0x8258,0x30c},
{0x8260,0x8279,0x316},
{0x8281,0x829a,0x330},
{0x829f,0x82f1,0x34a},
{0x8340,0x837e,0x39d},
{0x8380,0x8396,0x3dc},
{0x839f,0x83b6,0x3f3},
{0x83bf,0x83d6,0x40b},
{0x8440,0x8460,0x423},
{0x8470,0x847e,0x444},
{0x8480,0x8491,0x453},
{0x8540,0x857e,0xe8},
{0x8580,0x8580,0x186},
{0x8581,0x859e,0x128},
{0x859f,0x85dd,0x147},
{0x85de,0x85fc,0x187},
{0x8640,0x867e,0x1a6},
{0x8680,0x8691,0x1e5},
{0x8692,0x8692,0x127},
{0x8693,0x869e,0x1f7},
{0x86a2,0x86ed,0x1d37},
{0x8740,0x875d,0x1d83},
{0x875f,0x8775,0x1da1},
{0x877e,0x877e,0x2083},
{0x8780,0x878f,0x1db8},
{0x8790,0x8790,0x2fa},
{0x8791,0x8791,0x2f9},
{0x8792,0x8792,0x301},
{0x8793,0x8799,0x1dc8},
{0x879a,0x879a,0x300},
{0x879b,0x879c,0x1dcf},
{0x889f,0x889f,0x465},
{0x88a0,0x88a0,0x1dd1},
{0x88a1,0x88b0,0x467},
{0x88b1,0x88b1,0x1ca2},
{0x88b2,0x88b8,0x478},
{0x88b9,0x88b9,0x1dd2},
{0x88ba,0x88eb,0x480},
{0x88ec,0x88ec,0x1dd3},
{0x88ed,0x88f0,0x4b3},
{0x88f1,0x88f1,0x1dd4},
{0x88f2,0x88f9,0x4b8},
{0x88fa,0x88fa,0x1dd5},
{0x88fb,0x88fc,0x4c1},
{0x8940,0x8948,0x4c3},
{0x8949,0x8949,0x1dd6},
{0x894a,0x8953,0x4cd},
{0x8954,0x8954,0x1dd7},
{0x8955,0x8957,0x4d8},
{0x8958,0x8958,0x1dd8},
{0x8959,0x895a,0x4dc},
{0x895b,0x895c,0x1dd9},
{0x895d,0x8960,0x4e0},
{0x8961,0x8961,0x1ddb},
{0x8962,0x897e,0x4e5},
{0x8980,0x898a,0x502},
{0x898b,0x898b,0x1ddc},
{0x898c,0x89a5,0x50e},
{0x89a6,0x89a6,0x1ddd},
{0x89a7,0x89a7,0x1cc9},
{0x89a8,0x89a8,0x1dde},
{0x89a9,0x89dd,0x52b},
{0x89de,0x89de,0x1ddf},
{0x89df,0x89f7,0x561},
{0x89f8,0x89f8,0x1de0},
{0x89f9,0x89fa,0x57b},
{0x89fb,0x89fb,0x1de1},
{0x89fc,0x89fc,0x57e},
{0x8a40,0x8a40,0x57f},
{0x8a41,0x8a41,0x1de2},
{0x8a42,0x8a60,0x581},
{0x8a61,0x8a61,0x1961},
{0x8a62,0x8a67,0x5a1},
{0x8a68,0x8a68,0x139f},
{0x8a69,0x8a7e,0x5a8},
{0x8a80,0x8a84,0x5be},
{0x8a85,0x8a85,0x1de3},
{0x8a86,0x8a8a,0x5c4},
{0x8a8b,0x8a8b,0x1de4},
{0x8a8c,0x8a92,0x5ca},
{0x8a93,0x8a93,0x1de5},
{0x8a94,0x8a95,0x5d2},
{0x8a96,0x8a96,0x1731},
{0x8a97,0x8a99,0x5d5},
{0x8a9a,0x8a9a,0x1de6},
{0x8a9b,0x8abf,0x5d9},
{0x8ac0,0x8ac0,0x1de7},
{0x8ac1,0x8ac1,0x1572},
{0x8ac2,0x8aca,0x600},
{0x8acb,0x8acb,0x1de8},
{0x8acc,0x8acf,0x60a},
{0x8ad0,0x8ad0,0x1a20},
{0x8ad1,0x8ae2,0x60f},
{0x8ae3,0x8ae3,0x1de9},
{0x8ae4,0x8afc,0x622},
{0x8b40,0x8b49,0x63b},
{0x8b4a,0x8b4a,0x1dea},
{0x8b4b,0x8b5e,0x646},
{0x8b5f,0x8b5f,0x1deb},
{0x8b60,0x8b7e,0x65b},
{0x8b80,0x8b9f,0x67a},
{0x8ba0,0x8ba0,0x1dec},
{0x8ba1,0x8ba7,0x69b},
{0x8ba8,0x8ba8,0x1ded},
{0x8ba9,0x8bc3,0x6a3},
{0x8bc4,0x8bc4,0x1d32},
{0x8bc5,0x8bcc,0x6bf},
{0x8bcd,0x8bcd,0x1dee},
{0x8bce,0x8bea,0x6c8},
{0x8beb,0x8beb,0x1def},
{0x8bec,0x8bf1,0x6e6},
{0x8bf2,0x8bf2,0x1df0},
{0x8bf3,0x8bf8,0x6ed},
{0x8bf9,0x8bf9,0x1df1},
{0x8bfa,0x8bfa,0x6f4},
{0x8bfb,0x8bfb,0x1df2},
{0x8bfc,0x8bfc,0x6f6},
{0x8c40,0x8c42,0x6f7},
{0x8c43,0x8c43,0x1df3},
{0x8c44,0x8c55,0x6fb},
{0x8c56,0x8c56,0x1df4},
{0x8c57,0x8c63,0x70e},
{0x8c64,0x8c64,0x1df5},
{0x8c65,0x8c6c,0x71c},
{0x8c6d,0x8c6d,0x1df6},
{0x8c6e,0x8c70,0x725},
{0x8c71,0x8c71,0x1df7},
{0x8c72,0x8c73,0x729},
{0x8c74,0x8c74,0x1df8},
{0x8c75,0x8c79,0x72c},
{0x8c7a,0x8c7a,0x1c0d},
{0x8c7b,0x8c7e,0x732},
{0x8c80,0x8c83,0x736},
{0x8c84,0x8c84,0x1df9},
{0x8c85,0x8c90,0x73b},
{0x8c91,0x8c91,0x1dfa},
{0x8c92,0x8c98,0x748},
{0x8c99,0x8c99,0x1dfb},
{0x8c9a,0x8c9d,0x750},
{0x8c9e,0x8c9e,0x1dfc},
{0x8c9f,0x8cb1,0x755},
{0x8cb2,0x8cb2,0x1dfd},
{0x8cb3,0x8cbe,0x769},
{0x8cbf,0x8cbf,0x1dfe},
{0x8cc0,0x8cfc,0x776},
{0x8d40,0x8d49,0x7b3},
{0x8d4a,0x8d4a,0x1dff},
{0x8d4b,0x8d55,0x7be},
{0x8d56,0x8d56,0x1e00},
{0x8d57,0x8d60,0x7ca},
{0x8d61,0x8d61,0x1e01},
{0x8d62,0x8d7a,0x7d5},
{0x8d7b,0x8d7b,0x16dd},
{0x8d7c,0x8d7e,0x7ef},
{0x8d80,0x8d8c,0x7f2},
{0x8d8d,0x8d8d,0x1e02},
{0x8d8e,0x8d93,0x800},
{0x8d94,0x8d94,0x1e03},
{0x8d95,0x8d98,0x807},
{0x8d99,0x8d99,0x1e04},
{0x8d9a,0x8dd0,0x80c},
{0x8dd1,0x8dd1,0x1e05},
{0x8dd2,0x8de4,0x844},
{0x8de5,0x8de5,0x1e06},
{0x8de6,0x8df1,0x858},
{0x8df2,0x8df2,0x1e07},
{0x8df3,0x8dfc,0x865},
{0x8e40,0x8e45,0x86f},
{0x8e46,0x8e46,0x1e08},
{0x8e47,0x8e48,0x876},
{0x8e49,0x8e49,0x1e09},
{0x8e4a,0x8e4a,0x879},
{0x8e4b,0x8e4b,0x1e0a},
{0x8e4c,0x8e57,0x87b},
{0x8e58,0x8e58,0x1e0b},
{0x8e59,0x8e7e,0x888},
{0x8e80,0x8eb5,0x8ae},
{0x8eb6,0x8eb6,0x1e0c},
{0x8eb7,0x8ec5,0x8e5},
{0x8ec6,0x8ec6,0x1e0d},
{0x8ec7,0x8ec7,0x1929},
{0x8ec8,0x8ed4,0x8f6},
{0x8ed5,0x8ed5,0x1e0e},
{0x8ed6,0x8eda,0x904},
{0x8edb,0x8edc,0x1e0f},
{0x8edd,0x8efc,0x90b},
{0x8f40,0x8f49,0x92b},
{0x8f4a,0x8f4a,0x1e11},
{0x8f4b,0x8f54,0x936},
{0x8f55,0x8f55,0x1e12},
{0x8f56,0x8f7e,0x941},
{0x8f80,0x8f8b,0x96a},
{0x8f8c,0x8f8d,0x1e13},
{0x8f8e,0x8f91,0x978},
{0x8f92,0x8f93,0x1e15},
{0x8f94,0x8fa2,0x97e},
{0x8fa3,0x8fa3,0x1e17},
{0x8fa4,0x8fb0,0x98e},
{0x8fb1,0x8fb1,0x1e18},
{0x8fb2,0x8fbc,0x99c},
{0x8fbd,0x8fbd,0x1e19},
{0x8fbe,0x8fd2,0x9a8},
{0x8fd3,0x8fd3,0x1e1a},
{0x8fd4,0x8fdc,0x9be},
{0x8fdd,0x8fdd,0x1e1b},
{0x8fde,0x8fe1,0x9c8},
{0x8fe2,0x8fe2,0x1e1c},
{0x8fe3,0x8ffc,0x9cd},
{0x9040,0x9048,0x9e7},
{0x9049,0x9049,0x1e1d},
{0x904a,0x9077,0x9f1},
{0x9078,0x9078,0x1e1e},
{0x9079,0x907e,0xa20},
{0x9080,0x9080,0x1e1f},
{0x9081,0x9088,0xa27},
{0x9089,0x9089,0x1e20},
{0x908a,0x909f,0xa30},
{0x90a0,0x90a0,0x1e21},
{0x90a1,0x90bf,0xa47},
{0x90c0,0x90c0,0x1e22},
{0x90c1,0x90e3,0xa67},
{0x90e4,0x90e4,0x1e23},
{0x90e5,0x90ee,0xa8b},
{0x90ef,0x90f0,0x1e24},
{0x90f1,0x90f6,0xa97},
{0x90f7,0x90f8,0x1e26},
{0x90f9,0x90fc,0xa9f},
{0x9140,0x9145,0xaa3},
{0x9146,0x9146,0x1e28},
{0x9147,0x9147,0x1a6e},
{0x9148,0x9157,0xaab},
{0x9158,0x9158,0x1e29},
{0x9159,0x916a,0xabc},
{0x916b,0x916b,0x1e2a},
{0x916c,0x916d,0xacf},
{0x916e,0x916e,0x1e2b},
{0x916f,0x917d,0xad2},
{0x917e,0x917e,0x1e2c},
{0x9180,0x9188,0xae2},
{0x9189,0x9189,0x1e2d},
{0x918a,0x91ba,0xaec},
{0x91bb,0x91bb,0x1e2e},
{0x91bc,0x91ca,0xb1e},
{0x91cb,0x91cb,0x1e2f},
{0x91cc,0x91d9,0xb2e},
{0x91da,0x91da,0x1e30},
{0x91db,0x91e0,0xb3d},
{0x91e1,0x91e1,0x1e31},
{0x91e2,0x91ec,0xb44},
{0x91ed,0x91ed,0x1e32},
{0x91ee,0x91f2,0xb50},
{0x91f3,0x91f4,0x1e33},
{0x91f5,0x91fa,0xb57},
{0x91fb,0x91fb,0x1e35},
{0x91fc,0x91fc,0xb5e},
{0x9240,0x9245,0xb5f},
{0x9246,0x9246,0x1e36},
{0x9247,0x9247,0xb66},
{0x9248,0x9249,0x1e37},
{0x924a,0x924b,0xb69},
{0x924c,0x924d,0x1e39},
{0x924e,0x925b,0xb6d},
{0x925c,0x925c,0x1e3b},
{0x925d,0x927e,0xb7c},
{0x9280,0x928f,0xb9e},
{0x9290,0x9290,0x1e3c},
{0x9291,0x9294,0xbaf},
{0x9295,0x9295,0x1e3d},
{0x9296,0x929b,0xbb4},
{0x929c,0x929c,0x1e3e},
{0x929d,0x92ba,0xbbb},
{0x92bb,0x92bb,0x1e3f},
{0x92bc,0x92c5,0xbda},
{0x92c6,0x92c6,0x1e40},
{0x92c7,0x92c7,0xbe5},
{0x92c8,0x92c8,0x1e41},
{0x92c9,0x92ca,0xbe7},
{0x92cb,0x92cb,0x1e42},
{0x92cc,0x92cc,0xbea},
{0x92cd,0x92cd,0x1e43},
{0x92ce,0x92d8,0xbec},
{0x92d9,0x92d9,0x11b5},
{0x92da,0x92fc,0xbf8},
{0x9340,0x9340,0xc1b},
{0x9341,0x9341,0x1e44},
{0x9342,0x9345,0xc1d},
{0x9346,0x9346,0x1e45},
{0x9347,0x934c,0xc22},
{0x934d,0x934d,0x1e46},
{0x934e,0x9354,0xc29},
{0x9355,0x9355,0x1e47},
{0x9356,0x935d,0xc31},
{0x935e,0x935e,0x1e48},
{0x935f,0x9366,0xc3a},
{0x9367,0x9367,0x1e49},
{0x9368,0x9369,0xc43},
{0x936a,0x936a,0x1e4a},
{0x936b,0x936f,0xc46},
{0x9370,0x9371,0x1e4b},
{0x9372,0x9375,0xc4d},
{0x9376,0x9376,0x16df},
{0x9377,0x937e,0xc52},
{0x9380,0x9383,0xc5a},
{0x9384,0x9384,0x1e4d},
{0x9385,0x938d,0xc5f},
{0x938e,0x938e,0x1450},
{0x938f,0x9392,0xc69},
{0x9393,0x9393,0x1536},
{0x9394,0x9397,0xc6e},
{0x9398,0x9398,0x1e4e},
{0x9399,0x93bb,0xc73},
{0x93bc,0x93bc,0x1e4f},
{0x93bd,0x93bf,0xc97},
{0x93c0,0x93c0,0x1e50},
{0x93c1,0x93d1,0xc9b},
{0x93d2,0x93d3,0x1e51},
{0x93d4,0x93d8,0xcae},
{0x93d9,0x93da,0x1e53},
{0x93db,0x93de,0xcb5},
{0x93df,0x93df,0x1e55},
{0x93e0,0x93e3,0xcba},
{0x93e4,0x93e5,0x1e56},
{0x93e6,0x93e7,0xcc0},
{0x93e8,0x93e8,0x1e58},
{0x93e9,0x93f3,0xcc3},
{0x93f4,0x93f4,0x1aed},
{0x93f5,0x93fc,0xccf},
{0x9440,0x9447,0xcd7},
{0x9448,0x9448,0x1e59},
{0x9449,0x9457,0xce0},
{0x9458,0x9458,0x1e5a},
{0x9459,0x9475,0xcf0},
{0x9476,0x9476,0x1e5b},
{0x9477,0x947e,0xd0e},
{0x9480,0x9486,0xd16},
{0x9487,0x9487,0x1e5c},
{0x9488,0x9488,0x1989},
{0x9489,0x9489,0x1e5d},
{0x948a,0x948c,0xd20},
{0x948d,0x948d,0x1e5e},
{0x948e,0x94a1,0xd24},
{0x94a2,0x94a2,0x1e5f},
{0x94a3,0x94ab,0xd39},
{0x94ac,0x94ac,0x1e60},
{0x94ad,0x94ad,0xd43},
{0x94ae,0x94ae,0x1e61},
{0x94af,0x94d1,0xd45},
{0x94d2,0x94d2,0x1e62},
{0x94d3,0x94df,0xd69},
{0x94e0,0x94e0,0x1e63},
{0x94e1,0x94f2,0xd77},
{0x94f3,0x94f3,0x1e64},
{0x94f4,0x94fc,0xd8a},
{0x9540,0x9540,0xd93},
{0x9541,0x9542,0x1e65},
{0x9543,0x954d,0xd96},
{0x954e,0x954e,0x1e67},
{0x954f,0x954f,0x143b},
{0x9550,0x9550,0xda3},
{0x9551,0x9551,0x1e68},
{0x9552,0x9553,0xda5},
{0x9554,0x9554,0x1e69},
{0x9555,0x955e,0xda8},
{0x955f,0x955f,0x1e6a},
{0x9560,0x956c,0xdb3},
{0x956d,0x956d,0x1e6b},
{0x956e,0x956f,0xdc1},
{0x9570,0x9570,0x1e6c},
{0x9571,0x957e,0xdc4},
{0x9580,0x95c0,0xdd2},
{0x95c1,0x95c1,0x1e6d},
{0x95c2,0x95ca,0xe14},
{0x95cb,0x95cb,0x1e6e},
{0x95cc,0x95d7,0xe1e},
{0x95d8,0x95d8,0x1e6f},
{0x95d9,0x95f6,0xe2b},
{0x95f7,0x95f7,0x1e70},
{0x95f8,0x95fc,0xe4a},
{0x9640,0x9640,0xe4f},
{0x9641,0x9641,0x1e71},
{0x9642,0x9647,0xe51},
{0x9648,0x9648,0x1e72},
{0x9649,0x9669,0xe58},
{0x966a,0x966a,0x1e73},
{0x966b,0x967e,0xe7a},
{0x9680,0x9689,0xe8e},
{0x968a,0x968a,0x1d33},
{0x968b,0x968f,0xe99},
{0x9690,0x9690,0x1e74},
{0x9691,0x9698,0xe9f},
{0x9699,0x9699,0x102f},
{0x969a,0x96ca,0xea8},
{0x96cb,0x96cb,0x1e75},
{0x96cc,0x96d6,0xeda},
{0x96d7,0x96d7,0x1e76},
{0x96d8,0x96dc,0xee6},
{0x96dd,0x96dd,0x1e77},
{0x96de,0x96df,0xeec},
{0x96e0,0x96e0,0x1e78},
{0x96e1,0x96f6,0xeef},
{0x96f7,0x96f7,0x1935},
{0x96f8,0x96f8,0x1e79},
{0x96f9,0x96f9,0xf07},
{0x96fa,0x96fa,0x1e7a},
{0x96fb,0x96fb,0xf09},
{0x96fc,0x96fc,0x1e7b},
{0x9740,0x9750,0xf0b},
{0x9751,0x9751,0x1e7c},
{0x9752,0x976e,0xf1d},
{0x976f,0x976f,0x1e7d},
{0x9770,0x9772,0xf3b},
{0x9773,0x9773,0x1e7e},
{0x9774,0x9778,0xf3f},
{0x9779,0x9779,0x1d34},
{0x977a,0x977e,0xf45},
{0x9780,0x9788,0xf4a},
{0x9789,0x9789,0x1e7f},
{0x978a,0x97c8,0xf54},
{0x97c9,0x97c9,0x1e80},
{0x97ca,0x97f7,0xf94},
{0x97f8,0x97f9,0x1e81},
{0x97fa,0x97fc,0xfc4},
{0x9840,0x9840,0x1e83},
{0x9841,0x984f,0xfc8},
{0x9850,0x9850,0x1e84},
{0x9851,0x9854,0xfd8},
{0x9855,0x9855,0x1777},
{0x9856,0x9857,0xfdd},
{0x9858,0x9858,0x1e85},
{0x9859,0x9872,0xfe0},
{0x989f,0x98d3,0xffa},
{0x98d4,0x98d4,0xea7},
{0x98d5,0x98fc,0x1030},
{0x9940,0x995b,0x1058},
{0x995c,0x995c,0x1e86},
{0x995d,0x9965,0x1075},
{0x9966,0x9966,0x1e87},
{0x9967,0x9969,0x107f},
{0x996a,0x996a,0x1e88},
{0x996b,0x996b,0x1083},
{0x996c,0x996c,0x1e89},
{0x996d,0x997e,0x1085},
{0x9980,0x99fc,0x1097},
{0x9a40,0x9a4e,0x1114},
{0x9a4f,0x9a4f,0x1e8a},
{0x9a50,0x9a58,0x1124},
{0x9a59,0x9a59,0x1e8b},
{0x9a5a,0x9a6e,0x112e},
{0x9a6f,0x9a6f,0x1e8c},
{0x9a70,0x9a7c,0x1144},
{0x9a7d,0x9a7d,0x1e8d},
{0x9a7e,0x9a7e,0x1152},
{0x9a80,0x9a8a,0x1153},
{0x9a8b,0x9a8b,0x1e8e},
{0x9a8c,0x9ac1,0x115f},
{0x9ac2,0x9ac2,0x1e8f},
{0x9ac3,0x9ae1,0x1196},
{0x9ae2,0x9ae2,0xbf7},
{0x9ae3,0x9afc,0x11b6},
{0x9b40,0x9b5b,0x11d0},
{0x9b5c,0x9b5c,0x1e90},
{0x9b5d,0x9b7e,0x11ed},
{0x9b80,0x9b82,0x120f},
{0x9b83,0x9b83,0x1e91},
{0x9b84,0x9b9f,0x1213},
{0x9ba0,0x9ba0,0x1e92},
{0x9ba1,0x9bef,0x1230},
{0x9bf0,0x9bf0,0x1e93},
{0x9bf1,0x9bfc,0x1280},
{0x9c40,0x9c7e,0x128c},
{0x9c80,0x9ca1,0x12cb},
{0x9ca2,0x9ca2,0x1e94},
{0x9ca3,0x9cfc,0x12ee},
{0x9d40,0x9d7e,0x1348},
{0x9d80,0x9d80,0x1e95},
{0x9d81,0x9d8b,0x1388},
{0x9d8c,0x9d8c,0x1e96},
{0x9d8d,0x9d97,0x1394},
{0x9d98,0x9d98,0x5a7},
{0x9d99,0x9db6,0x13a0},
{0x9db7,0x9db7,0x1e97},
{0x9db8,0x9dca,0x13bf},
{0x9dcb,0x9dcb,0x1e98},
{0x9dcc,0x9dfc,0x13d3},
{0x9e40,0x9e63,0x1404},
{0x9e64,0x9e64,0x1e99},
{0x9e65,0x9e68,0x1429},
{0x9e69,0x9e69,0x1e9a},
{0x9e6a,0x9e76,0x142e},
{0x9e77,0x9e77,0xda2},
{0x9e78,0x9e7e,0x143c},
{0x9e80,0x9e8a,0x1443},
{0x9e8b,0x9e8b,0x1e9b},
{0x9e8c,0x9e8c,0x144f},
{0x9e8d,0x9e8d,0xc68},
{0x9e8e,0x9e93,0x1451},
{0x9e94,0x9e94,0x1e9c},
{0x9e95,0x9efc,0x1458},
{0x9f40,0x9f7e,0x14c0},
{0x9f80,0x9fb6,0x14ff},
{0x9fb7,0x9fb7,0xc6d},
{0x9fb8,0x9fcd,0x1537},
{0x9fce,0x9fce,0x1e9d},
{0x9fcf,0x9ff2,0x154e},
{0x9ff3,0x9ff3,0x5ff},
{0x9ff4,0x9ffc,0x1573},
{0xe040,0xe07e,0x157c},
{0xe080,0xe092,0x15bb},
{0xe093,0xe093,0x1e9e},
{0xe094,0xe0a3,0x15cf},
{0xe0a4,0xe0a4,0x1e9f},
{0xe0a5,0xe0dc,0x15e0},
{0xe0dd,0xe0dd,0x1ea0},
{0xe0de,0xe0f3,0x1619},
{0xe0f4,0xe0f4,0x1d35},
{0xe0f5,0xe0fc,0x1630},
{0xe140,0xe149,0x1638},
{0xe14a,0xe14a,0x1ea1},
{0xe14b,0xe14e,0x1643},
{0xe14f,0xe150,0x1ea2},
{0xe151,0xe17e,0x1649},
{0xe180,0xe1a8,0x1677},
{0xe1a9,0xe1a9,0x1ea4},
{0xe1aa,0xe1e5,0x16a1},
{0xe1e6,0xe1e6,0x7ee},
{0xe1e7,0xe1e7,0x16de},
{0xe1e8,0xe1e8,0xc51},
{0xe1e9,0xe1ec,0x16e0},
{0xe1ed,0xe1ed,0x1ea5},
{0xe1ee,0xe1fc,0x16e5},
{0xe240,0xe268,0x16f4},
{0xe269,0xe269,0x1ea6},
{0xe26a,0xe272,0x171e},
{0xe273,0xe273,0x1ea7},
{0xe274,0xe27c,0x1728},
{0xe27d,0xe27d,0x5d4},
{0xe27e,0xe27e,0x1732},
{0xe280,0xe2b6,0x1733},
{0xe2b7,0xe2b7,0x1ea8},
{0xe2b8,0xe2c3,0x176b},
{0xe2c4,0xe2c4,0xfdc},
{0xe2c5,0xe2e1,0x1778},
{0xe2e2,0xe2e2,0x1ea9},
{0xe2e3,0xe2eb,0x1796},
{0xe2ec,0xe2ec,0x1eaa},
{0xe2ed,0xe2fc,0x17a0},
{0xe340,0xe357,0x17b0},
{0xe358,0xe358,0x1eab},
{0xe359,0xe359,0x17c9},
{0xe35a,0xe35a,0x1eac},
{0xe35b,0xe364,0x17cb},
{0xe365,0xe365,0x1ead},
{0xe366,0xe37e,0x17d6},
{0xe380,0xe3c3,0x17ef},
{0xe3c4,0xe3c4,0x1eae},
{0xe3c5,0xe3fc,0x1834},
{0xe440,0xe47e,0x186c},
{0xe480,0xe483,0x18ab},
{0xe484,0xe484,0x1eaf},
{0xe485,0xe488,0x18b0},
{0xe489,0xe489,0x1eb0},
{0xe48a,0xe491,0x18b5},
{0xe492,0xe492,0x1eb1},
{0xe493,0xe4b1,0x18be},
{0xe4b2,0xe4b2,0x1eb2},
{0xe4b3,0xe4b8,0x18de},
{0xe4b9,0xe4b9,0x1eb3},
{0xe4ba,0xe4f1,0x18e5},
{0xe4f2,0xe4f2,0x1eb4},
{0xe4f3,0xe4fc,0x191e},
{0xe540,0xe540,0x1928},
{0xe541,0xe541,0x8f5},
{0xe542,0xe54c,0x192a},
{0xe54d,0xe54d,0xf05},
{0xe54e,0xe55a,0x1936},
{0xe55b,0xe55b,0x1eb5},
{0xe55c,0xe578,0x1944},
{0xe579,0xe579,0x5a0},
{0xe57a,0xe57e,0x1962},
{0xe580,0xe5a1,0x1967},
{0xe5a2,0xe5a2,0xd1e},
{0xe5a3,0xe5a4,0x198a},
{0xe5a5,0xe5a5,0x1eb6},
{0xe5a6,0xe5ba,0x198d},
{0xe5bb,0xe5bb,0x1eb7},
{0xe5bc,0xe5ec,0x19a3},
{0xe5ed,0xe5ed,0x1eb8},
{0xe5ee,0xe5fc,0x19d5},
{0xe640,0xe650,0x19e4},
{0xe651,0xe651,0x1eb9},
{0xe652,0xe67b,0x19f6},
{0xe67c,0xe67c,0x60e},
{0xe67d,0xe67e,0x1a21},
{0xe680,0xe685,0x1a23},
{0xe686,0xe686,0x1eba},
{0xe687,0xe695,0x1a2a},
{0xe696,0xe696,0x1ebb},
{0xe697,0xe6ca,0x1a3a},
{0xe6cb,0xe6cb,0xaaa},
{0xe6cc,0xe6e6,0x1a6f},
{0xe6e7,0xe6e7,0x1ebc},
{0xe6e8,0xe6f1,0x1a8b},
{0xe6f2,0xe6f2,0x1ebd},
{0xe6f3,0xe6fc,0x1a96},
{0xe740,0xe76c,0x1aa0},
{0xe76d,0xe76d,0x1ebe},
{0xe76e,0xe77e,0x1ace},
{0xe780,0xe78b,0x1adf},
{0xe78c,0xe78c,0x1ebf},
{0xe78d,0xe78d,0x1aec},
{0xe78e,0xe78e,0x1ec0},
{0xe78f,0xe7a6,0x1aee},
{0xe7a7,0xe7a7,0x1ec1},
{0xe7a8,0xe7ba,0x1b07},
{0xe7bb,0xe7bb,0x1ec2},
{0xe7bc,0xe7d4,0x1b1b},
{0xe7d5,0xe7d5,0x1ec3},
{0xe7d6,0xe7fc,0x1b35},
{0xe840,0xe87e,0x1b5c},
{0xe880,0xe884,0x1b9b},
{0xe885,0xe885,0x1ec4},
{0xe886,0xe8b0,0x1ba1},
{0xe8b1,0xe8b1,0x1ec5},
{0xe8b2,0xe8c2,0x1bcd},
{0xe8c3,0xe8c3,0x1ec6},
{0xe8c4,0xe8ce,0x1bdf},
{0xe8cf,0xe8cf,0x1ec7},
{0xe8d0,0xe8d4,0x1beb},
{0xe8d5,0xe8d5,0x1ec8},
{0xe8d6,0xe8f1,0x1bf1},
{0xe8f2,0xe8f2,0x731},
{0xe8f3,0xe8f3,0x1ec9},
{0xe8f4,0xe8fc,0x1c0f},
{0xe940,0xe97e,0x1c18},
{0xe980,0xe9aa,0x1c57},
{0xe9ab,0xe9ab,0x1eca},
{0xe9ac,0xe9b9,0x1c83},
{0xe9ba,0xe9ba,0x1ecb},
{0xe9bb,0xe9ca,0x1c92},
{0xe9cb,0xe9cb,0x477},
{0xe9cc,0xe9cc,0x1ecc},
{0xe9cd,0xe9f1,0x1ca4},
{0xe9f2,0xe9f2,0x529},
{0xe9f3,0xe9fc,0x1cca},
{0xea40,0xea6f,0x1cd4},
{0xea70,0xea70,0x1ecd},
{0xea71,0xea7e,0x1d05},
{0xea80,0xea9c,0x1d13},
{0xea9d,0xea9d,0x1ece},
{0xea9e,0xea9e,0x1d31},
{0xed40,0xed7e,0x20a7},
{0xed80,0xedb3,0x20e6},
{0xedb4,0xedb4,0x7c9},
{0xedb5,0xedfc,0x211a},
{0xee40,0xee7e,0x2162},
{0xee80,0xeeec,0x21a1},
{0xeeef,0xeef8,0x1f9c},
{0xeef9,0xeef9,0x2ef},
{0xeefa,0xeefc,0x1f45},
};

static pdf_cmap cmap_Ext_RKSJ_H = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "Ext-RKSJ-H",
	/* usecmap */ "", NULL,
	/* wmode */ 0,
	/* codespaces */ 4, {
		{ 1, 0x00, 0x80 },
		{ 2, 0x8140, 0x9ffc },
		{ 1, 0xa0, 0xdf },
		{ 2, 0xe040, 0xfcfc },
	},
	665, 665, (pdf_range*)cmap_Ext_RKSJ_H_ranges,
	0, 0, NULL, /* xranges */
	0, 0, NULL, /* mranges */
	0, 0, NULL, /* table */
	0, 0, 0, NULL /* splay tree */
};
