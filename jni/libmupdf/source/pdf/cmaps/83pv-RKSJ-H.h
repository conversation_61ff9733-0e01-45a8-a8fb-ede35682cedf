/* This is an automatically generated file. Do not edit. */

/* 83pv-RKSJ-H */

static const pdf_range cmap_83pv_RKSJ_H_ranges[] = {
{0x20,0x7e,0x1},
{0x80,0x80,0x61},
{0xa0,0xdf,0x146},
{0xfd,0xfd,0x98},
{0xfe,0xfe,0xe4},
{0xff,0xff,0x7c},
{0x8140,0x817e,0x279},
{0x8180,0x81ac,0x2b8},
{0x81b8,0x81bf,0x2e5},
{0x81c8,0x81ce,0x2ed},
{0x81da,0x81e8,0x2f4},
{0x81f0,0x81f7,0x303},
{0x81fc,0x81fc,0x30b},
{0x824f,0x8258,0x30c},
{0x8260,0x8279,0x316},
{0x8281,0x829a,0x330},
{0x829f,0x82f1,0x34a},
{0x8340,0x837e,0x39d},
{0x8380,0x8396,0x3dc},
{0x839f,0x83b6,0x3f3},
{0x83bf,0x83d6,0x40b},
{0x8440,0x8460,0x423},
{0x8470,0x847e,0x444},
{0x8480,0x8491,0x453},
{0x849f,0x849f,0x1d37},
{0x84a0,0x84a0,0x1d39},
{0x84a1,0x84a1,0x1d43},
{0x84a2,0x84a2,0x1d47},
{0x84a3,0x84a3,0x1d4f},
{0x84a4,0x84a4,0x1d4b},
{0x84a5,0x84a5,0x1d53},
{0x84a6,0x84a6,0x1d63},
{0x84a7,0x84a7,0x1d5b},
{0x84a8,0x84a8,0x1d6b},
{0x84a9,0x84a9,0x1d73},
{0x84aa,0x84aa,0x1d38},
{0x84ab,0x84ab,0x1d3a},
{0x84ac,0x84ac,0x1d46},
{0x84ad,0x84ad,0x1d4a},
{0x84ae,0x84ae,0x1d52},
{0x84af,0x84af,0x1d4e},
{0x84b0,0x84b0,0x1d5a},
{0x84b1,0x84b1,0x1d6a},
{0x84b2,0x84b2,0x1d62},
{0x84b3,0x84b3,0x1d72},
{0x84b4,0x84b4,0x1d82},
{0x84b5,0x84b5,0x1d57},
{0x84b6,0x84b6,0x1d66},
{0x84b7,0x84b7,0x1d5f},
{0x84b8,0x84b8,0x1d6e},
{0x84b9,0x84b9,0x1d76},
{0x84ba,0x84ba,0x1d54},
{0x84bb,0x84bb,0x1d67},
{0x84bc,0x84bc,0x1d5c},
{0x84bd,0x84bd,0x1d6f},
{0x84be,0x84be,0x1d79},
{0x8540,0x857e,0xe8},
{0x8580,0x8580,0x186},
{0x8581,0x859e,0x128},
{0x859f,0x85dd,0x147},
{0x85de,0x85fc,0x187},
{0x8640,0x867e,0x1a6},
{0x8680,0x8691,0x1e5},
{0x8692,0x8692,0x127},
{0x8693,0x869e,0x1f7},
{0x86a2,0x86ed,0x1d37},
{0x8740,0x875d,0x1d83},
{0x875f,0x8775,0x1da1},
{0x8780,0x878f,0x1db8},
{0x8790,0x8790,0x2fa},
{0x8791,0x8791,0x2f9},
{0x8792,0x8792,0x301},
{0x8793,0x8799,0x1dc8},
{0x879a,0x879a,0x300},
{0x879b,0x879c,0x1dcf},
{0x889f,0x88fc,0x465},
{0x8940,0x897e,0x4c3},
{0x8980,0x89fc,0x502},
{0x8a40,0x8a7e,0x57f},
{0x8a80,0x8afc,0x5be},
{0x8b40,0x8b7e,0x63b},
{0x8b80,0x8bfc,0x67a},
{0x8c40,0x8c7e,0x6f7},
{0x8c80,0x8cfc,0x736},
{0x8d40,0x8d7e,0x7b3},
{0x8d80,0x8dfc,0x7f2},
{0x8e40,0x8e7e,0x86f},
{0x8e80,0x8efc,0x8ae},
{0x8f40,0x8f7e,0x92b},
{0x8f80,0x8ffc,0x96a},
{0x9040,0x907e,0x9e7},
{0x9080,0x90fc,0xa26},
{0x9140,0x917e,0xaa3},
{0x9180,0x91fc,0xae2},
{0x9240,0x927e,0xb5f},
{0x9280,0x92fc,0xb9e},
{0x9340,0x937e,0xc1b},
{0x9380,0x93fc,0xc5a},
{0x9440,0x947e,0xcd7},
{0x9480,0x94fc,0xd16},
{0x9540,0x957e,0xd93},
{0x9580,0x95fc,0xdd2},
{0x9640,0x967e,0xe4f},
{0x9680,0x96fc,0xe8e},
{0x9740,0x977e,0xf0b},
{0x9780,0x97fc,0xf4a},
{0x9840,0x9872,0xfc7},
{0x989f,0x98fc,0xffa},
{0x9940,0x997e,0x1058},
{0x9980,0x99fc,0x1097},
{0x9a40,0x9a7e,0x1114},
{0x9a80,0x9afc,0x1153},
{0x9b40,0x9b7e,0x11d0},
{0x9b80,0x9bfc,0x120f},
{0x9c40,0x9c7e,0x128c},
{0x9c80,0x9cfc,0x12cb},
{0x9d40,0x9d7e,0x1348},
{0x9d80,0x9dfc,0x1387},
{0x9e40,0x9e7e,0x1404},
{0x9e80,0x9efc,0x1443},
{0x9f40,0x9f7e,0x14c0},
{0x9f80,0x9ffc,0x14ff},
{0xe040,0xe07e,0x157c},
{0xe080,0xe0fc,0x15bb},
{0xe140,0xe17e,0x1638},
{0xe180,0xe1fc,0x1677},
{0xe240,0xe27e,0x16f4},
{0xe280,0xe2fc,0x1733},
{0xe340,0xe37e,0x17b0},
{0xe380,0xe3fc,0x17ef},
{0xe440,0xe47e,0x186c},
{0xe480,0xe4fc,0x18ab},
{0xe540,0xe57e,0x1928},
{0xe580,0xe5fc,0x1967},
{0xe640,0xe67e,0x19e4},
{0xe680,0xe6fc,0x1a23},
{0xe740,0xe77e,0x1aa0},
{0xe780,0xe7fc,0x1adf},
{0xe840,0xe87e,0x1b5c},
{0xe880,0xe8fc,0x1b9b},
{0xe940,0xe97e,0x1c18},
{0xe980,0xe9fc,0x1c57},
{0xea40,0xea7e,0x1cd4},
{0xea80,0xeaa2,0x1d13},
{0xeaa3,0xeaa4,0x205c},
{0xeb40,0xeb40,0x279},
{0xeb41,0xeb42,0x1ecf},
{0xeb43,0xeb4f,0x27c},
{0xeb50,0xeb51,0x1ed1},
{0xeb52,0xeb5a,0x28b},
{0xeb5b,0xeb5d,0x1ed3},
{0xeb5e,0xeb5f,0x297},
{0xeb60,0xeb64,0x1ed6},
{0xeb65,0xeb68,0x29e},
{0xeb69,0xeb7a,0x1edb},
{0xeb7b,0xeb7e,0x2b4},
{0xeb80,0xeb80,0x2b8},
{0xeb81,0xeb81,0x1eed},
{0xeb82,0xebac,0x2ba},
{0xebb8,0xebbf,0x2e5},
{0xebc8,0xebce,0x2ed},
{0xebda,0xebe8,0x2f4},
{0xebf0,0xebf7,0x303},
{0xebfc,0xebfc,0x30b},
{0xec4f,0xec58,0x30c},
{0xec60,0xec79,0x316},
{0xec81,0xec9a,0x330},
{0xec9f,0xec9f,0x1eee},
{0xeca0,0xeca0,0x34b},
{0xeca1,0xeca1,0x1eef},
{0xeca2,0xeca2,0x34d},
{0xeca3,0xeca3,0x1ef0},
{0xeca4,0xeca4,0x34f},
{0xeca5,0xeca5,0x1ef1},
{0xeca6,0xeca6,0x351},
{0xeca7,0xeca7,0x1ef2},
{0xeca8,0xecc0,0x353},
{0xecc1,0xecc1,0x1ef3},
{0xecc2,0xece0,0x36d},
{0xece1,0xece1,0x1ef4},
{0xece2,0xece2,0x38d},
{0xece3,0xece3,0x1ef5},
{0xece4,0xece4,0x38f},
{0xece5,0xece5,0x1ef6},
{0xece6,0xeceb,0x391},
{0xecec,0xecec,0x1ef7},
{0xeced,0xecf1,0x398},
{0xed40,0xed40,0x1ef8},
{0xed41,0xed41,0x39e},
{0xed42,0xed42,0x1ef9},
{0xed43,0xed43,0x3a0},
{0xed44,0xed44,0x1efa},
{0xed45,0xed45,0x3a2},
{0xed46,0xed46,0x1efb},
{0xed47,0xed47,0x3a4},
{0xed48,0xed48,0x1efc},
{0xed49,0xed61,0x3a6},
{0xed62,0xed62,0x1efd},
{0xed63,0xed7e,0x3c0},
{0xed80,0xed82,0x3dc},
{0xed83,0xed83,0x1efe},
{0xed84,0xed84,0x3e0},
{0xed85,0xed85,0x1eff},
{0xed86,0xed86,0x3e2},
{0xed87,0xed87,0x1f00},
{0xed88,0xed8d,0x3e4},
{0xed8e,0xed8e,0x1f01},
{0xed8f,0xed94,0x3eb},
{0xed95,0xed96,0x1f02},
{0xed9f,0xedb6,0x3f3},
{0xedbf,0xedd6,0x40b},
{0xee40,0xee5d,0x1d83},
{0xee5f,0xee6e,0x1f04},
{0xee6f,0xee75,0x1db1},
{0xee80,0xee81,0x1f14},
{0xee82,0xee8f,0x1dba},
{0xee90,0xee90,0x2fa},
{0xee91,0xee91,0x2f9},
{0xee92,0xee92,0x301},
{0xee93,0xee99,0x1dc8},
{0xee9a,0xee9a,0x300},
{0xee9b,0xee9c,0x1dcf},
};

static pdf_cmap cmap_83pv_RKSJ_H = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "83pv-RKSJ-H",
	/* usecmap */ "", NULL,
	/* wmode */ 0,
	/* codespaces */ 5, {
		{ 1, 0x00, 0x80 },
		{ 2, 0x8140, 0x9ffc },
		{ 1, 0xa0, 0xdf },
		{ 2, 0xe040, 0xfcfc },
		{ 1, 0xfd, 0xff },
	},
	222, 222, (pdf_range*)cmap_83pv_RKSJ_H_ranges,
	0, 0, NULL, /* xranges */
	0, 0, NULL, /* mranges */
	0, 0, NULL, /* table */
	0, 0, 0, NULL /* splay tree */
};
