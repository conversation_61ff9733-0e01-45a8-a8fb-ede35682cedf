/* This is an automatically generated file. Do not edit. */

/* Add-RKSJ-H */

static const pdf_range cmap_Add_RKSJ_H_ranges[] = {
{0x20,0x7e,0xe7},
{0xa0,0xdf,0x146},
{0x8140,0x817e,0x279},
{0x8180,0x81ac,0x2b8},
{0x81b8,0x81bf,0x2e5},
{0x81c8,0x81ce,0x2ed},
{0x81da,0x81e8,0x2f4},
{0x81f0,0x81f7,0x303},
{0x81fc,0x81fc,0x30b},
{0x824f,0x8258,0x30c},
{0x8260,0x8279,0x316},
{0x8281,0x829a,0x330},
{0x829f,0x82f1,0x34a},
{0x82f2,0x82f4,0x1f16},
{0x8340,0x837e,0x39d},
{0x8380,0x8396,0x3dc},
{0x839f,0x83b6,0x3f3},
{0x83bf,0x83d6,0x40b},
{0x8440,0x8460,0x423},
{0x8470,0x847e,0x444},
{0x8480,0x8491,0x453},
{0x849f,0x849f,0x1d37},
{0x84a0,0x84a0,0x1d39},
{0x84a1,0x84a1,0x1d43},
{0x84a2,0x84a2,0x1d47},
{0x84a3,0x84a3,0x1d4f},
{0x84a4,0x84a4,0x1d4b},
{0x84a5,0x84a5,0x1d53},
{0x84a6,0x84a6,0x1d63},
{0x84a7,0x84a7,0x1d5b},
{0x84a8,0x84a8,0x1d6b},
{0x84a9,0x84a9,0x1d73},
{0x84aa,0x84aa,0x1d38},
{0x84ab,0x84ab,0x1d3a},
{0x84ac,0x84ac,0x1d46},
{0x84ad,0x84ad,0x1d4a},
{0x84ae,0x84ae,0x1d52},
{0x84af,0x84af,0x1d4e},
{0x84b0,0x84b0,0x1d5a},
{0x84b1,0x84b1,0x1d6a},
{0x84b2,0x84b2,0x1d62},
{0x84b3,0x84b3,0x1d72},
{0x84b4,0x84b4,0x1d82},
{0x84b5,0x84b5,0x1d57},
{0x84b6,0x84b6,0x1d66},
{0x84b7,0x84b7,0x1d5f},
{0x84b8,0x84b8,0x1d6e},
{0x84b9,0x84b9,0x1d76},
{0x84ba,0x84ba,0x1d54},
{0x84bb,0x84bb,0x1d67},
{0x84bc,0x84bc,0x1d5c},
{0x84bd,0x84bd,0x1d6f},
{0x84be,0x84be,0x1d79},
{0x889f,0x889f,0x465},
{0x88a0,0x88a0,0x1dd1},
{0x88a1,0x88af,0x467},
{0x88b0,0x88b0,0x1f19},
{0x88b1,0x88b8,0x477},
{0x88b9,0x88b9,0x1dd2},
{0x88ba,0x88eb,0x480},
{0x88ec,0x88ec,0x1dd3},
{0x88ed,0x88ee,0x4b3},
{0x88ef,0x88ef,0x1f1a},
{0x88f0,0x88f0,0x4b6},
{0x88f1,0x88f1,0x1dd4},
{0x88f2,0x88f9,0x4b8},
{0x88fa,0x88fa,0x1dd5},
{0x88fb,0x88fc,0x4c1},
{0x8940,0x8948,0x4c3},
{0x8949,0x8949,0x1dd6},
{0x894a,0x8951,0x4cd},
{0x8952,0x8952,0x1f1b},
{0x8953,0x8953,0x4d6},
{0x8954,0x8954,0x1dd7},
{0x8955,0x8957,0x4d8},
{0x8958,0x8958,0x1f1c},
{0x8959,0x895b,0x4dc},
{0x895c,0x895c,0x1dda},
{0x895d,0x8960,0x4e0},
{0x8961,0x8961,0x1ddb},
{0x8962,0x897e,0x4e5},
{0x8980,0x898a,0x502},
{0x898b,0x898b,0x1ddc},
{0x898c,0x89a5,0x50e},
{0x89a6,0x89a6,0x1ddd},
{0x89a7,0x89a7,0x529},
{0x89a8,0x89a8,0x1dde},
{0x89a9,0x89dd,0x52b},
{0x89de,0x89de,0x1ddf},
{0x89df,0x89e4,0x561},
{0x89e5,0x89e5,0x1f1d},
{0x89e6,0x89f7,0x568},
{0x89f8,0x89f8,0x1de0},
{0x89f9,0x89fc,0x57b},
{0x8a40,0x8a40,0x57f},
{0x8a41,0x8a41,0x1de2},
{0x8a42,0x8a7e,0x581},
{0x8a80,0x8a8a,0x5be},
{0x8a8b,0x8a8b,0x1de4},
{0x8a8c,0x8a92,0x5ca},
{0x8a93,0x8a93,0x1de5},
{0x8a94,0x8a99,0x5d2},
{0x8a9a,0x8a9a,0x1de6},
{0x8a9b,0x8abf,0x5d9},
{0x8ac0,0x8ac0,0x1de7},
{0x8ac1,0x8aca,0x5ff},
{0x8acb,0x8acb,0x1de8},
{0x8acc,0x8ae2,0x60a},
{0x8ae3,0x8ae3,0x1de9},
{0x8ae4,0x8afc,0x622},
{0x8b40,0x8b49,0x63b},
{0x8b4a,0x8b4a,0x1dea},
{0x8b4b,0x8b5e,0x646},
{0x8b5f,0x8b5f,0x1deb},
{0x8b60,0x8b7e,0x65b},
{0x8b80,0x8b81,0x67a},
{0x8b82,0x8b82,0x1f1e},
{0x8b83,0x8b87,0x67d},
{0x8b88,0x8b88,0x1f1f},
{0x8b89,0x8b9f,0x683},
{0x8ba0,0x8ba0,0x1dec},
{0x8ba1,0x8ba7,0x69b},
{0x8ba8,0x8ba8,0x1ded},
{0x8ba9,0x8bbf,0x6a3},
{0x8bc0,0x8bc0,0x1f20},
{0x8bc1,0x8bcc,0x6bb},
{0x8bcd,0x8bcd,0x1dee},
{0x8bce,0x8bea,0x6c8},
{0x8beb,0x8beb,0x1def},
{0x8bec,0x8bf1,0x6e6},
{0x8bf2,0x8bf2,0x1df0},
{0x8bf3,0x8bf8,0x6ed},
{0x8bf9,0x8bf9,0x1df1},
{0x8bfa,0x8bfa,0x6f4},
{0x8bfb,0x8bfb,0x1df2},
{0x8bfc,0x8bfc,0x6f6},
{0x8c40,0x8c55,0x6f7},
{0x8c56,0x8c56,0x1df4},
{0x8c57,0x8c70,0x70e},
{0x8c71,0x8c71,0x1df7},
{0x8c72,0x8c7e,0x729},
{0x8c80,0x8c90,0x736},
{0x8c91,0x8c91,0x1dfa},
{0x8c92,0x8c9c,0x748},
{0x8c9d,0x8c9d,0x1f21},
{0x8c9e,0x8c9e,0x1dfc},
{0x8c9f,0x8cb1,0x755},
{0x8cb2,0x8cb2,0x1dfd},
{0x8cb3,0x8cbe,0x769},
{0x8cbf,0x8cbf,0x1dfe},
{0x8cc0,0x8cfc,0x776},
{0x8d40,0x8d49,0x7b3},
{0x8d4a,0x8d4a,0x1dff},
{0x8d4b,0x8d7e,0x7be},
{0x8d80,0x8d8c,0x7f2},
{0x8d8d,0x8d8d,0x1e02},
{0x8d8e,0x8d93,0x800},
{0x8d94,0x8d94,0x1e03},
{0x8d95,0x8d98,0x807},
{0x8d99,0x8d99,0x1e04},
{0x8d9a,0x8dd0,0x80c},
{0x8dd1,0x8dd1,0x1e05},
{0x8dd2,0x8de4,0x844},
{0x8de5,0x8de5,0x1e06},
{0x8de6,0x8df1,0x858},
{0x8df2,0x8df2,0x1e07},
{0x8df3,0x8dfc,0x865},
{0x8e40,0x8e45,0x86f},
{0x8e46,0x8e46,0x1e08},
{0x8e47,0x8e48,0x876},
{0x8e49,0x8e49,0x1e09},
{0x8e4a,0x8e4a,0x879},
{0x8e4b,0x8e4b,0x1e0a},
{0x8e4c,0x8e57,0x87b},
{0x8e58,0x8e58,0x1e0b},
{0x8e59,0x8e5f,0x888},
{0x8e60,0x8e60,0x1f22},
{0x8e61,0x8e7e,0x890},
{0x8e80,0x8ec5,0x8ae},
{0x8ec6,0x8ec6,0x1e0d},
{0x8ec7,0x8eda,0x8f5},
{0x8edb,0x8edc,0x1e0f},
{0x8edd,0x8efc,0x90b},
{0x8f40,0x8f49,0x92b},
{0x8f4a,0x8f4a,0x1e11},
{0x8f4b,0x8f54,0x936},
{0x8f55,0x8f55,0x1e12},
{0x8f56,0x8f7e,0x941},
{0x8f80,0x8f8b,0x96a},
{0x8f8c,0x8f8c,0x1e13},
{0x8f8d,0x8f91,0x977},
{0x8f92,0x8f93,0x1e15},
{0x8f94,0x8fa2,0x97e},
{0x8fa3,0x8fa3,0x1e17},
{0x8fa4,0x8fb0,0x98e},
{0x8fb1,0x8fb1,0x1e18},
{0x8fb2,0x8fd2,0x99c},
{0x8fd3,0x8fd3,0x1e1a},
{0x8fd4,0x8fdc,0x9be},
{0x8fdd,0x8fdd,0x1e1b},
{0x8fde,0x8fe1,0x9c8},
{0x8fe2,0x8fe2,0x1e1c},
{0x8fe3,0x8ffc,0x9cd},
{0x9040,0x9048,0x9e7},
{0x9049,0x9049,0x1e1d},
{0x904a,0x9077,0x9f1},
{0x9078,0x9078,0x1f23},
{0x9079,0x907e,0xa20},
{0x9080,0x9080,0x1e1f},
{0x9081,0x909f,0xa27},
{0x90a0,0x90a0,0x1e21},
{0x90a1,0x90e3,0xa47},
{0x90e4,0x90e4,0x1e23},
{0x90e5,0x90ee,0xa8b},
{0x90ef,0x90ef,0x1e24},
{0x90f0,0x90f6,0xa96},
{0x90f7,0x90f7,0x1e26},
{0x90f8,0x90f8,0x1f24},
{0x90f9,0x90f9,0xa9f},
{0x90fa,0x90fb,0x1f25},
{0x90fc,0x90fc,0xaa2},
{0x9140,0x9145,0xaa3},
{0x9146,0x9146,0x1e28},
{0x9147,0x9157,0xaaa},
{0x9158,0x9158,0x1e29},
{0x9159,0x916a,0xabc},
{0x916b,0x916b,0x1e2a},
{0x916c,0x916d,0xacf},
{0x916e,0x916e,0x1e2b},
{0x916f,0x917d,0xad2},
{0x917e,0x917e,0x1e2c},
{0x9180,0x9188,0xae2},
{0x9189,0x9189,0x1e2d},
{0x918a,0x91b4,0xaec},
{0x91b5,0x91b5,0x1f27},
{0x91b6,0x91ba,0xb18},
{0x91bb,0x91bb,0x1e2e},
{0x91bc,0x91ca,0xb1e},
{0x91cb,0x91cb,0x1e2f},
{0x91cc,0x91d9,0xb2e},
{0x91da,0x91da,0x1e30},
{0x91db,0x91e0,0xb3d},
{0x91e1,0x91e1,0x1e31},
{0x91e2,0x91ec,0xb44},
{0x91ed,0x91ed,0x1e32},
{0x91ee,0x91fa,0xb50},
{0x91fb,0x91fb,0x1e35},
{0x91fc,0x91fc,0xb5e},
{0x9240,0x9245,0xb5f},
{0x9246,0x9246,0x1e36},
{0x9247,0x9247,0xb66},
{0x9248,0x9248,0x1e37},
{0x9249,0x924b,0xb68},
{0x924c,0x924d,0x1e39},
{0x924e,0x925b,0xb6d},
{0x925c,0x925c,0x1e3b},
{0x925d,0x927e,0xb7c},
{0x9280,0x928f,0xb9e},
{0x9290,0x9290,0x1e3c},
{0x9291,0x9294,0xbaf},
{0x9295,0x9295,0x1e3d},
{0x9296,0x929b,0xbb4},
{0x929c,0x929c,0x1e3e},
{0x929d,0x92ba,0xbbb},
{0x92bb,0x92bb,0x1e3f},
{0x92bc,0x92c5,0xbda},
{0x92c6,0x92c6,0x1e40},
{0x92c7,0x92c7,0xbe5},
{0x92c8,0x92c8,0x1e41},
{0x92c9,0x92cc,0xbe7},
{0x92cd,0x92cd,0x1e43},
{0x92ce,0x92fc,0xbec},
{0x9340,0x9340,0xc1b},
{0x9341,0x9341,0x1e44},
{0x9342,0x9345,0xc1d},
{0x9346,0x9346,0x1e45},
{0x9347,0x934c,0xc22},
{0x934d,0x934d,0x1e46},
{0x934e,0x9354,0xc29},
{0x9355,0x9355,0x1e47},
{0x9356,0x935d,0xc31},
{0x935e,0x935e,0x1e48},
{0x935f,0x9366,0xc3a},
{0x9367,0x9367,0x1e49},
{0x9368,0x9369,0xc43},
{0x936a,0x936a,0x1e4a},
{0x936b,0x936f,0xc46},
{0x9370,0x9370,0x1f28},
{0x9371,0x9371,0x1e4c},
{0x9372,0x937e,0xc4d},
{0x9380,0x9383,0xc5a},
{0x9384,0x9384,0x1e4d},
{0x9385,0x9397,0xc5f},
{0x9398,0x9398,0x1e4e},
{0x9399,0x93bf,0xc73},
{0x93c0,0x93c0,0x1e50},
{0x93c1,0x93d1,0xc9b},
{0x93d2,0x93d2,0x1e51},
{0x93d3,0x93d8,0xcad},
{0x93d9,0x93d9,0x1e53},
{0x93da,0x93e3,0xcb4},
{0x93e4,0x93e5,0x1e56},
{0x93e6,0x93e7,0xcc0},
{0x93e8,0x93e8,0x1e58},
{0x93e9,0x93f3,0xcc3},
{0x93f4,0x93f4,0x1ec0},
{0x93f5,0x93fc,0xccf},
{0x9440,0x9447,0xcd7},
{0x9448,0x9448,0x1e59},
{0x9449,0x9449,0x1f29},
{0x944a,0x9457,0xce1},
{0x9458,0x9458,0x1e5a},
{0x9459,0x9475,0xcf0},
{0x9476,0x9476,0x1e5b},
{0x9477,0x947e,0xd0e},
{0x9480,0x9486,0xd16},
{0x9487,0x9487,0x1e5c},
{0x9488,0x9488,0xd1e},
{0x9489,0x9489,0x1e5d},
{0x948a,0x948c,0xd20},
{0x948d,0x948d,0x1e5e},
{0x948e,0x94a1,0xd24},
{0x94a2,0x94a2,0x1e5f},
{0x94a3,0x94ab,0xd39},
{0x94ac,0x94ac,0x1e60},
{0x94ad,0x94ad,0xd43},
{0x94ae,0x94ae,0x1e61},
{0x94af,0x94bd,0xd45},
{0x94be,0x94be,0x1f2a},
{0x94bf,0x94d1,0xd55},
{0x94d2,0x94d2,0x1e62},
{0x94d3,0x94f2,0xd69},
{0x94f3,0x94f3,0x1e64},
{0x94f4,0x94fc,0xd8a},
{0x9540,0x9540,0xd93},
{0x9541,0x9542,0x1e65},
{0x9543,0x954d,0xd96},
{0x954e,0x954e,0x1e67},
{0x954f,0x9550,0xda2},
{0x9551,0x9551,0x1e68},
{0x9552,0x9553,0xda5},
{0x9554,0x9554,0x1e69},
{0x9555,0x955e,0xda8},
{0x955f,0x955f,0x1e6a},
{0x9560,0x956c,0xdb3},
{0x956d,0x956d,0x1e6b},
{0x956e,0x957e,0xdc1},
{0x9580,0x95c0,0xdd2},
{0x95c1,0x95c1,0x1e6d},
{0x95c2,0x95ca,0xe14},
{0x95cb,0x95cb,0x1e6e},
{0x95cc,0x95d0,0xe1e},
{0x95d1,0x95d1,0x1f2b},
{0x95d2,0x95d7,0xe24},
{0x95d8,0x95d8,0x1e6f},
{0x95d9,0x95f6,0xe2b},
{0x95f7,0x95f7,0x1e70},
{0x95f8,0x95fc,0xe4a},
{0x9640,0x9647,0xe4f},
{0x9648,0x9648,0x1e72},
{0x9649,0x9669,0xe58},
{0x966a,0x966a,0x1e73},
{0x966b,0x967e,0xe7a},
{0x9680,0x968f,0xe8e},
{0x9690,0x9690,0x1e74},
{0x9691,0x9697,0xe9f},
{0x9698,0x9698,0x1f2c},
{0x9699,0x96ca,0xea7},
{0x96cb,0x96cb,0x1e75},
{0x96cc,0x96d6,0xeda},
{0x96d7,0x96d7,0x1e76},
{0x96d8,0x96dc,0xee6},
{0x96dd,0x96dd,0x1e77},
{0x96de,0x96df,0xeec},
{0x96e0,0x96e0,0x1e78},
{0x96e1,0x96f7,0xeef},
{0x96f8,0x96f8,0x1e79},
{0x96f9,0x96f9,0xf07},
{0x96fa,0x96fa,0x1e7a},
{0x96fb,0x96fc,0xf09},
{0x9740,0x9750,0xf0b},
{0x9751,0x9751,0x1e7c},
{0x9752,0x976e,0xf1d},
{0x976f,0x976f,0x1e7d},
{0x9770,0x9772,0xf3b},
{0x9773,0x9773,0x1e7e},
{0x9774,0x977e,0xf3f},
{0x9780,0x9788,0xf4a},
{0x9789,0x9789,0x1e7f},
{0x978a,0x97f7,0xf54},
{0x97f8,0x97f9,0x1e81},
{0x97fa,0x97fa,0x1f2d},
{0x97fb,0x97fc,0xfc5},
{0x9840,0x9840,0x1e83},
{0x9841,0x984f,0xfc8},
{0x9850,0x9850,0x1e84},
{0x9851,0x9857,0xfd8},
{0x9858,0x9858,0x1e85},
{0x9859,0x9872,0xfe0},
{0x989f,0x98fc,0xffa},
{0x9940,0x9940,0x1058},
{0x9941,0x9941,0x1f2e},
{0x9942,0x995b,0x105a},
{0x995c,0x995c,0x1e86},
{0x995d,0x996b,0x1075},
{0x996c,0x996c,0x1e89},
{0x996d,0x997e,0x1085},
{0x9980,0x99b5,0x1097},
{0x99b6,0x99b6,0x1f2f},
{0x99b7,0x99fc,0x10ce},
{0x9a40,0x9a4e,0x1114},
{0x9a4f,0x9a4f,0x1e8a},
{0x9a50,0x9a58,0x1124},
{0x9a59,0x9a59,0x1e8b},
{0x9a5a,0x9a66,0x112e},
{0x9a67,0x9a67,0x1f30},
{0x9a68,0x9a7c,0x113c},
{0x9a7d,0x9a7d,0x1e8d},
{0x9a7e,0x9a7e,0x1152},
{0x9a80,0x9a8a,0x1153},
{0x9a8b,0x9a8b,0x1e8e},
{0x9a8c,0x9a8c,0x1f31},
{0x9a8d,0x9ac1,0x1160},
{0x9ac2,0x9ac2,0x1e8f},
{0x9ac3,0x9ac3,0x1f32},
{0x9ac4,0x9ae9,0x1197},
{0x9aea,0x9aea,0x1f33},
{0x9aeb,0x9afc,0x11be},
{0x9b40,0x9b5b,0x11d0},
{0x9b5c,0x9b5c,0x1e90},
{0x9b5d,0x9b7e,0x11ed},
{0x9b80,0x9b82,0x120f},
{0x9b83,0x9b83,0x1e91},
{0x9b84,0x9b97,0x1213},
{0x9b98,0x9b98,0x1f34},
{0x9b99,0x9b9f,0x1228},
{0x9ba0,0x9ba0,0x1e92},
{0x9ba1,0x9bfa,0x1230},
{0x9bfb,0x9bfc,0x1f35},
{0x9c40,0x9c7e,0x128c},
{0x9c80,0x9ca1,0x12cb},
{0x9ca2,0x9ca2,0x1e94},
{0x9ca3,0x9cfc,0x12ee},
{0x9d40,0x9d46,0x1348},
{0x9d47,0x9d47,0x1f37},
{0x9d48,0x9d7e,0x1350},
{0x9d80,0x9d80,0x1e95},
{0x9d81,0x9d8b,0x1388},
{0x9d8c,0x9d8c,0x1e96},
{0x9d8d,0x9db6,0x1394},
{0x9db7,0x9db7,0x1e97},
{0x9db8,0x9df7,0x13bf},
{0x9df8,0x9df8,0x1f38},
{0x9df9,0x9dfc,0x1400},
{0x9e40,0x9e63,0x1404},
{0x9e64,0x9e64,0x1e99},
{0x9e65,0x9e7e,0x1429},
{0x9e80,0x9e8a,0x1443},
{0x9e8b,0x9e8b,0x1e9b},
{0x9e8c,0x9efc,0x144f},
{0x9f40,0x9f7e,0x14c0},
{0x9f80,0x9f80,0x14ff},
{0x9f81,0x9f81,0x1f39},
{0x9f82,0x9fcd,0x1501},
{0x9fce,0x9fce,0x1e9d},
{0x9fcf,0x9fd3,0x154e},
{0x9fd4,0x9fd4,0x1f3a},
{0x9fd5,0x9ff3,0x1554},
{0x9ff4,0x9ff4,0x1f3b},
{0x9ff5,0x9ffc,0x1574},
{0xe040,0xe07e,0x157c},
{0xe080,0xe092,0x15bb},
{0xe093,0xe093,0x1e9e},
{0xe094,0xe0a3,0x15cf},
{0xe0a4,0xe0a4,0x1e9f},
{0xe0a5,0xe0dc,0x15e0},
{0xe0dd,0xe0dd,0x1ea0},
{0xe0de,0xe0fc,0x1619},
{0xe140,0xe149,0x1638},
{0xe14a,0xe14a,0x1ea1},
{0xe14b,0xe17e,0x1643},
{0xe180,0xe1ec,0x1677},
{0xe1ed,0xe1ed,0x1ea5},
{0xe1ee,0xe1fc,0x16e5},
{0xe240,0xe268,0x16f4},
{0xe269,0xe269,0x1ea6},
{0xe26a,0xe272,0x171e},
{0xe273,0xe273,0x1ea7},
{0xe274,0xe277,0x1728},
{0xe278,0xe278,0x1f3c},
{0xe279,0xe27e,0x172d},
{0xe280,0xe2b6,0x1733},
{0xe2b7,0xe2b7,0x1ea8},
{0xe2b8,0xe2bd,0x176b},
{0xe2be,0xe2be,0x1f3d},
{0xe2bf,0xe2e1,0x1772},
{0xe2e2,0xe2e2,0x1ea9},
{0xe2e3,0xe2eb,0x1796},
{0xe2ec,0xe2ec,0x1eaa},
{0xe2ed,0xe2fc,0x17a0},
{0xe340,0xe357,0x17b0},
{0xe358,0xe358,0x1eab},
{0xe359,0xe359,0x17c9},
{0xe35a,0xe35a,0x1eac},
{0xe35b,0xe364,0x17cb},
{0xe365,0xe365,0x1ead},
{0xe366,0xe37e,0x17d6},
{0xe380,0xe3c6,0x17ef},
{0xe3c7,0xe3c7,0x1f3e},
{0xe3c8,0xe3fc,0x1837},
{0xe440,0xe47e,0x186c},
{0xe480,0xe483,0x18ab},
{0xe484,0xe484,0x1eaf},
{0xe485,0xe488,0x18b0},
{0xe489,0xe489,0x1eb0},
{0xe48a,0xe491,0x18b5},
{0xe492,0xe492,0x1eb1},
{0xe493,0xe4b8,0x18be},
{0xe4b9,0xe4b9,0x1eb3},
{0xe4ba,0xe4ca,0x18e5},
{0xe4cb,0xe4cb,0x1f3f},
{0xe4cc,0xe4fc,0x18f7},
{0xe540,0xe57e,0x1928},
{0xe580,0xe59d,0x1967},
{0xe59e,0xe59e,0x1f40},
{0xe59f,0xe5b9,0x1986},
{0xe5ba,0xe5bb,0x1f41},
{0xe5bc,0xe5ec,0x19a3},
{0xe5ed,0xe5ed,0x1eb8},
{0xe5ee,0xe5fc,0x19d5},
{0xe640,0xe650,0x19e4},
{0xe651,0xe651,0x1eb9},
{0xe652,0xe67e,0x19f6},
{0xe680,0xe685,0x1a23},
{0xe686,0xe686,0x1eba},
{0xe687,0xe6e6,0x1a2a},
{0xe6e7,0xe6e7,0x1ebc},
{0xe6e8,0xe6fc,0x1a8b},
{0xe740,0xe76c,0x1aa0},
{0xe76d,0xe76d,0x1ebe},
{0xe76e,0xe77e,0x1ace},
{0xe780,0xe7a6,0x1adf},
{0xe7a7,0xe7a7,0x1ec1},
{0xe7a8,0xe7ba,0x1b07},
{0xe7bb,0xe7bb,0x1ec2},
{0xe7bc,0xe7fc,0x1b1b},
{0xe840,0xe87e,0x1b5c},
{0xe880,0xe8ce,0x1b9b},
{0xe8cf,0xe8cf,0x1ec7},
{0xe8d0,0xe8fc,0x1beb},
{0xe940,0xe977,0x1c18},
{0xe978,0xe978,0x1f43},
{0xe979,0xe97e,0x1c51},
{0xe980,0xe9aa,0x1c57},
{0xe9ab,0xe9ab,0x1eca},
{0xe9ac,0xe9b9,0x1c83},
{0xe9ba,0xe9ba,0x1ecb},
{0xe9bb,0xe9cb,0x1c92},
{0xe9cc,0xe9cc,0x1ecc},
{0xe9cd,0xe9fc,0x1ca4},
{0xea40,0xea6f,0x1cd4},
{0xea70,0xea70,0x1ecd},
{0xea71,0xea71,0x1f44},
{0xea72,0xea7e,0x1d06},
{0xea80,0xea9c,0x1d13},
{0xea9d,0xea9d,0x1ece},
{0xea9e,0xeaa2,0x1d31},
{0xeaa3,0xeaa4,0x205c},
{0xec40,0xec42,0x1f45},
{0xec46,0xec46,0x1f48},
{0xec47,0xec47,0x300},
{0xec48,0xec48,0x2fa},
{0xec49,0xec49,0x2f9},
{0xec4d,0xec57,0x1f49},
{0xec5b,0xec5d,0x1db1},
{0xec5e,0xec5e,0x1f54},
{0xec5f,0xec5f,0x1db7},
{0xec60,0xec62,0x1f55},
{0xec63,0xec65,0x1db4},
{0xec66,0xec6f,0x1f58},
{0xec70,0xec70,0x303},
{0xec71,0xec71,0x1f62},
{0xec72,0xec72,0x304},
{0xec73,0xec74,0x1f63},
{0xec76,0xec76,0x1f65},
{0xec78,0xec78,0x1da4},
{0xec79,0xec79,0x1da1},
{0xec7a,0xec7a,0x1f66},
{0xec7b,0xec7b,0x1da2},
{0xec7c,0xec7e,0x1f67},
{0xec80,0xec80,0x1da6},
{0xec81,0xec81,0x1f6a},
{0xec82,0xec82,0x1da8},
{0xec83,0xec83,0x1dac},
{0xec84,0xec84,0x1f6b},
{0xec85,0xec85,0x1dae},
{0xec86,0xec86,0x1dab},
{0xec87,0xec88,0x1f6c},
{0xec89,0xec89,0x1daf},
{0xec8a,0xec90,0x1f6e},
{0xec94,0xec99,0x1f75},
{0xec9a,0xec9a,0x1dba},
{0xec9b,0xec9b,0x1f7b},
{0xec9e,0xec9e,0x1f7c},
{0xeca7,0xecb0,0x1f7d},
{0xecb2,0xecc5,0x1f87},
{0xecc7,0xecda,0x1d83},
{0xecdb,0xecdb,0x1f9b},
{0xecdc,0xece5,0x1d97},
{0xece9,0xecfc,0x1f9c},
{0xed40,0xed59,0x1fb0},
{0xed64,0xed64,0x1f16},
{0xed68,0xed69,0x1fca},
{0xed6a,0xed6a,0x1dc4},
{0xed6b,0xed6d,0x1fcc},
{0xed6e,0xed6e,0x1dc3},
{0xed6f,0xed73,0x1fcf},
{0xed74,0xed74,0x1dc2},
{0xed75,0xed78,0x1fd4},
{0xed7c,0xed7e,0x1fd8},
{0xed80,0xed8a,0x1fdb},
{0xed8f,0xed9e,0x1fe6},
{0xef40,0xef41,0x1ecf},
{0xef42,0xef42,0x204c},
{0xef43,0xef43,0x2052},
{0xef44,0xef4d,0x1ed1},
{0xef4e,0xef4e,0x205a},
{0xef4f,0xef4f,0x2053},
{0xef50,0xef50,0x2058},
{0xef51,0xef51,0x2055},
{0xef52,0xef63,0x1edb},
{0xef64,0xef79,0x1eee},
{0xef7a,0xef7b,0x2048},
{0xef8d,0xef90,0x2e0},
{0xef91,0xef94,0x1ff6},
};

static pdf_cmap cmap_Add_RKSJ_H = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "Add-RKSJ-H",
	/* usecmap */ "", NULL,
	/* wmode */ 0,
	/* codespaces */ 4, {
		{ 1, 0x00, 0x80 },
		{ 2, 0x8140, 0x9ffc },
		{ 1, 0xa0, 0xdf },
		{ 2, 0xe040, 0xfcfc },
	},
	635, 635, (pdf_range*)cmap_Add_RKSJ_H_ranges,
	0, 0, NULL, /* xranges */
	0, 0, NULL, /* mranges */
	0, 0, NULL, /* table */
	0, 0, 0, NULL /* splay tree */
};
