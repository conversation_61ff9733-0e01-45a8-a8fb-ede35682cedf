/* This is an automatically generated file. Do not edit. */

/* Adobe-Korea1-UCS2 */

static const pdf_range cmap_Adobe_Korea1_UCS2_ranges[] = {
{0x0,0x0,0xfffd},
{0x1,0x5f,0x20},
{0x60,0x60,0x20a9},
{0x61,0x61,0x2010},
{0x62,0x62,0xa9},
{0x63,0x63,0x2122},
{0x64,0x64,0x22ef},
{0x65,0x67,0x3000},
{0x68,0x68,0xb7},
{0x69,0x6a,0x2025},
{0x6b,0x6b,0xa8},
{0x6c,0x6c,0x3003},
{0x6d,0x6e,0x2013},
{0x6f,0x6f,0x2016},
{0x70,0x70,0xff3c},
{0x71,0x71,0x223c},
{0x72,0x73,0x2018},
{0x74,0x75,0x201c},
{0x76,0x77,0x3014},
{0x78,0x81,0x3008},
{0x82,0x82,0xb1},
{0x83,0x83,0xd7},
{0x84,0x84,0xf7},
{0x85,0x85,0x2260},
{0x86,0x87,0x2264},
{0x88,0x88,0x221e},
{0x89,0x89,0x2234},
{0x8a,0x8a,0xb0},
{0x8b,0x8c,0x2032},
{0x8d,0x8d,0x2103},
{0x8e,0x8e,0x212b},
{0x8f,0x90,0xffe0},
{0x91,0x91,0xffe5},
{0x92,0x92,0x2642},
{0x93,0x93,0x2640},
{0x94,0x94,0x2220},
{0x95,0x95,0x22a5},
{0x96,0x96,0x2312},
{0x97,0x97,0x2202},
{0x98,0x98,0x2207},
{0x99,0x99,0x2261},
{0x9a,0x9a,0x2252},
{0x9b,0x9b,0xa7},
{0x9c,0x9c,0x203b},
{0x9d,0x9d,0x2606},
{0x9e,0x9e,0x2605},
{0x9f,0x9f,0x25cb},
{0xa0,0xa0,0x25cf},
{0xa1,0xa1,0x25ce},
{0xa2,0xa2,0x25c7},
{0xa3,0xa3,0x25c6},
{0xa4,0xa4,0x25a1},
{0xa5,0xa5,0x25a0},
{0xa6,0xa6,0x25b3},
{0xa7,0xa7,0x25b2},
{0xa8,0xa8,0x25bd},
{0xa9,0xa9,0x25bc},
{0xaa,0xaa,0x2192},
{0xab,0xac,0x2190},
{0xad,0xae,0x2193},
{0xaf,0xaf,0x3013},
{0xb0,0xb1,0x226a},
{0xb2,0xb2,0x221a},
{0xb3,0xb3,0x223d},
{0xb4,0xb4,0x221d},
{0xb5,0xb5,0x2235},
{0xb6,0xb7,0x222b},
{0xb8,0xb8,0x2208},
{0xb9,0xb9,0x220b},
{0xba,0xbb,0x2286},
{0xbc,0xbd,0x2282},
{0xbe,0xbe,0x222a},
{0xbf,0xbf,0x2229},
{0xc0,0xc1,0x2227},
{0xc2,0xc2,0xffe2},
{0xc3,0xc3,0x21d2},
{0xc4,0xc4,0x21d4},
{0xc5,0xc5,0x2200},
{0xc6,0xc6,0x2203},
{0xc7,0xc7,0xb4},
{0xc8,0xc8,0x2dc},
{0xc9,0xc9,0x2c7},
{0xca,0xca,0x2d8},
{0xcb,0xcb,0x2dd},
{0xcc,0xcc,0x2da},
{0xcd,0xcd,0x2d9},
{0xce,0xce,0xb8},
{0xcf,0xcf,0x2db},
{0xd0,0xd0,0xa1},
{0xd1,0xd1,0xbf},
{0xd2,0xd2,0x2236},
{0xd3,0xd3,0x222e},
{0xd4,0xd4,0x2211},
{0xd5,0xd5,0x220f},
{0xd6,0xd6,0xa4},
{0xd7,0xd7,0x2109},
{0xd8,0xd8,0x2030},
{0xd9,0xd9,0x25c1},
{0xda,0xda,0x25c0},
{0xdb,0xdb,0x25b7},
{0xdc,0xdc,0x25b6},
{0xdd,0xdd,0x2664},
{0xde,0xdf,0x2660},
{0xe0,0xe0,0x2665},
{0xe1,0xe1,0x2667},
{0xe2,0xe2,0x2663},
{0xe3,0xe3,0x2299},
{0xe4,0xe4,0x25c8},
{0xe5,0xe5,0x25a3},
{0xe6,0xe7,0x25d0},
{0xe8,0xe8,0x2592},
{0xe9,0xea,0x25a4},
{0xeb,0xeb,0x25a8},
{0xec,0xec,0x25a7},
{0xed,0xed,0x25a6},
{0xee,0xee,0x25a9},
{0xef,0xef,0x2668},
{0xf0,0xf0,0x260f},
{0xf1,0xf1,0x260e},
{0xf2,0xf2,0x261c},
{0xf3,0xf3,0x261e},
{0xf4,0xf4,0xb6},
{0xf5,0xf6,0x2020},
{0xf7,0xf7,0x2195},
{0xf8,0xf8,0x2197},
{0xf9,0xf9,0x2199},
{0xfa,0xfa,0x2196},
{0xfb,0xfb,0x2198},
{0xfc,0xfc,0x266d},
{0xfd,0xfe,0x2669},
{0xff,0xff,0x266c},
{0x100,0x100,0x327f},
{0x101,0x101,0x321c},
{0x102,0x102,0x2116},
{0x103,0x103,0x33c7},
{0x104,0x104,0x2122},
{0x105,0x105,0x33c2},
{0x106,0x106,0x33d8},
{0x107,0x107,0x2121},
{0x108,0x142,0xff01},
{0x143,0x143,0xffe6},
{0x144,0x164,0xff3d},
{0x165,0x165,0xffe3},
{0x166,0x198,0x3131},
{0x199,0x1c2,0x3165},
{0x1c3,0x1cc,0x2170},
{0x1cd,0x1d6,0x2160},
{0x1d7,0x1e7,0x391},
{0x1e8,0x1ee,0x3a3},
{0x1ef,0x1ff,0x3b1},
{0x200,0x206,0x3c3},
{0x207,0x207,0x2500},
{0x208,0x208,0x2502},
{0x209,0x209,0x250c},
{0x20a,0x20a,0x2510},
{0x20b,0x20b,0x2518},
{0x20c,0x20c,0x2514},
{0x20d,0x20d,0x251c},
{0x20e,0x20e,0x252c},
{0x20f,0x20f,0x2524},
{0x210,0x210,0x2534},
{0x211,0x211,0x253c},
{0x212,0x212,0x2501},
{0x213,0x213,0x2503},
{0x214,0x214,0x250f},
{0x215,0x215,0x2513},
{0x216,0x216,0x251b},
{0x217,0x217,0x2517},
{0x218,0x218,0x2523},
{0x219,0x219,0x2533},
{0x21a,0x21a,0x252b},
{0x21b,0x21b,0x253b},
{0x21c,0x21c,0x254b},
{0x21d,0x21d,0x2520},
{0x21e,0x21e,0x252f},
{0x21f,0x21f,0x2528},
{0x220,0x220,0x2537},
{0x221,0x221,0x253f},
{0x222,0x222,0x251d},
{0x223,0x223,0x2530},
{0x224,0x224,0x2525},
{0x225,0x225,0x2538},
{0x226,0x226,0x2542},
{0x227,0x227,0x2512},
{0x228,0x228,0x2511},
{0x229,0x229,0x251a},
{0x22a,0x22a,0x2519},
{0x22b,0x22b,0x2516},
{0x22c,0x22c,0x2515},
{0x22d,0x22d,0x250e},
{0x22e,0x22e,0x250d},
{0x22f,0x230,0x251e},
{0x231,0x232,0x2521},
{0x233,0x234,0x2526},
{0x235,0x236,0x2529},
{0x237,0x238,0x252d},
{0x239,0x23a,0x2531},
{0x23b,0x23c,0x2535},
{0x23d,0x23e,0x2539},
{0x23f,0x240,0x253d},
{0x241,0x242,0x2540},
{0x243,0x24a,0x2543},
{0x24b,0x24d,0x3395},
{0x24e,0x24e,0x2113},
{0x24f,0x24f,0x3398},
{0x250,0x250,0x33c4},
{0x251,0x254,0x33a3},
{0x255,0x25e,0x3399},
{0x25f,0x25f,0x33ca},
{0x260,0x262,0x338d},
{0x263,0x263,0x33cf},
{0x264,0x265,0x3388},
{0x266,0x266,0x33c8},
{0x267,0x268,0x33a7},
{0x269,0x272,0x33b0},
{0x273,0x277,0x3380},
{0x278,0x27d,0x33ba},
{0x27e,0x282,0x3390},
{0x283,0x283,0x2126},
{0x284,0x285,0x33c0},
{0x286,0x288,0x338a},
{0x289,0x289,0x33d6},
{0x28a,0x28a,0x33c5},
{0x28b,0x28d,0x33ad},
{0x28e,0x28e,0x33db},
{0x28f,0x292,0x33a9},
{0x293,0x293,0x33dd},
{0x294,0x294,0x33d0},
{0x295,0x295,0x33d3},
{0x296,0x296,0x33c3},
{0x297,0x297,0x33c9},
{0x298,0x298,0x33dc},
{0x299,0x299,0x33c6},
{0x29a,0x29a,0xc6},
{0x29b,0x29b,0xd0},
{0x29c,0x29c,0xaa},
{0x29d,0x29d,0x126},
{0x29e,0x29e,0x132},
{0x29f,0x29f,0x13f},
{0x2a0,0x2a0,0x141},
{0x2a1,0x2a1,0xd8},
{0x2a2,0x2a2,0x152},
{0x2a3,0x2a3,0xba},
{0x2a4,0x2a4,0xde},
{0x2a5,0x2a5,0x166},
{0x2a6,0x2a6,0x14a},
{0x2a7,0x2c2,0x3260},
{0x2c3,0x2dc,0x24d0},
{0x2dd,0x2eb,0x2460},
{0x2ec,0x2ec,0xbd},
{0x2ed,0x2ee,0x2153},
{0x2ef,0x2ef,0xbc},
{0x2f0,0x2f0,0xbe},
{0x2f1,0x2f4,0x215b},
{0x2f5,0x2f5,0xe6},
{0x2f6,0x2f6,0x111},
{0x2f7,0x2f7,0xf0},
{0x2f8,0x2f8,0x127},
{0x2f9,0x2f9,0x131},
{0x2fa,0x2fa,0x133},
{0x2fb,0x2fb,0x138},
{0x2fc,0x2fc,0x140},
{0x2fd,0x2fd,0x142},
{0x2fe,0x2fe,0xf8},
{0x2ff,0x2ff,0x153},
{0x300,0x300,0xdf},
{0x301,0x301,0xfe},
{0x302,0x302,0x167},
{0x303,0x303,0x14b},
{0x304,0x304,0x149},
{0x305,0x320,0x3200},
{0x321,0x33a,0x249c},
{0x33b,0x349,0x2474},
{0x34a,0x34a,0xb9},
{0x34b,0x34c,0xb2},
{0x34d,0x34d,0x2074},
{0x34e,0x34e,0x207f},
{0x34f,0x352,0x2081},
{0x353,0x3a5,0x3041},
{0x3a6,0x3fb,0x30a1},
{0x3fc,0x401,0x410},
{0x402,0x402,0x401},
{0x403,0x422,0x416},
{0x423,0x423,0x451},
{0x424,0x43d,0x436},
{0x43e,0x43f,0xac00},
{0x440,0x440,0xac04},
{0x441,0x444,0xac07},
{0x445,0x44c,0xac10},
{0x44d,0x451,0xac19},
{0x452,0x452,0xac20},
{0x453,0x453,0xac24},
{0x454,0x455,0xac2c},
{0x456,0x458,0xac2f},
{0x459,0x45a,0xac38},
{0x45b,0x45b,0xac3c},
{0x45c,0x45c,0xac40},
{0x45d,0x45d,0xac4b},
{0x45e,0x45e,0xac4d},
{0x45f,0x45f,0xac54},
{0x460,0x460,0xac58},
{0x461,0x461,0xac5c},
{0x462,0x463,0xac70},
{0x464,0x464,0xac74},
{0x465,0x466,0xac77},
{0x467,0x467,0xac7a},
{0x468,0x469,0xac80},
{0x46a,0x46d,0xac83},
{0x46e,0x471,0xac89},
{0x472,0x472,0xac90},
{0x473,0x473,0xac94},
{0x474,0x475,0xac9c},
{0x476,0x478,0xac9f},
{0x479,0x47b,0xaca8},
{0x47c,0x47c,0xacac},
{0x47d,0x47e,0xacaf},
{0x47f,0x480,0xacb8},
{0x481,0x483,0xacbb},
{0x484,0x484,0xacc1},
{0x485,0x485,0xacc4},
{0x486,0x486,0xacc8},
{0x487,0x487,0xaccc},
{0x488,0x488,0xacd5},
{0x489,0x489,0xacd7},
{0x48a,0x48b,0xace0},
{0x48c,0x48c,0xace4},
{0x48d,0x48e,0xace7},
{0x48f,0x48f,0xacea},
{0x490,0x490,0xacec},
{0x491,0x493,0xacef},
{0x494,0x494,0xacf3},
{0x495,0x496,0xacf5},
{0x497,0x498,0xacfc},
{0x499,0x499,0xad00},
{0x49a,0x49a,0xad04},
{0x49b,0x49b,0xad06},
{0x49c,0x49d,0xad0c},
{0x49e,0x49e,0xad0f},
{0x49f,0x49f,0xad11},
{0x4a0,0x4a0,0xad18},
{0x4a1,0x4a1,0xad1c},
{0x4a2,0x4a2,0xad20},
{0x4a3,0x4a3,0xad29},
{0x4a4,0x4a5,0xad2c},
{0x4a6,0x4a7,0xad34},
{0x4a8,0x4a8,0xad38},
{0x4a9,0x4a9,0xad3c},
{0x4aa,0x4ab,0xad44},
{0x4ac,0x4ac,0xad47},
{0x4ad,0x4ad,0xad49},
{0x4ae,0x4ae,0xad50},
{0x4af,0x4af,0xad54},
{0x4b0,0x4b0,0xad58},
{0x4b1,0x4b1,0xad61},
{0x4b2,0x4b2,0xad63},
{0x4b3,0x4b4,0xad6c},
{0x4b5,0x4b5,0xad70},
{0x4b6,0x4b9,0xad73},
{0x4ba,0x4bc,0xad7b},
{0x4bd,0x4bd,0xad7f},
{0x4be,0x4bf,0xad81},
{0x4c0,0x4c1,0xad88},
{0x4c2,0x4c2,0xad8c},
{0x4c3,0x4c3,0xad90},
{0x4c4,0x4c5,0xad9c},
{0x4c6,0x4c6,0xada4},
{0x4c7,0x4c7,0xadb7},
{0x4c8,0x4c9,0xadc0},
{0x4ca,0x4ca,0xadc4},
{0x4cb,0x4cb,0xadc8},
{0x4cc,0x4cd,0xadd0},
{0x4ce,0x4ce,0xadd3},
{0x4cf,0x4cf,0xaddc},
{0x4d0,0x4d0,0xade0},
{0x4d1,0x4d1,0xade4},
{0x4d2,0x4d3,0xadf8},
{0x4d4,0x4d4,0xadfc},
{0x4d5,0x4d7,0xadff},
{0x4d8,0x4d9,0xae08},
{0x4da,0x4da,0xae0b},
{0x4db,0x4db,0xae0d},
{0x4dc,0x4dc,0xae14},
{0x4dd,0x4de,0xae30},
{0x4df,0x4df,0xae34},
{0x4e0,0x4e1,0xae37},
{0x4e2,0x4e2,0xae3a},
{0x4e3,0x4e4,0xae40},
{0x4e5,0x4e5,0xae43},
{0x4e6,0x4e7,0xae45},
{0x4e8,0x4e8,0xae4a},
{0x4e9,0x4eb,0xae4c},
{0x4ec,0x4ec,0xae50},
{0x4ed,0x4ed,0xae54},
{0x4ee,0x4ee,0xae56},
{0x4ef,0x4f0,0xae5c},
{0x4f1,0x4f3,0xae5f},
{0x4f4,0x4f4,0xae65},
{0x4f5,0x4f6,0xae68},
{0x4f7,0x4f7,0xae6c},
{0x4f8,0x4f8,0xae70},
{0x4f9,0x4fa,0xae78},
{0x4fb,0x4fd,0xae7b},
{0x4fe,0x4ff,0xae84},
{0x500,0x500,0xae8c},
{0x501,0x503,0xaebc},
{0x504,0x504,0xaec0},
{0x505,0x505,0xaec4},
{0x506,0x507,0xaecc},
{0x508,0x50a,0xaecf},
{0x50b,0x50c,0xaed8},
{0x50d,0x50d,0xaedc},
{0x50e,0x50e,0xaee8},
{0x50f,0x50f,0xaeeb},
{0x510,0x510,0xaeed},
{0x511,0x511,0xaef4},
{0x512,0x512,0xaef8},
{0x513,0x513,0xaefc},
{0x514,0x515,0xaf07},
{0x516,0x516,0xaf0d},
{0x517,0x517,0xaf10},
{0x518,0x519,0xaf2c},
{0x51a,0x51a,0xaf30},
{0x51b,0x51b,0xaf32},
{0x51c,0x51c,0xaf34},
{0x51d,0x51e,0xaf3c},
{0x51f,0x51f,0xaf3f},
{0x520,0x522,0xaf41},
{0x523,0x524,0xaf48},
{0x525,0x525,0xaf50},
{0x526,0x527,0xaf5c},
{0x528,0x529,0xaf64},
{0x52a,0x52a,0xaf79},
{0x52b,0x52b,0xaf80},
{0x52c,0x52c,0xaf84},
{0x52d,0x52d,0xaf88},
{0x52e,0x52f,0xaf90},
{0x530,0x530,0xaf95},
{0x531,0x531,0xaf9c},
{0x532,0x533,0xafb8},
{0x534,0x534,0xafbc},
{0x535,0x535,0xafc0},
{0x536,0x538,0xafc7},
{0x539,0x539,0xafcb},
{0x53a,0x53b,0xafcd},
{0x53c,0x53c,0xafd4},
{0x53d,0x53d,0xafdc},
{0x53e,0x53f,0xafe8},
{0x540,0x541,0xaff0},
{0x542,0x542,0xaff4},
{0x543,0x543,0xaff8},
{0x544,0x545,0xb000},
{0x546,0x546,0xb004},
{0x547,0x547,0xb00c},
{0x548,0x548,0xb010},
{0x549,0x549,0xb014},
{0x54a,0x54b,0xb01c},
{0x54c,0x54c,0xb028},
{0x54d,0x54e,0xb044},
{0x54f,0x54f,0xb048},
{0x550,0x550,0xb04a},
{0x551,0x551,0xb04c},
{0x552,0x552,0xb04e},
{0x553,0x555,0xb053},
{0x556,0x556,0xb057},
{0x557,0x557,0xb059},
{0x558,0x558,0xb05d},
{0x559,0x55a,0xb07c},
{0x55b,0x55b,0xb080},
{0x55c,0x55c,0xb084},
{0x55d,0x55e,0xb08c},
{0x55f,0x55f,0xb08f},
{0x560,0x560,0xb091},
{0x561,0x563,0xb098},
{0x564,0x564,0xb09c},
{0x565,0x568,0xb09f},
{0x569,0x56a,0xb0a8},
{0x56b,0x56f,0xb0ab},
{0x570,0x570,0xb0b1},
{0x571,0x573,0xb0b3},
{0x574,0x574,0xb0b8},
{0x575,0x575,0xb0bc},
{0x576,0x577,0xb0c4},
{0x578,0x57a,0xb0c7},
{0x57b,0x57c,0xb0d0},
{0x57d,0x57d,0xb0d4},
{0x57e,0x57e,0xb0d8},
{0x57f,0x57f,0xb0e0},
{0x580,0x580,0xb0e5},
{0x581,0x582,0xb108},
{0x583,0x584,0xb10b},
{0x585,0x585,0xb110},
{0x586,0x587,0xb112},
{0x588,0x589,0xb118},
{0x58a,0x58c,0xb11b},
{0x58d,0x58f,0xb123},
{0x590,0x590,0xb128},
{0x591,0x591,0xb12c},
{0x592,0x593,0xb134},
{0x594,0x596,0xb137},
{0x597,0x598,0xb140},
{0x599,0x599,0xb144},
{0x59a,0x59a,0xb148},
{0x59b,0x59c,0xb150},
{0x59d,0x59e,0xb154},
{0x59f,0x59f,0xb158},
{0x5a0,0x5a0,0xb15c},
{0x5a1,0x5a1,0xb160},
{0x5a2,0x5a3,0xb178},
{0x5a4,0x5a4,0xb17c},
{0x5a5,0x5a5,0xb180},
{0x5a6,0x5a6,0xb182},
{0x5a7,0x5a8,0xb188},
{0x5a9,0x5a9,0xb18b},
{0x5aa,0x5aa,0xb18d},
{0x5ab,0x5ad,0xb192},
{0x5ae,0x5ae,0xb198},
{0x5af,0x5af,0xb19c},
{0x5b0,0x5b0,0xb1a8},
{0x5b1,0x5b1,0xb1cc},
{0x5b2,0x5b2,0xb1d0},
{0x5b3,0x5b3,0xb1d4},
{0x5b4,0x5b5,0xb1dc},
{0x5b6,0x5b6,0xb1df},
{0x5b7,0x5b8,0xb1e8},
{0x5b9,0x5b9,0xb1ec},
{0x5ba,0x5ba,0xb1f0},
{0x5bb,0x5bb,0xb1f9},
{0x5bc,0x5bc,0xb1fb},
{0x5bd,0x5bd,0xb1fd},
{0x5be,0x5bf,0xb204},
{0x5c0,0x5c0,0xb208},
{0x5c1,0x5c2,0xb20b},
{0x5c3,0x5c4,0xb214},
{0x5c5,0x5c5,0xb217},
{0x5c6,0x5c6,0xb219},
{0x5c7,0x5c7,0xb220},
{0x5c8,0x5c8,0xb234},
{0x5c9,0x5c9,0xb23c},
{0x5ca,0x5ca,0xb258},
{0x5cb,0x5cb,0xb25c},
{0x5cc,0x5cc,0xb260},
{0x5cd,0x5ce,0xb268},
{0x5cf,0x5d0,0xb274},
{0x5d1,0x5d1,0xb27c},
{0x5d2,0x5d3,0xb284},
{0x5d4,0x5d4,0xb289},
{0x5d5,0x5d6,0xb290},
{0x5d7,0x5d7,0xb294},
{0x5d8,0x5da,0xb298},
{0x5db,0x5dc,0xb2a0},
{0x5dd,0x5dd,0xb2a3},
{0x5de,0x5df,0xb2a5},
{0x5e0,0x5e0,0xb2aa},
{0x5e1,0x5e1,0xb2ac},
{0x5e2,0x5e2,0xb2b0},
{0x5e3,0x5e3,0xb2b4},
{0x5e4,0x5e5,0xb2c8},
{0x5e6,0x5e6,0xb2cc},
{0x5e7,0x5e7,0xb2d0},
{0x5e8,0x5e8,0xb2d2},
{0x5e9,0x5ea,0xb2d8},
{0x5eb,0x5eb,0xb2db},
{0x5ec,0x5ec,0xb2dd},
{0x5ed,0x5ed,0xb2e2},
{0x5ee,0x5f0,0xb2e4},
{0x5f1,0x5f1,0xb2e8},
{0x5f2,0x5f6,0xb2eb},
{0x5f7,0x5f9,0xb2f3},
{0x5fa,0x5fe,0xb2f7},
{0x5ff,0x601,0xb2ff},
{0x602,0x602,0xb304},
{0x603,0x603,0xb308},
{0x604,0x605,0xb310},
{0x606,0x608,0xb313},
{0x609,0x609,0xb31c},
{0x60a,0x60c,0xb354},
{0x60d,0x60d,0xb358},
{0x60e,0x60f,0xb35b},
{0x610,0x611,0xb35e},
{0x612,0x613,0xb364},
{0x614,0x614,0xb367},
{0x615,0x615,0xb369},
{0x616,0x616,0xb36b},
{0x617,0x617,0xb36e},
{0x618,0x619,0xb370},
{0x61a,0x61a,0xb374},
{0x61b,0x61b,0xb378},
{0x61c,0x61d,0xb380},
{0x61e,0x620,0xb383},
{0x621,0x621,0xb38c},
{0x622,0x622,0xb390},
{0x623,0x623,0xb394},
{0x624,0x625,0xb3a0},
{0x626,0x626,0xb3a8},
{0x627,0x627,0xb3ac},
{0x628,0x629,0xb3c4},
{0x62a,0x62a,0xb3c8},
{0x62b,0x62c,0xb3cb},
{0x62d,0x62d,0xb3ce},
{0x62e,0x62e,0xb3d0},
{0x62f,0x630,0xb3d4},
{0x631,0x631,0xb3d7},
{0x632,0x632,0xb3d9},
{0x633,0x633,0xb3db},
{0x634,0x634,0xb3dd},
{0x635,0x635,0xb3e0},
{0x636,0x636,0xb3e4},
{0x637,0x637,0xb3e8},
{0x638,0x638,0xb3fc},
{0x639,0x639,0xb410},
{0x63a,0x63a,0xb418},
{0x63b,0x63b,0xb41c},
{0x63c,0x63c,0xb420},
{0x63d,0x63e,0xb428},
{0x63f,0x63f,0xb42b},
{0x640,0x640,0xb434},
{0x641,0x642,0xb450},
{0x643,0x643,0xb454},
{0x644,0x644,0xb458},
{0x645,0x646,0xb460},
{0x647,0x647,0xb463},
{0x648,0x648,0xb465},
{0x649,0x649,0xb46c},
{0x64a,0x64a,0xb480},
{0x64b,0x64b,0xb488},
{0x64c,0x64c,0xb49d},
{0x64d,0x64d,0xb4a4},
{0x64e,0x64e,0xb4a8},
{0x64f,0x64f,0xb4ac},
{0x650,0x650,0xb4b5},
{0x651,0x651,0xb4b7},
{0x652,0x652,0xb4b9},
{0x653,0x653,0xb4c0},
{0x654,0x654,0xb4c4},
{0x655,0x655,0xb4c8},
{0x656,0x656,0xb4d0},
{0x657,0x657,0xb4d5},
{0x658,0x659,0xb4dc},
{0x65a,0x65a,0xb4e0},
{0x65b,0x65c,0xb4e3},
{0x65d,0x65d,0xb4e6},
{0x65e,0x65f,0xb4ec},
{0x660,0x660,0xb4ef},
{0x661,0x661,0xb4f1},
{0x662,0x662,0xb4f8},
{0x663,0x664,0xb514},
{0x665,0x665,0xb518},
{0x666,0x667,0xb51b},
{0x668,0x669,0xb524},
{0x66a,0x66d,0xb527},
{0x66e,0x66f,0xb530},
{0x670,0x670,0xb534},
{0x671,0x671,0xb538},
{0x672,0x673,0xb540},
{0x674,0x676,0xb543},
{0x677,0x679,0xb54b},
{0x67a,0x67a,0xb550},
{0x67b,0x67b,0xb554},
{0x67c,0x67d,0xb55c},
{0x67e,0x680,0xb55f},
{0x681,0x682,0xb5a0},
{0x683,0x683,0xb5a4},
{0x684,0x684,0xb5a8},
{0x685,0x686,0xb5aa},
{0x687,0x688,0xb5b0},
{0x689,0x68b,0xb5b3},
{0x68c,0x68e,0xb5bb},
{0x68f,0x68f,0xb5c0},
{0x690,0x690,0xb5c4},
{0x691,0x692,0xb5cc},
{0x693,0x695,0xb5cf},
{0x696,0x696,0xb5d8},
{0x697,0x697,0xb5ec},
{0x698,0x699,0xb610},
{0x69a,0x69a,0xb614},
{0x69b,0x69b,0xb618},
{0x69c,0x69c,0xb625},
{0x69d,0x69d,0xb62c},
{0x69e,0x69e,0xb634},
{0x69f,0x69f,0xb648},
{0x6a0,0x6a0,0xb664},
{0x6a1,0x6a1,0xb668},
{0x6a2,0x6a3,0xb69c},
{0x6a4,0x6a4,0xb6a0},
{0x6a5,0x6a5,0xb6a4},
{0x6a6,0x6a7,0xb6ab},
{0x6a8,0x6a8,0xb6b1},
{0x6a9,0x6a9,0xb6d4},
{0x6aa,0x6aa,0xb6f0},
{0x6ab,0x6ab,0xb6f4},
{0x6ac,0x6ac,0xb6f8},
{0x6ad,0x6ae,0xb700},
{0x6af,0x6af,0xb705},
{0x6b0,0x6b1,0xb728},
{0x6b2,0x6b2,0xb72c},
{0x6b3,0x6b4,0xb72f},
{0x6b5,0x6b6,0xb738},
{0x6b7,0x6b7,0xb73b},
{0x6b8,0x6b8,0xb744},
{0x6b9,0x6b9,0xb748},
{0x6ba,0x6ba,0xb74c},
{0x6bb,0x6bc,0xb754},
{0x6bd,0x6bd,0xb760},
{0x6be,0x6be,0xb764},
{0x6bf,0x6bf,0xb768},
{0x6c0,0x6c1,0xb770},
{0x6c2,0x6c2,0xb773},
{0x6c3,0x6c3,0xb775},
{0x6c4,0x6c5,0xb77c},
{0x6c6,0x6c6,0xb780},
{0x6c7,0x6c7,0xb784},
{0x6c8,0x6c9,0xb78c},
{0x6ca,0x6cd,0xb78f},
{0x6ce,0x6d1,0xb796},
{0x6d2,0x6d2,0xb79c},
{0x6d3,0x6d3,0xb7a0},
{0x6d4,0x6d5,0xb7a8},
{0x6d6,0x6d8,0xb7ab},
{0x6d9,0x6da,0xb7b4},
{0x6db,0x6db,0xb7b8},
{0x6dc,0x6dc,0xb7c7},
{0x6dd,0x6dd,0xb7c9},
{0x6de,0x6df,0xb7ec},
{0x6e0,0x6e0,0xb7f0},
{0x6e1,0x6e1,0xb7f4},
{0x6e2,0x6e3,0xb7fc},
{0x6e4,0x6e6,0xb7ff},
{0x6e7,0x6e9,0xb807},
{0x6ea,0x6ea,0xb80c},
{0x6eb,0x6eb,0xb810},
{0x6ec,0x6ed,0xb818},
{0x6ee,0x6ee,0xb81b},
{0x6ef,0x6ef,0xb81d},
{0x6f0,0x6f1,0xb824},
{0x6f2,0x6f2,0xb828},
{0x6f3,0x6f3,0xb82c},
{0x6f4,0x6f5,0xb834},
{0x6f6,0x6f8,0xb837},
{0x6f9,0x6f9,0xb840},
{0x6fa,0x6fa,0xb844},
{0x6fb,0x6fb,0xb851},
{0x6fc,0x6fc,0xb853},
{0x6fd,0x6fe,0xb85c},
{0x6ff,0x6ff,0xb860},
{0x700,0x700,0xb864},
{0x701,0x702,0xb86c},
{0x703,0x703,0xb86f},
{0x704,0x704,0xb871},
{0x705,0x705,0xb878},
{0x706,0x706,0xb87c},
{0x707,0x707,0xb88d},
{0x708,0x708,0xb8a8},
{0x709,0x709,0xb8b0},
{0x70a,0x70a,0xb8b4},
{0x70b,0x70b,0xb8b8},
{0x70c,0x70d,0xb8c0},
{0x70e,0x70e,0xb8c3},
{0x70f,0x70f,0xb8c5},
{0x710,0x710,0xb8cc},
{0x711,0x711,0xb8d0},
{0x712,0x712,0xb8d4},
{0x713,0x713,0xb8dd},
{0x714,0x714,0xb8df},
{0x715,0x715,0xb8e1},
{0x716,0x717,0xb8e8},
{0x718,0x718,0xb8ec},
{0x719,0x719,0xb8f0},
{0x71a,0x71b,0xb8f8},
{0x71c,0x71c,0xb8fb},
{0x71d,0x71d,0xb8fd},
{0x71e,0x71e,0xb904},
{0x71f,0x71f,0xb918},
{0x720,0x720,0xb920},
{0x721,0x722,0xb93c},
{0x723,0x723,0xb940},
{0x724,0x724,0xb944},
{0x725,0x725,0xb94c},
{0x726,0x726,0xb94f},
{0x727,0x727,0xb951},
{0x728,0x729,0xb958},
{0x72a,0x72a,0xb95c},
{0x72b,0x72b,0xb960},
{0x72c,0x72d,0xb968},
{0x72e,0x72e,0xb96b},
{0x72f,0x72f,0xb96d},
{0x730,0x731,0xb974},
{0x732,0x732,0xb978},
{0x733,0x733,0xb97c},
{0x734,0x735,0xb984},
{0x736,0x736,0xb987},
{0x737,0x738,0xb989},
{0x739,0x73a,0xb98d},
{0x73b,0x73c,0xb9ac},
{0x73d,0x73d,0xb9b0},
{0x73e,0x73e,0xb9b4},
{0x73f,0x740,0xb9bc},
{0x741,0x741,0xb9bf},
{0x742,0x742,0xb9c1},
{0x743,0x744,0xb9c8},
{0x745,0x745,0xb9cc},
{0x746,0x74a,0xb9ce},
{0x74b,0x74c,0xb9d8},
{0x74d,0x74d,0xb9db},
{0x74e,0x74f,0xb9dd},
{0x750,0x750,0xb9e1},
{0x751,0x753,0xb9e3},
{0x754,0x754,0xb9e8},
{0x755,0x755,0xb9ec},
{0x756,0x757,0xb9f4},
{0x758,0x75b,0xb9f7},
{0x75c,0x75d,0xba00},
{0x75e,0x75e,0xba08},
{0x75f,0x75f,0xba15},
{0x760,0x761,0xba38},
{0x762,0x762,0xba3c},
{0x763,0x763,0xba40},
{0x764,0x764,0xba42},
{0x765,0x766,0xba48},
{0x767,0x767,0xba4b},
{0x768,0x769,0xba4d},
{0x76a,0x76c,0xba53},
{0x76d,0x76d,0xba58},
{0x76e,0x76e,0xba5c},
{0x76f,0x770,0xba64},
{0x771,0x773,0xba67},
{0x774,0x775,0xba70},
{0x776,0x776,0xba74},
{0x777,0x777,0xba78},
{0x778,0x77a,0xba83},
{0x77b,0x77b,0xba87},
{0x77c,0x77c,0xba8c},
{0x77d,0x77e,0xbaa8},
{0x77f,0x780,0xbaab},
{0x781,0x781,0xbab0},
{0x782,0x782,0xbab2},
{0x783,0x784,0xbab8},
{0x785,0x785,0xbabb},
{0x786,0x786,0xbabd},
{0x787,0x787,0xbac4},
{0x788,0x788,0xbac8},
{0x789,0x78a,0xbad8},
{0x78b,0x78b,0xbafc},
{0x78c,0x78c,0xbb00},
{0x78d,0x78d,0xbb04},
{0x78e,0x78e,0xbb0d},
{0x78f,0x78f,0xbb0f},
{0x790,0x790,0xbb11},
{0x791,0x791,0xbb18},
{0x792,0x792,0xbb1c},
{0x793,0x793,0xbb20},
{0x794,0x794,0xbb29},
{0x795,0x795,0xbb2b},
{0x796,0x798,0xbb34},
{0x799,0x799,0xbb38},
{0x79a,0x79d,0xbb3b},
{0x79e,0x79f,0xbb44},
{0x7a0,0x7a0,0xbb47},
{0x7a1,0x7a1,0xbb49},
{0x7a2,0x7a2,0xbb4d},
{0x7a3,0x7a4,0xbb4f},
{0x7a5,0x7a5,0xbb54},
{0x7a6,0x7a6,0xbb58},
{0x7a7,0x7a7,0xbb61},
{0x7a8,0x7a8,0xbb63},
{0x7a9,0x7a9,0xbb6c},
{0x7aa,0x7aa,0xbb88},
{0x7ab,0x7ab,0xbb8c},
{0x7ac,0x7ac,0xbb90},
{0x7ad,0x7ad,0xbba4},
{0x7ae,0x7ae,0xbba8},
{0x7af,0x7af,0xbbac},
{0x7b0,0x7b0,0xbbb4},
{0x7b1,0x7b1,0xbbb7},
{0x7b2,0x7b2,0xbbc0},
{0x7b3,0x7b3,0xbbc4},
{0x7b4,0x7b4,0xbbc8},
{0x7b5,0x7b5,0xbbd0},
{0x7b6,0x7b6,0xbbd3},
{0x7b7,0x7b8,0xbbf8},
{0x7b9,0x7b9,0xbbfc},
{0x7ba,0x7bb,0xbbff},
{0x7bc,0x7bc,0xbc02},
{0x7bd,0x7be,0xbc08},
{0x7bf,0x7c1,0xbc0b},
{0x7c2,0x7c2,0xbc0f},
{0x7c3,0x7c3,0xbc11},
{0x7c4,0x7c8,0xbc14},
{0x7c9,0x7cd,0xbc1b},
{0x7ce,0x7cf,0xbc24},
{0x7d0,0x7d0,0xbc27},
{0x7d1,0x7d1,0xbc29},
{0x7d2,0x7d2,0xbc2d},
{0x7d3,0x7d4,0xbc30},
{0x7d5,0x7d5,0xbc34},
{0x7d6,0x7d6,0xbc38},
{0x7d7,0x7d8,0xbc40},
{0x7d9,0x7db,0xbc43},
{0x7dc,0x7dc,0xbc49},
{0x7dd,0x7de,0xbc4c},
{0x7df,0x7df,0xbc50},
{0x7e0,0x7e0,0xbc5d},
{0x7e1,0x7e2,0xbc84},
{0x7e3,0x7e3,0xbc88},
{0x7e4,0x7e5,0xbc8b},
{0x7e6,0x7e6,0xbc8e},
{0x7e7,0x7e8,0xbc94},
{0x7e9,0x7e9,0xbc97},
{0x7ea,0x7eb,0xbc99},
{0x7ec,0x7ed,0xbca0},
{0x7ee,0x7ee,0xbca4},
{0x7ef,0x7f0,0xbca7},
{0x7f1,0x7f2,0xbcb0},
{0x7f3,0x7f5,0xbcb3},
{0x7f6,0x7f7,0xbcbc},
{0x7f8,0x7f8,0xbcc0},
{0x7f9,0x7f9,0xbcc4},
{0x7fa,0x7fa,0xbccd},
{0x7fb,0x7fd,0xbccf},
{0x7fe,0x7fe,0xbcd5},
{0x7ff,0x7ff,0xbcd8},
{0x800,0x800,0xbcdc},
{0x801,0x803,0xbcf4},
{0x804,0x804,0xbcf8},
{0x805,0x805,0xbcfc},
{0x806,0x807,0xbd04},
{0x808,0x808,0xbd07},
{0x809,0x809,0xbd09},
{0x80a,0x80a,0xbd10},
{0x80b,0x80b,0xbd14},
{0x80c,0x80c,0xbd24},
{0x80d,0x80d,0xbd2c},
{0x80e,0x80e,0xbd40},
{0x80f,0x810,0xbd48},
{0x811,0x811,0xbd4c},
{0x812,0x812,0xbd50},
{0x813,0x814,0xbd58},
{0x815,0x815,0xbd64},
{0x816,0x816,0xbd68},
{0x817,0x818,0xbd80},
{0x819,0x819,0xbd84},
{0x81a,0x81d,0xbd87},
{0x81e,0x81f,0xbd90},
{0x820,0x820,0xbd93},
{0x821,0x821,0xbd95},
{0x822,0x823,0xbd99},
{0x824,0x824,0xbd9c},
{0x825,0x825,0xbda4},
{0x826,0x826,0xbdb0},
{0x827,0x827,0xbdb8},
{0x828,0x829,0xbdd4},
{0x82a,0x82a,0xbdd8},
{0x82b,0x82b,0xbddc},
{0x82c,0x82c,0xbde9},
{0x82d,0x82d,0xbdf0},
{0x82e,0x82e,0xbdf4},
{0x82f,0x82f,0xbdf8},
{0x830,0x830,0xbe00},
{0x831,0x831,0xbe03},
{0x832,0x832,0xbe05},
{0x833,0x834,0xbe0c},
{0x835,0x835,0xbe10},
{0x836,0x836,0xbe14},
{0x837,0x838,0xbe1c},
{0x839,0x839,0xbe1f},
{0x83a,0x83b,0xbe44},
{0x83c,0x83c,0xbe48},
{0x83d,0x83d,0xbe4c},
{0x83e,0x83e,0xbe4e},
{0x83f,0x840,0xbe54},
{0x841,0x841,0xbe57},
{0x842,0x844,0xbe59},
{0x845,0x846,0xbe60},
{0x847,0x847,0xbe64},
{0x848,0x848,0xbe68},
{0x849,0x849,0xbe6a},
{0x84a,0x84b,0xbe70},
{0x84c,0x84e,0xbe73},
{0x84f,0x851,0xbe7b},
{0x852,0x852,0xbe80},
{0x853,0x853,0xbe84},
{0x854,0x855,0xbe8c},
{0x856,0x858,0xbe8f},
{0x859,0x85a,0xbe98},
{0x85b,0x85b,0xbea8},
{0x85c,0x85d,0xbed0},
{0x85e,0x85e,0xbed4},
{0x85f,0x860,0xbed7},
{0x861,0x861,0xbee0},
{0x862,0x864,0xbee3},
{0x865,0x865,0xbeec},
{0x866,0x866,0xbf01},
{0x867,0x868,0xbf08},
{0x869,0x86a,0xbf18},
{0x86b,0x86d,0xbf1b},
{0x86e,0x86f,0xbf40},
{0x870,0x870,0xbf44},
{0x871,0x871,0xbf48},
{0x872,0x873,0xbf50},
{0x874,0x874,0xbf55},
{0x875,0x875,0xbf94},
{0x876,0x876,0xbfb0},
{0x877,0x877,0xbfc5},
{0x878,0x879,0xbfcc},
{0x87a,0x87a,0xbfd0},
{0x87b,0x87b,0xbfd4},
{0x87c,0x87c,0xbfdc},
{0x87d,0x87d,0xbfdf},
{0x87e,0x87e,0xbfe1},
{0x87f,0x87f,0xc03c},
{0x880,0x880,0xc051},
{0x881,0x881,0xc058},
{0x882,0x882,0xc05c},
{0x883,0x883,0xc060},
{0x884,0x885,0xc068},
{0x886,0x887,0xc090},
{0x888,0x888,0xc094},
{0x889,0x889,0xc098},
{0x88a,0x88b,0xc0a0},
{0x88c,0x88c,0xc0a3},
{0x88d,0x88d,0xc0a5},
{0x88e,0x88f,0xc0ac},
{0x890,0x891,0xc0af},
{0x892,0x895,0xc0b3},
{0x896,0x897,0xc0bc},
{0x898,0x89a,0xc0bf},
{0x89b,0x89b,0xc0c5},
{0x89c,0x89d,0xc0c8},
{0x89e,0x89e,0xc0cc},
{0x89f,0x89f,0xc0d0},
{0x8a0,0x8a1,0xc0d8},
{0x8a2,0x8a4,0xc0db},
{0x8a5,0x8a6,0xc0e4},
{0x8a7,0x8a7,0xc0e8},
{0x8a8,0x8a8,0xc0ec},
{0x8a9,0x8aa,0xc0f4},
{0x8ab,0x8ab,0xc0f7},
{0x8ac,0x8ac,0xc0f9},
{0x8ad,0x8ad,0xc100},
{0x8ae,0x8ae,0xc104},
{0x8af,0x8af,0xc108},
{0x8b0,0x8b0,0xc110},
{0x8b1,0x8b1,0xc115},
{0x8b2,0x8b6,0xc11c},
{0x8b7,0x8b8,0xc123},
{0x8b9,0x8ba,0xc126},
{0x8bb,0x8bc,0xc12c},
{0x8bd,0x8bf,0xc12f},
{0x8c0,0x8c0,0xc136},
{0x8c1,0x8c2,0xc138},
{0x8c3,0x8c3,0xc13c},
{0x8c4,0x8c4,0xc140},
{0x8c5,0x8c6,0xc148},
{0x8c7,0x8c9,0xc14b},
{0x8ca,0x8cb,0xc154},
{0x8cc,0x8cc,0xc158},
{0x8cd,0x8cd,0xc15c},
{0x8ce,0x8cf,0xc164},
{0x8d0,0x8d2,0xc167},
{0x8d3,0x8d3,0xc170},
{0x8d4,0x8d4,0xc174},
{0x8d5,0x8d5,0xc178},
{0x8d6,0x8d6,0xc185},
{0x8d7,0x8d9,0xc18c},
{0x8da,0x8da,0xc190},
{0x8db,0x8db,0xc194},
{0x8dc,0x8dc,0xc196},
{0x8dd,0x8de,0xc19c},
{0x8df,0x8df,0xc19f},
{0x8e0,0x8e0,0xc1a1},
{0x8e1,0x8e1,0xc1a5},
{0x8e2,0x8e3,0xc1a8},
{0x8e4,0x8e4,0xc1ac},
{0x8e5,0x8e5,0xc1b0},
{0x8e6,0x8e6,0xc1bd},
{0x8e7,0x8e7,0xc1c4},
{0x8e8,0x8e8,0xc1c8},
{0x8e9,0x8e9,0xc1cc},
{0x8ea,0x8ea,0xc1d4},
{0x8eb,0x8ec,0xc1d7},
{0x8ed,0x8ed,0xc1e0},
{0x8ee,0x8ee,0xc1e4},
{0x8ef,0x8ef,0xc1e8},
{0x8f0,0x8f1,0xc1f0},
{0x8f2,0x8f2,0xc1f3},
{0x8f3,0x8f4,0xc1fc},
{0x8f5,0x8f5,0xc200},
{0x8f6,0x8f6,0xc204},
{0x8f7,0x8f8,0xc20c},
{0x8f9,0x8f9,0xc20f},
{0x8fa,0x8fa,0xc211},
{0x8fb,0x8fc,0xc218},
{0x8fd,0x8fd,0xc21c},
{0x8fe,0x8ff,0xc21f},
{0x900,0x901,0xc228},
{0x902,0x902,0xc22b},
{0x903,0x903,0xc22d},
{0x904,0x904,0xc22f},
{0x905,0x906,0xc231},
{0x907,0x907,0xc234},
{0x908,0x908,0xc248},
{0x909,0x90a,0xc250},
{0x90b,0x90b,0xc254},
{0x90c,0x90c,0xc258},
{0x90d,0x90d,0xc260},
{0x90e,0x90e,0xc265},
{0x90f,0x910,0xc26c},
{0x911,0x911,0xc270},
{0x912,0x912,0xc274},
{0x913,0x914,0xc27c},
{0x915,0x915,0xc27f},
{0x916,0x916,0xc281},
{0x917,0x918,0xc288},
{0x919,0x919,0xc290},
{0x91a,0x91a,0xc298},
{0x91b,0x91b,0xc29b},
{0x91c,0x91c,0xc29d},
{0x91d,0x91e,0xc2a4},
{0x91f,0x91f,0xc2a8},
{0x920,0x921,0xc2ac},
{0x922,0x923,0xc2b4},
{0x924,0x924,0xc2b7},
{0x925,0x925,0xc2b9},
{0x926,0x927,0xc2dc},
{0x928,0x928,0xc2e0},
{0x929,0x92a,0xc2e3},
{0x92b,0x92d,0xc2eb},
{0x92e,0x92e,0xc2ef},
{0x92f,0x92f,0xc2f1},
{0x930,0x930,0xc2f6},
{0x931,0x932,0xc2f8},
{0x933,0x934,0xc2fb},
{0x935,0x935,0xc300},
{0x936,0x937,0xc308},
{0x938,0x939,0xc30c},
{0x93a,0x93c,0xc313},
{0x93d,0x93d,0xc318},
{0x93e,0x93e,0xc31c},
{0x93f,0x940,0xc324},
{0x941,0x942,0xc328},
{0x943,0x943,0xc345},
{0x944,0x945,0xc368},
{0x946,0x946,0xc36c},
{0x947,0x947,0xc370},
{0x948,0x948,0xc372},
{0x949,0x94a,0xc378},
{0x94b,0x94c,0xc37c},
{0x94d,0x94d,0xc384},
{0x94e,0x94e,0xc388},
{0x94f,0x94f,0xc38c},
{0x950,0x950,0xc3c0},
{0x951,0x952,0xc3d8},
{0x953,0x953,0xc3dc},
{0x954,0x955,0xc3df},
{0x956,0x956,0xc3e2},
{0x957,0x958,0xc3e8},
{0x959,0x959,0xc3ed},
{0x95a,0x95b,0xc3f4},
{0x95c,0x95c,0xc3f8},
{0x95d,0x95d,0xc408},
{0x95e,0x95e,0xc410},
{0x95f,0x95f,0xc424},
{0x960,0x960,0xc42c},
{0x961,0x961,0xc430},
{0x962,0x962,0xc434},
{0x963,0x964,0xc43c},
{0x965,0x965,0xc448},
{0x966,0x967,0xc464},
{0x968,0x968,0xc468},
{0x969,0x969,0xc46c},
{0x96a,0x96b,0xc474},
{0x96c,0x96c,0xc479},
{0x96d,0x96d,0xc480},
{0x96e,0x96e,0xc494},
{0x96f,0x96f,0xc49c},
{0x970,0x970,0xc4b8},
{0x971,0x971,0xc4bc},
{0x972,0x972,0xc4e9},
{0x973,0x974,0xc4f0},
{0x975,0x975,0xc4f4},
{0x976,0x976,0xc4f8},
{0x977,0x977,0xc4fa},
{0x978,0x97a,0xc4ff},
{0x97b,0x97b,0xc50c},
{0x97c,0x97c,0xc510},
{0x97d,0x97d,0xc514},
{0x97e,0x97e,0xc51c},
{0x97f,0x980,0xc528},
{0x981,0x981,0xc52c},
{0x982,0x982,0xc530},
{0x983,0x984,0xc538},
{0x985,0x985,0xc53b},
{0x986,0x986,0xc53d},
{0x987,0x988,0xc544},
{0x989,0x98b,0xc548},
{0x98c,0x98e,0xc54c},
{0x98f,0x991,0xc553},
{0x992,0x994,0xc557},
{0x995,0x996,0xc55d},
{0x997,0x998,0xc560},
{0x999,0x999,0xc564},
{0x99a,0x99a,0xc568},
{0x99b,0x99c,0xc570},
{0x99d,0x99f,0xc573},
{0x9a0,0x9a1,0xc57c},
{0x9a2,0x9a2,0xc580},
{0x9a3,0x9a3,0xc584},
{0x9a4,0x9a4,0xc587},
{0x9a5,0x9a6,0xc58c},
{0x9a7,0x9a7,0xc58f},
{0x9a8,0x9a8,0xc591},
{0x9a9,0x9a9,0xc595},
{0x9aa,0x9ab,0xc597},
{0x9ac,0x9ac,0xc59c},
{0x9ad,0x9ad,0xc5a0},
{0x9ae,0x9ae,0xc5a9},
{0x9af,0x9b0,0xc5b4},
{0x9b1,0x9b2,0xc5b8},
{0x9b3,0x9b6,0xc5bb},
{0x9b7,0x9bd,0xc5c4},
{0x9be,0x9be,0xc5cc},
{0x9bf,0x9bf,0xc5ce},
{0x9c0,0x9c1,0xc5d0},
{0x9c2,0x9c2,0xc5d4},
{0x9c3,0x9c3,0xc5d8},
{0x9c4,0x9c5,0xc5e0},
{0x9c6,0x9c6,0xc5e3},
{0x9c7,0x9c7,0xc5e5},
{0x9c8,0x9ca,0xc5ec},
{0x9cb,0x9cb,0xc5f0},
{0x9cc,0x9cc,0xc5f4},
{0x9cd,0x9ce,0xc5f6},
{0x9cf,0x9d4,0xc5fc},
{0x9d5,0x9d8,0xc605},
{0x9d9,0x9d9,0xc60c},
{0x9da,0x9da,0xc610},
{0x9db,0x9dc,0xc618},
{0x9dd,0x9de,0xc61b},
{0x9df,0x9e0,0xc624},
{0x9e1,0x9e1,0xc628},
{0x9e2,0x9e4,0xc62c},
{0x9e5,0x9e5,0xc630},
{0x9e6,0x9e8,0xc633},
{0x9e9,0x9e9,0xc637},
{0x9ea,0x9ea,0xc639},
{0x9eb,0x9eb,0xc63b},
{0x9ec,0x9ed,0xc640},
{0x9ee,0x9ee,0xc644},
{0x9ef,0x9ef,0xc648},
{0x9f0,0x9f1,0xc650},
{0x9f2,0x9f4,0xc653},
{0x9f5,0x9f6,0xc65c},
{0x9f7,0x9f7,0xc660},
{0x9f8,0x9f8,0xc66c},
{0x9f9,0x9f9,0xc66f},
{0x9fa,0x9fa,0xc671},
{0x9fb,0x9fc,0xc678},
{0x9fd,0x9fd,0xc67c},
{0x9fe,0x9fe,0xc680},
{0x9ff,0xa00,0xc688},
{0xa01,0xa01,0xc68b},
{0xa02,0xa02,0xc68d},
{0xa03,0xa04,0xc694},
{0xa05,0xa05,0xc698},
{0xa06,0xa06,0xc69c},
{0xa07,0xa08,0xc6a4},
{0xa09,0xa09,0xc6a7},
{0xa0a,0xa0a,0xc6a9},
{0xa0b,0xa0c,0xc6b0},
{0xa0d,0xa0d,0xc6b4},
{0xa0e,0xa10,0xc6b8},
{0xa11,0xa12,0xc6c0},
{0xa13,0xa13,0xc6c3},
{0xa14,0xa14,0xc6c5},
{0xa15,0xa16,0xc6cc},
{0xa17,0xa17,0xc6d0},
{0xa18,0xa18,0xc6d4},
{0xa19,0xa1a,0xc6dc},
{0xa1b,0xa1c,0xc6e0},
{0xa1d,0xa1e,0xc6e8},
{0xa1f,0xa1f,0xc6ec},
{0xa20,0xa20,0xc6f0},
{0xa21,0xa22,0xc6f8},
{0xa23,0xa23,0xc6fd},
{0xa24,0xa25,0xc704},
{0xa26,0xa26,0xc708},
{0xa27,0xa27,0xc70c},
{0xa28,0xa29,0xc714},
{0xa2a,0xa2a,0xc717},
{0xa2b,0xa2b,0xc719},
{0xa2c,0xa2d,0xc720},
{0xa2e,0xa2e,0xc724},
{0xa2f,0xa2f,0xc728},
{0xa30,0xa31,0xc730},
{0xa32,0xa32,0xc733},
{0xa33,0xa33,0xc735},
{0xa34,0xa34,0xc737},
{0xa35,0xa36,0xc73c},
{0xa37,0xa37,0xc740},
{0xa38,0xa38,0xc744},
{0xa39,0xa39,0xc74a},
{0xa3a,0xa3b,0xc74c},
{0xa3c,0xa3c,0xc74f},
{0xa3d,0xa44,0xc751},
{0xa45,0xa45,0xc75c},
{0xa46,0xa46,0xc760},
{0xa47,0xa47,0xc768},
{0xa48,0xa48,0xc76b},
{0xa49,0xa4a,0xc774},
{0xa4b,0xa4b,0xc778},
{0xa4c,0xa4e,0xc77c},
{0xa4f,0xa51,0xc783},
{0xa52,0xa55,0xc787},
{0xa56,0xa56,0xc78e},
{0xa57,0xa58,0xc790},
{0xa59,0xa59,0xc794},
{0xa5a,0xa5c,0xc796},
{0xa5d,0xa5d,0xc79a},
{0xa5e,0xa5f,0xc7a0},
{0xa60,0xa63,0xc7a3},
{0xa64,0xa65,0xc7ac},
{0xa66,0xa66,0xc7b0},
{0xa67,0xa67,0xc7b4},
{0xa68,0xa69,0xc7bc},
{0xa6a,0xa6c,0xc7bf},
{0xa6d,0xa6e,0xc7c8},
{0xa6f,0xa6f,0xc7cc},
{0xa70,0xa70,0xc7ce},
{0xa71,0xa71,0xc7d0},
{0xa72,0xa72,0xc7d8},
{0xa73,0xa73,0xc7dd},
{0xa74,0xa74,0xc7e4},
{0xa75,0xa75,0xc7e8},
{0xa76,0xa76,0xc7ec},
{0xa77,0xa78,0xc800},
{0xa79,0xa79,0xc804},
{0xa7a,0xa7a,0xc808},
{0xa7b,0xa7b,0xc80a},
{0xa7c,0xa7d,0xc810},
{0xa7e,0xa7e,0xc813},
{0xa7f,0xa80,0xc815},
{0xa81,0xa82,0xc81c},
{0xa83,0xa83,0xc820},
{0xa84,0xa84,0xc824},
{0xa85,0xa86,0xc82c},
{0xa87,0xa87,0xc82f},
{0xa88,0xa88,0xc831},
{0xa89,0xa89,0xc838},
{0xa8a,0xa8a,0xc83c},
{0xa8b,0xa8b,0xc840},
{0xa8c,0xa8d,0xc848},
{0xa8e,0xa8f,0xc84c},
{0xa90,0xa90,0xc854},
{0xa91,0xa92,0xc870},
{0xa93,0xa93,0xc874},
{0xa94,0xa94,0xc878},
{0xa95,0xa95,0xc87a},
{0xa96,0xa97,0xc880},
{0xa98,0xa98,0xc883},
{0xa99,0xa9b,0xc885},
{0xa9c,0xa9e,0xc88b},
{0xa9f,0xa9f,0xc894},
{0xaa0,0xaa0,0xc89d},
{0xaa1,0xaa1,0xc89f},
{0xaa2,0xaa2,0xc8a1},
{0xaa3,0xaa3,0xc8a8},
{0xaa4,0xaa5,0xc8bc},
{0xaa6,0xaa6,0xc8c4},
{0xaa7,0xaa7,0xc8c8},
{0xaa8,0xaa8,0xc8cc},
{0xaa9,0xaaa,0xc8d4},
{0xaab,0xaab,0xc8d7},
{0xaac,0xaac,0xc8d9},
{0xaad,0xaae,0xc8e0},
{0xaaf,0xaaf,0xc8e4},
{0xab0,0xab0,0xc8f5},
{0xab1,0xab2,0xc8fc},
{0xab3,0xab3,0xc900},
{0xab4,0xab6,0xc904},
{0xab7,0xab8,0xc90c},
{0xab9,0xab9,0xc90f},
{0xaba,0xaba,0xc911},
{0xabb,0xabb,0xc918},
{0xabc,0xabc,0xc92c},
{0xabd,0xabd,0xc934},
{0xabe,0xabf,0xc950},
{0xac0,0xac0,0xc954},
{0xac1,0xac1,0xc958},
{0xac2,0xac3,0xc960},
{0xac4,0xac4,0xc963},
{0xac5,0xac5,0xc96c},
{0xac6,0xac6,0xc970},
{0xac7,0xac7,0xc974},
{0xac8,0xac8,0xc97c},
{0xac9,0xaca,0xc988},
{0xacb,0xacb,0xc98c},
{0xacc,0xacc,0xc990},
{0xacd,0xace,0xc998},
{0xacf,0xacf,0xc99b},
{0xad0,0xad0,0xc99d},
{0xad1,0xad2,0xc9c0},
{0xad3,0xad3,0xc9c4},
{0xad4,0xad5,0xc9c7},
{0xad6,0xad6,0xc9ca},
{0xad7,0xad8,0xc9d0},
{0xad9,0xad9,0xc9d3},
{0xada,0xadb,0xc9d5},
{0xadc,0xadd,0xc9d9},
{0xade,0xadf,0xc9dc},
{0xae0,0xae0,0xc9e0},
{0xae1,0xae1,0xc9e2},
{0xae2,0xae2,0xc9e4},
{0xae3,0xae3,0xc9e7},
{0xae4,0xae5,0xc9ec},
{0xae6,0xae8,0xc9ef},
{0xae9,0xaea,0xc9f8},
{0xaeb,0xaeb,0xc9fc},
{0xaec,0xaec,0xca00},
{0xaed,0xaee,0xca08},
{0xaef,0xaf1,0xca0b},
{0xaf2,0xaf2,0xca14},
{0xaf3,0xaf3,0xca18},
{0xaf4,0xaf4,0xca29},
{0xaf5,0xaf6,0xca4c},
{0xaf7,0xaf7,0xca50},
{0xaf8,0xaf8,0xca54},
{0xaf9,0xafa,0xca5c},
{0xafb,0xafd,0xca5f},
{0xafe,0xafe,0xca68},
{0xaff,0xaff,0xca7d},
{0xb00,0xb00,0xca84},
{0xb01,0xb01,0xca98},
{0xb02,0xb03,0xcabc},
{0xb04,0xb04,0xcac0},
{0xb05,0xb05,0xcac4},
{0xb06,0xb07,0xcacc},
{0xb08,0xb08,0xcacf},
{0xb09,0xb09,0xcad1},
{0xb0a,0xb0a,0xcad3},
{0xb0b,0xb0c,0xcad8},
{0xb0d,0xb0d,0xcae0},
{0xb0e,0xb0e,0xcaec},
{0xb0f,0xb0f,0xcaf4},
{0xb10,0xb10,0xcb08},
{0xb11,0xb11,0xcb10},
{0xb12,0xb12,0xcb14},
{0xb13,0xb13,0xcb18},
{0xb14,0xb15,0xcb20},
{0xb16,0xb16,0xcb41},
{0xb17,0xb18,0xcb48},
{0xb19,0xb19,0xcb4c},
{0xb1a,0xb1a,0xcb50},
{0xb1b,0xb1c,0xcb58},
{0xb1d,0xb1d,0xcb5d},
{0xb1e,0xb1e,0xcb64},
{0xb1f,0xb20,0xcb78},
{0xb21,0xb21,0xcb9c},
{0xb22,0xb22,0xcbb8},
{0xb23,0xb23,0xcbd4},
{0xb24,0xb24,0xcbe4},
{0xb25,0xb25,0xcbe7},
{0xb26,0xb26,0xcbe9},
{0xb27,0xb28,0xcc0c},
{0xb29,0xb29,0xcc10},
{0xb2a,0xb2a,0xcc14},
{0xb2b,0xb2c,0xcc1c},
{0xb2d,0xb2e,0xcc21},
{0xb2f,0xb31,0xcc27},
{0xb32,0xb32,0xcc2c},
{0xb33,0xb33,0xcc2e},
{0xb34,0xb34,0xcc30},
{0xb35,0xb36,0xcc38},
{0xb37,0xb3a,0xcc3b},
{0xb3b,0xb3c,0xcc44},
{0xb3d,0xb3d,0xcc48},
{0xb3e,0xb3e,0xcc4c},
{0xb3f,0xb40,0xcc54},
{0xb41,0xb43,0xcc57},
{0xb44,0xb44,0xcc60},
{0xb45,0xb45,0xcc64},
{0xb46,0xb46,0xcc66},
{0xb47,0xb47,0xcc68},
{0xb48,0xb48,0xcc70},
{0xb49,0xb49,0xcc75},
{0xb4a,0xb4b,0xcc98},
{0xb4c,0xb4c,0xcc9c},
{0xb4d,0xb4d,0xcca0},
{0xb4e,0xb4f,0xcca8},
{0xb50,0xb52,0xccab},
{0xb53,0xb54,0xccb4},
{0xb55,0xb55,0xccb8},
{0xb56,0xb56,0xccbc},
{0xb57,0xb58,0xccc4},
{0xb59,0xb59,0xccc7},
{0xb5a,0xb5a,0xccc9},
{0xb5b,0xb5b,0xccd0},
{0xb5c,0xb5c,0xccd4},
{0xb5d,0xb5d,0xcce4},
{0xb5e,0xb5e,0xccec},
{0xb5f,0xb5f,0xccf0},
{0xb60,0xb60,0xcd01},
{0xb61,0xb62,0xcd08},
{0xb63,0xb63,0xcd0c},
{0xb64,0xb64,0xcd10},
{0xb65,0xb66,0xcd18},
{0xb67,0xb67,0xcd1b},
{0xb68,0xb68,0xcd1d},
{0xb69,0xb69,0xcd24},
{0xb6a,0xb6a,0xcd28},
{0xb6b,0xb6b,0xcd2c},
{0xb6c,0xb6c,0xcd39},
{0xb6d,0xb6d,0xcd5c},
{0xb6e,0xb6e,0xcd60},
{0xb6f,0xb6f,0xcd64},
{0xb70,0xb71,0xcd6c},
{0xb72,0xb72,0xcd6f},
{0xb73,0xb73,0xcd71},
{0xb74,0xb74,0xcd78},
{0xb75,0xb75,0xcd88},
{0xb76,0xb77,0xcd94},
{0xb78,0xb78,0xcd98},
{0xb79,0xb79,0xcd9c},
{0xb7a,0xb7b,0xcda4},
{0xb7c,0xb7c,0xcda7},
{0xb7d,0xb7d,0xcda9},
{0xb7e,0xb7e,0xcdb0},
{0xb7f,0xb7f,0xcdc4},
{0xb80,0xb80,0xcdcc},
{0xb81,0xb81,0xcdd0},
{0xb82,0xb82,0xcde8},
{0xb83,0xb83,0xcdec},
{0xb84,0xb84,0xcdf0},
{0xb85,0xb86,0xcdf8},
{0xb87,0xb87,0xcdfb},
{0xb88,0xb88,0xcdfd},
{0xb89,0xb89,0xce04},
{0xb8a,0xb8a,0xce08},
{0xb8b,0xb8b,0xce0c},
{0xb8c,0xb8c,0xce14},
{0xb8d,0xb8d,0xce19},
{0xb8e,0xb8f,0xce20},
{0xb90,0xb90,0xce24},
{0xb91,0xb91,0xce28},
{0xb92,0xb93,0xce30},
{0xb94,0xb94,0xce33},
{0xb95,0xb95,0xce35},
{0xb96,0xb97,0xce58},
{0xb98,0xb98,0xce5c},
{0xb99,0xb9b,0xce5f},
{0xb9c,0xb9d,0xce68},
{0xb9e,0xb9e,0xce6b},
{0xb9f,0xb9f,0xce6d},
{0xba0,0xba1,0xce74},
{0xba2,0xba2,0xce78},
{0xba3,0xba3,0xce7c},
{0xba4,0xba5,0xce84},
{0xba6,0xba6,0xce87},
{0xba7,0xba7,0xce89},
{0xba8,0xba9,0xce90},
{0xbaa,0xbaa,0xce94},
{0xbab,0xbab,0xce98},
{0xbac,0xbad,0xcea0},
{0xbae,0xbb0,0xcea3},
{0xbb1,0xbb2,0xceac},
{0xbb3,0xbb3,0xcec1},
{0xbb4,0xbb5,0xcee4},
{0xbb6,0xbb6,0xcee8},
{0xbb7,0xbb8,0xceeb},
{0xbb9,0xbba,0xcef4},
{0xbbb,0xbbd,0xcef7},
{0xbbe,0xbbf,0xcf00},
{0xbc0,0xbc0,0xcf04},
{0xbc1,0xbc1,0xcf08},
{0xbc2,0xbc3,0xcf10},
{0xbc4,0xbc4,0xcf13},
{0xbc5,0xbc5,0xcf15},
{0xbc6,0xbc6,0xcf1c},
{0xbc7,0xbc7,0xcf20},
{0xbc8,0xbc8,0xcf24},
{0xbc9,0xbca,0xcf2c},
{0xbcb,0xbcd,0xcf2f},
{0xbce,0xbce,0xcf38},
{0xbcf,0xbd0,0xcf54},
{0xbd1,0xbd1,0xcf58},
{0xbd2,0xbd2,0xcf5c},
{0xbd3,0xbd4,0xcf64},
{0xbd5,0xbd5,0xcf67},
{0xbd6,0xbd6,0xcf69},
{0xbd7,0xbd8,0xcf70},
{0xbd9,0xbd9,0xcf74},
{0xbda,0xbda,0xcf78},
{0xbdb,0xbdb,0xcf80},
{0xbdc,0xbdc,0xcf85},
{0xbdd,0xbdd,0xcf8c},
{0xbde,0xbde,0xcfa1},
{0xbdf,0xbdf,0xcfa8},
{0xbe0,0xbe0,0xcfb0},
{0xbe1,0xbe1,0xcfc4},
{0xbe2,0xbe3,0xcfe0},
{0xbe4,0xbe4,0xcfe4},
{0xbe5,0xbe5,0xcfe8},
{0xbe6,0xbe7,0xcff0},
{0xbe8,0xbe8,0xcff3},
{0xbe9,0xbe9,0xcff5},
{0xbea,0xbea,0xcffc},
{0xbeb,0xbeb,0xd000},
{0xbec,0xbec,0xd004},
{0xbed,0xbed,0xd011},
{0xbee,0xbee,0xd018},
{0xbef,0xbef,0xd02d},
{0xbf0,0xbf1,0xd034},
{0xbf2,0xbf2,0xd038},
{0xbf3,0xbf3,0xd03c},
{0xbf4,0xbf5,0xd044},
{0xbf6,0xbf6,0xd047},
{0xbf7,0xbf7,0xd049},
{0xbf8,0xbf8,0xd050},
{0xbf9,0xbf9,0xd054},
{0xbfa,0xbfa,0xd058},
{0xbfb,0xbfb,0xd060},
{0xbfc,0xbfd,0xd06c},
{0xbfe,0xbfe,0xd070},
{0xbff,0xbff,0xd074},
{0xc00,0xc01,0xd07c},
{0xc02,0xc02,0xd081},
{0xc03,0xc04,0xd0a4},
{0xc05,0xc05,0xd0a8},
{0xc06,0xc06,0xd0ac},
{0xc07,0xc08,0xd0b4},
{0xc09,0xc09,0xd0b7},
{0xc0a,0xc0a,0xd0b9},
{0xc0b,0xc0c,0xd0c0},
{0xc0d,0xc0d,0xd0c4},
{0xc0e,0xc0f,0xd0c8},
{0xc10,0xc11,0xd0d0},
{0xc12,0xc14,0xd0d3},
{0xc15,0xc16,0xd0dc},
{0xc17,0xc17,0xd0e0},
{0xc18,0xc18,0xd0e4},
{0xc19,0xc1a,0xd0ec},
{0xc1b,0xc1d,0xd0ef},
{0xc1e,0xc1e,0xd0f8},
{0xc1f,0xc1f,0xd10d},
{0xc20,0xc21,0xd130},
{0xc22,0xc22,0xd134},
{0xc23,0xc23,0xd138},
{0xc24,0xc24,0xd13a},
{0xc25,0xc26,0xd140},
{0xc27,0xc29,0xd143},
{0xc2a,0xc2b,0xd14c},
{0xc2c,0xc2c,0xd150},
{0xc2d,0xc2d,0xd154},
{0xc2e,0xc2f,0xd15c},
{0xc30,0xc30,0xd15f},
{0xc31,0xc31,0xd161},
{0xc32,0xc32,0xd168},
{0xc33,0xc33,0xd16c},
{0xc34,0xc34,0xd17c},
{0xc35,0xc35,0xd184},
{0xc36,0xc36,0xd188},
{0xc37,0xc38,0xd1a0},
{0xc39,0xc39,0xd1a4},
{0xc3a,0xc3a,0xd1a8},
{0xc3b,0xc3c,0xd1b0},
{0xc3d,0xc3d,0xd1b3},
{0xc3e,0xc3e,0xd1b5},
{0xc3f,0xc3f,0xd1ba},
{0xc40,0xc40,0xd1bc},
{0xc41,0xc41,0xd1c0},
{0xc42,0xc42,0xd1d8},
{0xc43,0xc43,0xd1f4},
{0xc44,0xc44,0xd1f8},
{0xc45,0xc45,0xd207},
{0xc46,0xc46,0xd209},
{0xc47,0xc47,0xd210},
{0xc48,0xc49,0xd22c},
{0xc4a,0xc4a,0xd230},
{0xc4b,0xc4b,0xd234},
{0xc4c,0xc4d,0xd23c},
{0xc4e,0xc4e,0xd23f},
{0xc4f,0xc4f,0xd241},
{0xc50,0xc50,0xd248},
{0xc51,0xc51,0xd25c},
{0xc52,0xc52,0xd264},
{0xc53,0xc54,0xd280},
{0xc55,0xc55,0xd284},
{0xc56,0xc56,0xd288},
{0xc57,0xc58,0xd290},
{0xc59,0xc59,0xd295},
{0xc5a,0xc5a,0xd29c},
{0xc5b,0xc5b,0xd2a0},
{0xc5c,0xc5c,0xd2a4},
{0xc5d,0xc5d,0xd2ac},
{0xc5e,0xc5e,0xd2b1},
{0xc5f,0xc60,0xd2b8},
{0xc61,0xc61,0xd2bc},
{0xc62,0xc63,0xd2bf},
{0xc64,0xc64,0xd2c2},
{0xc65,0xc66,0xd2c8},
{0xc67,0xc67,0xd2cb},
{0xc68,0xc68,0xd2d4},
{0xc69,0xc69,0xd2d8},
{0xc6a,0xc6a,0xd2dc},
{0xc6b,0xc6c,0xd2e4},
{0xc6d,0xc6e,0xd2f0},
{0xc6f,0xc6f,0xd2f4},
{0xc70,0xc70,0xd2f8},
{0xc71,0xc72,0xd300},
{0xc73,0xc73,0xd303},
{0xc74,0xc74,0xd305},
{0xc75,0xc77,0xd30c},
{0xc78,0xc78,0xd310},
{0xc79,0xc79,0xd314},
{0xc7a,0xc7a,0xd316},
{0xc7b,0xc7c,0xd31c},
{0xc7d,0xc7f,0xd31f},
{0xc80,0xc80,0xd325},
{0xc81,0xc82,0xd328},
{0xc83,0xc83,0xd32c},
{0xc84,0xc84,0xd330},
{0xc85,0xc86,0xd338},
{0xc87,0xc89,0xd33b},
{0xc8a,0xc8b,0xd344},
{0xc8c,0xc8d,0xd37c},
{0xc8e,0xc8e,0xd380},
{0xc8f,0xc8f,0xd384},
{0xc90,0xc91,0xd38c},
{0xc92,0xc94,0xd38f},
{0xc95,0xc96,0xd398},
{0xc97,0xc97,0xd39c},
{0xc98,0xc98,0xd3a0},
{0xc99,0xc9a,0xd3a8},
{0xc9b,0xc9b,0xd3ab},
{0xc9c,0xc9c,0xd3ad},
{0xc9d,0xc9d,0xd3b4},
{0xc9e,0xc9e,0xd3b8},
{0xc9f,0xc9f,0xd3bc},
{0xca0,0xca1,0xd3c4},
{0xca2,0xca3,0xd3c8},
{0xca4,0xca4,0xd3d0},
{0xca5,0xca5,0xd3d8},
{0xca6,0xca6,0xd3e1},
{0xca7,0xca7,0xd3e3},
{0xca8,0xca9,0xd3ec},
{0xcaa,0xcaa,0xd3f0},
{0xcab,0xcab,0xd3f4},
{0xcac,0xcad,0xd3fc},
{0xcae,0xcae,0xd3ff},
{0xcaf,0xcaf,0xd401},
{0xcb0,0xcb0,0xd408},
{0xcb1,0xcb1,0xd41d},
{0xcb2,0xcb2,0xd440},
{0xcb3,0xcb3,0xd444},
{0xcb4,0xcb4,0xd45c},
{0xcb5,0xcb5,0xd460},
{0xcb6,0xcb6,0xd464},
{0xcb7,0xcb7,0xd46d},
{0xcb8,0xcb8,0xd46f},
{0xcb9,0xcba,0xd478},
{0xcbb,0xcbb,0xd47c},
{0xcbc,0xcbd,0xd47f},
{0xcbe,0xcbe,0xd482},
{0xcbf,0xcc0,0xd488},
{0xcc1,0xcc1,0xd48b},
{0xcc2,0xcc2,0xd48d},
{0xcc3,0xcc3,0xd494},
{0xcc4,0xcc4,0xd4a9},
{0xcc5,0xcc5,0xd4cc},
{0xcc6,0xcc6,0xd4d0},
{0xcc7,0xcc7,0xd4d4},
{0xcc8,0xcc8,0xd4dc},
{0xcc9,0xcc9,0xd4df},
{0xcca,0xcca,0xd4e8},
{0xccb,0xccb,0xd4ec},
{0xccc,0xccc,0xd4f0},
{0xccd,0xccd,0xd4f8},
{0xcce,0xcce,0xd4fb},
{0xccf,0xccf,0xd4fd},
{0xcd0,0xcd0,0xd504},
{0xcd1,0xcd1,0xd508},
{0xcd2,0xcd2,0xd50c},
{0xcd3,0xcd4,0xd514},
{0xcd5,0xcd5,0xd517},
{0xcd6,0xcd7,0xd53c},
{0xcd8,0xcd8,0xd540},
{0xcd9,0xcd9,0xd544},
{0xcda,0xcdb,0xd54c},
{0xcdc,0xcdc,0xd54f},
{0xcdd,0xcdd,0xd551},
{0xcde,0xcdf,0xd558},
{0xce0,0xce0,0xd55c},
{0xce1,0xce1,0xd560},
{0xce2,0xce2,0xd565},
{0xce3,0xce4,0xd568},
{0xce5,0xce5,0xd56b},
{0xce6,0xce6,0xd56d},
{0xce7,0xce8,0xd574},
{0xce9,0xce9,0xd578},
{0xcea,0xcea,0xd57c},
{0xceb,0xcec,0xd584},
{0xced,0xcef,0xd587},
{0xcf0,0xcf0,0xd590},
{0xcf1,0xcf1,0xd5a5},
{0xcf2,0xcf3,0xd5c8},
{0xcf4,0xcf4,0xd5cc},
{0xcf5,0xcf5,0xd5d0},
{0xcf6,0xcf6,0xd5d2},
{0xcf7,0xcf8,0xd5d8},
{0xcf9,0xcf9,0xd5db},
{0xcfa,0xcfa,0xd5dd},
{0xcfb,0xcfc,0xd5e4},
{0xcfd,0xcfd,0xd5e8},
{0xcfe,0xcfe,0xd5ec},
{0xcff,0xd00,0xd5f4},
{0xd01,0xd01,0xd5f7},
{0xd02,0xd02,0xd5f9},
{0xd03,0xd04,0xd600},
{0xd05,0xd05,0xd604},
{0xd06,0xd06,0xd608},
{0xd07,0xd08,0xd610},
{0xd09,0xd0b,0xd613},
{0xd0c,0xd0c,0xd61c},
{0xd0d,0xd0d,0xd620},
{0xd0e,0xd0e,0xd624},
{0xd0f,0xd0f,0xd62d},
{0xd10,0xd11,0xd638},
{0xd12,0xd12,0xd63c},
{0xd13,0xd13,0xd640},
{0xd14,0xd14,0xd645},
{0xd15,0xd16,0xd648},
{0xd17,0xd17,0xd64b},
{0xd18,0xd18,0xd64d},
{0xd19,0xd19,0xd651},
{0xd1a,0xd1b,0xd654},
{0xd1c,0xd1c,0xd658},
{0xd1d,0xd1d,0xd65c},
{0xd1e,0xd1e,0xd667},
{0xd1f,0xd1f,0xd669},
{0xd20,0xd21,0xd670},
{0xd22,0xd22,0xd674},
{0xd23,0xd23,0xd683},
{0xd24,0xd24,0xd685},
{0xd25,0xd26,0xd68c},
{0xd27,0xd27,0xd690},
{0xd28,0xd28,0xd694},
{0xd29,0xd29,0xd69d},
{0xd2a,0xd2a,0xd69f},
{0xd2b,0xd2b,0xd6a1},
{0xd2c,0xd2c,0xd6a8},
{0xd2d,0xd2d,0xd6ac},
{0xd2e,0xd2e,0xd6b0},
{0xd2f,0xd2f,0xd6b9},
{0xd30,0xd30,0xd6bb},
{0xd31,0xd32,0xd6c4},
{0xd33,0xd33,0xd6c8},
{0xd34,0xd34,0xd6cc},
{0xd35,0xd35,0xd6d1},
{0xd36,0xd36,0xd6d4},
{0xd37,0xd37,0xd6d7},
{0xd38,0xd38,0xd6d9},
{0xd39,0xd39,0xd6e0},
{0xd3a,0xd3a,0xd6e4},
{0xd3b,0xd3b,0xd6e8},
{0xd3c,0xd3c,0xd6f0},
{0xd3d,0xd3d,0xd6f5},
{0xd3e,0xd3f,0xd6fc},
{0xd40,0xd40,0xd700},
{0xd41,0xd41,0xd704},
{0xd42,0xd42,0xd711},
{0xd43,0xd44,0xd718},
{0xd45,0xd45,0xd71c},
{0xd46,0xd46,0xd720},
{0xd47,0xd48,0xd728},
{0xd49,0xd49,0xd72b},
{0xd4a,0xd4a,0xd72d},
{0xd4b,0xd4c,0xd734},
{0xd4d,0xd4d,0xd738},
{0xd4e,0xd4e,0xd73c},
{0xd4f,0xd4f,0xd744},
{0xd50,0xd50,0xd747},
{0xd51,0xd51,0xd749},
{0xd52,0xd53,0xd750},
{0xd54,0xd54,0xd754},
{0xd55,0xd58,0xd756},
{0xd59,0xd5a,0xd760},
{0xd5b,0xd5b,0xd763},
{0xd5c,0xd5c,0xd765},
{0xd5d,0xd5d,0xd769},
{0xd5e,0xd5e,0xd76c},
{0xd5f,0xd5f,0xd770},
{0xd60,0xd60,0xd774},
{0xd61,0xd62,0xd77c},
{0xd63,0xd63,0xd781},
{0xd64,0xd65,0xd788},
{0xd66,0xd66,0xd78c},
{0xd67,0xd67,0xd790},
{0xd68,0xd69,0xd798},
{0xd6a,0xd6a,0xd79b},
{0xd6b,0xd6b,0xd79d},
{0xd6c,0xd6c,0x4f3d},
{0xd6d,0xd6d,0x4f73},
{0xd6e,0xd6e,0x5047},
{0xd6f,0xd6f,0x50f9},
{0xd70,0xd70,0x52a0},
{0xd71,0xd71,0x53ef},
{0xd72,0xd72,0x5475},
{0xd73,0xd73,0x54e5},
{0xd74,0xd74,0x5609},
{0xd75,0xd75,0x5ac1},
{0xd76,0xd76,0x5bb6},
{0xd77,0xd77,0x6687},
{0xd78,0xd79,0x67b6},
{0xd7a,0xd7a,0x67ef},
{0xd7b,0xd7b,0x6b4c},
{0xd7c,0xd7c,0x73c2},
{0xd7d,0xd7d,0x75c2},
{0xd7e,0xd7e,0x7a3c},
{0xd7f,0xd7f,0x82db},
{0xd80,0xd80,0x8304},
{0xd81,0xd81,0x8857},
{0xd82,0xd82,0x8888},
{0xd83,0xd83,0x8a36},
{0xd84,0xd84,0x8cc8},
{0xd85,0xd85,0x8dcf},
{0xd86,0xd86,0x8efb},
{0xd87,0xd87,0x8fe6},
{0xd88,0xd88,0x99d5},
{0xd89,0xd89,0x523b},
{0xd8a,0xd8a,0x5374},
{0xd8b,0xd8b,0x5404},
{0xd8c,0xd8c,0x606a},
{0xd8d,0xd8d,0x6164},
{0xd8e,0xd8e,0x6bbc},
{0xd8f,0xd8f,0x73cf},
{0xd90,0xd90,0x811a},
{0xd91,0xd91,0x89ba},
{0xd92,0xd92,0x89d2},
{0xd93,0xd93,0x95a3},
{0xd94,0xd94,0x4f83},
{0xd95,0xd95,0x520a},
{0xd96,0xd96,0x58be},
{0xd97,0xd97,0x5978},
{0xd98,0xd98,0x59e6},
{0xd99,0xd99,0x5e72},
{0xd9a,0xd9a,0x5e79},
{0xd9b,0xd9b,0x61c7},
{0xd9c,0xd9c,0x63c0},
{0xd9d,0xd9d,0x6746},
{0xd9e,0xd9e,0x67ec},
{0xd9f,0xd9f,0x687f},
{0xda0,0xda0,0x6f97},
{0xda1,0xda1,0x764e},
{0xda2,0xda2,0x770b},
{0xda3,0xda3,0x78f5},
{0xda4,0xda4,0x7a08},
{0xda5,0xda5,0x7aff},
{0xda6,0xda6,0x7c21},
{0xda7,0xda7,0x809d},
{0xda8,0xda8,0x826e},
{0xda9,0xda9,0x8271},
{0xdaa,0xdaa,0x8aeb},
{0xdab,0xdab,0x9593},
{0xdac,0xdac,0x4e6b},
{0xdad,0xdad,0x559d},
{0xdae,0xdae,0x66f7},
{0xdaf,0xdaf,0x6e34},
{0xdb0,0xdb0,0x78a3},
{0xdb1,0xdb1,0x7aed},
{0xdb2,0xdb2,0x845b},
{0xdb3,0xdb3,0x8910},
{0xdb4,0xdb4,0x874e},
{0xdb5,0xdb5,0x97a8},
{0xdb6,0xdb6,0x52d8},
{0xdb7,0xdb7,0x574e},
{0xdb8,0xdb8,0x582a},
{0xdb9,0xdb9,0x5d4c},
{0xdba,0xdba,0x611f},
{0xdbb,0xdbb,0x61be},
{0xdbc,0xdbc,0x6221},
{0xdbd,0xdbd,0x6562},
{0xdbe,0xdbe,0x67d1},
{0xdbf,0xdbf,0x6a44},
{0xdc0,0xdc0,0x6e1b},
{0xdc1,0xdc1,0x7518},
{0xdc2,0xdc2,0x75b3},
{0xdc3,0xdc3,0x76e3},
{0xdc4,0xdc4,0x77b0},
{0xdc5,0xdc5,0x7d3a},
{0xdc6,0xdc6,0x90af},
{0xdc7,0xdc8,0x9451},
{0xdc9,0xdc9,0x9f95},
{0xdca,0xdca,0x5323},
{0xdcb,0xdcb,0x5cac},
{0xdcc,0xdcc,0x7532},
{0xdcd,0xdcd,0x80db},
{0xdce,0xdce,0x9240},
{0xdcf,0xdcf,0x9598},
{0xdd0,0xdd0,0x525b},
{0xdd1,0xdd1,0x5808},
{0xdd2,0xdd2,0x59dc},
{0xdd3,0xdd3,0x5ca1},
{0xdd4,0xdd4,0x5d17},
{0xdd5,0xdd5,0x5eb7},
{0xdd6,0xdd6,0x5f3a},
{0xdd7,0xdd7,0x5f4a},
{0xdd8,0xdd8,0x6177},
{0xdd9,0xdd9,0x6c5f},
{0xdda,0xdda,0x757a},
{0xddb,0xddb,0x7586},
{0xddc,0xddc,0x7ce0},
{0xddd,0xddd,0x7d73},
{0xdde,0xdde,0x7db1},
{0xddf,0xddf,0x7f8c},
{0xde0,0xde0,0x8154},
{0xde1,0xde1,0x8221},
{0xde2,0xde2,0x8591},
{0xde3,0xde3,0x8941},
{0xde4,0xde4,0x8b1b},
{0xde5,0xde5,0x92fc},
{0xde6,0xde6,0x964d},
{0xde7,0xde7,0x9c47},
{0xde8,0xde8,0x4ecb},
{0xde9,0xde9,0x4ef7},
{0xdea,0xdea,0x500b},
{0xdeb,0xdeb,0x51f1},
{0xdec,0xdec,0x584f},
{0xded,0xded,0x6137},
{0xdee,0xdee,0x613e},
{0xdef,0xdef,0x6168},
{0xdf0,0xdf0,0x6539},
{0xdf1,0xdf1,0x69ea},
{0xdf2,0xdf2,0x6f11},
{0xdf3,0xdf3,0x75a5},
{0xdf4,0xdf4,0x7686},
{0xdf5,0xdf5,0x76d6},
{0xdf6,0xdf6,0x7b87},
{0xdf7,0xdf7,0x82a5},
{0xdf8,0xdf8,0x84cb},
{0xdf9,0xdf9,0x93a7},
{0xdfa,0xdfa,0x958b},
{0xdfb,0xdfb,0x5580},
{0xdfc,0xdfc,0x5ba2},
{0xdfd,0xdfd,0x5751},
{0xdfe,0xdfe,0x7cb3},
{0xdff,0xdff,0x7fb9},
{0xe00,0xe00,0x91b5},
{0xe01,0xe01,0x5028},
{0xe02,0xe02,0x53bb},
{0xe03,0xe03,0x5c45},
{0xe04,0xe04,0x5de8},
{0xe05,0xe05,0x62d2},
{0xe06,0xe06,0x636e},
{0xe07,0xe07,0x64da},
{0xe08,0xe08,0x64e7},
{0xe09,0xe09,0x6e20},
{0xe0a,0xe0a,0x70ac},
{0xe0b,0xe0b,0x795b},
{0xe0c,0xe0c,0x8ddd},
{0xe0d,0xe0d,0x8e1e},
{0xe0e,0xe0e,0x907d},
{0xe0f,0xe0f,0x9245},
{0xe10,0xe10,0x92f8},
{0xe11,0xe11,0x4e7e},
{0xe12,0xe12,0x4ef6},
{0xe13,0xe13,0x5065},
{0xe14,0xe14,0x5dfe},
{0xe15,0xe15,0x5efa},
{0xe16,0xe16,0x6106},
{0xe17,0xe17,0x6957},
{0xe18,0xe18,0x8171},
{0xe19,0xe19,0x8654},
{0xe1a,0xe1a,0x8e47},
{0xe1b,0xe1b,0x9375},
{0xe1c,0xe1c,0x9a2b},
{0xe1d,0xe1d,0x4e5e},
{0xe1e,0xe1e,0x5091},
{0xe1f,0xe1f,0x6770},
{0xe20,0xe20,0x6840},
{0xe21,0xe21,0x5109},
{0xe22,0xe22,0x528d},
{0xe23,0xe23,0x5292},
{0xe24,0xe24,0x6aa2},
{0xe25,0xe25,0x77bc},
{0xe26,0xe26,0x9210},
{0xe27,0xe27,0x9ed4},
{0xe28,0xe28,0x52ab},
{0xe29,0xe29,0x602f},
{0xe2a,0xe2a,0x8ff2},
{0xe2b,0xe2b,0x5048},
{0xe2c,0xe2c,0x61a9},
{0xe2d,0xe2d,0x63ed},
{0xe2e,0xe2e,0x64ca},
{0xe2f,0xe2f,0x683c},
{0xe30,0xe30,0x6a84},
{0xe31,0xe31,0x6fc0},
{0xe32,0xe32,0x8188},
{0xe33,0xe33,0x89a1},
{0xe34,0xe34,0x9694},
{0xe35,0xe35,0x5805},
{0xe36,0xe36,0x727d},
{0xe37,0xe37,0x72ac},
{0xe38,0xe38,0x7504},
{0xe39,0xe39,0x7d79},
{0xe3a,0xe3a,0x7e6d},
{0xe3b,0xe3b,0x80a9},
{0xe3c,0xe3c,0x898b},
{0xe3d,0xe3d,0x8b74},
{0xe3e,0xe3e,0x9063},
{0xe3f,0xe3f,0x9d51},
{0xe40,0xe40,0x6289},
{0xe41,0xe41,0x6c7a},
{0xe42,0xe42,0x6f54},
{0xe43,0xe43,0x7d50},
{0xe44,0xe44,0x7f3a},
{0xe45,0xe45,0x8a23},
{0xe46,0xe46,0x517c},
{0xe47,0xe47,0x614a},
{0xe48,0xe48,0x7b9d},
{0xe49,0xe49,0x8b19},
{0xe4a,0xe4a,0x9257},
{0xe4b,0xe4b,0x938c},
{0xe4c,0xe4c,0x4eac},
{0xe4d,0xe4d,0x4fd3},
{0xe4e,0xe4e,0x501e},
{0xe4f,0xe4f,0x50be},
{0xe50,0xe50,0x5106},
{0xe51,0xe51,0x52c1},
{0xe52,0xe52,0x52cd},
{0xe53,0xe53,0x537f},
{0xe54,0xe54,0x5770},
{0xe55,0xe55,0x5883},
{0xe56,0xe56,0x5e9a},
{0xe57,0xe57,0x5f91},
{0xe58,0xe58,0x6176},
{0xe59,0xe59,0x61ac},
{0xe5a,0xe5a,0x64ce},
{0xe5b,0xe5b,0x656c},
{0xe5c,0xe5c,0x666f},
{0xe5d,0xe5d,0x66bb},
{0xe5e,0xe5e,0x66f4},
{0xe5f,0xe5f,0x6897},
{0xe60,0xe60,0x6d87},
{0xe61,0xe61,0x7085},
{0xe62,0xe62,0x70f1},
{0xe63,0xe63,0x749f},
{0xe64,0xe64,0x74a5},
{0xe65,0xe65,0x74ca},
{0xe66,0xe66,0x75d9},
{0xe67,0xe67,0x786c},
{0xe68,0xe68,0x78ec},
{0xe69,0xe69,0x7adf},
{0xe6a,0xe6a,0x7af6},
{0xe6b,0xe6b,0x7d45},
{0xe6c,0xe6c,0x7d93},
{0xe6d,0xe6d,0x8015},
{0xe6e,0xe6e,0x803f},
{0xe6f,0xe6f,0x811b},
{0xe70,0xe70,0x8396},
{0xe71,0xe71,0x8b66},
{0xe72,0xe72,0x8f15},
{0xe73,0xe73,0x9015},
{0xe74,0xe74,0x93e1},
{0xe75,0xe75,0x9803},
{0xe76,0xe76,0x9838},
{0xe77,0xe77,0x9a5a},
{0xe78,0xe78,0x9be8},
{0xe79,0xe79,0x4fc2},
{0xe7a,0xe7a,0x5553},
{0xe7b,0xe7b,0x583a},
{0xe7c,0xe7c,0x5951},
{0xe7d,0xe7d,0x5b63},
{0xe7e,0xe7e,0x5c46},
{0xe7f,0xe7f,0x60b8},
{0xe80,0xe80,0x6212},
{0xe81,0xe81,0x6842},
{0xe82,0xe82,0x68b0},
{0xe83,0xe83,0x68e8},
{0xe84,0xe84,0x6eaa},
{0xe85,0xe85,0x754c},
{0xe86,0xe86,0x7678},
{0xe87,0xe87,0x78ce},
{0xe88,0xe88,0x7a3d},
{0xe89,0xe89,0x7cfb},
{0xe8a,0xe8a,0x7e6b},
{0xe8b,0xe8b,0x7e7c},
{0xe8c,0xe8c,0x8a08},
{0xe8d,0xe8d,0x8aa1},
{0xe8e,0xe8e,0x8c3f},
{0xe8f,0xe8f,0x968e},
{0xe90,0xe90,0x9dc4},
{0xe91,0xe91,0x53e4},
{0xe92,0xe92,0x53e9},
{0xe93,0xe93,0x544a},
{0xe94,0xe94,0x5471},
{0xe95,0xe95,0x56fa},
{0xe96,0xe96,0x59d1},
{0xe97,0xe97,0x5b64},
{0xe98,0xe98,0x5c3b},
{0xe99,0xe99,0x5eab},
{0xe9a,0xe9a,0x62f7},
{0xe9b,0xe9b,0x6537},
{0xe9c,0xe9c,0x6545},
{0xe9d,0xe9d,0x6572},
{0xe9e,0xe9e,0x66a0},
{0xe9f,0xe9f,0x67af},
{0xea0,0xea0,0x69c1},
{0xea1,0xea1,0x6cbd},
{0xea2,0xea2,0x75fc},
{0xea3,0xea3,0x7690},
{0xea4,0xea4,0x777e},
{0xea5,0xea5,0x7a3f},
{0xea6,0xea6,0x7f94},
{0xea7,0xea7,0x8003},
{0xea8,0xea8,0x80a1},
{0xea9,0xea9,0x818f},
{0xeaa,0xeaa,0x82e6},
{0xeab,0xeab,0x82fd},
{0xeac,0xeac,0x83f0},
{0xead,0xead,0x85c1},
{0xeae,0xeae,0x8831},
{0xeaf,0xeaf,0x88b4},
{0xeb0,0xeb0,0x8aa5},
{0xeb1,0xeb1,0x8f9c},
{0xeb2,0xeb2,0x932e},
{0xeb3,0xeb3,0x96c7},
{0xeb4,0xeb4,0x9867},
{0xeb5,0xeb5,0x9ad8},
{0xeb6,0xeb6,0x9f13},
{0xeb7,0xeb7,0x54ed},
{0xeb8,0xeb8,0x659b},
{0xeb9,0xeb9,0x66f2},
{0xeba,0xeba,0x688f},
{0xebb,0xebb,0x7a40},
{0xebc,0xebc,0x8c37},
{0xebd,0xebd,0x9d60},
{0xebe,0xebe,0x56f0},
{0xebf,0xebf,0x5764},
{0xec0,0xec0,0x5d11},
{0xec1,0xec1,0x6606},
{0xec2,0xec2,0x68b1},
{0xec3,0xec3,0x68cd},
{0xec4,0xec4,0x6efe},
{0xec5,0xec5,0x7428},
{0xec6,0xec6,0x889e},
{0xec7,0xec7,0x9be4},
{0xec8,0xec8,0x6c68},
{0xec9,0xec9,0x9aa8},
{0xeca,0xeca,0x4f9b},
{0xecb,0xecb,0x516c},
{0xecc,0xecc,0x5171},
{0xecd,0xecd,0x529f},
{0xece,0xece,0x5b54},
{0xecf,0xecf,0x5de5},
{0xed0,0xed0,0x6050},
{0xed1,0xed1,0x606d},
{0xed2,0xed2,0x62f1},
{0xed3,0xed3,0x63a7},
{0xed4,0xed4,0x653b},
{0xed5,0xed5,0x73d9},
{0xed6,0xed6,0x7a7a},
{0xed7,0xed7,0x86a3},
{0xed8,0xed8,0x8ca2},
{0xed9,0xed9,0x978f},
{0xeda,0xeda,0x4e32},
{0xedb,0xedb,0x5be1},
{0xedc,0xedc,0x6208},
{0xedd,0xedd,0x679c},
{0xede,0xede,0x74dc},
{0xedf,0xedf,0x79d1},
{0xee0,0xee0,0x83d3},
{0xee1,0xee1,0x8a87},
{0xee2,0xee2,0x8ab2},
{0xee3,0xee3,0x8de8},
{0xee4,0xee4,0x904e},
{0xee5,0xee5,0x934b},
{0xee6,0xee6,0x9846},
{0xee7,0xee7,0x5ed3},
{0xee8,0xee8,0x69e8},
{0xee9,0xee9,0x85ff},
{0xeea,0xeea,0x90ed},
{0xeeb,0xeeb,0x51a0},
{0xeec,0xeec,0x5b98},
{0xeed,0xeed,0x5bec},
{0xeee,0xeee,0x6163},
{0xeef,0xeef,0x68fa},
{0xef0,0xef0,0x6b3e},
{0xef1,0xef1,0x704c},
{0xef2,0xef2,0x742f},
{0xef3,0xef3,0x74d8},
{0xef4,0xef4,0x7ba1},
{0xef5,0xef5,0x7f50},
{0xef6,0xef6,0x83c5},
{0xef7,0xef7,0x89c0},
{0xef8,0xef8,0x8cab},
{0xef9,0xef9,0x95dc},
{0xefa,0xefa,0x9928},
{0xefb,0xefb,0x522e},
{0xefc,0xefc,0x605d},
{0xefd,0xefd,0x62ec},
{0xefe,0xefe,0x9002},
{0xeff,0xeff,0x4f8a},
{0xf00,0xf00,0x5149},
{0xf01,0xf01,0x5321},
{0xf02,0xf02,0x58d9},
{0xf03,0xf03,0x5ee3},
{0xf04,0xf04,0x66e0},
{0xf05,0xf05,0x6d38},
{0xf06,0xf06,0x709a},
{0xf07,0xf07,0x72c2},
{0xf08,0xf08,0x73d6},
{0xf09,0xf09,0x7b50},
{0xf0a,0xf0a,0x80f1},
{0xf0b,0xf0b,0x945b},
{0xf0c,0xf0c,0x5366},
{0xf0d,0xf0d,0x639b},
{0xf0e,0xf0e,0x7f6b},
{0xf0f,0xf0f,0x4e56},
{0xf10,0xf10,0x5080},
{0xf11,0xf11,0x584a},
{0xf12,0xf12,0x58de},
{0xf13,0xf13,0x602a},
{0xf14,0xf14,0x6127},
{0xf15,0xf15,0x62d0},
{0xf16,0xf16,0x69d0},
{0xf17,0xf17,0x9b41},
{0xf18,0xf18,0x5b8f},
{0xf19,0xf19,0x7d18},
{0xf1a,0xf1a,0x80b1},
{0xf1b,0xf1b,0x8f5f},
{0xf1c,0xf1c,0x4ea4},
{0xf1d,0xf1d,0x50d1},
{0xf1e,0xf1e,0x54ac},
{0xf1f,0xf1f,0x55ac},
{0xf20,0xf20,0x5b0c},
{0xf21,0xf21,0x5da0},
{0xf22,0xf22,0x5de7},
{0xf23,0xf23,0x652a},
{0xf24,0xf24,0x654e},
{0xf25,0xf25,0x6821},
{0xf26,0xf26,0x6a4b},
{0xf27,0xf27,0x72e1},
{0xf28,0xf28,0x768e},
{0xf29,0xf29,0x77ef},
{0xf2a,0xf2a,0x7d5e},
{0xf2b,0xf2b,0x7ff9},
{0xf2c,0xf2c,0x81a0},
{0xf2d,0xf2d,0x854e},
{0xf2e,0xf2e,0x86df},
{0xf2f,0xf2f,0x8f03},
{0xf30,0xf30,0x8f4e},
{0xf31,0xf31,0x90ca},
{0xf32,0xf32,0x9903},
{0xf33,0xf33,0x9a55},
{0xf34,0xf34,0x9bab},
{0xf35,0xf35,0x4e18},
{0xf36,0xf36,0x4e45},
{0xf37,0xf37,0x4e5d},
{0xf38,0xf38,0x4ec7},
{0xf39,0xf39,0x4ff1},
{0xf3a,0xf3a,0x5177},
{0xf3b,0xf3b,0x52fe},
{0xf3c,0xf3c,0x5340},
{0xf3d,0xf3d,0x53e3},
{0xf3e,0xf3e,0x53e5},
{0xf3f,0xf3f,0x548e},
{0xf40,0xf40,0x5614},
{0xf41,0xf41,0x5775},
{0xf42,0xf42,0x57a2},
{0xf43,0xf43,0x5bc7},
{0xf44,0xf44,0x5d87},
{0xf45,0xf45,0x5ed0},
{0xf46,0xf46,0x61fc},
{0xf47,0xf47,0x62d8},
{0xf48,0xf48,0x6551},
{0xf49,0xf49,0x67b8},
{0xf4a,0xf4a,0x67e9},
{0xf4b,0xf4b,0x69cb},
{0xf4c,0xf4c,0x6b50},
{0xf4d,0xf4d,0x6bc6},
{0xf4e,0xf4e,0x6bec},
{0xf4f,0xf4f,0x6c42},
{0xf50,0xf50,0x6e9d},
{0xf51,0xf51,0x7078},
{0xf52,0xf52,0x72d7},
{0xf53,0xf53,0x7396},
{0xf54,0xf54,0x7403},
{0xf55,0xf55,0x77bf},
{0xf56,0xf56,0x77e9},
{0xf57,0xf57,0x7a76},
{0xf58,0xf58,0x7d7f},
{0xf59,0xf59,0x8009},
{0xf5a,0xf5a,0x81fc},
{0xf5b,0xf5b,0x8205},
{0xf5c,0xf5c,0x820a},
{0xf5d,0xf5d,0x82df},
{0xf5e,0xf5e,0x8862},
{0xf5f,0xf5f,0x8b33},
{0xf60,0xf60,0x8cfc},
{0xf61,0xf61,0x8ec0},
{0xf62,0xf62,0x9011},
{0xf63,0xf63,0x90b1},
{0xf64,0xf64,0x9264},
{0xf65,0xf65,0x92b6},
{0xf66,0xf66,0x99d2},
{0xf67,0xf67,0x9a45},
{0xf68,0xf68,0x9ce9},
{0xf69,0xf69,0x9dd7},
{0xf6a,0xf6a,0x9f9c},
{0xf6b,0xf6b,0x570b},
{0xf6c,0xf6c,0x5c40},
{0xf6d,0xf6d,0x83ca},
{0xf6e,0xf6e,0x97a0},
{0xf6f,0xf6f,0x97ab},
{0xf70,0xf70,0x9eb4},
{0xf71,0xf71,0x541b},
{0xf72,0xf72,0x7a98},
{0xf73,0xf73,0x7fa4},
{0xf74,0xf74,0x88d9},
{0xf75,0xf75,0x8ecd},
{0xf76,0xf76,0x90e1},
{0xf77,0xf77,0x5800},
{0xf78,0xf78,0x5c48},
{0xf79,0xf79,0x6398},
{0xf7a,0xf7a,0x7a9f},
{0xf7b,0xf7b,0x5bae},
{0xf7c,0xf7c,0x5f13},
{0xf7d,0xf7d,0x7a79},
{0xf7e,0xf7e,0x7aae},
{0xf7f,0xf7f,0x828e},
{0xf80,0xf80,0x8eac},
{0xf81,0xf81,0x5026},
{0xf82,0xf82,0x5238},
{0xf83,0xf83,0x52f8},
{0xf84,0xf84,0x5377},
{0xf85,0xf85,0x5708},
{0xf86,0xf86,0x62f3},
{0xf87,0xf87,0x6372},
{0xf88,0xf88,0x6b0a},
{0xf89,0xf89,0x6dc3},
{0xf8a,0xf8a,0x7737},
{0xf8b,0xf8b,0x53a5},
{0xf8c,0xf8c,0x7357},
{0xf8d,0xf8d,0x8568},
{0xf8e,0xf8e,0x8e76},
{0xf8f,0xf8f,0x95d5},
{0xf90,0xf90,0x673a},
{0xf91,0xf91,0x6ac3},
{0xf92,0xf92,0x6f70},
{0xf93,0xf93,0x8a6d},
{0xf94,0xf94,0x8ecc},
{0xf95,0xf95,0x994b},
{0xf96,0xf96,0x6677},
{0xf97,0xf97,0x6b78},
{0xf98,0xf98,0x8cb4},
{0xf99,0xf99,0x9b3c},
{0xf9a,0xf9a,0x53eb},
{0xf9b,0xf9b,0x572d},
{0xf9c,0xf9c,0x594e},
{0xf9d,0xf9d,0x63c6},
{0xf9e,0xf9e,0x69fb},
{0xf9f,0xf9f,0x73ea},
{0xfa0,0xfa0,0x7845},
{0xfa1,0xfa1,0x7aba},
{0xfa2,0xfa2,0x7ac5},
{0xfa3,0xfa3,0x7cfe},
{0xfa4,0xfa4,0x8475},
{0xfa5,0xfa5,0x898f},
{0xfa6,0xfa6,0x8d73},
{0xfa7,0xfa7,0x9035},
{0xfa8,0xfa8,0x95a8},
{0xfa9,0xfa9,0x52fb},
{0xfaa,0xfaa,0x5747},
{0xfab,0xfab,0x7547},
{0xfac,0xfac,0x7b60},
{0xfad,0xfad,0x83cc},
{0xfae,0xfae,0x921e},
{0xfaf,0xfaf,0x6a58},
{0xfb0,0xfb0,0x514b},
{0xfb1,0xfb1,0x524b},
{0xfb2,0xfb2,0x5287},
{0xfb3,0xfb3,0x621f},
{0xfb4,0xfb4,0x68d8},
{0xfb5,0xfb5,0x6975},
{0xfb6,0xfb6,0x9699},
{0xfb7,0xfb7,0x50c5},
{0xfb8,0xfb8,0x52a4},
{0xfb9,0xfb9,0x52e4},
{0xfba,0xfba,0x61c3},
{0xfbb,0xfbb,0x65a4},
{0xfbc,0xfbc,0x6839},
{0xfbd,0xfbd,0x69ff},
{0xfbe,0xfbe,0x747e},
{0xfbf,0xfbf,0x7b4b},
{0xfc0,0xfc0,0x82b9},
{0xfc1,0xfc1,0x83eb},
{0xfc2,0xfc2,0x89b2},
{0xfc3,0xfc3,0x8b39},
{0xfc4,0xfc4,0x8fd1},
{0xfc5,0xfc5,0x9949},
{0xfc6,0xfc6,0x4eca},
{0xfc7,0xfc7,0x5997},
{0xfc8,0xfc8,0x64d2},
{0xfc9,0xfc9,0x6611},
{0xfca,0xfca,0x6a8e},
{0xfcb,0xfcb,0x7434},
{0xfcc,0xfcc,0x7981},
{0xfcd,0xfcd,0x79bd},
{0xfce,0xfce,0x82a9},
{0xfcf,0xfd0,0x887e},
{0xfd1,0xfd1,0x895f},
{0xfd2,0xfd2,0x9326},
{0xfd3,0xfd3,0x4f0b},
{0xfd4,0xfd4,0x53ca},
{0xfd5,0xfd5,0x6025},
{0xfd6,0xfd6,0x6271},
{0xfd7,0xfd7,0x6c72},
{0xfd8,0xfd8,0x7d1a},
{0xfd9,0xfd9,0x7d66},
{0xfda,0xfda,0x4e98},
{0xfdb,0xfdb,0x5162},
{0xfdc,0xfdc,0x77dc},
{0xfdd,0xfdd,0x80af},
{0xfde,0xfde,0x4f01},
{0xfdf,0xfdf,0x4f0e},
{0xfe0,0xfe0,0x5176},
{0xfe1,0xfe1,0x5180},
{0xfe2,0xfe2,0x55dc},
{0xfe3,0xfe3,0x5668},
{0xfe4,0xfe4,0x573b},
{0xfe5,0xfe5,0x57fa},
{0xfe6,0xfe6,0x57fc},
{0xfe7,0xfe7,0x5914},
{0xfe8,0xfe8,0x5947},
{0xfe9,0xfe9,0x5993},
{0xfea,0xfea,0x5bc4},
{0xfeb,0xfeb,0x5c90},
{0xfec,0xfec,0x5d0e},
{0xfed,0xfed,0x5df1},
{0xfee,0xfee,0x5e7e},
{0xfef,0xfef,0x5fcc},
{0xff0,0xff0,0x6280},
{0xff1,0xff1,0x65d7},
{0xff2,0xff2,0x65e3},
{0xff3,0xff4,0x671e},
{0xff5,0xff5,0x675e},
{0xff6,0xff6,0x68cb},
{0xff7,0xff7,0x68c4},
{0xff8,0xff8,0x6a5f},
{0xff9,0xff9,0x6b3a},
{0xffa,0xffa,0x6c23},
{0xffb,0xffb,0x6c7d},
{0xffc,0xffc,0x6c82},
{0xffd,0xffd,0x6dc7},
{0xffe,0xffe,0x7398},
{0xfff,0xfff,0x7426},
{0x1000,0x1000,0x742a},
{0x1001,0x1001,0x7482},
{0x1002,0x1002,0x74a3},
{0x1003,0x1003,0x7578},
{0x1004,0x1004,0x757f},
{0x1005,0x1005,0x7881},
{0x1006,0x1006,0x78ef},
{0x1007,0x1007,0x7941},
{0x1008,0x1009,0x7947},
{0x100a,0x100a,0x797a},
{0x100b,0x100b,0x7b95},
{0x100c,0x100c,0x7d00},
{0x100d,0x100d,0x7dba},
{0x100e,0x100e,0x7f88},
{0x100f,0x100f,0x8006},
{0x1010,0x1010,0x802d},
{0x1011,0x1011,0x808c},
{0x1012,0x1012,0x8a18},
{0x1013,0x1013,0x8b4f},
{0x1014,0x1014,0x8c48},
{0x1015,0x1015,0x8d77},
{0x1016,0x1016,0x9321},
{0x1017,0x1017,0x9324},
{0x1018,0x1018,0x98e2},
{0x1019,0x1019,0x9951},
{0x101a,0x101b,0x9a0e},
{0x101c,0x101c,0x9a65},
{0x101d,0x101d,0x9e92},
{0x101e,0x101e,0x7dca},
{0x101f,0x101f,0x4f76},
{0x1020,0x1020,0x5409},
{0x1021,0x1021,0x62ee},
{0x1022,0x1022,0x6854},
{0x1023,0x1023,0x91d1},
{0x1024,0x1024,0x55ab},
{0x1025,0x1025,0x513a},
{0x1026,0x1026,0x5a1c},
{0x1027,0x1027,0x61e6},
{0x1028,0x1028,0x62cf},
{0x1029,0x1029,0x62ff},
{0x102a,0x102a,0x90a3},
{0x102b,0x102b,0x8afe},
{0x102c,0x102c,0x6696},
{0x102d,0x102d,0x7156},
{0x102e,0x102e,0x96e3},
{0x102f,0x102f,0x634f},
{0x1030,0x1030,0x637a},
{0x1031,0x1031,0x5357},
{0x1032,0x1032,0x678f},
{0x1033,0x1033,0x6960},
{0x1034,0x1034,0x6e73},
{0x1035,0x1035,0x7537},
{0x1036,0x1036,0x7d0d},
{0x1037,0x1037,0x8872},
{0x1038,0x1038,0x56ca},
{0x1039,0x1039,0x5a18},
{0x103a,0x103a,0x4e43},
{0x103b,0x103b,0x5167},
{0x103c,0x103c,0x5948},
{0x103d,0x103d,0x67f0},
{0x103e,0x103e,0x8010},
{0x103f,0x103f,0x5973},
{0x1040,0x1040,0x5e74},
{0x1041,0x1041,0x649a},
{0x1042,0x1042,0x79ca},
{0x1043,0x1043,0x5ff5},
{0x1044,0x1044,0x606c},
{0x1045,0x1045,0x62c8},
{0x1046,0x1046,0x637b},
{0x1047,0x1047,0x5be7},
{0x1048,0x1048,0x5bd7},
{0x1049,0x1049,0x52aa},
{0x104a,0x104a,0x5974},
{0x104b,0x104b,0x5f29},
{0x104c,0x104c,0x6012},
{0x104d,0x104d,0x7459},
{0x104e,0x104e,0x99d1},
{0x104f,0x104f,0x6fc3},
{0x1050,0x1050,0x81bf},
{0x1051,0x1051,0x8fb2},
{0x1052,0x1052,0x60f1},
{0x1053,0x1053,0x8166},
{0x1054,0x1054,0x5c3f},
{0x1055,0x1055,0x5ae9},
{0x1056,0x1056,0x8a25},
{0x1057,0x1057,0x677b},
{0x1058,0x1058,0x7d10},
{0x1059,0x1059,0x80fd},
{0x105a,0x105a,0x5c3c},
{0x105b,0x105b,0x6ce5},
{0x105c,0x105c,0x533f},
{0x105d,0x105d,0x6eba},
{0x105e,0x105e,0x591a},
{0x105f,0x105f,0x8336},
{0x1060,0x1060,0x4e39},
{0x1061,0x1061,0x4eb6},
{0x1062,0x1062,0x4f46},
{0x1063,0x1063,0x55ae},
{0x1064,0x1064,0x5718},
{0x1065,0x1065,0x58c7},
{0x1066,0x1066,0x5f56},
{0x1067,0x1067,0x65b7},
{0x1068,0x1068,0x65e6},
{0x1069,0x1069,0x6a80},
{0x106a,0x106a,0x6bb5},
{0x106b,0x106b,0x6e4d},
{0x106c,0x106c,0x77ed},
{0x106d,0x106d,0x7aef},
{0x106e,0x106e,0x7c1e},
{0x106f,0x106f,0x7dde},
{0x1070,0x1070,0x86cb},
{0x1071,0x1071,0x8892},
{0x1072,0x1072,0x9132},
{0x1073,0x1073,0x935b},
{0x1074,0x1074,0x64bb},
{0x1075,0x1075,0x6fbe},
{0x1076,0x1076,0x737a},
{0x1077,0x1077,0x75b8},
{0x1078,0x1078,0x9054},
{0x1079,0x1079,0x5556},
{0x107a,0x107a,0x574d},
{0x107b,0x107b,0x61ba},
{0x107c,0x107c,0x64d4},
{0x107d,0x107d,0x66c7},
{0x107e,0x107e,0x6de1},
{0x107f,0x107f,0x6e5b},
{0x1080,0x1080,0x6f6d},
{0x1081,0x1081,0x6fb9},
{0x1082,0x1082,0x75f0},
{0x1083,0x1083,0x8043},
{0x1084,0x1084,0x81bd},
{0x1085,0x1085,0x8541},
{0x1086,0x1086,0x8983},
{0x1087,0x1087,0x8ac7},
{0x1088,0x1088,0x8b5a},
{0x1089,0x1089,0x931f},
{0x108a,0x108a,0x6c93},
{0x108b,0x108b,0x7553},
{0x108c,0x108c,0x7b54},
{0x108d,0x108d,0x8e0f},
{0x108e,0x108e,0x905d},
{0x108f,0x108f,0x5510},
{0x1090,0x1090,0x5802},
{0x1091,0x1091,0x5858},
{0x1092,0x1092,0x5e62},
{0x1093,0x1093,0x6207},
{0x1094,0x1094,0x649e},
{0x1095,0x1095,0x68e0},
{0x1096,0x1096,0x7576},
{0x1097,0x1097,0x7cd6},
{0x1098,0x1098,0x87b3},
{0x1099,0x1099,0x9ee8},
{0x109a,0x109a,0x4ee3},
{0x109b,0x109b,0x5788},
{0x109c,0x109c,0x576e},
{0x109d,0x109d,0x5927},
{0x109e,0x109e,0x5c0d},
{0x109f,0x109f,0x5cb1},
{0x10a0,0x10a0,0x5e36},
{0x10a1,0x10a1,0x5f85},
{0x10a2,0x10a2,0x6234},
{0x10a3,0x10a3,0x64e1},
{0x10a4,0x10a4,0x73b3},
{0x10a5,0x10a5,0x81fa},
{0x10a6,0x10a6,0x888b},
{0x10a7,0x10a7,0x8cb8},
{0x10a8,0x10a8,0x968a},
{0x10a9,0x10a9,0x9edb},
{0x10aa,0x10aa,0x5b85},
{0x10ab,0x10ab,0x5fb7},
{0x10ac,0x10ac,0x60b3},
{0x10ad,0x10ad,0x5012},
{0x10ae,0x10ae,0x5200},
{0x10af,0x10af,0x5230},
{0x10b0,0x10b0,0x5716},
{0x10b1,0x10b1,0x5835},
{0x10b2,0x10b2,0x5857},
{0x10b3,0x10b3,0x5c0e},
{0x10b4,0x10b4,0x5c60},
{0x10b5,0x10b5,0x5cf6},
{0x10b6,0x10b6,0x5d8b},
{0x10b7,0x10b7,0x5ea6},
{0x10b8,0x10b8,0x5f92},
{0x10b9,0x10b9,0x60bc},
{0x10ba,0x10ba,0x6311},
{0x10bb,0x10bb,0x6389},
{0x10bc,0x10bc,0x6417},
{0x10bd,0x10bd,0x6843},
{0x10be,0x10be,0x68f9},
{0x10bf,0x10bf,0x6ac2},
{0x10c0,0x10c0,0x6dd8},
{0x10c1,0x10c1,0x6e21},
{0x10c2,0x10c2,0x6ed4},
{0x10c3,0x10c3,0x6fe4},
{0x10c4,0x10c4,0x71fe},
{0x10c5,0x10c5,0x76dc},
{0x10c6,0x10c6,0x7779},
{0x10c7,0x10c7,0x79b1},
{0x10c8,0x10c8,0x7a3b},
{0x10c9,0x10c9,0x8404},
{0x10ca,0x10ca,0x89a9},
{0x10cb,0x10cb,0x8ced},
{0x10cc,0x10cc,0x8df3},
{0x10cd,0x10cd,0x8e48},
{0x10ce,0x10ce,0x9003},
{0x10cf,0x10cf,0x9014},
{0x10d0,0x10d0,0x9053},
{0x10d1,0x10d1,0x90fd},
{0x10d2,0x10d2,0x934d},
{0x10d3,0x10d3,0x9676},
{0x10d4,0x10d4,0x97dc},
{0x10d5,0x10d5,0x6bd2},
{0x10d6,0x10d6,0x7006},
{0x10d7,0x10d7,0x7258},
{0x10d8,0x10d8,0x72a2},
{0x10d9,0x10d9,0x7368},
{0x10da,0x10da,0x7763},
{0x10db,0x10db,0x79bf},
{0x10dc,0x10dc,0x7be4},
{0x10dd,0x10dd,0x7e9b},
{0x10de,0x10de,0x8b80},
{0x10df,0x10df,0x58a9},
{0x10e0,0x10e0,0x60c7},
{0x10e1,0x10e1,0x6566},
{0x10e2,0x10e2,0x65fd},
{0x10e3,0x10e3,0x66be},
{0x10e4,0x10e4,0x6c8c},
{0x10e5,0x10e5,0x711e},
{0x10e6,0x10e6,0x71c9},
{0x10e7,0x10e7,0x8c5a},
{0x10e8,0x10e8,0x9813},
{0x10e9,0x10e9,0x4e6d},
{0x10ea,0x10ea,0x7a81},
{0x10eb,0x10eb,0x4edd},
{0x10ec,0x10ec,0x51ac},
{0x10ed,0x10ed,0x51cd},
{0x10ee,0x10ee,0x52d5},
{0x10ef,0x10ef,0x540c},
{0x10f0,0x10f0,0x61a7},
{0x10f1,0x10f1,0x6771},
{0x10f2,0x10f2,0x6850},
{0x10f3,0x10f3,0x68df},
{0x10f4,0x10f4,0x6d1e},
{0x10f5,0x10f5,0x6f7c},
{0x10f6,0x10f6,0x75bc},
{0x10f7,0x10f7,0x77b3},
{0x10f8,0x10f8,0x7ae5},
{0x10f9,0x10f9,0x80f4},
{0x10fa,0x10fa,0x8463},
{0x10fb,0x10fb,0x9285},
{0x10fc,0x10fc,0x515c},
{0x10fd,0x10fd,0x6597},
{0x10fe,0x10fe,0x675c},
{0x10ff,0x10ff,0x6793},
{0x1100,0x1100,0x75d8},
{0x1101,0x1101,0x7ac7},
{0x1102,0x1102,0x8373},
{0x1103,0x1103,0x8c46},
{0x1104,0x1104,0x9017},
{0x1105,0x1105,0x982d},
{0x1106,0x1106,0x5c6f},
{0x1107,0x1107,0x81c0},
{0x1108,0x1108,0x829a},
{0x1109,0x1109,0x9041},
{0x110a,0x110a,0x906f},
{0x110b,0x110b,0x920d},
{0x110c,0x110c,0x5f97},
{0x110d,0x110d,0x5d9d},
{0x110e,0x110e,0x6a59},
{0x110f,0x110f,0x71c8},
{0x1110,0x1110,0x767b},
{0x1111,0x1111,0x7b49},
{0x1112,0x1112,0x85e4},
{0x1113,0x1113,0x8b04},
{0x1114,0x1114,0x9127},
{0x1115,0x1115,0x9a30},
{0x1116,0x1116,0x5587},
{0x1117,0x1117,0x61f6},
{0x1118,0x1118,0x7669},
{0x1119,0x1119,0x7f85},
{0x111a,0x111a,0x863f},
{0x111b,0x111b,0x87ba},
{0x111c,0x111c,0x88f8},
{0x111d,0x111d,0x908f},
{0x111e,0x111e,0x6d1b},
{0x111f,0x111f,0x70d9},
{0x1120,0x1120,0x73de},
{0x1121,0x1121,0x7d61},
{0x1122,0x1122,0x843d},
{0x1123,0x1123,0x916a},
{0x1124,0x1124,0x99f1},
{0x1125,0x1125,0x4e82},
{0x1126,0x1126,0x5375},
{0x1127,0x1127,0x6b04},
{0x1128,0x1128,0x6b12},
{0x1129,0x1129,0x703e},
{0x112a,0x112a,0x721b},
{0x112b,0x112b,0x862d},
{0x112c,0x112c,0x9e1e},
{0x112d,0x112d,0x524c},
{0x112e,0x112e,0x8fa3},
{0x112f,0x112f,0x5d50},
{0x1130,0x1130,0x64e5},
{0x1131,0x1131,0x652c},
{0x1132,0x1132,0x6b16},
{0x1133,0x1133,0x6feb},
{0x1134,0x1134,0x7c43},
{0x1135,0x1135,0x7e9c},
{0x1136,0x1136,0x85cd},
{0x1137,0x1137,0x8964},
{0x1138,0x1138,0x89bd},
{0x1139,0x1139,0x62c9},
{0x113a,0x113a,0x81d8},
{0x113b,0x113b,0x881f},
{0x113c,0x113c,0x5eca},
{0x113d,0x113d,0x6717},
{0x113e,0x113e,0x6d6a},
{0x113f,0x113f,0x72fc},
{0x1140,0x1140,0x7405},
{0x1141,0x1141,0x746f},
{0x1142,0x1142,0x8782},
{0x1143,0x1143,0x90de},
{0x1144,0x1144,0x4f86},
{0x1145,0x1145,0x5d0d},
{0x1146,0x1146,0x5fa0},
{0x1147,0x1147,0x840a},
{0x1148,0x1148,0x51b7},
{0x1149,0x1149,0x63a0},
{0x114a,0x114a,0x7565},
{0x114b,0x114b,0x4eae},
{0x114c,0x114c,0x5006},
{0x114d,0x114d,0x5169},
{0x114e,0x114e,0x51c9},
{0x114f,0x114f,0x6881},
{0x1150,0x1150,0x6a11},
{0x1151,0x1151,0x7cae},
{0x1152,0x1152,0x7cb1},
{0x1153,0x1153,0x7ce7},
{0x1154,0x1154,0x826f},
{0x1155,0x1155,0x8ad2},
{0x1156,0x1156,0x8f1b},
{0x1157,0x1157,0x91cf},
{0x1158,0x1158,0x4fb6},
{0x1159,0x1159,0x5137},
{0x115a,0x115a,0x52f5},
{0x115b,0x115b,0x5442},
{0x115c,0x115c,0x5eec},
{0x115d,0x115d,0x616e},
{0x115e,0x115e,0x623e},
{0x115f,0x115f,0x65c5},
{0x1160,0x1160,0x6ada},
{0x1161,0x1161,0x6ffe},
{0x1162,0x1162,0x792a},
{0x1163,0x1163,0x85dc},
{0x1164,0x1164,0x8823},
{0x1165,0x1165,0x95ad},
{0x1166,0x1166,0x9a62},
{0x1167,0x1167,0x9a6a},
{0x1168,0x1168,0x9e97},
{0x1169,0x1169,0x9ece},
{0x116a,0x116a,0x529b},
{0x116b,0x116b,0x66c6},
{0x116c,0x116c,0x6b77},
{0x116d,0x116d,0x701d},
{0x116e,0x116e,0x792b},
{0x116f,0x116f,0x8f62},
{0x1170,0x1170,0x9742},
{0x1171,0x1171,0x6190},
{0x1172,0x1172,0x6200},
{0x1173,0x1173,0x6523},
{0x1174,0x1174,0x6f23},
{0x1175,0x1175,0x7149},
{0x1176,0x1176,0x7489},
{0x1177,0x1177,0x7df4},
{0x1178,0x1178,0x806f},
{0x1179,0x1179,0x84ee},
{0x117a,0x117a,0x8f26},
{0x117b,0x117b,0x9023},
{0x117c,0x117c,0x934a},
{0x117d,0x117d,0x51bd},
{0x117e,0x117e,0x5217},
{0x117f,0x117f,0x52a3},
{0x1180,0x1180,0x6d0c},
{0x1181,0x1181,0x70c8},
{0x1182,0x1182,0x88c2},
{0x1183,0x1183,0x5ec9},
{0x1184,0x1184,0x6582},
{0x1185,0x1185,0x6bae},
{0x1186,0x1186,0x6fc2},
{0x1187,0x1187,0x7c3e},
{0x1188,0x1188,0x7375},
{0x1189,0x1189,0x4ee4},
{0x118a,0x118a,0x4f36},
{0x118b,0x118b,0x56f9},
{0x118c,0x118c,0x5cba},
{0x118d,0x118d,0x5dba},
{0x118e,0x118e,0x601c},
{0x118f,0x118f,0x73b2},
{0x1190,0x1190,0x7b2d},
{0x1191,0x1191,0x7f9a},
{0x1192,0x1192,0x7fce},
{0x1193,0x1193,0x8046},
{0x1194,0x1194,0x901e},
{0x1195,0x1195,0x9234},
{0x1196,0x1196,0x96f6},
{0x1197,0x1197,0x9748},
{0x1198,0x1198,0x9818},
{0x1199,0x1199,0x9f61},
{0x119a,0x119a,0x4f8b},
{0x119b,0x119b,0x6fa7},
{0x119c,0x119c,0x79ae},
{0x119d,0x119d,0x91b4},
{0x119e,0x119e,0x96b7},
{0x119f,0x119f,0x52de},
{0x11a0,0x11a0,0x6488},
{0x11a1,0x11a1,0x64c4},
{0x11a2,0x11a2,0x6ad3},
{0x11a3,0x11a3,0x6f5e},
{0x11a4,0x11a4,0x7018},
{0x11a5,0x11a5,0x7210},
{0x11a6,0x11a6,0x76e7},
{0x11a7,0x11a7,0x8001},
{0x11a8,0x11a8,0x8606},
{0x11a9,0x11a9,0x865c},
{0x11aa,0x11aa,0x8def},
{0x11ab,0x11ab,0x8f05},
{0x11ac,0x11ac,0x9732},
{0x11ad,0x11ad,0x9b6f},
{0x11ae,0x11ae,0x9dfa},
{0x11af,0x11af,0x9e75},
{0x11b0,0x11b0,0x788c},
{0x11b1,0x11b1,0x797f},
{0x11b2,0x11b2,0x7da0},
{0x11b3,0x11b3,0x83c9},
{0x11b4,0x11b4,0x9304},
{0x11b5,0x11b5,0x9e7f},
{0x11b6,0x11b6,0x9e93},
{0x11b7,0x11b7,0x8ad6},
{0x11b8,0x11b8,0x58df},
{0x11b9,0x11b9,0x5f04},
{0x11ba,0x11ba,0x6727},
{0x11bb,0x11bb,0x7027},
{0x11bc,0x11bc,0x74cf},
{0x11bd,0x11bd,0x7c60},
{0x11be,0x11be,0x807e},
{0x11bf,0x11bf,0x5121},
{0x11c0,0x11c0,0x7028},
{0x11c1,0x11c1,0x7262},
{0x11c2,0x11c2,0x78ca},
{0x11c3,0x11c3,0x8cc2},
{0x11c4,0x11c4,0x8cda},
{0x11c5,0x11c5,0x8cf4},
{0x11c6,0x11c6,0x96f7},
{0x11c7,0x11c7,0x4e86},
{0x11c8,0x11c8,0x50da},
{0x11c9,0x11c9,0x5bee},
{0x11ca,0x11ca,0x5ed6},
{0x11cb,0x11cb,0x6599},
{0x11cc,0x11cc,0x71ce},
{0x11cd,0x11cd,0x7642},
{0x11ce,0x11ce,0x77ad},
{0x11cf,0x11cf,0x804a},
{0x11d0,0x11d0,0x84fc},
{0x11d1,0x11d1,0x907c},
{0x11d2,0x11d2,0x9b27},
{0x11d3,0x11d3,0x9f8d},
{0x11d4,0x11d4,0x58d8},
{0x11d5,0x11d5,0x5a41},
{0x11d6,0x11d6,0x5c62},
{0x11d7,0x11d7,0x6a13},
{0x11d8,0x11d8,0x6dda},
{0x11d9,0x11d9,0x6f0f},
{0x11da,0x11da,0x763b},
{0x11db,0x11db,0x7d2f},
{0x11dc,0x11dc,0x7e37},
{0x11dd,0x11dd,0x851e},
{0x11de,0x11de,0x8938},
{0x11df,0x11df,0x93e4},
{0x11e0,0x11e0,0x964b},
{0x11e1,0x11e1,0x5289},
{0x11e2,0x11e2,0x65d2},
{0x11e3,0x11e3,0x67f3},
{0x11e4,0x11e4,0x69b4},
{0x11e5,0x11e5,0x6d41},
{0x11e6,0x11e6,0x6e9c},
{0x11e7,0x11e7,0x700f},
{0x11e8,0x11e8,0x7409},
{0x11e9,0x11e9,0x7460},
{0x11ea,0x11ea,0x7559},
{0x11eb,0x11eb,0x7624},
{0x11ec,0x11ec,0x786b},
{0x11ed,0x11ed,0x8b2c},
{0x11ee,0x11ee,0x985e},
{0x11ef,0x11ef,0x516d},
{0x11f0,0x11f0,0x622e},
{0x11f1,0x11f1,0x9678},
{0x11f2,0x11f2,0x4f96},
{0x11f3,0x11f3,0x502b},
{0x11f4,0x11f4,0x5d19},
{0x11f5,0x11f5,0x6dea},
{0x11f6,0x11f6,0x7db8},
{0x11f7,0x11f7,0x8f2a},
{0x11f8,0x11f8,0x5f8b},
{0x11f9,0x11f9,0x6144},
{0x11fa,0x11fa,0x6817},
{0x11fb,0x11fb,0x9686},
{0x11fc,0x11fc,0x52d2},
{0x11fd,0x11fd,0x808b},
{0x11fe,0x11fe,0x51dc},
{0x11ff,0x11ff,0x51cc},
{0x1200,0x1200,0x695e},
{0x1201,0x1201,0x7a1c},
{0x1202,0x1202,0x7dbe},
{0x1203,0x1203,0x83f1},
{0x1204,0x1204,0x9675},
{0x1205,0x1205,0x4fda},
{0x1206,0x1206,0x5229},
{0x1207,0x1207,0x5398},
{0x1208,0x1208,0x540f},
{0x1209,0x1209,0x550e},
{0x120a,0x120a,0x5c65},
{0x120b,0x120b,0x60a7},
{0x120c,0x120c,0x674e},
{0x120d,0x120d,0x68a8},
{0x120e,0x120e,0x6d6c},
{0x120f,0x120f,0x7281},
{0x1210,0x1210,0x72f8},
{0x1211,0x1211,0x7406},
{0x1212,0x1212,0x7483},
{0x1213,0x1213,0x75e2},
{0x1214,0x1214,0x7c6c},
{0x1215,0x1215,0x7f79},
{0x1216,0x1216,0x7fb8},
{0x1217,0x1217,0x8389},
{0x1218,0x1218,0x88cf},
{0x1219,0x1219,0x88e1},
{0x121a,0x121a,0x91cc},
{0x121b,0x121b,0x91d0},
{0x121c,0x121c,0x96e2},
{0x121d,0x121d,0x9bc9},
{0x121e,0x121e,0x541d},
{0x121f,0x121f,0x6f7e},
{0x1220,0x1220,0x71d0},
{0x1221,0x1221,0x7498},
{0x1222,0x1222,0x85fa},
{0x1223,0x1223,0x8eaa},
{0x1224,0x1224,0x96a3},
{0x1225,0x1225,0x9c57},
{0x1226,0x1226,0x9e9f},
{0x1227,0x1227,0x6797},
{0x1228,0x1228,0x6dcb},
{0x1229,0x1229,0x7433},
{0x122a,0x122a,0x81e8},
{0x122b,0x122b,0x9716},
{0x122c,0x122c,0x782c},
{0x122d,0x122d,0x7acb},
{0x122e,0x122e,0x7b20},
{0x122f,0x122f,0x7c92},
{0x1230,0x1230,0x6469},
{0x1231,0x1231,0x746a},
{0x1232,0x1232,0x75f2},
{0x1233,0x1233,0x78bc},
{0x1234,0x1234,0x78e8},
{0x1235,0x1235,0x99ac},
{0x1236,0x1236,0x9b54},
{0x1237,0x1237,0x9ebb},
{0x1238,0x1238,0x5bde},
{0x1239,0x1239,0x5e55},
{0x123a,0x123a,0x6f20},
{0x123b,0x123b,0x819c},
{0x123c,0x123c,0x83ab},
{0x123d,0x123d,0x9088},
{0x123e,0x123e,0x4e07},
{0x123f,0x123f,0x534d},
{0x1240,0x1240,0x5a29},
{0x1241,0x1241,0x5dd2},
{0x1242,0x1242,0x5f4e},
{0x1243,0x1243,0x6162},
{0x1244,0x1244,0x633d},
{0x1245,0x1245,0x6669},
{0x1246,0x1246,0x66fc},
{0x1247,0x1247,0x6eff},
{0x1248,0x1248,0x6f2b},
{0x1249,0x1249,0x7063},
{0x124a,0x124a,0x779e},
{0x124b,0x124b,0x842c},
{0x124c,0x124c,0x8513},
{0x124d,0x124d,0x883b},
{0x124e,0x124e,0x8f13},
{0x124f,0x124f,0x9945},
{0x1250,0x1250,0x9c3b},
{0x1251,0x1251,0x551c},
{0x1252,0x1252,0x62b9},
{0x1253,0x1253,0x672b},
{0x1254,0x1254,0x6cab},
{0x1255,0x1255,0x8309},
{0x1256,0x1256,0x896a},
{0x1257,0x1257,0x977a},
{0x1258,0x1258,0x4ea1},
{0x1259,0x1259,0x5984},
{0x125a,0x125b,0x5fd8},
{0x125c,0x125c,0x671b},
{0x125d,0x125d,0x7db2},
{0x125e,0x125e,0x7f54},
{0x125f,0x125f,0x8292},
{0x1260,0x1260,0x832b},
{0x1261,0x1261,0x83bd},
{0x1262,0x1262,0x8f1e},
{0x1263,0x1263,0x9099},
{0x1264,0x1264,0x57cb},
{0x1265,0x1265,0x59b9},
{0x1266,0x1266,0x5a92},
{0x1267,0x1267,0x5bd0},
{0x1268,0x1268,0x6627},
{0x1269,0x1269,0x679a},
{0x126a,0x126a,0x6885},
{0x126b,0x126b,0x6bcf},
{0x126c,0x126c,0x7164},
{0x126d,0x126d,0x7f75},
{0x126e,0x126e,0x8cb7},
{0x126f,0x126f,0x8ce3},
{0x1270,0x1270,0x9081},
{0x1271,0x1271,0x9b45},
{0x1272,0x1272,0x8108},
{0x1273,0x1273,0x8c8a},
{0x1274,0x1274,0x964c},
{0x1275,0x1275,0x9a40},
{0x1276,0x1276,0x9ea5},
{0x1277,0x1277,0x5b5f},
{0x1278,0x1278,0x6c13},
{0x1279,0x1279,0x731b},
{0x127a,0x127a,0x76f2},
{0x127b,0x127b,0x76df},
{0x127c,0x127c,0x840c},
{0x127d,0x127d,0x51aa},
{0x127e,0x127e,0x8993},
{0x127f,0x127f,0x514d},
{0x1280,0x1280,0x5195},
{0x1281,0x1281,0x52c9},
{0x1282,0x1282,0x68c9},
{0x1283,0x1283,0x6c94},
{0x1284,0x1284,0x7704},
{0x1285,0x1285,0x7720},
{0x1286,0x1286,0x7dbf},
{0x1287,0x1287,0x7dec},
{0x1288,0x1288,0x9762},
{0x1289,0x1289,0x9eb5},
{0x128a,0x128a,0x6ec5},
{0x128b,0x128b,0x8511},
{0x128c,0x128c,0x51a5},
{0x128d,0x128d,0x540d},
{0x128e,0x128e,0x547d},
{0x128f,0x128f,0x660e},
{0x1290,0x1290,0x669d},
{0x1291,0x1291,0x6927},
{0x1292,0x1292,0x6e9f},
{0x1293,0x1293,0x76bf},
{0x1294,0x1294,0x7791},
{0x1295,0x1295,0x8317},
{0x1296,0x1296,0x84c2},
{0x1297,0x1297,0x879f},
{0x1298,0x1298,0x9169},
{0x1299,0x1299,0x9298},
{0x129a,0x129a,0x9cf4},
{0x129b,0x129b,0x8882},
{0x129c,0x129c,0x4fae},
{0x129d,0x129d,0x5192},
{0x129e,0x129e,0x52df},
{0x129f,0x129f,0x59c6},
{0x12a0,0x12a0,0x5e3d},
{0x12a1,0x12a1,0x6155},
{0x12a2,0x12a3,0x6478},
{0x12a4,0x12a4,0x66ae},
{0x12a5,0x12a5,0x67d0},
{0x12a6,0x12a6,0x6a21},
{0x12a7,0x12a7,0x6bcd},
{0x12a8,0x12a8,0x6bdb},
{0x12a9,0x12a9,0x725f},
{0x12aa,0x12aa,0x7261},
{0x12ab,0x12ab,0x7441},
{0x12ac,0x12ac,0x7738},
{0x12ad,0x12ad,0x77db},
{0x12ae,0x12ae,0x8017},
{0x12af,0x12af,0x82bc},
{0x12b0,0x12b0,0x8305},
{0x12b1,0x12b1,0x8b00},
{0x12b2,0x12b2,0x8b28},
{0x12b3,0x12b3,0x8c8c},
{0x12b4,0x12b4,0x6728},
{0x12b5,0x12b5,0x6c90},
{0x12b6,0x12b6,0x7267},
{0x12b7,0x12b7,0x76ee},
{0x12b8,0x12b8,0x7766},
{0x12b9,0x12b9,0x7a46},
{0x12ba,0x12ba,0x9da9},
{0x12bb,0x12bb,0x6b7f},
{0x12bc,0x12bc,0x6c92},
{0x12bd,0x12bd,0x5922},
{0x12be,0x12be,0x6726},
{0x12bf,0x12bf,0x8499},
{0x12c0,0x12c0,0x536f},
{0x12c1,0x12c1,0x5893},
{0x12c2,0x12c2,0x5999},
{0x12c3,0x12c3,0x5edf},
{0x12c4,0x12c4,0x63cf},
{0x12c5,0x12c5,0x6634},
{0x12c6,0x12c6,0x6773},
{0x12c7,0x12c7,0x6e3a},
{0x12c8,0x12c8,0x732b},
{0x12c9,0x12c9,0x7ad7},
{0x12ca,0x12ca,0x82d7},
{0x12cb,0x12cb,0x9328},
{0x12cc,0x12cc,0x52d9},
{0x12cd,0x12cd,0x5deb},
{0x12ce,0x12ce,0x61ae},
{0x12cf,0x12cf,0x61cb},
{0x12d0,0x12d0,0x620a},
{0x12d1,0x12d1,0x62c7},
{0x12d2,0x12d2,0x64ab},
{0x12d3,0x12d3,0x65e0},
{0x12d4,0x12d4,0x6959},
{0x12d5,0x12d5,0x6b66},
{0x12d6,0x12d6,0x6bcb},
{0x12d7,0x12d7,0x7121},
{0x12d8,0x12d8,0x73f7},
{0x12d9,0x12d9,0x755d},
{0x12da,0x12da,0x7e46},
{0x12db,0x12db,0x821e},
{0x12dc,0x12dc,0x8302},
{0x12dd,0x12dd,0x856a},
{0x12de,0x12de,0x8aa3},
{0x12df,0x12df,0x8cbf},
{0x12e0,0x12e0,0x9727},
{0x12e1,0x12e1,0x9d61},
{0x12e2,0x12e2,0x58a8},
{0x12e3,0x12e3,0x9ed8},
{0x12e4,0x12e4,0x5011},
{0x12e5,0x12e5,0x520e},
{0x12e6,0x12e6,0x543b},
{0x12e7,0x12e7,0x554f},
{0x12e8,0x12e8,0x6587},
{0x12e9,0x12e9,0x6c76},
{0x12ea,0x12eb,0x7d0a},
{0x12ec,0x12ec,0x805e},
{0x12ed,0x12ed,0x868a},
{0x12ee,0x12ee,0x9580},
{0x12ef,0x12ef,0x96ef},
{0x12f0,0x12f0,0x52ff},
{0x12f1,0x12f1,0x6c95},
{0x12f2,0x12f2,0x7269},
{0x12f3,0x12f3,0x5473},
{0x12f4,0x12f4,0x5a9a},
{0x12f5,0x12f5,0x5c3e},
{0x12f6,0x12f6,0x5d4b},
{0x12f7,0x12f7,0x5f4c},
{0x12f8,0x12f8,0x5fae},
{0x12f9,0x12f9,0x672a},
{0x12fa,0x12fa,0x68b6},
{0x12fb,0x12fb,0x6963},
{0x12fc,0x12fc,0x6e3c},
{0x12fd,0x12fd,0x6e44},
{0x12fe,0x12fe,0x7709},
{0x12ff,0x12ff,0x7c73},
{0x1300,0x1300,0x7f8e},
{0x1301,0x1301,0x8587},
{0x1302,0x1302,0x8b0e},
{0x1303,0x1303,0x8ff7},
{0x1304,0x1304,0x9761},
{0x1305,0x1305,0x9ef4},
{0x1306,0x1306,0x5cb7},
{0x1307,0x1307,0x60b6},
{0x1308,0x1308,0x610d},
{0x1309,0x1309,0x61ab},
{0x130a,0x130a,0x654f},
{0x130b,0x130c,0x65fb},
{0x130d,0x130d,0x6c11},
{0x130e,0x130e,0x6cef},
{0x130f,0x130f,0x739f},
{0x1310,0x1310,0x73c9},
{0x1311,0x1311,0x7de1},
{0x1312,0x1312,0x9594},
{0x1313,0x1313,0x5bc6},
{0x1314,0x1314,0x871c},
{0x1315,0x1315,0x8b10},
{0x1316,0x1316,0x525d},
{0x1317,0x1317,0x535a},
{0x1318,0x1318,0x62cd},
{0x1319,0x1319,0x640f},
{0x131a,0x131a,0x64b2},
{0x131b,0x131b,0x6734},
{0x131c,0x131c,0x6a38},
{0x131d,0x131d,0x6cca},
{0x131e,0x131e,0x73c0},
{0x131f,0x131f,0x749e},
{0x1320,0x1320,0x7b94},
{0x1321,0x1321,0x7c95},
{0x1322,0x1322,0x7e1b},
{0x1323,0x1323,0x818a},
{0x1324,0x1324,0x8236},
{0x1325,0x1325,0x8584},
{0x1326,0x1326,0x8feb},
{0x1327,0x1327,0x96f9},
{0x1328,0x1328,0x99c1},
{0x1329,0x1329,0x4f34},
{0x132a,0x132a,0x534a},
{0x132b,0x132b,0x53cd},
{0x132c,0x132c,0x53db},
{0x132d,0x132d,0x62cc},
{0x132e,0x132e,0x642c},
{0x132f,0x132f,0x6500},
{0x1330,0x1330,0x6591},
{0x1331,0x1331,0x69c3},
{0x1332,0x1332,0x6cee},
{0x1333,0x1333,0x6f58},
{0x1334,0x1334,0x73ed},
{0x1335,0x1335,0x7554},
{0x1336,0x1336,0x7622},
{0x1337,0x1337,0x76e4},
{0x1338,0x1338,0x76fc},
{0x1339,0x1339,0x78d0},
{0x133a,0x133a,0x78fb},
{0x133b,0x133b,0x792c},
{0x133c,0x133c,0x7d46},
{0x133d,0x133d,0x822c},
{0x133e,0x133e,0x87e0},
{0x133f,0x133f,0x8fd4},
{0x1340,0x1340,0x9812},
{0x1341,0x1341,0x98ef},
{0x1342,0x1342,0x52c3},
{0x1343,0x1343,0x62d4},
{0x1344,0x1344,0x64a5},
{0x1345,0x1345,0x6e24},
{0x1346,0x1346,0x6f51},
{0x1347,0x1347,0x767c},
{0x1348,0x1348,0x8dcb},
{0x1349,0x1349,0x91b1},
{0x134a,0x134a,0x9262},
{0x134b,0x134b,0x9aee},
{0x134c,0x134c,0x9b43},
{0x134d,0x134d,0x5023},
{0x134e,0x134e,0x508d},
{0x134f,0x134f,0x574a},
{0x1350,0x1350,0x59a8},
{0x1351,0x1351,0x5c28},
{0x1352,0x1352,0x5e47},
{0x1353,0x1353,0x5f77},
{0x1354,0x1354,0x623f},
{0x1355,0x1355,0x653e},
{0x1356,0x1356,0x65b9},
{0x1357,0x1357,0x65c1},
{0x1358,0x1358,0x6609},
{0x1359,0x1359,0x678b},
{0x135a,0x135a,0x699c},
{0x135b,0x135b,0x6ec2},
{0x135c,0x135c,0x78c5},
{0x135d,0x135d,0x7d21},
{0x135e,0x135e,0x80aa},
{0x135f,0x135f,0x8180},
{0x1360,0x1360,0x822b},
{0x1361,0x1361,0x82b3},
{0x1362,0x1362,0x84a1},
{0x1363,0x1363,0x868c},
{0x1364,0x1364,0x8a2a},
{0x1365,0x1365,0x8b17},
{0x1366,0x1366,0x90a6},
{0x1367,0x1367,0x9632},
{0x1368,0x1368,0x9f90},
{0x1369,0x1369,0x500d},
{0x136a,0x136a,0x4ff3},
{0x136b,0x136b,0x57f9},
{0x136c,0x136c,0x5f98},
{0x136d,0x136d,0x62dc},
{0x136e,0x136e,0x6392},
{0x136f,0x136f,0x676f},
{0x1370,0x1370,0x6e43},
{0x1371,0x1371,0x7119},
{0x1372,0x1372,0x76c3},
{0x1373,0x1373,0x80cc},
{0x1374,0x1374,0x80da},
{0x1375,0x1376,0x88f4},
{0x1377,0x1377,0x8919},
{0x1378,0x1378,0x8ce0},
{0x1379,0x1379,0x8f29},
{0x137a,0x137a,0x914d},
{0x137b,0x137b,0x966a},
{0x137c,0x137c,0x4f2f},
{0x137d,0x137d,0x4f70},
{0x137e,0x137e,0x5e1b},
{0x137f,0x137f,0x67cf},
{0x1380,0x1380,0x6822},
{0x1381,0x1382,0x767d},
{0x1383,0x1383,0x9b44},
{0x1384,0x1384,0x5e61},
{0x1385,0x1385,0x6a0a},
{0x1386,0x1386,0x7169},
{0x1387,0x1387,0x71d4},
{0x1388,0x1388,0x756a},
{0x1389,0x1389,0x7e41},
{0x138a,0x138a,0x8543},
{0x138b,0x138b,0x85e9},
{0x138c,0x138c,0x98dc},
{0x138d,0x138d,0x4f10},
{0x138e,0x138e,0x7b4f},
{0x138f,0x138f,0x7f70},
{0x1390,0x1390,0x95a5},
{0x1391,0x1391,0x51e1},
{0x1392,0x1392,0x5e06},
{0x1393,0x1393,0x68b5},
{0x1394,0x1394,0x6c3e},
{0x1395,0x1395,0x6c4e},
{0x1396,0x1396,0x6cdb},
{0x1397,0x1397,0x72af},
{0x1398,0x1398,0x7bc4},
{0x1399,0x1399,0x8303},
{0x139a,0x139a,0x6cd5},
{0x139b,0x139b,0x743a},
{0x139c,0x139c,0x50fb},
{0x139d,0x139d,0x5288},
{0x139e,0x139e,0x58c1},
{0x139f,0x139f,0x64d8},
{0x13a0,0x13a0,0x6a97},
{0x13a1,0x13a1,0x74a7},
{0x13a2,0x13a2,0x7656},
{0x13a3,0x13a3,0x78a7},
{0x13a4,0x13a4,0x8617},
{0x13a5,0x13a5,0x95e2},
{0x13a6,0x13a6,0x9739},
{0x13a7,0x13a7,0x535e},
{0x13a8,0x13a8,0x5f01},
{0x13a9,0x13a9,0x8b8a},
{0x13aa,0x13aa,0x8fa8},
{0x13ab,0x13ab,0x8faf},
{0x13ac,0x13ac,0x908a},
{0x13ad,0x13ad,0x5225},
{0x13ae,0x13ae,0x77a5},
{0x13af,0x13af,0x9c49},
{0x13b0,0x13b0,0x9f08},
{0x13b1,0x13b1,0x4e19},
{0x13b2,0x13b2,0x5002},
{0x13b3,0x13b3,0x5175},
{0x13b4,0x13b4,0x5c5b},
{0x13b5,0x13b5,0x5e77},
{0x13b6,0x13b6,0x661e},
{0x13b7,0x13b7,0x663a},
{0x13b8,0x13b8,0x67c4},
{0x13b9,0x13b9,0x68c5},
{0x13ba,0x13ba,0x70b3},
{0x13bb,0x13bb,0x7501},
{0x13bc,0x13bc,0x75c5},
{0x13bd,0x13bd,0x79c9},
{0x13be,0x13be,0x7add},
{0x13bf,0x13bf,0x8f27},
{0x13c0,0x13c0,0x9920},
{0x13c1,0x13c1,0x9a08},
{0x13c2,0x13c2,0x4fdd},
{0x13c3,0x13c3,0x5821},
{0x13c4,0x13c4,0x5831},
{0x13c5,0x13c5,0x5bf6},
{0x13c6,0x13c6,0x666e},
{0x13c7,0x13c7,0x6b65},
{0x13c8,0x13c8,0x6d11},
{0x13c9,0x13c9,0x6e7a},
{0x13ca,0x13ca,0x6f7d},
{0x13cb,0x13cb,0x73e4},
{0x13cc,0x13cc,0x752b},
{0x13cd,0x13cd,0x83e9},
{0x13ce,0x13ce,0x88dc},
{0x13cf,0x13cf,0x8913},
{0x13d0,0x13d0,0x8b5c},
{0x13d1,0x13d1,0x8f14},
{0x13d2,0x13d2,0x4f0f},
{0x13d3,0x13d3,0x50d5},
{0x13d4,0x13d4,0x5310},
{0x13d5,0x13d5,0x535c},
{0x13d6,0x13d6,0x5b93},
{0x13d7,0x13d7,0x5fa9},
{0x13d8,0x13d8,0x670d},
{0x13d9,0x13d9,0x798f},
{0x13da,0x13da,0x8179},
{0x13db,0x13db,0x832f},
{0x13dc,0x13dc,0x8514},
{0x13dd,0x13dd,0x8907},
{0x13de,0x13de,0x8986},
{0x13df,0x13df,0x8f39},
{0x13e0,0x13e0,0x8f3b},
{0x13e1,0x13e1,0x99a5},
{0x13e2,0x13e2,0x9c12},
{0x13e3,0x13e3,0x672c},
{0x13e4,0x13e4,0x4e76},
{0x13e5,0x13e5,0x4ff8},
{0x13e6,0x13e6,0x5949},
{0x13e7,0x13e7,0x5c01},
{0x13e8,0x13e9,0x5cef},
{0x13ea,0x13ea,0x6367},
{0x13eb,0x13eb,0x68d2},
{0x13ec,0x13ec,0x70fd},
{0x13ed,0x13ed,0x71a2},
{0x13ee,0x13ee,0x742b},
{0x13ef,0x13ef,0x7e2b},
{0x13f0,0x13f0,0x84ec},
{0x13f1,0x13f1,0x8702},
{0x13f2,0x13f2,0x9022},
{0x13f3,0x13f3,0x92d2},
{0x13f4,0x13f4,0x9cf3},
{0x13f5,0x13f5,0x4e0d},
{0x13f6,0x13f6,0x4ed8},
{0x13f7,0x13f7,0x4fef},
{0x13f8,0x13f8,0x5085},
{0x13f9,0x13f9,0x5256},
{0x13fa,0x13fa,0x526f},
{0x13fb,0x13fb,0x5426},
{0x13fc,0x13fc,0x5490},
{0x13fd,0x13fd,0x57e0},
{0x13fe,0x13fe,0x592b},
{0x13ff,0x13ff,0x5a66},
{0x1400,0x1400,0x5b5a},
{0x1401,0x1401,0x5b75},
{0x1402,0x1402,0x5bcc},
{0x1403,0x1403,0x5e9c},
{0x1404,0x1404,0x6276},
{0x1405,0x1405,0x6577},
{0x1406,0x1406,0x65a7},
{0x1407,0x1407,0x6d6e},
{0x1408,0x1408,0x6ea5},
{0x1409,0x1409,0x7236},
{0x140a,0x140a,0x7b26},
{0x140b,0x140b,0x7c3f},
{0x140c,0x140c,0x7f36},
{0x140d,0x140e,0x8150},
{0x140f,0x140f,0x819a},
{0x1410,0x1410,0x8240},
{0x1411,0x1411,0x8299},
{0x1412,0x1412,0x83a9},
{0x1413,0x1413,0x8a03},
{0x1414,0x1414,0x8ca0},
{0x1415,0x1415,0x8ce6},
{0x1416,0x1416,0x8cfb},
{0x1417,0x1417,0x8d74},
{0x1418,0x1418,0x8dba},
{0x1419,0x1419,0x90e8},
{0x141a,0x141a,0x91dc},
{0x141b,0x141b,0x961c},
{0x141c,0x141c,0x9644},
{0x141d,0x141d,0x99d9},
{0x141e,0x141e,0x9ce7},
{0x141f,0x141f,0x5317},
{0x1420,0x1420,0x5206},
{0x1421,0x1421,0x5429},
{0x1422,0x1422,0x5674},
{0x1423,0x1423,0x58b3},
{0x1424,0x1424,0x5954},
{0x1425,0x1425,0x596e},
{0x1426,0x1426,0x5fff},
{0x1427,0x1427,0x61a4},
{0x1428,0x1428,0x626e},
{0x1429,0x1429,0x6610},
{0x142a,0x142a,0x6c7e},
{0x142b,0x142b,0x711a},
{0x142c,0x142c,0x76c6},
{0x142d,0x142d,0x7c89},
{0x142e,0x142e,0x7cde},
{0x142f,0x142f,0x7d1b},
{0x1430,0x1430,0x82ac},
{0x1431,0x1431,0x8cc1},
{0x1432,0x1432,0x96f0},
{0x1433,0x1433,0x4f5b},
{0x1434,0x1434,0x5f17},
{0x1435,0x1435,0x5f7f},
{0x1436,0x1436,0x62c2},
{0x1437,0x1437,0x5d29},
{0x1438,0x1438,0x670b},
{0x1439,0x1439,0x68da},
{0x143a,0x143a,0x787c},
{0x143b,0x143b,0x7e43},
{0x143c,0x143c,0x9d6c},
{0x143d,0x143d,0x4e15},
{0x143e,0x143e,0x5099},
{0x143f,0x143f,0x5315},
{0x1440,0x1440,0x532a},
{0x1441,0x1441,0x5351},
{0x1442,0x1442,0x5983},
{0x1443,0x1443,0x5a62},
{0x1444,0x1444,0x5e87},
{0x1445,0x1445,0x60b2},
{0x1446,0x1446,0x618a},
{0x1447,0x1447,0x6249},
{0x1448,0x1448,0x6279},
{0x1449,0x1449,0x6590},
{0x144a,0x144a,0x6787},
{0x144b,0x144b,0x69a7},
{0x144c,0x144c,0x6bd4},
{0x144d,0x144f,0x6bd6},
{0x1450,0x1450,0x6cb8},
{0x1451,0x1451,0x7435},
{0x1452,0x1452,0x75fa},
{0x1453,0x1453,0x7812},
{0x1454,0x1454,0x7891},
{0x1455,0x1455,0x79d5},
{0x1456,0x1456,0x79d8},
{0x1457,0x1457,0x7c83},
{0x1458,0x1458,0x7dcb},
{0x1459,0x1459,0x7fe1},
{0x145a,0x145a,0x80a5},
{0x145b,0x145b,0x813e},
{0x145c,0x145c,0x81c2},
{0x145d,0x145d,0x83f2},
{0x145e,0x145e,0x871a},
{0x145f,0x145f,0x88e8},
{0x1460,0x1460,0x8ab9},
{0x1461,0x1461,0x8b6c},
{0x1462,0x1462,0x8cbb},
{0x1463,0x1463,0x9119},
{0x1464,0x1464,0x975e},
{0x1465,0x1465,0x98db},
{0x1466,0x1466,0x9f3b},
{0x1467,0x1467,0x56ac},
{0x1468,0x1468,0x5b2a},
{0x1469,0x1469,0x5f6c},
{0x146a,0x146a,0x658c},
{0x146b,0x146b,0x6ab3},
{0x146c,0x146c,0x6baf},
{0x146d,0x146d,0x6d5c},
{0x146e,0x146e,0x6ff1},
{0x146f,0x146f,0x7015},
{0x1470,0x1470,0x725d},
{0x1471,0x1471,0x73ad},
{0x1472,0x1472,0x8ca7},
{0x1473,0x1473,0x8cd3},
{0x1474,0x1474,0x983b},
{0x1475,0x1475,0x6191},
{0x1476,0x1476,0x6c37},
{0x1477,0x1477,0x8058},
{0x1478,0x1478,0x9a01},
{0x1479,0x1479,0x4e4d},
{0x147a,0x147a,0x4e8b},
{0x147b,0x147b,0x4e9b},
{0x147c,0x147c,0x4ed5},
{0x147d,0x147d,0x4f3a},
{0x147e,0x147e,0x4f3c},
{0x147f,0x147f,0x4f7f},
{0x1480,0x1480,0x4fdf},
{0x1481,0x1481,0x50ff},
{0x1482,0x1482,0x53f2},
{0x1483,0x1483,0x53f8},
{0x1484,0x1484,0x5506},
{0x1485,0x1485,0x55e3},
{0x1486,0x1486,0x56db},
{0x1487,0x1487,0x58eb},
{0x1488,0x1488,0x5962},
{0x1489,0x1489,0x5a11},
{0x148a,0x148a,0x5beb},
{0x148b,0x148b,0x5bfa},
{0x148c,0x148c,0x5c04},
{0x148d,0x148d,0x5df3},
{0x148e,0x148e,0x5e2b},
{0x148f,0x148f,0x5f99},
{0x1490,0x1490,0x601d},
{0x1491,0x1491,0x6368},
{0x1492,0x1492,0x659c},
{0x1493,0x1493,0x65af},
{0x1494,0x1494,0x67f6},
{0x1495,0x1495,0x67fb},
{0x1496,0x1496,0x68ad},
{0x1497,0x1497,0x6b7b},
{0x1498,0x1498,0x6c99},
{0x1499,0x1499,0x6cd7},
{0x149a,0x149a,0x6e23},
{0x149b,0x149b,0x7009},
{0x149c,0x149c,0x7345},
{0x149d,0x149d,0x7802},
{0x149e,0x149e,0x793e},
{0x149f,0x149f,0x7940},
{0x14a0,0x14a0,0x7960},
{0x14a1,0x14a1,0x79c1},
{0x14a2,0x14a2,0x7be9},
{0x14a3,0x14a3,0x7d17},
{0x14a4,0x14a4,0x7d72},
{0x14a5,0x14a5,0x8086},
{0x14a6,0x14a6,0x820d},
{0x14a7,0x14a7,0x838e},
{0x14a8,0x14a8,0x84d1},
{0x14a9,0x14a9,0x86c7},
{0x14aa,0x14aa,0x88df},
{0x14ab,0x14ab,0x8a50},
{0x14ac,0x14ac,0x8a5e},
{0x14ad,0x14ad,0x8b1d},
{0x14ae,0x14ae,0x8cdc},
{0x14af,0x14af,0x8d66},
{0x14b0,0x14b0,0x8fad},
{0x14b1,0x14b1,0x90aa},
{0x14b2,0x14b2,0x98fc},
{0x14b3,0x14b3,0x99df},
{0x14b4,0x14b4,0x9e9d},
{0x14b5,0x14b5,0x524a},
{0x14b6,0x14b6,0x6714},
{0x14b7,0x14b7,0x5098},
{0x14b8,0x14b8,0x522a},
{0x14b9,0x14b9,0x5c71},
{0x14ba,0x14ba,0x6563},
{0x14bb,0x14bb,0x6c55},
{0x14bc,0x14bc,0x73ca},
{0x14bd,0x14bd,0x7523},
{0x14be,0x14be,0x759d},
{0x14bf,0x14bf,0x7b97},
{0x14c0,0x14c0,0x849c},
{0x14c1,0x14c1,0x9178},
{0x14c2,0x14c2,0x9730},
{0x14c3,0x14c3,0x4e77},
{0x14c4,0x14c4,0x6492},
{0x14c5,0x14c5,0x6bba},
{0x14c6,0x14c6,0x715e},
{0x14c7,0x14c7,0x85a9},
{0x14c8,0x14c8,0x4e09},
{0x14c9,0x14c9,0x6749},
{0x14ca,0x14ca,0x68ee},
{0x14cb,0x14cb,0x6e17},
{0x14cc,0x14cc,0x829f},
{0x14cd,0x14cd,0x8518},
{0x14ce,0x14ce,0x886b},
{0x14cf,0x14cf,0x63f7},
{0x14d0,0x14d0,0x6f81},
{0x14d1,0x14d1,0x9212},
{0x14d2,0x14d2,0x98af},
{0x14d3,0x14d3,0x4e0a},
{0x14d4,0x14d4,0x50b7},
{0x14d5,0x14d5,0x50cf},
{0x14d6,0x14d6,0x511f},
{0x14d7,0x14d7,0x5546},
{0x14d8,0x14d8,0x55aa},
{0x14d9,0x14d9,0x5617},
{0x14da,0x14da,0x5b40},
{0x14db,0x14db,0x5c19},
{0x14dc,0x14dc,0x5ce0},
{0x14dd,0x14dd,0x5e38},
{0x14de,0x14de,0x5e8a},
{0x14df,0x14df,0x5ea0},
{0x14e0,0x14e0,0x5ec2},
{0x14e1,0x14e1,0x60f3},
{0x14e2,0x14e2,0x6851},
{0x14e3,0x14e3,0x6a61},
{0x14e4,0x14e4,0x6e58},
{0x14e5,0x14e5,0x723d},
{0x14e6,0x14e6,0x7240},
{0x14e7,0x14e7,0x72c0},
{0x14e8,0x14e8,0x76f8},
{0x14e9,0x14e9,0x7965},
{0x14ea,0x14ea,0x7bb1},
{0x14eb,0x14eb,0x7fd4},
{0x14ec,0x14ec,0x88f3},
{0x14ed,0x14ed,0x89f4},
{0x14ee,0x14ee,0x8a73},
{0x14ef,0x14ef,0x8c61},
{0x14f0,0x14f0,0x8cde},
{0x14f1,0x14f1,0x971c},
{0x14f2,0x14f2,0x585e},
{0x14f3,0x14f3,0x74bd},
{0x14f4,0x14f4,0x8cfd},
{0x14f5,0x14f5,0x55c7},
{0x14f6,0x14f6,0x7a61},
{0x14f7,0x14f7,0x7d22},
{0x14f8,0x14f8,0x8272},
{0x14f9,0x14f9,0x7272},
{0x14fa,0x14fa,0x751f},
{0x14fb,0x14fb,0x7525},
{0x14fc,0x14fc,0x7b19},
{0x14fd,0x14fd,0x5885},
{0x14fe,0x14fe,0x58fb},
{0x14ff,0x14ff,0x5dbc},
{0x1500,0x1500,0x5e8f},
{0x1501,0x1501,0x5eb6},
{0x1502,0x1502,0x5f90},
{0x1503,0x1503,0x6055},
{0x1504,0x1504,0x6292},
{0x1505,0x1505,0x637f},
{0x1506,0x1506,0x654d},
{0x1507,0x1507,0x6691},
{0x1508,0x1508,0x66d9},
{0x1509,0x1509,0x66f8},
{0x150a,0x150a,0x6816},
{0x150b,0x150b,0x68f2},
{0x150c,0x150c,0x7280},
{0x150d,0x150d,0x745e},
{0x150e,0x150e,0x7b6e},
{0x150f,0x150f,0x7d6e},
{0x1510,0x1510,0x7dd6},
{0x1511,0x1511,0x7f72},
{0x1512,0x1512,0x80e5},
{0x1513,0x1513,0x8212},
{0x1514,0x1514,0x85af},
{0x1515,0x1515,0x897f},
{0x1516,0x1516,0x8a93},
{0x1517,0x1517,0x901d},
{0x1518,0x1518,0x92e4},
{0x1519,0x1519,0x9ecd},
{0x151a,0x151a,0x9f20},
{0x151b,0x151b,0x5915},
{0x151c,0x151c,0x596d},
{0x151d,0x151d,0x5e2d},
{0x151e,0x151e,0x60dc},
{0x151f,0x151f,0x6614},
{0x1520,0x1520,0x6673},
{0x1521,0x1521,0x6790},
{0x1522,0x1522,0x6c50},
{0x1523,0x1523,0x6dc5},
{0x1524,0x1524,0x6f5f},
{0x1525,0x1525,0x77f3},
{0x1526,0x1526,0x78a9},
{0x1527,0x1527,0x84c6},
{0x1528,0x1528,0x91cb},
{0x1529,0x1529,0x932b},
{0x152a,0x152a,0x4ed9},
{0x152b,0x152b,0x50ca},
{0x152c,0x152c,0x5148},
{0x152d,0x152d,0x5584},
{0x152e,0x152e,0x5b0b},
{0x152f,0x152f,0x5ba3},
{0x1530,0x1530,0x6247},
{0x1531,0x1531,0x657e},
{0x1532,0x1532,0x65cb},
{0x1533,0x1533,0x6e32},
{0x1534,0x1534,0x717d},
{0x1535,0x1535,0x7401},
{0x1536,0x1536,0x7444},
{0x1537,0x1537,0x7487},
{0x1538,0x1538,0x74bf},
{0x1539,0x1539,0x766c},
{0x153a,0x153a,0x79aa},
{0x153b,0x153b,0x7dda},
{0x153c,0x153c,0x7e55},
{0x153d,0x153d,0x7fa8},
{0x153e,0x153e,0x817a},
{0x153f,0x153f,0x81b3},
{0x1540,0x1540,0x8239},
{0x1541,0x1541,0x861a},
{0x1542,0x1542,0x87ec},
{0x1543,0x1543,0x8a75},
{0x1544,0x1544,0x8de3},
{0x1545,0x1545,0x9078},
{0x1546,0x1546,0x9291},
{0x1547,0x1547,0x9425},
{0x1548,0x1548,0x994d},
{0x1549,0x1549,0x9bae},
{0x154a,0x154a,0x5368},
{0x154b,0x154b,0x5c51},
{0x154c,0x154c,0x6954},
{0x154d,0x154d,0x6cc4},
{0x154e,0x154e,0x6d29},
{0x154f,0x154f,0x6e2b},
{0x1550,0x1550,0x820c},
{0x1551,0x1551,0x859b},
{0x1552,0x1552,0x893b},
{0x1553,0x1553,0x8a2d},
{0x1554,0x1554,0x8aaa},
{0x1555,0x1555,0x96ea},
{0x1556,0x1556,0x9f67},
{0x1557,0x1557,0x5261},
{0x1558,0x1558,0x66b9},
{0x1559,0x1559,0x6bb2},
{0x155a,0x155a,0x7e96},
{0x155b,0x155b,0x87fe},
{0x155c,0x155c,0x8d0d},
{0x155d,0x155d,0x9583},
{0x155e,0x155e,0x965d},
{0x155f,0x155f,0x651d},
{0x1560,0x1560,0x6d89},
{0x1561,0x1561,0x71ee},
{0x1562,0x1562,0x57ce},
{0x1563,0x1563,0x59d3},
{0x1564,0x1564,0x5bac},
{0x1565,0x1565,0x6027},
{0x1566,0x1566,0x60fa},
{0x1567,0x1567,0x6210},
{0x1568,0x1568,0x661f},
{0x1569,0x1569,0x665f},
{0x156a,0x156a,0x7329},
{0x156b,0x156b,0x73f9},
{0x156c,0x156c,0x76db},
{0x156d,0x156d,0x7701},
{0x156e,0x156e,0x7b6c},
{0x156f,0x156f,0x8056},
{0x1570,0x1570,0x8072},
{0x1571,0x1571,0x8165},
{0x1572,0x1572,0x8aa0},
{0x1573,0x1573,0x9192},
{0x1574,0x1574,0x4e16},
{0x1575,0x1575,0x52e2},
{0x1576,0x1576,0x6b72},
{0x1577,0x1577,0x6d17},
{0x1578,0x1578,0x7a05},
{0x1579,0x1579,0x7b39},
{0x157a,0x157a,0x7d30},
{0x157b,0x157b,0x8cb0},
{0x157c,0x157c,0x53ec},
{0x157d,0x157d,0x562f},
{0x157e,0x157e,0x5851},
{0x157f,0x157f,0x5bb5},
{0x1580,0x1580,0x5c0f},
{0x1581,0x1581,0x5c11},
{0x1582,0x1582,0x5de2},
{0x1583,0x1583,0x6240},
{0x1584,0x1584,0x6383},
{0x1585,0x1585,0x6414},
{0x1586,0x1586,0x662d},
{0x1587,0x1587,0x68b3},
{0x1588,0x1588,0x6cbc},
{0x1589,0x1589,0x6d88},
{0x158a,0x158a,0x6eaf},
{0x158b,0x158b,0x701f},
{0x158c,0x158c,0x70a4},
{0x158d,0x158d,0x71d2},
{0x158e,0x158e,0x7526},
{0x158f,0x158f,0x758f},
{0x1590,0x1590,0x758e},
{0x1591,0x1591,0x7619},
{0x1592,0x1592,0x7b11},
{0x1593,0x1593,0x7be0},
{0x1594,0x1594,0x7c2b},
{0x1595,0x1595,0x7d20},
{0x1596,0x1596,0x7d39},
{0x1597,0x1597,0x852c},
{0x1598,0x1598,0x856d},
{0x1599,0x1599,0x8607},
{0x159a,0x159a,0x8a34},
{0x159b,0x159b,0x900d},
{0x159c,0x159c,0x9061},
{0x159d,0x159d,0x90b5},
{0x159e,0x159e,0x92b7},
{0x159f,0x159f,0x97f6},
{0x15a0,0x15a0,0x9a37},
{0x15a1,0x15a1,0x4fd7},
{0x15a2,0x15a2,0x5c6c},
{0x15a3,0x15a3,0x675f},
{0x15a4,0x15a4,0x6d91},
{0x15a5,0x15a5,0x7c9f},
{0x15a6,0x15a6,0x7e8c},
{0x15a7,0x15a7,0x8b16},
{0x15a8,0x15a8,0x8d16},
{0x15a9,0x15a9,0x901f},
{0x15aa,0x15aa,0x5b6b},
{0x15ab,0x15ab,0x5dfd},
{0x15ac,0x15ac,0x640d},
{0x15ad,0x15ad,0x84c0},
{0x15ae,0x15ae,0x905c},
{0x15af,0x15af,0x98e1},
{0x15b0,0x15b0,0x7387},
{0x15b1,0x15b1,0x5b8b},
{0x15b2,0x15b2,0x609a},
{0x15b3,0x15b3,0x677e},
{0x15b4,0x15b4,0x6dde},
{0x15b5,0x15b5,0x8a1f},
{0x15b6,0x15b6,0x8aa6},
{0x15b7,0x15b7,0x9001},
{0x15b8,0x15b8,0x980c},
{0x15b9,0x15b9,0x5237},
{0x15ba,0x15ba,0x7051},
{0x15bb,0x15bb,0x788e},
{0x15bc,0x15bc,0x9396},
{0x15bd,0x15bd,0x8870},
{0x15be,0x15be,0x91d7},
{0x15bf,0x15bf,0x4fee},
{0x15c0,0x15c0,0x53d7},
{0x15c1,0x15c1,0x55fd},
{0x15c2,0x15c2,0x56da},
{0x15c3,0x15c3,0x5782},
{0x15c4,0x15c4,0x58fd},
{0x15c5,0x15c5,0x5ac2},
{0x15c6,0x15c6,0x5b88},
{0x15c7,0x15c7,0x5cab},
{0x15c8,0x15c8,0x5cc0},
{0x15c9,0x15c9,0x5e25},
{0x15ca,0x15ca,0x6101},
{0x15cb,0x15cb,0x620d},
{0x15cc,0x15cc,0x624b},
{0x15cd,0x15cd,0x6388},
{0x15ce,0x15ce,0x641c},
{0x15cf,0x15cf,0x6536},
{0x15d0,0x15d0,0x6578},
{0x15d1,0x15d1,0x6a39},
{0x15d2,0x15d2,0x6b8a},
{0x15d3,0x15d3,0x6c34},
{0x15d4,0x15d4,0x6d19},
{0x15d5,0x15d5,0x6f31},
{0x15d6,0x15d6,0x71e7},
{0x15d7,0x15d7,0x72e9},
{0x15d8,0x15d8,0x7378},
{0x15d9,0x15d9,0x7407},
{0x15da,0x15da,0x74b2},
{0x15db,0x15db,0x7626},
{0x15dc,0x15dc,0x7761},
{0x15dd,0x15dd,0x79c0},
{0x15de,0x15de,0x7a57},
{0x15df,0x15df,0x7aea},
{0x15e0,0x15e0,0x7cb9},
{0x15e1,0x15e1,0x7d8f},
{0x15e2,0x15e2,0x7dac},
{0x15e3,0x15e3,0x7e61},
{0x15e4,0x15e4,0x7f9e},
{0x15e5,0x15e5,0x8129},
{0x15e6,0x15e6,0x8331},
{0x15e7,0x15e7,0x8490},
{0x15e8,0x15e8,0x84da},
{0x15e9,0x15e9,0x85ea},
{0x15ea,0x15ea,0x8896},
{0x15eb,0x15eb,0x8ab0},
{0x15ec,0x15ec,0x8b90},
{0x15ed,0x15ed,0x8f38},
{0x15ee,0x15ee,0x9042},
{0x15ef,0x15ef,0x9083},
{0x15f0,0x15f0,0x916c},
{0x15f1,0x15f1,0x9296},
{0x15f2,0x15f2,0x92b9},
{0x15f3,0x15f3,0x968b},
{0x15f4,0x15f5,0x96a7},
{0x15f6,0x15f6,0x96d6},
{0x15f7,0x15f7,0x9700},
{0x15f8,0x15f8,0x9808},
{0x15f9,0x15f9,0x9996},
{0x15fa,0x15fa,0x9ad3},
{0x15fb,0x15fb,0x9b1a},
{0x15fc,0x15fc,0x53d4},
{0x15fd,0x15fd,0x587e},
{0x15fe,0x15fe,0x5919},
{0x15ff,0x15ff,0x5b70},
{0x1600,0x1600,0x5bbf},
{0x1601,0x1601,0x6dd1},
{0x1602,0x1602,0x6f5a},
{0x1603,0x1603,0x719f},
{0x1604,0x1604,0x7421},
{0x1605,0x1605,0x74b9},
{0x1606,0x1606,0x8085},
{0x1607,0x1607,0x83fd},
{0x1608,0x1608,0x5de1},
{0x1609,0x1609,0x5f87},
{0x160a,0x160a,0x5faa},
{0x160b,0x160b,0x6042},
{0x160c,0x160c,0x65ec},
{0x160d,0x160d,0x6812},
{0x160e,0x160e,0x696f},
{0x160f,0x160f,0x6a53},
{0x1610,0x1610,0x6b89},
{0x1611,0x1611,0x6d35},
{0x1612,0x1612,0x6df3},
{0x1613,0x1613,0x73e3},
{0x1614,0x1614,0x76fe},
{0x1615,0x1615,0x77ac},
{0x1616,0x1616,0x7b4d},
{0x1617,0x1617,0x7d14},
{0x1618,0x1618,0x8123},
{0x1619,0x1619,0x821c},
{0x161a,0x161a,0x8340},
{0x161b,0x161b,0x84f4},
{0x161c,0x161c,0x8563},
{0x161d,0x161d,0x8a62},
{0x161e,0x161e,0x8ac4},
{0x161f,0x161f,0x9187},
{0x1620,0x1620,0x931e},
{0x1621,0x1621,0x9806},
{0x1622,0x1622,0x99b4},
{0x1623,0x1623,0x620c},
{0x1624,0x1624,0x8853},
{0x1625,0x1625,0x8ff0},
{0x1626,0x1626,0x9265},
{0x1627,0x1627,0x5d07},
{0x1628,0x1628,0x5d27},
{0x1629,0x1629,0x5d69},
{0x162a,0x162a,0x745f},
{0x162b,0x162b,0x819d},
{0x162c,0x162c,0x8768},
{0x162d,0x162d,0x6fd5},
{0x162e,0x162e,0x62fe},
{0x162f,0x162f,0x7fd2},
{0x1630,0x1630,0x8936},
{0x1631,0x1631,0x8972},
{0x1632,0x1632,0x4e1e},
{0x1633,0x1633,0x4e58},
{0x1634,0x1634,0x50e7},
{0x1635,0x1635,0x52dd},
{0x1636,0x1636,0x5347},
{0x1637,0x1637,0x627f},
{0x1638,0x1638,0x6607},
{0x1639,0x1639,0x7e69},
{0x163a,0x163a,0x8805},
{0x163b,0x163b,0x965e},
{0x163c,0x163c,0x4f8d},
{0x163d,0x163d,0x5319},
{0x163e,0x163e,0x5636},
{0x163f,0x163f,0x59cb},
{0x1640,0x1640,0x5aa4},
{0x1641,0x1641,0x5c38},
{0x1642,0x1642,0x5c4e},
{0x1643,0x1643,0x5c4d},
{0x1644,0x1644,0x5e02},
{0x1645,0x1645,0x5f11},
{0x1646,0x1646,0x6043},
{0x1647,0x1647,0x65bd},
{0x1648,0x1648,0x662f},
{0x1649,0x1649,0x6642},
{0x164a,0x164a,0x67be},
{0x164b,0x164b,0x67f4},
{0x164c,0x164c,0x731c},
{0x164d,0x164d,0x77e2},
{0x164e,0x164e,0x793a},
{0x164f,0x164f,0x7fc5},
{0x1650,0x1650,0x8494},
{0x1651,0x1651,0x84cd},
{0x1652,0x1652,0x8996},
{0x1653,0x1653,0x8a66},
{0x1654,0x1654,0x8a69},
{0x1655,0x1655,0x8ae1},
{0x1656,0x1656,0x8c55},
{0x1657,0x1657,0x8c7a},
{0x1658,0x1658,0x57f4},
{0x1659,0x1659,0x5bd4},
{0x165a,0x165a,0x5f0f},
{0x165b,0x165b,0x606f},
{0x165c,0x165c,0x62ed},
{0x165d,0x165d,0x690d},
{0x165e,0x165e,0x6b96},
{0x165f,0x165f,0x6e5c},
{0x1660,0x1660,0x7184},
{0x1661,0x1661,0x7bd2},
{0x1662,0x1662,0x8755},
{0x1663,0x1663,0x8b58},
{0x1664,0x1664,0x8efe},
{0x1665,0x1665,0x98df},
{0x1666,0x1666,0x98fe},
{0x1667,0x1667,0x4f38},
{0x1668,0x1668,0x4f81},
{0x1669,0x1669,0x4fe1},
{0x166a,0x166a,0x547b},
{0x166b,0x166b,0x5a20},
{0x166c,0x166c,0x5bb8},
{0x166d,0x166d,0x613c},
{0x166e,0x166e,0x65b0},
{0x166f,0x166f,0x6668},
{0x1670,0x1670,0x71fc},
{0x1671,0x1671,0x7533},
{0x1672,0x1672,0x795e},
{0x1673,0x1673,0x7d33},
{0x1674,0x1674,0x814e},
{0x1675,0x1675,0x81e3},
{0x1676,0x1676,0x8398},
{0x1677,0x1677,0x85aa},
{0x1678,0x1678,0x85ce},
{0x1679,0x1679,0x8703},
{0x167a,0x167a,0x8a0a},
{0x167b,0x167b,0x8eab},
{0x167c,0x167c,0x8f9b},
{0x167d,0x167d,0x8fc5},
{0x167e,0x167e,0x5931},
{0x167f,0x167f,0x5ba4},
{0x1680,0x1680,0x5be6},
{0x1681,0x1681,0x6089},
{0x1682,0x1682,0x5be9},
{0x1683,0x1683,0x5c0b},
{0x1684,0x1684,0x5fc3},
{0x1685,0x1685,0x6c81},
{0x1686,0x1686,0x6df1},
{0x1687,0x1687,0x700b},
{0x1688,0x1688,0x751a},
{0x1689,0x1689,0x82af},
{0x168a,0x168a,0x8af6},
{0x168b,0x168b,0x4ec0},
{0x168c,0x168c,0x5341},
{0x168d,0x168d,0x96d9},
{0x168e,0x168e,0x6c0f},
{0x168f,0x168f,0x4e9e},
{0x1690,0x1690,0x4fc4},
{0x1691,0x1691,0x5152},
{0x1692,0x1692,0x555e},
{0x1693,0x1693,0x5a25},
{0x1694,0x1694,0x5ce8},
{0x1695,0x1695,0x6211},
{0x1696,0x1696,0x7259},
{0x1697,0x1697,0x82bd},
{0x1698,0x1698,0x83aa},
{0x1699,0x1699,0x86fe},
{0x169a,0x169a,0x8859},
{0x169b,0x169b,0x8a1d},
{0x169c,0x169c,0x963f},
{0x169d,0x169d,0x96c5},
{0x169e,0x169e,0x9913},
{0x169f,0x169f,0x9d09},
{0x16a0,0x16a0,0x9d5d},
{0x16a1,0x16a1,0x580a},
{0x16a2,0x16a2,0x5cb3},
{0x16a3,0x16a3,0x5dbd},
{0x16a4,0x16a4,0x5e44},
{0x16a5,0x16a5,0x60e1},
{0x16a6,0x16a6,0x6115},
{0x16a7,0x16a7,0x63e1},
{0x16a8,0x16a8,0x6a02},
{0x16a9,0x16a9,0x6e25},
{0x16aa,0x16aa,0x9102},
{0x16ab,0x16ab,0x9354},
{0x16ac,0x16ac,0x984e},
{0x16ad,0x16ad,0x9c10},
{0x16ae,0x16ae,0x9f77},
{0x16af,0x16af,0x5b89},
{0x16b0,0x16b0,0x5cb8},
{0x16b1,0x16b1,0x6309},
{0x16b2,0x16b2,0x664f},
{0x16b3,0x16b3,0x6848},
{0x16b4,0x16b4,0x773c},
{0x16b5,0x16b5,0x96c1},
{0x16b6,0x16b6,0x978d},
{0x16b7,0x16b7,0x9854},
{0x16b8,0x16b8,0x9b9f},
{0x16b9,0x16b9,0x65a1},
{0x16ba,0x16ba,0x8b01},
{0x16bb,0x16bb,0x8ecb},
{0x16bc,0x16bc,0x95bc},
{0x16bd,0x16bd,0x5535},
{0x16be,0x16be,0x5ca9},
{0x16bf,0x16bf,0x5dd6},
{0x16c0,0x16c0,0x5eb5},
{0x16c1,0x16c1,0x6697},
{0x16c2,0x16c2,0x764c},
{0x16c3,0x16c3,0x83f4},
{0x16c4,0x16c4,0x95c7},
{0x16c5,0x16c5,0x58d3},
{0x16c6,0x16c6,0x62bc},
{0x16c7,0x16c7,0x72ce},
{0x16c8,0x16c8,0x9d28},
{0x16c9,0x16c9,0x4ef0},
{0x16ca,0x16ca,0x592e},
{0x16cb,0x16cb,0x600f},
{0x16cc,0x16cc,0x663b},
{0x16cd,0x16cd,0x6b83},
{0x16ce,0x16ce,0x79e7},
{0x16cf,0x16cf,0x9d26},
{0x16d0,0x16d0,0x5393},
{0x16d1,0x16d1,0x54c0},
{0x16d2,0x16d2,0x57c3},
{0x16d3,0x16d3,0x5d16},
{0x16d4,0x16d4,0x611b},
{0x16d5,0x16d5,0x66d6},
{0x16d6,0x16d6,0x6daf},
{0x16d7,0x16d7,0x788d},
{0x16d8,0x16d8,0x827e},
{0x16d9,0x16d9,0x9698},
{0x16da,0x16da,0x9744},
{0x16db,0x16db,0x5384},
{0x16dc,0x16dc,0x627c},
{0x16dd,0x16dd,0x6396},
{0x16de,0x16de,0x6db2},
{0x16df,0x16df,0x7e0a},
{0x16e0,0x16e0,0x814b},
{0x16e1,0x16e1,0x984d},
{0x16e2,0x16e2,0x6afb},
{0x16e3,0x16e3,0x7f4c},
{0x16e4,0x16e4,0x9daf},
{0x16e5,0x16e5,0x9e1a},
{0x16e6,0x16e6,0x4e5f},
{0x16e7,0x16e7,0x503b},
{0x16e8,0x16e8,0x51b6},
{0x16e9,0x16e9,0x591c},
{0x16ea,0x16ea,0x60f9},
{0x16eb,0x16eb,0x63f6},
{0x16ec,0x16ec,0x6930},
{0x16ed,0x16ed,0x723a},
{0x16ee,0x16ee,0x8036},
{0x16ef,0x16ef,0x91ce},
{0x16f0,0x16f0,0x5f31},
{0x16f1,0x16f1,0x7d04},
{0x16f2,0x16f2,0x82e5},
{0x16f3,0x16f3,0x846f},
{0x16f4,0x16f4,0x84bb},
{0x16f5,0x16f5,0x85e5},
{0x16f6,0x16f6,0x8e8d},
{0x16f7,0x16f7,0x4f6f},
{0x16f8,0x16f8,0x58e4},
{0x16f9,0x16f9,0x5b43},
{0x16fa,0x16fa,0x6059},
{0x16fb,0x16fb,0x63da},
{0x16fc,0x16fc,0x6518},
{0x16fd,0x16fd,0x656d},
{0x16fe,0x16fe,0x6698},
{0x16ff,0x16ff,0x694a},
{0x1700,0x1700,0x6a23},
{0x1701,0x1701,0x6d0b},
{0x1702,0x1702,0x7001},
{0x1703,0x1703,0x716c},
{0x1704,0x1704,0x75d2},
{0x1705,0x1705,0x760d},
{0x1706,0x1706,0x79b3},
{0x1707,0x1707,0x7a70},
{0x1708,0x1708,0x7f8a},
{0x1709,0x1709,0x8944},
{0x170a,0x170a,0x8b93},
{0x170b,0x170b,0x91c0},
{0x170c,0x170c,0x967d},
{0x170d,0x170d,0x990a},
{0x170e,0x170e,0x5704},
{0x170f,0x170f,0x5fa1},
{0x1710,0x1710,0x65bc},
{0x1711,0x1711,0x6f01},
{0x1712,0x1712,0x7600},
{0x1713,0x1713,0x79a6},
{0x1714,0x1714,0x8a9e},
{0x1715,0x1715,0x99ad},
{0x1716,0x1716,0x9b5a},
{0x1717,0x1717,0x9f6c},
{0x1718,0x1718,0x5104},
{0x1719,0x1719,0x61b6},
{0x171a,0x171a,0x6291},
{0x171b,0x171b,0x6a8d},
{0x171c,0x171c,0x81c6},
{0x171d,0x171d,0x5043},
{0x171e,0x171e,0x5830},
{0x171f,0x171f,0x5f66},
{0x1720,0x1720,0x7109},
{0x1721,0x1721,0x8a00},
{0x1722,0x1722,0x8afa},
{0x1723,0x1723,0x5b7c},
{0x1724,0x1724,0x8616},
{0x1725,0x1725,0x4ffa},
{0x1726,0x1726,0x513c},
{0x1727,0x1727,0x56b4},
{0x1728,0x1728,0x5944},
{0x1729,0x1729,0x63a9},
{0x172a,0x172a,0x6df9},
{0x172b,0x172b,0x5daa},
{0x172c,0x172c,0x696d},
{0x172d,0x172d,0x5186},
{0x172e,0x172e,0x4e88},
{0x172f,0x172f,0x4f59},
{0x1730,0x1730,0x5982},
{0x1731,0x1731,0x6b5f},
{0x1732,0x1732,0x6c5d},
{0x1733,0x1733,0x74b5},
{0x1734,0x1734,0x7916},
{0x1735,0x1735,0x8207},
{0x1736,0x1736,0x8245},
{0x1737,0x1737,0x8339},
{0x1738,0x1738,0x8f3f},
{0x1739,0x1739,0x8f5d},
{0x173a,0x173a,0x9918},
{0x173b,0x173b,0x4ea6},
{0x173c,0x173c,0x57df},
{0x173d,0x173d,0x5f79},
{0x173e,0x173e,0x6613},
{0x173f,0x173f,0x75ab},
{0x1740,0x1740,0x7e79},
{0x1741,0x1741,0x8b6f},
{0x1742,0x1742,0x9006},
{0x1743,0x1743,0x9a5b},
{0x1744,0x1744,0x56a5},
{0x1745,0x1745,0x5827},
{0x1746,0x1746,0x59f8},
{0x1747,0x1747,0x5a1f},
{0x1748,0x1748,0x5bb4},
{0x1749,0x1749,0x5ef6},
{0x174a,0x174a,0x6350},
{0x174b,0x174b,0x633b},
{0x174c,0x174c,0x693d},
{0x174d,0x174d,0x6c87},
{0x174e,0x174e,0x6cbf},
{0x174f,0x174f,0x6d8e},
{0x1750,0x1750,0x6d93},
{0x1751,0x1751,0x6df5},
{0x1752,0x1752,0x6f14},
{0x1753,0x1753,0x70df},
{0x1754,0x1754,0x7136},
{0x1755,0x1755,0x7159},
{0x1756,0x1756,0x71c3},
{0x1757,0x1757,0x71d5},
{0x1758,0x1758,0x784f},
{0x1759,0x1759,0x786f},
{0x175a,0x175a,0x7b75},
{0x175b,0x175b,0x7de3},
{0x175c,0x175c,0x7e2f},
{0x175d,0x175d,0x884d},
{0x175e,0x175e,0x8edf},
{0x175f,0x175f,0x925b},
{0x1760,0x1760,0x9cf6},
{0x1761,0x1761,0x6085},
{0x1762,0x1762,0x6d85},
{0x1763,0x1763,0x71b1},
{0x1764,0x1764,0x95b1},
{0x1765,0x1765,0x53ad},
{0x1766,0x1766,0x67d3},
{0x1767,0x1767,0x708e},
{0x1768,0x1768,0x7130},
{0x1769,0x1769,0x7430},
{0x176a,0x176a,0x8276},
{0x176b,0x176b,0x82d2},
{0x176c,0x176c,0x95bb},
{0x176d,0x176d,0x9ae5},
{0x176e,0x176e,0x9e7d},
{0x176f,0x176f,0x66c4},
{0x1770,0x1770,0x71c1},
{0x1771,0x1771,0x8449},
{0x1772,0x1772,0x584b},
{0x1773,0x1773,0x5db8},
{0x1774,0x1774,0x5f71},
{0x1775,0x1775,0x6620},
{0x1776,0x1776,0x668e},
{0x1777,0x1777,0x6979},
{0x1778,0x1778,0x69ae},
{0x1779,0x1779,0x6c38},
{0x177a,0x177a,0x6cf3},
{0x177b,0x177b,0x6e36},
{0x177c,0x177c,0x6f41},
{0x177d,0x177d,0x6fda},
{0x177e,0x177e,0x701b},
{0x177f,0x177f,0x702f},
{0x1780,0x1780,0x7150},
{0x1781,0x1781,0x71df},
{0x1782,0x1782,0x7370},
{0x1783,0x1783,0x745b},
{0x1784,0x1784,0x74d4},
{0x1785,0x1785,0x76c8},
{0x1786,0x1786,0x7a4e},
{0x1787,0x1787,0x7e93},
{0x1788,0x1788,0x82f1},
{0x1789,0x1789,0x8a60},
{0x178a,0x178a,0x8fce},
{0x178b,0x178b,0x9348},
{0x178c,0x178c,0x9719},
{0x178d,0x178d,0x4e42},
{0x178e,0x178e,0x502a},
{0x178f,0x178f,0x5208},
{0x1790,0x1790,0x53e1},
{0x1791,0x1791,0x66f3},
{0x1792,0x1792,0x6c6d},
{0x1793,0x1793,0x6fca},
{0x1794,0x1794,0x730a},
{0x1795,0x1795,0x777f},
{0x1796,0x1796,0x7a62},
{0x1797,0x1797,0x82ae},
{0x1798,0x1798,0x85dd},
{0x1799,0x1799,0x8602},
{0x179a,0x179a,0x88d4},
{0x179b,0x179b,0x8a63},
{0x179c,0x179c,0x8b7d},
{0x179d,0x179d,0x8c6b},
{0x179e,0x179e,0x92b3},
{0x179f,0x179f,0x9713},
{0x17a0,0x17a0,0x9810},
{0x17a1,0x17a1,0x4e94},
{0x17a2,0x17a2,0x4f0d},
{0x17a3,0x17a3,0x4fc9},
{0x17a4,0x17a4,0x50b2},
{0x17a5,0x17a5,0x5348},
{0x17a6,0x17a6,0x543e},
{0x17a7,0x17a7,0x5433},
{0x17a8,0x17a8,0x55da},
{0x17a9,0x17a9,0x5862},
{0x17aa,0x17aa,0x58ba},
{0x17ab,0x17ab,0x5967},
{0x17ac,0x17ac,0x5a1b},
{0x17ad,0x17ad,0x5be4},
{0x17ae,0x17ae,0x609f},
{0x17af,0x17af,0x61ca},
{0x17b0,0x17b0,0x6556},
{0x17b1,0x17b1,0x65ff},
{0x17b2,0x17b2,0x6664},
{0x17b3,0x17b3,0x68a7},
{0x17b4,0x17b4,0x6c5a},
{0x17b5,0x17b5,0x6fb3},
{0x17b6,0x17b6,0x70cf},
{0x17b7,0x17b7,0x71ac},
{0x17b8,0x17b8,0x7352},
{0x17b9,0x17b9,0x7b7d},
{0x17ba,0x17ba,0x8708},
{0x17bb,0x17bb,0x8aa4},
{0x17bc,0x17bc,0x9c32},
{0x17bd,0x17bd,0x9f07},
{0x17be,0x17be,0x5c4b},
{0x17bf,0x17bf,0x6c83},
{0x17c0,0x17c0,0x7344},
{0x17c1,0x17c1,0x7389},
{0x17c2,0x17c2,0x923a},
{0x17c3,0x17c3,0x6eab},
{0x17c4,0x17c4,0x7465},
{0x17c5,0x17c5,0x761f},
{0x17c6,0x17c6,0x7a69},
{0x17c7,0x17c7,0x7e15},
{0x17c8,0x17c8,0x860a},
{0x17c9,0x17c9,0x5140},
{0x17ca,0x17ca,0x58c5},
{0x17cb,0x17cb,0x64c1},
{0x17cc,0x17cc,0x74ee},
{0x17cd,0x17cd,0x7515},
{0x17ce,0x17ce,0x7670},
{0x17cf,0x17cf,0x7fc1},
{0x17d0,0x17d0,0x9095},
{0x17d1,0x17d1,0x96cd},
{0x17d2,0x17d2,0x9954},
{0x17d3,0x17d3,0x6e26},
{0x17d4,0x17d4,0x74e6},
{0x17d5,0x17d6,0x7aa9},
{0x17d7,0x17d7,0x81e5},
{0x17d8,0x17d8,0x86d9},
{0x17d9,0x17d9,0x8778},
{0x17da,0x17da,0x8a1b},
{0x17db,0x17db,0x5a49},
{0x17dc,0x17dc,0x5b8c},
{0x17dd,0x17dd,0x5b9b},
{0x17de,0x17de,0x68a1},
{0x17df,0x17df,0x6900},
{0x17e0,0x17e0,0x6d63},
{0x17e1,0x17e1,0x73a9},
{0x17e2,0x17e2,0x7413},
{0x17e3,0x17e3,0x742c},
{0x17e4,0x17e4,0x7897},
{0x17e5,0x17e5,0x7de9},
{0x17e6,0x17e6,0x7feb},
{0x17e7,0x17e7,0x8118},
{0x17e8,0x17e8,0x8155},
{0x17e9,0x17e9,0x839e},
{0x17ea,0x17ea,0x8c4c},
{0x17eb,0x17eb,0x962e},
{0x17ec,0x17ec,0x9811},
{0x17ed,0x17ed,0x66f0},
{0x17ee,0x17ee,0x5f80},
{0x17ef,0x17ef,0x65fa},
{0x17f0,0x17f0,0x6789},
{0x17f1,0x17f1,0x6c6a},
{0x17f2,0x17f2,0x738b},
{0x17f3,0x17f3,0x502d},
{0x17f4,0x17f4,0x5a03},
{0x17f5,0x17f5,0x6b6a},
{0x17f6,0x17f6,0x77ee},
{0x17f7,0x17f7,0x5916},
{0x17f8,0x17f8,0x5d6c},
{0x17f9,0x17f9,0x5dcd},
{0x17fa,0x17fa,0x7325},
{0x17fb,0x17fb,0x754f},
{0x17fc,0x17fc,0x50e5},
{0x17fd,0x17fd,0x51f9},
{0x17fe,0x17fe,0x582f},
{0x17ff,0x17ff,0x592d},
{0x1800,0x1800,0x5996},
{0x1801,0x1801,0x59da},
{0x1802,0x1802,0x5be5},
{0x1803,0x1803,0x5da2},
{0x1804,0x1804,0x62d7},
{0x1805,0x1805,0x6416},
{0x1806,0x1806,0x6493},
{0x1807,0x1807,0x64fe},
{0x1808,0x1808,0x66dc},
{0x1809,0x1809,0x6a48},
{0x180a,0x180a,0x71ff},
{0x180b,0x180b,0x7464},
{0x180c,0x180c,0x7a88},
{0x180d,0x180d,0x7aaf},
{0x180e,0x180e,0x7e47},
{0x180f,0x180f,0x7e5e},
{0x1810,0x1810,0x8000},
{0x1811,0x1811,0x8170},
{0x1812,0x1812,0x87ef},
{0x1813,0x1813,0x8981},
{0x1814,0x1814,0x8b20},
{0x1815,0x1815,0x9059},
{0x1816,0x1816,0x9080},
{0x1817,0x1817,0x9952},
{0x1818,0x1818,0x617e},
{0x1819,0x1819,0x6b32},
{0x181a,0x181a,0x6d74},
{0x181b,0x181b,0x7e1f},
{0x181c,0x181c,0x8925},
{0x181d,0x181d,0x8fb1},
{0x181e,0x181e,0x4fd1},
{0x181f,0x181f,0x50ad},
{0x1820,0x1820,0x5197},
{0x1821,0x1821,0x52c7},
{0x1822,0x1822,0x57c7},
{0x1823,0x1823,0x5889},
{0x1824,0x1824,0x5bb9},
{0x1825,0x1825,0x5eb8},
{0x1826,0x1826,0x6142},
{0x1827,0x1827,0x6995},
{0x1828,0x1828,0x6d8c},
{0x1829,0x1829,0x6e67},
{0x182a,0x182a,0x6eb6},
{0x182b,0x182b,0x7194},
{0x182c,0x182c,0x7462},
{0x182d,0x182d,0x7528},
{0x182e,0x182e,0x752c},
{0x182f,0x182f,0x8073},
{0x1830,0x1830,0x8338},
{0x1831,0x1831,0x84c9},
{0x1832,0x1832,0x8e0a},
{0x1833,0x1833,0x9394},
{0x1834,0x1834,0x93de},
{0x1835,0x1835,0x4e8e},
{0x1836,0x1836,0x4f51},
{0x1837,0x1837,0x5076},
{0x1838,0x1838,0x512a},
{0x1839,0x1839,0x53c8},
{0x183a,0x183a,0x53cb},
{0x183b,0x183b,0x53f3},
{0x183c,0x183c,0x5b87},
{0x183d,0x183d,0x5bd3},
{0x183e,0x183e,0x5c24},
{0x183f,0x183f,0x611a},
{0x1840,0x1840,0x6182},
{0x1841,0x1841,0x65f4},
{0x1842,0x1842,0x725b},
{0x1843,0x1843,0x7397},
{0x1844,0x1844,0x7440},
{0x1845,0x1845,0x76c2},
{0x1846,0x1846,0x7950},
{0x1847,0x1847,0x7991},
{0x1848,0x1848,0x79b9},
{0x1849,0x1849,0x7d06},
{0x184a,0x184a,0x7fbd},
{0x184b,0x184b,0x828b},
{0x184c,0x184c,0x85d5},
{0x184d,0x184d,0x865e},
{0x184e,0x184e,0x8fc2},
{0x184f,0x184f,0x9047},
{0x1850,0x1850,0x90f5},
{0x1851,0x1851,0x91ea},
{0x1852,0x1852,0x9685},
{0x1853,0x1854,0x96e8},
{0x1855,0x1855,0x52d6},
{0x1856,0x1856,0x5f67},
{0x1857,0x1857,0x65ed},
{0x1858,0x1858,0x6631},
{0x1859,0x1859,0x682f},
{0x185a,0x185a,0x715c},
{0x185b,0x185b,0x7a36},
{0x185c,0x185c,0x90c1},
{0x185d,0x185d,0x980a},
{0x185e,0x185e,0x4e91},
{0x185f,0x185f,0x6a52},
{0x1860,0x1860,0x6b9e},
{0x1861,0x1861,0x6f90},
{0x1862,0x1862,0x7189},
{0x1863,0x1863,0x8018},
{0x1864,0x1864,0x82b8},
{0x1865,0x1865,0x8553},
{0x1866,0x1866,0x904b},
{0x1867,0x1867,0x9695},
{0x1868,0x1868,0x96f2},
{0x1869,0x1869,0x97fb},
{0x186a,0x186a,0x851a},
{0x186b,0x186b,0x9b31},
{0x186c,0x186c,0x4e90},
{0x186d,0x186d,0x718a},
{0x186e,0x186e,0x96c4},
{0x186f,0x186f,0x5143},
{0x1870,0x1870,0x539f},
{0x1871,0x1871,0x54e1},
{0x1872,0x1872,0x5713},
{0x1873,0x1873,0x5712},
{0x1874,0x1874,0x57a3},
{0x1875,0x1875,0x5a9b},
{0x1876,0x1876,0x5ac4},
{0x1877,0x1877,0x5bc3},
{0x1878,0x1878,0x6028},
{0x1879,0x1879,0x613f},
{0x187a,0x187a,0x63f4},
{0x187b,0x187b,0x6c85},
{0x187c,0x187c,0x6d39},
{0x187d,0x187d,0x6e72},
{0x187e,0x187e,0x6e90},
{0x187f,0x187f,0x7230},
{0x1880,0x1880,0x733f},
{0x1881,0x1881,0x7457},
{0x1882,0x1882,0x82d1},
{0x1883,0x1883,0x8881},
{0x1884,0x1884,0x8f45},
{0x1885,0x1885,0x9060},
{0x1886,0x1886,0x9662},
{0x1887,0x1887,0x9858},
{0x1888,0x1888,0x9d1b},
{0x1889,0x1889,0x6708},
{0x188a,0x188a,0x8d8a},
{0x188b,0x188b,0x925e},
{0x188c,0x188c,0x4f4d},
{0x188d,0x188d,0x5049},
{0x188e,0x188e,0x50de},
{0x188f,0x188f,0x5371},
{0x1890,0x1890,0x570d},
{0x1891,0x1891,0x59d4},
{0x1892,0x1892,0x5a01},
{0x1893,0x1893,0x5c09},
{0x1894,0x1894,0x6170},
{0x1895,0x1895,0x6690},
{0x1896,0x1896,0x6e2d},
{0x1897,0x1897,0x7232},
{0x1898,0x1898,0x744b},
{0x1899,0x1899,0x7def},
{0x189a,0x189a,0x80c3},
{0x189b,0x189b,0x840e},
{0x189c,0x189c,0x8466},
{0x189d,0x189d,0x853f},
{0x189e,0x189e,0x875f},
{0x189f,0x189f,0x885b},
{0x18a0,0x18a0,0x8918},
{0x18a1,0x18a1,0x8b02},
{0x18a2,0x18a2,0x9055},
{0x18a3,0x18a3,0x97cb},
{0x18a4,0x18a4,0x9b4f},
{0x18a5,0x18a5,0x4e73},
{0x18a6,0x18a6,0x4f91},
{0x18a7,0x18a7,0x5112},
{0x18a8,0x18a8,0x516a},
{0x18a9,0x18a9,0x552f},
{0x18aa,0x18aa,0x55a9},
{0x18ab,0x18ab,0x5b7a},
{0x18ac,0x18ac,0x5ba5},
{0x18ad,0x18ae,0x5e7c},
{0x18af,0x18af,0x5ebe},
{0x18b0,0x18b0,0x60a0},
{0x18b1,0x18b1,0x60df},
{0x18b2,0x18b3,0x6108},
{0x18b4,0x18b4,0x63c4},
{0x18b5,0x18b5,0x6538},
{0x18b6,0x18b6,0x6709},
{0x18b7,0x18b7,0x67d4},
{0x18b8,0x18b8,0x67da},
{0x18b9,0x18ba,0x6961},
{0x18bb,0x18bb,0x6cb9},
{0x18bc,0x18bc,0x6d27},
{0x18bd,0x18bd,0x6e38},
{0x18be,0x18be,0x6fe1},
{0x18bf,0x18c0,0x7336},
{0x18c1,0x18c1,0x745c},
{0x18c2,0x18c2,0x7531},
{0x18c3,0x18c3,0x7652},
{0x18c4,0x18c4,0x7dad},
{0x18c5,0x18c5,0x81fe},
{0x18c6,0x18c6,0x8438},
{0x18c7,0x18c7,0x88d5},
{0x18c8,0x18c8,0x8a98},
{0x18c9,0x18c9,0x8adb},
{0x18ca,0x18ca,0x8aed},
{0x18cb,0x18cb,0x8e30},
{0x18cc,0x18cc,0x8e42},
{0x18cd,0x18cd,0x904a},
{0x18ce,0x18ce,0x903e},
{0x18cf,0x18cf,0x907a},
{0x18d0,0x18d0,0x9149},
{0x18d1,0x18d1,0x91c9},
{0x18d2,0x18d2,0x936e},
{0x18d3,0x18d3,0x5809},
{0x18d4,0x18d4,0x6bd3},
{0x18d5,0x18d5,0x8089},
{0x18d6,0x18d6,0x80b2},
{0x18d7,0x18d7,0x5141},
{0x18d8,0x18d8,0x596b},
{0x18d9,0x18d9,0x5c39},
{0x18da,0x18da,0x6f64},
{0x18db,0x18db,0x73a7},
{0x18dc,0x18dc,0x80e4},
{0x18dd,0x18dd,0x8d07},
{0x18de,0x18de,0x9217},
{0x18df,0x18df,0x958f},
{0x18e0,0x18e0,0x807f},
{0x18e1,0x18e1,0x620e},
{0x18e2,0x18e2,0x701c},
{0x18e3,0x18e3,0x7d68},
{0x18e4,0x18e4,0x878d},
{0x18e5,0x18e5,0x57a0},
{0x18e6,0x18e6,0x6069},
{0x18e7,0x18e7,0x6147},
{0x18e8,0x18e8,0x6bb7},
{0x18e9,0x18e9,0x8abe},
{0x18ea,0x18ea,0x9280},
{0x18eb,0x18eb,0x96b1},
{0x18ec,0x18ec,0x4e59},
{0x18ed,0x18ed,0x541f},
{0x18ee,0x18ee,0x6deb},
{0x18ef,0x18ef,0x852d},
{0x18f0,0x18f0,0x9670},
{0x18f1,0x18f1,0x97f3},
{0x18f2,0x18f2,0x98ee},
{0x18f3,0x18f3,0x63d6},
{0x18f4,0x18f4,0x6ce3},
{0x18f5,0x18f5,0x9091},
{0x18f6,0x18f6,0x51dd},
{0x18f7,0x18f7,0x61c9},
{0x18f8,0x18f8,0x81ba},
{0x18f9,0x18f9,0x9df9},
{0x18fa,0x18fa,0x4f9d},
{0x18fb,0x18fb,0x501a},
{0x18fc,0x18fc,0x5100},
{0x18fd,0x18fd,0x5b9c},
{0x18fe,0x18fe,0x610f},
{0x18ff,0x18ff,0x61ff},
{0x1900,0x1900,0x64ec},
{0x1901,0x1901,0x6905},
{0x1902,0x1902,0x6bc5},
{0x1903,0x1903,0x7591},
{0x1904,0x1904,0x77e3},
{0x1905,0x1905,0x7fa9},
{0x1906,0x1906,0x8264},
{0x1907,0x1907,0x858f},
{0x1908,0x1908,0x87fb},
{0x1909,0x1909,0x8863},
{0x190a,0x190a,0x8abc},
{0x190b,0x190b,0x8b70},
{0x190c,0x190c,0x91ab},
{0x190d,0x190d,0x4e8c},
{0x190e,0x190e,0x4ee5},
{0x190f,0x190f,0x4f0a},
{0x1910,0x1910,0x5937},
{0x1911,0x1911,0x59e8},
{0x1912,0x1912,0x5df2},
{0x1913,0x1913,0x5f1b},
{0x1914,0x1914,0x5f5b},
{0x1915,0x1915,0x6021},
{0x1916,0x1916,0x723e},
{0x1917,0x1917,0x73e5},
{0x1918,0x1918,0x7570},
{0x1919,0x1919,0x75cd},
{0x191a,0x191a,0x79fb},
{0x191b,0x191b,0x800c},
{0x191c,0x191c,0x8033},
{0x191d,0x191d,0x8084},
{0x191e,0x191e,0x82e1},
{0x191f,0x191f,0x8351},
{0x1920,0x1920,0x8cbd},
{0x1921,0x1921,0x8cb3},
{0x1922,0x1922,0x9087},
{0x1923,0x1923,0x98f4},
{0x1924,0x1924,0x990c},
{0x1925,0x1925,0x7037},
{0x1926,0x1926,0x76ca},
{0x1927,0x1927,0x7fca},
{0x1928,0x1928,0x7fcc},
{0x1929,0x1929,0x7ffc},
{0x192a,0x192a,0x8b1a},
{0x192b,0x192b,0x4eba},
{0x192c,0x192c,0x4ec1},
{0x192d,0x192d,0x5203},
{0x192e,0x192e,0x5370},
{0x192f,0x192f,0x54bd},
{0x1930,0x1930,0x56e0},
{0x1931,0x1931,0x59fb},
{0x1932,0x1932,0x5bc5},
{0x1933,0x1933,0x5f15},
{0x1934,0x1934,0x5fcd},
{0x1935,0x1935,0x6e6e},
{0x1936,0x1936,0x7d6a},
{0x1937,0x1937,0x8335},
{0x1938,0x1938,0x8693},
{0x1939,0x1939,0x8a8d},
{0x193a,0x193a,0x976d},
{0x193b,0x193b,0x9777},
{0x193c,0x193c,0x4e00},
{0x193d,0x193d,0x4f5a},
{0x193e,0x193e,0x4f7e},
{0x193f,0x193f,0x58f9},
{0x1940,0x1940,0x65e5},
{0x1941,0x1941,0x6ea2},
{0x1942,0x1942,0x9038},
{0x1943,0x1943,0x93b0},
{0x1944,0x1944,0x99b9},
{0x1945,0x1945,0x4efb},
{0x1946,0x1946,0x58ec},
{0x1947,0x1947,0x598a},
{0x1948,0x1948,0x59d9},
{0x1949,0x1949,0x6041},
{0x194a,0x194a,0x7a14},
{0x194b,0x194b,0x834f},
{0x194c,0x194c,0x8cc3},
{0x194d,0x194d,0x5165},
{0x194e,0x194e,0x5344},
{0x194f,0x194f,0x4ecd},
{0x1950,0x1950,0x5269},
{0x1951,0x1951,0x5b55},
{0x1952,0x1952,0x82bf},
{0x1953,0x1953,0x4ed4},
{0x1954,0x1954,0x523a},
{0x1955,0x1955,0x54a8},
{0x1956,0x1956,0x59c9},
{0x1957,0x1957,0x59ff},
{0x1958,0x1958,0x5b50},
{0x1959,0x1959,0x5b57},
{0x195a,0x195a,0x5b5c},
{0x195b,0x195b,0x6063},
{0x195c,0x195c,0x6148},
{0x195d,0x195d,0x6ecb},
{0x195e,0x195e,0x7099},
{0x195f,0x195f,0x716e},
{0x1960,0x1960,0x7386},
{0x1961,0x1961,0x74f7},
{0x1962,0x1962,0x75b5},
{0x1963,0x1963,0x78c1},
{0x1964,0x1964,0x7d2b},
{0x1965,0x1965,0x8005},
{0x1966,0x1966,0x81ea},
{0x1967,0x1967,0x8328},
{0x1968,0x1968,0x8517},
{0x1969,0x1969,0x85c9},
{0x196a,0x196a,0x8aee},
{0x196b,0x196b,0x8cc7},
{0x196c,0x196c,0x96cc},
{0x196d,0x196d,0x4f5c},
{0x196e,0x196e,0x52fa},
{0x196f,0x196f,0x56bc},
{0x1970,0x1970,0x65ab},
{0x1971,0x1971,0x6628},
{0x1972,0x1972,0x707c},
{0x1973,0x1973,0x70b8},
{0x1974,0x1974,0x7235},
{0x1975,0x1975,0x7dbd},
{0x1976,0x1976,0x828d},
{0x1977,0x1977,0x914c},
{0x1978,0x1978,0x96c0},
{0x1979,0x1979,0x9d72},
{0x197a,0x197a,0x5b71},
{0x197b,0x197b,0x68e7},
{0x197c,0x197c,0x6b98},
{0x197d,0x197d,0x6f7a},
{0x197e,0x197e,0x76de},
{0x197f,0x197f,0x5c91},
{0x1980,0x1980,0x66ab},
{0x1981,0x1981,0x6f5b},
{0x1982,0x1982,0x7bb4},
{0x1983,0x1983,0x7c2a},
{0x1984,0x1984,0x8836},
{0x1985,0x1985,0x96dc},
{0x1986,0x1986,0x4e08},
{0x1987,0x1987,0x4ed7},
{0x1988,0x1988,0x5320},
{0x1989,0x1989,0x5834},
{0x198a,0x198a,0x58bb},
{0x198b,0x198b,0x58ef},
{0x198c,0x198c,0x596c},
{0x198d,0x198d,0x5c07},
{0x198e,0x198e,0x5e33},
{0x198f,0x198f,0x5e84},
{0x1990,0x1990,0x5f35},
{0x1991,0x1991,0x638c},
{0x1992,0x1992,0x66b2},
{0x1993,0x1993,0x6756},
{0x1994,0x1994,0x6a1f},
{0x1995,0x1995,0x6aa3},
{0x1996,0x1996,0x6b0c},
{0x1997,0x1997,0x6f3f},
{0x1998,0x1998,0x7246},
{0x1999,0x1999,0x7350},
{0x199a,0x199a,0x748b},
{0x199b,0x199b,0x7ae0},
{0x199c,0x199c,0x7ca7},
{0x199d,0x199d,0x8178},
{0x199e,0x199e,0x81df},
{0x199f,0x199f,0x81e7},
{0x19a0,0x19a0,0x838a},
{0x19a1,0x19a1,0x846c},
{0x19a2,0x19a2,0x8523},
{0x19a3,0x19a3,0x8594},
{0x19a4,0x19a4,0x85cf},
{0x19a5,0x19a5,0x88dd},
{0x19a6,0x19a6,0x8d13},
{0x19a7,0x19a7,0x91ac},
{0x19a8,0x19a8,0x9577},
{0x19a9,0x19a9,0x969c},
{0x19aa,0x19aa,0x518d},
{0x19ab,0x19ab,0x54c9},
{0x19ac,0x19ac,0x5728},
{0x19ad,0x19ad,0x5bb0},
{0x19ae,0x19ae,0x624d},
{0x19af,0x19af,0x6750},
{0x19b0,0x19b0,0x683d},
{0x19b1,0x19b1,0x6893},
{0x19b2,0x19b2,0x6e3d},
{0x19b3,0x19b3,0x6ed3},
{0x19b4,0x19b4,0x707d},
{0x19b5,0x19b5,0x7e21},
{0x19b6,0x19b6,0x88c1},
{0x19b7,0x19b7,0x8ca1},
{0x19b8,0x19b8,0x8f09},
{0x19b9,0x19b9,0x9f4b},
{0x19ba,0x19ba,0x9f4e},
{0x19bb,0x19bb,0x722d},
{0x19bc,0x19bc,0x7b8f},
{0x19bd,0x19bd,0x8acd},
{0x19be,0x19be,0x931a},
{0x19bf,0x19bf,0x4f47},
{0x19c0,0x19c0,0x4f4e},
{0x19c1,0x19c1,0x5132},
{0x19c2,0x19c2,0x5480},
{0x19c3,0x19c3,0x59d0},
{0x19c4,0x19c4,0x5e95},
{0x19c5,0x19c5,0x62b5},
{0x19c6,0x19c6,0x6775},
{0x19c7,0x19c7,0x696e},
{0x19c8,0x19c8,0x6a17},
{0x19c9,0x19c9,0x6cae},
{0x19ca,0x19ca,0x6e1a},
{0x19cb,0x19cb,0x72d9},
{0x19cc,0x19cc,0x732a},
{0x19cd,0x19cd,0x75bd},
{0x19ce,0x19ce,0x7bb8},
{0x19cf,0x19cf,0x7d35},
{0x19d0,0x19d0,0x82e7},
{0x19d1,0x19d1,0x83f9},
{0x19d2,0x19d2,0x8457},
{0x19d3,0x19d3,0x85f7},
{0x19d4,0x19d4,0x8a5b},
{0x19d5,0x19d5,0x8caf},
{0x19d6,0x19d6,0x8e87},
{0x19d7,0x19d7,0x9019},
{0x19d8,0x19d8,0x90b8},
{0x19d9,0x19d9,0x96ce},
{0x19da,0x19da,0x9f5f},
{0x19db,0x19db,0x52e3},
{0x19dc,0x19dc,0x540a},
{0x19dd,0x19dd,0x5ae1},
{0x19de,0x19de,0x5bc2},
{0x19df,0x19df,0x6458},
{0x19e0,0x19e0,0x6575},
{0x19e1,0x19e1,0x6ef4},
{0x19e2,0x19e2,0x72c4},
{0x19e3,0x19e3,0x7684},
{0x19e4,0x19e4,0x7a4d},
{0x19e5,0x19e5,0x7b1b},
{0x19e6,0x19e6,0x7c4d},
{0x19e7,0x19e7,0x7e3e},
{0x19e8,0x19e8,0x7fdf},
{0x19e9,0x19e9,0x837b},
{0x19ea,0x19ea,0x8b2b},
{0x19eb,0x19eb,0x8cca},
{0x19ec,0x19ec,0x8d64},
{0x19ed,0x19ed,0x8de1},
{0x19ee,0x19ee,0x8e5f},
{0x19ef,0x19ef,0x8fea},
{0x19f0,0x19f0,0x8ff9},
{0x19f1,0x19f1,0x9069},
{0x19f2,0x19f2,0x93d1},
{0x19f3,0x19f3,0x4f43},
{0x19f4,0x19f4,0x4f7a},
{0x19f5,0x19f5,0x50b3},
{0x19f6,0x19f6,0x5168},
{0x19f7,0x19f7,0x5178},
{0x19f8,0x19f8,0x524d},
{0x19f9,0x19f9,0x526a},
{0x19fa,0x19fa,0x5861},
{0x19fb,0x19fb,0x587c},
{0x19fc,0x19fc,0x5960},
{0x19fd,0x19fd,0x5c08},
{0x19fe,0x19fe,0x5c55},
{0x19ff,0x19ff,0x5edb},
{0x1a00,0x1a00,0x609b},
{0x1a01,0x1a01,0x6230},
{0x1a02,0x1a02,0x6813},
{0x1a03,0x1a03,0x6bbf},
{0x1a04,0x1a04,0x6c08},
{0x1a05,0x1a05,0x6fb1},
{0x1a06,0x1a06,0x714e},
{0x1a07,0x1a07,0x7420},
{0x1a08,0x1a08,0x7530},
{0x1a09,0x1a09,0x7538},
{0x1a0a,0x1a0a,0x7551},
{0x1a0b,0x1a0b,0x7672},
{0x1a0c,0x1a0c,0x7b4c},
{0x1a0d,0x1a0d,0x7b8b},
{0x1a0e,0x1a0e,0x7bad},
{0x1a0f,0x1a0f,0x7bc6},
{0x1a10,0x1a10,0x7e8f},
{0x1a11,0x1a11,0x8a6e},
{0x1a12,0x1a12,0x8f3e},
{0x1a13,0x1a13,0x8f49},
{0x1a14,0x1a14,0x923f},
{0x1a15,0x1a15,0x9293},
{0x1a16,0x1a16,0x9322},
{0x1a17,0x1a17,0x942b},
{0x1a18,0x1a18,0x96fb},
{0x1a19,0x1a19,0x985a},
{0x1a1a,0x1a1a,0x986b},
{0x1a1b,0x1a1b,0x991e},
{0x1a1c,0x1a1c,0x5207},
{0x1a1d,0x1a1d,0x622a},
{0x1a1e,0x1a1e,0x6298},
{0x1a1f,0x1a1f,0x6d59},
{0x1a20,0x1a20,0x7664},
{0x1a21,0x1a21,0x7aca},
{0x1a22,0x1a22,0x7bc0},
{0x1a23,0x1a23,0x7d76},
{0x1a24,0x1a24,0x5360},
{0x1a25,0x1a25,0x5cbe},
{0x1a26,0x1a26,0x5e97},
{0x1a27,0x1a27,0x6f38},
{0x1a28,0x1a28,0x70b9},
{0x1a29,0x1a29,0x7c98},
{0x1a2a,0x1a2a,0x9711},
{0x1a2b,0x1a2b,0x9b8e},
{0x1a2c,0x1a2c,0x9ede},
{0x1a2d,0x1a2d,0x63a5},
{0x1a2e,0x1a2e,0x647a},
{0x1a2f,0x1a2f,0x8776},
{0x1a30,0x1a30,0x4e01},
{0x1a31,0x1a31,0x4e95},
{0x1a32,0x1a32,0x4ead},
{0x1a33,0x1a33,0x505c},
{0x1a34,0x1a34,0x5075},
{0x1a35,0x1a35,0x5448},
{0x1a36,0x1a36,0x59c3},
{0x1a37,0x1a37,0x5b9a},
{0x1a38,0x1a38,0x5e40},
{0x1a39,0x1a39,0x5ead},
{0x1a3a,0x1a3a,0x5ef7},
{0x1a3b,0x1a3b,0x5f81},
{0x1a3c,0x1a3c,0x60c5},
{0x1a3d,0x1a3d,0x633a},
{0x1a3e,0x1a3e,0x653f},
{0x1a3f,0x1a3f,0x6574},
{0x1a40,0x1a40,0x65cc},
{0x1a41,0x1a41,0x6676},
{0x1a42,0x1a42,0x6678},
{0x1a43,0x1a43,0x67fe},
{0x1a44,0x1a44,0x6968},
{0x1a45,0x1a45,0x6a89},
{0x1a46,0x1a46,0x6b63},
{0x1a47,0x1a47,0x6c40},
{0x1a48,0x1a48,0x6dc0},
{0x1a49,0x1a49,0x6de8},
{0x1a4a,0x1a4a,0x6e1f},
{0x1a4b,0x1a4b,0x6e5e},
{0x1a4c,0x1a4c,0x701e},
{0x1a4d,0x1a4d,0x70a1},
{0x1a4e,0x1a4e,0x738e},
{0x1a4f,0x1a4f,0x73fd},
{0x1a50,0x1a50,0x753a},
{0x1a51,0x1a51,0x775b},
{0x1a52,0x1a52,0x7887},
{0x1a53,0x1a53,0x798e},
{0x1a54,0x1a54,0x7a0b},
{0x1a55,0x1a55,0x7a7d},
{0x1a56,0x1a56,0x7cbe},
{0x1a57,0x1a57,0x7d8e},
{0x1a58,0x1a58,0x8247},
{0x1a59,0x1a59,0x8a02},
{0x1a5a,0x1a5a,0x8aea},
{0x1a5b,0x1a5b,0x8c9e},
{0x1a5c,0x1a5c,0x912d},
{0x1a5d,0x1a5d,0x914a},
{0x1a5e,0x1a5e,0x91d8},
{0x1a5f,0x1a5f,0x9266},
{0x1a60,0x1a60,0x92cc},
{0x1a61,0x1a61,0x9320},
{0x1a62,0x1a62,0x9706},
{0x1a63,0x1a63,0x9756},
{0x1a64,0x1a64,0x975c},
{0x1a65,0x1a65,0x9802},
{0x1a66,0x1a66,0x9f0e},
{0x1a67,0x1a67,0x5236},
{0x1a68,0x1a68,0x5291},
{0x1a69,0x1a69,0x557c},
{0x1a6a,0x1a6a,0x5824},
{0x1a6b,0x1a6b,0x5e1d},
{0x1a6c,0x1a6c,0x5f1f},
{0x1a6d,0x1a6d,0x608c},
{0x1a6e,0x1a6e,0x63d0},
{0x1a6f,0x1a6f,0x68af},
{0x1a70,0x1a70,0x6fdf},
{0x1a71,0x1a71,0x796d},
{0x1a72,0x1a72,0x7b2c},
{0x1a73,0x1a73,0x81cd},
{0x1a74,0x1a74,0x85ba},
{0x1a75,0x1a75,0x88fd},
{0x1a76,0x1a76,0x8af8},
{0x1a77,0x1a77,0x8e44},
{0x1a78,0x1a78,0x918d},
{0x1a79,0x1a79,0x9664},
{0x1a7a,0x1a7a,0x969b},
{0x1a7b,0x1a7b,0x973d},
{0x1a7c,0x1a7c,0x984c},
{0x1a7d,0x1a7d,0x9f4a},
{0x1a7e,0x1a7e,0x4fce},
{0x1a7f,0x1a7f,0x5146},
{0x1a80,0x1a80,0x51cb},
{0x1a81,0x1a81,0x52a9},
{0x1a82,0x1a82,0x5632},
{0x1a83,0x1a83,0x5f14},
{0x1a84,0x1a84,0x5f6b},
{0x1a85,0x1a85,0x63aa},
{0x1a86,0x1a86,0x64cd},
{0x1a87,0x1a87,0x65e9},
{0x1a88,0x1a88,0x6641},
{0x1a89,0x1a89,0x66fa},
{0x1a8a,0x1a8a,0x66f9},
{0x1a8b,0x1a8b,0x671d},
{0x1a8c,0x1a8c,0x689d},
{0x1a8d,0x1a8d,0x68d7},
{0x1a8e,0x1a8e,0x69fd},
{0x1a8f,0x1a8f,0x6f15},
{0x1a90,0x1a90,0x6f6e},
{0x1a91,0x1a91,0x7167},
{0x1a92,0x1a92,0x71e5},
{0x1a93,0x1a93,0x722a},
{0x1a94,0x1a94,0x74aa},
{0x1a95,0x1a95,0x773a},
{0x1a96,0x1a96,0x7956},
{0x1a97,0x1a97,0x795a},
{0x1a98,0x1a98,0x79df},
{0x1a99,0x1a99,0x7a20},
{0x1a9a,0x1a9a,0x7a95},
{0x1a9b,0x1a9b,0x7c97},
{0x1a9c,0x1a9c,0x7cdf},
{0x1a9d,0x1a9d,0x7d44},
{0x1a9e,0x1a9e,0x7e70},
{0x1a9f,0x1a9f,0x8087},
{0x1aa0,0x1aa0,0x85fb},
{0x1aa1,0x1aa1,0x86a4},
{0x1aa2,0x1aa2,0x8a54},
{0x1aa3,0x1aa3,0x8abf},
{0x1aa4,0x1aa4,0x8d99},
{0x1aa5,0x1aa5,0x8e81},
{0x1aa6,0x1aa6,0x9020},
{0x1aa7,0x1aa7,0x906d},
{0x1aa8,0x1aa8,0x91e3},
{0x1aa9,0x1aa9,0x963b},
{0x1aaa,0x1aaa,0x96d5},
{0x1aab,0x1aab,0x9ce5},
{0x1aac,0x1aac,0x65cf},
{0x1aad,0x1aad,0x7c07},
{0x1aae,0x1aae,0x8db3},
{0x1aaf,0x1aaf,0x93c3},
{0x1ab0,0x1ab0,0x5b58},
{0x1ab1,0x1ab1,0x5c0a},
{0x1ab2,0x1ab2,0x5352},
{0x1ab3,0x1ab3,0x62d9},
{0x1ab4,0x1ab4,0x731d},
{0x1ab5,0x1ab5,0x5027},
{0x1ab6,0x1ab6,0x5b97},
{0x1ab7,0x1ab7,0x5f9e},
{0x1ab8,0x1ab8,0x60b0},
{0x1ab9,0x1ab9,0x616b},
{0x1aba,0x1aba,0x68d5},
{0x1abb,0x1abb,0x6dd9},
{0x1abc,0x1abc,0x742e},
{0x1abd,0x1abd,0x7a2e},
{0x1abe,0x1abe,0x7d42},
{0x1abf,0x1abf,0x7d9c},
{0x1ac0,0x1ac0,0x7e31},
{0x1ac1,0x1ac1,0x816b},
{0x1ac2,0x1ac2,0x8e2a},
{0x1ac3,0x1ac3,0x8e35},
{0x1ac4,0x1ac4,0x937e},
{0x1ac5,0x1ac5,0x9418},
{0x1ac6,0x1ac6,0x4f50},
{0x1ac7,0x1ac7,0x5750},
{0x1ac8,0x1ac8,0x5de6},
{0x1ac9,0x1ac9,0x5ea7},
{0x1aca,0x1aca,0x632b},
{0x1acb,0x1acb,0x7f6a},
{0x1acc,0x1acc,0x4e3b},
{0x1acd,0x1acd,0x4f4f},
{0x1ace,0x1ace,0x4f8f},
{0x1acf,0x1acf,0x505a},
{0x1ad0,0x1ad0,0x59dd},
{0x1ad1,0x1ad1,0x80c4},
{0x1ad2,0x1ad2,0x546a},
{0x1ad3,0x1ad3,0x5468},
{0x1ad4,0x1ad4,0x55fe},
{0x1ad5,0x1ad5,0x594f},
{0x1ad6,0x1ad6,0x5b99},
{0x1ad7,0x1ad7,0x5dde},
{0x1ad8,0x1ad8,0x5eda},
{0x1ad9,0x1ad9,0x665d},
{0x1ada,0x1ada,0x6731},
{0x1adb,0x1adb,0x67f1},
{0x1adc,0x1adc,0x682a},
{0x1add,0x1add,0x6ce8},
{0x1ade,0x1ade,0x6d32},
{0x1adf,0x1adf,0x6e4a},
{0x1ae0,0x1ae0,0x6f8d},
{0x1ae1,0x1ae1,0x70b7},
{0x1ae2,0x1ae2,0x73e0},
{0x1ae3,0x1ae3,0x7587},
{0x1ae4,0x1ae4,0x7c4c},
{0x1ae5,0x1ae5,0x7d02},
{0x1ae6,0x1ae6,0x7d2c},
{0x1ae7,0x1ae7,0x7da2},
{0x1ae8,0x1ae8,0x821f},
{0x1ae9,0x1ae9,0x86db},
{0x1aea,0x1aea,0x8a3b},
{0x1aeb,0x1aeb,0x8a85},
{0x1aec,0x1aec,0x8d70},
{0x1aed,0x1aed,0x8e8a},
{0x1aee,0x1aee,0x8f33},
{0x1aef,0x1aef,0x9031},
{0x1af0,0x1af0,0x914e},
{0x1af1,0x1af1,0x9152},
{0x1af2,0x1af2,0x9444},
{0x1af3,0x1af3,0x99d0},
{0x1af4,0x1af4,0x7af9},
{0x1af5,0x1af5,0x7ca5},
{0x1af6,0x1af6,0x4fca},
{0x1af7,0x1af7,0x5101},
{0x1af8,0x1af8,0x51c6},
{0x1af9,0x1af9,0x57c8},
{0x1afa,0x1afa,0x5bef},
{0x1afb,0x1afb,0x5cfb},
{0x1afc,0x1afc,0x6659},
{0x1afd,0x1afd,0x6a3d},
{0x1afe,0x1afe,0x6d5a},
{0x1aff,0x1aff,0x6e96},
{0x1b00,0x1b00,0x6fec},
{0x1b01,0x1b01,0x710c},
{0x1b02,0x1b02,0x756f},
{0x1b03,0x1b03,0x7ae3},
{0x1b04,0x1b04,0x8822},
{0x1b05,0x1b05,0x9021},
{0x1b06,0x1b06,0x9075},
{0x1b07,0x1b07,0x96cb},
{0x1b08,0x1b08,0x99ff},
{0x1b09,0x1b09,0x8301},
{0x1b0a,0x1b0a,0x4e2d},
{0x1b0b,0x1b0b,0x4ef2},
{0x1b0c,0x1b0c,0x8846},
{0x1b0d,0x1b0d,0x91cd},
{0x1b0e,0x1b0e,0x537d},
{0x1b0f,0x1b0f,0x6adb},
{0x1b10,0x1b10,0x696b},
{0x1b11,0x1b11,0x6c41},
{0x1b12,0x1b12,0x847a},
{0x1b13,0x1b13,0x589e},
{0x1b14,0x1b14,0x618e},
{0x1b15,0x1b15,0x66fe},
{0x1b16,0x1b16,0x62ef},
{0x1b17,0x1b17,0x70dd},
{0x1b18,0x1b18,0x7511},
{0x1b19,0x1b19,0x75c7},
{0x1b1a,0x1b1a,0x7e52},
{0x1b1b,0x1b1b,0x84b8},
{0x1b1c,0x1b1c,0x8b49},
{0x1b1d,0x1b1d,0x8d08},
{0x1b1e,0x1b1e,0x4e4b},
{0x1b1f,0x1b1f,0x53ea},
{0x1b20,0x1b20,0x54ab},
{0x1b21,0x1b21,0x5730},
{0x1b22,0x1b22,0x5740},
{0x1b23,0x1b23,0x5fd7},
{0x1b24,0x1b24,0x6301},
{0x1b25,0x1b25,0x6307},
{0x1b26,0x1b26,0x646f},
{0x1b27,0x1b27,0x652f},
{0x1b28,0x1b28,0x65e8},
{0x1b29,0x1b29,0x667a},
{0x1b2a,0x1b2a,0x679d},
{0x1b2b,0x1b2b,0x67b3},
{0x1b2c,0x1b2c,0x6b62},
{0x1b2d,0x1b2d,0x6c60},
{0x1b2e,0x1b2e,0x6c9a},
{0x1b2f,0x1b2f,0x6f2c},
{0x1b30,0x1b30,0x77e5},
{0x1b31,0x1b31,0x7825},
{0x1b32,0x1b32,0x7949},
{0x1b33,0x1b33,0x7957},
{0x1b34,0x1b34,0x7d19},
{0x1b35,0x1b35,0x80a2},
{0x1b36,0x1b36,0x8102},
{0x1b37,0x1b37,0x81f3},
{0x1b38,0x1b38,0x829d},
{0x1b39,0x1b39,0x82b7},
{0x1b3a,0x1b3a,0x8718},
{0x1b3b,0x1b3b,0x8a8c},
{0x1b3c,0x1b3c,0x8d04},
{0x1b3d,0x1b3d,0x8dbe},
{0x1b3e,0x1b3e,0x9072},
{0x1b3f,0x1b3f,0x76f4},
{0x1b40,0x1b40,0x7a19},
{0x1b41,0x1b41,0x7a37},
{0x1b42,0x1b42,0x7e54},
{0x1b43,0x1b43,0x8077},
{0x1b44,0x1b44,0x5507},
{0x1b45,0x1b45,0x55d4},
{0x1b46,0x1b46,0x5875},
{0x1b47,0x1b47,0x632f},
{0x1b48,0x1b48,0x6422},
{0x1b49,0x1b49,0x6649},
{0x1b4a,0x1b4a,0x664b},
{0x1b4b,0x1b4b,0x686d},
{0x1b4c,0x1b4c,0x699b},
{0x1b4d,0x1b4d,0x6b84},
{0x1b4e,0x1b4e,0x6d25},
{0x1b4f,0x1b4f,0x6eb1},
{0x1b50,0x1b50,0x73cd},
{0x1b51,0x1b51,0x7468},
{0x1b52,0x1b52,0x74a1},
{0x1b53,0x1b53,0x755b},
{0x1b54,0x1b54,0x75b9},
{0x1b55,0x1b55,0x76e1},
{0x1b56,0x1b56,0x771e},
{0x1b57,0x1b57,0x778b},
{0x1b58,0x1b58,0x79e6},
{0x1b59,0x1b59,0x7e09},
{0x1b5a,0x1b5a,0x7e1d},
{0x1b5b,0x1b5b,0x81fb},
{0x1b5c,0x1b5c,0x852f},
{0x1b5d,0x1b5d,0x8897},
{0x1b5e,0x1b5e,0x8a3a},
{0x1b5f,0x1b5f,0x8cd1},
{0x1b60,0x1b60,0x8eeb},
{0x1b61,0x1b61,0x8fb0},
{0x1b62,0x1b62,0x9032},
{0x1b63,0x1b63,0x93ad},
{0x1b64,0x1b64,0x9663},
{0x1b65,0x1b65,0x9673},
{0x1b66,0x1b66,0x9707},
{0x1b67,0x1b67,0x4f84},
{0x1b68,0x1b68,0x53f1},
{0x1b69,0x1b69,0x59ea},
{0x1b6a,0x1b6a,0x5ac9},
{0x1b6b,0x1b6b,0x5e19},
{0x1b6c,0x1b6c,0x684e},
{0x1b6d,0x1b6d,0x74c6},
{0x1b6e,0x1b6e,0x75be},
{0x1b6f,0x1b6f,0x79e9},
{0x1b70,0x1b70,0x7a92},
{0x1b71,0x1b71,0x81a3},
{0x1b72,0x1b72,0x86ed},
{0x1b73,0x1b73,0x8cea},
{0x1b74,0x1b74,0x8dcc},
{0x1b75,0x1b75,0x8fed},
{0x1b76,0x1b76,0x659f},
{0x1b77,0x1b77,0x6715},
{0x1b78,0x1b78,0x57f7},
{0x1b79,0x1b79,0x6f57},
{0x1b7a,0x1b7a,0x7ddd},
{0x1b7b,0x1b7b,0x8f2f},
{0x1b7c,0x1b7c,0x93f6},
{0x1b7d,0x1b7d,0x96c6},
{0x1b7e,0x1b7e,0x5fb5},
{0x1b7f,0x1b7f,0x61f2},
{0x1b80,0x1b80,0x6f84},
{0x1b81,0x1b81,0x4e14},
{0x1b82,0x1b82,0x4f98},
{0x1b83,0x1b83,0x501f},
{0x1b84,0x1b84,0x53c9},
{0x1b85,0x1b85,0x55df},
{0x1b86,0x1b86,0x5d6f},
{0x1b87,0x1b87,0x5dee},
{0x1b88,0x1b88,0x6b21},
{0x1b89,0x1b89,0x6b64},
{0x1b8a,0x1b8a,0x78cb},
{0x1b8b,0x1b8b,0x7b9a},
{0x1b8c,0x1b8c,0x8e49},
{0x1b8d,0x1b8d,0x8eca},
{0x1b8e,0x1b8e,0x906e},
{0x1b8f,0x1b8f,0x6349},
{0x1b90,0x1b90,0x643e},
{0x1b91,0x1b91,0x7740},
{0x1b92,0x1b92,0x7a84},
{0x1b93,0x1b93,0x932f},
{0x1b94,0x1b94,0x947f},
{0x1b95,0x1b95,0x9f6a},
{0x1b96,0x1b96,0x64b0},
{0x1b97,0x1b97,0x6faf},
{0x1b98,0x1b98,0x71e6},
{0x1b99,0x1b99,0x74a8},
{0x1b9a,0x1b9a,0x74da},
{0x1b9b,0x1b9b,0x7ac4},
{0x1b9c,0x1b9c,0x7c12},
{0x1b9d,0x1b9d,0x7e82},
{0x1b9e,0x1b9e,0x7cb2},
{0x1b9f,0x1b9f,0x7e98},
{0x1ba0,0x1ba0,0x8b9a},
{0x1ba1,0x1ba1,0x8d0a},
{0x1ba2,0x1ba2,0x947d},
{0x1ba3,0x1ba3,0x9910},
{0x1ba4,0x1ba4,0x994c},
{0x1ba5,0x1ba5,0x5239},
{0x1ba6,0x1ba6,0x5bdf},
{0x1ba7,0x1ba7,0x64e6},
{0x1ba8,0x1ba8,0x672d},
{0x1ba9,0x1ba9,0x7d2e},
{0x1baa,0x1baa,0x50ed},
{0x1bab,0x1bab,0x53c3},
{0x1bac,0x1bac,0x5879},
{0x1bad,0x1bae,0x6158},
{0x1baf,0x1baf,0x61fa},
{0x1bb0,0x1bb0,0x65ac},
{0x1bb1,0x1bb1,0x7ad9},
{0x1bb2,0x1bb2,0x8b92},
{0x1bb3,0x1bb3,0x8b96},
{0x1bb4,0x1bb4,0x5009},
{0x1bb5,0x1bb5,0x5021},
{0x1bb6,0x1bb6,0x5275},
{0x1bb7,0x1bb7,0x5531},
{0x1bb8,0x1bb8,0x5a3c},
{0x1bb9,0x1bb9,0x5ee0},
{0x1bba,0x1bba,0x5f70},
{0x1bbb,0x1bbb,0x6134},
{0x1bbc,0x1bbc,0x655e},
{0x1bbd,0x1bbd,0x660c},
{0x1bbe,0x1bbe,0x6636},
{0x1bbf,0x1bbf,0x66a2},
{0x1bc0,0x1bc0,0x69cd},
{0x1bc1,0x1bc1,0x6ec4},
{0x1bc2,0x1bc2,0x6f32},
{0x1bc3,0x1bc3,0x7316},
{0x1bc4,0x1bc4,0x7621},
{0x1bc5,0x1bc5,0x7a93},
{0x1bc6,0x1bc6,0x8139},
{0x1bc7,0x1bc7,0x8259},
{0x1bc8,0x1bc8,0x83d6},
{0x1bc9,0x1bc9,0x84bc},
{0x1bca,0x1bca,0x50b5},
{0x1bcb,0x1bcb,0x57f0},
{0x1bcc,0x1bcc,0x5bc0},
{0x1bcd,0x1bcd,0x5be8},
{0x1bce,0x1bce,0x5f69},
{0x1bcf,0x1bcf,0x63a1},
{0x1bd0,0x1bd0,0x7826},
{0x1bd1,0x1bd1,0x7db5},
{0x1bd2,0x1bd2,0x83dc},
{0x1bd3,0x1bd3,0x8521},
{0x1bd4,0x1bd4,0x91c7},
{0x1bd5,0x1bd5,0x91f5},
{0x1bd6,0x1bd6,0x518a},
{0x1bd7,0x1bd7,0x67f5},
{0x1bd8,0x1bd8,0x7b56},
{0x1bd9,0x1bd9,0x8cac},
{0x1bda,0x1bda,0x51c4},
{0x1bdb,0x1bdb,0x59bb},
{0x1bdc,0x1bdc,0x60bd},
{0x1bdd,0x1bdd,0x8655},
{0x1bde,0x1bde,0x501c},
{0x1bdf,0x1bdf,0x5254},
{0x1be0,0x1be0,0x5c3a},
{0x1be1,0x1be1,0x617d},
{0x1be2,0x1be2,0x621a},
{0x1be3,0x1be3,0x62d3},
{0x1be4,0x1be4,0x64f2},
{0x1be5,0x1be5,0x65a5},
{0x1be6,0x1be6,0x6ecc},
{0x1be7,0x1be7,0x7620},
{0x1be8,0x1be8,0x810a},
{0x1be9,0x1be9,0x8e60},
{0x1bea,0x1bea,0x965f},
{0x1beb,0x1beb,0x96bb},
{0x1bec,0x1bec,0x4edf},
{0x1bed,0x1bed,0x5343},
{0x1bee,0x1bee,0x5598},
{0x1bef,0x1bef,0x5929},
{0x1bf0,0x1bf0,0x5ddd},
{0x1bf1,0x1bf1,0x64c5},
{0x1bf2,0x1bf2,0x6cc9},
{0x1bf3,0x1bf3,0x6dfa},
{0x1bf4,0x1bf4,0x7394},
{0x1bf5,0x1bf5,0x7a7f},
{0x1bf6,0x1bf6,0x821b},
{0x1bf7,0x1bf7,0x85a6},
{0x1bf8,0x1bf8,0x8ce4},
{0x1bf9,0x1bf9,0x8e10},
{0x1bfa,0x1bfa,0x9077},
{0x1bfb,0x1bfb,0x91e7},
{0x1bfc,0x1bfc,0x95e1},
{0x1bfd,0x1bfd,0x9621},
{0x1bfe,0x1bfe,0x97c6},
{0x1bff,0x1bff,0x51f8},
{0x1c00,0x1c00,0x54f2},
{0x1c01,0x1c01,0x5586},
{0x1c02,0x1c02,0x5fb9},
{0x1c03,0x1c03,0x64a4},
{0x1c04,0x1c04,0x6f88},
{0x1c05,0x1c05,0x7db4},
{0x1c06,0x1c06,0x8f1f},
{0x1c07,0x1c07,0x8f4d},
{0x1c08,0x1c08,0x9435},
{0x1c09,0x1c09,0x50c9},
{0x1c0a,0x1c0a,0x5c16},
{0x1c0b,0x1c0b,0x6cbe},
{0x1c0c,0x1c0c,0x6dfb},
{0x1c0d,0x1c0d,0x751b},
{0x1c0e,0x1c0e,0x77bb},
{0x1c0f,0x1c0f,0x7c3d},
{0x1c10,0x1c10,0x7c64},
{0x1c11,0x1c11,0x8a79},
{0x1c12,0x1c12,0x8ac2},
{0x1c13,0x1c13,0x581e},
{0x1c14,0x1c14,0x59be},
{0x1c15,0x1c15,0x5e16},
{0x1c16,0x1c16,0x6377},
{0x1c17,0x1c17,0x7252},
{0x1c18,0x1c18,0x758a},
{0x1c19,0x1c19,0x776b},
{0x1c1a,0x1c1a,0x8adc},
{0x1c1b,0x1c1b,0x8cbc},
{0x1c1c,0x1c1c,0x8f12},
{0x1c1d,0x1c1d,0x5ef3},
{0x1c1e,0x1c1e,0x6674},
{0x1c1f,0x1c1f,0x6df8},
{0x1c20,0x1c20,0x807d},
{0x1c21,0x1c21,0x83c1},
{0x1c22,0x1c22,0x8acb},
{0x1c23,0x1c23,0x9751},
{0x1c24,0x1c24,0x9bd6},
{0x1c25,0x1c25,0x5243},
{0x1c26,0x1c26,0x66ff},
{0x1c27,0x1c27,0x6d95},
{0x1c28,0x1c28,0x6eef},
{0x1c29,0x1c29,0x7de0},
{0x1c2a,0x1c2a,0x8ae6},
{0x1c2b,0x1c2b,0x902e},
{0x1c2c,0x1c2c,0x905e},
{0x1c2d,0x1c2d,0x9ad4},
{0x1c2e,0x1c2e,0x521d},
{0x1c2f,0x1c2f,0x527f},
{0x1c30,0x1c30,0x54e8},
{0x1c31,0x1c31,0x6194},
{0x1c32,0x1c32,0x6284},
{0x1c33,0x1c33,0x62db},
{0x1c34,0x1c34,0x68a2},
{0x1c35,0x1c35,0x6912},
{0x1c36,0x1c36,0x695a},
{0x1c37,0x1c37,0x6a35},
{0x1c38,0x1c38,0x7092},
{0x1c39,0x1c39,0x7126},
{0x1c3a,0x1c3a,0x785d},
{0x1c3b,0x1c3b,0x7901},
{0x1c3c,0x1c3c,0x790e},
{0x1c3d,0x1c3d,0x79d2},
{0x1c3e,0x1c3e,0x7a0d},
{0x1c3f,0x1c3f,0x8096},
{0x1c40,0x1c40,0x8278},
{0x1c41,0x1c41,0x82d5},
{0x1c42,0x1c42,0x8349},
{0x1c43,0x1c43,0x8549},
{0x1c44,0x1c44,0x8c82},
{0x1c45,0x1c45,0x8d85},
{0x1c46,0x1c46,0x9162},
{0x1c47,0x1c47,0x918b},
{0x1c48,0x1c48,0x91ae},
{0x1c49,0x1c49,0x4fc3},
{0x1c4a,0x1c4a,0x56d1},
{0x1c4b,0x1c4b,0x71ed},
{0x1c4c,0x1c4c,0x77d7},
{0x1c4d,0x1c4d,0x8700},
{0x1c4e,0x1c4e,0x89f8},
{0x1c4f,0x1c4f,0x5bf8},
{0x1c50,0x1c50,0x5fd6},
{0x1c51,0x1c51,0x6751},
{0x1c52,0x1c52,0x90a8},
{0x1c53,0x1c53,0x53e2},
{0x1c54,0x1c54,0x585a},
{0x1c55,0x1c55,0x5bf5},
{0x1c56,0x1c56,0x60a4},
{0x1c57,0x1c57,0x6181},
{0x1c58,0x1c58,0x6460},
{0x1c59,0x1c59,0x7e3d},
{0x1c5a,0x1c5a,0x8070},
{0x1c5b,0x1c5b,0x8525},
{0x1c5c,0x1c5c,0x9283},
{0x1c5d,0x1c5d,0x64ae},
{0x1c5e,0x1c5e,0x50ac},
{0x1c5f,0x1c5f,0x5d14},
{0x1c60,0x1c60,0x6700},
{0x1c61,0x1c61,0x589c},
{0x1c62,0x1c62,0x62bd},
{0x1c63,0x1c63,0x63a8},
{0x1c64,0x1c64,0x690e},
{0x1c65,0x1c65,0x6978},
{0x1c66,0x1c66,0x6a1e},
{0x1c67,0x1c67,0x6e6b},
{0x1c68,0x1c68,0x76ba},
{0x1c69,0x1c69,0x79cb},
{0x1c6a,0x1c6a,0x82bb},
{0x1c6b,0x1c6b,0x8429},
{0x1c6c,0x1c6c,0x8acf},
{0x1c6d,0x1c6d,0x8da8},
{0x1c6e,0x1c6e,0x8ffd},
{0x1c6f,0x1c6f,0x9112},
{0x1c70,0x1c70,0x914b},
{0x1c71,0x1c71,0x919c},
{0x1c72,0x1c72,0x9310},
{0x1c73,0x1c73,0x9318},
{0x1c74,0x1c74,0x939a},
{0x1c75,0x1c75,0x96db},
{0x1c76,0x1c76,0x9a36},
{0x1c77,0x1c77,0x9c0d},
{0x1c78,0x1c78,0x4e11},
{0x1c79,0x1c79,0x755c},
{0x1c7a,0x1c7a,0x795d},
{0x1c7b,0x1c7b,0x7afa},
{0x1c7c,0x1c7c,0x7b51},
{0x1c7d,0x1c7d,0x7bc9},
{0x1c7e,0x1c7e,0x7e2e},
{0x1c7f,0x1c7f,0x84c4},
{0x1c80,0x1c80,0x8e59},
{0x1c81,0x1c81,0x8e74},
{0x1c82,0x1c82,0x8ef8},
{0x1c83,0x1c83,0x9010},
{0x1c84,0x1c84,0x6625},
{0x1c85,0x1c85,0x693f},
{0x1c86,0x1c86,0x7443},
{0x1c87,0x1c87,0x51fa},
{0x1c88,0x1c88,0x672e},
{0x1c89,0x1c89,0x9edc},
{0x1c8a,0x1c8a,0x5145},
{0x1c8b,0x1c8b,0x5fe0},
{0x1c8c,0x1c8c,0x6c96},
{0x1c8d,0x1c8d,0x87f2},
{0x1c8e,0x1c8e,0x885d},
{0x1c8f,0x1c8f,0x8877},
{0x1c90,0x1c90,0x60b4},
{0x1c91,0x1c91,0x81b5},
{0x1c92,0x1c92,0x8403},
{0x1c93,0x1c93,0x8d05},
{0x1c94,0x1c94,0x53d6},
{0x1c95,0x1c95,0x5439},
{0x1c96,0x1c96,0x5634},
{0x1c97,0x1c97,0x5a36},
{0x1c98,0x1c98,0x5c31},
{0x1c99,0x1c99,0x708a},
{0x1c9a,0x1c9a,0x7fe0},
{0x1c9b,0x1c9b,0x805a},
{0x1c9c,0x1c9c,0x8106},
{0x1c9d,0x1c9d,0x81ed},
{0x1c9e,0x1c9e,0x8da3},
{0x1c9f,0x1c9f,0x9189},
{0x1ca0,0x1ca0,0x9a5f},
{0x1ca1,0x1ca1,0x9df2},
{0x1ca2,0x1ca2,0x5074},
{0x1ca3,0x1ca3,0x4ec4},
{0x1ca4,0x1ca4,0x53a0},
{0x1ca5,0x1ca5,0x60fb},
{0x1ca6,0x1ca6,0x6e2c},
{0x1ca7,0x1ca7,0x5c64},
{0x1ca8,0x1ca8,0x4f88},
{0x1ca9,0x1ca9,0x5024},
{0x1caa,0x1caa,0x55e4},
{0x1cab,0x1cab,0x5cd9},
{0x1cac,0x1cac,0x5e5f},
{0x1cad,0x1cad,0x6065},
{0x1cae,0x1cae,0x6894},
{0x1caf,0x1caf,0x6cbb},
{0x1cb0,0x1cb0,0x6dc4},
{0x1cb1,0x1cb1,0x71be},
{0x1cb2,0x1cb2,0x75d4},
{0x1cb3,0x1cb3,0x75f4},
{0x1cb4,0x1cb4,0x7661},
{0x1cb5,0x1cb5,0x7a1a},
{0x1cb6,0x1cb6,0x7a49},
{0x1cb7,0x1cb7,0x7dc7},
{0x1cb8,0x1cb8,0x7dfb},
{0x1cb9,0x1cb9,0x7f6e},
{0x1cba,0x1cba,0x81f4},
{0x1cbb,0x1cbb,0x86a9},
{0x1cbc,0x1cbc,0x8f1c},
{0x1cbd,0x1cbd,0x96c9},
{0x1cbe,0x1cbe,0x99b3},
{0x1cbf,0x1cbf,0x9f52},
{0x1cc0,0x1cc0,0x5247},
{0x1cc1,0x1cc1,0x52c5},
{0x1cc2,0x1cc2,0x98ed},
{0x1cc3,0x1cc3,0x89aa},
{0x1cc4,0x1cc4,0x4e03},
{0x1cc5,0x1cc5,0x67d2},
{0x1cc6,0x1cc6,0x6f06},
{0x1cc7,0x1cc7,0x4fb5},
{0x1cc8,0x1cc8,0x5be2},
{0x1cc9,0x1cc9,0x6795},
{0x1cca,0x1cca,0x6c88},
{0x1ccb,0x1ccb,0x6d78},
{0x1ccc,0x1ccc,0x741b},
{0x1ccd,0x1ccd,0x7827},
{0x1cce,0x1cce,0x91dd},
{0x1ccf,0x1ccf,0x937c},
{0x1cd0,0x1cd0,0x87c4},
{0x1cd1,0x1cd1,0x79e4},
{0x1cd2,0x1cd2,0x7a31},
{0x1cd3,0x1cd3,0x5feb},
{0x1cd4,0x1cd4,0x4ed6},
{0x1cd5,0x1cd5,0x54a4},
{0x1cd6,0x1cd6,0x553e},
{0x1cd7,0x1cd7,0x58ae},
{0x1cd8,0x1cd8,0x59a5},
{0x1cd9,0x1cd9,0x60f0},
{0x1cda,0x1cda,0x6253},
{0x1cdb,0x1cdb,0x62d6},
{0x1cdc,0x1cdc,0x6736},
{0x1cdd,0x1cdd,0x6955},
{0x1cde,0x1cde,0x8235},
{0x1cdf,0x1cdf,0x9640},
{0x1ce0,0x1ce0,0x99b1},
{0x1ce1,0x1ce1,0x99dd},
{0x1ce2,0x1ce2,0x502c},
{0x1ce3,0x1ce3,0x5353},
{0x1ce4,0x1ce4,0x5544},
{0x1ce5,0x1ce5,0x577c},
{0x1ce6,0x1ce6,0x6258},
{0x1ce7,0x1ce7,0x64e2},
{0x1ce8,0x1ce8,0x666b},
{0x1ce9,0x1ce9,0x67dd},
{0x1cea,0x1cea,0x6fc1},
{0x1ceb,0x1ceb,0x6fef},
{0x1cec,0x1cec,0x7422},
{0x1ced,0x1ced,0x7438},
{0x1cee,0x1cee,0x8a17},
{0x1cef,0x1cef,0x9438},
{0x1cf0,0x1cf0,0x5451},
{0x1cf1,0x1cf1,0x5606},
{0x1cf2,0x1cf2,0x5766},
{0x1cf3,0x1cf3,0x5f48},
{0x1cf4,0x1cf4,0x619a},
{0x1cf5,0x1cf5,0x6b4e},
{0x1cf6,0x1cf6,0x7058},
{0x1cf7,0x1cf7,0x70ad},
{0x1cf8,0x1cf8,0x7dbb},
{0x1cf9,0x1cf9,0x8a95},
{0x1cfa,0x1cfa,0x596a},
{0x1cfb,0x1cfb,0x812b},
{0x1cfc,0x1cfc,0x63a2},
{0x1cfd,0x1cfd,0x7708},
{0x1cfe,0x1cfe,0x803d},
{0x1cff,0x1cff,0x8caa},
{0x1d00,0x1d00,0x5854},
{0x1d01,0x1d01,0x642d},
{0x1d02,0x1d02,0x69bb},
{0x1d03,0x1d03,0x5b95},
{0x1d04,0x1d04,0x5e11},
{0x1d05,0x1d05,0x6e6f},
{0x1d06,0x1d06,0x8569},
{0x1d07,0x1d07,0x514c},
{0x1d08,0x1d08,0x53f0},
{0x1d09,0x1d09,0x592a},
{0x1d0a,0x1d0a,0x6020},
{0x1d0b,0x1d0b,0x614b},
{0x1d0c,0x1d0c,0x6b86},
{0x1d0d,0x1d0d,0x6c70},
{0x1d0e,0x1d0e,0x6cf0},
{0x1d0f,0x1d0f,0x7b1e},
{0x1d10,0x1d10,0x80ce},
{0x1d11,0x1d11,0x82d4},
{0x1d12,0x1d12,0x8dc6},
{0x1d13,0x1d13,0x90b0},
{0x1d14,0x1d14,0x98b1},
{0x1d15,0x1d15,0x64c7},
{0x1d16,0x1d16,0x6fa4},
{0x1d17,0x1d17,0x6491},
{0x1d18,0x1d18,0x6504},
{0x1d19,0x1d19,0x514e},
{0x1d1a,0x1d1a,0x5410},
{0x1d1b,0x1d1b,0x571f},
{0x1d1c,0x1d1c,0x8a0e},
{0x1d1d,0x1d1d,0x615f},
{0x1d1e,0x1d1e,0x6876},
{0x1d1f,0x1d1f,0x75db},
{0x1d20,0x1d20,0x7b52},
{0x1d21,0x1d21,0x7d71},
{0x1d22,0x1d22,0x901a},
{0x1d23,0x1d23,0x5806},
{0x1d24,0x1d24,0x69cc},
{0x1d25,0x1d25,0x817f},
{0x1d26,0x1d26,0x892a},
{0x1d27,0x1d27,0x9000},
{0x1d28,0x1d28,0x9839},
{0x1d29,0x1d29,0x5078},
{0x1d2a,0x1d2a,0x5957},
{0x1d2b,0x1d2b,0x59ac},
{0x1d2c,0x1d2c,0x6295},
{0x1d2d,0x1d2d,0x900f},
{0x1d2e,0x1d2e,0x9b2a},
{0x1d2f,0x1d2f,0x615d},
{0x1d30,0x1d30,0x7279},
{0x1d31,0x1d31,0x95d6},
{0x1d32,0x1d32,0x5761},
{0x1d33,0x1d33,0x5a46},
{0x1d34,0x1d34,0x5df4},
{0x1d35,0x1d35,0x628a},
{0x1d36,0x1d36,0x64ad},
{0x1d37,0x1d37,0x64fa},
{0x1d38,0x1d38,0x6777},
{0x1d39,0x1d39,0x6ce2},
{0x1d3a,0x1d3a,0x6d3e},
{0x1d3b,0x1d3b,0x722c},
{0x1d3c,0x1d3c,0x7436},
{0x1d3d,0x1d3d,0x7834},
{0x1d3e,0x1d3e,0x7f77},
{0x1d3f,0x1d3f,0x82ad},
{0x1d40,0x1d40,0x8ddb},
{0x1d41,0x1d41,0x9817},
{0x1d42,0x1d42,0x5224},
{0x1d43,0x1d43,0x5742},
{0x1d44,0x1d44,0x677f},
{0x1d45,0x1d45,0x7248},
{0x1d46,0x1d46,0x74e3},
{0x1d47,0x1d47,0x8ca9},
{0x1d48,0x1d48,0x8fa6},
{0x1d49,0x1d49,0x9211},
{0x1d4a,0x1d4a,0x962a},
{0x1d4b,0x1d4b,0x516b},
{0x1d4c,0x1d4c,0x53ed},
{0x1d4d,0x1d4d,0x634c},
{0x1d4e,0x1d4e,0x4f69},
{0x1d4f,0x1d4f,0x5504},
{0x1d50,0x1d50,0x6096},
{0x1d51,0x1d51,0x6557},
{0x1d52,0x1d52,0x6c9b},
{0x1d53,0x1d53,0x6d7f},
{0x1d54,0x1d54,0x724c},
{0x1d55,0x1d55,0x72fd},
{0x1d56,0x1d56,0x7a17},
{0x1d57,0x1d57,0x8987},
{0x1d58,0x1d58,0x8c9d},
{0x1d59,0x1d59,0x5f6d},
{0x1d5a,0x1d5a,0x6f8e},
{0x1d5b,0x1d5b,0x70f9},
{0x1d5c,0x1d5c,0x81a8},
{0x1d5d,0x1d5d,0x610e},
{0x1d5e,0x1d5e,0x4fbf},
{0x1d5f,0x1d5f,0x504f},
{0x1d60,0x1d60,0x6241},
{0x1d61,0x1d61,0x7247},
{0x1d62,0x1d62,0x7bc7},
{0x1d63,0x1d63,0x7de8},
{0x1d64,0x1d64,0x7fe9},
{0x1d65,0x1d65,0x904d},
{0x1d66,0x1d66,0x97ad},
{0x1d67,0x1d67,0x9a19},
{0x1d68,0x1d68,0x8cb6},
{0x1d69,0x1d69,0x576a},
{0x1d6a,0x1d6a,0x5e73},
{0x1d6b,0x1d6b,0x67b0},
{0x1d6c,0x1d6c,0x840d},
{0x1d6d,0x1d6d,0x8a55},
{0x1d6e,0x1d6e,0x5420},
{0x1d6f,0x1d6f,0x5b16},
{0x1d70,0x1d70,0x5e63},
{0x1d71,0x1d71,0x5ee2},
{0x1d72,0x1d72,0x5f0a},
{0x1d73,0x1d73,0x6583},
{0x1d74,0x1d74,0x80ba},
{0x1d75,0x1d75,0x853d},
{0x1d76,0x1d76,0x9589},
{0x1d77,0x1d77,0x965b},
{0x1d78,0x1d78,0x4f48},
{0x1d79,0x1d79,0x5305},
{0x1d7a,0x1d7a,0x530d},
{0x1d7b,0x1d7b,0x530f},
{0x1d7c,0x1d7c,0x5486},
{0x1d7d,0x1d7d,0x54fa},
{0x1d7e,0x1d7e,0x5703},
{0x1d7f,0x1d7f,0x5e03},
{0x1d80,0x1d80,0x6016},
{0x1d81,0x1d81,0x629b},
{0x1d82,0x1d82,0x62b1},
{0x1d83,0x1d83,0x6355},
{0x1d84,0x1d84,0x6ce1},
{0x1d85,0x1d85,0x6d66},
{0x1d86,0x1d86,0x75b1},
{0x1d87,0x1d87,0x7832},
{0x1d88,0x1d88,0x80de},
{0x1d89,0x1d89,0x812f},
{0x1d8a,0x1d8a,0x82de},
{0x1d8b,0x1d8b,0x8461},
{0x1d8c,0x1d8c,0x84b2},
{0x1d8d,0x1d8d,0x888d},
{0x1d8e,0x1d8e,0x8912},
{0x1d8f,0x1d8f,0x900b},
{0x1d90,0x1d90,0x92ea},
{0x1d91,0x1d91,0x98fd},
{0x1d92,0x1d92,0x9b91},
{0x1d93,0x1d93,0x5e45},
{0x1d94,0x1d94,0x66b4},
{0x1d95,0x1d95,0x66dd},
{0x1d96,0x1d96,0x7011},
{0x1d97,0x1d97,0x7206},
{0x1d98,0x1d98,0x4ff5},
{0x1d99,0x1d99,0x527d},
{0x1d9a,0x1d9a,0x5f6a},
{0x1d9b,0x1d9b,0x6153},
{0x1d9c,0x1d9c,0x6753},
{0x1d9d,0x1d9d,0x6a19},
{0x1d9e,0x1d9e,0x6f02},
{0x1d9f,0x1d9f,0x74e2},
{0x1da0,0x1da0,0x7968},
{0x1da1,0x1da1,0x8868},
{0x1da2,0x1da2,0x8c79},
{0x1da3,0x1da3,0x98c7},
{0x1da4,0x1da4,0x98c4},
{0x1da5,0x1da5,0x9a43},
{0x1da6,0x1da6,0x54c1},
{0x1da7,0x1da7,0x7a1f},
{0x1da8,0x1da8,0x6953},
{0x1da9,0x1da9,0x8af7},
{0x1daa,0x1daa,0x8c4a},
{0x1dab,0x1dab,0x98a8},
{0x1dac,0x1dac,0x99ae},
{0x1dad,0x1dad,0x5f7c},
{0x1dae,0x1dae,0x62ab},
{0x1daf,0x1daf,0x75b2},
{0x1db0,0x1db0,0x76ae},
{0x1db1,0x1db1,0x88ab},
{0x1db2,0x1db2,0x907f},
{0x1db3,0x1db3,0x9642},
{0x1db4,0x1db4,0x5339},
{0x1db5,0x1db5,0x5f3c},
{0x1db6,0x1db6,0x5fc5},
{0x1db7,0x1db7,0x6ccc},
{0x1db8,0x1db8,0x73cc},
{0x1db9,0x1db9,0x7562},
{0x1dba,0x1dba,0x758b},
{0x1dbb,0x1dbb,0x7b46},
{0x1dbc,0x1dbc,0x82fe},
{0x1dbd,0x1dbd,0x999d},
{0x1dbe,0x1dbe,0x4e4f},
{0x1dbf,0x1dbf,0x903c},
{0x1dc0,0x1dc0,0x4e0b},
{0x1dc1,0x1dc1,0x4f55},
{0x1dc2,0x1dc2,0x53a6},
{0x1dc3,0x1dc3,0x590f},
{0x1dc4,0x1dc4,0x5ec8},
{0x1dc5,0x1dc5,0x6630},
{0x1dc6,0x1dc6,0x6cb3},
{0x1dc7,0x1dc7,0x7455},
{0x1dc8,0x1dc8,0x8377},
{0x1dc9,0x1dc9,0x8766},
{0x1dca,0x1dca,0x8cc0},
{0x1dcb,0x1dcb,0x9050},
{0x1dcc,0x1dcc,0x971e},
{0x1dcd,0x1dcd,0x9c15},
{0x1dce,0x1dce,0x58d1},
{0x1dcf,0x1dcf,0x5b78},
{0x1dd0,0x1dd0,0x8650},
{0x1dd1,0x1dd1,0x8b14},
{0x1dd2,0x1dd2,0x9db4},
{0x1dd3,0x1dd3,0x5bd2},
{0x1dd4,0x1dd4,0x6068},
{0x1dd5,0x1dd5,0x608d},
{0x1dd6,0x1dd6,0x65f1},
{0x1dd7,0x1dd7,0x6c57},
{0x1dd8,0x1dd8,0x6f22},
{0x1dd9,0x1dd9,0x6fa3},
{0x1dda,0x1dda,0x701a},
{0x1ddb,0x1ddb,0x7f55},
{0x1ddc,0x1ddc,0x7ff0},
{0x1ddd,0x1dde,0x9591},
{0x1ddf,0x1ddf,0x9650},
{0x1de0,0x1de0,0x97d3},
{0x1de1,0x1de1,0x5272},
{0x1de2,0x1de2,0x8f44},
{0x1de3,0x1de3,0x51fd},
{0x1de4,0x1de4,0x542b},
{0x1de5,0x1de5,0x54b8},
{0x1de6,0x1de6,0x5563},
{0x1de7,0x1de7,0x558a},
{0x1de8,0x1de8,0x6abb},
{0x1de9,0x1de9,0x6db5},
{0x1dea,0x1dea,0x7dd8},
{0x1deb,0x1deb,0x8266},
{0x1dec,0x1dec,0x929c},
{0x1ded,0x1ded,0x9677},
{0x1dee,0x1dee,0x9e79},
{0x1def,0x1def,0x5408},
{0x1df0,0x1df0,0x54c8},
{0x1df1,0x1df1,0x76d2},
{0x1df2,0x1df2,0x86e4},
{0x1df3,0x1df3,0x95a4},
{0x1df4,0x1df4,0x95d4},
{0x1df5,0x1df5,0x965c},
{0x1df6,0x1df6,0x4ea2},
{0x1df7,0x1df7,0x4f09},
{0x1df8,0x1df8,0x59ee},
{0x1df9,0x1df9,0x5ae6},
{0x1dfa,0x1dfa,0x5df7},
{0x1dfb,0x1dfb,0x6052},
{0x1dfc,0x1dfc,0x6297},
{0x1dfd,0x1dfd,0x676d},
{0x1dfe,0x1dfe,0x6841},
{0x1dff,0x1dff,0x6c86},
{0x1e00,0x1e00,0x6e2f},
{0x1e01,0x1e01,0x7f38},
{0x1e02,0x1e02,0x809b},
{0x1e03,0x1e03,0x822a},
{0x1e04,0x1e04,0x9805},
{0x1e05,0x1e05,0x4ea5},
{0x1e06,0x1e06,0x5055},
{0x1e07,0x1e07,0x54b3},
{0x1e08,0x1e08,0x5793},
{0x1e09,0x1e09,0x595a},
{0x1e0a,0x1e0a,0x5b69},
{0x1e0b,0x1e0b,0x5bb3},
{0x1e0c,0x1e0c,0x61c8},
{0x1e0d,0x1e0d,0x6977},
{0x1e0e,0x1e0e,0x6d77},
{0x1e0f,0x1e0f,0x7023},
{0x1e10,0x1e10,0x87f9},
{0x1e11,0x1e11,0x89e3},
{0x1e12,0x1e12,0x8a72},
{0x1e13,0x1e13,0x8ae7},
{0x1e14,0x1e14,0x9082},
{0x1e15,0x1e15,0x99ed},
{0x1e16,0x1e16,0x9ab8},
{0x1e17,0x1e17,0x52be},
{0x1e18,0x1e18,0x6838},
{0x1e19,0x1e19,0x5016},
{0x1e1a,0x1e1a,0x5e78},
{0x1e1b,0x1e1b,0x674f},
{0x1e1c,0x1e1c,0x8347},
{0x1e1d,0x1e1d,0x884c},
{0x1e1e,0x1e1e,0x4eab},
{0x1e1f,0x1e1f,0x5411},
{0x1e20,0x1e20,0x56ae},
{0x1e21,0x1e21,0x73e6},
{0x1e22,0x1e22,0x9115},
{0x1e23,0x1e23,0x97ff},
{0x1e24,0x1e24,0x9909},
{0x1e25,0x1e25,0x9957},
{0x1e26,0x1e26,0x9999},
{0x1e27,0x1e27,0x5653},
{0x1e28,0x1e28,0x589f},
{0x1e29,0x1e29,0x865b},
{0x1e2a,0x1e2a,0x8a31},
{0x1e2b,0x1e2b,0x61b2},
{0x1e2c,0x1e2c,0x6af6},
{0x1e2d,0x1e2d,0x737b},
{0x1e2e,0x1e2e,0x8ed2},
{0x1e2f,0x1e2f,0x6b47},
{0x1e30,0x1e30,0x96aa},
{0x1e31,0x1e31,0x9a57},
{0x1e32,0x1e32,0x5955},
{0x1e33,0x1e33,0x7200},
{0x1e34,0x1e34,0x8d6b},
{0x1e35,0x1e35,0x9769},
{0x1e36,0x1e36,0x4fd4},
{0x1e37,0x1e37,0x5cf4},
{0x1e38,0x1e38,0x5f26},
{0x1e39,0x1e39,0x61f8},
{0x1e3a,0x1e3a,0x665b},
{0x1e3b,0x1e3b,0x6ceb},
{0x1e3c,0x1e3c,0x70ab},
{0x1e3d,0x1e3d,0x7384},
{0x1e3e,0x1e3e,0x73b9},
{0x1e3f,0x1e3f,0x73fe},
{0x1e40,0x1e40,0x7729},
{0x1e41,0x1e41,0x774d},
{0x1e42,0x1e42,0x7d43},
{0x1e43,0x1e43,0x7d62},
{0x1e44,0x1e44,0x7e23},
{0x1e45,0x1e45,0x8237},
{0x1e46,0x1e46,0x8852},
{0x1e47,0x1e47,0x8ce2},
{0x1e48,0x1e48,0x9249},
{0x1e49,0x1e49,0x986f},
{0x1e4a,0x1e4a,0x5b51},
{0x1e4b,0x1e4b,0x7a74},
{0x1e4c,0x1e4c,0x8840},
{0x1e4d,0x1e4d,0x9801},
{0x1e4e,0x1e4e,0x5acc},
{0x1e4f,0x1e4f,0x4fe0},
{0x1e50,0x1e50,0x5354},
{0x1e51,0x1e51,0x593e},
{0x1e52,0x1e52,0x5cfd},
{0x1e53,0x1e53,0x633e},
{0x1e54,0x1e54,0x6d79},
{0x1e55,0x1e55,0x72f9},
{0x1e56,0x1e56,0x8105},
{0x1e57,0x1e57,0x8107},
{0x1e58,0x1e58,0x83a2},
{0x1e59,0x1e59,0x92cf},
{0x1e5a,0x1e5a,0x9830},
{0x1e5b,0x1e5b,0x4ea8},
{0x1e5c,0x1e5c,0x5144},
{0x1e5d,0x1e5d,0x5211},
{0x1e5e,0x1e5e,0x578b},
{0x1e5f,0x1e5f,0x5f62},
{0x1e60,0x1e60,0x6cc2},
{0x1e61,0x1e61,0x6ece},
{0x1e62,0x1e62,0x7005},
{0x1e63,0x1e63,0x7050},
{0x1e64,0x1e64,0x70af},
{0x1e65,0x1e65,0x7192},
{0x1e66,0x1e66,0x73e9},
{0x1e67,0x1e67,0x7469},
{0x1e68,0x1e68,0x834a},
{0x1e69,0x1e69,0x87a2},
{0x1e6a,0x1e6a,0x8861},
{0x1e6b,0x1e6b,0x9008},
{0x1e6c,0x1e6c,0x90a2},
{0x1e6d,0x1e6d,0x93a3},
{0x1e6e,0x1e6e,0x99a8},
{0x1e6f,0x1e6f,0x516e},
{0x1e70,0x1e70,0x5f57},
{0x1e71,0x1e71,0x60e0},
{0x1e72,0x1e72,0x6167},
{0x1e73,0x1e73,0x66b3},
{0x1e74,0x1e74,0x8559},
{0x1e75,0x1e75,0x8e4a},
{0x1e76,0x1e76,0x91af},
{0x1e77,0x1e77,0x978b},
{0x1e78,0x1e78,0x4e4e},
{0x1e79,0x1e79,0x4e92},
{0x1e7a,0x1e7a,0x547c},
{0x1e7b,0x1e7b,0x58d5},
{0x1e7c,0x1e7c,0x58fa},
{0x1e7d,0x1e7d,0x597d},
{0x1e7e,0x1e7e,0x5cb5},
{0x1e7f,0x1e7f,0x5f27},
{0x1e80,0x1e80,0x6236},
{0x1e81,0x1e81,0x6248},
{0x1e82,0x1e82,0x660a},
{0x1e83,0x1e83,0x6667},
{0x1e84,0x1e84,0x6beb},
{0x1e85,0x1e85,0x6d69},
{0x1e86,0x1e86,0x6dcf},
{0x1e87,0x1e87,0x6e56},
{0x1e88,0x1e88,0x6ef8},
{0x1e89,0x1e89,0x6f94},
{0x1e8a,0x1e8a,0x6fe0},
{0x1e8b,0x1e8b,0x6fe9},
{0x1e8c,0x1e8c,0x705d},
{0x1e8d,0x1e8d,0x72d0},
{0x1e8e,0x1e8e,0x7425},
{0x1e8f,0x1e8f,0x745a},
{0x1e90,0x1e90,0x74e0},
{0x1e91,0x1e91,0x7693},
{0x1e92,0x1e92,0x795c},
{0x1e93,0x1e93,0x7cca},
{0x1e94,0x1e94,0x7e1e},
{0x1e95,0x1e95,0x80e1},
{0x1e96,0x1e96,0x82a6},
{0x1e97,0x1e97,0x846b},
{0x1e98,0x1e98,0x84bf},
{0x1e99,0x1e99,0x864e},
{0x1e9a,0x1e9a,0x865f},
{0x1e9b,0x1e9b,0x8774},
{0x1e9c,0x1e9c,0x8b77},
{0x1e9d,0x1e9d,0x8c6a},
{0x1e9e,0x1e9e,0x93ac},
{0x1e9f,0x1e9f,0x9800},
{0x1ea0,0x1ea0,0x9865},
{0x1ea1,0x1ea1,0x60d1},
{0x1ea2,0x1ea2,0x6216},
{0x1ea3,0x1ea3,0x9177},
{0x1ea4,0x1ea4,0x5a5a},
{0x1ea5,0x1ea5,0x660f},
{0x1ea6,0x1ea6,0x6df7},
{0x1ea7,0x1ea7,0x6e3e},
{0x1ea8,0x1ea8,0x743f},
{0x1ea9,0x1ea9,0x9b42},
{0x1eaa,0x1eaa,0x5ffd},
{0x1eab,0x1eab,0x60da},
{0x1eac,0x1eac,0x7b0f},
{0x1ead,0x1ead,0x54c4},
{0x1eae,0x1eae,0x5f18},
{0x1eaf,0x1eaf,0x6c5e},
{0x1eb0,0x1eb0,0x6cd3},
{0x1eb1,0x1eb1,0x6d2a},
{0x1eb2,0x1eb2,0x70d8},
{0x1eb3,0x1eb3,0x7d05},
{0x1eb4,0x1eb4,0x8679},
{0x1eb5,0x1eb5,0x8a0c},
{0x1eb6,0x1eb6,0x9d3b},
{0x1eb7,0x1eb7,0x5316},
{0x1eb8,0x1eb8,0x548c},
{0x1eb9,0x1eb9,0x5b05},
{0x1eba,0x1eba,0x6a3a},
{0x1ebb,0x1ebb,0x706b},
{0x1ebc,0x1ebc,0x7575},
{0x1ebd,0x1ebd,0x798d},
{0x1ebe,0x1ebe,0x79be},
{0x1ebf,0x1ebf,0x82b1},
{0x1ec0,0x1ec0,0x83ef},
{0x1ec1,0x1ec1,0x8a71},
{0x1ec2,0x1ec2,0x8b41},
{0x1ec3,0x1ec3,0x8ca8},
{0x1ec4,0x1ec4,0x9774},
{0x1ec5,0x1ec5,0x64f4},
{0x1ec6,0x1ec6,0x652b},
{0x1ec7,0x1ec8,0x78ba},
{0x1ec9,0x1ec9,0x7a6b},
{0x1eca,0x1eca,0x4e38},
{0x1ecb,0x1ecb,0x559a},
{0x1ecc,0x1ecc,0x5950},
{0x1ecd,0x1ecd,0x5ba6},
{0x1ece,0x1ece,0x5e7b},
{0x1ecf,0x1ecf,0x60a3},
{0x1ed0,0x1ed0,0x63db},
{0x1ed1,0x1ed1,0x6b61},
{0x1ed2,0x1ed2,0x6665},
{0x1ed3,0x1ed3,0x6853},
{0x1ed4,0x1ed4,0x6e19},
{0x1ed5,0x1ed5,0x7165},
{0x1ed6,0x1ed6,0x74b0},
{0x1ed7,0x1ed7,0x7d08},
{0x1ed8,0x1ed8,0x9084},
{0x1ed9,0x1ed9,0x9a69},
{0x1eda,0x1eda,0x9c25},
{0x1edb,0x1edb,0x6d3b},
{0x1edc,0x1edc,0x6ed1},
{0x1edd,0x1edd,0x733e},
{0x1ede,0x1ede,0x8c41},
{0x1edf,0x1edf,0x95ca},
{0x1ee0,0x1ee0,0x51f0},
{0x1ee1,0x1ee1,0x5e4c},
{0x1ee2,0x1ee2,0x5fa8},
{0x1ee3,0x1ee3,0x604d},
{0x1ee4,0x1ee4,0x60f6},
{0x1ee5,0x1ee5,0x6130},
{0x1ee6,0x1ee6,0x614c},
{0x1ee7,0x1ee8,0x6643},
{0x1ee9,0x1ee9,0x69a5},
{0x1eea,0x1eea,0x6cc1},
{0x1eeb,0x1eeb,0x6e5f},
{0x1eec,0x1eec,0x6ec9},
{0x1eed,0x1eed,0x6f62},
{0x1eee,0x1eee,0x714c},
{0x1eef,0x1eef,0x749c},
{0x1ef0,0x1ef0,0x7687},
{0x1ef1,0x1ef1,0x7bc1},
{0x1ef2,0x1ef2,0x7c27},
{0x1ef3,0x1ef3,0x8352},
{0x1ef4,0x1ef4,0x8757},
{0x1ef5,0x1ef5,0x9051},
{0x1ef6,0x1ef6,0x968d},
{0x1ef7,0x1ef7,0x9ec3},
{0x1ef8,0x1ef8,0x532f},
{0x1ef9,0x1ef9,0x56de},
{0x1efa,0x1efa,0x5efb},
{0x1efb,0x1efb,0x5f8a},
{0x1efc,0x1efc,0x6062},
{0x1efd,0x1efd,0x6094},
{0x1efe,0x1efe,0x61f7},
{0x1eff,0x1eff,0x6666},
{0x1f00,0x1f00,0x6703},
{0x1f01,0x1f01,0x6a9c},
{0x1f02,0x1f02,0x6dee},
{0x1f03,0x1f03,0x6fae},
{0x1f04,0x1f04,0x7070},
{0x1f05,0x1f05,0x736a},
{0x1f06,0x1f06,0x7e6a},
{0x1f07,0x1f07,0x81be},
{0x1f08,0x1f08,0x8334},
{0x1f09,0x1f09,0x86d4},
{0x1f0a,0x1f0a,0x8aa8},
{0x1f0b,0x1f0b,0x8cc4},
{0x1f0c,0x1f0c,0x5283},
{0x1f0d,0x1f0d,0x7372},
{0x1f0e,0x1f0e,0x5b96},
{0x1f0f,0x1f0f,0x6a6b},
{0x1f10,0x1f10,0x9404},
{0x1f11,0x1f11,0x54ee},
{0x1f12,0x1f12,0x5686},
{0x1f13,0x1f13,0x5b5d},
{0x1f14,0x1f14,0x6548},
{0x1f15,0x1f15,0x6585},
{0x1f16,0x1f16,0x66c9},
{0x1f17,0x1f17,0x689f},
{0x1f18,0x1f18,0x6d8d},
{0x1f19,0x1f19,0x6dc6},
{0x1f1a,0x1f1a,0x723b},
{0x1f1b,0x1f1b,0x80b4},
{0x1f1c,0x1f1c,0x9175},
{0x1f1d,0x1f1d,0x9a4d},
{0x1f1e,0x1f1e,0x4faf},
{0x1f1f,0x1f1f,0x5019},
{0x1f20,0x1f20,0x539a},
{0x1f21,0x1f21,0x540e},
{0x1f22,0x1f22,0x543c},
{0x1f23,0x1f23,0x5589},
{0x1f24,0x1f24,0x55c5},
{0x1f25,0x1f25,0x5e3f},
{0x1f26,0x1f26,0x5f8c},
{0x1f27,0x1f27,0x673d},
{0x1f28,0x1f28,0x7166},
{0x1f29,0x1f29,0x73dd},
{0x1f2a,0x1f2a,0x9005},
{0x1f2b,0x1f2b,0x52db},
{0x1f2c,0x1f2c,0x52f3},
{0x1f2d,0x1f2d,0x5864},
{0x1f2e,0x1f2e,0x58ce},
{0x1f2f,0x1f2f,0x7104},
{0x1f30,0x1f30,0x718f},
{0x1f31,0x1f31,0x71fb},
{0x1f32,0x1f32,0x85b0},
{0x1f33,0x1f33,0x8a13},
{0x1f34,0x1f34,0x6688},
{0x1f35,0x1f35,0x85a8},
{0x1f36,0x1f36,0x55a7},
{0x1f37,0x1f37,0x6684},
{0x1f38,0x1f38,0x714a},
{0x1f39,0x1f39,0x8431},
{0x1f3a,0x1f3a,0x5349},
{0x1f3b,0x1f3b,0x5599},
{0x1f3c,0x1f3c,0x6bc1},
{0x1f3d,0x1f3d,0x5f59},
{0x1f3e,0x1f3e,0x5fbd},
{0x1f3f,0x1f3f,0x63ee},
{0x1f40,0x1f40,0x6689},
{0x1f41,0x1f41,0x7147},
{0x1f42,0x1f42,0x8af1},
{0x1f43,0x1f43,0x8f1d},
{0x1f44,0x1f44,0x9ebe},
{0x1f45,0x1f45,0x4f11},
{0x1f46,0x1f46,0x643a},
{0x1f47,0x1f47,0x70cb},
{0x1f48,0x1f48,0x7566},
{0x1f49,0x1f49,0x8667},
{0x1f4a,0x1f4a,0x6064},
{0x1f4b,0x1f4b,0x8b4e},
{0x1f4c,0x1f4c,0x9df8},
{0x1f4d,0x1f4d,0x5147},
{0x1f4e,0x1f4e,0x51f6},
{0x1f4f,0x1f4f,0x5308},
{0x1f50,0x1f50,0x6d36},
{0x1f51,0x1f51,0x80f8},
{0x1f52,0x1f52,0x9ed1},
{0x1f53,0x1f53,0x6615},
{0x1f54,0x1f54,0x6b23},
{0x1f55,0x1f55,0x7098},
{0x1f56,0x1f56,0x75d5},
{0x1f57,0x1f57,0x5403},
{0x1f58,0x1f58,0x5c79},
{0x1f59,0x1f59,0x7d07},
{0x1f5a,0x1f5a,0x8a16},
{0x1f5b,0x1f5b,0x6b20},
{0x1f5c,0x1f5c,0x6b3d},
{0x1f5d,0x1f5d,0x6b46},
{0x1f5e,0x1f5e,0x5438},
{0x1f5f,0x1f5f,0x6070},
{0x1f60,0x1f60,0x6d3d},
{0x1f61,0x1f61,0x7fd5},
{0x1f62,0x1f62,0x8208},
{0x1f63,0x1f63,0x50d6},
{0x1f64,0x1f64,0x51de},
{0x1f65,0x1f65,0x559c},
{0x1f66,0x1f66,0x566b},
{0x1f67,0x1f67,0x56cd},
{0x1f68,0x1f68,0x59ec},
{0x1f69,0x1f69,0x5b09},
{0x1f6a,0x1f6a,0x5e0c},
{0x1f6b,0x1f6b,0x6199},
{0x1f6c,0x1f6c,0x6198},
{0x1f6d,0x1f6d,0x6231},
{0x1f6e,0x1f6e,0x665e},
{0x1f6f,0x1f6f,0x66e6},
{0x1f70,0x1f70,0x7199},
{0x1f71,0x1f72,0x71b9},
{0x1f73,0x1f73,0x72a7},
{0x1f74,0x1f74,0x79a7},
{0x1f75,0x1f75,0x7a00},
{0x1f76,0x1f76,0x7fb2},
{0x1f77,0x1f77,0x8a70},
{0x1f78,0x1f79,0x3001},
{0x1f7a,0x1f7a,0x2025},
{0x1f7b,0x1f7b,0xad},
{0x1f7c,0x1f7c,0x2015},
{0x1f7d,0x1f7d,0x2225},
{0x1f7e,0x1f7e,0xff5e},
{0x1f7f,0x1f80,0x3014},
{0x1f81,0x1f8a,0x3008},
{0x1f8b,0x1f8b,0x3013},
{0x1f8c,0x1f8c,0xff01},
{0x1f8d,0x1f8e,0xff08},
{0x1f8f,0x1f8f,0xff0c},
{0x1f90,0x1f90,0xff0e},
{0x1f91,0x1f96,0xff1a},
{0x1f97,0x1f97,0xff3b},
{0x1f98,0x1f98,0xff3d},
{0x1f99,0x1f99,0xff3f},
{0x1f9a,0x1f9d,0xff5b},
{0x1f9e,0x1ffb,0x20},
{0x1ffc,0x1ffc,0x203e},
{0x1ffd,0x1ffd,0x7e},
{0x1ffe,0x1ffe,0x5c},
{0x1fff,0x1fff,0x231c},
{0x2000,0x2000,0x231f},
{0x2005,0x2006,0x300e},
{0x2007,0x2008,0x300a},
{0x2009,0x200a,0x3008},
{0x200b,0x200c,0x28},
{0x2015,0x2016,0x28},
{0x2019,0x201a,0x3016},
{0x201b,0x201e,0x3016},
{0x201f,0x201f,0x5b},
{0x2020,0x2020,0x5d},
{0x2023,0x2023,0x263e},
{0x2024,0x2024,0x263d},
{0x2025,0x2025,0x2021},
{0x2026,0x2026,0x2021},
{0x2027,0x2028,0x2020},
{0x202a,0x202a,0x2217},
{0x202d,0x202d,0x201f},
{0x202e,0x202e,0x201b},
{0x202f,0x2030,0x2d6},
{0x2031,0x2031,0xd7},
{0x2032,0x2032,0x221e},
{0x2034,0x2034,0xb1},
{0x2035,0x2035,0x2213},
{0x2036,0x2036,0x3e},
{0x2037,0x2038,0x3c},
{0x2039,0x2039,0x2260},
{0x203a,0x203b,0x207d},
{0x203c,0x203c,0x7b},
{0x203d,0x203d,0x7d},
{0x2040,0x2041,0x2229},
{0x2042,0x2042,0x2282},
{0x2043,0x2043,0x2208},
{0x2044,0x2044,0x3a3},
{0x2045,0x2045,0x21},
{0x2047,0x2049,0x3021},
{0x204d,0x204d,0x4e03},
{0x2050,0x2050,0x5341},
{0x2053,0x2053,0x27},
{0x2054,0x2055,0x3014},
{0x2056,0x2056,0x2a},
{0x2058,0x2058,0x2217},
{0x2059,0x2059,0x2a},
{0x205b,0x205c,0x3001},
{0x2061,0x2062,0x28},
{0x2063,0x2064,0x3016},
{0x2065,0x2066,0x3010},
{0x207d,0x207d,0xb6},
{0x2080,0x2080,0x22ef},
{0x2081,0x2081,0x2237},
{0x2096,0x209f,0x278a},
{0x20c4,0x20dd,0x24b6},
{0x211b,0x211b,0x22cf},
{0x211c,0x211d,0x22ce},
{0x211e,0x211e,0x22ce},
{0x211f,0x2120,0x2280},
{0x2121,0x2124,0x2270},
{0x2126,0x2126,0x228a},
{0x2128,0x2128,0x228b},
{0x2129,0x212a,0x2276},
{0x212b,0x212b,0x2279},
{0x212c,0x212d,0x22da},
{0x2132,0x2132,0x2221},
{0x2133,0x2133,0x2245},
{0x2134,0x2134,0x2243},
{0x2135,0x2135,0x2248},
{0x2137,0x2137,0x22a4},
{0x2138,0x2138,0x22ab},
{0x2145,0x214d,0x2460},
{0x2182,0x2182,0x21},
{0x2183,0x2183,0xb0},
{0x2184,0x2184,0x27},
{0x2185,0x2185,0x22},
{0x2186,0x2186,0x2034},
{0x218d,0x218d,0x2020},
{0x2191,0x2191,0xa7},
{0x2192,0x2192,0x23},
{0x2193,0x2193,0x2a},
{0x2194,0x2194,0x2a},
{0x2197,0x2197,0x2042},
{0x2198,0x2198,0x2a},
{0x219b,0x219b,0x2042},
{0x219c,0x219c,0x2a},
{0x219f,0x219f,0x2022},
{0x21a4,0x21a7,0x3008},
{0x21a8,0x21a8,0x261c},
{0x21a9,0x21a9,0x261e},
{0x21b6,0x21b6,0x271a},
{0x21b7,0x21b7,0x2716},
{0x21b9,0x21b9,0x25ef},
{0x21bf,0x21bf,0x25cc},
{0x21d5,0x21d5,0x20a9},
{0x21d8,0x21d8,0x262f},
{0x21da,0x21da,0x262f},
{0x21db,0x21db,0x262f},
{0x21df,0x21df,0x3020},
{0x21e1,0x21ea,0x2776},
{0x21fb,0x21fb,0x2642},
{0x21fc,0x21fc,0x22a9},
{0x21fd,0x21fd,0x3036},
{0x21fe,0x21fe,0x25cb},
{0x21ff,0x21ff,0x25d3},
{0x2200,0x2200,0x25a1},
{0x2201,0x2201,0x2302},
{0x2203,0x2203,0x25ad},
{0x2204,0x2204,0x25ad},
{0x2208,0x2208,0x25c7},
{0x220b,0x220b,0x2206},
{0x220c,0x220c,0x22bf},
{0x220d,0x220d,0x231e},
{0x220f,0x2210,0x2225},
{0x2212,0x2212,0x2253},
{0x2213,0x2213,0x2251},
{0x2214,0x2215,0x2266},
{0x2216,0x2216,0x2213},
{0x2217,0x2219,0x2295},
{0x221b,0x221b,0x2314},
{0x221e,0x221e,0x2262},
{0x2220,0x2220,0x25b1},
{0x2222,0x2222,0x2222},
{0x2223,0x2223,0x2250},
{0x2224,0x2224,0xd8},
{0x2225,0x2225,0x234a},
{0x2226,0x2226,0x22a3},
{0x2229,0x222a,0x226e},
{0x222b,0x222b,0x2285},
{0x222c,0x222c,0x2284},
{0x222d,0x222d,0x2209},
{0x222e,0x222e,0x220c},
{0x222f,0x222f,0x22bb},
{0x2230,0x2230,0x16},
{0x2231,0x2231,0x225a},
{0x2232,0x2232,0x2306},
{0x2233,0x2233,0x24},
{0x2239,0x2239,0x329e},
{0x223b,0x223b,0x203c},
{0x223f,0x223f,0x22c4},
{0x2240,0x2240,0x25c7},
{0x2241,0x2241,0x22c4},
{0x2242,0x2242,0x25c6},
{0x2243,0x2243,0x25ab},
{0x2244,0x2244,0x25a1},
{0x2245,0x2245,0x25ab},
{0x2246,0x2246,0x25a0},
{0x2247,0x2247,0x25e6},
{0x2248,0x2248,0x25cb},
{0x2249,0x2249,0x25e6},
{0x224a,0x224a,0x25cf},
{0x224b,0x224b,0x25bd},
{0x224c,0x224c,0x25b3},
{0x224d,0x224d,0x25b7},
{0x224e,0x224e,0x25c1},
{0x224f,0x224f,0x2726},
{0x2250,0x2250,0x25cf},
{0x2251,0x2251,0x25a0},
{0x2253,0x2253,0x25ca},
{0x2254,0x2254,0x3231},
{0x2255,0x2255,0x3239},
{0x2256,0x2256,0x33cb},
{0x2257,0x2257,0x2483},
{0x2258,0x225b,0x2494},
{0x2266,0x2266,0x2192},
{0x2267,0x2268,0x2190},
{0x2269,0x2269,0x2193},
{0x226a,0x226d,0x2196},
{0x226e,0x226e,0x21d0},
{0x226f,0x226f,0x21cf},
{0x2270,0x2270,0x21cd},
{0x2271,0x2271,0x21d4},
{0x2279,0x2279,0x2794},
{0x2285,0x2285,0x279c},
{0x2291,0x2291,0x27ba},
{0x2296,0x2296,0x21d1},
{0x2297,0x2297,0x21d3},
{0x2299,0x2299,0x279e},
{0x229d,0x229d,0x279e},
{0x22a0,0x22a0,0x21b5},
{0x22a1,0x22a1,0x21b1},
{0x22a3,0x22a3,0x21b4},
{0x22a4,0x22a4,0x21b0},
{0x22a5,0x22a5,0x21b3},
{0x22b0,0x22b0,0x21e6},
{0x22b1,0x22b1,0x21e8},
{0x22b2,0x22b2,0x21e7},
{0x22b3,0x22b3,0x21e9},
{0x22b4,0x22b4,0x21bc},
{0x22b5,0x22b5,0x21c0},
{0x22ba,0x22ba,0x21c0},
{0x22bb,0x22bb,0x21bc},
{0x22c0,0x22c1,0x21c4},
{0x2352,0x2356,0x2483},
{0x2378,0x2378,0x3294},
{0x2388,0x2388,0x32a5},
{0x23da,0x23da,0x2794},
{0x23e6,0x23e9,0x21e0},
{0x23eb,0x23eb,0x27a1},
{0x23ee,0x23ee,0x21e6},
{0x23ef,0x23ef,0x21e8},
{0x23f0,0x23f0,0x21e7},
{0x23f1,0x23f1,0x21e9},
{0x2406,0x2406,0x261d},
{0x2407,0x2407,0x261f},
{0x2454,0x2454,0x3290},
{0x2455,0x245a,0x328a},
{0x2462,0x2465,0x300c},
{0x2466,0x2466,0x21e8},
{0x2467,0x2468,0x21e6},
{0x2469,0x2469,0x21e9},
{0x246a,0x246a,0x22},
{0x246b,0x246b,0x301f},
{0x246c,0x246c,0x301d},
{0x246d,0x246d,0x2036},
{0x246e,0x246e,0x60},
{0x246f,0x246f,0x3001},
{0x2471,0x2471,0x21},
{0x2472,0x2472,0x3021},
{0x2473,0x2473,0x2c},
{0x2474,0x2474,0x3002},
{0x2475,0x2476,0xac02},
{0x2477,0x2478,0xac05},
{0x2479,0x247d,0xac0b},
{0x247e,0x247e,0xac18},
{0x247f,0x2480,0xac1e},
{0x2481,0x2483,0xac21},
{0x2484,0x248a,0xac25},
{0x248b,0x248b,0xac2e},
{0x248c,0x2491,0xac32},
{0x2492,0x2493,0xac3a},
{0x2494,0x2496,0xac3d},
{0x2497,0x24a0,0xac41},
{0x24a1,0x24a1,0xac4c},
{0x24a2,0x24a7,0xac4e},
{0x24a8,0x24aa,0xac55},
{0x24ab,0x24ad,0xac59},
{0x24ae,0x24c0,0xac5d},
{0x24c1,0x24c2,0xac72},
{0x24c3,0x24c4,0xac75},
{0x24c5,0x24c5,0xac79},
{0x24c6,0x24ca,0xac7b},
{0x24cb,0x24cb,0xac82},
{0x24cc,0x24cd,0xac87},
{0x24ce,0x24d0,0xac8d},
{0x24d1,0x24d3,0xac91},
{0x24d4,0x24da,0xac95},
{0x24db,0x24db,0xac9e},
{0x24dc,0x24e1,0xaca2},
{0x24e2,0x24e2,0xacab},
{0x24e3,0x24e4,0xacad},
{0x24e5,0x24eb,0xacb1},
{0x24ec,0x24ec,0xacba},
{0x24ed,0x24ef,0xacbe},
{0x24f0,0x24f1,0xacc2},
{0x24f2,0x24f4,0xacc5},
{0x24f5,0x24f7,0xacc9},
{0x24f8,0x24ff,0xaccd},
{0x2500,0x2500,0xacd6},
{0x2501,0x2508,0xacd8},
{0x2509,0x250a,0xace2},
{0x250b,0x250c,0xace5},
{0x250d,0x250d,0xace9},
{0x250e,0x250e,0xaceb},
{0x250f,0x2510,0xaced},
{0x2511,0x2511,0xacf2},
{0x2512,0x2512,0xacf4},
{0x2513,0x2517,0xacf7},
{0x2518,0x2519,0xacfe},
{0x251a,0x251c,0xad01},
{0x251d,0x251d,0xad05},
{0x251e,0x2522,0xad07},
{0x2523,0x2523,0xad0e},
{0x2524,0x2524,0xad10},
{0x2525,0x252a,0xad12},
{0x252b,0x252d,0xad19},
{0x252e,0x2530,0xad1d},
{0x2531,0x2538,0xad21},
{0x2539,0x253a,0xad2a},
{0x253b,0x2540,0xad2e},
{0x2541,0x2542,0xad36},
{0x2543,0x2545,0xad39},
{0x2546,0x254c,0xad3d},
{0x254d,0x254d,0xad46},
{0x254e,0x254e,0xad48},
{0x254f,0x2554,0xad4a},
{0x2555,0x2557,0xad51},
{0x2558,0x255a,0xad55},
{0x255b,0x2562,0xad59},
{0x2563,0x2563,0xad62},
{0x2564,0x256b,0xad64},
{0x256c,0x256d,0xad6e},
{0x256e,0x256f,0xad71},
{0x2570,0x2573,0xad77},
{0x2574,0x2574,0xad7e},
{0x2575,0x2575,0xad80},
{0x2576,0x257a,0xad83},
{0x257b,0x257c,0xad8a},
{0x257d,0x257f,0xad8d},
{0x2580,0x258a,0xad91},
{0x258b,0x2590,0xad9e},
{0x2591,0x25a2,0xada5},
{0x25a3,0x25aa,0xadb8},
{0x25ab,0x25ac,0xadc2},
{0x25ad,0x25af,0xadc5},
{0x25b0,0x25b6,0xadc9},
{0x25b7,0x25b7,0xadd2},
{0x25b8,0x25bf,0xadd4},
{0x25c0,0x25c2,0xaddd},
{0x25c3,0x25c5,0xade1},
{0x25c6,0x25d8,0xade5},
{0x25d9,0x25da,0xadfa},
{0x25db,0x25dc,0xadfd},
{0x25dd,0x25e2,0xae02},
{0x25e3,0x25e3,0xae0a},
{0x25e4,0x25e4,0xae0c},
{0x25e5,0x25ea,0xae0e},
{0x25eb,0x2605,0xae15},
{0x2606,0x2607,0xae32},
{0x2608,0x2609,0xae35},
{0x260a,0x260a,0xae39},
{0x260b,0x260f,0xae3b},
{0x2610,0x2610,0xae42},
{0x2611,0x2611,0xae44},
{0x2612,0x2614,0xae47},
{0x2615,0x2615,0xae4b},
{0x2616,0x2616,0xae4f},
{0x2617,0x2619,0xae51},
{0x261a,0x261a,0xae55},
{0x261b,0x261f,0xae57},
{0x2620,0x2620,0xae5e},
{0x2621,0x2623,0xae62},
{0x2624,0x2625,0xae66},
{0x2626,0x2627,0xae6a},
{0x2628,0x262a,0xae6d},
{0x262b,0x2631,0xae71},
{0x2632,0x2632,0xae7a},
{0x2633,0x2638,0xae7e},
{0x2639,0x263e,0xae86},
{0x263f,0x266d,0xae8d},
{0x266e,0x266e,0xaebf},
{0x266f,0x2671,0xaec1},
{0x2672,0x2678,0xaec5},
{0x2679,0x2679,0xaece},
{0x267a,0x267f,0xaed2},
{0x2680,0x2681,0xaeda},
{0x2682,0x268c,0xaedd},
{0x268d,0x268e,0xaee9},
{0x268f,0x268f,0xaeec},
{0x2690,0x2695,0xaeee},
{0x2696,0x2698,0xaef5},
{0x2699,0x269b,0xaef9},
{0x269c,0x26a5,0xaefd},
{0x26a6,0x26a9,0xaf09},
{0x26aa,0x26ab,0xaf0e},
{0x26ac,0x26c6,0xaf11},
{0x26c7,0x26c8,0xaf2e},
{0x26c9,0x26c9,0xaf31},
{0x26ca,0x26ca,0xaf33},
{0x26cb,0x26d1,0xaf35},
{0x26d2,0x26d2,0xaf3e},
{0x26d3,0x26d3,0xaf40},
{0x26d4,0x26d7,0xaf44},
{0x26d8,0x26dd,0xaf4a},
{0x26de,0x26e8,0xaf51},
{0x26e9,0x26ee,0xaf5e},
{0x26ef,0x2701,0xaf66},
{0x2702,0x2707,0xaf7a},
{0x2708,0x270a,0xaf81},
{0x270b,0x270d,0xaf85},
{0x270e,0x2714,0xaf89},
{0x2715,0x2717,0xaf92},
{0x2718,0x271d,0xaf96},
{0x271e,0x2738,0xaf9d},
{0x2739,0x273a,0xafba},
{0x273b,0x273d,0xafbd},
{0x273e,0x2743,0xafc1},
{0x2744,0x2744,0xafca},
{0x2745,0x2745,0xafcc},
{0x2746,0x274a,0xafcf},
{0x274b,0x2751,0xafd5},
{0x2752,0x275c,0xafdd},
{0x275d,0x2762,0xafea},
{0x2763,0x2764,0xaff2},
{0x2765,0x2767,0xaff5},
{0x2768,0x276e,0xaff9},
{0x276f,0x2770,0xb002},
{0x2771,0x2777,0xb005},
{0x2778,0x277a,0xb00d},
{0x277b,0x277d,0xb011},
{0x277e,0x2784,0xb015},
{0x2785,0x278e,0xb01e},
{0x278f,0x27a9,0xb029},
{0x27aa,0x27ab,0xb046},
{0x27ac,0x27ac,0xb049},
{0x27ad,0x27ad,0xb04b},
{0x27ae,0x27ae,0xb04d},
{0x27af,0x27b2,0xb04f},
{0x27b3,0x27b3,0xb056},
{0x27b4,0x27b4,0xb058},
{0x27b5,0x27b7,0xb05a},
{0x27b8,0x27d5,0xb05e},
{0x27d6,0x27d7,0xb07e},
{0x27d8,0x27da,0xb081},
{0x27db,0x27e1,0xb085},
{0x27e2,0x27e2,0xb08e},
{0x27e3,0x27e3,0xb090},
{0x27e4,0x27e9,0xb092},
{0x27ea,0x27ea,0xb09b},
{0x27eb,0x27ec,0xb09d},
{0x27ed,0x27f1,0xb0a3},
{0x27f2,0x27f2,0xb0aa},
{0x27f3,0x27f3,0xb0b0},
{0x27f4,0x27f4,0xb0b2},
{0x27f5,0x27f6,0xb0b6},
{0x27f7,0x27f9,0xb0b9},
{0x27fa,0x2800,0xb0bd},
{0x2801,0x2801,0xb0c6},
{0x2802,0x2807,0xb0ca},
{0x2808,0x2809,0xb0d2},
{0x280a,0x280c,0xb0d5},
{0x280d,0x2813,0xb0d9},
{0x2814,0x2817,0xb0e1},
{0x2818,0x2839,0xb0e6},
{0x283a,0x283a,0xb10a},
{0x283b,0x283d,0xb10d},
{0x283e,0x283e,0xb111},
{0x283f,0x2842,0xb114},
{0x2843,0x2843,0xb11a},
{0x2844,0x2848,0xb11e},
{0x2849,0x284a,0xb126},
{0x284b,0x284d,0xb129},
{0x284e,0x2854,0xb12d},
{0x2855,0x2855,0xb136},
{0x2856,0x285b,0xb13a},
{0x285c,0x285d,0xb142},
{0x285e,0x2860,0xb145},
{0x2861,0x2867,0xb149},
{0x2868,0x2869,0xb152},
{0x286a,0x286b,0xb156},
{0x286c,0x286e,0xb159},
{0x286f,0x2871,0xb15d},
{0x2872,0x2888,0xb161},
{0x2889,0x288a,0xb17a},
{0x288b,0x288d,0xb17d},
{0x288e,0x288e,0xb181},
{0x288f,0x2893,0xb183},
{0x2894,0x2894,0xb18a},
{0x2895,0x2895,0xb18c},
{0x2896,0x2899,0xb18e},
{0x289a,0x289c,0xb195},
{0x289d,0x289f,0xb199},
{0x28a0,0x28aa,0xb19d},
{0x28ab,0x28cd,0xb1a9},
{0x28ce,0x28d0,0xb1cd},
{0x28d1,0x28d3,0xb1d1},
{0x28d4,0x28da,0xb1d5},
{0x28db,0x28db,0xb1de},
{0x28dc,0x28e3,0xb1e0},
{0x28e4,0x28e5,0xb1ea},
{0x28e6,0x28e8,0xb1ed},
{0x28e9,0x28f0,0xb1f1},
{0x28f1,0x28f1,0xb1fa},
{0x28f2,0x28f2,0xb1fc},
{0x28f3,0x28f8,0xb1fe},
{0x28f9,0x28fa,0xb206},
{0x28fb,0x28fc,0xb209},
{0x28fd,0x2903,0xb20d},
{0x2904,0x2904,0xb216},
{0x2905,0x2905,0xb218},
{0x2906,0x290b,0xb21a},
{0x290c,0x291e,0xb221},
{0x291f,0x2925,0xb235},
{0x2926,0x2940,0xb23d},
{0x2941,0x2943,0xb259},
{0x2944,0x2946,0xb25d},
{0x2947,0x294d,0xb261},
{0x294e,0x2957,0xb26a},
{0x2958,0x295d,0xb276},
{0x295e,0x2964,0xb27d},
{0x2965,0x2967,0xb286},
{0x2968,0x296d,0xb28a},
{0x296e,0x296f,0xb292},
{0x2970,0x2972,0xb295},
{0x2973,0x2977,0xb29b},
{0x2978,0x2978,0xb2a2},
{0x2979,0x2979,0xb2a4},
{0x297a,0x297c,0xb2a7},
{0x297d,0x297d,0xb2ab},
{0x297e,0x2980,0xb2ad},
{0x2981,0x2983,0xb2b1},
{0x2984,0x2996,0xb2b5},
{0x2997,0x2998,0xb2ca},
{0x2999,0x299b,0xb2cd},
{0x299c,0x299c,0xb2d1},
{0x299d,0x29a1,0xb2d3},
{0x29a2,0x29a2,0xb2da},
{0x29a3,0x29a3,0xb2dc},
{0x29a4,0x29a7,0xb2de},
{0x29a8,0x29a8,0xb2e3},
{0x29a9,0x29a9,0xb2e7},
{0x29aa,0x29ab,0xb2e9},
{0x29ac,0x29ae,0xb2f0},
{0x29af,0x29af,0xb2f6},
{0x29b0,0x29b2,0xb2fc},
{0x29b3,0x29b4,0xb302},
{0x29b5,0x29b7,0xb305},
{0x29b8,0x29be,0xb309},
{0x29bf,0x29bf,0xb312},
{0x29c0,0x29c5,0xb316},
{0x29c6,0x29fc,0xb31d},
{0x29fd,0x29fd,0xb357},
{0x29fe,0x29ff,0xb359},
{0x2a00,0x2a00,0xb35d},
{0x2a01,0x2a04,0xb360},
{0x2a05,0x2a05,0xb366},
{0x2a06,0x2a06,0xb368},
{0x2a07,0x2a07,0xb36a},
{0x2a08,0x2a09,0xb36c},
{0x2a0a,0x2a0a,0xb36f},
{0x2a0b,0x2a0c,0xb372},
{0x2a0d,0x2a0f,0xb375},
{0x2a10,0x2a16,0xb379},
{0x2a17,0x2a17,0xb382},
{0x2a18,0x2a1d,0xb386},
{0x2a1e,0x2a20,0xb38d},
{0x2a21,0x2a23,0xb391},
{0x2a24,0x2a2e,0xb395},
{0x2a2f,0x2a34,0xb3a2},
{0x2a35,0x2a37,0xb3a9},
{0x2a38,0x2a4e,0xb3ad},
{0x2a4f,0x2a50,0xb3c6},
{0x2a51,0x2a52,0xb3c9},
{0x2a53,0x2a53,0xb3cd},
{0x2a54,0x2a54,0xb3cf},
{0x2a55,0x2a57,0xb3d1},
{0x2a58,0x2a58,0xb3d6},
{0x2a59,0x2a59,0xb3d8},
{0x2a5a,0x2a5a,0xb3da},
{0x2a5b,0x2a5b,0xb3dc},
{0x2a5c,0x2a5d,0xb3de},
{0x2a5e,0x2a60,0xb3e1},
{0x2a61,0x2a63,0xb3e5},
{0x2a64,0x2a76,0xb3e9},
{0x2a77,0x2a89,0xb3fd},
{0x2a8a,0x2a90,0xb411},
{0x2a91,0x2a93,0xb419},
{0x2a94,0x2a96,0xb41d},
{0x2a97,0x2a9d,0xb421},
{0x2a9e,0x2a9e,0xb42a},
{0x2a9f,0x2aa6,0xb42c},
{0x2aa7,0x2ac1,0xb435},
{0x2ac2,0x2ac3,0xb452},
{0x2ac4,0x2ac6,0xb455},
{0x2ac7,0x2acd,0xb459},
{0x2ace,0x2ace,0xb462},
{0x2acf,0x2acf,0xb464},
{0x2ad0,0x2ad5,0xb466},
{0x2ad6,0x2ae8,0xb46d},
{0x2ae9,0x2aef,0xb481},
{0x2af0,0x2b03,0xb489},
{0x2b04,0x2b09,0xb49e},
{0x2b0a,0x2b0c,0xb4a5},
{0x2b0d,0x2b0f,0xb4a9},
{0x2b10,0x2b17,0xb4ad},
{0x2b18,0x2b18,0xb4b6},
{0x2b19,0x2b19,0xb4b8},
{0x2b1a,0x2b1f,0xb4ba},
{0x2b20,0x2b22,0xb4c1},
{0x2b23,0x2b25,0xb4c5},
{0x2b26,0x2b2c,0xb4c9},
{0x2b2d,0x2b30,0xb4d1},
{0x2b31,0x2b36,0xb4d6},
{0x2b37,0x2b38,0xb4de},
{0x2b39,0x2b3a,0xb4e1},
{0x2b3b,0x2b3b,0xb4e5},
{0x2b3c,0x2b40,0xb4e7},
{0x2b41,0x2b41,0xb4ee},
{0x2b42,0x2b42,0xb4f0},
{0x2b43,0x2b48,0xb4f2},
{0x2b49,0x2b63,0xb4f9},
{0x2b64,0x2b65,0xb516},
{0x2b66,0x2b67,0xb519},
{0x2b68,0x2b6e,0xb51d},
{0x2b6f,0x2b6f,0xb526},
{0x2b70,0x2b74,0xb52b},
{0x2b75,0x2b76,0xb532},
{0x2b77,0x2b79,0xb535},
{0x2b7a,0x2b80,0xb539},
{0x2b81,0x2b81,0xb542},
{0x2b82,0x2b86,0xb546},
{0x2b87,0x2b88,0xb54e},
{0x2b89,0x2b8b,0xb551},
{0x2b8c,0x2b92,0xb555},
{0x2b93,0x2b93,0xb55e},
{0x2b94,0x2bd1,0xb562},
{0x2bd2,0x2bd3,0xb5a2},
{0x2bd4,0x2bd6,0xb5a5},
{0x2bd7,0x2bd7,0xb5a9},
{0x2bd8,0x2bdb,0xb5ac},
{0x2bdc,0x2bdc,0xb5b2},
{0x2bdd,0x2be1,0xb5b6},
{0x2be2,0x2be3,0xb5be},
{0x2be4,0x2be6,0xb5c1},
{0x2be7,0x2bed,0xb5c5},
{0x2bee,0x2bee,0xb5ce},
{0x2bef,0x2bf4,0xb5d2},
{0x2bf5,0x2c07,0xb5d9},
{0x2c08,0x2c2a,0xb5ed},
{0x2c2b,0x2c2c,0xb612},
{0x2c2d,0x2c2f,0xb615},
{0x2c30,0x2c3b,0xb619},
{0x2c3c,0x2c41,0xb626},
{0x2c42,0x2c48,0xb62d},
{0x2c49,0x2c5b,0xb635},
{0x2c5c,0x2c76,0xb649},
{0x2c77,0x2c79,0xb665},
{0x2c7a,0x2cac,0xb669},
{0x2cad,0x2cae,0xb69e},
{0x2caf,0x2cb1,0xb6a1},
{0x2cb2,0x2cb7,0xb6a5},
{0x2cb8,0x2cbb,0xb6ad},
{0x2cbc,0x2cdd,0xb6b2},
{0x2cde,0x2cf8,0xb6d5},
{0x2cf9,0x2cfb,0xb6f1},
{0x2cfc,0x2cfe,0xb6f5},
{0x2cff,0x2d05,0xb6f9},
{0x2d06,0x2d08,0xb702},
{0x2d09,0x2d2a,0xb706},
{0x2d2b,0x2d2c,0xb72a},
{0x2d2d,0x2d2e,0xb72d},
{0x2d2f,0x2d35,0xb731},
{0x2d36,0x2d36,0xb73a},
{0x2d37,0x2d3e,0xb73c},
{0x2d3f,0x2d41,0xb745},
{0x2d42,0x2d44,0xb749},
{0x2d45,0x2d4b,0xb74d},
{0x2d4c,0x2d55,0xb756},
{0x2d56,0x2d58,0xb761},
{0x2d59,0x2d5b,0xb765},
{0x2d5c,0x2d62,0xb769},
{0x2d63,0x2d63,0xb772},
{0x2d64,0x2d64,0xb774},
{0x2d65,0x2d6a,0xb776},
{0x2d6b,0x2d6c,0xb77e},
{0x2d6d,0x2d6f,0xb781},
{0x2d70,0x2d76,0xb785},
{0x2d77,0x2d77,0xb78e},
{0x2d78,0x2d7a,0xb793},
{0x2d7b,0x2d7c,0xb79a},
{0x2d7d,0x2d7f,0xb79d},
{0x2d80,0x2d86,0xb7a1},
{0x2d87,0x2d87,0xb7aa},
{0x2d88,0x2d8d,0xb7ae},
{0x2d8e,0x2d8f,0xb7b6},
{0x2d90,0x2d9d,0xb7b9},
{0x2d9e,0x2d9e,0xb7c8},
{0x2d9f,0x2dc0,0xb7ca},
{0x2dc1,0x2dc2,0xb7ee},
{0x2dc3,0x2dc5,0xb7f1},
{0x2dc6,0x2dcc,0xb7f5},
{0x2dcd,0x2dcd,0xb7fe},
{0x2dce,0x2dd2,0xb802},
{0x2dd3,0x2dd4,0xb80a},
{0x2dd5,0x2dd7,0xb80d},
{0x2dd8,0x2dde,0xb811},
{0x2ddf,0x2ddf,0xb81a},
{0x2de0,0x2de0,0xb81c},
{0x2de1,0x2de6,0xb81e},
{0x2de7,0x2de8,0xb826},
{0x2de9,0x2deb,0xb829},
{0x2dec,0x2df2,0xb82d},
{0x2df3,0x2df3,0xb836},
{0x2df4,0x2df9,0xb83a},
{0x2dfa,0x2dfc,0xb841},
{0x2dfd,0x2e08,0xb845},
{0x2e09,0x2e09,0xb852},
{0x2e0a,0x2e11,0xb854},
{0x2e12,0x2e13,0xb85e},
{0x2e14,0x2e16,0xb861},
{0x2e17,0x2e1d,0xb865},
{0x2e1e,0x2e1e,0xb86e},
{0x2e1f,0x2e1f,0xb870},
{0x2e20,0x2e25,0xb872},
{0x2e26,0x2e28,0xb879},
{0x2e29,0x2e38,0xb87d},
{0x2e39,0x2e52,0xb88e},
{0x2e53,0x2e59,0xb8a9},
{0x2e5a,0x2e5c,0xb8b1},
{0x2e5d,0x2e5f,0xb8b5},
{0x2e60,0x2e66,0xb8b9},
{0x2e67,0x2e67,0xb8c2},
{0x2e68,0x2e68,0xb8c4},
{0x2e69,0x2e6e,0xb8c6},
{0x2e6f,0x2e71,0xb8cd},
{0x2e72,0x2e74,0xb8d1},
{0x2e75,0x2e7c,0xb8d5},
{0x2e7d,0x2e7d,0xb8de},
{0x2e7e,0x2e7e,0xb8e0},
{0x2e7f,0x2e84,0xb8e2},
{0x2e85,0x2e86,0xb8ea},
{0x2e87,0x2e89,0xb8ed},
{0x2e8a,0x2e90,0xb8f1},
{0x2e91,0x2e91,0xb8fa},
{0x2e92,0x2e92,0xb8fc},
{0x2e93,0x2e98,0xb8fe},
{0x2e99,0x2eab,0xb905},
{0x2eac,0x2eb2,0xb919},
{0x2eb3,0x2ecd,0xb921},
{0x2ece,0x2ecf,0xb93e},
{0x2ed0,0x2ed2,0xb941},
{0x2ed3,0x2ed9,0xb945},
{0x2eda,0x2edb,0xb94d},
{0x2edc,0x2edc,0xb950},
{0x2edd,0x2ee2,0xb952},
{0x2ee3,0x2ee4,0xb95a},
{0x2ee5,0x2ee7,0xb95d},
{0x2ee8,0x2eee,0xb961},
{0x2eef,0x2eef,0xb96a},
{0x2ef0,0x2ef0,0xb96c},
{0x2ef1,0x2ef6,0xb96e},
{0x2ef7,0x2ef8,0xb976},
{0x2ef9,0x2efb,0xb979},
{0x2efc,0x2f02,0xb97d},
{0x2f03,0x2f03,0xb986},
{0x2f04,0x2f04,0xb988},
{0x2f05,0x2f06,0xb98b},
{0x2f07,0x2f23,0xb98f},
{0x2f24,0x2f25,0xb9ae},
{0x2f26,0x2f28,0xb9b1},
{0x2f29,0x2f2f,0xb9b5},
{0x2f30,0x2f30,0xb9be},
{0x2f31,0x2f31,0xb9c0},
{0x2f32,0x2f37,0xb9c2},
{0x2f38,0x2f39,0xb9ca},
{0x2f3a,0x2f3a,0xb9cd},
{0x2f3b,0x2f3f,0xb9d3},
{0x2f40,0x2f40,0xb9da},
{0x2f41,0x2f41,0xb9dc},
{0x2f42,0x2f43,0xb9df},
{0x2f44,0x2f44,0xb9e2},
{0x2f45,0x2f46,0xb9e6},
{0x2f47,0x2f49,0xb9e9},
{0x2f4a,0x2f50,0xb9ed},
{0x2f51,0x2f51,0xb9f6},
{0x2f52,0x2f56,0xb9fb},
{0x2f57,0x2f5c,0xba02},
{0x2f5d,0x2f68,0xba09},
{0x2f69,0x2f8a,0xba16},
{0x2f8b,0x2f8c,0xba3a},
{0x2f8d,0x2f8f,0xba3d},
{0x2f90,0x2f90,0xba41},
{0x2f91,0x2f95,0xba43},
{0x2f96,0x2f96,0xba4a},
{0x2f97,0x2f97,0xba4c},
{0x2f98,0x2f9b,0xba4f},
{0x2f9c,0x2f9d,0xba56},
{0x2f9e,0x2fa0,0xba59},
{0x2fa1,0x2fa7,0xba5d},
{0x2fa8,0x2fa8,0xba66},
{0x2fa9,0x2fae,0xba6a},
{0x2faf,0x2fb0,0xba72},
{0x2fb1,0x2fb3,0xba75},
{0x2fb4,0x2fbd,0xba79},
{0x2fbe,0x2fbe,0xba86},
{0x2fbf,0x2fc2,0xba88},
{0x2fc3,0x2fdd,0xba8d},
{0x2fde,0x2fde,0xbaaa},
{0x2fdf,0x2fe1,0xbaad},
{0x2fe2,0x2fe2,0xbab1},
{0x2fe3,0x2fe7,0xbab3},
{0x2fe8,0x2fe8,0xbaba},
{0x2fe9,0x2fe9,0xbabc},
{0x2fea,0x2fef,0xbabe},
{0x2ff0,0x2ff2,0xbac5},
{0x2ff3,0x3001,0xbac9},
{0x3002,0x3023,0xbada},
{0x3024,0x3026,0xbafd},
{0x3027,0x3029,0xbb01},
{0x302a,0x3031,0xbb05},
{0x3032,0x3032,0xbb0e},
{0x3033,0x3033,0xbb10},
{0x3034,0x3039,0xbb12},
{0x303a,0x303c,0xbb19},
{0x303d,0x303f,0xbb1d},
{0x3040,0x3047,0xbb21},
{0x3048,0x3048,0xbb2a},
{0x3049,0x3050,0xbb2c},
{0x3051,0x3051,0xbb37},
{0x3052,0x3053,0xbb39},
{0x3054,0x3058,0xbb3f},
{0x3059,0x3059,0xbb46},
{0x305a,0x305a,0xbb48},
{0x305b,0x305d,0xbb4a},
{0x305e,0x305e,0xbb4e},
{0x305f,0x3061,0xbb51},
{0x3062,0x3064,0xbb55},
{0x3065,0x306c,0xbb59},
{0x306d,0x306d,0xbb62},
{0x306e,0x3075,0xbb64},
{0x3076,0x3090,0xbb6d},
{0x3091,0x3093,0xbb89},
{0x3094,0x3096,0xbb8d},
{0x3097,0x30a9,0xbb91},
{0x30aa,0x30ac,0xbba5},
{0x30ad,0x30af,0xbba9},
{0x30b0,0x30b6,0xbbad},
{0x30b7,0x30b8,0xbbb5},
{0x30b9,0x30c0,0xbbb8},
{0x30c1,0x30c3,0xbbc1},
{0x30c4,0x30c6,0xbbc5},
{0x30c7,0x30cd,0xbbc9},
{0x30ce,0x30cf,0xbbd1},
{0x30d0,0x30f3,0xbbd4},
{0x30f4,0x30f5,0xbbfa},
{0x30f6,0x30f7,0xbbfd},
{0x30f8,0x30f8,0xbc01},
{0x30f9,0x30fd,0xbc03},
{0x30fe,0x30fe,0xbc0a},
{0x30ff,0x30ff,0xbc0e},
{0x3100,0x3100,0xbc10},
{0x3101,0x3102,0xbc12},
{0x3103,0x3104,0xbc19},
{0x3105,0x3108,0xbc20},
{0x3109,0x3109,0xbc26},
{0x310a,0x310a,0xbc28},
{0x310b,0x310d,0xbc2a},
{0x310e,0x310f,0xbc2e},
{0x3110,0x3111,0xbc32},
{0x3112,0x3114,0xbc35},
{0x3115,0x311b,0xbc39},
{0x311c,0x311c,0xbc42},
{0x311d,0x311f,0xbc46},
{0x3120,0x3121,0xbc4a},
{0x3122,0x3123,0xbc4e},
{0x3124,0x312f,0xbc51},
{0x3130,0x3155,0xbc5e},
{0x3156,0x3157,0xbc86},
{0x3158,0x3159,0xbc89},
{0x315a,0x315a,0xbc8d},
{0x315b,0x315f,0xbc8f},
{0x3160,0x3160,0xbc96},
{0x3161,0x3161,0xbc98},
{0x3162,0x3166,0xbc9b},
{0x3167,0x3168,0xbca2},
{0x3169,0x316a,0xbca5},
{0x316b,0x3171,0xbca9},
{0x3172,0x3172,0xbcb2},
{0x3173,0x3178,0xbcb6},
{0x3179,0x317a,0xbcbe},
{0x317b,0x317d,0xbcc1},
{0x317e,0x3185,0xbcc5},
{0x3186,0x3186,0xbcce},
{0x3187,0x3189,0xbcd2},
{0x318a,0x318b,0xbcd6},
{0x318c,0x318e,0xbcd9},
{0x318f,0x31a5,0xbcdd},
{0x31a6,0x31a6,0xbcf7},
{0x31a7,0x31a9,0xbcf9},
{0x31aa,0x31b0,0xbcfd},
{0x31b1,0x31b1,0xbd06},
{0x31b2,0x31b2,0xbd08},
{0x31b3,0x31b8,0xbd0a},
{0x31b9,0x31bb,0xbd11},
{0x31bc,0x31ca,0xbd15},
{0x31cb,0x31d1,0xbd25},
{0x31d2,0x31e4,0xbd2d},
{0x31e5,0x31eb,0xbd41},
{0x31ec,0x31ed,0xbd4a},
{0x31ee,0x31f0,0xbd4d},
{0x31f1,0x31f7,0xbd51},
{0x31f8,0x3201,0xbd5a},
{0x3202,0x3204,0xbd65},
{0x3205,0x321b,0xbd69},
{0x321c,0x321d,0xbd82},
{0x321e,0x321f,0xbd85},
{0x3220,0x3224,0xbd8b},
{0x3225,0x3225,0xbd92},
{0x3226,0x3226,0xbd94},
{0x3227,0x3229,0xbd96},
{0x322a,0x322a,0xbd9b},
{0x322b,0x3231,0xbd9d},
{0x3232,0x323c,0xbda5},
{0x323d,0x3243,0xbdb1},
{0x3244,0x325e,0xbdb9},
{0x325f,0x3260,0xbdd6},
{0x3261,0x3263,0xbdd9},
{0x3264,0x326f,0xbddd},
{0x3270,0x3275,0xbdea},
{0x3276,0x3278,0xbdf1},
{0x3279,0x327b,0xbdf5},
{0x327c,0x3282,0xbdf9},
{0x3283,0x3284,0xbe01},
{0x3285,0x3285,0xbe04},
{0x3286,0x328b,0xbe06},
{0x328c,0x328d,0xbe0e},
{0x328e,0x3290,0xbe11},
{0x3291,0x3297,0xbe15},
{0x3298,0x3298,0xbe1e},
{0x3299,0x32bc,0xbe20},
{0x32bd,0x32be,0xbe46},
{0x32bf,0x32c1,0xbe49},
{0x32c2,0x32c2,0xbe4d},
{0x32c3,0x32c7,0xbe4f},
{0x32c8,0x32c8,0xbe56},
{0x32c9,0x32c9,0xbe58},
{0x32ca,0x32cd,0xbe5c},
{0x32ce,0x32cf,0xbe62},
{0x32d0,0x32d2,0xbe65},
{0x32d3,0x32d3,0xbe69},
{0x32d4,0x32d8,0xbe6b},
{0x32d9,0x32d9,0xbe72},
{0x32da,0x32de,0xbe76},
{0x32df,0x32e0,0xbe7e},
{0x32e1,0x32e3,0xbe81},
{0x32e4,0x32ea,0xbe85},
{0x32eb,0x32eb,0xbe8e},
{0x32ec,0x32f1,0xbe92},
{0x32f2,0x32ff,0xbe9a},
{0x3300,0x3326,0xbea9},
{0x3327,0x3328,0xbed2},
{0x3329,0x332a,0xbed5},
{0x332b,0x3331,0xbed9},
{0x3332,0x3333,0xbee1},
{0x3334,0x3339,0xbee6},
{0x333a,0x334d,0xbeed},
{0x334e,0x3353,0xbf02},
{0x3354,0x3361,0xbf0a},
{0x3362,0x3362,0xbf1a},
{0x3363,0x3384,0xbf1e},
{0x3385,0x3386,0xbf42},
{0x3387,0x3389,0xbf45},
{0x338a,0x3390,0xbf49},
{0x3391,0x3393,0xbf52},
{0x3394,0x33d1,0xbf56},
{0x33d2,0x33ec,0xbf95},
{0x33ed,0x3400,0xbfb1},
{0x3401,0x3406,0xbfc6},
{0x3407,0x3408,0xbfce},
{0x3409,0x340b,0xbfd1},
{0x340c,0x3412,0xbfd5},
{0x3413,0x3414,0xbfdd},
{0x3415,0x3415,0xbfe0},
{0x3416,0x346f,0xbfe2},
{0x3470,0x3483,0xc03d},
{0x3484,0x3489,0xc052},
{0x348a,0x348c,0xc059},
{0x348d,0x348f,0xc05d},
{0x3490,0x3496,0xc061},
{0x3497,0x34bc,0xc06a},
{0x34bd,0x34be,0xc092},
{0x34bf,0x34c1,0xc095},
{0x34c2,0x34c8,0xc099},
{0x34c9,0x34c9,0xc0a2},
{0x34ca,0x34ca,0xc0a4},
{0x34cb,0x34d0,0xc0a6},
{0x34d1,0x34d1,0xc0ae},
{0x34d2,0x34d3,0xc0b1},
{0x34d4,0x34d8,0xc0b7},
{0x34d9,0x34d9,0xc0be},
{0x34da,0x34dc,0xc0c2},
{0x34dd,0x34de,0xc0c6},
{0x34df,0x34e0,0xc0ca},
{0x34e1,0x34e3,0xc0cd},
{0x34e4,0x34ea,0xc0d1},
{0x34eb,0x34eb,0xc0da},
{0x34ec,0x34f1,0xc0de},
{0x34f2,0x34f3,0xc0e6},
{0x34f4,0x34f6,0xc0e9},
{0x34f7,0x34fd,0xc0ed},
{0x34fe,0x34fe,0xc0f6},
{0x34ff,0x34ff,0xc0f8},
{0x3500,0x3505,0xc0fa},
{0x3506,0x3508,0xc101},
{0x3509,0x350b,0xc105},
{0x350c,0x3512,0xc109},
{0x3513,0x3516,0xc111},
{0x3517,0x351c,0xc116},
{0x351d,0x351e,0xc121},
{0x351f,0x351f,0xc125},
{0x3520,0x3523,0xc128},
{0x3524,0x3524,0xc12e},
{0x3525,0x3528,0xc132},
{0x3529,0x3529,0xc137},
{0x352a,0x352b,0xc13a},
{0x352c,0x352e,0xc13d},
{0x352f,0x3535,0xc141},
{0x3536,0x3536,0xc14a},
{0x3537,0x353c,0xc14e},
{0x353d,0x353e,0xc156},
{0x353f,0x3541,0xc159},
{0x3542,0x3548,0xc15d},
{0x3549,0x3549,0xc166},
{0x354a,0x354f,0xc16a},
{0x3550,0x3552,0xc171},
{0x3553,0x3555,0xc175},
{0x3556,0x3561,0xc179},
{0x3562,0x3567,0xc186},
{0x3568,0x3568,0xc18f},
{0x3569,0x356b,0xc191},
{0x356c,0x356c,0xc195},
{0x356d,0x3571,0xc197},
{0x3572,0x3572,0xc19e},
{0x3573,0x3573,0xc1a0},
{0x3574,0x3576,0xc1a2},
{0x3577,0x3578,0xc1a6},
{0x3579,0x357a,0xc1aa},
{0x357b,0x357d,0xc1ad},
{0x357e,0x3589,0xc1b1},
{0x358a,0x358f,0xc1be},
{0x3590,0x3592,0xc1c5},
{0x3593,0x3595,0xc1c9},
{0x3596,0x359c,0xc1cd},
{0x359d,0x359e,0xc1d5},
{0x359f,0x35a5,0xc1d9},
{0x35a6,0x35a8,0xc1e1},
{0x35a9,0x35ab,0xc1e5},
{0x35ac,0x35b2,0xc1e9},
{0x35b3,0x35b3,0xc1f2},
{0x35b4,0x35bb,0xc1f4},
{0x35bc,0x35bd,0xc1fe},
{0x35be,0x35c0,0xc201},
{0x35c1,0x35c7,0xc205},
{0x35c8,0x35c8,0xc20e},
{0x35c9,0x35c9,0xc210},
{0x35ca,0x35cf,0xc212},
{0x35d0,0x35d1,0xc21a},
{0x35d2,0x35d3,0xc21d},
{0x35d4,0x35da,0xc221},
{0x35db,0x35db,0xc22a},
{0x35dc,0x35dc,0xc22c},
{0x35dd,0x35dd,0xc22e},
{0x35de,0x35de,0xc230},
{0x35df,0x35df,0xc233},
{0x35e0,0x35f2,0xc235},
{0x35f3,0x35f9,0xc249},
{0x35fa,0x35fb,0xc252},
{0x35fc,0x35fe,0xc255},
{0x35ff,0x3605,0xc259},
{0x3606,0x3609,0xc261},
{0x360a,0x360f,0xc266},
{0x3610,0x3611,0xc26e},
{0x3612,0x3614,0xc271},
{0x3615,0x361b,0xc275},
{0x361c,0x361c,0xc27e},
{0x361d,0x361d,0xc280},
{0x361e,0x3623,0xc282},
{0x3624,0x3629,0xc28a},
{0x362a,0x3630,0xc291},
{0x3631,0x3632,0xc299},
{0x3633,0x3633,0xc29c},
{0x3634,0x3639,0xc29e},
{0x363a,0x363b,0xc2a6},
{0x363c,0x363e,0xc2a9},
{0x363f,0x3644,0xc2ae},
{0x3645,0x3645,0xc2b6},
{0x3646,0x3646,0xc2b8},
{0x3647,0x3668,0xc2ba},
{0x3669,0x366a,0xc2de},
{0x366b,0x366c,0xc2e1},
{0x366d,0x3672,0xc2e5},
{0x3673,0x3673,0xc2ee},
{0x3674,0x3674,0xc2f0},
{0x3675,0x3678,0xc2f2},
{0x3679,0x3679,0xc2f7},
{0x367a,0x367a,0xc2fa},
{0x367b,0x367d,0xc2fd},
{0x367e,0x3684,0xc301},
{0x3685,0x3686,0xc30a},
{0x3687,0x368b,0xc30e},
{0x368c,0x368d,0xc316},
{0x368e,0x3690,0xc319},
{0x3691,0x3697,0xc31d},
{0x3698,0x3699,0xc326},
{0x369a,0x36b4,0xc32a},
{0x36b5,0x36d6,0xc346},
{0x36d7,0x36d8,0xc36a},
{0x36d9,0x36db,0xc36d},
{0x36dc,0x36dc,0xc371},
{0x36dd,0x36e1,0xc373},
{0x36e2,0x36e3,0xc37a},
{0x36e4,0x36e9,0xc37e},
{0x36ea,0x36ec,0xc385},
{0x36ed,0x36ef,0xc389},
{0x36f0,0x3722,0xc38d},
{0x3723,0x3739,0xc3c1},
{0x373a,0x373b,0xc3da},
{0x373c,0x373d,0xc3dd},
{0x373e,0x373e,0xc3e1},
{0x373f,0x3743,0xc3e3},
{0x3744,0x3746,0xc3ea},
{0x3747,0x374c,0xc3ee},
{0x374d,0x374e,0xc3f6},
{0x374f,0x375d,0xc3f9},
{0x375e,0x3764,0xc409},
{0x3765,0x3777,0xc411},
{0x3778,0x377e,0xc425},
{0x377f,0x3781,0xc42d},
{0x3782,0x3784,0xc431},
{0x3785,0x378b,0xc435},
{0x378c,0x3795,0xc43e},
{0x3796,0x37b0,0xc449},
{0x37b1,0x37b2,0xc466},
{0x37b3,0x37b5,0xc469},
{0x37b6,0x37bc,0xc46d},
{0x37bd,0x37bf,0xc476},
{0x37c0,0x37c5,0xc47a},
{0x37c6,0x37d8,0xc481},
{0x37d9,0x37df,0xc495},
{0x37e0,0x37fa,0xc49d},
{0x37fb,0x37fd,0xc4b9},
{0x37fe,0x3829,0xc4bd},
{0x382a,0x382f,0xc4ea},
{0x3830,0x3831,0xc4f2},
{0x3832,0x3834,0xc4f5},
{0x3835,0x3835,0xc4f9},
{0x3836,0x3839,0xc4fb},
{0x383a,0x3843,0xc502},
{0x3844,0x3846,0xc50d},
{0x3847,0x3849,0xc511},
{0x384a,0x3850,0xc515},
{0x3851,0x385b,0xc51d},
{0x385c,0x385d,0xc52a},
{0x385e,0x3860,0xc52d},
{0x3861,0x3867,0xc531},
{0x3868,0x3868,0xc53a},
{0x3869,0x3869,0xc53c},
{0x386a,0x386f,0xc53e},
{0x3870,0x3871,0xc546},
{0x3872,0x3872,0xc54b},
{0x3873,0x3876,0xc54f},
{0x3877,0x3877,0xc556},
{0x3878,0x387a,0xc55a},
{0x387b,0x387b,0xc55f},
{0x387c,0x387d,0xc562},
{0x387e,0x3880,0xc565},
{0x3881,0x3887,0xc569},
{0x3888,0x3888,0xc572},
{0x3889,0x388e,0xc576},
{0x388f,0x3890,0xc57e},
{0x3891,0x3893,0xc581},
{0x3894,0x3895,0xc585},
{0x3896,0x3899,0xc588},
{0x389a,0x389a,0xc58e},
{0x389b,0x389b,0xc590},
{0x389c,0x389e,0xc592},
{0x389f,0x389f,0xc596},
{0x38a0,0x38a2,0xc599},
{0x38a3,0x38a5,0xc59d},
{0x38a6,0x38ad,0xc5a1},
{0x38ae,0x38b7,0xc5aa},
{0x38b8,0x38b9,0xc5b6},
{0x38ba,0x38ba,0xc5ba},
{0x38bb,0x38bf,0xc5bf},
{0x38c0,0x38c0,0xc5cb},
{0x38c1,0x38c1,0xc5cd},
{0x38c2,0x38c2,0xc5cf},
{0x38c3,0x38c4,0xc5d2},
{0x38c5,0x38c7,0xc5d5},
{0x38c8,0x38ce,0xc5d9},
{0x38cf,0x38cf,0xc5e2},
{0x38d0,0x38d0,0xc5e4},
{0x38d1,0x38d6,0xc5e6},
{0x38d7,0x38d7,0xc5ef},
{0x38d8,0x38da,0xc5f1},
{0x38db,0x38db,0xc5f5},
{0x38dc,0x38df,0xc5f8},
{0x38e0,0x38e2,0xc602},
{0x38e3,0x38e5,0xc609},
{0x38e6,0x38e8,0xc60d},
{0x38e9,0x38ef,0xc611},
{0x38f0,0x38f0,0xc61a},
{0x38f1,0x38f7,0xc61d},
{0x38f8,0x38f9,0xc626},
{0x38fa,0x38fc,0xc629},
{0x38fd,0x38fd,0xc62f},
{0x38fe,0x38ff,0xc631},
{0x3900,0x3900,0xc636},
{0x3901,0x3901,0xc638},
{0x3902,0x3902,0xc63a},
{0x3903,0x3906,0xc63c},
{0x3907,0x3908,0xc642},
{0x3909,0x390b,0xc645},
{0x390c,0x3912,0xc649},
{0x3913,0x3913,0xc652},
{0x3914,0x3919,0xc656},
{0x391a,0x391b,0xc65e},
{0x391c,0x3926,0xc661},
{0x3927,0x3928,0xc66d},
{0x3929,0x3929,0xc670},
{0x392a,0x392f,0xc672},
{0x3930,0x3931,0xc67a},
{0x3932,0x3934,0xc67d},
{0x3935,0x393b,0xc681},
{0x393c,0x393c,0xc68a},
{0x393d,0x393d,0xc68c},
{0x393e,0x3943,0xc68e},
{0x3944,0x3945,0xc696},
{0x3946,0x3948,0xc699},
{0x3949,0x394f,0xc69d},
{0x3950,0x3950,0xc6a6},
{0x3951,0x3951,0xc6a8},
{0x3952,0x3957,0xc6aa},
{0x3958,0x3959,0xc6b2},
{0x395a,0x395c,0xc6b5},
{0x395d,0x3961,0xc6bb},
{0x3962,0x3962,0xc6c2},
{0x3963,0x3963,0xc6c4},
{0x3964,0x3969,0xc6c6},
{0x396a,0x396b,0xc6ce},
{0x396c,0x396e,0xc6d1},
{0x396f,0x3975,0xc6d5},
{0x3976,0x3977,0xc6de},
{0x3978,0x397d,0xc6e2},
{0x397e,0x397f,0xc6ea},
{0x3980,0x3982,0xc6ed},
{0x3983,0x3989,0xc6f1},
{0x398a,0x398c,0xc6fa},
{0x398d,0x3992,0xc6fe},
{0x3993,0x3994,0xc706},
{0x3995,0x3997,0xc709},
{0x3998,0x399e,0xc70d},
{0x399f,0x399f,0xc716},
{0x39a0,0x39a0,0xc718},
{0x39a1,0x39a6,0xc71a},
{0x39a7,0x39a8,0xc722},
{0x39a9,0x39ab,0xc725},
{0x39ac,0x39b2,0xc729},
{0x39b3,0x39b3,0xc732},
{0x39b4,0x39b4,0xc734},
{0x39b5,0x39b5,0xc736},
{0x39b6,0x39b9,0xc738},
{0x39ba,0x39bb,0xc73e},
{0x39bc,0x39be,0xc741},
{0x39bf,0x39c3,0xc745},
{0x39c4,0x39c4,0xc74b},
{0x39c5,0x39c5,0xc74e},
{0x39c6,0x39c6,0xc750},
{0x39c7,0x39c9,0xc759},
{0x39ca,0x39cc,0xc75d},
{0x39cd,0x39d3,0xc761},
{0x39d4,0x39d5,0xc769},
{0x39d6,0x39dd,0xc76c},
{0x39de,0x39df,0xc776},
{0x39e0,0x39e2,0xc779},
{0x39e3,0x39e6,0xc77f},
{0x39e7,0x39e7,0xc786},
{0x39e8,0x39ea,0xc78b},
{0x39eb,0x39eb,0xc78f},
{0x39ec,0x39ed,0xc792},
{0x39ee,0x39ee,0xc795},
{0x39ef,0x39ef,0xc799},
{0x39f0,0x39f4,0xc79b},
{0x39f5,0x39f5,0xc7a2},
{0x39f6,0x39fa,0xc7a7},
{0x39fb,0x39fc,0xc7ae},
{0x39fd,0x39ff,0xc7b1},
{0x3a00,0x3a06,0xc7b5},
{0x3a07,0x3a07,0xc7be},
{0x3a08,0x3a0d,0xc7c2},
{0x3a0e,0x3a0f,0xc7ca},
{0x3a10,0x3a10,0xc7cd},
{0x3a11,0x3a11,0xc7cf},
{0x3a12,0x3a18,0xc7d1},
{0x3a19,0x3a1c,0xc7d9},
{0x3a1d,0x3a22,0xc7de},
{0x3a23,0x3a25,0xc7e5},
{0x3a26,0x3a28,0xc7e9},
{0x3a29,0x3a3b,0xc7ed},
{0x3a3c,0x3a3d,0xc802},
{0x3a3e,0x3a40,0xc805},
{0x3a41,0x3a41,0xc809},
{0x3a42,0x3a46,0xc80b},
{0x3a47,0x3a47,0xc812},
{0x3a48,0x3a48,0xc814},
{0x3a49,0x3a4d,0xc817},
{0x3a4e,0x3a4f,0xc81e},
{0x3a50,0x3a52,0xc821},
{0x3a53,0x3a59,0xc825},
{0x3a5a,0x3a5a,0xc82e},
{0x3a5b,0x3a5b,0xc830},
{0x3a5c,0x3a61,0xc832},
{0x3a62,0x3a64,0xc839},
{0x3a65,0x3a67,0xc83d},
{0x3a68,0x3a6e,0xc841},
{0x3a6f,0x3a70,0xc84a},
{0x3a71,0x3a76,0xc84e},
{0x3a77,0x3a91,0xc855},
{0x3a92,0x3a93,0xc872},
{0x3a94,0x3a96,0xc875},
{0x3a97,0x3a97,0xc879},
{0x3a98,0x3a9c,0xc87b},
{0x3a9d,0x3a9d,0xc882},
{0x3a9e,0x3a9e,0xc884},
{0x3a9f,0x3aa1,0xc888},
{0x3aa2,0x3aa7,0xc88e},
{0x3aa8,0x3aaf,0xc895},
{0x3ab0,0x3ab0,0xc89e},
{0x3ab1,0x3ab1,0xc8a0},
{0x3ab2,0x3ab7,0xc8a2},
{0x3ab8,0x3aca,0xc8a9},
{0x3acb,0x3ad0,0xc8be},
{0x3ad1,0x3ad3,0xc8c5},
{0x3ad4,0x3ad6,0xc8c9},
{0x3ad7,0x3add,0xc8cd},
{0x3ade,0x3ade,0xc8d6},
{0x3adf,0x3adf,0xc8d8},
{0x3ae0,0x3ae5,0xc8da},
{0x3ae6,0x3ae7,0xc8e2},
{0x3ae8,0x3af7,0xc8e5},
{0x3af8,0x3afd,0xc8f6},
{0x3afe,0x3aff,0xc8fe},
{0x3b00,0x3b02,0xc901},
{0x3b03,0x3b07,0xc907},
{0x3b08,0x3b08,0xc90e},
{0x3b09,0x3b09,0xc910},
{0x3b0a,0x3b0f,0xc912},
{0x3b10,0x3b22,0xc919},
{0x3b23,0x3b29,0xc92d},
{0x3b2a,0x3b44,0xc935},
{0x3b45,0x3b46,0xc952},
{0x3b47,0x3b49,0xc955},
{0x3b4a,0x3b50,0xc959},
{0x3b51,0x3b51,0xc962},
{0x3b52,0x3b59,0xc964},
{0x3b5a,0x3b5c,0xc96d},
{0x3b5d,0x3b5f,0xc971},
{0x3b60,0x3b66,0xc975},
{0x3b67,0x3b71,0xc97d},
{0x3b72,0x3b73,0xc98a},
{0x3b74,0x3b76,0xc98d},
{0x3b77,0x3b7d,0xc991},
{0x3b7e,0x3b7e,0xc99a},
{0x3b7f,0x3b7f,0xc99c},
{0x3b80,0x3ba1,0xc99e},
{0x3ba2,0x3ba3,0xc9c2},
{0x3ba4,0x3ba5,0xc9c5},
{0x3ba6,0x3ba6,0xc9c9},
{0x3ba7,0x3bab,0xc9cb},
{0x3bac,0x3bac,0xc9d2},
{0x3bad,0x3bad,0xc9d4},
{0x3bae,0x3baf,0xc9d7},
{0x3bb0,0x3bb0,0xc9db},
{0x3bb1,0x3bb2,0xc9de},
{0x3bb3,0x3bb3,0xc9e1},
{0x3bb4,0x3bb4,0xc9e3},
{0x3bb5,0x3bb6,0xc9e5},
{0x3bb7,0x3bba,0xc9e8},
{0x3bbb,0x3bbb,0xc9ee},
{0x3bbc,0x3bc1,0xc9f2},
{0x3bc2,0x3bc3,0xc9fa},
{0x3bc4,0x3bc6,0xc9fd},
{0x3bc7,0x3bcd,0xca01},
{0x3bce,0x3bce,0xca0a},
{0x3bcf,0x3bd4,0xca0e},
{0x3bd5,0x3bd7,0xca15},
{0x3bd8,0x3be7,0xca19},
{0x3be8,0x3c09,0xca2a},
{0x3c0a,0x3c0b,0xca4e},
{0x3c0c,0x3c0e,0xca51},
{0x3c0f,0x3c15,0xca55},
{0x3c16,0x3c16,0xca5e},
{0x3c17,0x3c1c,0xca62},
{0x3c1d,0x3c30,0xca69},
{0x3c31,0x3c36,0xca7e},
{0x3c37,0x3c49,0xca85},
{0x3c4a,0x3c6c,0xca99},
{0x3c6d,0x3c6e,0xcabe},
{0x3c6f,0x3c71,0xcac1},
{0x3c72,0x3c78,0xcac5},
{0x3c79,0x3c79,0xcace},
{0x3c7a,0x3c7a,0xcad0},
{0x3c7b,0x3c7b,0xcad2},
{0x3c7c,0x3c7f,0xcad4},
{0x3c80,0x3c85,0xcada},
{0x3c86,0x3c90,0xcae1},
{0x3c91,0x3c97,0xcaed},
{0x3c98,0x3caa,0xcaf5},
{0x3cab,0x3cb1,0xcb09},
{0x3cb2,0x3cb4,0xcb11},
{0x3cb5,0x3cb7,0xcb15},
{0x3cb8,0x3cbe,0xcb19},
{0x3cbf,0x3cdd,0xcb22},
{0x3cde,0x3ce3,0xcb42},
{0x3ce4,0x3ce5,0xcb4a},
{0x3ce6,0x3ce8,0xcb4d},
{0x3ce9,0x3cef,0xcb51},
{0x3cf0,0x3cf2,0xcb5a},
{0x3cf3,0x3cf8,0xcb5e},
{0x3cf9,0x3d0b,0xcb65},
{0x3d0c,0x3d2d,0xcb7a},
{0x3d2e,0x3d48,0xcb9d},
{0x3d49,0x3d63,0xcbb9},
{0x3d64,0x3d72,0xcbd5},
{0x3d73,0x3d74,0xcbe5},
{0x3d75,0x3d75,0xcbe8},
{0x3d76,0x3d97,0xcbea},
{0x3d98,0x3d99,0xcc0e},
{0x3d9a,0x3d9c,0xcc11},
{0x3d9d,0x3da3,0xcc15},
{0x3da4,0x3da6,0xcc1e},
{0x3da7,0x3daa,0xcc23},
{0x3dab,0x3dac,0xcc2a},
{0x3dad,0x3dad,0xcc2d},
{0x3dae,0x3dae,0xcc2f},
{0x3daf,0x3db5,0xcc31},
{0x3db6,0x3db6,0xcc3a},
{0x3db7,0x3dbb,0xcc3f},
{0x3dbc,0x3dbd,0xcc46},
{0x3dbe,0x3dc0,0xcc49},
{0x3dc1,0x3dc7,0xcc4d},
{0x3dc8,0x3dc8,0xcc56},
{0x3dc9,0x3dce,0xcc5a},
{0x3dcf,0x3dd1,0xcc61},
{0x3dd2,0x3dd2,0xcc65},
{0x3dd3,0x3dd3,0xcc67},
{0x3dd4,0x3dda,0xcc69},
{0x3ddb,0x3dde,0xcc71},
{0x3ddf,0x3e00,0xcc76},
{0x3e01,0x3e02,0xcc9a},
{0x3e03,0x3e05,0xcc9d},
{0x3e06,0x3e0c,0xcca1},
{0x3e0d,0x3e0d,0xccaa},
{0x3e0e,0x3e13,0xccae},
{0x3e14,0x3e15,0xccb6},
{0x3e16,0x3e18,0xccb9},
{0x3e19,0x3e1f,0xccbd},
{0x3e20,0x3e20,0xccc6},
{0x3e21,0x3e21,0xccc8},
{0x3e22,0x3e27,0xccca},
{0x3e28,0x3e2a,0xccd1},
{0x3e2b,0x3e39,0xccd5},
{0x3e3a,0x3e40,0xcce5},
{0x3e41,0x3e43,0xcced},
{0x3e44,0x3e53,0xccf1},
{0x3e54,0x3e59,0xcd02},
{0x3e5a,0x3e5b,0xcd0a},
{0x3e5c,0x3e5e,0xcd0d},
{0x3e5f,0x3e65,0xcd11},
{0x3e66,0x3e66,0xcd1a},
{0x3e67,0x3e67,0xcd1c},
{0x3e68,0x3e6d,0xcd1e},
{0x3e6e,0x3e70,0xcd25},
{0x3e71,0x3e73,0xcd29},
{0x3e74,0x3e7f,0xcd2d},
{0x3e80,0x3ea1,0xcd3a},
{0x3ea2,0x3ea4,0xcd5d},
{0x3ea5,0x3ea7,0xcd61},
{0x3ea8,0x3eae,0xcd65},
{0x3eaf,0x3eaf,0xcd6e},
{0x3eb0,0x3eb0,0xcd70},
{0x3eb1,0x3eb6,0xcd72},
{0x3eb7,0x3ec5,0xcd79},
{0x3ec6,0x3ed0,0xcd89},
{0x3ed1,0x3ed2,0xcd96},
{0x3ed3,0x3ed5,0xcd99},
{0x3ed6,0x3edc,0xcd9d},
{0x3edd,0x3edd,0xcda6},
{0x3ede,0x3ede,0xcda8},
{0x3edf,0x3ee4,0xcdaa},
{0x3ee5,0x3ef7,0xcdb1},
{0x3ef8,0x3efe,0xcdc5},
{0x3eff,0x3f01,0xcdcd},
{0x3f02,0x3f18,0xcdd1},
{0x3f19,0x3f1b,0xcde9},
{0x3f1c,0x3f1e,0xcded},
{0x3f1f,0x3f25,0xcdf1},
{0x3f26,0x3f26,0xcdfa},
{0x3f27,0x3f27,0xcdfc},
{0x3f28,0x3f2d,0xcdfe},
{0x3f2e,0x3f30,0xce05},
{0x3f31,0x3f33,0xce09},
{0x3f34,0x3f3a,0xce0d},
{0x3f3b,0x3f3e,0xce15},
{0x3f3f,0x3f44,0xce1a},
{0x3f45,0x3f46,0xce22},
{0x3f47,0x3f49,0xce25},
{0x3f4a,0x3f50,0xce29},
{0x3f51,0x3f51,0xce32},
{0x3f52,0x3f52,0xce34},
{0x3f53,0x3f74,0xce36},
{0x3f75,0x3f76,0xce5a},
{0x3f77,0x3f78,0xce5d},
{0x3f79,0x3f7e,0xce62},
{0x3f7f,0x3f7f,0xce6a},
{0x3f80,0x3f80,0xce6c},
{0x3f81,0x3f86,0xce6e},
{0x3f87,0x3f88,0xce76},
{0x3f89,0x3f8b,0xce79},
{0x3f8c,0x3f92,0xce7d},
{0x3f93,0x3f93,0xce86},
{0x3f94,0x3f94,0xce88},
{0x3f95,0x3f9a,0xce8a},
{0x3f9b,0x3f9c,0xce92},
{0x3f9d,0x3f9f,0xce95},
{0x3fa0,0x3fa6,0xce99},
{0x3fa7,0x3fa7,0xcea2},
{0x3fa8,0x3fad,0xcea6},
{0x3fae,0x3fc0,0xceae},
{0x3fc1,0x3fe2,0xcec2},
{0x3fe3,0x3fe4,0xcee6},
{0x3fe5,0x3fe6,0xcee9},
{0x3fe7,0x3fed,0xceed},
{0x3fee,0x3fee,0xcef6},
{0x3fef,0x3ff4,0xcefa},
{0x3ff5,0x3ff6,0xcf02},
{0x3ff7,0x3ff9,0xcf05},
{0x3ffa,0x4000,0xcf09},
{0x4001,0x4001,0xcf12},
{0x4002,0x4002,0xcf14},
{0x4003,0x4008,0xcf16},
{0x4009,0x400b,0xcf1d},
{0x400c,0x400e,0xcf21},
{0x400f,0x4015,0xcf25},
{0x4016,0x4016,0xcf2e},
{0x4017,0x401c,0xcf32},
{0x401d,0x4037,0xcf39},
{0x4038,0x4039,0xcf56},
{0x403a,0x403c,0xcf59},
{0x403d,0x4043,0xcf5d},
{0x4044,0x4044,0xcf66},
{0x4045,0x4045,0xcf68},
{0x4046,0x404b,0xcf6a},
{0x404c,0x404d,0xcf72},
{0x404e,0x4050,0xcf75},
{0x4051,0x4057,0xcf79},
{0x4058,0x405b,0xcf81},
{0x405c,0x4061,0xcf86},
{0x4062,0x4075,0xcf8d},
{0x4076,0x407b,0xcfa2},
{0x407c,0x4082,0xcfa9},
{0x4083,0x4095,0xcfb1},
{0x4096,0x40b0,0xcfc5},
{0x40b1,0x40b2,0xcfe2},
{0x40b3,0x40b5,0xcfe5},
{0x40b6,0x40bc,0xcfe9},
{0x40bd,0x40bd,0xcff2},
{0x40be,0x40be,0xcff4},
{0x40bf,0x40c4,0xcff6},
{0x40c5,0x40c7,0xcffd},
{0x40c8,0x40ca,0xd001},
{0x40cb,0x40d6,0xd005},
{0x40d7,0x40dc,0xd012},
{0x40dd,0x40f0,0xd019},
{0x40f1,0x40f6,0xd02e},
{0x40f7,0x40f8,0xd036},
{0x40f9,0x40fb,0xd039},
{0x40fc,0x4102,0xd03d},
{0x4103,0x4103,0xd046},
{0x4104,0x4104,0xd048},
{0x4105,0x410a,0xd04a},
{0x410b,0x410d,0xd051},
{0x410e,0x4110,0xd055},
{0x4111,0x4117,0xd059},
{0x4118,0x4122,0xd061},
{0x4123,0x4124,0xd06e},
{0x4125,0x4127,0xd071},
{0x4128,0x412e,0xd075},
{0x412f,0x4131,0xd07e},
{0x4132,0x4153,0xd082},
{0x4154,0x4155,0xd0a6},
{0x4156,0x4158,0xd0a9},
{0x4159,0x415f,0xd0ad},
{0x4160,0x4160,0xd0b6},
{0x4161,0x4161,0xd0b8},
{0x4162,0x4167,0xd0ba},
{0x4168,0x4169,0xd0c2},
{0x416a,0x416c,0xd0c5},
{0x416d,0x4172,0xd0ca},
{0x4173,0x4173,0xd0d2},
{0x4174,0x4179,0xd0d6},
{0x417a,0x417b,0xd0de},
{0x417c,0x417e,0xd0e1},
{0x417f,0x4185,0xd0e5},
{0x4186,0x4186,0xd0ee},
{0x4187,0x418c,0xd0f2},
{0x418d,0x41a0,0xd0f9},
{0x41a1,0x41c2,0xd10e},
{0x41c3,0x41c4,0xd132},
{0x41c5,0x41c7,0xd135},
{0x41c8,0x41c8,0xd139},
{0x41c9,0x41cd,0xd13b},
{0x41ce,0x41ce,0xd142},
{0x41cf,0x41d4,0xd146},
{0x41d5,0x41d6,0xd14e},
{0x41d7,0x41d9,0xd151},
{0x41da,0x41e0,0xd155},
{0x41e1,0x41e1,0xd15e},
{0x41e2,0x41e2,0xd160},
{0x41e3,0x41e8,0xd162},
{0x41e9,0x41eb,0xd169},
{0x41ec,0x41fa,0xd16d},
{0x41fb,0x4201,0xd17d},
{0x4202,0x4204,0xd185},
{0x4205,0x421b,0xd189},
{0x421c,0x421d,0xd1a2},
{0x421e,0x4220,0xd1a5},
{0x4221,0x4227,0xd1a9},
{0x4228,0x4228,0xd1b2},
{0x4229,0x4229,0xd1b4},
{0x422a,0x422d,0xd1b6},
{0x422e,0x422e,0xd1bb},
{0x422f,0x4231,0xd1bd},
{0x4232,0x4248,0xd1c1},
{0x4249,0x4263,0xd1d9},
{0x4264,0x4266,0xd1f5},
{0x4267,0x4274,0xd1f9},
{0x4275,0x4275,0xd208},
{0x4276,0x427b,0xd20a},
{0x427c,0x4296,0xd211},
{0x4297,0x4298,0xd22e},
{0x4299,0x429b,0xd231},
{0x429c,0x42a2,0xd235},
{0x42a3,0x42a3,0xd23e},
{0x42a4,0x42a4,0xd240},
{0x42a5,0x42aa,0xd242},
{0x42ab,0x42bd,0xd249},
{0x42be,0x42c4,0xd25d},
{0x42c5,0x42df,0xd265},
{0x42e0,0x42e1,0xd282},
{0x42e2,0x42e4,0xd285},
{0x42e5,0x42eb,0xd289},
{0x42ec,0x42ee,0xd292},
{0x42ef,0x42f4,0xd296},
{0x42f5,0x42f7,0xd29d},
{0x42f8,0x42fa,0xd2a1},
{0x42fb,0x4301,0xd2a5},
{0x4302,0x4305,0xd2ad},
{0x4306,0x430b,0xd2b2},
{0x430c,0x430d,0xd2ba},
{0x430e,0x430f,0xd2bd},
{0x4310,0x4310,0xd2c1},
{0x4311,0x4315,0xd2c3},
{0x4316,0x4316,0xd2ca},
{0x4317,0x431e,0xd2cc},
{0x431f,0x4321,0xd2d5},
{0x4322,0x4324,0xd2d9},
{0x4325,0x432b,0xd2dd},
{0x432c,0x4335,0xd2e6},
{0x4336,0x4337,0xd2f2},
{0x4338,0x433a,0xd2f5},
{0x433b,0x4341,0xd2f9},
{0x4342,0x4342,0xd302},
{0x4343,0x4343,0xd304},
{0x4344,0x4349,0xd306},
{0x434a,0x434a,0xd30f},
{0x434b,0x434d,0xd311},
{0x434e,0x434e,0xd315},
{0x434f,0x4353,0xd317},
{0x4354,0x4354,0xd31e},
{0x4355,0x4357,0xd322},
{0x4358,0x4359,0xd326},
{0x435a,0x435b,0xd32a},
{0x435c,0x435e,0xd32d},
{0x435f,0x4365,0xd331},
{0x4366,0x4366,0xd33a},
{0x4367,0x436c,0xd33e},
{0x436d,0x43a2,0xd346},
{0x43a3,0x43a4,0xd37e},
{0x43a5,0x43a7,0xd381},
{0x43a8,0x43ae,0xd385},
{0x43af,0x43af,0xd38e},
{0x43b0,0x43b5,0xd392},
{0x43b6,0x43b7,0xd39a},
{0x43b8,0x43ba,0xd39d},
{0x43bb,0x43c1,0xd3a1},
{0x43c2,0x43c2,0xd3aa},
{0x43c3,0x43c3,0xd3ac},
{0x43c4,0x43c9,0xd3ae},
{0x43ca,0x43cc,0xd3b5},
{0x43cd,0x43cf,0xd3b9},
{0x43d0,0x43d6,0xd3bd},
{0x43d7,0x43d8,0xd3c6},
{0x43d9,0x43de,0xd3ca},
{0x43df,0x43e5,0xd3d1},
{0x43e6,0x43ed,0xd3d9},
{0x43ee,0x43ee,0xd3e2},
{0x43ef,0x43f6,0xd3e4},
{0x43f7,0x43f8,0xd3ee},
{0x43f9,0x43fb,0xd3f1},
{0x43fc,0x4402,0xd3f5},
{0x4403,0x4403,0xd3fe},
{0x4404,0x4404,0xd400},
{0x4405,0x440a,0xd402},
{0x440b,0x441e,0xd409},
{0x441f,0x4440,0xd41e},
{0x4441,0x4443,0xd441},
{0x4444,0x445a,0xd445},
{0x445b,0x445d,0xd45d},
{0x445e,0x4460,0xd461},
{0x4461,0x4468,0xd465},
{0x4469,0x4469,0xd46e},
{0x446a,0x4471,0xd470},
{0x4472,0x4473,0xd47a},
{0x4474,0x4475,0xd47d},
{0x4476,0x4476,0xd481},
{0x4477,0x447b,0xd483},
{0x447c,0x447c,0xd48a},
{0x447d,0x447d,0xd48c},
{0x447e,0x4483,0xd48e},
{0x4484,0x4497,0xd495},
{0x4498,0x44b9,0xd4aa},
{0x44ba,0x44bc,0xd4cd},
{0x44bd,0x44bf,0xd4d1},
{0x44c0,0x44c6,0xd4d5},
{0x44c7,0x44c8,0xd4dd},
{0x44c9,0x44d0,0xd4e0},
{0x44d1,0x44d3,0xd4e9},
{0x44d4,0x44d6,0xd4ed},
{0x44d7,0x44dd,0xd4f1},
{0x44de,0x44df,0xd4f9},
{0x44e0,0x44e0,0xd4fc},
{0x44e1,0x44e6,0xd4fe},
{0x44e7,0x44e9,0xd505},
{0x44ea,0x44ec,0xd509},
{0x44ed,0x44f3,0xd50d},
{0x44f4,0x44f4,0xd516},
{0x44f5,0x4518,0xd518},
{0x4519,0x451a,0xd53e},
{0x451b,0x451d,0xd541},
{0x451e,0x4524,0xd545},
{0x4525,0x4525,0xd54e},
{0x4526,0x4526,0xd550},
{0x4527,0x452c,0xd552},
{0x452d,0x452e,0xd55a},
{0x452f,0x4531,0xd55d},
{0x4532,0x4535,0xd561},
{0x4536,0x4537,0xd566},
{0x4538,0x4538,0xd56a},
{0x4539,0x4539,0xd56c},
{0x453a,0x453f,0xd56e},
{0x4540,0x4541,0xd576},
{0x4542,0x4544,0xd579},
{0x4545,0x454b,0xd57d},
{0x454c,0x454c,0xd586},
{0x454d,0x4552,0xd58a},
{0x4553,0x4566,0xd591},
{0x4567,0x4588,0xd5a6},
{0x4589,0x458a,0xd5ca},
{0x458b,0x458d,0xd5cd},
{0x458e,0x458e,0xd5d1},
{0x458f,0x4593,0xd5d3},
{0x4594,0x4594,0xd5da},
{0x4595,0x4595,0xd5dc},
{0x4596,0x459b,0xd5de},
{0x459c,0x459d,0xd5e6},
{0x459e,0x45a0,0xd5e9},
{0x45a1,0x45a7,0xd5ed},
{0x45a8,0x45a8,0xd5f6},
{0x45a9,0x45a9,0xd5f8},
{0x45aa,0x45af,0xd5fa},
{0x45b0,0x45b1,0xd602},
{0x45b2,0x45b4,0xd605},
{0x45b5,0x45bb,0xd609},
{0x45bc,0x45bc,0xd612},
{0x45bd,0x45c2,0xd616},
{0x45c3,0x45c5,0xd61d},
{0x45c6,0x45c8,0xd621},
{0x45c9,0x45d0,0xd625},
{0x45d1,0x45da,0xd62e},
{0x45db,0x45dc,0xd63a},
{0x45dd,0x45df,0xd63d},
{0x45e0,0x45e3,0xd641},
{0x45e4,0x45e5,0xd646},
{0x45e6,0x45e6,0xd64a},
{0x45e7,0x45e7,0xd64c},
{0x45e8,0x45ea,0xd64e},
{0x45eb,0x45ec,0xd652},
{0x45ed,0x45ee,0xd656},
{0x45ef,0x45f1,0xd659},
{0x45f2,0x45fb,0xd65d},
{0x45fc,0x45fc,0xd668},
{0x45fd,0x4602,0xd66a},
{0x4603,0x4604,0xd672},
{0x4605,0x4612,0xd675},
{0x4613,0x4613,0xd684},
{0x4614,0x4619,0xd686},
{0x461a,0x461b,0xd68e},
{0x461c,0x461e,0xd691},
{0x461f,0x4626,0xd695},
{0x4627,0x4627,0xd69e},
{0x4628,0x4628,0xd6a0},
{0x4629,0x462e,0xd6a2},
{0x462f,0x4631,0xd6a9},
{0x4632,0x4634,0xd6ad},
{0x4635,0x463c,0xd6b1},
{0x463d,0x463d,0xd6ba},
{0x463e,0x4645,0xd6bc},
{0x4646,0x4647,0xd6c6},
{0x4648,0x464a,0xd6c9},
{0x464b,0x464e,0xd6cd},
{0x464f,0x4650,0xd6d2},
{0x4651,0x4652,0xd6d5},
{0x4653,0x4653,0xd6d8},
{0x4654,0x4659,0xd6da},
{0x465a,0x465c,0xd6e1},
{0x465d,0x465f,0xd6e5},
{0x4660,0x4666,0xd6e9},
{0x4667,0x466a,0xd6f1},
{0x466b,0x4670,0xd6f6},
{0x4671,0x4672,0xd6fe},
{0x4673,0x4675,0xd701},
{0x4676,0x4681,0xd705},
{0x4682,0x4687,0xd712},
{0x4688,0x4689,0xd71a},
{0x468a,0x468c,0xd71d},
{0x468d,0x4693,0xd721},
{0x4694,0x4694,0xd72a},
{0x4695,0x4695,0xd72c},
{0x4696,0x469b,0xd72e},
{0x469c,0x469d,0xd736},
{0x469e,0x46a0,0xd739},
{0x46a1,0x46a7,0xd73d},
{0x46a8,0x46a9,0xd745},
{0x46aa,0x46aa,0xd748},
{0x46ab,0x46b0,0xd74a},
{0x46b1,0x46b2,0xd752},
{0x46b3,0x46b3,0xd755},
{0x46b4,0x46b9,0xd75a},
{0x46ba,0x46ba,0xd762},
{0x46bb,0x46bb,0xd764},
{0x46bc,0x46be,0xd766},
{0x46bf,0x46c0,0xd76a},
{0x46c1,0x46c3,0xd76d},
{0x46c4,0x46c6,0xd771},
{0x46c7,0x46cd,0xd775},
{0x46ce,0x46d0,0xd77e},
{0x46d1,0x46d6,0xd782},
{0x46d7,0x46d8,0xd78a},
{0x46d9,0x46db,0xd78d},
{0x46dc,0x46e2,0xd791},
{0x46e3,0x46e3,0xd79a},
{0x46e4,0x46e4,0xd79c},
{0x46e5,0x46ea,0xd79e},
{0x46eb,0x4749,0x20},
{0x474a,0x474a,0x20a9},
{0x474b,0x474b,0x2010},
{0x474c,0x474c,0xa9},
{0x474d,0x474d,0x2122},
{0x474e,0x474e,0x22ef},
{0x474f,0x47ac,0x20},
{0x47ad,0x47ad,0x203e},
{0x47ae,0x47ae,0x7e},
{0x47af,0x47af,0x5c},
};

static const pdf_mrange cmap_Adobe_Korea1_UCS2_mranges[] = {
{0x200f,0x0},
{0x2010,0x3},
{0x2011,0x6},
{0x2012,0x9},
{0x205a,0xc},
{0x205d,0xf},
{0x205e,0x12},
{0x205f,0x15},
{0x2060,0x18},
{0x2069,0x1b},
{0x206a,0x1e},
{0x206b,0x21},
{0x206c,0x24},
{0x206d,0x27},
{0x206e,0x2a},
{0x206f,0x2d},
{0x2070,0x30},
{0x2071,0x33},
{0x2072,0x36},
{0x2073,0x3b},
{0x2074,0x40},
{0x2075,0x45},
{0x2076,0x4a},
{0x2077,0x4f},
{0x2078,0x54},
{0x2079,0x59},
{0x207a,0x5e},
{0x207b,0x63},
{0x207c,0x68},
{0x2082,0x6d},
{0x2083,0x70},
{0x2084,0x73},
{0x2085,0x76},
{0x2086,0x79},
{0x2087,0x7c},
{0x2088,0x7f},
{0x2089,0x82},
{0x208a,0x85},
{0x208b,0x88},
{0x208c,0x8d},
{0x208d,0x92},
{0x208e,0x97},
{0x208f,0x9c},
{0x2090,0xa1},
{0x2091,0xa6},
{0x2092,0xab},
{0x2093,0xb0},
{0x2094,0xb5},
{0x2095,0xba},
{0x20aa,0xbf},
{0x20ab,0xc3},
{0x20ac,0xc7},
{0x20ad,0xcb},
{0x20ae,0xcf},
{0x20af,0xd3},
{0x20b0,0xd7},
{0x20b1,0xdb},
{0x20b2,0xdf},
{0x20b3,0xe3},
{0x20b4,0xe7},
{0x20b5,0xeb},
{0x20b6,0xef},
{0x20b7,0xf3},
{0x20b8,0xf7},
{0x20b9,0xfb},
{0x20ba,0xff},
{0x20bb,0x103},
{0x20bc,0x107},
{0x20bd,0x10b},
{0x20be,0x10f},
{0x20bf,0x113},
{0x20c0,0x117},
{0x20c1,0x11b},
{0x20c2,0x11f},
{0x20c3,0x123},
{0x20de,0x127},
{0x20df,0x12a},
{0x20e0,0x12d},
{0x20e1,0x130},
{0x20e2,0x133},
{0x20e3,0x136},
{0x20e4,0x139},
{0x20e5,0x13c},
{0x20e6,0x13f},
{0x20e7,0x142},
{0x20e8,0x147},
{0x20e9,0x14c},
{0x20ea,0x151},
{0x20eb,0x156},
{0x20ec,0x15b},
{0x20ed,0x160},
{0x20ee,0x165},
{0x20ef,0x16a},
{0x20f0,0x16f},
{0x20f1,0x174},
{0x20f2,0x179},
{0x20f3,0x17c},
{0x20f4,0x17f},
{0x20f5,0x182},
{0x20f6,0x185},
{0x20f7,0x188},
{0x20f8,0x18b},
{0x20f9,0x18e},
{0x20fa,0x191},
{0x20fb,0x194},
{0x20fc,0x199},
{0x20fd,0x19e},
{0x20fe,0x1a3},
{0x20ff,0x1a8},
{0x2100,0x1ad},
{0x2101,0x1b2},
{0x2102,0x1b7},
{0x2103,0x1bc},
{0x2104,0x1c1},
{0x2105,0x1c6},
{0x213a,0x1cb},
{0x213b,0x1ce},
{0x213c,0x1d1},
{0x213d,0x1d4},
{0x213e,0x1d7},
{0x213f,0x1da},
{0x2140,0x1dd},
{0x2141,0x1e0},
{0x2142,0x1e3},
{0x2143,0x1e6},
{0x214e,0x1e9},
{0x214f,0x1ec},
{0x2150,0x1ef},
{0x2151,0x1f2},
{0x2152,0x1f5},
{0x2153,0x1f8},
{0x2154,0x1fb},
{0x2155,0x1fe},
{0x2156,0x201},
{0x2157,0x204},
{0x2158,0x207},
{0x2159,0x20a},
{0x215a,0x20d},
{0x215b,0x210},
{0x215c,0x213},
{0x215d,0x216},
{0x215e,0x219},
{0x215f,0x21c},
{0x2160,0x21f},
{0x2161,0x222},
{0x2162,0x225},
{0x2163,0x228},
{0x2164,0x22b},
{0x2165,0x22e},
{0x2166,0x231},
{0x2167,0x234},
{0x2168,0x237},
{0x2169,0x23a},
{0x216a,0x23d},
{0x216b,0x240},
{0x216c,0x243},
{0x216d,0x246},
{0x216e,0x249},
{0x216f,0x24c},
{0x2170,0x24f},
{0x2171,0x252},
{0x2172,0x255},
{0x2173,0x258},
{0x2174,0x25b},
{0x2175,0x25e},
{0x2176,0x261},
{0x2177,0x264},
{0x2178,0x267},
{0x2179,0x26a},
{0x217a,0x26d},
{0x217b,0x270},
{0x217c,0x273},
{0x217d,0x276},
{0x217e,0x279},
{0x217f,0x27c},
{0x2180,0x27f},
{0x2181,0x282},
{0x2187,0x285},
{0x2188,0x28a},
{0x2189,0x28f},
{0x218a,0x294},
{0x218e,0x299},
{0x218f,0x29c},
{0x2190,0x29f},
{0x21a0,0x2a3},
{0x21a1,0x2a6},
{0x21a3,0x2a9},
{0x21ac,0x2ac},
{0x21ae,0x2af},
{0x21af,0x2b2},
{0x21b4,0x2b5},
{0x21b5,0x2b8},
{0x21f5,0x2bb},
{0x21f6,0x2c0},
{0x21f7,0x2c5},
{0x21f8,0x2ca},
{0x21f9,0x2cf},
{0x21fa,0x2d4},
{0x221a,0x2d9},
{0x2227,0x2dc},
{0x223c,0x2df},
{0x223e,0x2e2},
{0x225c,0x2e5},
{0x225d,0x2ea},
{0x225e,0x2ef},
{0x225f,0x2f4},
{0x2260,0x2f9},
{0x2261,0x2fe},
{0x2262,0x303},
{0x2263,0x308},
{0x2264,0x30d},
{0x2265,0x312},
{0x22c2,0x317},
{0x22c3,0x31a},
{0x22c4,0x31d},
{0x22c5,0x320},
{0x22c6,0x323},
{0x22c7,0x326},
{0x22c8,0x329},
{0x22c9,0x32c},
{0x22ca,0x32f},
{0x22cb,0x332},
{0x22cc,0x335},
{0x22cd,0x338},
{0x22ce,0x33b},
{0x22cf,0x33e},
{0x22d0,0x341},
{0x22d1,0x344},
{0x22d2,0x347},
{0x22d3,0x34a},
{0x22d4,0x34d},
{0x22d5,0x350},
{0x22d6,0x353},
{0x22d7,0x356},
{0x22d8,0x359},
{0x22d9,0x35c},
{0x22da,0x35f},
{0x22db,0x362},
{0x22dc,0x365},
{0x22dd,0x368},
{0x22de,0x36b},
{0x22df,0x36e},
{0x22e0,0x371},
{0x22e1,0x374},
{0x22e2,0x377},
{0x22e3,0x37a},
{0x22e4,0x37d},
{0x22e5,0x380},
{0x22e6,0x383},
{0x22e7,0x386},
{0x22e8,0x389},
{0x22e9,0x38c},
{0x22ea,0x38f},
{0x22eb,0x392},
{0x22ec,0x395},
{0x22ed,0x398},
{0x22ee,0x39b},
{0x22ef,0x39e},
{0x22f0,0x3a1},
{0x22f1,0x3a4},
{0x22f2,0x3a7},
{0x22f3,0x3aa},
{0x22f4,0x3ad},
{0x22f5,0x3b0},
{0x22f6,0x3b3},
{0x22f7,0x3b6},
{0x22f8,0x3b9},
{0x22f9,0x3bc},
{0x22fa,0x3bf},
{0x22fb,0x3c2},
{0x22fc,0x3c5},
{0x22fd,0x3c8},
{0x22fe,0x3cb},
{0x22ff,0x3ce},
{0x2300,0x3d1},
{0x2301,0x3d4},
{0x2302,0x3d7},
{0x2303,0x3da},
{0x2304,0x3dd},
{0x2305,0x3e0},
{0x2306,0x3e3},
{0x2307,0x3e6},
{0x2308,0x3e9},
{0x2309,0x3ec},
{0x230a,0x3ef},
{0x230b,0x3f2},
{0x230c,0x3f5},
{0x230d,0x3f8},
{0x230e,0x3fb},
{0x230f,0x3fe},
{0x2310,0x401},
{0x2311,0x404},
{0x2312,0x407},
{0x2313,0x40a},
{0x2314,0x40d},
{0x2315,0x410},
{0x2316,0x413},
{0x2317,0x416},
{0x2318,0x419},
{0x2319,0x41c},
{0x231a,0x41f},
{0x231b,0x422},
{0x231c,0x425},
{0x231d,0x428},
{0x231e,0x42b},
{0x231f,0x42e},
{0x2320,0x431},
{0x2321,0x434},
{0x2322,0x437},
{0x2323,0x43a},
{0x2324,0x43d},
{0x2325,0x440},
{0x2326,0x443},
{0x2327,0x446},
{0x2328,0x449},
{0x2329,0x44c},
{0x232a,0x44f},
{0x232b,0x452},
{0x232c,0x455},
{0x232d,0x458},
{0x232e,0x45b},
{0x232f,0x45e},
{0x2330,0x461},
{0x2331,0x464},
{0x2332,0x467},
{0x2333,0x46a},
{0x2334,0x46d},
{0x2335,0x470},
{0x2336,0x473},
{0x2337,0x476},
{0x2338,0x479},
{0x2339,0x47c},
{0x233a,0x47f},
{0x233b,0x482},
{0x233c,0x485},
{0x233d,0x488},
{0x233e,0x48b},
{0x233f,0x48e},
{0x2340,0x491},
{0x2341,0x494},
{0x2342,0x497},
{0x2343,0x49a},
{0x2344,0x49d},
{0x2345,0x4a0},
{0x2346,0x4a3},
{0x2347,0x4a6},
{0x2348,0x4a9},
{0x2349,0x4ac},
{0x234a,0x4af},
{0x234b,0x4b2},
{0x234c,0x4b5},
{0x234d,0x4b8},
{0x234e,0x4bb},
{0x234f,0x4be},
{0x2350,0x4c1},
{0x2351,0x4c4},
{0x2357,0x4c7},
{0x2358,0x4cc},
{0x2359,0x4d1},
{0x235a,0x4d6},
{0x235b,0x4db},
{0x235c,0x4e0},
{0x235d,0x4e5},
{0x235e,0x4e8},
{0x235f,0x4eb},
{0x2360,0x4ee},
{0x2361,0x4f1},
{0x2362,0x4f4},
{0x2363,0x4f7},
{0x2364,0x4fa},
{0x2365,0x4fd},
{0x2366,0x500},
{0x2367,0x503},
{0x2368,0x506},
{0x2369,0x509},
{0x236a,0x50c},
{0x236b,0x50f},
{0x236c,0x512},
{0x236d,0x515},
{0x236e,0x518},
{0x236f,0x51b},
{0x2370,0x51e},
{0x2371,0x521},
{0x2372,0x524},
{0x2373,0x527},
{0x2374,0x52a},
{0x2375,0x52d},
{0x2376,0x530},
{0x2377,0x533},
{0x2379,0x536},
{0x237a,0x539},
{0x237b,0x53c},
{0x237c,0x53f},
{0x237d,0x542},
{0x237e,0x545},
{0x237f,0x548},
{0x2380,0x54b},
{0x2381,0x54e},
{0x2382,0x551},
{0x2383,0x554},
{0x2384,0x557},
{0x2385,0x55a},
{0x2386,0x55d},
{0x2387,0x560},
{0x2389,0x563},
{0x238a,0x566},
{0x238b,0x569},
{0x238c,0x56c},
{0x238d,0x56f},
{0x238e,0x572},
{0x238f,0x575},
{0x2390,0x578},
{0x2391,0x57b},
{0x2392,0x57e},
{0x2393,0x581},
{0x2394,0x584},
{0x2395,0x587},
{0x2396,0x58a},
{0x2397,0x58d},
{0x2398,0x590},
{0x2399,0x593},
{0x239a,0x596},
{0x239b,0x599},
{0x239c,0x59c},
{0x239d,0x59f},
{0x239e,0x5a2},
{0x239f,0x5a5},
{0x23a0,0x5a8},
{0x23a1,0x5ab},
{0x23a2,0x5ae},
{0x23a3,0x5b1},
{0x23a4,0x5b4},
{0x23a5,0x5b7},
{0x23a6,0x5ba},
{0x23a7,0x5bd},
{0x23a8,0x5c0},
{0x23a9,0x5c3},
{0x23aa,0x5c6},
{0x23ab,0x5c9},
{0x23ac,0x5cc},
{0x23ad,0x5cf},
{0x23ae,0x5d2},
{0x23af,0x5d5},
{0x23b0,0x5d8},
{0x23b1,0x5db},
{0x23b2,0x5de},
{0x23b3,0x5e1},
{0x23b4,0x5e4},
{0x23b5,0x5e7},
{0x23b6,0x5ea},
{0x23b7,0x5ed},
{0x23b8,0x5f0},
{0x23b9,0x5f3},
{0x23ba,0x5f8},
{0x23bb,0x5fd},
{0x23bc,0x602},
{0x240a,0x607},
{0x240b,0x60a},
{0x240c,0x60d},
{0x240d,0x610},
{0x240e,0x613},
{0x240f,0x616},
{0x2410,0x619},
{0x2411,0x61c},
{0x2412,0x61f},
{0x2413,0x622},
{0x2414,0x625},
{0x2415,0x628},
{0x2416,0x62b},
{0x2417,0x62e},
{0x2418,0x631},
{0x2419,0x634},
{0x241a,0x637},
{0x241b,0x63c},
{0x241c,0x641},
{0x241d,0x646},
{0x241e,0x64b},
{0x241f,0x650},
{0x2420,0x655},
{0x2421,0x65a},
{0x2422,0x65f},
{0x2423,0x664},
{0x2424,0x669},
{0x2425,0x66e},
{0x2426,0x671},
{0x2427,0x674},
{0x2428,0x677},
{0x2429,0x67a},
{0x242a,0x67d},
{0x242b,0x680},
{0x242c,0x683},
{0x242d,0x686},
{0x242e,0x689},
{0x242f,0x68c},
{0x2430,0x691},
{0x2431,0x696},
{0x2432,0x69b},
{0x2433,0x6a0},
{0x2434,0x6a5},
{0x2435,0x6aa},
{0x2436,0x6af},
{0x2437,0x6b4},
{0x2438,0x6b9},
{0x2439,0x6be},
{0x243a,0x6c1},
{0x243b,0x6c4},
{0x243c,0x6c7},
{0x243d,0x6ca},
{0x243e,0x6cd},
{0x243f,0x6d0},
{0x2440,0x6d3},
{0x2441,0x6d6},
{0x2442,0x6d9},
{0x2443,0x6dc},
{0x2444,0x6e1},
{0x2445,0x6e6},
{0x2446,0x6eb},
{0x2447,0x6f0},
{0x2448,0x6f5},
{0x2449,0x6fa},
{0x244a,0x6ff},
{0x244b,0x704},
{0x244c,0x709},
{0x244d,0x70e},
{0x244e,0x711},
{0x244f,0x714},
{0x2450,0x717},
{0x2451,0x71a},
{0x2452,0x71d},
{0x2453,0x720},
{0x245b,0x723},
{0x245c,0x726},
{0x245d,0x729},
{0x245e,0x72c},
{0x245f,0x72f},
{0x2460,0x732},
{0x2461,0x735},
};

static const int cmap_Adobe_Korea1_UCS2_table[] = {
0x2,0x28,0x28,
0x2,0x29,0x29,
0x2,0x28,0x28,
0x2,0x29,0x29,
0x2,0xcdcd,0xcdcd,
0x2,0x28,0x28,
0x2,0x29,0x29,
0x2,0x28,0x28,
0x2,0x29,0x29,
0x2,0x31,0x20de,
0x2,0x32,0x20de,
0x2,0x33,0x20de,
0x2,0x34,0x20de,
0x2,0x35,0x20de,
0x2,0x36,0x20de,
0x2,0x37,0x20de,
0x2,0x38,0x20de,
0x2,0x39,0x20de,
0x4,0x5b,0x31,0x30,0x5d,
0x4,0x5b,0x31,0x31,0x5d,
0x4,0x5b,0x31,0x32,0x5d,
0x4,0x5b,0x31,0x33,0x5d,
0x4,0x5b,0x31,0x34,0x5d,
0x4,0x5b,0x31,0x35,0x5d,
0x4,0x5b,0x31,0x36,0x5d,
0x4,0x5b,0x31,0x37,0x5d,
0x4,0x5b,0x31,0x38,0x5d,
0x4,0x5b,0x31,0x39,0x5d,
0x4,0x5b,0x32,0x30,0x5d,
0x2,0x31,0x20de,
0x2,0x32,0x20de,
0x2,0x33,0x20de,
0x2,0x34,0x20de,
0x2,0x35,0x20de,
0x2,0x36,0x20de,
0x2,0x37,0x20de,
0x2,0x38,0x20de,
0x2,0x39,0x20de,
0x4,0x5b,0x31,0x30,0x5d,
0x4,0x5b,0x31,0x31,0x5d,
0x4,0x5b,0x31,0x32,0x5d,
0x4,0x5b,0x31,0x33,0x5d,
0x4,0x5b,0x31,0x34,0x5d,
0x4,0x5b,0x31,0x35,0x5d,
0x4,0x5b,0x31,0x36,0x5d,
0x4,0x5b,0x31,0x37,0x5d,
0x4,0x5b,0x31,0x38,0x5d,
0x4,0x5b,0x31,0x39,0x5d,
0x4,0x5b,0x32,0x30,0x5d,
0x3,0x28,0x41,0x29,
0x3,0x28,0x42,0x29,
0x3,0x28,0x43,0x29,
0x3,0x28,0x44,0x29,
0x3,0x28,0x45,0x29,
0x3,0x28,0x46,0x29,
0x3,0x28,0x47,0x29,
0x3,0x28,0x48,0x29,
0x3,0x28,0x49,0x29,
0x3,0x28,0x4a,0x29,
0x3,0x28,0x4b,0x29,
0x3,0x28,0x4c,0x29,
0x3,0x28,0x4d,0x29,
0x3,0x28,0x4e,0x29,
0x3,0x28,0x4f,0x29,
0x3,0x28,0x50,0x29,
0x3,0x28,0x51,0x29,
0x3,0x28,0x52,0x29,
0x3,0x28,0x53,0x29,
0x3,0x28,0x54,0x29,
0x3,0x28,0x55,0x29,
0x3,0x28,0x56,0x29,
0x3,0x28,0x57,0x29,
0x3,0x28,0x58,0x29,
0x3,0x28,0x59,0x29,
0x3,0x28,0x5a,0x29,
0x2,0x31,0x20de,
0x2,0x32,0x20de,
0x2,0x33,0x20de,
0x2,0x34,0x20de,
0x2,0x35,0x20de,
0x2,0x36,0x20de,
0x2,0x37,0x20de,
0x2,0x38,0x20de,
0x2,0x39,0x20de,
0x4,0x5b,0x31,0x30,0x5d,
0x4,0x5b,0x31,0x31,0x5d,
0x4,0x5b,0x31,0x32,0x5d,
0x4,0x5b,0x31,0x33,0x5d,
0x4,0x5b,0x31,0x34,0x5d,
0x4,0x5b,0x31,0x35,0x5d,
0x4,0x5b,0x31,0x36,0x5d,
0x4,0x5b,0x31,0x37,0x5d,
0x4,0x5b,0x31,0x38,0x5d,
0x4,0x5b,0x31,0x39,0x5d,
0x4,0x5b,0x32,0x30,0x5d,
0x2,0x31,0x20de,
0x2,0x32,0x20de,
0x2,0x33,0x20de,
0x2,0x34,0x20de,
0x2,0x35,0x20de,
0x2,0x36,0x20de,
0x2,0x37,0x20de,
0x2,0x38,0x20de,
0x2,0x39,0x20de,
0x4,0x5b,0x31,0x30,0x5d,
0x4,0x5b,0x31,0x31,0x5d,
0x4,0x5b,0x31,0x32,0x5d,
0x4,0x5b,0x31,0x33,0x5d,
0x4,0x5b,0x31,0x34,0x5d,
0x4,0x5b,0x31,0x35,0x5d,
0x4,0x5b,0x31,0x36,0x5d,
0x4,0x5b,0x31,0x37,0x5d,
0x4,0x5b,0x31,0x38,0x5d,
0x4,0x5b,0x31,0x39,0x5d,
0x4,0x5b,0x32,0x30,0x5d,
0x2,0x30,0x20de,
0x2,0x31,0x20de,
0x2,0x32,0x20de,
0x2,0x33,0x20de,
0x2,0x34,0x20de,
0x2,0x35,0x20de,
0x2,0x36,0x20de,
0x2,0x37,0x20de,
0x2,0x38,0x20de,
0x2,0x39,0x20de,
0x2,0x41,0x29,
0x2,0x42,0x29,
0x2,0x43,0x29,
0x2,0x44,0x29,
0x2,0x45,0x29,
0x2,0x46,0x29,
0x2,0x47,0x29,
0x2,0x48,0x29,
0x2,0x49,0x29,
0x2,0x4a,0x29,
0x2,0x4b,0x29,
0x2,0x4c,0x29,
0x2,0x4d,0x29,
0x2,0x4e,0x29,
0x2,0x4f,0x29,
0x2,0x50,0x29,
0x2,0x51,0x29,
0x2,0x52,0x29,
0x2,0x53,0x29,
0x2,0x54,0x29,
0x2,0x55,0x29,
0x2,0x56,0x29,
0x2,0x57,0x29,
0x2,0x58,0x29,
0x2,0x59,0x29,
0x2,0x5a,0x29,
0x2,0x61,0x29,
0x2,0x62,0x29,
0x2,0x63,0x29,
0x2,0x64,0x29,
0x2,0x65,0x29,
0x2,0x66,0x29,
0x2,0x67,0x29,
0x2,0x68,0x29,
0x2,0x69,0x29,
0x2,0x6a,0x29,
0x2,0x6b,0x29,
0x2,0x6c,0x29,
0x2,0x6d,0x29,
0x2,0x6e,0x29,
0x2,0x6f,0x29,
0x2,0x70,0x29,
0x2,0x71,0x29,
0x2,0x72,0x29,
0x2,0x73,0x29,
0x2,0x74,0x29,
0x2,0x75,0x29,
0x2,0x76,0x29,
0x2,0x77,0x29,
0x2,0x78,0x29,
0x2,0x79,0x29,
0x2,0x7a,0x29,
0x4,0x28,0x32,0x37,0x29,
0x4,0x28,0x32,0x38,0x29,
0x4,0x28,0x32,0x39,0x29,
0x4,0x28,0x33,0x30,0x29,
0x2,0x2020,0x2020,
0x2,0x2021,0x2021,
0x3,0x2020,0x2020,0x2020,
0x2,0x25a0,0x20df,
0x2,0x25c7,0x20df,
0x2,0x25a1,0x20df,
0x2,0x25c7,0x20de,
0x2,0x25a1,0x20de,
0x2,0x25c6,0x20de,
0x2,0x25b3,0x20dd,
0x2,0x25b2,0x20dd,
0x4,0x28,0x32,0x31,0x29,
0x4,0x28,0x32,0x32,0x29,
0x4,0x28,0x32,0x33,0x29,
0x4,0x28,0x32,0x34,0x29,
0x4,0x28,0x32,0x35,0x29,
0x4,0x28,0x32,0x36,0x29,
0x2,0xf7,0x20dd,
0x2,0x22a5,0x338,
0x2,0x21,0x3f,
0x2,0x3f,0x3f,
0x4,0x28,0x32,0x31,0x29,
0x4,0x28,0x32,0x32,0x29,
0x4,0x28,0x32,0x33,0x29,
0x4,0x28,0x32,0x34,0x29,
0x4,0x28,0x32,0x35,0x29,
0x4,0x28,0x32,0x36,0x29,
0x4,0x28,0x32,0x37,0x29,
0x4,0x28,0x32,0x38,0x29,
0x4,0x28,0x32,0x39,0x29,
0x4,0x28,0x33,0x30,0x29,
0x2,0x41,0x2e,
0x2,0x42,0x2e,
0x2,0x43,0x2e,
0x2,0x44,0x2e,
0x2,0x45,0x2e,
0x2,0x46,0x2e,
0x2,0x47,0x2e,
0x2,0x48,0x2e,
0x2,0x49,0x2e,
0x2,0x4a,0x2e,
0x2,0x4b,0x2e,
0x2,0x4c,0x2e,
0x2,0x4d,0x2e,
0x2,0x4e,0x2e,
0x2,0x4f,0x2e,
0x2,0x50,0x2e,
0x2,0x51,0x2e,
0x2,0x52,0x2e,
0x2,0x53,0x2e,
0x2,0x54,0x2e,
0x2,0x55,0x2e,
0x2,0x56,0x2e,
0x2,0x57,0x2e,
0x2,0x58,0x2e,
0x2,0x59,0x2e,
0x2,0x5a,0x2e,
0x2,0x61,0x2e,
0x2,0x62,0x2e,
0x2,0x63,0x2e,
0x2,0x64,0x2e,
0x2,0x65,0x2e,
0x2,0x66,0x2e,
0x2,0x67,0x2e,
0x2,0x68,0x2e,
0x2,0x69,0x2e,
0x2,0x6a,0x2e,
0x2,0x6b,0x2e,
0x2,0x6c,0x2e,
0x2,0x6d,0x2e,
0x2,0x6e,0x2e,
0x2,0x6f,0x2e,
0x2,0x70,0x2e,
0x2,0x71,0x2e,
0x2,0x72,0x2e,
0x2,0x73,0x2e,
0x2,0x74,0x2e,
0x2,0x75,0x2e,
0x2,0x76,0x2e,
0x2,0x77,0x2e,
0x2,0x78,0x2e,
0x2,0x79,0x2e,
0x2,0x7a,0x2e,
0x2,0xc6b4,0x20de,
0x2,0xb2f5,0x20de,
0x2,0xc8fc,0x20de,
0x2,0xba85,0x20de,
0x2,0xb300,0x20de,
0x2,0xd615,0x20de,
0x2,0xbd80,0x20de,
0x2,0xc804,0x20de,
0x2,0xc811,0x20de,
0x2,0xc218,0x20de,
0x2,0xb3d9,0x20de,
0x2,0xbe44,0x20de,
0x2,0xbc18,0x20de,
0x2,0xc790,0x20de,
0x2,0xd0c0,0x20de,
0x2,0xac10,0x20de,
0x2,0xc57d,0x20de,
0x2,0xc778,0x20de,
0x2,0xb73b,0x20de,
0x2,0x5370,0x20de,
0x2,0x8a3b,0x20de,
0x2,0xc608,0x20de,
0x2,0x611f,0x20de,
0x2,0x51a0,0x20de,
0x2,0x7b54,0x20de,
0x2,0x4ee3,0x20de,
0x2,0x982d,0x20de,
0x2,0x52d5,0x20de,
0x2,0x540d,0x20de,
0x2,0x76ee,0x20de,
0x2,0x53cd,0x20de,
0x2,0x88dc,0x20de,
0x2,0x672c,0x20de,
0x2,0x526f,0x20de,
0x2,0x5e8f,0x20de,
0x2,0x9023,0x20de,
0x2,0x5f71,0x20de,
0x2,0x4f8b,0x20de,
0x2,0x6e90,0x20de,
0x2,0x5b50,0x20de,
0x2,0x524d,0x20de,
0x2,0x7bc0,0x20de,
0x2,0x63a5,0x20de,
0x2,0x52a9,0x20de,
0x2,0x6307,0x20de,
0x2,0x4ed6,0x20de,
0x2,0x6d3e,0x20de,
0x2,0x5f62,0x20de,
0x2,0xc870,0x20de,
0x2,0xbb38,0x20de,
0x2,0xb2f5,0x20de,
0x2,0xc8fc,0x20de,
0x2,0xb73b,0x20de,
0x2,0x8a3b,0x20de,
0x2,0xad50,0x20de,
0x2,0xc5ed,0x20de,
0x2,0xc74c,0x20de,
0x2,0xc815,0x20de,
0x2,0xd574,0x20de,
0x2,0xc608,0x20de,
0x2,0xc874,0x20dd,
0x2,0xb77c,0x20dd,
0x2,0xb9c8,0x20dd,
0x2,0xbc14,0x20dd,
0x2,0xc0ac,0x20dd,
0x2,0xc544,0x20dd,
0x2,0xc790,0x20dd,
0x2,0xcc28,0x20dd,
0x2,0xce74,0x20dd,
0x2,0xd0c0,0x20dd,
0x2,0xd30c,0x20dd,
0x2,0xb192,0x20dd,
0x2,0xb0ae,0x20dd,
0x2,0xba85,0x20dd,
0x2,0xb300,0x20dd,
0x2,0xd615,0x20dd,
0x2,0xbd80,0x20dd,
0x2,0xc804,0x20dd,
0x2,0xc811,0x20dd,
0x2,0xc218,0x20dd,
0x2,0xb3d9,0x20dd,
0x2,0xbe44,0x20dd,
0x2,0xac8c,0x20dd,
0x2,0xbc18,0x20dd,
0x2,0xc18d,0x20dd,
0x2,0xc778,0x20dd,
0x2,0xbcf8,0x20dd,
0x2,0xc57d,0x20dd,
0x2,0xc219,0x20dd,
0x2,0xc720,0x20dd,
0x2,0xad00,0x20dd,
0x2,0x51a0,0x20dd,
0x4,0x28,0x32,0x31,0x29,
0x4,0x28,0x32,0x32,0x29,
0x4,0x28,0x32,0x33,0x29,
0x4,0x28,0x32,0x34,0x29,
0x4,0x28,0x32,0x35,0x29,
0x4,0x28,0x32,0x36,0x29,
0x2,0xc870,0x20dd,
0x2,0xad6d,0x20dd,
0x2,0xac10,0x20dd,
0x2,0x5370,0x20dd,
0x2,0x8863,0x20dd,
0x2,0x672b,0x20dd,
0x2,0xac70,0x20dd,
0x2,0xb2f5,0x20dd,
0x2,0xbcc0,0x20dd,
0x2,0xc0c1,0x20dd,
0x2,0xc13c,0x20dd,
0x2,0xc2e0,0x20dd,
0x2,0xc5ec,0x20dd,
0x2,0xc608,0x20dd,
0x2,0xc6d0,0x20dd,
0x2,0xc791,0x20dd,
0x2,0xc900,0x20dd,
0x2,0xd0b9,0x20dd,
0x2,0xc678,0x20dd,
0x2,0xd65c,0x20dd,
0x2,0xac04,0x20dd,
0x2,0xac19,0x20dd,
0x2,0xc2e4,0x20dd,
0x2,0x611f,0x20dd,
0x2,0x6163,0x20dd,
0x2,0x4ee3,0x20dd,
0x2,0x52d5,0x20dd,
0x2,0x53cd,0x20dd,
0x2,0x526f,0x20dd,
0x2,0x81ea,0x20dd,
0x2,0x524d,0x20dd,
0x2,0x96fb,0x20dd,
0x2,0x63a5,0x20dd,
0x2,0x52a9,0x20dd,
0x2,0x6ce8,0x20dd,
0x2,0x53c3,0x20dd,
0x2,0x672c,0x20dd,
0x2,0x65b0,0x20dd,
0x2,0x73fe,0x20dd,
0x2,0x5f62,0x20dd,
0x2,0x9593,0x20dd,
0x2,0x570b,0x20dd,
0x2,0x4ed6,0x20dd,
0x2,0xbe60,0x20dd,
0x2,0xc2dc,0x20dd,
0x2,0xc785,0x20dd,
0x2,0xc73c,0x20dd,
0x2,0xc74c,0x20dd,
0x2,0xc9c1,0x20dd,
0x2,0xd45c,0x20dd,
0x2,0xac00,0x20dd,
0x2,0xb098,0x20dd,
0x2,0xb2e4,0x20dd,
0x2,0xd558,0x20dd,
0x2,0xb9c8,0x20dd,
0x2,0xbc14,0x20dd,
0x2,0xc0ac,0x20dd,
0x2,0xc544,0x20dd,
0x2,0xc790,0x20dd,
0x2,0xcc28,0x20dd,
0x2,0xce74,0x20dd,
0x2,0xd0c0,0x20dd,
0x2,0xd30c,0x20dd,
0x2,0xd558,0x20dd,
0x2,0xbe44,0x20dd,
0x2,0xb2f5,0x20dd,
0x2,0xbe60,0x20dd,
0x2,0xbcf8,0x20dd,
0x2,0xb2e8,0x20dd,
0x2,0xc13c,0x20dd,
0x2,0xc2dc,0x20dd,
0x2,0xc5ec,0x20dd,
0x2,0xc608,0x20dd,
0x2,0xc73c,0x20dd,
0x2,0xc74c,0x20dd,
0x2,0xc785,0x20dd,
0x2,0xc81c,0x20dd,
0x2,0xc874,0x20dd,
0x2,0xc900,0x20dd,
0x2,0xd45c,0x20dd,
0x2,0xd574,0x20dd,
0x2,0xb290,0x20dd,
0x2,0xb192,0x20dd,
0x2,0xb0ae,0x20dd,
0x2,0xbc18,0x20dd,
0x2,0xac00,0x20dd,
0x2,0xb098,0x20dd,
0x2,0xb2e4,0x20dd,
0x2,0xb77c,0x20dd,
0x2,0xc678,0x20dd,
0x4,0x28,0x32,0x37,0x29,
0x4,0x28,0x32,0x38,0x29,
0x4,0x28,0x32,0x39,0x29,
0x4,0x28,0x33,0x30,0x29,
0x2,0xb2e8,0x20dd,
0x2,0xcc38,0x20dd,
0x2,0xc18c,0x20dd,
0x2,0xc911,0x20dd,
0x2,0xc77c,0x20dd,
0x2,0xc774,0x20dd,
0x2,0xd734,0x20dd,
0x2,0x31,0x20de,
0x2,0x32,0x20de,
0x2,0x33,0x20de,
0x2,0x34,0x20de,
0x2,0x35,0x20de,
0x2,0x36,0x20de,
0x2,0x37,0x20de,
0x2,0x38,0x20de,
0x2,0x39,0x20de,
0x4,0x5b,0x31,0x30,0x5d,
0x4,0x5b,0x31,0x31,0x5d,
0x4,0x5b,0x31,0x32,0x5d,
0x4,0x5b,0x31,0x33,0x5d,
0x4,0x5b,0x31,0x34,0x5d,
0x4,0x5b,0x31,0x35,0x5d,
0x4,0x5b,0x31,0x36,0x5d,
0x4,0x5b,0x31,0x37,0x5d,
0x4,0x5b,0x31,0x38,0x5d,
0x4,0x5b,0x31,0x39,0x5d,
0x4,0x5b,0x32,0x30,0x5d,
0x2,0x4e00,0x20de,
0x2,0x4e8c,0x20de,
0x2,0x4e09,0x20de,
0x2,0x56db,0x20de,
0x2,0x4e94,0x20de,
0x2,0x516d,0x20de,
0x2,0x4e03,0x20de,
0x2,0x516b,0x20de,
0x2,0x4e5d,0x20de,
0x2,0x5341,0x20de,
0x4,0x5b,0x5341,0x4e00,0x5d,
0x4,0x5b,0x5341,0x4e8c,0x5d,
0x4,0x5b,0x5341,0x4e09,0x5d,
0x4,0x5b,0x5341,0x56db,0x5d,
0x4,0x5b,0x5341,0x4e94,0x5d,
0x4,0x5b,0x5341,0x516d,0x5d,
0x4,0x5b,0x5341,0x4e03,0x5d,
0x4,0x5b,0x5341,0x516b,0x5d,
0x4,0x5b,0x5341,0x4e5d,0x5d,
0x4,0x5b,0x4e8c,0x5341,0x5d,
0x2,0x4e00,0x20de,
0x2,0x4e8c,0x20de,
0x2,0x4e09,0x20de,
0x2,0x56db,0x20de,
0x2,0x4e94,0x20de,
0x2,0x516d,0x20de,
0x2,0x4e03,0x20de,
0x2,0x516b,0x20de,
0x2,0x4e5d,0x20de,
0x2,0x5341,0x20de,
0x4,0x5b,0x5341,0x4e00,0x5d,
0x4,0x5b,0x5341,0x4e8c,0x5d,
0x4,0x5b,0x5341,0x4e09,0x5d,
0x4,0x5b,0x5341,0x56db,0x5d,
0x4,0x5b,0x5341,0x4e94,0x5d,
0x4,0x5b,0x5341,0x516d,0x5d,
0x4,0x5b,0x5341,0x4e03,0x5d,
0x4,0x5b,0x5341,0x516b,0x5d,
0x4,0x5b,0x5341,0x4e5d,0x5d,
0x4,0x5b,0x4e8c,0x5341,0x5d,
0x2,0x65e5,0x20de,
0x2,0x6708,0x20de,
0x2,0x706b,0x20de,
0x2,0x6c34,0x20de,
0x2,0x6728,0x20de,
0x2,0x91d1,0x20de,
0x2,0x571f,0x20de,
0x2,0x65e5,0x20de,
0x2,0x6708,0x20de,
0x2,0x706b,0x20de,
0x2,0x6c34,0x20de,
0x2,0x6728,0x20de,
0x2,0x91d1,0x20de,
0x2,0x571f,0x20de,
};

static pdf_cmap cmap_Adobe_Korea1_UCS2 = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "Adobe-Korea1-UCS2",
	/* usecmap */ "", NULL,
	/* wmode */ 0,
	/* codespaces */ 1, {
		{ 2, 0x0000, 0xffff },
	},
	8353, 8353, (pdf_range*)cmap_Adobe_Korea1_UCS2_ranges,
	0, 0, NULL, /* xranges */
	537, 537, (pdf_mrange*)cmap_Adobe_Korea1_UCS2_mranges,
	1848, 1848, (int*)cmap_Adobe_Korea1_UCS2_table,
	0, 0, 0, NULL /* splay tree */
};
