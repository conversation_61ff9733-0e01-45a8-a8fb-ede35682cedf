/* This is an automatically generated file. Do not edit. */

/* 90msp-RKSJ-H */

static const pdf_range cmap_90msp_RKSJ_H_ranges[] = {
{0x20,0x7e,0x1},
{0xa0,0xdf,0x146},
{0x8140,0x817e,0x279},
{0x8180,0x81ac,0x2b8},
{0x81b8,0x81bf,0x2e5},
{0x81c8,0x81ce,0x2ed},
{0x81da,0x81e8,0x2f4},
{0x81f0,0x81f7,0x303},
{0x81fc,0x81fc,0x30b},
{0x824f,0x8258,0x30c},
{0x8260,0x8279,0x316},
{0x8281,0x829a,0x330},
{0x829f,0x82f1,0x34a},
{0x8340,0x837e,0x39d},
{0x8380,0x8396,0x3dc},
{0x839f,0x83b6,0x3f3},
{0x83bf,0x83d6,0x40b},
{0x8440,0x8460,0x423},
{0x8470,0x847e,0x444},
{0x8480,0x8491,0x453},
{0x849f,0x849f,0x1d37},
{0x84a0,0x84a0,0x1d39},
{0x84a1,0x84a1,0x1d43},
{0x84a2,0x84a2,0x1d47},
{0x84a3,0x84a3,0x1d4f},
{0x84a4,0x84a4,0x1d4b},
{0x84a5,0x84a5,0x1d53},
{0x84a6,0x84a6,0x1d63},
{0x84a7,0x84a7,0x1d5b},
{0x84a8,0x84a8,0x1d6b},
{0x84a9,0x84a9,0x1d73},
{0x84aa,0x84aa,0x1d38},
{0x84ab,0x84ab,0x1d3a},
{0x84ac,0x84ac,0x1d46},
{0x84ad,0x84ad,0x1d4a},
{0x84ae,0x84ae,0x1d52},
{0x84af,0x84af,0x1d4e},
{0x84b0,0x84b0,0x1d5a},
{0x84b1,0x84b1,0x1d6a},
{0x84b2,0x84b2,0x1d62},
{0x84b3,0x84b3,0x1d72},
{0x84b4,0x84b4,0x1d82},
{0x84b5,0x84b5,0x1d57},
{0x84b6,0x84b6,0x1d66},
{0x84b7,0x84b7,0x1d5f},
{0x84b8,0x84b8,0x1d6e},
{0x84b9,0x84b9,0x1d76},
{0x84ba,0x84ba,0x1d54},
{0x84bb,0x84bb,0x1d67},
{0x84bc,0x84bc,0x1d5c},
{0x84bd,0x84bd,0x1d6f},
{0x84be,0x84be,0x1d79},
{0x8740,0x875d,0x1d83},
{0x875f,0x8760,0x1da1},
{0x8761,0x8761,0x1f66},
{0x8762,0x8762,0x1da4},
{0x8763,0x8763,0x1f68},
{0x8764,0x8764,0x1da6},
{0x8765,0x8765,0x1f6a},
{0x8766,0x8767,0x1da8},
{0x8768,0x8768,0x1f6c},
{0x8769,0x876a,0x1dab},
{0x876b,0x876b,0x1f6b},
{0x876c,0x876d,0x1dae},
{0x876e,0x876e,0x1f6f},
{0x876f,0x8775,0x1db1},
{0x877e,0x877e,0x2083},
{0x8780,0x8783,0x1db8},
{0x8784,0x8784,0x1f77},
{0x8785,0x878f,0x1dbd},
{0x8790,0x8790,0x2fa},
{0x8791,0x8791,0x2f9},
{0x8792,0x8792,0x301},
{0x8793,0x8799,0x1dc8},
{0x879a,0x879a,0x300},
{0x879b,0x879c,0x1dcf},
{0x889f,0x88fc,0x465},
{0x8940,0x897e,0x4c3},
{0x8980,0x89fc,0x502},
{0x8a40,0x8a7e,0x57f},
{0x8a80,0x8afc,0x5be},
{0x8b40,0x8b7e,0x63b},
{0x8b80,0x8bfc,0x67a},
{0x8c40,0x8c7e,0x6f7},
{0x8c80,0x8cfc,0x736},
{0x8d40,0x8d7e,0x7b3},
{0x8d80,0x8dfc,0x7f2},
{0x8e40,0x8e7e,0x86f},
{0x8e80,0x8efc,0x8ae},
{0x8f40,0x8f7e,0x92b},
{0x8f80,0x8ffc,0x96a},
{0x9040,0x907e,0x9e7},
{0x9080,0x90fc,0xa26},
{0x9140,0x917e,0xaa3},
{0x9180,0x91fc,0xae2},
{0x9240,0x927e,0xb5f},
{0x9280,0x92fc,0xb9e},
{0x9340,0x937e,0xc1b},
{0x9380,0x93fc,0xc5a},
{0x9440,0x947e,0xcd7},
{0x9480,0x94fc,0xd16},
{0x9540,0x957e,0xd93},
{0x9580,0x95fc,0xdd2},
{0x9640,0x967e,0xe4f},
{0x9680,0x96fc,0xe8e},
{0x9740,0x977e,0xf0b},
{0x9780,0x97fc,0xf4a},
{0x9840,0x9872,0xfc7},
{0x989f,0x98fc,0xffa},
{0x9940,0x997e,0x1058},
{0x9980,0x99fc,0x1097},
{0x9a40,0x9a7e,0x1114},
{0x9a80,0x9afc,0x1153},
{0x9b40,0x9b7e,0x11d0},
{0x9b80,0x9bfc,0x120f},
{0x9c40,0x9c7e,0x128c},
{0x9c80,0x9cfc,0x12cb},
{0x9d40,0x9d7e,0x1348},
{0x9d80,0x9dfc,0x1387},
{0x9e40,0x9e7e,0x1404},
{0x9e80,0x9efc,0x1443},
{0x9f40,0x9f7e,0x14c0},
{0x9f80,0x9ffc,0x14ff},
{0xe040,0xe07e,0x157c},
{0xe080,0xe0fc,0x15bb},
{0xe140,0xe17e,0x1638},
{0xe180,0xe1fc,0x1677},
{0xe240,0xe27e,0x16f4},
{0xe280,0xe2fc,0x1733},
{0xe340,0xe37e,0x17b0},
{0xe380,0xe3fc,0x17ef},
{0xe440,0xe47e,0x186c},
{0xe480,0xe4fc,0x18ab},
{0xe540,0xe57e,0x1928},
{0xe580,0xe5fc,0x1967},
{0xe640,0xe67e,0x19e4},
{0xe680,0xe6fc,0x1a23},
{0xe740,0xe77e,0x1aa0},
{0xe780,0xe7fc,0x1adf},
{0xe840,0xe87e,0x1b5c},
{0xe880,0xe8fc,0x1b9b},
{0xe940,0xe97e,0x1c18},
{0xe980,0xe9fc,0x1c57},
{0xea40,0xea7e,0x1cd4},
{0xea80,0xeaa2,0x1d13},
{0xeaa3,0xeaa4,0x205c},
{0xed40,0xed7e,0x20a7},
{0xed80,0xedb3,0x20e6},
{0xedb4,0xedb4,0x7c9},
{0xedb5,0xedfc,0x211a},
{0xee40,0xee7e,0x2162},
{0xee80,0xeeec,0x21a1},
{0xeeef,0xeef8,0x1f9c},
{0xeef9,0xeef9,0x2ef},
{0xeefa,0xeefc,0x1f45},
{0xfa40,0xfa49,0x1f9c},
{0xfa4a,0xfa53,0x1d97},
{0xfa54,0xfa54,0x2ef},
{0xfa55,0xfa57,0x1f45},
{0xfa58,0xfa58,0x1dc2},
{0xfa59,0xfa59,0x1dba},
{0xfa5a,0xfa5a,0x1f77},
{0xfa5b,0xfa5b,0x300},
{0xfa5c,0xfa7e,0x20a7},
{0xfa80,0xfacf,0x20ca},
{0xfad0,0xfad0,0x7c9},
{0xfad1,0xfafc,0x211a},
{0xfb40,0xfb7e,0x2146},
{0xfb80,0xfbfc,0x2185},
{0xfc40,0xfc4b,0x2202},
};

static pdf_cmap cmap_90msp_RKSJ_H = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "90msp-RKSJ-H",
	/* usecmap */ "", NULL,
	/* wmode */ 0,
	/* codespaces */ 4, {
		{ 1, 0x00, 0x80 },
		{ 2, 0x8140, 0x9ffc },
		{ 1, 0xa0, 0xdf },
		{ 2, 0xe040, 0xfcfc },
	},
	170, 170, (pdf_range*)cmap_90msp_RKSJ_H_ranges,
	0, 0, NULL, /* xranges */
	0, 0, NULL, /* mranges */
	0, 0, NULL, /* table */
	0, 0, 0, NULL /* splay tree */
};
