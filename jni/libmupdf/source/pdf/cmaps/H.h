/* This is an automatically generated file. Do not edit. */

/* H */

static const pdf_range cmap_H_ranges[] = {
{0x2121,0x217e,0x279},
{0x2221,0x222e,0x2d7},
{0x223a,0x2241,0x2e5},
{0x224a,0x2250,0x2ed},
{0x225c,0x226a,0x2f4},
{0x2272,0x2279,0x303},
{0x227e,0x227e,0x30b},
{0x2330,0x2339,0x30c},
{0x2341,0x235a,0x316},
{0x2361,0x237a,0x330},
{0x2421,0x2473,0x34a},
{0x2521,0x2576,0x39d},
{0x2621,0x2638,0x3f3},
{0x2641,0x2658,0x40b},
{0x2721,0x2741,0x423},
{0x2751,0x2771,0x444},
{0x2821,0x2821,0x1d37},
{0x2822,0x2822,0x1d39},
{0x2823,0x2823,0x1d43},
{0x2824,0x2824,0x1d47},
{0x2825,0x2825,0x1d4f},
{0x2826,0x2826,0x1d4b},
{0x2827,0x2827,0x1d53},
{0x2828,0x2828,0x1d63},
{0x2829,0x2829,0x1d5b},
{0x282a,0x282a,0x1d6b},
{0x282b,0x282b,0x1d73},
{0x282c,0x282c,0x1d38},
{0x282d,0x282d,0x1d3a},
{0x282e,0x282e,0x1d46},
{0x282f,0x282f,0x1d4a},
{0x2830,0x2830,0x1d52},
{0x2831,0x2831,0x1d4e},
{0x2832,0x2832,0x1d5a},
{0x2833,0x2833,0x1d6a},
{0x2834,0x2834,0x1d62},
{0x2835,0x2835,0x1d72},
{0x2836,0x2836,0x1d82},
{0x2837,0x2837,0x1d57},
{0x2838,0x2838,0x1d66},
{0x2839,0x2839,0x1d5f},
{0x283a,0x283a,0x1d6e},
{0x283b,0x283b,0x1d76},
{0x283c,0x283c,0x1d54},
{0x283d,0x283d,0x1d67},
{0x283e,0x283e,0x1d5c},
{0x283f,0x283f,0x1d6f},
{0x2840,0x2840,0x1d79},
{0x3021,0x307e,0x465},
{0x3121,0x317e,0x4c3},
{0x3221,0x327e,0x521},
{0x3321,0x337e,0x57f},
{0x3421,0x347e,0x5dd},
{0x3521,0x357e,0x63b},
{0x3621,0x367e,0x699},
{0x3721,0x377e,0x6f7},
{0x3821,0x387e,0x755},
{0x3921,0x397e,0x7b3},
{0x3a21,0x3a7e,0x811},
{0x3b21,0x3b7e,0x86f},
{0x3c21,0x3c7e,0x8cd},
{0x3d21,0x3d7e,0x92b},
{0x3e21,0x3e7e,0x989},
{0x3f21,0x3f7e,0x9e7},
{0x4021,0x407e,0xa45},
{0x4121,0x417e,0xaa3},
{0x4221,0x427e,0xb01},
{0x4321,0x437e,0xb5f},
{0x4421,0x447e,0xbbd},
{0x4521,0x457e,0xc1b},
{0x4621,0x467e,0xc79},
{0x4721,0x477e,0xcd7},
{0x4821,0x487e,0xd35},
{0x4921,0x497e,0xd93},
{0x4a21,0x4a7e,0xdf1},
{0x4b21,0x4b7e,0xe4f},
{0x4c21,0x4c7e,0xead},
{0x4d21,0x4d7e,0xf0b},
{0x4e21,0x4e7e,0xf69},
{0x4f21,0x4f53,0xfc7},
{0x5021,0x507e,0xffa},
{0x5121,0x517e,0x1058},
{0x5221,0x527e,0x10b6},
{0x5321,0x537e,0x1114},
{0x5421,0x547e,0x1172},
{0x5521,0x557e,0x11d0},
{0x5621,0x567e,0x122e},
{0x5721,0x577e,0x128c},
{0x5821,0x587e,0x12ea},
{0x5921,0x597e,0x1348},
{0x5a21,0x5a7e,0x13a6},
{0x5b21,0x5b7e,0x1404},
{0x5c21,0x5c7e,0x1462},
{0x5d21,0x5d7e,0x14c0},
{0x5e21,0x5e7e,0x151e},
{0x5f21,0x5f7e,0x157c},
{0x6021,0x607e,0x15da},
{0x6121,0x617e,0x1638},
{0x6221,0x627e,0x1696},
{0x6321,0x637e,0x16f4},
{0x6421,0x647e,0x1752},
{0x6521,0x657e,0x17b0},
{0x6621,0x667e,0x180e},
{0x6721,0x677e,0x186c},
{0x6821,0x687e,0x18ca},
{0x6921,0x697e,0x1928},
{0x6a21,0x6a7e,0x1986},
{0x6b21,0x6b7e,0x19e4},
{0x6c21,0x6c7e,0x1a42},
{0x6d21,0x6d7e,0x1aa0},
{0x6e21,0x6e7e,0x1afe},
{0x6f21,0x6f7e,0x1b5c},
{0x7021,0x707e,0x1bba},
{0x7121,0x717e,0x1c18},
{0x7221,0x727e,0x1c76},
{0x7321,0x737e,0x1cd4},
{0x7421,0x7424,0x1d32},
{0x7425,0x7426,0x205c},
};

static pdf_cmap cmap_H = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "H",
	/* usecmap */ "", NULL,
	/* wmode */ 0,
	/* codespaces */ 1, {
		{ 2, 0x2121, 0x7e7e },
	},
	118, 118, (pdf_range*)cmap_H_ranges,
	0, 0, NULL, /* xranges */
	0, 0, NULL, /* mranges */
	0, 0, NULL, /* table */
	0, 0, 0, NULL /* splay tree */
};
