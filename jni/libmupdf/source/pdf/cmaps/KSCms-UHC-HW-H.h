/* This is an automatically generated file. Do not edit. */

/* KSCms-UHC-HW-H */

static const pdf_range cmap_KSCms_UHC_HW_H_ranges[] = {
{0x20,0x7e,0x1f9e},
{0x8141,0x815a,0x2475},
{0x8161,0x817a,0x248f},
{0x8181,0x81fe,0x24a9},
{0x8241,0x825a,0x2527},
{0x8261,0x827a,0x2541},
{0x8281,0x82fe,0x255b},
{0x8341,0x835a,0x25d9},
{0x8361,0x837a,0x25f3},
{0x8381,0x83fe,0x260d},
{0x8441,0x845a,0x268b},
{0x8461,0x847a,0x26a5},
{0x8481,0x84fe,0x26bf},
{0x8541,0x855a,0x273d},
{0x8561,0x857a,0x2757},
{0x8581,0x85fe,0x2771},
{0x8641,0x865a,0x27ef},
{0x8661,0x867a,0x2809},
{0x8681,0x86fe,0x2823},
{0x8741,0x875a,0x28a1},
{0x8761,0x877a,0x28bb},
{0x8781,0x87fe,0x28d5},
{0x8841,0x885a,0x2953},
{0x8861,0x887a,0x296d},
{0x8881,0x88fe,0x2987},
{0x8941,0x895a,0x2a05},
{0x8961,0x897a,0x2a1f},
{0x8981,0x89fe,0x2a39},
{0x8a41,0x8a5a,0x2ab7},
{0x8a61,0x8a7a,0x2ad1},
{0x8a81,0x8afe,0x2aeb},
{0x8b41,0x8b5a,0x2b69},
{0x8b61,0x8b7a,0x2b83},
{0x8b81,0x8bfe,0x2b9d},
{0x8c41,0x8c5a,0x2c1b},
{0x8c61,0x8c7a,0x2c35},
{0x8c81,0x8cfe,0x2c4f},
{0x8d41,0x8d5a,0x2ccd},
{0x8d61,0x8d7a,0x2ce7},
{0x8d81,0x8dfe,0x2d01},
{0x8e41,0x8e5a,0x2d7f},
{0x8e61,0x8e7a,0x2d99},
{0x8e81,0x8efe,0x2db3},
{0x8f41,0x8f5a,0x2e31},
{0x8f61,0x8f7a,0x2e4b},
{0x8f81,0x8ffe,0x2e65},
{0x9041,0x905a,0x2ee3},
{0x9061,0x907a,0x2efd},
{0x9081,0x90fe,0x2f17},
{0x9141,0x915a,0x2f95},
{0x9161,0x917a,0x2faf},
{0x9181,0x91fe,0x2fc9},
{0x9241,0x925a,0x3047},
{0x9261,0x927a,0x3061},
{0x9281,0x92fe,0x307b},
{0x9341,0x935a,0x30f9},
{0x9361,0x937a,0x3113},
{0x9381,0x93fe,0x312d},
{0x9441,0x945a,0x31ab},
{0x9461,0x947a,0x31c5},
{0x9481,0x94fe,0x31df},
{0x9541,0x955a,0x325d},
{0x9561,0x957a,0x3277},
{0x9581,0x95fe,0x3291},
{0x9641,0x965a,0x330f},
{0x9661,0x967a,0x3329},
{0x9681,0x96fe,0x3343},
{0x9741,0x975a,0x33c1},
{0x9761,0x977a,0x33db},
{0x9781,0x97fe,0x33f5},
{0x9841,0x985a,0x3473},
{0x9861,0x987a,0x348d},
{0x9881,0x98fe,0x34a7},
{0x9941,0x995a,0x3525},
{0x9961,0x997a,0x353f},
{0x9981,0x99fe,0x3559},
{0x9a41,0x9a5a,0x35d7},
{0x9a61,0x9a7a,0x35f1},
{0x9a81,0x9afe,0x360b},
{0x9b41,0x9b5a,0x3689},
{0x9b61,0x9b7a,0x36a3},
{0x9b81,0x9bfe,0x36bd},
{0x9c41,0x9c5a,0x373b},
{0x9c61,0x9c7a,0x3755},
{0x9c81,0x9cfe,0x376f},
{0x9d41,0x9d5a,0x37ed},
{0x9d61,0x9d7a,0x3807},
{0x9d81,0x9dfe,0x3821},
{0x9e41,0x9e5a,0x389f},
{0x9e61,0x9e7a,0x38b9},
{0x9e81,0x9efe,0x38d3},
{0x9f41,0x9f5a,0x3951},
{0x9f61,0x9f7a,0x396b},
{0x9f81,0x9ffe,0x3985},
{0xa041,0xa05a,0x3a03},
{0xa061,0xa07a,0x3a1d},
{0xa081,0xa0fe,0x3a37},
{0xa141,0xa15a,0x3ab5},
{0xa161,0xa17a,0x3acf},
{0xa181,0xa1a0,0x3ae9},
{0xa1a1,0xa1fe,0x65},
{0xa241,0xa25a,0x3b09},
{0xa261,0xa27a,0x3b23},
{0xa281,0xa2a0,0x3b3d},
{0xa2a1,0xa2e5,0xc3},
{0xa341,0xa35a,0x3b5d},
{0xa361,0xa37a,0x3b77},
{0xa381,0xa3a0,0x3b91},
{0xa3a1,0xa3fe,0x108},
{0xa441,0xa45a,0x3bb1},
{0xa461,0xa47a,0x3bcb},
{0xa481,0xa4a0,0x3be5},
{0xa4a1,0xa4d3,0x166},
{0xa4d5,0xa4fe,0x199},
{0xa541,0xa55a,0x3c05},
{0xa561,0xa57a,0x3c1f},
{0xa581,0xa5a0,0x3c39},
{0xa5a1,0xa5aa,0x1c3},
{0xa5b0,0xa5b9,0x1cd},
{0xa5c1,0xa5d8,0x1d7},
{0xa5e1,0xa5f8,0x1ef},
{0xa641,0xa65a,0x3c59},
{0xa661,0xa67a,0x3c73},
{0xa681,0xa6a0,0x3c8d},
{0xa6a1,0xa6e4,0x207},
{0xa741,0xa75a,0x3cad},
{0xa761,0xa77a,0x3cc7},
{0xa781,0xa7a0,0x3ce1},
{0xa7a1,0xa7ef,0x24b},
{0xa841,0xa85a,0x3d01},
{0xa861,0xa87a,0x3d1b},
{0xa881,0xa8a0,0x3d35},
{0xa8a1,0xa8a4,0x29a},
{0xa8a6,0xa8a6,0x29e},
{0xa8a8,0xa8af,0x29f},
{0xa8b1,0xa8fe,0x2a7},
{0xa941,0xa95a,0x3d55},
{0xa961,0xa97a,0x3d6f},
{0xa981,0xa9a0,0x3d89},
{0xa9a1,0xa9fe,0x2f5},
{0xaa41,0xaa5a,0x3da9},
{0xaa61,0xaa7a,0x3dc3},
{0xaa81,0xaaa0,0x3ddd},
{0xaaa1,0xaaf3,0x353},
{0xab41,0xab5a,0x3dfd},
{0xab61,0xab7a,0x3e17},
{0xab81,0xaba0,0x3e31},
{0xaba1,0xabf6,0x3a6},
{0xac41,0xac5a,0x3e51},
{0xac61,0xac7a,0x3e6b},
{0xac81,0xaca0,0x3e85},
{0xaca1,0xacc1,0x3fc},
{0xacd1,0xacf1,0x41d},
{0xad41,0xad5a,0x3ea5},
{0xad61,0xad7a,0x3ebf},
{0xad81,0xada0,0x3ed9},
{0xae41,0xae5a,0x3ef9},
{0xae61,0xae7a,0x3f13},
{0xae81,0xaea0,0x3f2d},
{0xaf41,0xaf5a,0x3f4d},
{0xaf61,0xaf7a,0x3f67},
{0xaf81,0xafa0,0x3f81},
{0xb041,0xb05a,0x3fa1},
{0xb061,0xb07a,0x3fbb},
{0xb081,0xb0a0,0x3fd5},
{0xb0a1,0xb0fe,0x43e},
{0xb141,0xb15a,0x3ff5},
{0xb161,0xb17a,0x400f},
{0xb181,0xb1a0,0x4029},
{0xb1a1,0xb1fe,0x49c},
{0xb241,0xb25a,0x4049},
{0xb261,0xb27a,0x4063},
{0xb281,0xb2a0,0x407d},
{0xb2a1,0xb2fe,0x4fa},
{0xb341,0xb35a,0x409d},
{0xb361,0xb37a,0x40b7},
{0xb381,0xb3a0,0x40d1},
{0xb3a1,0xb3fe,0x558},
{0xb441,0xb45a,0x40f1},
{0xb461,0xb47a,0x410b},
{0xb481,0xb4a0,0x4125},
{0xb4a1,0xb4fe,0x5b6},
{0xb541,0xb55a,0x4145},
{0xb561,0xb57a,0x415f},
{0xb581,0xb5a0,0x4179},
{0xb5a1,0xb5fe,0x614},
{0xb641,0xb65a,0x4199},
{0xb661,0xb67a,0x41b3},
{0xb681,0xb6a0,0x41cd},
{0xb6a1,0xb6fe,0x672},
{0xb741,0xb75a,0x41ed},
{0xb761,0xb77a,0x4207},
{0xb781,0xb7a0,0x4221},
{0xb7a1,0xb7fe,0x6d0},
{0xb841,0xb85a,0x4241},
{0xb861,0xb87a,0x425b},
{0xb881,0xb8a0,0x4275},
{0xb8a1,0xb8fe,0x72e},
{0xb941,0xb95a,0x4295},
{0xb961,0xb97a,0x42af},
{0xb981,0xb9a0,0x42c9},
{0xb9a1,0xb9fe,0x78c},
{0xba41,0xba5a,0x42e9},
{0xba61,0xba7a,0x4303},
{0xba81,0xbaa0,0x431d},
{0xbaa1,0xbafe,0x7ea},
{0xbb41,0xbb5a,0x433d},
{0xbb61,0xbb7a,0x4357},
{0xbb81,0xbba0,0x4371},
{0xbba1,0xbbfe,0x848},
{0xbc41,0xbc5a,0x4391},
{0xbc61,0xbc7a,0x43ab},
{0xbc81,0xbca0,0x43c5},
{0xbca1,0xbcfe,0x8a6},
{0xbd41,0xbd5a,0x43e5},
{0xbd61,0xbd7a,0x43ff},
{0xbd81,0xbda0,0x4419},
{0xbda1,0xbdfe,0x904},
{0xbe41,0xbe5a,0x4439},
{0xbe61,0xbe7a,0x4453},
{0xbe81,0xbea0,0x446d},
{0xbea1,0xbefe,0x962},
{0xbf41,0xbf5a,0x448d},
{0xbf61,0xbf7a,0x44a7},
{0xbf81,0xbfa0,0x44c1},
{0xbfa1,0xbffe,0x9c0},
{0xc041,0xc05a,0x44e1},
{0xc061,0xc07a,0x44fb},
{0xc081,0xc0a0,0x4515},
{0xc0a1,0xc0fe,0xa1e},
{0xc141,0xc15a,0x4535},
{0xc161,0xc17a,0x454f},
{0xc181,0xc1a0,0x4569},
{0xc1a1,0xc1fe,0xa7c},
{0xc241,0xc25a,0x4589},
{0xc261,0xc27a,0x45a3},
{0xc281,0xc2a0,0x45bd},
{0xc2a1,0xc2fe,0xada},
{0xc341,0xc35a,0x45dd},
{0xc361,0xc37a,0x45f7},
{0xc381,0xc3a0,0x4611},
{0xc3a1,0xc3fe,0xb38},
{0xc441,0xc45a,0x4631},
{0xc461,0xc47a,0x464b},
{0xc481,0xc4a0,0x4665},
{0xc4a1,0xc4fe,0xb96},
{0xc541,0xc55a,0x4685},
{0xc561,0xc57a,0x469f},
{0xc581,0xc5a0,0x46b9},
{0xc5a1,0xc5fe,0xbf4},
{0xc641,0xc652,0x46d9},
{0xc6a1,0xc6fe,0xc52},
{0xc7a1,0xc7fe,0xcb0},
{0xc8a1,0xc8fe,0xd0e},
{0xcaa1,0xcafe,0xd6c},
{0xcba1,0xcbcf,0xdca},
{0xcbd0,0xcbd0,0x1014},
{0xcbd1,0xcbd5,0xdf9},
{0xcbd6,0xcbd6,0xe5e},
{0xcbd7,0xcbe6,0xdfe},
{0xcbe7,0xcbe7,0x1b8d},
{0xcbe8,0xcbfe,0xe0e},
{0xcca1,0xccfe,0xe25},
{0xcda1,0xcdce,0xe83},
{0xcdcf,0xcdcf,0xd84},
{0xcdd0,0xcde7,0xeb1},
{0xcde8,0xcde8,0x1edc},
{0xcde9,0xcdfe,0xec9},
{0xcea1,0xceac,0xedf},
{0xcead,0xcead,0xeda},
{0xceae,0xcefe,0xeeb},
{0xcfa1,0xcffa,0xf3c},
{0xcffb,0xcffb,0xf3e},
{0xcffc,0xcffe,0xf96},
{0xd0a1,0xd0a1,0xf99},
{0xd0a2,0xd0a2,0xf6a},
{0xd0a3,0xd0b7,0xf9a},
{0xd0b8,0xd0b8,0xf6a},
{0xd0b9,0xd0cf,0xfaf},
{0xd0d0,0xd0d0,0xe7c},
{0xd0d1,0xd0dc,0xfc6},
{0xd0dd,0xd0dd,0x1023},
{0xd0de,0xd0fe,0xfd2},
{0xd1a1,0xd1d3,0xff3},
{0xd1d4,0xd1d4,0x1116},
{0xd1d5,0xd1d5,0x103c},
{0xd1d6,0xd1d7,0x1026},
{0xd1d8,0xd1d8,0x1117},
{0xd1d9,0xd1da,0x1028},
{0xd1db,0xd1e0,0x1118},
{0xd1e1,0xd1e1,0x102a},
{0xd1e2,0xd1e2,0x16a8},
{0xd1e3,0xd1e5,0x111e},
{0xd1e6,0xd1e6,0x1122},
{0xd1e7,0xd1e7,0x102b},
{0xd1e8,0xd1eb,0x1123},
{0xd1ec,0xd1ec,0x102c},
{0xd1ed,0xd1ed,0x1127},
{0xd1ee,0xd1ee,0x102d},
{0xd1ef,0xd1f0,0x112a},
{0xd1f1,0xd1f1,0x102e},
{0xd1f2,0xd1f2,0x112c},
{0xd1f3,0xd1f5,0x102f},
{0xd1f6,0xd1f6,0x112f},
{0xd1f7,0xd1f9,0x1032},
{0xd1fa,0xd1fa,0x1133},
{0xd1fb,0xd1fb,0x1035},
{0xd1fc,0xd1fd,0x1136},
{0xd1fe,0xd1fe,0x1139},
{0xd2a1,0xd2a1,0x1036},
{0xd2a2,0xd2a3,0x113a},
{0xd2a4,0xd2a6,0x1037},
{0xd2a7,0xd2aa,0x113c},
{0xd2ab,0xd2ab,0x1143},
{0xd2ac,0xd2ac,0x103a},
{0xd2ad,0xd2ad,0x1144},
{0xd2ae,0xd2b1,0x103b},
{0xd2b2,0xd2b2,0x1148},
{0xd2b3,0xd2bd,0x103f},
{0xd2be,0xd2be,0x119f},
{0xd2bf,0xd2c1,0x104a},
{0xd2c2,0xd2c3,0x11a1},
{0xd2c4,0xd2c4,0x11a5},
{0xd2c5,0xd2c5,0x104d},
{0xd2c6,0xd2ca,0x11a6},
{0xd2cb,0xd2cb,0x11ac},
{0xd2cc,0xd2cc,0x104e},
{0xd2cd,0xd2ce,0x11ad},
{0xd2cf,0xd2d4,0x11b0},
{0xd2d5,0xd2d7,0x11b7},
{0xd2d8,0xd2d8,0x104f},
{0xd2d9,0xd2da,0x11bd},
{0xd2db,0xd2dd,0x1050},
{0xd2de,0xd2df,0x11c1},
{0xd2e0,0xd2e0,0x1053},
{0xd2e1,0xd2e1,0x11c3},
{0xd2e2,0xd2e2,0x11c6},
{0xd2e3,0xd2e3,0x1054},
{0xd2e4,0xd2e4,0x11d4},
{0xd2e5,0xd2e8,0x11d6},
{0xd2e9,0xd2ea,0x11db},
{0xd2eb,0xd2eb,0x11e0},
{0xd2ec,0xd2ef,0x1055},
{0xd2f0,0xd2f3,0x11fc},
{0xd2f4,0xd2f5,0x1201},
{0xd2f6,0xd2f6,0x1059},
{0xd2f7,0xd2f8,0x1203},
{0xd2f9,0xd2fe,0x105a},
{0xd3a1,0xd3fe,0x1060},
{0xd4a1,0xd4e5,0x10be},
{0xd4e6,0xd4e6,0x10de},
{0xd4e7,0xd4fb,0x1103},
{0xd4fc,0xd4fc,0x1028},
{0xd4fd,0xd4fe,0x1118},
{0xd5a1,0xd5a4,0x111a},
{0xd5a5,0xd5a5,0x16a8},
{0xd5a6,0xd5aa,0x111e},
{0xd5ab,0xd5ab,0x102b},
{0xd5ac,0xd5ad,0x1123},
{0xd5ae,0xd5ae,0x1060},
{0xd5af,0xd5fe,0x1125},
{0xd6a1,0xd6b7,0x1175},
{0xd6b8,0xd6b8,0x1047},
{0xd6b9,0xd6cc,0x118c},
{0xd6cd,0xd6cd,0x104c},
{0xd6ce,0xd6fe,0x11a0},
{0xd7a1,0xd7ca,0x11d1},
{0xd7cb,0xd7cb,0x15b0},
{0xd7cc,0xd7e3,0x11fb},
{0xd7e4,0xd7e4,0x1918},
{0xd7e5,0xd7fe,0x1213},
{0xd8a1,0xd8fe,0x122d},
{0xd9a1,0xd9fe,0x128b},
{0xdaa1,0xdafe,0x12e9},
{0xdba1,0xdbc4,0x1347},
{0xdbc5,0xdbc5,0x141f},
{0xdbc6,0xdbe3,0x136b},
{0xdbe4,0xdbe4,0x133a},
{0xdbe5,0xdbfe,0x1389},
{0xdca1,0xdca4,0x13a3},
{0xdca5,0xdca5,0x1d5e},
{0xdca6,0xdcfe,0x13a7},
{0xdda1,0xdda4,0x1400},
{0xdda5,0xdda5,0x13d7},
{0xdda6,0xddd4,0x1404},
{0xddd5,0xddd5,0x13f5},
{0xddd6,0xddf3,0x1433},
{0xddf4,0xddf4,0x1db7},
{0xddf5,0xddfe,0x1451},
{0xdea1,0xdefb,0x145b},
{0xdefc,0xdefc,0x15d0},
{0xdefd,0xdefd,0x14b6},
{0xdefe,0xdefe,0x14f7},
{0xdfa1,0xdfb2,0x14b7},
{0xdfb3,0xdfb3,0x1bab},
{0xdfb4,0xdfe0,0x14c9},
{0xdfe1,0xdfe1,0x14f2},
{0xdfe2,0xdfe7,0x14f6},
{0xdfe8,0xdfe8,0x156d},
{0xdfe9,0xdffe,0x14fc},
{0xe0a1,0xe0f0,0x1512},
{0xe0f1,0xe0f1,0x1771},
{0xe0f2,0xe0fe,0x1562},
{0xe1a1,0xe1ac,0x156f},
{0xe1ad,0xe1ad,0x1554},
{0xe1ae,0xe1ec,0x157b},
{0xe1ed,0xe1ed,0x14c5},
{0xe1ee,0xe1fe,0x15ba},
{0xe2a1,0xe2fe,0x15cb},
{0xe3a1,0xe3f4,0x1629},
{0xe3f5,0xe3f5,0x1b61},
{0xe3f6,0xe3fe,0x167d},
{0xe4a1,0xe4a1,0x1cca},
{0xe4a2,0xe4a8,0x1686},
{0xe4a9,0xe4a9,0x162e},
{0xe4aa,0xe4fe,0x168d},
{0xe5a1,0xe5ad,0x16e2},
{0xe5ae,0xe5ae,0x16f2},
{0xe5af,0xe5b0,0x16ef},
{0xe5b1,0xe5b2,0x1149},
{0xe5b3,0xe5b8,0x16f1},
{0xe5b9,0xe5b9,0x114b},
{0xe5ba,0xe5ba,0x16f7},
{0xe5bb,0xe5bc,0x114d},
{0xe5bd,0xe5c3,0x16f8},
{0xe5c4,0xe5c4,0x114f},
{0xe5c5,0xe5cd,0x16ff},
{0xe5ce,0xe5ce,0x1153},
{0xe5cf,0xe5cf,0x1708},
{0xe5d0,0xe5d0,0x1154},
{0xe5d1,0xe5d1,0x1709},
{0xe5d2,0xe5d2,0x1155},
{0xe5d3,0xe5d5,0x170a},
{0xe5d6,0xe5d6,0x1157},
{0xe5d7,0xe5f9,0x170d},
{0xe5fa,0xe5fb,0x115a},
{0xe5fc,0xe5fc,0x103f},
{0xe5fd,0xe5fd,0x1730},
{0xe5fe,0xe5fe,0x115c},
{0xe6a1,0xe6a1,0x115f},
{0xe6a2,0xe6a3,0x1731},
{0xe6a4,0xe6a4,0x1161},
{0xe6a5,0xe6a6,0x1733},
{0xe6a7,0xe6a7,0x1162},
{0xe6a8,0xe6ac,0x1735},
{0xe6ad,0xe6ad,0x1165},
{0xe6ae,0xe6ae,0x173a},
{0xe6af,0xe6b1,0x1167},
{0xe6b2,0xe6b2,0x173b},
{0xe6b3,0xe6b3,0x116a},
{0xe6b4,0xe6b6,0x173c},
{0xe6b7,0xe6b8,0x116b},
{0xe6b9,0xe6bb,0x173f},
{0xe6bc,0xe6bc,0x116f},
{0xe6bd,0xe6c3,0x1742},
{0xe6c4,0xe6c4,0x1040},
{0xe6c5,0xe6c5,0x1749},
{0xe6c6,0xe6c7,0x1171},
{0xe6c8,0xe6c9,0x174a},
{0xe6ca,0xe6ca,0x1041},
{0xe6cb,0xe6d1,0x174c},
{0xe6d2,0xe6d2,0x1174},
{0xe6d3,0xe6d5,0x1753},
{0xe6d6,0xe6d6,0x1175},
{0xe6d7,0xe6d8,0x1756},
{0xe6d9,0xe6d9,0x1176},
{0xe6da,0xe6db,0x1758},
{0xe6dc,0xe6dc,0x1042},
{0xe6dd,0xe6de,0x175a},
{0xe6df,0xe6df,0x1177},
{0xe6e0,0xe6e0,0x175c},
{0xe6e1,0xe6e1,0x1178},
{0xe6e2,0xe6e3,0x175d},
{0xe6e4,0xe6e4,0x117a},
{0xe6e5,0xe6e5,0x1179},
{0xe6e6,0xe6e6,0x117b},
{0xe6e7,0xe6e7,0x175f},
{0xe6e8,0xe6e8,0x117c},
{0xe6e9,0xe6e9,0x1760},
{0xe6ea,0xe6eb,0x117e},
{0xe6ec,0xe6ec,0x192f},
{0xe6ed,0xe6ee,0x1761},
{0xe6ef,0xe6ef,0x1181},
{0xe6f0,0xe6f0,0x1763},
{0xe6f1,0xe6f1,0x1182},
{0xe6f2,0xe6f2,0x1554},
{0xe6f3,0xe6f4,0x1764},
{0xe6f5,0xe6f5,0x1183},
{0xe6f6,0xe6f6,0x1043},
{0xe6f7,0xe6f7,0x1046},
{0xe6f8,0xe6f8,0x1766},
{0xe6f9,0xe6f9,0x1185},
{0xe6fa,0xe6fe,0x1767},
{0xe7a1,0xe7a1,0x1187},
{0xe7a2,0xe7a5,0x176c},
{0xe7a6,0xe7a6,0x1188},
{0xe7a7,0xe7a8,0x1770},
{0xe7a9,0xe7a9,0x1189},
{0xe7aa,0xe7aa,0x118b},
{0xe7ab,0xe7ab,0x1772},
{0xe7ac,0xe7ac,0x1047},
{0xe7ad,0xe7ad,0x118d},
{0xe7ae,0xe7af,0x1773},
{0xe7b0,0xe7b0,0x118e},
{0xe7b1,0xe7be,0x1775},
{0xe7bf,0xe7bf,0x118f},
{0xe7c0,0xe7c0,0x1783},
{0xe7c1,0xe7c1,0x1e67},
{0xe7c2,0xe7c5,0x1784},
{0xe7c6,0xe7c6,0x1191},
{0xe7c7,0xe7c7,0x1193},
{0xe7c8,0xe7ca,0x1788},
{0xe7cb,0xe7cb,0x1195},
{0xe7cc,0xe7cc,0x178b},
{0xe7cd,0xe7cd,0x1196},
{0xe7ce,0xe7ce,0x178c},
{0xe7cf,0xe7d0,0x1197},
{0xe7d1,0xe7d2,0x178d},
{0xe7d3,0xe7d3,0x119a},
{0xe7d4,0xe7de,0x178f},
{0xe7df,0xe7df,0x119c},
{0xe7e0,0xe7e3,0x179a},
{0xe7e4,0xe7e4,0x119d},
{0xe7e5,0xe7e5,0x179e},
{0xe7e6,0xe7e6,0x119e},
{0xe7e7,0xe7f6,0x179f},
{0xe7f7,0xe7f7,0x16a5},
{0xe7f8,0xe7fe,0x17af},
{0xe8a1,0xe8e6,0x17b6},
{0xe8e7,0xe8e8,0x11c7},
{0xe8e9,0xe8ef,0x17fc},
{0xe8f0,0xe8f0,0x11c9},
{0xe8f1,0xe8f1,0x1054},
{0xe8f2,0xe8f6,0x1803},
{0xe8f7,0xe8f7,0x11cb},
{0xe8f8,0xe8f8,0x1808},
{0xe8f9,0xe8f9,0x16a8},
{0xe8fa,0xe8fa,0x1809},
{0xe8fb,0xe8fb,0x11cc},
{0xe8fc,0xe8fd,0x180a},
{0xe8fe,0xe8fe,0x11cd},
{0xe9a1,0xe9a6,0x180c},
{0xe9a7,0xe9a7,0x11d0},
{0xe9a8,0xe9ab,0x1812},
{0xe9ac,0xe9ac,0x11d1},
{0xe9ad,0xe9cb,0x1816},
{0xe9cc,0xe9cc,0x11d3},
{0xe9cd,0xe9f6,0x1835},
{0xe9f7,0xe9f7,0x1f34},
{0xe9f8,0xe9fe,0x185f},
{0xeaa1,0xeac0,0x1866},
{0xeac1,0xeac1,0x17eb},
{0xeac2,0xeae4,0x1886},
{0xeae5,0xeae5,0x11e1},
{0xeae6,0xeaf3,0x18a9},
{0xeaf4,0xeaf4,0x1057},
{0xeaf5,0xeaf6,0x18b7},
{0xeaf7,0xeaf7,0x11e3},
{0xeaf8,0xeafb,0x18b9},
{0xeafc,0xeafc,0x11e5},
{0xeafd,0xeafd,0x18bd},
{0xeafe,0xeafe,0x11e6},
{0xeba1,0xeba3,0x18be},
{0xeba4,0xeba4,0x11e8},
{0xeba5,0xeba6,0x18c1},
{0xeba7,0xeba7,0x11ea},
{0xeba8,0xeba8,0x18c3},
{0xeba9,0xeba9,0x11ec},
{0xebaa,0xebaa,0x1058},
{0xebab,0xebb9,0x18c4},
{0xebba,0xebbb,0x11ee},
{0xebbc,0xebbc,0x18d3},
{0xebbd,0xebbd,0x11f0},
{0xebbe,0xebc0,0x18d4},
{0xebc1,0xebc1,0x11f1},
{0xebc2,0xebc2,0x11f3},
{0xebc3,0xebc5,0x18d7},
{0xebc6,0xebc7,0x11f4},
{0xebc8,0xebcb,0x18da},
{0xebcc,0xebcc,0x11f7},
{0xebcd,0xebce,0x18de},
{0xebcf,0xebd1,0x11f8},
{0xebd2,0xebd2,0x15b0},
{0xebd3,0xebd7,0x18e0},
{0xebd8,0xebd8,0x11fb},
{0xebd9,0xebfe,0x18e5},
{0xeca1,0xeca5,0x190b},
{0xeca6,0xeca6,0x1206},
{0xeca7,0xeca7,0x1208},
{0xeca8,0xeca9,0x1910},
{0xecaa,0xecaa,0x120a},
{0xecab,0xecae,0x1912},
{0xecaf,0xecaf,0x173e},
{0xecb0,0xecb1,0x120c},
{0xecb2,0xecb2,0x105b},
{0xecb3,0xecb4,0x1916},
{0xecb5,0xecb5,0x1211},
{0xecb6,0xecb7,0x1918},
{0xecb8,0xecb8,0x1213},
{0xecb9,0xecb9,0x191a},
{0xecba,0xecba,0x1215},
{0xecbb,0xecbf,0x191b},
{0xecc0,0xecc1,0x1218},
{0xecc2,0xecc4,0x1920},
{0xecc5,0xecc5,0x121a},
{0xecc6,0xecc6,0x121c},
{0xecc7,0xecc8,0x1923},
{0xecc9,0xecca,0x105c},
{0xeccb,0xecd4,0x1925},
{0xecd5,0xecd5,0x121e},
{0xecd6,0xecdc,0x192f},
{0xecdd,0xecde,0x1220},
{0xecdf,0xece0,0x1936},
{0xece1,0xece1,0x1222},
{0xece2,0xece3,0x1938},
{0xece4,0xece4,0x1224},
{0xece5,0xece6,0x193a},
{0xece7,0xece8,0x1225},
{0xece9,0xecf6,0x193c},
{0xecf7,0xecf8,0x1227},
{0xecf9,0xecf9,0x194a},
{0xecfa,0xecfa,0x122a},
{0xecfb,0xecfe,0x194b},
{0xeda1,0xeda3,0x122d},
{0xeda4,0xeded,0x194f},
{0xedee,0xedee,0x14e7},
{0xedef,0xedfe,0x1999},
{0xeea1,0xeeda,0x19a9},
{0xeedb,0xeedb,0x195e},
{0xeedc,0xeefe,0x19e3},
{0xefa1,0xeffe,0x1a06},
{0xf0a1,0xf0fe,0x1a64},
{0xf1a1,0xf1fe,0x1ac2},
{0xf2a1,0xf2bc,0x1b20},
{0xf2bd,0xf2bd,0x1663},
{0xf2be,0xf2f9,0x1b3c},
{0xf2fa,0xf2fa,0x168b},
{0xf2fb,0xf2fe,0x1b78},
{0xf3a1,0xf3b0,0x1b7c},
{0xf3b1,0xf3b1,0x105f},
{0xf3b2,0xf3fe,0x1b8c},
{0xf4a1,0xf4a6,0x1bd9},
{0xf4a7,0xf4a7,0x1954},
{0xf4a8,0xf4ed,0x1bdf},
{0xf4ee,0xf4ee,0x1a1c},
{0xf4ef,0xf4fe,0x1c25},
{0xf5a1,0xf5fe,0x1c35},
{0xf6a1,0xf6f3,0x1c93},
{0xf6f4,0xf6f4,0x10b7},
{0xf6f5,0xf6f5,0x1ce6},
{0xf6f6,0xf6f6,0x1be3},
{0xf6f7,0xf6fe,0x1ce7},
{0xf7a1,0xf7b7,0x1cef},
{0xf7b8,0xf7b8,0x1097},
{0xf7b9,0xf7c7,0x1d06},
{0xf7c8,0xf7c8,0x10aa},
{0xf7c9,0xf7d2,0x1d15},
{0xf7d3,0xf7d3,0x10f4},
{0xf7d4,0xf7fe,0x1d1f},
{0xf8a1,0xf8da,0x1d4a},
{0xf8db,0xf8db,0x1d94},
{0xf8dc,0xf8ef,0x1d84},
{0xf8f0,0xf8f0,0x13e0},
{0xf8f1,0xf8fe,0x1d98},
{0xf9a1,0xf9fe,0x1da6},
{0xfaa1,0xfaa1,0x1e1d},
{0xfaa2,0xfaa2,0xde6},
{0xfaa3,0xfae5,0x1e04},
{0xfae6,0xfae6,0xe3c},
{0xfae7,0xfafe,0x1e47},
{0xfba1,0xfbfe,0x1e5f},
{0xfca1,0xfca8,0x1ebd},
{0xfca9,0xfca9,0xee7},
{0xfcaa,0xfcfe,0x1ec5},
{0xfda1,0xfdfe,0x1f1a},
};

static pdf_cmap cmap_KSCms_UHC_HW_H = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "KSCms-UHC-HW-H",
	/* usecmap */ "", NULL,
	/* wmode */ 0,
	/* codespaces */ 2, {
		{ 1, 0x00, 0x80 },
		{ 2, 0x8141, 0xfefe },
	},
	675, 675, (pdf_range*)cmap_KSCms_UHC_HW_H_ranges,
	0, 0, NULL, /* xranges */
	0, 0, NULL, /* mranges */
	0, 0, NULL, /* table */
	0, 0, 0, NULL /* splay tree */
};
