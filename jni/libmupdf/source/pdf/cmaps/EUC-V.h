/* This is an automatically generated file. Do not edit. */

/* EUC-V */

static const pdf_range cmap_EUC_V_ranges[] = {
{0xa1a2,0xa1a3,0x1ecf},
{0xa1b1,0xa1b2,0x1ed1},
{0xa1bc,0xa1be,0x1ed3},
{0xa1c1,0xa1c5,0x1ed6},
{0xa1ca,0xa1db,0x1edb},
{0xa1e1,0xa1e1,0x1eed},
{0xa4a1,0xa4a1,0x1eee},
{0xa4a3,0xa4a3,0x1eef},
{0xa4a5,0xa4a5,0x1ef0},
{0xa4a7,0xa4a7,0x1ef1},
{0xa4a9,0xa4a9,0x1ef2},
{0xa4c3,0xa4c3,0x1ef3},
{0xa4e3,0xa4e3,0x1ef4},
{0xa4e5,0xa4e5,0x1ef5},
{0xa4e7,0xa4e7,0x1ef6},
{0xa4ee,0xa4ee,0x1ef7},
{0xa5a1,0xa5a1,0x1ef8},
{0xa5a3,0xa5a3,0x1ef9},
{0xa5a5,0xa5a5,0x1efa},
{0xa5a7,0xa5a7,0x1efb},
{0xa5a9,0xa5a9,0x1efc},
{0xa5c3,0xa5c3,0x1efd},
{0xa5e3,0xa5e3,0x1efe},
{0xa5e5,0xa5e5,0x1eff},
{0xa5e7,0xa5e7,0x1f00},
{0xa5ee,0xa5ee,0x1f01},
{0xa5f5,0xa5f6,0x1f02},
};

static pdf_cmap cmap_EUC_V = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "EUC-V",
	/* usecmap */ "EUC-H", NULL,
	/* wmode */ 1,
	/* codespaces */ 0, {
		{ 0, 0, 0 },
	},
	27, 27, (pdf_range*)cmap_EUC_V_ranges,
	0, 0, NULL, /* xranges */
	0, 0, NULL, /* mranges */
	0, 0, NULL, /* table */
	0, 0, 0, NULL /* splay tree */
};
