/* This is an automatically generated file. Do not edit. */

/* GBK2K-V */

static const pdf_range cmap_GBK2K_V_ranges[] = {
{0xa1a2,0xa1a2,0x23f},
{0xa1a3,0xa1a3,0x23e},
{0xa1aa,0xa1aa,0x256},
{0xa1ab,0xa1ac,0x1e18},
{0xa1ad,0xa1ad,0x257},
{0xa1b2,0xa1bf,0x246},
{0xa1fe,0xa1fe,0x1e1a},
{0xa3a1,0xa3a1,0x242},
{0xa3a8,0xa3a9,0x244},
{0xa3ac,0xa3ac,0x23d},
{0xa3ae,0xa3ae,0x1e1b},
{0xa3ba,0xa3bb,0x240},
{0xa3bd,0xa3bd,0x1e1c},
{0xa3bf,0xa3bf,0x243},
{0xa3db,0xa3db,0x1e1d},
{0xa3dd,0xa3dd,0x1e1e},
{0xa3df,0xa3df,0x258},
{0xa3fb,0xa3fb,0x254},
{0xa3fd,0xa3fd,0x255},
{0xa3fe,0xa3fe,0x1e1f},
{0xa4a1,0xa4a1,0x5757},
{0xa4a3,0xa4a3,0x5759},
{0xa4a5,0xa4a5,0x5762},
{0xa4a7,0xa4a7,0x5758},
{0xa4a9,0xa4a9,0x5760},
{0xa4c3,0xa4c3,0x5761},
{0xa4e3,0xa4e3,0x5764},
{0xa4e5,0xa4e5,0x5766},
{0xa4e7,0xa4e7,0x5765},
{0xa4ee,0xa4ee,0x5763},
{0xa5a1,0xa5a1,0x5768},
{0xa5a3,0xa5a3,0x576a},
{0xa5a5,0xa5a5,0x5771},
{0xa5a7,0xa5a7,0x5769},
{0xa5a9,0xa5a9,0x576f},
{0xa5c3,0xa5c3,0x5770},
{0xa5e3,0xa5e3,0x5773},
{0xa5e5,0xa5e5,0x5775},
{0xa5e7,0xa5e7,0x5774},
{0xa5ee,0xa5ee,0x5772},
{0xa960,0xa960,0x577a},
};

static pdf_cmap cmap_GBK2K_V = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "GBK2K-V",
	/* usecmap */ "GBK2K-H", NULL,
	/* wmode */ 1,
	/* codespaces */ 0, {
		{ 0, 0, 0 },
	},
	41, 41, (pdf_range*)cmap_GBK2K_V_ranges,
	0, 0, NULL, /* xranges */
	0, 0, NULL, /* mranges */
	0, 0, NULL, /* table */
	0, 0, 0, NULL /* splay tree */
};
