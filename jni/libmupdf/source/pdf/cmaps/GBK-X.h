/* This is an automatically generated file. Do not edit. */

/* GBK-X */

static const pdf_range cmap_GBK_X_ranges[] = {
{0x8140,0x8178,0x2758},
{0x8179,0x8179,0x2059},
{0x817a,0x817e,0x2791},
{0x8180,0x8185,0x2796},
{0x8186,0x8186,0x21f1},
{0x8187,0x81ec,0x279c},
{0x81ed,0x81ed,0x1ff2},
{0x81ee,0x81f5,0x2802},
{0x81f6,0x81f6,0x205d},
{0x81f7,0x81fe,0x280a},
{0x8240,0x8252,0x2812},
{0x8253,0x8253,0x269c},
{0x8254,0x8261,0x2825},
{0x8262,0x8262,0x21b5},
{0x8263,0x8273,0x2833},
{0x8274,0x8274,0x22cc},
{0x8275,0x8279,0x2844},
{0x827a,0x827a,0x2016},
{0x827b,0x827c,0x2849},
{0x827d,0x827d,0x1e62},
{0x827e,0x827e,0x284b},
{0x8280,0x8280,0x1f20},
{0x8281,0x8282,0x284c},
{0x8283,0x8283,0x207f},
{0x8284,0x828f,0x284e},
{0x8290,0x8290,0x205c},
{0x8291,0x82a4,0x285a},
{0x82a5,0x82a5,0x2194},
{0x82a6,0x82c7,0x286e},
{0x82c8,0x82c8,0x1e65},
{0x82c9,0x82c9,0x2281},
{0x82ca,0x82e0,0x2890},
{0x82e1,0x82e1,0x22cd},
{0x82e2,0x82e2,0x28a7},
{0x82e3,0x82e3,0x210a},
{0x82e4,0x82e4,0x1e3e},
{0x82e5,0x82ec,0x28a8},
{0x82ed,0x82ed,0x267f},
{0x82ee,0x82f1,0x28b0},
{0x82f2,0x82f2,0x222e},
{0x82f3,0x82f6,0x28b4},
{0x82f7,0x82f7,0x1e96},
{0x82f8,0x82f8,0x22cb},
{0x82f9,0x82f9,0x226c},
{0x82fa,0x82fa,0x28b8},
{0x82fb,0x82fb,0x2117},
{0x82fc,0x82fe,0x28b9},
{0x8340,0x8340,0x28bc},
{0x8341,0x8341,0x20e8},
{0x8342,0x8344,0x28bd},
{0x8345,0x8345,0x22d4},
{0x8346,0x8347,0x28c0},
{0x8348,0x8348,0x1fb9},
{0x8349,0x834b,0x28c2},
{0x834c,0x834c,0x22d8},
{0x834d,0x8352,0x28c5},
{0x8353,0x8353,0x20df},
{0x8354,0x8356,0x28cb},
{0x8357,0x8357,0x20c2},
{0x8358,0x835d,0x28ce},
{0x835e,0x835e,0x2195},
{0x835f,0x8364,0x28d4},
{0x8365,0x8365,0x1fac},
{0x8366,0x8366,0x22d3},
{0x8367,0x8371,0x28da},
{0x8372,0x8372,0x1f81},
{0x8373,0x8377,0x28e5},
{0x8378,0x8378,0x2210},
{0x8379,0x8379,0x28ea},
{0x837a,0x837a,0x22cf},
{0x837b,0x837b,0x28eb},
{0x837c,0x837c,0x2213},
{0x837d,0x837d,0x28ec},
{0x837e,0x837e,0x1fe4},
{0x8380,0x8380,0x1f90},
{0x8381,0x8385,0x28ed},
{0x8386,0x8386,0x22d6},
{0x8387,0x8388,0x28f2},
{0x8389,0x8389,0x22d0},
{0x838a,0x838a,0x22ce},
{0x838b,0x838c,0x28f4},
{0x838d,0x838d,0x2681},
{0x838e,0x8393,0x28f6},
{0x8394,0x8394,0x1e76},
{0x8395,0x839d,0x28fc},
{0x839e,0x839e,0x2231},
{0x839f,0x83a5,0x2905},
{0x83a6,0x83a6,0x1e93},
{0x83a7,0x83aa,0x290c},
{0x83ab,0x83ab,0x22d2},
{0x83ac,0x83ad,0x2910},
{0x83ae,0x83ae,0x22d7},
{0x83af,0x83af,0x22d5},
{0x83b0,0x83b0,0x22d1},
{0x83b1,0x83b9,0x2912},
{0x83ba,0x83ba,0x1ee5},
{0x83bb,0x83c8,0x291b},
{0x83c9,0x83c9,0x2025},
{0x83ca,0x83f5,0x2929},
{0x83f6,0x83f6,0x1ecf},
{0x83f7,0x83fe,0x2955},
{0x8440,0x844f,0x295d},
{0x8450,0x8450,0x1fd9},
{0x8451,0x8470,0x296d},
{0x8471,0x8471,0x22c8},
{0x8472,0x8473,0x298d},
{0x8474,0x8474,0x2263},
{0x8475,0x8476,0x298f},
{0x8477,0x8477,0x2683},
{0x8478,0x847e,0x2991},
{0x8480,0x8481,0x2998},
{0x8482,0x8482,0x1f17},
{0x8483,0x848d,0x299a},
{0x848e,0x848e,0x1f2b},
{0x848f,0x8491,0x29a5},
{0x8492,0x8492,0x22ca},
{0x8493,0x8493,0x1e99},
{0x8494,0x849c,0x29a8},
{0x849d,0x849d,0x1f4f},
{0x849e,0x84a0,0x29b1},
{0x84a1,0x84a1,0x1fcf},
{0x84a2,0x84a2,0x2036},
{0x84a3,0x84a3,0x1f3a},
{0x84a4,0x84a4,0x29b4},
{0x84a5,0x84a5,0x22c9},
{0x84a6,0x84a6,0x1f99},
{0x84a7,0x84a8,0x29b5},
{0x84a9,0x84a9,0x1f75},
{0x84aa,0x84c4,0x29b7},
{0x84c5,0x84c5,0x1fbe},
{0x84c6,0x84d2,0x29d2},
{0x84d3,0x84d3,0x1ecd},
{0x84d4,0x84d4,0x29df},
{0x84d5,0x84d5,0x21a9},
{0x84d6,0x84d6,0x29e0},
{0x84d7,0x84d7,0x21e6},
{0x84d8,0x84d8,0x29e1},
{0x84d9,0x84d9,0x2127},
{0x84da,0x84da,0x2003},
{0x84db,0x84dc,0x29e2},
{0x84dd,0x84dd,0x2132},
{0x84de,0x84e9,0x29e4},
{0x84ea,0x84ea,0x2323},
{0x84eb,0x84ed,0x29f0},
{0x84ee,0x84ee,0x2011},
{0x84ef,0x84f0,0x29f3},
{0x84f1,0x84f1,0x20f5},
{0x84f2,0x84fe,0x29f5},
{0x8540,0x8550,0x2a02},
{0x8551,0x8551,0x22c5},
{0x8552,0x8552,0x1f5e},
{0x8553,0x8553,0x2a13},
{0x8554,0x8554,0x22c6},
{0x8555,0x855d,0x2a14},
{0x855e,0x855e,0x20ef},
{0x855f,0x8565,0x2a1d},
{0x8566,0x8566,0x21d0},
{0x8567,0x857e,0x2a24},
{0x8580,0x8586,0x2a3c},
{0x8587,0x8587,0x22c1},
{0x8588,0x858a,0x2a43},
{0x858b,0x858b,0x1e64},
{0x858c,0x8591,0x2a46},
{0x8592,0x8592,0x21f9},
{0x8593,0x8595,0x2a4c},
{0x8596,0x8596,0x2010},
{0x8597,0x8597,0x2a4f},
{0x8598,0x8598,0x22c2},
{0x8599,0x85a1,0x2a50},
{0x85a2,0x85a2,0x1e5a},
{0x85a3,0x85b1,0x2a59},
{0x85b2,0x85b2,0x1ea2},
{0x85b3,0x85fe,0x2a68},
{0x8640,0x8649,0x2ab4},
{0x864a,0x864a,0x236d},
{0x864b,0x8653,0x2abe},
{0x8654,0x8654,0x2247},
{0x8655,0x8667,0x2ac7},
{0x8668,0x8668,0x236c},
{0x8669,0x867e,0x2ada},
{0x8680,0x8695,0x2af0},
{0x8696,0x8696,0x219c},
{0x8697,0x8698,0x2b06},
{0x8699,0x8699,0x20c9},
{0x869a,0x86a0,0x2b08},
{0x86a1,0x86a1,0x21f0},
{0x86a2,0x86c9,0x2b0f},
{0x86ca,0x86ca,0x210b},
{0x86cb,0x86cb,0x2b37},
{0x86cc,0x86cc,0x20de},
{0x86cd,0x86cd,0x2b38},
{0x86ce,0x86ce,0x1eaa},
{0x86cf,0x86d0,0x2b39},
{0x86d1,0x86d1,0x222c},
{0x86d2,0x86db,0x2b3b},
{0x86dc,0x86dc,0x20d8},
{0x86dd,0x86dd,0x22c0},
{0x86de,0x86e0,0x2b45},
{0x86e1,0x86e1,0x206f},
{0x86e2,0x86e7,0x2b48},
{0x86e8,0x86e8,0x21a1},
{0x86e9,0x86ed,0x2b4e},
{0x86ee,0x86ee,0x2379},
{0x86ef,0x86f3,0x2b53},
{0x86f4,0x86f4,0x2372},
{0x86f5,0x86fe,0x2b58},
{0x8740,0x8740,0x216a},
{0x8741,0x8743,0x2b62},
{0x8744,0x8744,0x237c},
{0x8745,0x8748,0x2b65},
{0x8749,0x8749,0x20b0},
{0x874a,0x874a,0x2b69},
{0x874b,0x874b,0x237a},
{0x874c,0x874c,0x1e74},
{0x874d,0x874e,0x2b6a},
{0x874f,0x874f,0x2377},
{0x8750,0x8756,0x2b6c},
{0x8757,0x8757,0x1f4c},
{0x8758,0x8759,0x2b73},
{0x875a,0x875a,0x2378},
{0x875b,0x875b,0x21cf},
{0x875c,0x875c,0x2368},
{0x875d,0x875d,0x2b75},
{0x875e,0x875e,0x2371},
{0x875f,0x875f,0x2b76},
{0x8760,0x8760,0x2369},
{0x8761,0x8765,0x2b77},
{0x8766,0x8766,0x2674},
{0x8767,0x8779,0x2b7c},
{0x877a,0x877a,0x236f},
{0x877b,0x877c,0x2b8f},
{0x877d,0x877d,0x2370},
{0x877e,0x877e,0x2b91},
{0x8780,0x8780,0x2b92},
{0x8781,0x8781,0x2376},
{0x8782,0x8782,0x2373},
{0x8783,0x8785,0x2b93},
{0x8786,0x8786,0x237f},
{0x8787,0x8787,0x2b96},
{0x8788,0x8788,0x2374},
{0x8789,0x8789,0x2b97},
{0x878a,0x878a,0x20b5},
{0x878b,0x878c,0x2b98},
{0x878d,0x878d,0x1edb},
{0x878e,0x878e,0x2672},
{0x878f,0x8792,0x2b9a},
{0x8793,0x8793,0x236e},
{0x8794,0x8797,0x2b9e},
{0x8798,0x8798,0x21b7},
{0x8799,0x879c,0x2ba2},
{0x879d,0x879d,0x2375},
{0x879e,0x87a2,0x2ba6},
{0x87a3,0x87a3,0x2382},
{0x87a4,0x87a6,0x2bab},
{0x87a7,0x87a7,0x209e},
{0x87a8,0x87b2,0x2bae},
{0x87b3,0x87b3,0x236b},
{0x87b4,0x87b4,0x2bb9},
{0x87b5,0x87b5,0x2039},
{0x87b6,0x87ba,0x2bba},
{0x87bb,0x87bb,0x269f},
{0x87bc,0x87be,0x2bbf},
{0x87bf,0x87bf,0x237d},
{0x87c0,0x87c0,0x21f5},
{0x87c1,0x87c1,0x2bc2},
{0x87c2,0x87c2,0x2381},
{0x87c3,0x87c9,0x2bc3},
{0x87ca,0x87ca,0x237b},
{0x87cb,0x87cb,0x237e},
{0x87cc,0x87cc,0x21cc},
{0x87cd,0x87ce,0x2bca},
{0x87cf,0x87cf,0x22db},
{0x87d0,0x87d1,0x2bcc},
{0x87d2,0x87d2,0x236a},
{0x87d3,0x87d3,0x2689},
{0x87d4,0x87d4,0x2bce},
{0x87d5,0x87d5,0x2697},
{0x87d6,0x87d9,0x2bcf},
{0x87da,0x87da,0x22a1},
{0x87db,0x87f6,0x2bd3},
{0x87f7,0x87f7,0x2383},
{0x87f8,0x87f8,0x1f3d},
{0x87f9,0x87f9,0x2bef},
{0x87fa,0x87fa,0x218f},
{0x87fb,0x87fe,0x2bf0},
{0x8840,0x8840,0x2246},
{0x8841,0x8841,0x2248},
{0x8842,0x8843,0x2bf4},
{0x8844,0x8844,0x217e},
{0x8845,0x8845,0x2bf6},
{0x8846,0x8846,0x2180},
{0x8847,0x887e,0x2bf7},
{0x8880,0x88b9,0x2c2f},
{0x88ba,0x88ba,0x232a},
{0x88bb,0x88cb,0x2c69},
{0x88cc,0x88cc,0x228b},
{0x88cd,0x88d3,0x2c7a},
{0x88d4,0x88d4,0x1f85},
{0x88d5,0x88d6,0x2c81},
{0x88d7,0x88d7,0x2325},
{0x88d8,0x88de,0x2c83},
{0x88df,0x88df,0x232c},
{0x88e0,0x88e4,0x2c8a},
{0x88e5,0x88e5,0x232e},
{0x88e6,0x88f1,0x2c8f},
{0x88f2,0x88f2,0x2205},
{0x88f3,0x88f3,0x1e38},
{0x88f4,0x88f5,0x2c9b},
{0x88f6,0x88f6,0x1e73},
{0x88f7,0x88fe,0x2c9d},
{0x8940,0x894a,0x2ca5},
{0x894b,0x894b,0x1fe3},
{0x894c,0x894c,0x2339},
{0x894d,0x894d,0x2cb0},
{0x894e,0x894e,0x232b},
{0x894f,0x894f,0x2cb1},
{0x8950,0x8950,0x232d},
{0x8951,0x8953,0x2cb2},
{0x8954,0x8954,0x217f},
{0x8955,0x895c,0x2cb5},
{0x895d,0x895d,0x21a7},
{0x895e,0x895e,0x2cbd},
{0x895f,0x895f,0x232f},
{0x8960,0x896c,0x2cbe},
{0x896d,0x896d,0x1e7d},
{0x896e,0x8970,0x2ccb},
{0x8971,0x8971,0x20d6},
{0x8972,0x897b,0x2cce},
{0x897c,0x897c,0x1ec2},
{0x897d,0x897e,0x2cd8},
{0x8980,0x898a,0x2cda},
{0x898b,0x898b,0x22b2},
{0x898c,0x8998,0x2ce5},
{0x8999,0x8999,0x1edf},
{0x899a,0x899d,0x2cf2},
{0x899e,0x899e,0x1ef9},
{0x899f,0x89a5,0x2cf6},
{0x89a6,0x89a6,0x20d9},
{0x89a7,0x89a7,0x2cfd},
{0x89a8,0x89a8,0x1fdd},
{0x89a9,0x89ae,0x2cfe},
{0x89af,0x89af,0x2167},
{0x89b0,0x89b9,0x2d04},
{0x89ba,0x89ba,0x21ed},
{0x89bb,0x89bd,0x2d0e},
{0x89be,0x89be,0x2007},
{0x89bf,0x89bf,0x2326},
{0x89c0,0x89c0,0x2329},
{0x89c1,0x89c3,0x2d11},
{0x89c4,0x89c4,0x1f52},
{0x89c5,0x89c5,0x203b},
{0x89c6,0x89c6,0x2328},
{0x89c7,0x89c7,0x2d14},
{0x89c8,0x89c8,0x2327},
{0x89c9,0x89cd,0x2d15},
{0x89ce,0x89ce,0x1e2b},
{0x89cf,0x89d0,0x2d1a},
{0x89d1,0x89d1,0x22ae},
{0x89d2,0x89d7,0x2d1c},
{0x89d8,0x89d8,0x1f49},
{0x89d9,0x89da,0x2d22},
{0x89db,0x89db,0x2138},
{0x89dc,0x89f3,0x2d24},
{0x89f4,0x89f4,0x2081},
{0x89f5,0x89fe,0x2d3c},
{0x8a40,0x8a40,0x2d46},
{0x8a41,0x8a41,0x1f7c},
{0x8a42,0x8a58,0x2d47},
{0x8a59,0x8a59,0x235b},
{0x8a5a,0x8a5a,0x1ede},
{0x8a5b,0x8a5b,0x2d5e},
{0x8a5c,0x8a5c,0x1fa2},
{0x8a5d,0x8a5d,0x2d5f},
{0x8a5e,0x8a5e,0x1efa},
{0x8a5f,0x8a78,0x2d60},
{0x8a79,0x8a79,0x22ad},
{0x8a7a,0x8a7e,0x2d7a},
{0x8a80,0x8ae3,0x2d7f},
{0x8ae4,0x8ae4,0x203f},
{0x8ae5,0x8afe,0x2de3},
{0x8b40,0x8b43,0x2dfd},
{0x8b44,0x8b44,0x1f0e},
{0x8b45,0x8b48,0x2e01},
{0x8b49,0x8b49,0x23f9},
{0x8b4a,0x8b79,0x2e05},
{0x8b7a,0x8b7a,0x23fc},
{0x8b7b,0x8b7e,0x2e35},
{0x8b80,0x8b8b,0x2e39},
{0x8b8c,0x8b8c,0x2069},
{0x8b8d,0x8b9d,0x2e45},
{0x8b9e,0x8b9e,0x23f7},
{0x8b9f,0x8bb2,0x2e56},
{0x8bb3,0x8bb3,0x23f6},
{0x8bb4,0x8bb8,0x2e6a},
{0x8bb9,0x8bb9,0x23fd},
{0x8bba,0x8bbd,0x2e6f},
{0x8bbe,0x8bbe,0x23f8},
{0x8bbf,0x8bc5,0x2e73},
{0x8bc6,0x8bc6,0x23fa},
{0x8bc7,0x8bc7,0x2e7a},
{0x8bc8,0x8bc8,0x23fe},
{0x8bc9,0x8bc9,0x1fa8},
{0x8bca,0x8bd3,0x2e7b},
{0x8bd4,0x8bd4,0x2401},
{0x8bd5,0x8bdb,0x2e85},
{0x8bdc,0x8bdc,0x23ff},
{0x8bdd,0x8be4,0x2e8c},
{0x8be5,0x8be5,0x2400},
{0x8be6,0x8bea,0x2e94},
{0x8beb,0x8beb,0x2221},
{0x8bec,0x8bef,0x2e99},
{0x8bf0,0x8bf0,0x2122},
{0x8bf1,0x8bfe,0x2e9d},
{0x8c40,0x8c43,0x2eab},
{0x8c44,0x8c44,0x23fb},
{0x8c45,0x8c4e,0x2eaf},
{0x8c4f,0x8c4f,0x215a},
{0x8c50,0x8c56,0x2eb9},
{0x8c57,0x8c57,0x21e5},
{0x8c58,0x8c5b,0x2ec0},
{0x8c5c,0x8c5c,0x2057},
{0x8c5d,0x8c7e,0x2ec4},
{0x8c80,0x8c8a,0x2ee6},
{0x8c8b,0x8c8b,0x20e5},
{0x8c8c,0x8c8c,0x2ef1},
{0x8c8d,0x8c8d,0x212f},
{0x8c8e,0x8c8e,0x20a3},
{0x8c8f,0x8c8f,0x2121},
{0x8c90,0x8c90,0x2ef2},
{0x8c91,0x8c91,0x21d4},
{0x8c92,0x8c92,0x1fe5},
{0x8c93,0x8c98,0x2ef3},
{0x8c99,0x8c99,0x1e8a},
{0x8c9a,0x8c9a,0x1e37},
{0x8c9b,0x8ca1,0x2ef9},
{0x8ca2,0x8ca2,0x1f9e},
{0x8ca3,0x8ca3,0x22a6},
{0x8ca4,0x8ca4,0x21e8},
{0x8ca5,0x8ca5,0x2f00},
{0x8ca6,0x8ca6,0x1eda},
{0x8ca7,0x8ca7,0x1eb9},
{0x8ca8,0x8cbf,0x2f01},
{0x8cc0,0x8cc0,0x235c},
{0x8cc1,0x8cd1,0x2f19},
{0x8cd2,0x8cd2,0x2050},
{0x8cd3,0x8cd3,0x1e67},
{0x8cd4,0x8cd4,0x2f2a},
{0x8cd5,0x8cd5,0x23f4},
{0x8cd6,0x8cd8,0x2f2b},
{0x8cd9,0x8cd9,0x213e},
{0x8cda,0x8cf8,0x2f2e},
{0x8cf9,0x8cf9,0x1f16},
{0x8cfa,0x8cfe,0x2f4d},
{0x8d40,0x8d72,0x2f52},
{0x8d73,0x8d73,0x2389},
{0x8d74,0x8d74,0x2f85},
{0x8d75,0x8d75,0x1eb7},
{0x8d76,0x8d7a,0x2f86},
{0x8d7b,0x8d7b,0x21b4},
{0x8d7c,0x8d7e,0x2f8b},
{0x8d80,0x8d87,0x2f8e},
{0x8d88,0x8d88,0x238f},
{0x8d89,0x8d8e,0x2f96},
{0x8d8f,0x8d8f,0x1f1a},
{0x8d90,0x8d9d,0x2f9c},
{0x8d9e,0x8d9e,0x238b},
{0x8d9f,0x8db8,0x2faa},
{0x8db9,0x8db9,0x238a},
{0x8dba,0x8de1,0x2fc4},
{0x8de2,0x8de2,0x2391},
{0x8de3,0x8de3,0x2fec},
{0x8de4,0x8de4,0x2271},
{0x8de5,0x8de6,0x2fed},
{0x8de7,0x8de7,0x2388},
{0x8de8,0x8df6,0x2fef},
{0x8df7,0x8df7,0x238e},
{0x8df8,0x8dfd,0x2ffe},
{0x8dfe,0x8dfe,0x238d},
{0x8e40,0x8e45,0x3004},
{0x8e46,0x8e46,0x238c},
{0x8e47,0x8e55,0x300a},
{0x8e56,0x8e56,0x2390},
{0x8e57,0x8e57,0x3019},
{0x8e58,0x8e58,0x2033},
{0x8e59,0x8e59,0x301a},
{0x8e5a,0x8e5a,0x223c},
{0x8e5b,0x8e67,0x301b},
{0x8e68,0x8e68,0x1fe9},
{0x8e69,0x8e6d,0x3028},
{0x8e6e,0x8e6e,0x2055},
{0x8e6f,0x8e6f,0x302d},
{0x8e70,0x8e70,0x2392},
{0x8e71,0x8e7e,0x302e},
{0x8e80,0x8e80,0x2324},
{0x8e81,0x8e9a,0x303c},
{0x8e9b,0x8e9b,0x2143},
{0x8e9c,0x8e9e,0x3056},
{0x8e9f,0x8e9f,0x2129},
{0x8ea0,0x8ea3,0x3059},
{0x8ea4,0x8ea4,0x2277},
{0x8ea5,0x8ea6,0x305d},
{0x8ea7,0x8ea7,0x1ea7},
{0x8ea8,0x8eab,0x305f},
{0x8eac,0x8eac,0x2285},
{0x8ead,0x8ead,0x3063},
{0x8eae,0x8eae,0x2384},
{0x8eaf,0x8ebc,0x3064},
{0x8ebd,0x8ebd,0x2387},
{0x8ebe,0x8ebe,0x2386},
{0x8ebf,0x8ec2,0x3072},
{0x8ec3,0x8ec3,0x2290},
{0x8ec4,0x8ec4,0x3076},
{0x8ec5,0x8ec5,0x1e44},
{0x8ec6,0x8ecc,0x3077},
{0x8ecd,0x8ecd,0x1e32},
{0x8ece,0x8ece,0x2385},
{0x8ecf,0x8ed5,0x307e},
{0x8ed6,0x8ed6,0x1f13},
{0x8ed7,0x8ed7,0x1f73},
{0x8ed8,0x8eeb,0x3085},
{0x8eec,0x8eec,0x1fe0},
{0x8eed,0x8efe,0x3099},
{0x8f40,0x8f51,0x30ab},
{0x8f52,0x8f52,0x2087},
{0x8f53,0x8f53,0x1e78},
{0x8f54,0x8f54,0x23ae},
{0x8f55,0x8f55,0x1ef6},
{0x8f56,0x8f56,0x1f31},
{0x8f57,0x8f5c,0x30bd},
{0x8f5d,0x8f5d,0x2045},
{0x8f5e,0x8f63,0x30c3},
{0x8f64,0x8f64,0x2178},
{0x8f65,0x8f7e,0x30c9},
{0x8f80,0x8f85,0x30e3},
{0x8f86,0x8f86,0x23f5},
{0x8f87,0x8f87,0x30e9},
{0x8f88,0x8f88,0x2275},
{0x8f89,0x8f94,0x30ea},
{0x8f95,0x8f95,0x266e},
{0x8f96,0x8f96,0x30f6},
{0x8f97,0x8f97,0x1eb0},
{0x8f98,0x8f9a,0x30f7},
{0x8f9b,0x8f9b,0x2083},
{0x8f9c,0x8f9c,0x30fa},
{0x8f9d,0x8f9d,0x2188},
{0x8f9e,0x8fa0,0x30fb},
{0x8fa1,0x8fa1,0x267c},
{0x8fa2,0x8fbc,0x30fe},
{0x8fbd,0x8fbd,0x1fc5},
{0x8fbe,0x8fc3,0x3119},
{0x8fc4,0x8fc4,0x1ea1},
{0x8fc5,0x8fc5,0x311f},
{0x8fc6,0x8fc6,0x2393},
{0x8fc7,0x8fcc,0x3120},
{0x8fcd,0x8fcd,0x1f0b},
{0x8fce,0x8fd7,0x3126},
{0x8fd8,0x8fd8,0x1e7c},
{0x8fd9,0x8ffe,0x3130},
{0x9040,0x907e,0x3156},
{0x9080,0x909c,0x3195},
{0x909d,0x909d,0x23b4},
{0x909e,0x909e,0x207e},
{0x909f,0x90b9,0x31b2},
{0x90ba,0x90ba,0x1ee3},
{0x90bb,0x90bf,0x31cd},
{0x90c0,0x90c0,0x2095},
{0x90c1,0x90c1,0x23bb},
{0x90c2,0x90c4,0x31d2},
{0x90c5,0x90c5,0x23b9},
{0x90c6,0x90da,0x31d5},
{0x90db,0x90db,0x1e28},
{0x90dc,0x90dc,0x23bd},
{0x90dd,0x90ec,0x31ea},
{0x90ed,0x90ed,0x23b5},
{0x90ee,0x90ef,0x31fa},
{0x90f0,0x90f0,0x23ba},
{0x90f1,0x90f6,0x31fc},
{0x90f7,0x90f7,0x23b3},
{0x90f8,0x90fe,0x3202},
{0x9140,0x9141,0x3209},
{0x9142,0x9142,0x2162},
{0x9143,0x914a,0x320b},
{0x914b,0x914b,0x1e5e},
{0x914c,0x914c,0x3213},
{0x914d,0x914d,0x1e5d},
{0x914e,0x9150,0x3214},
{0x9151,0x9151,0x23b7},
{0x9152,0x9153,0x3217},
{0x9154,0x9154,0x1f2f},
{0x9155,0x9155,0x24df},
{0x9156,0x9158,0x3219},
{0x9159,0x9159,0x23b2},
{0x915a,0x915a,0x214e},
{0x915b,0x915c,0x321c},
{0x915d,0x915d,0x2052},
{0x915e,0x9160,0x321e},
{0x9161,0x9161,0x23bc},
{0x9162,0x9162,0x3221},
{0x9163,0x9163,0x20eb},
{0x9164,0x916d,0x3222},
{0x916e,0x916e,0x2232},
{0x916f,0x9175,0x322c},
{0x9176,0x9176,0x1e3f},
{0x9177,0x9179,0x3233},
{0x917a,0x917a,0x201b},
{0x917b,0x917b,0x20bc},
{0x917c,0x917c,0x23be},
{0x917d,0x917e,0x3236},
{0x9180,0x9183,0x3238},
{0x9184,0x9184,0x1eae},
{0x9185,0x918c,0x323c},
{0x918d,0x918d,0x1efb},
{0x918e,0x9190,0x3244},
{0x9191,0x9191,0x2089},
{0x9192,0x9192,0x3247},
{0x9193,0x9193,0x23b1},
{0x9194,0x9196,0x3248},
{0x9197,0x9197,0x21c4},
{0x9198,0x919a,0x324b},
{0x919b,0x919b,0x2214},
{0x919c,0x91a8,0x324e},
{0x91a9,0x91a9,0x1fde},
{0x91aa,0x91aa,0x2223},
{0x91ab,0x91ab,0x23b6},
{0x91ac,0x91b9,0x325b},
{0x91ba,0x91ba,0x268c},
{0x91bb,0x91bb,0x24de},
{0x91bc,0x91be,0x3269},
{0x91bf,0x91bf,0x24e0},
{0x91c0,0x91c2,0x326c},
{0x91c3,0x91c3,0x23b8},
{0x91c4,0x91cc,0x326f},
{0x91cd,0x91cd,0x1e81},
{0x91ce,0x91cf,0x3278},
{0x91d0,0x91d0,0x1ffe},
{0x91d1,0x91d1,0x1f51},
{0x91d2,0x91d2,0x21e1},
{0x91d3,0x91d3,0x327a},
{0x91d4,0x91d4,0x23b0},
{0x91d5,0x91d5,0x327b},
{0x91d6,0x91d6,0x1fce},
{0x91d7,0x91d7,0x327c},
{0x91d8,0x91d8,0x211e},
{0x91d9,0x91d9,0x2021},
{0x91da,0x91de,0x327d},
{0x91df,0x91df,0x24e1},
{0x91e0,0x91e1,0x3282},
{0x91e2,0x91e2,0x24a3},
{0x91e3,0x91e9,0x3284},
{0x91ea,0x91ea,0x24a4},
{0x91eb,0x91ef,0x328b},
{0x91f0,0x91f0,0x2273},
{0x91f1,0x91f1,0x3290},
{0x91f2,0x91f2,0x21b0},
{0x91f3,0x91fe,0x3291},
{0x9240,0x927e,0x329d},
{0x9280,0x92b5,0x32dc},
{0x92b6,0x92b6,0x21d1},
{0x92b7,0x92cd,0x3312},
{0x92ce,0x92ce,0x211c},
{0x92cf,0x92cf,0x3329},
{0x92d0,0x92d0,0x235d},
{0x92d1,0x92d3,0x332a},
{0x92d4,0x92d4,0x2682},
{0x92d5,0x92de,0x332d},
{0x92df,0x92df,0x210d},
{0x92e0,0x92e0,0x205a},
{0x92e1,0x92fd,0x3337},
{0x92fe,0x92fe,0x1f8d},
{0x9340,0x934f,0x3354},
{0x9350,0x9350,0x21ff},
{0x9351,0x935c,0x3364},
{0x935d,0x935d,0x1f58},
{0x935e,0x936f,0x3370},
{0x9370,0x9370,0x215b},
{0x9371,0x9375,0x3382},
{0x9376,0x9376,0x1eb6},
{0x9377,0x937e,0x3387},
{0x9380,0x938b,0x338f},
{0x938c,0x938c,0x20db},
{0x938d,0x939c,0x339b},
{0x939d,0x939d,0x2360},
{0x939e,0x93a4,0x33ab},
{0x93a5,0x93a5,0x2361},
{0x93a6,0x93a6,0x33b2},
{0x93a7,0x93a7,0x2040},
{0x93a8,0x93b3,0x33b3},
{0x93b4,0x93b4,0x228e},
{0x93b5,0x93b7,0x33bf},
{0x93b8,0x93b8,0x1fdf},
{0x93b9,0x93ba,0x33c2},
{0x93bb,0x93bb,0x235e},
{0x93bc,0x93bc,0x33c4},
{0x93bd,0x93bd,0x1e6a},
{0x93be,0x93c5,0x33c5},
{0x93c6,0x93c6,0x2002},
{0x93c7,0x93ce,0x33cd},
{0x93cf,0x93cf,0x2093},
{0x93d0,0x93d6,0x33d5},
{0x93d7,0x93d7,0x235f},
{0x93d8,0x93da,0x33dc},
{0x93db,0x93db,0x1eac},
{0x93dc,0x93dc,0x1e54},
{0x93dd,0x93e0,0x33df},
{0x93e1,0x93e1,0x1f08},
{0x93e2,0x93e3,0x33e3},
{0x93e4,0x93e4,0x20c0},
{0x93e5,0x93e5,0x2362},
{0x93e6,0x93e8,0x33e5},
{0x93e9,0x93e9,0x2160},
{0x93ea,0x93ea,0x33e8},
{0x93eb,0x93eb,0x219d},
{0x93ec,0x93ec,0x1f8e},
{0x93ed,0x93ed,0x222d},
{0x93ee,0x93ee,0x33e9},
{0x93ef,0x93ef,0x2047},
{0x93f0,0x93f0,0x33ea},
{0x93f1,0x93f1,0x2262},
{0x93f2,0x93f3,0x33eb},
{0x93f4,0x93f4,0x1f67},
{0x93f5,0x93f5,0x1eb2},
{0x93f6,0x93f9,0x33ed},
{0x93fa,0x93fa,0x1ea9},
{0x93fb,0x93fd,0x33f1},
{0x93fe,0x93fe,0x1fcc},
{0x9440,0x9443,0x33f4},
{0x9444,0x9444,0x1f72},
{0x9445,0x944c,0x33f8},
{0x944d,0x944d,0x2098},
{0x944e,0x944f,0x3400},
{0x9450,0x9450,0x1e52},
{0x9451,0x9451,0x20a4},
{0x9452,0x9452,0x1f1c},
{0x9453,0x9453,0x228f},
{0x9454,0x9454,0x3402},
{0x9455,0x9455,0x1fed},
{0x9456,0x9457,0x3403},
{0x9458,0x9458,0x2365},
{0x9459,0x945a,0x3405},
{0x945b,0x945b,0x1e2d},
{0x945c,0x945c,0x2152},
{0x945d,0x945d,0x2366},
{0x945e,0x945e,0x3407},
{0x945f,0x945f,0x20fa},
{0x9460,0x9463,0x3408},
{0x9464,0x9464,0x2363},
{0x9465,0x9465,0x340c},
{0x9466,0x9466,0x209a},
{0x9467,0x946d,0x340d},
{0x946e,0x946e,0x203c},
{0x946f,0x9471,0x3414},
{0x9472,0x9472,0x1ff6},
{0x9473,0x9473,0x3417},
{0x9474,0x9474,0x2364},
{0x9475,0x9475,0x3418},
{0x9476,0x9476,0x1e69},
{0x9477,0x9477,0x3419},
{0x9478,0x9478,0x2367},
{0x9479,0x9479,0x341a},
{0x947a,0x947a,0x211d},
{0x947b,0x947e,0x341b},
{0x9480,0x9480,0x2259},
{0x9481,0x9481,0x2056},
{0x9482,0x9482,0x2163},
{0x9483,0x9486,0x341f},
{0x9487,0x9487,0x1fa9},
{0x9488,0x9488,0x1ffc},
{0x9489,0x94a0,0x3423},
{0x94a1,0x94a1,0x1e2e},
{0x94a2,0x94b2,0x343b},
{0x94b3,0x94b3,0x1ebc},
{0x94b4,0x94b4,0x344c},
{0x94b5,0x94b5,0x2142},
{0x94b6,0x94be,0x344d},
{0x94bf,0x94bf,0x201e},
{0x94c0,0x94c0,0x1e43},
{0x94c1,0x94cb,0x3456},
{0x94cc,0x94cc,0x24d4},
{0x94cd,0x94d7,0x3461},
{0x94d8,0x94d8,0x226f},
{0x94d9,0x94df,0x346c},
{0x94e0,0x94e0,0x1ed7},
{0x94e1,0x94fe,0x3473},
{0x9540,0x9571,0x3491},
{0x9572,0x9572,0x212d},
{0x9573,0x957e,0x34c3},
{0x9580,0x9582,0x34cf},
{0x9583,0x9583,0x229b},
{0x9584,0x959d,0x34d2},
{0x959e,0x959e,0x2256},
{0x959f,0x959f,0x24a8},
{0x95a0,0x95b2,0x34ec},
{0x95b3,0x95b3,0x1e79},
{0x95b4,0x95b9,0x34ff},
{0x95ba,0x95ba,0x225a},
{0x95bb,0x95ce,0x3505},
{0x95cf,0x95cf,0x24a7},
{0x95d0,0x95d0,0x3519},
{0x95d1,0x95d1,0x2686},
{0x95d2,0x95d2,0x24a6},
{0x95d3,0x95d3,0x351a},
{0x95d4,0x95d4,0x21ce},
{0x95d5,0x95e0,0x351b},
{0x95e1,0x95e1,0x24a9},
{0x95e2,0x95e6,0x3527},
{0x95e7,0x95e7,0x1fe7},
{0x95e8,0x95f0,0x352c},
{0x95f1,0x95f1,0x2112},
{0x95f2,0x95f7,0x3535},
{0x95f8,0x95f8,0x213c},
{0x95f9,0x95fd,0x353b},
{0x95fe,0x95fe,0x1f5c},
{0x9640,0x9655,0x3540},
{0x9656,0x9656,0x24c4},
{0x9657,0x967b,0x3556},
{0x967c,0x967c,0x1ecc},
{0x967d,0x967e,0x357b},
{0x9680,0x96fe,0x357d},
{0x9740,0x9766,0x35fc},
{0x9767,0x9767,0x246a},
{0x9768,0x976b,0x3623},
{0x976c,0x976c,0x2175},
{0x976d,0x976d,0x3627},
{0x976e,0x976e,0x246d},
{0x976f,0x977e,0x3628},
{0x9780,0x9795,0x3638},
{0x9796,0x9796,0x246b},
{0x9797,0x9797,0x225f},
{0x9798,0x979c,0x364e},
{0x979d,0x979d,0x1ece},
{0x979e,0x97a2,0x3653},
{0x97a3,0x97a3,0x2272},
{0x97a4,0x97be,0x3658},
{0x97bf,0x97bf,0x2473},
{0x97c0,0x97ed,0x3673},
{0x97ee,0x97ee,0x21fe},
{0x97ef,0x97f6,0x36a1},
{0x97f7,0x97f7,0x1efe},
{0x97f8,0x97fe,0x36a9},
{0x9840,0x9844,0x36b0},
{0x9845,0x9845,0x2475},
{0x9846,0x9848,0x36b5},
{0x9849,0x9849,0x220a},
{0x984a,0x984e,0x36b8},
{0x984f,0x984f,0x1f6f},
{0x9850,0x9870,0x36bd},
{0x9871,0x9871,0x2468},
{0x9872,0x9872,0x36de},
{0x9873,0x9873,0x2100},
{0x9874,0x987e,0x36df},
{0x9880,0x9880,0x36ea},
{0x9881,0x9881,0x2476},
{0x9882,0x988a,0x36eb},
{0x988b,0x988b,0x1f27},
{0x988c,0x988c,0x20d7},
{0x988d,0x989f,0x36f4},
{0x98a0,0x98a0,0x247c},
{0x98a1,0x98a9,0x3707},
{0x98aa,0x98aa,0x1fa1},
{0x98ab,0x98b5,0x3710},
{0x98b6,0x98b6,0x22aa},
{0x98b7,0x98b7,0x2005},
{0x98b8,0x98b9,0x371b},
{0x98ba,0x98ba,0x246c},
{0x98bb,0x98c6,0x371d},
{0x98c7,0x98c7,0x203e},
{0x98c8,0x98ca,0x3729},
{0x98cb,0x98cb,0x1e4c},
{0x98cc,0x98cf,0x372c},
{0x98d0,0x98d0,0x213a},
{0x98d1,0x98d2,0x3730},
{0x98d3,0x98d3,0x2204},
{0x98d4,0x98e2,0x3732},
{0x98e3,0x98e3,0x20c3},
{0x98e4,0x98e4,0x2140},
{0x98e5,0x98e5,0x2477},
{0x98e6,0x98ee,0x3741},
{0x98ef,0x98ef,0x2474},
{0x98f0,0x98f1,0x374a},
{0x98f2,0x98f2,0x20dd},
{0x98f3,0x98fe,0x374c},
{0x9940,0x9942,0x3758},
{0x9943,0x9943,0x1f68},
{0x9944,0x9944,0x375b},
{0x9945,0x9945,0x2185},
{0x9946,0x9965,0x375c},
{0x9966,0x9966,0x2472},
{0x9967,0x996d,0x377c},
{0x996e,0x996e,0x1eb5},
{0x996f,0x9974,0x3783},
{0x9975,0x9975,0x2478},
{0x9976,0x9979,0x3789},
{0x997a,0x997a,0x1f8b},
{0x997b,0x997b,0x2484},
{0x997c,0x997e,0x378d},
{0x9980,0x9984,0x3790},
{0x9985,0x9985,0x2699},
{0x9986,0x9988,0x3795},
{0x9989,0x9989,0x2482},
{0x998a,0x998d,0x3798},
{0x998e,0x998e,0x20a1},
{0x998f,0x9990,0x379c},
{0x9991,0x9991,0x1f92},
{0x9992,0x9998,0x379e},
{0x9999,0x9999,0x1f38},
{0x999a,0x99a8,0x37a5},
{0x99a9,0x99a9,0x2485},
{0x99aa,0x99af,0x37b4},
{0x99b0,0x99b0,0x2480},
{0x99b1,0x99b1,0x246e},
{0x99b2,0x99b2,0x37ba},
{0x99b3,0x99b3,0x247b},
{0x99b4,0x99b4,0x2486},
{0x99b5,0x99b5,0x2471},
{0x99b6,0x99bc,0x37bb},
{0x99bd,0x99bd,0x2483},
{0x99be,0x99be,0x2470},
{0x99bf,0x99bf,0x37c2},
{0x99c0,0x99c0,0x2469},
{0x99c1,0x99c1,0x37c3},
{0x99c2,0x99c2,0x247f},
{0x99c3,0x99c8,0x37c4},
{0x99c9,0x99c9,0x246f},
{0x99ca,0x99cd,0x37ca},
{0x99ce,0x99ce,0x2481},
{0x99cf,0x99d0,0x37ce},
{0x99d1,0x99d1,0x2220},
{0x99d2,0x99d9,0x37d0},
{0x99da,0x99da,0x1ff5},
{0x99db,0x99df,0x37d8},
{0x99e0,0x99e0,0x20f4},
{0x99e1,0x99e4,0x37dd},
{0x99e5,0x99e5,0x247d},
{0x99e6,0x99e7,0x37e1},
{0x99e8,0x99e8,0x2479},
{0x99e9,0x99eb,0x37e3},
{0x99ec,0x99ec,0x247e},
{0x99ed,0x99f3,0x37e6},
{0x99f4,0x99f4,0x247a},
{0x99f5,0x99fe,0x37ed},
{0x9a40,0x9a49,0x37f7},
{0x9a4a,0x9a4a,0x20e3},
{0x9a4b,0x9a56,0x3801},
{0x9a57,0x9a57,0x20ad},
{0x9a58,0x9a64,0x380d},
{0x9a65,0x9a65,0x24cb},
{0x9a66,0x9a66,0x381a},
{0x9a67,0x9a67,0x1f53},
{0x9a68,0x9a70,0x381b},
{0x9a71,0x9a71,0x2159},
{0x9a72,0x9a75,0x3824},
{0x9a76,0x9a76,0x2013},
{0x9a77,0x9a77,0x1f33},
{0x9a78,0x9a7e,0x3828},
{0x9a80,0x9a87,0x382f},
{0x9a88,0x9a88,0x1e5c},
{0x9a89,0x9a8b,0x3837},
{0x9a8c,0x9a8c,0x2488},
{0x9a8d,0x9a90,0x383a},
{0x9a91,0x9a91,0x2487},
{0x9a92,0x9a96,0x383e},
{0x9a97,0x9a97,0x248a},
{0x9a98,0x9a99,0x3843},
{0x9a9a,0x9a9a,0x2489},
{0x9a9b,0x9a9b,0x248b},
{0x9a9c,0x9a9d,0x3845},
{0x9a9e,0x9a9e,0x1f83},
{0x9a9f,0x9aa1,0x3847},
{0x9aa2,0x9aa2,0x210f},
{0x9aa3,0x9aa3,0x1fdb},
{0x9aa4,0x9aa9,0x384a},
{0x9aaa,0x9aaa,0x20af},
{0x9aab,0x9acf,0x3850},
{0x9ad0,0x9ad0,0x24c0},
{0x9ad1,0x9ad5,0x3875},
{0x9ad6,0x9ad6,0x226d},
{0x9ad7,0x9ad9,0x387a},
{0x9ada,0x9ada,0x24c1},
{0x9adb,0x9ae1,0x387d},
{0x9ae2,0x9ae2,0x20ca},
{0x9ae3,0x9ae3,0x3884},
{0x9ae4,0x9ae4,0x20e7},
{0x9ae5,0x9ae5,0x24c2},
{0x9ae6,0x9afe,0x3885},
{0x9b40,0x9b7e,0x389e},
{0x9b80,0x9bd0,0x38dd},
{0x9bd1,0x9bd1,0x23dc},
{0x9bd2,0x9bdb,0x392e},
{0x9bdc,0x9bdc,0x23db},
{0x9bdd,0x9bfe,0x3938},
{0x9c40,0x9c52,0x395a},
{0x9c53,0x9c53,0x205e},
{0x9c54,0x9c58,0x396d},
{0x9c59,0x9c59,0x2244},
{0x9c5a,0x9c5a,0x23e2},
{0x9c5b,0x9c5b,0x3972},
{0x9c5c,0x9c5c,0x20d4},
{0x9c5d,0x9c74,0x3973},
{0x9c75,0x9c75,0x219f},
{0x9c76,0x9c78,0x398b},
{0x9c79,0x9c79,0x1e66},
{0x9c7a,0x9c7e,0x398e},
{0x9c80,0x9c85,0x3993},
{0x9c86,0x9c86,0x1f63},
{0x9c87,0x9c9c,0x3999},
{0x9c9d,0x9c9d,0x23dd},
{0x9c9e,0x9caa,0x39af},
{0x9cab,0x9cab,0x216b},
{0x9cac,0x9cc9,0x39bc},
{0x9cca,0x9cca,0x22b5},
{0x9ccb,0x9cce,0x39da},
{0x9ccf,0x9ccf,0x1f26},
{0x9cd0,0x9ce5,0x39de},
{0x9ce6,0x9ce6,0x1e63},
{0x9ce7,0x9ce7,0x2088},
{0x9ce8,0x9ceb,0x39f4},
{0x9cec,0x9cec,0x1ebd},
{0x9ced,0x9ced,0x39f8},
{0x9cee,0x9cee,0x2341},
{0x9cef,0x9cfa,0x39f9},
{0x9cfb,0x9cfb,0x1f4b},
{0x9cfc,0x9cfd,0x3a05},
{0x9cfe,0x9cfe,0x2292},
{0x9d40,0x9d41,0x3a07},
{0x9d42,0x9d42,0x2124},
{0x9d43,0x9d45,0x3a09},
{0x9d46,0x9d46,0x2048},
{0x9d47,0x9d47,0x23e0},
{0x9d48,0x9d4c,0x3a0c},
{0x9d4d,0x9d4d,0x2077},
{0x9d4e,0x9d4e,0x3a11},
{0x9d4f,0x9d4f,0x223a},
{0x9d50,0x9d60,0x3a12},
{0x9d61,0x9d61,0x20b1},
{0x9d62,0x9d67,0x3a23},
{0x9d68,0x9d68,0x1f41},
{0x9d69,0x9d69,0x201c},
{0x9d6a,0x9d6d,0x3a29},
{0x9d6e,0x9d6e,0x22b8},
{0x9d6f,0x9d70,0x3a2d},
{0x9d71,0x9d71,0x2276},
{0x9d72,0x9d74,0x3a2f},
{0x9d75,0x9d75,0x1f9b},
{0x9d76,0x9d7a,0x3a32},
{0x9d7b,0x9d7b,0x1f9f},
{0x9d7c,0x9d7c,0x3a37},
{0x9d7d,0x9d7d,0x25ca},
{0x9d7e,0x9d7e,0x3a38},
{0x9d80,0x9d89,0x3a39},
{0x9d8a,0x9d8a,0x20be},
{0x9d8b,0x9d8c,0x3a43},
{0x9d8d,0x9d8d,0x1fb4},
{0x9d8e,0x9d90,0x3a45},
{0x9d91,0x9d91,0x23d7},
{0x9d92,0x9d98,0x3a48},
{0x9d99,0x9d99,0x2105},
{0x9d9a,0x9da0,0x3a4f},
{0x9da1,0x9da1,0x23e1},
{0x9da2,0x9da2,0x1fec},
{0x9da3,0x9da6,0x3a56},
{0x9da7,0x9da7,0x23ea},
{0x9da8,0x9dab,0x3a5a},
{0x9dac,0x9dac,0x23e3},
{0x9dad,0x9dad,0x210e},
{0x9dae,0x9db1,0x3a5e},
{0x9db2,0x9db2,0x1fa6},
{0x9db3,0x9db3,0x2004},
{0x9db4,0x9dbd,0x3a62},
{0x9dbe,0x9dbe,0x1f9d},
{0x9dbf,0x9dc5,0x3a6c},
{0x9dc6,0x9dc6,0x23e5},
{0x9dc7,0x9dc8,0x3a73},
{0x9dc9,0x9dc9,0x2264},
{0x9dca,0x9dcc,0x3a75},
{0x9dcd,0x9dcd,0x24e2},
{0x9dce,0x9dd1,0x3a78},
{0x9dd2,0x9dd2,0x23de},
{0x9dd3,0x9dd4,0x3a7c},
{0x9dd5,0x9dd5,0x1ec4},
{0x9dd6,0x9de0,0x3a7e},
{0x9de1,0x9de1,0x22b6},
{0x9de2,0x9de2,0x20a9},
{0x9de3,0x9df0,0x3a89},
{0x9df1,0x9df1,0x212b},
{0x9df2,0x9df3,0x3a97},
{0x9df4,0x9df4,0x20a5},
{0x9df5,0x9df6,0x3a99},
{0x9df7,0x9df7,0x268b},
{0x9df8,0x9df9,0x3a9b},
{0x9dfa,0x9dfa,0x1f76},
{0x9dfb,0x9dfc,0x3a9d},
{0x9dfd,0x9dfd,0x216d},
{0x9dfe,0x9dfe,0x3a9f},
{0x9e40,0x9e44,0x3aa0},
{0x9e45,0x9e45,0x2001},
{0x9e46,0x9e47,0x3aa5},
{0x9e48,0x9e48,0x2191},
{0x9e49,0x9e49,0x1e50},
{0x9e4a,0x9e51,0x3aa7},
{0x9e52,0x9e52,0x1f9c},
{0x9e53,0x9e53,0x3aaf},
{0x9e54,0x9e54,0x23da},
{0x9e55,0x9e55,0x3ab0},
{0x9e56,0x9e56,0x2053},
{0x9e57,0x9e5c,0x3ab1},
{0x9e5d,0x9e5d,0x23e9},
{0x9e5e,0x9e5e,0x23e4},
{0x9e5f,0x9e60,0x3ab7},
{0x9e61,0x9e61,0x21d5},
{0x9e62,0x9e62,0x3ab9},
{0x9e63,0x9e63,0x23e6},
{0x9e64,0x9e66,0x3aba},
{0x9e67,0x9e67,0x23df},
{0x9e68,0x9e6b,0x3abd},
{0x9e6c,0x9e6c,0x1e4f},
{0x9e6d,0x9e6e,0x3ac1},
{0x9e6f,0x9e6f,0x23d9},
{0x9e70,0x9e71,0x3ac3},
{0x9e72,0x9e72,0x2014},
{0x9e73,0x9e73,0x3ac5},
{0x9e74,0x9e74,0x23ec},
{0x9e75,0x9e75,0x23eb},
{0x9e76,0x9e7a,0x3ac6},
{0x9e7b,0x9e7b,0x23d8},
{0x9e7c,0x9e7c,0x23ee},
{0x9e7d,0x9e7e,0x3acb},
{0x9e80,0x9e84,0x3acd},
{0x9e85,0x9e85,0x268e},
{0x9e86,0x9e86,0x3ad2},
{0x9e87,0x9e87,0x23ed},
{0x9e88,0x9e90,0x3ad3},
{0x9e91,0x9e91,0x1ffa},
{0x9e92,0x9e95,0x3adc},
{0x9e96,0x9e96,0x23d6},
{0x9e97,0x9e97,0x23e8},
{0x9e98,0x9ea1,0x3ae0},
{0x9ea2,0x9ea2,0x2106},
{0x9ea3,0x9ea5,0x3aea},
{0x9ea6,0x9ea6,0x200b},
{0x9ea7,0x9ea8,0x3aed},
{0x9ea9,0x9ea9,0x2166},
{0x9eaa,0x9ead,0x3aef},
{0x9eae,0x9eae,0x23ef},
{0x9eaf,0x9eb2,0x3af3},
{0x9eb3,0x9eb3,0x2189},
{0x9eb4,0x9eb4,0x2058},
{0x9eb5,0x9eb6,0x3af7},
{0x9eb7,0x9eb7,0x23e7},
{0x9eb8,0x9ef4,0x3af9},
{0x9ef5,0x9ef5,0x21a3},
{0x9ef6,0x9efe,0x3b36},
{0x9f40,0x9f4d,0x3b3f},
{0x9f4e,0x9f4e,0x217a},
{0x9f4f,0x9f6e,0x3b4d},
{0x9f6f,0x9f6f,0x21a5},
{0x9f70,0x9f7e,0x3b6d},
{0x9f80,0x9f91,0x3b7c},
{0x9f92,0x9f92,0x2022},
{0x9f93,0x9f97,0x3b8e},
{0x9f98,0x9f98,0x24d6},
{0x9f99,0x9fa5,0x3b93},
{0x9fa6,0x9fa6,0x233a},
{0x9fa7,0x9fa8,0x3ba0},
{0x9fa9,0x9fa9,0x1eee},
{0x9faa,0x9fab,0x3ba2},
{0x9fac,0x9fac,0x24d5},
{0x9fad,0x9fc8,0x3ba4},
{0x9fc9,0x9fc9,0x2228},
{0x9fca,0x9fcc,0x3bc0},
{0x9fcd,0x9fcd,0x24d7},
{0x9fce,0x9fe0,0x3bc3},
{0x9fe1,0x9fe1,0x20fc},
{0x9fe2,0x9fea,0x3bd6},
{0x9feb,0x9feb,0x1e87},
{0x9fec,0x9fed,0x3bdf},
{0x9fee,0x9fee,0x24d8},
{0x9fef,0x9ff3,0x3be1},
{0x9ff4,0x9ff4,0x1eba},
{0x9ff5,0x9ffc,0x3be6},
{0x9ffd,0x9ffd,0x2119},
{0x9ffe,0x9ffe,0x3bee},
{0xa040,0xa042,0x3bef},
{0xa043,0xa043,0x216c},
{0xa044,0xa045,0x3bf2},
{0xa046,0xa046,0x24d9},
{0xa047,0xa048,0x3bf4},
{0xa049,0xa049,0x2227},
{0xa04a,0xa04d,0x3bf6},
{0xa04e,0xa04e,0x1e5f},
{0xa04f,0xa053,0x3bfa},
{0xa054,0xa054,0x229f},
{0xa055,0xa059,0x3bff},
{0xa05a,0xa05a,0x1f5d},
{0xa05b,0xa060,0x3c04},
{0xa061,0xa061,0x1fbc},
{0xa062,0xa062,0x3c0a},
{0xa063,0xa063,0x24da},
{0xa064,0xa070,0x3c0b},
{0xa071,0xa071,0x2149},
{0xa072,0xa073,0x3c18},
{0xa074,0xa074,0x2046},
{0xa075,0xa07e,0x3c1a},
{0xa080,0xa080,0x2000},
{0xa081,0xa090,0x3c24},
{0xa091,0xa091,0x2190},
{0xa092,0xa093,0x3c34},
{0xa094,0xa094,0x2208},
{0xa095,0xa095,0x3c36},
{0xa096,0xa096,0x1ee6},
{0xa097,0xa0a8,0x3c37},
{0xa0a9,0xa0a9,0x24c3},
{0xa0aa,0xa0be,0x3c49},
{0xa0bf,0xa0bf,0x20cc},
{0xa0c0,0xa0cd,0x3c5e},
{0xa0ce,0xa0ce,0x2340},
{0xa0cf,0xa0d8,0x3c6c},
{0xa0d9,0xa0d9,0x1ed1},
{0xa0da,0xa0dd,0x3c76},
{0xa0de,0xa0de,0x21ac},
{0xa0df,0xa0ed,0x3c7a},
{0xa0ee,0xa0ee,0x22af},
{0xa0ef,0xa0fe,0x3c89},
{0xa1a1,0xa1fe,0x60},
{0xa2a1,0xa2aa,0x26a9},
{0xa2b1,0xa2e2,0xbe},
{0xa2e5,0xa2ee,0xf0},
{0xa2f1,0xa2fc,0xfa},
{0xa3a1,0xa3fe,0x106},
{0xa4a1,0xa4f3,0x164},
{0xa5a1,0xa5f6,0x1b7},
{0xa6a1,0xa6b8,0x20d},
{0xa6c1,0xa6f5,0x225},
{0xa7a1,0xa7c1,0x25a},
{0xa7d1,0xa7f1,0x27b},
{0xa840,0xa87e,0x26b3},
{0xa880,0xa895,0x26f2},
{0xa8a1,0xa8c0,0x29c},
{0xa8c5,0xa8ea,0x2bc},
{0xa940,0xa957,0x2708},
{0xa959,0xa95a,0x2720},
{0xa95c,0xa95c,0x2722},
{0xa960,0xa97e,0x2723},
{0xa980,0xa995,0x2742},
{0xa996,0xa996,0x1e17},
{0xa9a4,0xa9ef,0x2e2},
{0xaa40,0xaa4c,0x3c99},
{0xaa4d,0xaa4d,0x21b6},
{0xaa4e,0xaa4e,0x1e3d},
{0xaa4f,0xaa70,0x3ca6},
{0xaa71,0xaa71,0x2235},
{0xaa72,0xaa72,0x3cc8},
{0xaa73,0xaa73,0x2397},
{0xaa74,0xaa76,0x3cc9},
{0xaa77,0xaa77,0x2395},
{0xaa78,0xaa79,0x3ccc},
{0xaa7a,0xaa7a,0x223f},
{0xaa7b,0xaa7b,0x212a},
{0xaa7c,0xaa7e,0x3cce},
{0xaa80,0xaa99,0x3cd1},
{0xaa9a,0xaa9a,0x1ed2},
{0xaa9b,0xaa9b,0x3ceb},
{0xaa9c,0xaa9c,0x2396},
{0xaa9d,0xaa9d,0x2398},
{0xaa9e,0xaa9e,0x3cec},
{0xaa9f,0xaa9f,0x20a2},
{0xaaa0,0xaaa0,0x3ced},
{0xaaa1,0xaafe,0x32e},
{0xab40,0xab40,0x1f64},
{0xab41,0xab42,0x3cee},
{0xab43,0xab43,0x202b},
{0xab44,0xab44,0x3cf0},
{0xab45,0xab45,0x2394},
{0xab46,0xab46,0x2139},
{0xab47,0xab47,0x3cf1},
{0xab48,0xab48,0x215f},
{0xab49,0xab49,0x21c1},
{0xab4a,0xab4a,0x239a},
{0xab4b,0xab4c,0x3cf2},
{0xab4d,0xab4d,0x2399},
{0xab4e,0xab7e,0x3cf4},
{0xab80,0xaba0,0x3d25},
{0xaba1,0xabc0,0x38c},
{0xac40,0xac45,0x3d46},
{0xac46,0xac46,0x21c0},
{0xac47,0xac70,0x3d4c},
{0xac71,0xac71,0x2460},
{0xac72,0xac7b,0x3d76},
{0xac7c,0xac7c,0x245c},
{0xac7d,0xac7e,0x3d80},
{0xac80,0xac8c,0x3d82},
{0xac8d,0xac8d,0x215d},
{0xac8e,0xac92,0x3d8f},
{0xac93,0xac93,0x2225},
{0xac94,0xac94,0x206a},
{0xac95,0xaca0,0x3d94},
{0xad40,0xad48,0x3da0},
{0xad49,0xad49,0x2461},
{0xad4a,0xad5d,0x3da9},
{0xad5e,0xad5e,0x245b},
{0xad5f,0xad60,0x3dbd},
{0xad61,0xad61,0x2462},
{0xad62,0xad67,0x3dbf},
{0xad68,0xad68,0x1f54},
{0xad69,0xad73,0x3dc5},
{0xad74,0xad74,0x245f},
{0xad75,0xad7e,0x3dd0},
{0xad80,0xad81,0x3dda},
{0xad82,0xad82,0x20ec},
{0xad83,0xad86,0x3ddc},
{0xad87,0xad87,0x245d},
{0xad88,0xad8a,0x3de0},
{0xad8b,0xad8b,0x2463},
{0xad8c,0xad90,0x3de3},
{0xad91,0xad91,0x2464},
{0xad92,0xada0,0x3de8},
{0xae40,0xae53,0x3df7},
{0xae54,0xae54,0x24a5},
{0xae55,0xae61,0x3e0b},
{0xae62,0xae62,0x1e70},
{0xae63,0xae7e,0x3e18},
{0xae80,0xae80,0x208f},
{0xae81,0xae84,0x3e34},
{0xae85,0xae85,0x1e42},
{0xae86,0xae8a,0x3e38},
{0xae8b,0xae8b,0x1f4e},
{0xae8c,0xae93,0x3e3d},
{0xae94,0xae94,0x1eb1},
{0xae95,0xae9f,0x3e45},
{0xaea0,0xaea0,0x1e8b},
{0xaf40,0xaf63,0x3e50},
{0xaf64,0xaf64,0x1fc6},
{0xaf65,0xaf7a,0x3e74},
{0xaf7b,0xaf7b,0x25ae},
{0xaf7c,0xaf7e,0x3e8a},
{0xaf80,0xaf81,0x3e8d},
{0xaf82,0xaf82,0x1f01},
{0xaf83,0xaf83,0x2200},
{0xaf84,0xaf8d,0x3e8f},
{0xaf8e,0xaf8e,0x25b2},
{0xaf8f,0xaf8f,0x1e97},
{0xaf90,0xaf90,0x3e99},
{0xaf91,0xaf91,0x20ab},
{0xaf92,0xaf9b,0x3e9a},
{0xaf9c,0xaf9c,0x25b3},
{0xaf9d,0xaf9e,0x3ea4},
{0xaf9f,0xaf9f,0x2028},
{0xafa0,0xafa0,0x3ea6},
{0xb040,0xb040,0x3ea7},
{0xb041,0xb042,0x25af},
{0xb043,0xb043,0x3ea8},
{0xb044,0xb044,0x25b1},
{0xb045,0xb04e,0x3ea9},
{0xb04f,0xb04f,0x25ac},
{0xb050,0xb053,0x3eb3},
{0xb054,0xb054,0x1e4e},
{0xb055,0xb056,0x3eb7},
{0xb057,0xb057,0x2202},
{0xb058,0xb058,0x25ab},
{0xb059,0xb059,0x2286},
{0xb05a,0xb05a,0x3eb9},
{0xb05b,0xb05b,0x25ad},
{0xb05c,0xb05c,0x3eba},
{0xb05d,0xb05d,0x25b6},
{0xb05e,0xb05e,0x3ebb},
{0xb05f,0xb05f,0x21e3},
{0xb060,0xb061,0x25b4},
{0xb062,0xb062,0x222f},
{0xb063,0xb063,0x2165},
{0xb064,0xb064,0x25b7},
{0xb065,0xb06b,0x3ebc},
{0xb06c,0xb06c,0x1ee9},
{0xb06d,0xb07c,0x3ec3},
{0xb07d,0xb07d,0x1e25},
{0xb07e,0xb07e,0x3ed3},
{0xb080,0xb096,0x3ed4},
{0xb097,0xb097,0x25c0},
{0xb098,0xb098,0x3eeb},
{0xb099,0xb099,0x229a},
{0xb09a,0xb0a0,0x3eec},
{0xb0a1,0xb0fe,0x3ac},
{0xb140,0xb14a,0x3ef3},
{0xb14b,0xb14b,0x226e},
{0xb14c,0xb14c,0x3efe},
{0xb14d,0xb14d,0x1fbd},
{0xb14e,0xb14e,0x3eff},
{0xb14f,0xb14f,0x1f84},
{0xb150,0xb150,0x20b2},
{0xb151,0xb151,0x3f00},
{0xb152,0xb152,0x2043},
{0xb153,0xb17e,0x3f01},
{0xb180,0xb196,0x3f2d},
{0xb197,0xb197,0x2684},
{0xb198,0xb1a0,0x3f44},
{0xb1a1,0xb1fe,0x40a},
{0xb240,0xb240,0x3f4d},
{0xb241,0xb241,0x24ef},
{0xb242,0xb266,0x3f4e},
{0xb267,0xb267,0x24ee},
{0xb268,0xb26c,0x3f73},
{0xb26d,0xb26d,0x2074},
{0xb26e,0xb273,0x3f78},
{0xb274,0xb274,0x2687},
{0xb275,0xb27e,0x3f7e},
{0xb280,0xb280,0x24f0},
{0xb281,0xb288,0x3f88},
{0xb289,0xb289,0x268d},
{0xb28a,0xb299,0x3f90},
{0xb29a,0xb29a,0x22a0},
{0xb29b,0xb2a0,0x3fa0},
{0xb2a1,0xb2fe,0x468},
{0xb340,0xb342,0x3fa6},
{0xb343,0xb343,0x1fab},
{0xb344,0xb36f,0x3fa9},
{0xb370,0xb370,0x26a8},
{0xb371,0xb37e,0x3fd5},
{0xb380,0xb387,0x3fe3},
{0xb388,0xb388,0x24e9},
{0xb389,0xb38b,0x3feb},
{0xb38c,0xb38c,0x24e5},
{0xb38d,0xb38d,0x3fee},
{0xb38e,0xb38e,0x21fa},
{0xb38f,0xb3a0,0x3fef},
{0xb3a1,0xb3fe,0x4c6},
{0xb440,0xb453,0x4001},
{0xb454,0xb454,0x2148},
{0xb455,0xb457,0x4015},
{0xb458,0xb458,0x24e4},
{0xb459,0xb45d,0x4018},
{0xb45e,0xb45e,0x24e6},
{0xb45f,0xb45f,0x20f7},
{0xb460,0xb460,0x401d},
{0xb461,0xb461,0x206b},
{0xb462,0xb474,0x401e},
{0xb475,0xb475,0x22a7},
{0xb476,0xb47d,0x4031},
{0xb47e,0xb47e,0x24ec},
{0xb480,0xb482,0x4039},
{0xb483,0xb483,0x24eb},
{0xb484,0xb488,0x403c},
{0xb489,0xb489,0x24e3},
{0xb48a,0xb492,0x4041},
{0xb493,0xb493,0x24ea},
{0xb494,0xb4a0,0x404a},
{0xb4a1,0xb4fe,0x524},
{0xb540,0xb540,0x4057},
{0xb541,0xb541,0x1e92},
{0xb542,0xb54a,0x4058},
{0xb54b,0xb54b,0x1e27},
{0xb54c,0xb555,0x4061},
{0xb556,0xb556,0x1fe6},
{0xb557,0xb559,0x406b},
{0xb55a,0xb55a,0x24e7},
{0xb55b,0xb55b,0x2012},
{0xb55c,0xb55c,0x1eec},
{0xb55d,0xb560,0x406e},
{0xb561,0xb561,0x24e8},
{0xb562,0xb57e,0x4072},
{0xb580,0xb59b,0x408f},
{0xb59c,0xb59c,0x1f66},
{0xb59d,0xb59d,0x24dc},
{0xb59e,0xb5a0,0x40ab},
{0xb5a1,0xb5fe,0x582},
{0xb640,0xb651,0x40ae},
{0xb652,0xb652,0x26a2},
{0xb653,0xb654,0x40c0},
{0xb655,0xb655,0x24dd},
{0xb656,0xb658,0x40c2},
{0xb659,0xb659,0x200e},
{0xb65a,0xb65a,0x40c5},
{0xb65b,0xb65b,0x24db},
{0xb65c,0xb65c,0x1eb8},
{0xb65d,0xb67e,0x40c6},
{0xb680,0xb6a0,0x40e8},
{0xb6a1,0xb6fe,0x5e0},
{0xb740,0xb74d,0x4109},
{0xb74e,0xb74e,0x2295},
{0xb74f,0xb750,0x4117},
{0xb751,0xb751,0x1e80},
{0xb752,0xb758,0x4119},
{0xb759,0xb759,0x2677},
{0xb75a,0xb763,0x4120},
{0xb764,0xb764,0x2624},
{0xb765,0xb765,0x1f69},
{0xb766,0xb766,0x222b},
{0xb767,0xb776,0x412a},
{0xb777,0xb777,0x257f},
{0xb778,0xb778,0x1f5b},
{0xb779,0xb77e,0x413a},
{0xb780,0xb780,0x219b},
{0xb781,0xb781,0x4140},
{0xb782,0xb782,0x267d},
{0xb783,0xb7a0,0x4141},
{0xb7a1,0xb7fe,0x63e},
{0xb840,0xb842,0x415f},
{0xb843,0xb843,0x21a0},
{0xb844,0xb844,0x2186},
{0xb845,0xb845,0x4162},
{0xb846,0xb846,0x20ed},
{0xb847,0xb84c,0x4163},
{0xb84d,0xb84d,0x25b9},
{0xb84e,0xb850,0x4169},
{0xb851,0xb851,0x1fea},
{0xb852,0xb859,0x416c},
{0xb85a,0xb85a,0x1ea4},
{0xb85b,0xb85b,0x20e1},
{0xb85c,0xb85c,0x4174},
{0xb85d,0xb85d,0x25b8},
{0xb85e,0xb85e,0x2260},
{0xb85f,0xb85f,0x4175},
{0xb860,0xb860,0x20e2},
{0xb861,0xb876,0x4176},
{0xb877,0xb877,0x2141},
{0xb878,0xb87e,0x418c},
{0xb880,0xb881,0x4193},
{0xb882,0xb882,0x1fc7},
{0xb883,0xb8a0,0x4195},
{0xb8a1,0xb8fe,0x69c},
{0xb940,0xb94f,0x41b3},
{0xb950,0xb950,0x1e41},
{0xb951,0xb960,0x41c3},
{0xb961,0xb961,0x25e4},
{0xb962,0xb97a,0x41d3},
{0xb97b,0xb97b,0x1f86},
{0xb97c,0xb97e,0x41ec},
{0xb980,0xb99c,0x41ef},
{0xb99d,0xb99d,0x1fb3},
{0xb99e,0xb99f,0x420c},
{0xb9a0,0xb9a0,0x1eef},
{0xb9a1,0xb9fe,0x6fa},
{0xba40,0xba41,0x420e},
{0xba42,0xba42,0x22a4},
{0xba43,0xba43,0x4210},
{0xba44,0xba44,0x25e8},
{0xba45,0xba55,0x4211},
{0xba56,0xba56,0x25e3},
{0xba57,0xba58,0x4222},
{0xba59,0xba59,0x2111},
{0xba5a,0xba5f,0x4224},
{0xba60,0xba60,0x25e6},
{0xba61,0xba69,0x422a},
{0xba6a,0xba6a,0x25e7},
{0xba6b,0xba73,0x4233},
{0xba74,0xba74,0x2041},
{0xba75,0xba7e,0x423c},
{0xba80,0xba83,0x4246},
{0xba84,0xba84,0x25ea},
{0xba85,0xba85,0x424a},
{0xba86,0xba86,0x1f8f},
{0xba87,0xba87,0x424b},
{0xba88,0xba88,0x25ec},
{0xba89,0xba8c,0x424c},
{0xba8d,0xba8d,0x25eb},
{0xba8e,0xba9d,0x4250},
{0xba9e,0xba9e,0x20d0},
{0xba9f,0xba9f,0x201d},
{0xbaa0,0xbaa0,0x4260},
{0xbaa1,0xbafe,0x758},
{0xbb40,0xbb40,0x1ff7},
{0xbb41,0xbb48,0x4261},
{0xbb49,0xbb49,0x1e8d},
{0xbb4a,0xbb57,0x4269},
{0xbb58,0xbb58,0x25e9},
{0xbb59,0xbb5a,0x4277},
{0xbb5b,0xbb5b,0x25ee},
{0xbb5c,0xbb5c,0x203a},
{0xbb5d,0xbb5f,0x4279},
{0xbb60,0xbb60,0x2693},
{0xbb61,0xbb64,0x427c},
{0xbb65,0xbb65,0x25e5},
{0xbb66,0xbb66,0x25ed},
{0xbb67,0xbb67,0x4280},
{0xbb68,0xbb68,0x2009},
{0xbb69,0xbb69,0x4281},
{0xbb6a,0xbb6a,0x2065},
{0xbb6b,0xbb6d,0x4282},
{0xbb6e,0xbb6e,0x26a3},
{0xbb6f,0xbb7e,0x4285},
{0xbb80,0xbba0,0x4295},
{0xbba1,0xbbfe,0x7b6},
{0xbc40,0xbc51,0x42b6},
{0xbc52,0xbc52,0x25f5},
{0xbc53,0xbc53,0x1efc},
{0xbc54,0xbc59,0x42c8},
{0xbc5a,0xbc5a,0x2024},
{0xbc5b,0xbc60,0x42ce},
{0xbc61,0xbc61,0x269b},
{0xbc62,0xbc62,0x42d4},
{0xbc63,0xbc63,0x25f3},
{0xbc64,0xbc64,0x42d5},
{0xbc65,0xbc65,0x22d9},
{0xbc66,0xbc66,0x42d6},
{0xbc67,0xbc67,0x25f4},
{0xbc68,0xbc68,0x42d7},
{0xbc69,0xbc69,0x241b},
{0xbc6a,0xbc6c,0x42d8},
{0xbc6d,0xbc6d,0x1fc8},
{0xbc6e,0xbc6e,0x42db},
{0xbc6f,0xbc6f,0x1f7b},
{0xbc70,0xbc70,0x42dc},
{0xbc71,0xbc71,0x241d},
{0xbc72,0xbc72,0x42dd},
{0xbc73,0xbc73,0x224c},
{0xbc74,0xbc74,0x1f48},
{0xbc75,0xbc75,0x241c},
{0xbc76,0xbc77,0x241e},
{0xbc78,0xbc78,0x20ff},
{0xbc79,0xbc79,0x219a},
{0xbc7a,0xbc7a,0x42de},
{0xbc7b,0xbc7b,0x2091},
{0xbc7c,0xbc7d,0x42df},
{0xbc7e,0xbc7e,0x20a7},
{0xbc80,0xbc81,0x42e1},
{0xbc82,0xbc82,0x2423},
{0xbc83,0xbc83,0x1e9b},
{0xbc84,0xbc84,0x2422},
{0xbc85,0xbc85,0x42e3},
{0xbc86,0xbc86,0x2110},
{0xbc87,0xbc87,0x42e4},
{0xbc88,0xbc88,0x228d},
{0xbc89,0xbc89,0x1f71},
{0xbc8a,0xbc8a,0x1ef8},
{0xbc8b,0xbc8b,0x2421},
{0xbc8c,0xbc8e,0x42e5},
{0xbc8f,0xbc8f,0x1ef3},
{0xbc90,0xbc99,0x42e8},
{0xbc9a,0xbc9a,0x21b1},
{0xbc9b,0xbc9b,0x2426},
{0xbc9c,0xbc9c,0x2425},
{0xbc9d,0xbc9d,0x2120},
{0xbc9e,0xbca0,0x42f2},
{0xbca1,0xbcfe,0x814},
{0xbd40,0xbd41,0x42f5},
{0xbd42,0xbd42,0x211a},
{0xbd43,0xbd43,0x2424},
{0xbd44,0xbd44,0x42f7},
{0xbd45,0xbd45,0x2428},
{0xbd46,0xbd47,0x42f8},
{0xbd48,0xbd48,0x242a},
{0xbd49,0xbd49,0x2429},
{0xbd4a,0xbd4a,0x42fa},
{0xbd4b,0xbd4b,0x2294},
{0xbd4c,0xbd4c,0x42fb},
{0xbd4d,0xbd4d,0x22be},
{0xbd4e,0xbd4e,0x42fc},
{0xbd4f,0xbd4f,0x1e31},
{0xbd50,0xbd56,0x42fd},
{0xbd57,0xbd57,0x242c},
{0xbd58,0xbd58,0x4304},
{0xbd59,0xbd59,0x1fb5},
{0xbd5a,0xbd65,0x4305},
{0xbd66,0xbd66,0x242b},
{0xbd67,0xbd67,0x1faf},
{0xbd68,0xbd69,0x4311},
{0xbd6a,0xbd6a,0x2068},
{0xbd6b,0xbd6b,0x21e4},
{0xbd6c,0xbd6e,0x4313},
{0xbd6f,0xbd6f,0x1f21},
{0xbd70,0xbd70,0x4316},
{0xbd71,0xbd71,0x2101},
{0xbd72,0xbd78,0x4317},
{0xbd79,0xbd79,0x217c},
{0xbd7a,0xbd7a,0x214a},
{0xbd7b,0xbd7b,0x242d},
{0xbd7c,0xbd7d,0x431e},
{0xbd7e,0xbd7e,0x1fd4},
{0xbd80,0xbd80,0x4320},
{0xbd81,0xbd81,0x1fd1},
{0xbd82,0xbd88,0x4321},
{0xbd89,0xbd89,0x1e33},
{0xbd8a,0xbd8a,0x4328},
{0xbd8b,0xbd8b,0x242f},
{0xbd8c,0xbd8d,0x4329},
{0xbd8e,0xbd8e,0x242e},
{0xbd8f,0xbd8f,0x432b},
{0xbd90,0xbd90,0x2430},
{0xbd91,0xbd91,0x21db},
{0xbd92,0xbd96,0x432c},
{0xbd97,0xbd97,0x2158},
{0xbd98,0xbd9a,0x4331},
{0xbd9b,0xbd9b,0x1fc2},
{0xbd9c,0xbda0,0x4334},
{0xbda1,0xbdfe,0x872},
{0xbe40,0xbe42,0x4339},
{0xbe43,0xbe43,0x22b9},
{0xbe44,0xbe44,0x433c},
{0xbe45,0xbe45,0x2436},
{0xbe46,0xbe48,0x433d},
{0xbe49,0xbe49,0x1e8e},
{0xbe4a,0xbe4a,0x2439},
{0xbe4b,0xbe50,0x4340},
{0xbe51,0xbe51,0x21c5},
{0xbe52,0xbe52,0x2437},
{0xbe53,0xbe53,0x2192},
{0xbe54,0xbe54,0x4346},
{0xbe55,0xbe55,0x243a},
{0xbe56,0xbe56,0x1f19},
{0xbe57,0xbe57,0x218c},
{0xbe58,0xbe58,0x1e40},
{0xbe59,0xbe59,0x22b3},
{0xbe5a,0xbe5c,0x4347},
{0xbe5d,0xbe5d,0x205f},
{0xbe5e,0xbe5e,0x2438},
{0xbe5f,0xbe5f,0x2432},
{0xbe60,0xbe60,0x2274},
{0xbe61,0xbe61,0x434a},
{0xbe62,0xbe62,0x1e9c},
{0xbe63,0xbe63,0x2431},
{0xbe64,0xbe64,0x2085},
{0xbe65,0xbe68,0x434b},
{0xbe69,0xbe69,0x2435},
{0xbe6a,0xbe6b,0x434f},
{0xbe6c,0xbe6c,0x243b},
{0xbe6d,0xbe6e,0x4351},
{0xbe6f,0xbe6f,0x1fb7},
{0xbe70,0xbe70,0x2433},
{0xbe71,0xbe75,0x4353},
{0xbe76,0xbe76,0x2054},
{0xbe77,0xbe77,0x21de},
{0xbe78,0xbe78,0x4358},
{0xbe79,0xbe79,0x2434},
{0xbe7a,0xbe7b,0x4359},
{0xbe7c,0xbe7c,0x243d},
{0xbe7d,0xbe7d,0x1f89},
{0xbe7e,0xbe7e,0x243c},
{0xbe80,0xbe82,0x435b},
{0xbe83,0xbe83,0x1f6e},
{0xbe84,0xbe84,0x1ed8},
{0xbe85,0xbe85,0x435e},
{0xbe86,0xbe86,0x1ebf},
{0xbe87,0xbe87,0x2445},
{0xbe88,0xbe88,0x435f},
{0xbe89,0xbe89,0x2249},
{0xbe8a,0xbe8b,0x4360},
{0xbe8c,0xbe8c,0x2441},
{0xbe8d,0xbe8d,0x4362},
{0xbe8e,0xbe8e,0x1e47},
{0xbe8f,0xbe8f,0x1f56},
{0xbe90,0xbe91,0x4363},
{0xbe92,0xbe92,0x2086},
{0xbe93,0xbe94,0x4365},
{0xbe95,0xbe95,0x2196},
{0xbe96,0xbe96,0x4367},
{0xbe97,0xbe97,0x2443},
{0xbe98,0xbe98,0x243f},
{0xbe99,0xbe99,0x4368},
{0xbe9a,0xbe9a,0x2023},
{0xbe9b,0xbe9b,0x4369},
{0xbe9c,0xbe9c,0x2442},
{0xbe9d,0xbe9e,0x436a},
{0xbe9f,0xbe9f,0x243e},
{0xbea0,0xbea0,0x436c},
{0xbea1,0xbefe,0x8d0},
{0xbf40,0xbf40,0x26a6},
{0xbf41,0xbf4c,0x436d},
{0xbf4d,0xbf4d,0x234e},
{0xbf4e,0xbf4e,0x2446},
{0xbf4f,0xbf4f,0x244b},
{0xbf50,0xbf50,0x2444},
{0xbf51,0xbf54,0x4379},
{0xbf55,0xbf55,0x2427},
{0xbf56,0xbf56,0x244c},
{0xbf57,0xbf5f,0x437d},
{0xbf60,0xbf60,0x1f0f},
{0xbf61,0xbf61,0x4386},
{0xbf62,0xbf62,0x2447},
{0xbf63,0xbf63,0x2449},
{0xbf64,0xbf64,0x2448},
{0xbf65,0xbf67,0x4387},
{0xbf68,0xbf68,0x21c2},
{0xbf69,0xbf6b,0x438a},
{0xbf6c,0xbf6c,0x216e},
{0xbf6d,0xbf6f,0x438d},
{0xbf70,0xbf70,0x1f03},
{0xbf71,0xbf71,0x4390},
{0xbf72,0xbf72,0x244a},
{0xbf73,0xbf73,0x215c},
{0xbf74,0xbf75,0x4391},
{0xbf76,0xbf76,0x22bb},
{0xbf77,0xbf77,0x2450},
{0xbf78,0xbf78,0x4393},
{0xbf79,0xbf79,0x2694},
{0xbf7a,0xbf7a,0x244f},
{0xbf7b,0xbf7b,0x25f6},
{0xbf7c,0xbf7c,0x2051},
{0xbf7d,0xbf7d,0x4394},
{0xbf7e,0xbf7e,0x244e},
{0xbf80,0xbf81,0x4395},
{0xbf82,0xbf82,0x22ba},
{0xbf83,0xbf83,0x1f6d},
{0xbf84,0xbf88,0x4397},
{0xbf89,0xbf89,0x2452},
{0xbf8a,0xbf8a,0x2451},
{0xbf8b,0xbf94,0x439c},
{0xbf95,0xbf95,0x2455},
{0xbf96,0xbf96,0x43a6},
{0xbf97,0xbf97,0x2289},
{0xbf98,0xbf98,0x2116},
{0xbf99,0xbf9c,0x43a7},
{0xbf9d,0xbf9d,0x2454},
{0xbf9e,0xbfa0,0x43ab},
{0xbfa1,0xbffe,0x92e},
{0xc040,0xc040,0x20fb},
{0xc041,0xc043,0x43ae},
{0xc044,0xc044,0x2440},
{0xc045,0xc04a,0x43b1},
{0xc04b,0xc04b,0x2126},
{0xc04c,0xc04c,0x1f61},
{0xc04d,0xc04d,0x269d},
{0xc04e,0xc04e,0x43b7},
{0xc04f,0xc04f,0x1f8a},
{0xc050,0xc050,0x2456},
{0xc051,0xc051,0x2459},
{0xc052,0xc052,0x2458},
{0xc053,0xc054,0x43b8},
{0xc055,0xc055,0x1fae},
{0xc056,0xc05a,0x43ba},
{0xc05b,0xc05b,0x221a},
{0xc05c,0xc05d,0x43bf},
{0xc05e,0xc05e,0x1f7a},
{0xc05f,0xc05f,0x244d},
{0xc060,0xc060,0x2457},
{0xc061,0xc068,0x43c1},
{0xc069,0xc069,0x2453},
{0xc06a,0xc06a,0x43c9},
{0xc06b,0xc06b,0x2420},
{0xc06c,0xc06c,0x43ca},
{0xc06d,0xc06d,0x21df},
{0xc06e,0xc06e,0x2685},
{0xc06f,0xc06f,0x43cb},
{0xc070,0xc070,0x1e6e},
{0xc071,0xc073,0x43cc},
{0xc074,0xc074,0x2224},
{0xc075,0xc075,0x2670},
{0xc076,0xc076,0x43cf},
{0xc077,0xc077,0x21ba},
{0xc078,0xc078,0x43d0},
{0xc079,0xc079,0x245a},
{0xc07a,0xc07b,0x43d1},
{0xc07c,0xc07c,0x1fff},
{0xc07d,0xc07e,0x43d3},
{0xc080,0xc09a,0x43d5},
{0xc09b,0xc09b,0x25e2},
{0xc09c,0xc09c,0x43f0},
{0xc09d,0xc09d,0x269a},
{0xc09e,0xc0a0,0x43f1},
{0xc0a1,0xc0fe,0x98c},
{0xc140,0xc14f,0x43f4},
{0xc150,0xc150,0x1eea},
{0xc151,0xc153,0x4404},
{0xc154,0xc154,0x1e2c},
{0xc155,0xc15e,0x4407},
{0xc15f,0xc15f,0x2062},
{0xc160,0xc160,0x24f1},
{0xc161,0xc161,0x4411},
{0xc162,0xc162,0x24f2},
{0xc163,0xc174,0x4412},
{0xc175,0xc175,0x25f2},
{0xc176,0xc177,0x4424},
{0xc178,0xc178,0x2215},
{0xc179,0xc17e,0x4426},
{0xc180,0xc194,0x442c},
{0xc195,0xc195,0x21ae},
{0xc196,0xc1a0,0x4441},
{0xc1a1,0xc1fe,0x9ea},
{0xc240,0xc24d,0x444c},
{0xc24e,0xc24e,0x20e0},
{0xc24f,0xc264,0x445a},
{0xc265,0xc265,0x25c2},
{0xc266,0xc266,0x4470},
{0xc267,0xc267,0x25c1},
{0xc268,0xc27c,0x4471},
{0xc27d,0xc27d,0x2128},
{0xc27e,0xc27e,0x4486},
{0xc280,0xc283,0x4487},
{0xc284,0xc284,0x2199},
{0xc285,0xc292,0x448b},
{0xc293,0xc293,0x2017},
{0xc294,0xc294,0x1ea0},
{0xc295,0xc295,0x2125},
{0xc296,0xc296,0x214d},
{0xc297,0xc297,0x4499},
{0xc298,0xc298,0x25c4},
{0xc299,0xc299,0x209d},
{0xc29a,0xc29a,0x228a},
{0xc29b,0xc29b,0x449a},
{0xc29c,0xc29c,0x25c3},
{0xc29d,0xc29f,0x449b},
{0xc2a0,0xc2a0,0x2179},
{0xc2a1,0xc2fe,0xa48},
{0xc340,0xc340,0x2038},
{0xc341,0xc342,0x449e},
{0xc343,0xc343,0x2155},
{0xc344,0xc37a,0x44a0},
{0xc37b,0xc37b,0x21d2},
{0xc37c,0xc37e,0x44d7},
{0xc380,0xc383,0x44da},
{0xc384,0xc384,0x24c7},
{0xc385,0xc39a,0x44de},
{0xc39b,0xc39b,0x2279},
{0xc39c,0xc3a0,0x44f4},
{0xc3a1,0xc3fe,0xaa6},
{0xc440,0xc448,0x44f9},
{0xc449,0xc449,0x2123},
{0xc44a,0xc44b,0x4502},
{0xc44c,0xc44c,0x24c5},
{0xc44d,0xc453,0x4504},
{0xc454,0xc454,0x24c9},
{0xc455,0xc457,0x450b},
{0xc458,0xc458,0x2094},
{0xc459,0xc45a,0x450e},
{0xc45b,0xc45b,0x2296},
{0xc45c,0xc462,0x4510},
{0xc463,0xc463,0x1e77},
{0xc464,0xc476,0x4517},
{0xc477,0xc477,0x1f06},
{0xc478,0xc479,0x452a},
{0xc47a,0xc47a,0x1fa5},
{0xc47b,0xc47e,0x452c},
{0xc480,0xc480,0x4530},
{0xc481,0xc481,0x2099},
{0xc482,0xc490,0x4531},
{0xc491,0xc491,0x1ead},
{0xc492,0xc492,0x24c8},
{0xc493,0xc493,0x20a8},
{0xc494,0xc497,0x4540},
{0xc498,0xc498,0x201f},
{0xc499,0xc499,0x4544},
{0xc49a,0xc49a,0x20c5},
{0xc49b,0xc49b,0x4545},
{0xc49c,0xc49c,0x24ca},
{0xc49d,0xc4a0,0x4546},
{0xc4a1,0xc4fe,0xb04},
{0xc540,0xc543,0x454a},
{0xc544,0xc544,0x1ff0},
{0xc545,0xc545,0x454e},
{0xc546,0xc546,0x24c6},
{0xc547,0xc54a,0x454f},
{0xc54b,0xc54b,0x225d},
{0xc54c,0xc54c,0x22de},
{0xc54d,0xc551,0x4553},
{0xc552,0xc552,0x202c},
{0xc553,0xc55e,0x4558},
{0xc55f,0xc55f,0x2161},
{0xc560,0xc562,0x4564},
{0xc563,0xc563,0x223b},
{0xc564,0xc564,0x21d9},
{0xc565,0xc565,0x1fcb},
{0xc566,0xc566,0x1fc9},
{0xc567,0xc57e,0x4567},
{0xc580,0xc592,0x457f},
{0xc593,0xc593,0x1e61},
{0xc594,0xc59b,0x4592},
{0xc59c,0xc59c,0x25ef},
{0xc59d,0xc59d,0x459a},
{0xc59e,0xc59e,0x1f98},
{0xc59f,0xc5a0,0x459b},
{0xc5a1,0xc5fe,0xb62},
{0xc640,0xc640,0x459d},
{0xc641,0xc641,0x25f0},
{0xc642,0xc643,0x459e},
{0xc644,0xc644,0x1f88},
{0xc645,0xc646,0x45a0},
{0xc647,0xc647,0x21f8},
{0xc648,0xc662,0x45a2},
{0xc663,0xc663,0x2322},
{0xc664,0xc671,0x45bd},
{0xc672,0xc672,0x2336},
{0xc673,0xc67e,0x45cb},
{0xc680,0xc6a0,0x45d7},
{0xc6a1,0xc6fe,0xbc0},
{0xc740,0xc765,0x45f8},
{0xc766,0xc766,0x22ab},
{0xc767,0xc76e,0x461e},
{0xc76f,0xc76f,0x1fbf},
{0xc770,0xc775,0x4626},
{0xc776,0xc776,0x1f7d},
{0xc777,0xc77a,0x462c},
{0xc77b,0xc77b,0x2333},
{0xc77c,0xc77e,0x4630},
{0xc780,0xc7a0,0x4633},
{0xc7a1,0xc7fe,0xc1e},
{0xc840,0xc840,0x4654},
{0xc841,0xc841,0x1f4d},
{0xc842,0xc84e,0x4655},
{0xc84f,0xc84f,0x2334},
{0xc850,0xc851,0x4662},
{0xc852,0xc852,0x1ff1},
{0xc853,0xc865,0x4664},
{0xc866,0xc866,0x218b},
{0xc867,0xc86d,0x4677},
{0xc86e,0xc86e,0x2349},
{0xc86f,0xc87d,0x467e},
{0xc87e,0xc87e,0x220b},
{0xc880,0xc886,0x468d},
{0xc887,0xc887,0x2346},
{0xc888,0xc891,0x4694},
{0xc892,0xc892,0x2347},
{0xc893,0xc893,0x469e},
{0xc894,0xc894,0x2193},
{0xc895,0xc898,0x469f},
{0xc899,0xc899,0x26a1},
{0xc89a,0xc89c,0x46a3},
{0xc89d,0xc89d,0x1f62},
{0xc89e,0xc8a0,0x46a6},
{0xc8a1,0xc8fe,0xc7c},
{0xc940,0xc94e,0x46a9},
{0xc94f,0xc94f,0x234d},
{0xc950,0xc950,0x2348},
{0xc951,0xc96d,0x46b8},
{0xc96e,0xc96e,0x1e60},
{0xc96f,0xc96f,0x46d5},
{0xc970,0xc970,0x2345},
{0xc971,0xc976,0x46d6},
{0xc977,0xc977,0x1f12},
{0xc978,0xc97e,0x46dc},
{0xc980,0xc98e,0x46e3},
{0xc98f,0xc98f,0x2018},
{0xc990,0xc990,0x2335},
{0xc991,0xc99b,0x46f2},
{0xc99c,0xc99c,0x233c},
{0xc99d,0xc9a0,0x46fd},
{0xc9a1,0xc9fe,0xcda},
{0xca40,0xca4d,0x4701},
{0xca4e,0xca4e,0x266f},
{0xca4f,0xca55,0x470f},
{0xca56,0xca56,0x2351},
{0xca57,0xca58,0x4716},
{0xca59,0xca59,0x1fa0},
{0xca5a,0xca5b,0x4718},
{0xca5c,0xca5c,0x2338},
{0xca5d,0xca60,0x471a},
{0xca61,0xca61,0x221b},
{0xca62,0xca6d,0x471e},
{0xca6e,0xca6e,0x2342},
{0xca6f,0xca71,0x472a},
{0xca72,0xca72,0x234f},
{0xca73,0xca76,0x472d},
{0xca77,0xca77,0x233d},
{0xca78,0xca7a,0x4731},
{0xca7b,0xca7b,0x2344},
{0xca7c,0xca7c,0x2331},
{0xca7d,0xca7d,0x4734},
{0xca7e,0xca7e,0x234b},
{0xca80,0xca80,0x4735},
{0xca81,0xca81,0x233b},
{0xca82,0xca88,0x4736},
{0xca89,0xca89,0x2350},
{0xca8a,0xca8d,0x473d},
{0xca8e,0xca8e,0x1eb4},
{0xca8f,0xca8f,0x21a6},
{0xca90,0xca91,0x4741},
{0xca92,0xca92,0x21cb},
{0xca93,0xca99,0x4743},
{0xca9a,0xca9a,0x2355},
{0xca9b,0xcaa0,0x474a},
{0xcaa1,0xcafe,0xd38},
{0xcb40,0xcb42,0x4750},
{0xcb43,0xcb43,0x233e},
{0xcb44,0xcb44,0x4753},
{0xcb45,0xcb45,0x1f74},
{0xcb46,0xcb46,0x4754},
{0xcb47,0xcb47,0x2330},
{0xcb48,0xcb4a,0x4755},
{0xcb4b,0xcb4b,0x2680},
{0xcb4c,0xcb4d,0x4758},
{0xcb4e,0xcb4e,0x20da},
{0xcb4f,0xcb56,0x475a},
{0xcb57,0xcb57,0x234a},
{0xcb58,0xcb5c,0x4762},
{0xcb5d,0xcb5d,0x1f91},
{0xcb5e,0xcb5e,0x4767},
{0xcb5f,0xcb5f,0x2107},
{0xcb60,0xcb69,0x4768},
{0xcb6a,0xcb6a,0x233f},
{0xcb6b,0xcb7a,0x4772},
{0xcb7b,0xcb7b,0x1ff4},
{0xcb7c,0xcb7c,0x2343},
{0xcb7d,0xcb7e,0x4782},
{0xcb80,0xcb86,0x4784},
{0xcb87,0xcb87,0x2212},
{0xcb88,0xcb8d,0x478b},
{0xcb8e,0xcb8e,0x2207},
{0xcb8f,0xcb91,0x4791},
{0xcb92,0xcb92,0x2359},
{0xcb93,0xcb9b,0x4794},
{0xcb9c,0xcb9c,0x2254},
{0xcb9d,0xcb9d,0x479d},
{0xcb9e,0xcb9e,0x2332},
{0xcb9f,0xcba0,0x479e},
{0xcba1,0xcbfe,0xd96},
{0xcc40,0xcc40,0x1e26},
{0xcc41,0xcc41,0x2357},
{0xcc42,0xcc48,0x47a0},
{0xcc49,0xcc49,0x2358},
{0xcc4a,0xcc4a,0x2042},
{0xcc4b,0xcc4b,0x2153},
{0xcc4c,0xcc4e,0x47a7},
{0xcc4f,0xcc4f,0x20bb},
{0xcc50,0xcc5b,0x47aa},
{0xcc5c,0xcc5c,0x235a},
{0xcc5d,0xcc5f,0x47b6},
{0xcc60,0xcc60,0x2356},
{0xcc61,0xcc63,0x47b9},
{0xcc64,0xcc64,0x2337},
{0xcc65,0xcc6c,0x47bc},
{0xcc6d,0xcc6d,0x1ff9},
{0xcc6e,0xcc78,0x47c4},
{0xcc79,0xcc79,0x2353},
{0xcc7a,0xcc7c,0x47cf},
{0xcc7d,0xcc7d,0x2061},
{0xcc7e,0xcc7e,0x47d2},
{0xcc80,0xcc8d,0x47d3},
{0xcc8e,0xcc8e,0x1e95},
{0xcc8f,0xcc93,0x47e1},
{0xcc94,0xcc94,0x2049},
{0xcc95,0xcc95,0x47e6},
{0xcc96,0xcc96,0x1f42},
{0xcc97,0xcc9c,0x47e7},
{0xcc9d,0xcc9d,0x1fe8},
{0xcc9e,0xcca0,0x47ed},
{0xcca1,0xccfe,0xdf4},
{0xcd40,0xcd7e,0x47f0},
{0xcd80,0xcd8f,0x482f},
{0xcd90,0xcd90,0x25da},
{0xcd91,0xcd97,0x483f},
{0xcd98,0xcd98,0x25d7},
{0xcd99,0xcda0,0x4846},
{0xcda1,0xcdfe,0xe52},
{0xce40,0xce66,0x484e},
{0xce67,0xce67,0x212e},
{0xce68,0xce71,0x4875},
{0xce72,0xce72,0x21b2},
{0xce73,0xce7e,0x487f},
{0xce80,0xce80,0x488b},
{0xce81,0xce81,0x219e},
{0xce82,0xce86,0x488c},
{0xce87,0xce87,0x25dc},
{0xce88,0xce9a,0x4891},
{0xce9b,0xce9b,0x206c},
{0xce9c,0xce9d,0x48a4},
{0xce9e,0xce9e,0x2226},
{0xce9f,0xcea0,0x48a6},
{0xcea1,0xcefe,0xeb0},
{0xcf40,0xcf4d,0x48a8},
{0xcf4e,0xcf4e,0x25e0},
{0xcf4f,0xcf54,0x48b6},
{0xcf55,0xcf55,0x227b},
{0xcf56,0xcf57,0x48bc},
{0xcf58,0xcf58,0x25de},
{0xcf59,0xcf5b,0x48be},
{0xcf5c,0xcf5c,0x25e1},
{0xcf5d,0xcf6b,0x48c1},
{0xcf6c,0xcf6c,0x25d5},
{0xcf6d,0xcf72,0x48d0},
{0xcf73,0xcf73,0x1e6b},
{0xcf74,0xcf74,0x48d6},
{0xcf75,0xcf75,0x25db},
{0xcf76,0xcf77,0x48d7},
{0xcf78,0xcf78,0x1e89},
{0xcf79,0xcf7b,0x48d9},
{0xcf7c,0xcf7c,0x25d9},
{0xcf7d,0xcf7e,0x48dc},
{0xcf80,0xcf80,0x48de},
{0xcf81,0xcf81,0x2211},
{0xcf82,0xcf88,0x48df},
{0xcf89,0xcf89,0x2229},
{0xcf8a,0xcf8a,0x25d6},
{0xcf8b,0xcf92,0x48e6},
{0xcf93,0xcf93,0x25dd},
{0xcf94,0xcf94,0x25df},
{0xcf95,0xcf9d,0x48ee},
{0xcf9e,0xcf9e,0x1fef},
{0xcf9f,0xcf9f,0x48f7},
{0xcfa0,0xcfa0,0x25d8},
{0xcfa1,0xcffe,0xf0e},
{0xd040,0xd04c,0x48f8},
{0xd04d,0xd04d,0x1f29},
{0xd04e,0xd050,0x4905},
{0xd051,0xd051,0x1e5b},
{0xd052,0xd054,0x4908},
{0xd055,0xd055,0x2076},
{0xd056,0xd05b,0x490b},
{0xd05c,0xd05c,0x2297},
{0xd05d,0xd05f,0x4911},
{0xd060,0xd060,0x2690},
{0xd061,0xd066,0x4914},
{0xd067,0xd067,0x213f},
{0xd068,0xd06b,0x491a},
{0xd06c,0xd06c,0x2198},
{0xd06d,0xd06d,0x491e},
{0xd06e,0xd06e,0x1e88},
{0xd06f,0xd07c,0x491f},
{0xd07d,0xd07d,0x228c},
{0xd07e,0xd07e,0x492d},
{0xd080,0xd0a0,0x492e},
{0xd0a1,0xd0fe,0xf6c},
{0xd140,0xd154,0x494f},
{0xd155,0xd155,0x25f1},
{0xd156,0xd158,0x4964},
{0xd159,0xd159,0x200c},
{0xd15a,0xd160,0x4967},
{0xd161,0xd161,0x1e58},
{0xd162,0xd162,0x22ac},
{0xd163,0xd174,0x496e},
{0xd175,0xd175,0x26a7},
{0xd176,0xd17c,0x4980},
{0xd17d,0xd17d,0x2676},
{0xd17e,0xd17e,0x4987},
{0xd180,0xd19c,0x4988},
{0xd19d,0xd19d,0x1fe1},
{0xd19e,0xd19e,0x25bb},
{0xd19f,0xd1a0,0x49a5},
{0xd1a1,0xd1fe,0xfca},
{0xd240,0xd240,0x25be},
{0xd241,0xd242,0x49a7},
{0xd243,0xd243,0x22dd},
{0xd244,0xd24c,0x49a9},
{0xd24d,0xd24d,0x25bd},
{0xd24e,0xd25b,0x49b2},
{0xd25c,0xd25c,0x1e2a},
{0xd25d,0xd262,0x49c0},
{0xd263,0xd263,0x25bc},
{0xd264,0xd264,0x25ba},
{0xd265,0xd267,0x49c6},
{0xd268,0xd268,0x25bf},
{0xd269,0xd26c,0x49c9},
{0xd26d,0xd26d,0x2187},
{0xd26e,0xd26e,0x49cd},
{0xd26f,0xd26f,0x266b},
{0xd270,0xd271,0x49ce},
{0xd272,0xd272,0x1e7f},
{0xd273,0xd274,0x49d0},
{0xd275,0xd275,0x21ad},
{0xd276,0xd27e,0x49d2},
{0xd280,0xd289,0x49db},
{0xd28a,0xd28a,0x1f96},
{0xd28b,0xd28d,0x49e5},
{0xd28e,0xd28e,0x1f32},
{0xd28f,0xd291,0x49e8},
{0xd292,0xd292,0x2084},
{0xd293,0xd294,0x49eb},
{0xd295,0xd295,0x2136},
{0xd296,0xd296,0x49ed},
{0xd297,0xd297,0x24b8},
{0xd298,0xd29f,0x49ee},
{0xd2a0,0xd2a0,0x24ba},
{0xd2a1,0xd2fe,0x1028},
{0xd340,0xd343,0x49f6},
{0xd344,0xd344,0x24bc},
{0xd345,0xd347,0x49fa},
{0xd348,0xd348,0x20e4},
{0xd349,0xd349,0x49fd},
{0xd34a,0xd34a,0x24b9},
{0xd34b,0xd34c,0x49fe},
{0xd34d,0xd34d,0x24bd},
{0xd34e,0xd34f,0x4a00},
{0xd350,0xd350,0x24be},
{0xd351,0xd354,0x4a02},
{0xd355,0xd355,0x24bf},
{0xd356,0xd357,0x4a06},
{0xd358,0xd358,0x1fd2},
{0xd359,0xd35a,0x4a08},
{0xd35b,0xd35b,0x1ffd},
{0xd35c,0xd35c,0x4a0a},
{0xd35d,0xd35d,0x24bb},
{0xd35e,0xd35e,0x1f2d},
{0xd35f,0xd377,0x4a0b},
{0xd378,0xd378,0x2609},
{0xd379,0xd379,0x4a24},
{0xd37a,0xd37a,0x260a},
{0xd37b,0xd37b,0x4a25},
{0xd37c,0xd37c,0x1e94},
{0xd37d,0xd37e,0x4a26},
{0xd380,0xd384,0x4a28},
{0xd385,0xd385,0x22df},
{0xd386,0xd386,0x1ecb},
{0xd387,0xd387,0x1f0d},
{0xd388,0xd38a,0x4a2d},
{0xd38b,0xd38b,0x1f77},
{0xd38c,0xd38c,0x4a30},
{0xd38d,0xd38d,0x21eb},
{0xd38e,0xd38e,0x4a31},
{0xd38f,0xd38f,0x22e1},
{0xd390,0xd390,0x4a32},
{0xd391,0xd391,0x216f},
{0xd392,0xd392,0x4a33},
{0xd393,0xd393,0x22e0},
{0xd394,0xd395,0x4a34},
{0xd396,0xd396,0x21ea},
{0xd397,0xd397,0x4a36},
{0xd398,0xd398,0x22e2},
{0xd399,0xd399,0x20cb},
{0xd39a,0xd39a,0x4a37},
{0xd39b,0xd39b,0x1f78},
{0xd39c,0xd39d,0x4a38},
{0xd39e,0xd39e,0x1ee2},
{0xd39f,0xd39f,0x4a3a},
{0xd3a0,0xd3a0,0x21f2},
{0xd3a1,0xd3fe,0x1086},
{0xd440,0xd440,0x4a3b},
{0xd441,0xd441,0x2150},
{0xd442,0xd444,0x4a3c},
{0xd445,0xd445,0x1fd3},
{0xd446,0xd446,0x4a3f},
{0xd447,0xd447,0x22e5},
{0xd448,0xd44b,0x4a40},
{0xd44c,0xd44c,0x1ef2},
{0xd44d,0xd44e,0x4a44},
{0xd44f,0xd44f,0x211f},
{0xd450,0xd452,0x4a46},
{0xd453,0xd453,0x21dd},
{0xd454,0xd455,0x4a49},
{0xd456,0xd456,0x2154},
{0xd457,0xd457,0x4a4b},
{0xd458,0xd458,0x22e7},
{0xd459,0xd45b,0x4a4c},
{0xd45c,0xd45c,0x2282},
{0xd45d,0xd461,0x4a4f},
{0xd462,0xd462,0x22e6},
{0xd463,0xd466,0x4a54},
{0xd467,0xd467,0x22e8},
{0xd468,0xd46d,0x4a58},
{0xd46e,0xd46e,0x22e4},
{0xd46f,0xd46f,0x4a5e},
{0xd470,0xd470,0x226a},
{0xd471,0xd471,0x4a5f},
{0xd472,0xd472,0x22eb},
{0xd473,0xd473,0x4a60},
{0xd474,0xd474,0x22e9},
{0xd475,0xd475,0x20bd},
{0xd476,0xd477,0x4a61},
{0xd478,0xd478,0x22ea},
{0xd479,0xd47a,0x4a63},
{0xd47b,0xd47b,0x22bd},
{0xd47c,0xd47d,0x4a65},
{0xd47e,0xd47e,0x1e9e},
{0xd480,0xd481,0x4a67},
{0xd482,0xd482,0x22f6},
{0xd483,0xd483,0x21e7},
{0xd484,0xd484,0x2216},
{0xd485,0xd486,0x4a69},
{0xd487,0xd487,0x2137},
{0xd488,0xd489,0x4a6b},
{0xd48a,0xd48a,0x212c},
{0xd48b,0xd48b,0x4a6d},
{0xd48c,0xd48c,0x1e68},
{0xd48d,0xd48d,0x22f2},
{0xd48e,0xd48e,0x1f37},
{0xd48f,0xd48f,0x22f3},
{0xd490,0xd490,0x4a6e},
{0xd491,0xd491,0x22ef},
{0xd492,0xd492,0x1f50},
{0xd493,0xd493,0x1f10},
{0xd494,0xd494,0x21c8},
{0xd495,0xd495,0x4a6f},
{0xd496,0xd496,0x22f1},
{0xd497,0xd49b,0x4a70},
{0xd49c,0xd49c,0x22f0},
{0xd49d,0xd49e,0x4a75},
{0xd49f,0xd49f,0x22ee},
{0xd4a0,0xd4a0,0x4a77},
{0xd4a1,0xd4fe,0x10e4},
{0xd540,0xd542,0x4a78},
{0xd543,0xd543,0x22ed},
{0xd544,0xd544,0x229e},
{0xd545,0xd545,0x22ec},
{0xd546,0xd546,0x1fe2},
{0xd547,0xd549,0x4a7b},
{0xd54a,0xd54a,0x20fe},
{0xd54b,0xd54d,0x4a7e},
{0xd54e,0xd54f,0x22f9},
{0xd550,0xd550,0x4a81},
{0xd551,0xd551,0x1eaf},
{0xd552,0xd553,0x4a82},
{0xd554,0xd554,0x2236},
{0xd555,0xd555,0x4a84},
{0xd556,0xd556,0x22f7},
{0xd557,0xd559,0x4a85},
{0xd55a,0xd55a,0x223d},
{0xd55b,0xd55b,0x4a88},
{0xd55c,0xd55c,0x1e82},
{0xd55d,0xd55d,0x1fb6},
{0xd55e,0xd55e,0x4a89},
{0xd55f,0xd55f,0x21a4},
{0xd560,0xd560,0x21aa},
{0xd561,0xd561,0x22f8},
{0xd562,0xd562,0x2151},
{0xd563,0xd563,0x4a8a},
{0xd564,0xd564,0x1f60},
{0xd565,0xd567,0x4a8b},
{0xd568,0xd568,0x2147},
{0xd569,0xd56b,0x4a8e},
{0xd56c,0xd56c,0x2145},
{0xd56d,0xd56d,0x4a91},
{0xd56e,0xd56e,0x1fdc},
{0xd56f,0xd571,0x4a92},
{0xd572,0xd572,0x2301},
{0xd573,0xd574,0x4a95},
{0xd575,0xd575,0x1ef5},
{0xd576,0xd577,0x4a97},
{0xd578,0xd578,0x2218},
{0xd579,0xd57a,0x4a99},
{0xd57b,0xd57b,0x1ec6},
{0xd57c,0xd57d,0x4a9b},
{0xd57e,0xd57e,0x2300},
{0xd580,0xd580,0x4a9d},
{0xd581,0xd581,0x22b4},
{0xd582,0xd583,0x4a9e},
{0xd584,0xd584,0x2169},
{0xd585,0xd585,0x4aa0},
{0xd586,0xd586,0x22fd},
{0xd587,0xd587,0x4aa1},
{0xd588,0xd588,0x20ea},
{0xd589,0xd589,0x4aa2},
{0xd58a,0xd58a,0x22f4},
{0xd58b,0xd58b,0x4aa3},
{0xd58c,0xd58c,0x22fb},
{0xd58d,0xd58d,0x4aa4},
{0xd58e,0xd58e,0x22fc},
{0xd58f,0xd58f,0x2027},
{0xd590,0xd592,0x4aa5},
{0xd593,0xd593,0x2060},
{0xd594,0xd594,0x22ff},
{0xd595,0xd597,0x4aa8},
{0xd598,0xd598,0x22fe},
{0xd599,0xd599,0x1ec7},
{0xd59a,0xd59a,0x4aab},
{0xd59b,0xd59b,0x230c},
{0xd59c,0xd59e,0x4aac},
{0xd59f,0xd59f,0x22f5},
{0xd5a0,0xd5a0,0x4aaf},
{0xd5a1,0xd5fe,0x1142},
{0xd640,0xd640,0x2306},
{0xd641,0xd641,0x4ab0},
{0xd642,0xd642,0x230a},
{0xd643,0xd643,0x21d3},
{0xd644,0xd646,0x4ab1},
{0xd647,0xd647,0x2303},
{0xd648,0xd648,0x4ab4},
{0xd649,0xd649,0x2307},
{0xd64a,0xd64a,0x230b},
{0xd64b,0xd64c,0x4ab5},
{0xd64d,0xd64d,0x1f5f},
{0xd64e,0xd64e,0x4ab7},
{0xd64f,0xd64f,0x2309},
{0xd650,0xd651,0x4ab8},
{0xd652,0xd652,0x2302},
{0xd653,0xd653,0x1f04},
{0xd654,0xd654,0x229d},
{0xd655,0xd655,0x4aba},
{0xd656,0xd656,0x21fb},
{0xd657,0xd657,0x4abb},
{0xd658,0xd658,0x2308},
{0xd659,0xd659,0x4abc},
{0xd65a,0xd65a,0x20ac},
{0xd65b,0xd65b,0x4abd},
{0xd65c,0xd65c,0x208e},
{0xd65d,0xd65d,0x2305},
{0xd65e,0xd65e,0x2197},
{0xd65f,0xd65f,0x4abe},
{0xd660,0xd660,0x2171},
{0xd661,0xd661,0x2298},
{0xd662,0xd664,0x4abf},
{0xd665,0xd665,0x1f57},
{0xd666,0xd668,0x4ac2},
{0xd669,0xd669,0x2082},
{0xd66a,0xd66a,0x4ac5},
{0xd66b,0xd66b,0x2311},
{0xd66c,0xd66e,0x4ac6},
{0xd66f,0xd66f,0x2304},
{0xd670,0xd670,0x4ac9},
{0xd671,0xd671,0x230f},
{0xd672,0xd672,0x1e35},
{0xd673,0xd673,0x4aca},
{0xd674,0xd674,0x20d1},
{0xd675,0xd675,0x2310},
{0xd676,0xd676,0x1fa3},
{0xd677,0xd677,0x4acb},
{0xd678,0xd678,0x21d6},
{0xd679,0xd67b,0x4acc},
{0xd67c,0xd67c,0x2206},
{0xd67d,0xd67e,0x4acf},
{0xd680,0xd682,0x4ad1},
{0xd683,0xd683,0x230d},
{0xd684,0xd685,0x4ad4},
{0xd686,0xd686,0x2312},
{0xd687,0xd687,0x208d},
{0xd688,0xd688,0x2313},
{0xd689,0xd68d,0x4ad6},
{0xd68e,0xd68e,0x22e3},
{0xd68f,0xd693,0x4adb},
{0xd694,0xd694,0x1fba},
{0xd695,0xd698,0x4ae0},
{0xd699,0xd699,0x2078},
{0xd69a,0xd6a0,0x4ae4},
{0xd6a1,0xd6fe,0x11a0},
{0xd740,0xd742,0x4aeb},
{0xd743,0xd743,0x2288},
{0xd744,0xd747,0x4aee},
{0xd748,0xd748,0x2316},
{0xd749,0xd749,0x1f6b},
{0xd74a,0xd74f,0x4af2},
{0xd750,0xd750,0x2314},
{0xd751,0xd751,0x4af8},
{0xd752,0xd752,0x2130},
{0xd753,0xd753,0x2315},
{0xd754,0xd754,0x2168},
{0xd755,0xd755,0x4af9},
{0xd756,0xd756,0x20c4},
{0xd757,0xd763,0x4afa},
{0xd764,0xd764,0x2318},
{0xd765,0xd766,0x4b07},
{0xd767,0xd767,0x2219},
{0xd768,0xd768,0x2217},
{0xd769,0xd76b,0x4b09},
{0xd76c,0xd76c,0x20d5},
{0xd76d,0xd76e,0x4b0c},
{0xd76f,0xd76f,0x1f4a},
{0xd770,0xd774,0x4b0e},
{0xd775,0xd775,0x2240},
{0xd776,0xd777,0x4b13},
{0xd778,0xd778,0x1ed3},
{0xd779,0xd77e,0x4b15},
{0xd780,0xd782,0x4b1b},
{0xd783,0xd783,0x1e49},
{0xd784,0xd786,0x4b1e},
{0xd787,0xd787,0x261b},
{0xd788,0xd78a,0x4b21},
{0xd78b,0xd78b,0x1e6d},
{0xd78c,0xd78c,0x20f8},
{0xd78d,0xd78d,0x4b24},
{0xd78e,0xd78e,0x1ffb},
{0xd78f,0xd78f,0x2319},
{0xd790,0xd794,0x4b25},
{0xd795,0xd795,0x230e},
{0xd796,0xd796,0x4b2a},
{0xd797,0xd797,0x2317},
{0xd798,0xd7a0,0x4b2b},
{0xd7a1,0xd7f9,0x11fe},
{0xd840,0xd84c,0x4b34},
{0xd84d,0xd84d,0x20c8},
{0xd84e,0xd852,0x4b41},
{0xd853,0xd853,0x1efd},
{0xd854,0xd87e,0x4b46},
{0xd880,0xd88f,0x4b71},
{0xd890,0xd890,0x1e3b},
{0xd891,0xd891,0x227f},
{0xd892,0xd892,0x4b81},
{0xd893,0xd893,0x1f0c},
{0xd894,0xd894,0x1e59},
{0xd895,0xd895,0x1f24},
{0xd896,0xd899,0x4b82},
{0xd89a,0xd89a,0x20ba},
{0xd89b,0xd89b,0x1f65},
{0xd89c,0xd89c,0x1ef0},
{0xd89d,0xd89d,0x2164},
{0xd89e,0xd89e,0x1f30},
{0xd89f,0xd89f,0x2261},
{0xd8a0,0xd8a0,0x4b86},
{0xd8a1,0xd8fe,0x1257},
{0xd940,0xd940,0x4b87},
{0xd941,0xd941,0x22a2},
{0xd942,0xd942,0x24ab},
{0xd943,0xd943,0x4b88},
{0xd944,0xd944,0x24af},
{0xd945,0xd945,0x1ee8},
{0xd946,0xd946,0x1f39},
{0xd947,0xd947,0x4b89},
{0xd948,0xd948,0x1e48},
{0xd949,0xd949,0x2070},
{0xd94a,0xd94a,0x1ea8},
{0xd94b,0xd94b,0x4b8a},
{0xd94c,0xd94c,0x24ac},
{0xd94d,0xd94d,0x1ef7},
{0xd94e,0xd94e,0x2176},
{0xd94f,0xd94f,0x24ad},
{0xd950,0xd950,0x4b8b},
{0xd951,0xd951,0x207b},
{0xd952,0xd952,0x1f45},
{0xd953,0xd953,0x24aa},
{0xd954,0xd954,0x204b},
{0xd955,0xd955,0x202f},
{0xd956,0xd956,0x1f5a},
{0xd957,0xd957,0x24b0},
{0xd958,0xd958,0x4b8c},
{0xd959,0xd959,0x22b7},
{0xd95a,0xd95a,0x1f7f},
{0xd95b,0xd95b,0x4b8d},
{0xd95c,0xd95c,0x2265},
{0xd95d,0xd962,0x4b8e},
{0xd963,0xd963,0x24b2},
{0xd964,0xd964,0x211b},
{0xd965,0xd965,0x1e51},
{0xd966,0xd966,0x4b94},
{0xd967,0xd967,0x24b4},
{0xd968,0xd96b,0x4b95},
{0xd96c,0xd96c,0x24b3},
{0xd96d,0xd96d,0x4b99},
{0xd96e,0xd96e,0x1e9f},
{0xd96f,0xd96f,0x4b9a},
{0xd970,0xd970,0x2118},
{0xd971,0xd971,0x4b9b},
{0xd972,0xd972,0x20b4},
{0xd973,0xd973,0x23af},
{0xd974,0xd974,0x21bb},
{0xd975,0xd975,0x2072},
{0xd976,0xd976,0x1f95},
{0xd977,0xd977,0x4b9c},
{0xd978,0xd978,0x1f0a},
{0xd979,0xd979,0x24b6},
{0xd97a,0xd97b,0x4b9d},
{0xd97c,0xd97c,0x2291},
{0xd97d,0xd97d,0x24b5},
{0xd97e,0xd97e,0x2278},
{0xd980,0xd980,0x1ed4},
{0xd981,0xd986,0x4b9f},
{0xd987,0xd987,0x1ff3},
{0xd988,0xd98c,0x4ba5},
{0xd98d,0xd98d,0x22a9},
{0xd98e,0xd98e,0x24b7},
{0xd98f,0xd98f,0x1f28},
{0xd990,0xd990,0x2109},
{0xd991,0xd991,0x22c7},
{0xd992,0xd996,0x4baa},
{0xd997,0xd997,0x24ae},
{0xd998,0xd998,0x22b1},
{0xd999,0xd99a,0x4baf},
{0xd99b,0xd99b,0x2266},
{0xd99c,0xd99c,0x4bb1},
{0xd99d,0xd99d,0x225b},
{0xd99e,0xd99e,0x22c4},
{0xd99f,0xd99f,0x4bb2},
{0xd9a0,0xd9a0,0x2115},
{0xd9a1,0xd9fe,0x12b5},
{0xda40,0xda40,0x4bb3},
{0xda41,0xda41,0x222a},
{0xda42,0xda42,0x24b1},
{0xda43,0xda47,0x4bb4},
{0xda48,0xda48,0x213d},
{0xda49,0xda4c,0x4bb9},
{0xda4d,0xda4d,0x1f15},
{0xda4e,0xda4e,0x225c},
{0xda4f,0xda72,0x4bbd},
{0xda73,0xda73,0x1f14},
{0xda74,0xda76,0x4be1},
{0xda77,0xda77,0x227a},
{0xda78,0xda7e,0x4be4},
{0xda80,0xda84,0x4beb},
{0xda85,0xda85,0x20ee},
{0xda86,0xda8d,0x4bf0},
{0xda8e,0xda8e,0x25f8},
{0xda8f,0xdaa0,0x4bf8},
{0xdaa1,0xdafe,0x1313},
{0xdb40,0xdb5f,0x4c0a},
{0xdb60,0xdb60,0x1f94},
{0xdb61,0xdb77,0x4c2a},
{0xdb78,0xdb78,0x2230},
{0xdb79,0xdb7e,0x4c41},
{0xdb80,0xdb83,0x4c47},
{0xdb84,0xdb84,0x25fd},
{0xdb85,0xdb8a,0x4c4b},
{0xdb8b,0xdb8b,0x2600},
{0xdb8c,0xdb97,0x4c51},
{0xdb98,0xdb98,0x2606},
{0xdb99,0xdba0,0x4c5d},
{0xdba1,0xdbfe,0x1371},
{0xdc40,0xdc44,0x4c65},
{0xdc45,0xdc45,0x25ff},
{0xdc46,0xdc4e,0x4c6a},
{0xdc4f,0xdc4f,0x25fc},
{0xdc50,0xdc50,0x1e8c},
{0xdc51,0xdc51,0x2602},
{0xdc52,0xdc52,0x4c73},
{0xdc53,0xdc53,0x224d},
{0xdc54,0xdc54,0x4c74},
{0xdc55,0xdc55,0x2604},
{0xdc56,0xdc56,0x25fe},
{0xdc57,0xdc57,0x2603},
{0xdc58,0xdc5c,0x4c75},
{0xdc5d,0xdc5d,0x2601},
{0xdc5e,0xdc61,0x4c7a},
{0xdc62,0xdc62,0x2605},
{0xdc63,0xdc65,0x4c7e},
{0xdc66,0xdc66,0x1ea3},
{0xdc67,0xdc67,0x2608},
{0xdc68,0xdc6a,0x4c81},
{0xdc6b,0xdc6b,0x2607},
{0xdc6c,0xdc7b,0x4c84},
{0xdc7c,0xdc7c,0x20f0},
{0xdc7d,0xdc7e,0x4c94},
{0xdc80,0xdc86,0x4c96},
{0xdc87,0xdc87,0x1e7b},
{0xdc88,0xdc88,0x2267},
{0xdc89,0xdc89,0x1f36},
{0xdc8a,0xdc8a,0x1fd6},
{0xdc8b,0xdc8d,0x4c9d},
{0xdc8e,0xdc8e,0x21e0},
{0xdc8f,0xdc8f,0x4ca0},
{0xdc90,0xdc90,0x248c},
{0xdc91,0xdc96,0x4ca1},
{0xdc97,0xdc97,0x248d},
{0xdc98,0xdc9a,0x4ca7},
{0xdc9b,0xdc9b,0x2102},
{0xdc9c,0xdc9f,0x4caa},
{0xdca0,0xdca0,0x2494},
{0xdca1,0xdcfe,0x13cf},
{0xdd40,0xdd45,0x4cae},
{0xdd46,0xdd46,0x2493},
{0xdd47,0xdd4c,0x4cb4},
{0xdd4d,0xdd4d,0x248e},
{0xdd4e,0xdd52,0x4cba},
{0xdd53,0xdd53,0x2299},
{0xdd54,0xdd54,0x2491},
{0xdd55,0xdd55,0x2496},
{0xdd56,0xdd56,0x248f},
{0xdd57,0xdd57,0x2492},
{0xdd58,0xdd58,0x4cbf},
{0xdd59,0xdd59,0x2497},
{0xdd5a,0xdd5d,0x4cc0},
{0xdd5e,0xdd5e,0x1fb1},
{0xdd5f,0xdd5f,0x4cc4},
{0xdd60,0xdd60,0x249a},
{0xdd61,0xdd61,0x4cc5},
{0xdd62,0xdd62,0x2499},
{0xdd63,0xdd63,0x4cc6},
{0xdd64,0xdd64,0x2258},
{0xdd65,0xdd65,0x2498},
{0xdd66,0xdd6c,0x4cc7},
{0xdd6d,0xdd6d,0x249b},
{0xdd6e,0xdd6e,0x4cce},
{0xdd6f,0xdd6f,0x1f09},
{0xdd70,0xdd70,0x20e6},
{0xdd71,0xdd75,0x4ccf},
{0xdd76,0xdd76,0x2026},
{0xdd77,0xdd77,0x249f},
{0xdd78,0xdd78,0x1f59},
{0xdd79,0xdd7a,0x249d},
{0xdd7b,0xdd7e,0x4cd4},
{0xdd80,0xdd80,0x4cd8},
{0xdd81,0xdd81,0x1f3b},
{0xdd82,0xdd82,0x249c},
{0xdd83,0xdd84,0x4cd9},
{0xdd85,0xdd85,0x1e3a},
{0xdd86,0xdd86,0x205b},
{0xdd87,0xdd8a,0x4cdb},
{0xdd8b,0xdd8b,0x1f70},
{0xdd8c,0xdd8e,0x4cdf},
{0xdd8f,0xdd8f,0x24a0},
{0xdd90,0xdd93,0x4ce2},
{0xdd94,0xdd94,0x213b},
{0xdd95,0xdd96,0x4ce6},
{0xdd97,0xdd97,0x1f07},
{0xdd98,0xdd99,0x4ce8},
{0xdd9a,0xdd9a,0x2270},
{0xdd9b,0xdd9b,0x2237},
{0xdd9c,0xdd9d,0x4cea},
{0xdd9e,0xdd9e,0x24d2},
{0xdd9f,0xdd9f,0x4cec},
{0xdda0,0xdda0,0x21b3},
{0xdda1,0xddfe,0x142d},
{0xde40,0xde40,0x2245},
{0xde41,0xde41,0x24a1},
{0xde42,0xde43,0x4ced},
{0xde44,0xde44,0x22a8},
{0xde45,0xde47,0x4cef},
{0xde48,0xde48,0x227c},
{0xde49,0xde49,0x1fb0},
{0xde4a,0xde4e,0x4cf2},
{0xde4f,0xde4f,0x24a2},
{0xde50,0xde59,0x4cf7},
{0xde5a,0xde5a,0x1f46},
{0xde5b,0xde5b,0x4d01},
{0xde5c,0xde5c,0x2380},
{0xde5d,0xde5d,0x2495},
{0xde5e,0xde5e,0x4d02},
{0xde5f,0xde5f,0x2490},
{0xde60,0xde6a,0x4d03},
{0xde6b,0xde6b,0x1e30},
{0xde6c,0xde6e,0x4d0e},
{0xde6f,0xde6f,0x1e9d},
{0xde70,0xde70,0x1e4b},
{0xde71,0xde71,0x1e4a},
{0xde72,0xde72,0x20aa},
{0xde73,0xde7e,0x4d11},
{0xde80,0xde91,0x4d1d},
{0xde92,0xde92,0x267b},
{0xde93,0xde9e,0x4d2f},
{0xde9f,0xde9f,0x23f2},
{0xdea0,0xdea0,0x4d3b},
{0xdea1,0xdefe,0x148b},
{0xdf40,0xdf40,0x227e},
{0xdf41,0xdf41,0x4d3c},
{0xdf42,0xdf42,0x2019},
{0xdf43,0xdf4c,0x4d3d},
{0xdf4d,0xdf4d,0x1fbb},
{0xdf4e,0xdf5b,0x4d47},
{0xdf5c,0xdf5c,0x2253},
{0xdf5d,0xdf5d,0x4d55},
{0xdf5e,0xdf5e,0x1f3e},
{0xdf5f,0xdf5f,0x1ea6},
{0xdf60,0xdf60,0x218e},
{0xdf61,0xdf63,0x4d56},
{0xdf64,0xdf64,0x21ec},
{0xdf65,0xdf65,0x4d59},
{0xdf66,0xdf66,0x1ebe},
{0xdf67,0xdf67,0x4d5a},
{0xdf68,0xdf68,0x224a},
{0xdf69,0xdf6c,0x4d5b},
{0xdf6d,0xdf6d,0x2133},
{0xdf6e,0xdf73,0x4d5f},
{0xdf74,0xdf74,0x1e84},
{0xdf75,0xdf76,0x4d65},
{0xdf77,0xdf77,0x20cf},
{0xdf78,0xdf78,0x21e2},
{0xdf79,0xdf79,0x4d67},
{0xdf7a,0xdf7a,0x220f},
{0xdf7b,0xdf7b,0x4d68},
{0xdf7c,0xdf7c,0x2029},
{0xdf7d,0xdf7d,0x4d69},
{0xdf7e,0xdf7e,0x2073},
{0xdf80,0xdf80,0x1f55},
{0xdf81,0xdf82,0x4d6a},
{0xdf83,0xdf83,0x23f1},
{0xdf84,0xdf84,0x4d6c},
{0xdf85,0xdf85,0x1e46},
{0xdf86,0xdf88,0x4d6d},
{0xdf89,0xdf89,0x2063},
{0xdf8a,0xdf8a,0x23f3},
{0xdf8b,0xdfa0,0x4d70},
{0xdfa1,0xdffe,0x14e9},
{0xe040,0xe04f,0x4d86},
{0xe050,0xe050,0x231e},
{0xe051,0xe05c,0x4d96},
{0xe05d,0xe05d,0x2233},
{0xe05e,0xe068,0x4da2},
{0xe069,0xe069,0x2320},
{0xe06a,0xe06b,0x4dad},
{0xe06c,0xe06c,0x21c7},
{0xe06d,0xe074,0x4daf},
{0xe075,0xe075,0x22bc},
{0xe076,0xe076,0x4db7},
{0xe077,0xe077,0x231c},
{0xe078,0xe078,0x4db8},
{0xe079,0xe079,0x2251},
{0xe07a,0xe07e,0x4db9},
{0xe080,0xe086,0x4dbe},
{0xe087,0xe087,0x1ebb},
{0xe088,0xe08c,0x4dc5},
{0xe08d,0xe08d,0x2287},
{0xe08e,0xe08e,0x4dca},
{0xe08f,0xe08f,0x202d},
{0xe090,0xe090,0x1eab},
{0xe091,0xe091,0x4dcb},
{0xe092,0xe092,0x231d},
{0xe093,0xe093,0x4dcc},
{0xe094,0xe094,0x231f},
{0xe095,0xe096,0x4dcd},
{0xe097,0xe097,0x231b},
{0xe098,0xe0a0,0x4dcf},
{0xe0a1,0xe0fe,0x1547},
{0xe140,0xe141,0x4dd8},
{0xe142,0xe142,0x2321},
{0xe143,0xe163,0x4dda},
{0xe164,0xe164,0x2255},
{0xe165,0xe167,0x4dfb},
{0xe168,0xe168,0x1e8f},
{0xe169,0xe173,0x4dfe},
{0xe174,0xe174,0x220c},
{0xe175,0xe175,0x1fa4},
{0xe176,0xe17e,0x4e09},
{0xe180,0xe183,0x4e12},
{0xe184,0xe184,0x209b},
{0xe185,0xe185,0x21d8},
{0xe186,0xe186,0x4e16},
{0xe187,0xe187,0x25fa},
{0xe188,0xe188,0x4e17},
{0xe189,0xe189,0x25f9},
{0xe18a,0xe18b,0x4e18},
{0xe18c,0xe18c,0x2134},
{0xe18d,0xe18d,0x4e1a},
{0xe18e,0xe190,0x24f3},
{0xe191,0xe191,0x24f8},
{0xe192,0xe192,0x4e1b},
{0xe193,0xe193,0x24f7},
{0xe194,0xe194,0x1ec8},
{0xe195,0xe195,0x24f6},
{0xe196,0xe197,0x4e1c},
{0xe198,0xe198,0x2280},
{0xe199,0xe19d,0x4e1e},
{0xe19e,0xe19e,0x1ec5},
{0xe19f,0xe19f,0x24fb},
{0xe1a0,0xe1a0,0x4e23},
{0xe1a1,0xe1fe,0x15a5},
{0xe240,0xe240,0x4e24},
{0xe241,0xe241,0x24fa},
{0xe242,0xe242,0x4e25},
{0xe243,0xe243,0x1eed},
{0xe244,0xe24e,0x4e26},
{0xe24f,0xe24f,0x24fd},
{0xe250,0xe250,0x4e31},
{0xe251,0xe251,0x24f9},
{0xe252,0xe252,0x4e32},
{0xe253,0xe253,0x24fe},
{0xe254,0xe254,0x20cd},
{0xe255,0xe259,0x4e33},
{0xe25a,0xe25a,0x2508},
{0xe25b,0xe25b,0x2504},
{0xe25c,0xe25d,0x4e38},
{0xe25e,0xe25e,0x2506},
{0xe25f,0xe261,0x4e3a},
{0xe262,0xe262,0x24ff},
{0xe263,0xe263,0x2090},
{0xe264,0xe266,0x4e3d},
{0xe267,0xe267,0x1edd},
{0xe268,0xe268,0x1f25},
{0xe269,0xe269,0x4e40},
{0xe26a,0xe26a,0x2503},
{0xe26b,0xe26b,0x2502},
{0xe26c,0xe26d,0x4e41},
{0xe26e,0xe26e,0x1e7a},
{0xe26f,0xe26f,0x20a6},
{0xe270,0xe277,0x4e43},
{0xe278,0xe278,0x1fd5},
{0xe279,0xe27c,0x4e4b},
{0xe27d,0xe27d,0x1f11},
{0xe27e,0xe27e,0x4e4f},
{0xe280,0xe280,0x2507},
{0xe281,0xe281,0x2500},
{0xe282,0xe282,0x2505},
{0xe283,0xe288,0x4e50},
{0xe289,0xe289,0x2519},
{0xe28a,0xe28a,0x4e56},
{0xe28b,0xe28b,0x2515},
{0xe28c,0xe28d,0x4e57},
{0xe28e,0xe28e,0x250c},
{0xe28f,0xe28f,0x2031},
{0xe290,0xe291,0x4e59},
{0xe292,0xe292,0x250b},
{0xe293,0xe293,0x250f},
{0xe294,0xe294,0x251a},
{0xe295,0xe295,0x2509},
{0xe296,0xe297,0x4e5b},
{0xe298,0xe298,0x250e},
{0xe299,0xe299,0x2234},
{0xe29a,0xe29a,0x2513},
{0xe29b,0xe29b,0x1f80},
{0xe29c,0xe29f,0x4e5d},
{0xe2a0,0xe2a0,0x2501},
{0xe2a1,0xe2fe,0x1603},
{0xe340,0xe341,0x4e61},
{0xe342,0xe342,0x2517},
{0xe343,0xe343,0x2516},
{0xe344,0xe346,0x4e63},
{0xe347,0xe347,0x2518},
{0xe348,0xe34a,0x4e66},
{0xe34b,0xe34b,0x1e56},
{0xe34c,0xe34e,0x4e69},
{0xe34f,0xe34f,0x250d},
{0xe350,0xe350,0x4e6c},
{0xe351,0xe351,0x20d3},
{0xe352,0xe353,0x4e6d},
{0xe354,0xe354,0x207a},
{0xe355,0xe355,0x20ce},
{0xe356,0xe357,0x4e6f},
{0xe358,0xe358,0x2510},
{0xe359,0xe35b,0x4e71},
{0xe35c,0xe35c,0x1e55},
{0xe35d,0xe35f,0x4e74},
{0xe360,0xe360,0x250a},
{0xe361,0xe365,0x4e77},
{0xe366,0xe367,0x2511},
{0xe368,0xe370,0x4e7c},
{0xe371,0xe371,0x1faa},
{0xe372,0xe372,0x4e85},
{0xe373,0xe373,0x251e},
{0xe374,0xe374,0x1f1f},
{0xe375,0xe377,0x4e86},
{0xe378,0xe378,0x252d},
{0xe379,0xe379,0x221d},
{0xe37a,0xe37b,0x4e89},
{0xe37c,0xe37c,0x2532},
{0xe37d,0xe37d,0x4e8b},
{0xe37e,0xe37e,0x217b},
{0xe380,0xe389,0x4e8c},
{0xe38a,0xe38a,0x21af},
{0xe38b,0xe38b,0x4e96},
{0xe38c,0xe38c,0x252c},
{0xe38d,0xe38e,0x4e97},
{0xe38f,0xe38f,0x2528},
{0xe390,0xe390,0x4e99},
{0xe391,0xe391,0x208c},
{0xe392,0xe392,0x4e9a},
{0xe393,0xe393,0x252f},
{0xe394,0xe394,0x4e9b},
{0xe395,0xe395,0x21bc},
{0xe396,0xe398,0x4e9c},
{0xe399,0xe399,0x251d},
{0xe39a,0xe39b,0x4e9f},
{0xe39c,0xe39c,0x2535},
{0xe39d,0xe39d,0x4ea1},
{0xe39e,0xe39e,0x220d},
{0xe39f,0xe39f,0x2526},
{0xe3a0,0xe3a0,0x4ea2},
{0xe3a1,0xe3fe,0x1661},
{0xe440,0xe440,0x2534},
{0xe441,0xe441,0x252a},
{0xe442,0xe442,0x251f},
{0xe443,0xe443,0x2531},
{0xe444,0xe444,0x251c},
{0xe445,0xe447,0x4ea3},
{0xe448,0xe448,0x2525},
{0xe449,0xe44d,0x4ea6},
{0xe44e,0xe44e,0x21cd},
{0xe44f,0xe44f,0x4eab},
{0xe450,0xe450,0x21da},
{0xe451,0xe451,0x4eac},
{0xe452,0xe452,0x2172},
{0xe453,0xe453,0x253e},
{0xe454,0xe457,0x4ead},
{0xe458,0xe458,0x204f},
{0xe459,0xe459,0x4eb1},
{0xe45a,0xe45a,0x2543},
{0xe45b,0xe45b,0x4eb2},
{0xe45c,0xe45c,0x21d7},
{0xe45d,0xe45d,0x4eb3},
{0xe45e,0xe45e,0x1e3c},
{0xe45f,0xe461,0x4eb4},
{0xe462,0xe462,0x2529},
{0xe463,0xe464,0x4eb7},
{0xe465,0xe465,0x2521},
{0xe466,0xe467,0x4eb9},
{0xe468,0xe468,0x1eff},
{0xe469,0xe472,0x4ebb},
{0xe473,0xe473,0x253f},
{0xe474,0xe474,0x4ec5},
{0xe475,0xe475,0x2544},
{0xe476,0xe478,0x4ec6},
{0xe479,0xe479,0x2523},
{0xe47a,0xe47a,0x1e90},
{0xe47b,0xe47b,0x253a},
{0xe47c,0xe47c,0x2545},
{0xe47d,0xe47d,0x4ec9},
{0xe47e,0xe47e,0x253d},
{0xe480,0xe480,0x4eca},
{0xe481,0xe481,0x20c1},
{0xe482,0xe483,0x4ecb},
{0xe484,0xe484,0x2103},
{0xe485,0xe485,0x2520},
{0xe486,0xe486,0x253c},
{0xe487,0xe487,0x253b},
{0xe488,0xe488,0x2538},
{0xe489,0xe48c,0x4ecd},
{0xe48d,0xe48d,0x2540},
{0xe48e,0xe48e,0x4ed1},
{0xe48f,0xe48f,0x1fcd},
{0xe490,0xe492,0x4ed2},
{0xe493,0xe493,0x1f18},
{0xe494,0xe497,0x4ed5},
{0xe498,0xe498,0x254b},
{0xe499,0xe49c,0x4ed9},
{0xe49d,0xe49d,0x2547},
{0xe49e,0xe49f,0x254f},
{0xe4a0,0xe4a0,0x4edd},
{0xe4a1,0xe4fe,0x16bf},
{0xe540,0xe545,0x4ede},
{0xe546,0xe546,0x22b0},
{0xe547,0xe547,0x4ee4},
{0xe548,0xe548,0x2546},
{0xe549,0xe54a,0x4ee5},
{0xe54b,0xe54b,0x254c},
{0xe54c,0xe54d,0x4ee7},
{0xe54e,0xe54e,0x1e9a},
{0xe54f,0xe54f,0x2552},
{0xe550,0xe550,0x2530},
{0xe551,0xe551,0x2549},
{0xe552,0xe554,0x4ee9},
{0xe555,0xe555,0x2551},
{0xe556,0xe556,0x1eca},
{0xe557,0xe557,0x4eec},
{0xe558,0xe558,0x20d2},
{0xe559,0xe55b,0x4eed},
{0xe55c,0xe55c,0x1fb8},
{0xe55d,0xe55d,0x4ef0},
{0xe55e,0xe55e,0x2079},
{0xe55f,0xe560,0x4ef1},
{0xe561,0xe561,0x21ab},
{0xe562,0xe563,0x4ef3},
{0xe564,0xe564,0x254d},
{0xe565,0xe565,0x1ea5},
{0xe566,0xe567,0x4ef5},
{0xe568,0xe568,0x204c},
{0xe569,0xe569,0x2080},
{0xe56a,0xe56b,0x4ef7},
{0xe56c,0xe56c,0x266d},
{0xe56d,0xe56d,0x4ef9},
{0xe56e,0xe56e,0x2537},
{0xe56f,0xe574,0x4efa},
{0xe575,0xe575,0x254a},
{0xe576,0xe576,0x21b8},
{0xe577,0xe577,0x4f00},
{0xe578,0xe578,0x254e},
{0xe579,0xe57a,0x4f01},
{0xe57b,0xe57b,0x24fc},
{0xe57c,0xe57c,0x2554},
{0xe57d,0xe57e,0x4f03},
{0xe580,0xe580,0x4f05},
{0xe581,0xe581,0x1f3c},
{0xe582,0xe582,0x4f06},
{0xe583,0xe583,0x1ed5},
{0xe584,0xe589,0x4f07},
{0xe58a,0xe58a,0x2556},
{0xe58b,0xe58d,0x4f0d},
{0xe58e,0xe58e,0x2268},
{0xe58f,0xe590,0x4f10},
{0xe591,0xe591,0x1ed6},
{0xe592,0xe599,0x4f12},
{0xe59a,0xe59a,0x2557},
{0xe59b,0xe59b,0x2553},
{0xe59c,0xe59e,0x4f1a},
{0xe59f,0xe59f,0x2548},
{0xe5a0,0xe5a0,0x4f1d},
{0xe5a1,0xe5fe,0x171d},
{0xe640,0xe640,0x20dc},
{0xe641,0xe643,0x4f1e},
{0xe644,0xe644,0x2559},
{0xe645,0xe648,0x4f21},
{0xe649,0xe649,0x1f97},
{0xe64a,0xe64a,0x2555},
{0xe64b,0xe64d,0x4f25},
{0xe64e,0xe64e,0x227d},
{0xe64f,0xe651,0x4f28},
{0xe652,0xe652,0x257e},
{0xe653,0xe655,0x4f2b},
{0xe656,0xe656,0x207c},
{0xe657,0xe657,0x4f2e},
{0xe658,0xe658,0x255a},
{0xe659,0xe65a,0x4f2f},
{0xe65b,0xe65b,0x255e},
{0xe65c,0xe65d,0x4f31},
{0xe65e,0xe65e,0x1e34},
{0xe65f,0xe668,0x4f33},
{0xe669,0xe669,0x215e},
{0xe66a,0xe66a,0x4f3d},
{0xe66b,0xe66b,0x2560},
{0xe66c,0xe674,0x4f3e},
{0xe675,0xe675,0x21a2},
{0xe676,0xe676,0x2354},
{0xe677,0xe678,0x4f47},
{0xe679,0xe679,0x2563},
{0xe67a,0xe67a,0x2527},
{0xe67b,0xe67b,0x4f49},
{0xe67c,0xe67c,0x252e},
{0xe67d,0xe67d,0x2558},
{0xe67e,0xe67e,0x4f4a},
{0xe680,0xe680,0x1f1b},
{0xe681,0xe681,0x4f4b},
{0xe682,0xe682,0x2283},
{0xe683,0xe683,0x4f4c},
{0xe684,0xe684,0x2564},
{0xe685,0xe686,0x4f4d},
{0xe687,0xe687,0x20a0},
{0xe688,0xe688,0x4f4f},
{0xe689,0xe689,0x2565},
{0xe68a,0xe68b,0x4f50},
{0xe68c,0xe68c,0x2561},
{0xe68d,0xe692,0x4f52},
{0xe693,0xe693,0x2562},
{0xe694,0xe696,0x4f58},
{0xe697,0xe697,0x256c},
{0xe698,0xe69a,0x4f5b},
{0xe69b,0xe69b,0x256d},
{0xe69c,0xe69c,0x2020},
{0xe69d,0xe69e,0x4f5e},
{0xe69f,0xe69f,0x255f},
{0xe6a0,0xe6a0,0x256a},
{0xe6a1,0xe6fe,0x177b},
{0xe740,0xe742,0x4f60},
{0xe743,0xe743,0x256e},
{0xe744,0xe747,0x4f63},
{0xe748,0xe748,0x2539},
{0xe749,0xe749,0x255c},
{0xe74a,0xe74c,0x4f67},
{0xe74d,0xe74e,0x2568},
{0xe74f,0xe74f,0x256b},
{0xe750,0xe750,0x1e6f},
{0xe751,0xe751,0x4f6a},
{0xe752,0xe752,0x1fc4},
{0xe753,0xe753,0x2567},
{0xe754,0xe754,0x4f6b},
{0xe755,0xe755,0x255b},
{0xe756,0xe758,0x4f6c},
{0xe759,0xe759,0x261d},
{0xe75a,0xe765,0x4f6f},
{0xe766,0xe766,0x252b},
{0xe767,0xe767,0x4f7b},
{0xe768,0xe768,0x2571},
{0xe769,0xe769,0x4f7c},
{0xe76a,0xe76a,0x2577},
{0xe76b,0xe773,0x4f7d},
{0xe774,0xe774,0x2522},
{0xe775,0xe77b,0x4f86},
{0xe77c,0xe77c,0x2533},
{0xe77d,0xe77e,0x4f8d},
{0xe780,0xe781,0x4f8f},
{0xe782,0xe782,0x202a},
{0xe783,0xe783,0x4f91},
{0xe784,0xe784,0x2536},
{0xe785,0xe785,0x2573},
{0xe786,0xe786,0x256f},
{0xe787,0xe789,0x4f92},
{0xe78a,0xe78a,0x2293},
{0xe78b,0xe78b,0x2578},
{0xe78c,0xe78e,0x4f95},
{0xe78f,0xe78f,0x2570},
{0xe790,0xe791,0x4f98},
{0xe792,0xe792,0x2575},
{0xe793,0xe797,0x4f9a},
{0xe798,0xe799,0x2541},
{0xe79a,0xe79a,0x255d},
{0xe79b,0xe79f,0x4f9f},
{0xe7a0,0xe7a0,0x201a},
{0xe7a1,0xe7fe,0x17d9},
{0xe840,0xe842,0x4fa4},
{0xe843,0xe843,0x257a},
{0xe844,0xe844,0x2006},
{0xe845,0xe845,0x4fa7},
{0xe846,0xe846,0x2177},
{0xe847,0xe848,0x4fa8},
{0xe849,0xe849,0x251b},
{0xe84a,0xe84a,0x4faa},
{0xe84b,0xe84b,0x2524},
{0xe84c,0xe84e,0x4fab},
{0xe84f,0xe84f,0x257b},
{0xe850,0xe853,0x4fae},
{0xe854,0xe854,0x22a3},
{0xe855,0xe859,0x4fb2},
{0xe85a,0xe85a,0x2579},
{0xe85b,0xe85b,0x4fb7},
{0xe85c,0xe85c,0x2566},
{0xe85d,0xe861,0x4fb8},
{0xe862,0xe862,0x1f93},
{0xe863,0xe863,0x4fbd},
{0xe864,0xe864,0x257c},
{0xe865,0xe86f,0x4fbe},
{0xe870,0xe870,0x2514},
{0xe871,0xe872,0x4fc9},
{0xe873,0xe873,0x257d},
{0xe874,0xe874,0x4fcb},
{0xe875,0xe875,0x2572},
{0xe876,0xe87b,0x4fcc},
{0xe87c,0xe87c,0x2574},
{0xe87d,0xe87e,0x4fd2},
{0xe880,0xe880,0x224e},
{0xe881,0xe881,0x4fd4},
{0xe882,0xe882,0x21c6},
{0xe883,0xe886,0x4fd5},
{0xe887,0xe887,0x209f},
{0xe888,0xe888,0x4fd9},
{0xe889,0xe889,0x2576},
{0xe88a,0xe88b,0x4fda},
{0xe88c,0xe88c,0x2064},
{0xe88d,0xe88d,0x22bf},
{0xe88e,0xe88e,0x261c},
{0xe88f,0xe88f,0x225e},
{0xe890,0xe8a0,0x4fdc},
{0xe8a1,0xe8fe,0x1837},
{0xe940,0xe94b,0x4fed},
{0xe94c,0xe94c,0x1e75},
{0xe94d,0xe953,0x4ff9},
{0xe954,0xe954,0x207d},
{0xe955,0xe955,0x5000},
{0xe956,0xe956,0x23bf},
{0xe957,0xe957,0x2113},
{0xe958,0xe959,0x5001},
{0xe95a,0xe95a,0x23c0},
{0xe95b,0xe95c,0x5003},
{0xe95d,0xe95d,0x1e45},
{0xe95e,0xe95e,0x5005},
{0xe95f,0xe95f,0x1fd8},
{0xe960,0xe960,0x23c4},
{0xe961,0xe961,0x5006},
{0xe962,0xe962,0x23c2},
{0xe963,0xe963,0x2104},
{0xe964,0xe964,0x5007},
{0xe965,0xe965,0x21bd},
{0xe966,0xe966,0x5008},
{0xe967,0xe967,0x1f87},
{0xe968,0xe968,0x23c3},
{0xe969,0xe96b,0x5009},
{0xe96c,0xe96c,0x2269},
{0xe96d,0xe974,0x500c},
{0xe975,0xe975,0x1f43},
{0xe976,0xe976,0x5014},
{0xe977,0xe977,0x1f1e},
{0xe978,0xe978,0x2679},
{0xe979,0xe979,0x1eeb},
{0xe97a,0xe97b,0x5015},
{0xe97c,0xe97c,0x1f35},
{0xe97d,0xe97d,0x208a},
{0xe97e,0xe97e,0x5017},
{0xe980,0xe980,0x23c7},
{0xe981,0xe981,0x23c9},
{0xe982,0xe982,0x23c6},
{0xe983,0xe986,0x5018},
{0xe987,0xe987,0x224f},
{0xe988,0xe98a,0x501c},
{0xe98b,0xe98b,0x23cb},
{0xe98c,0xe98d,0x501f},
{0xe98e,0xe98e,0x21f3},
{0xe98f,0xe98f,0x5021},
{0xe990,0xe990,0x21f7},
{0xe991,0xe991,0x23cf},
{0xe992,0xe992,0x23ce},
{0xe993,0xe993,0x23ca},
{0xe994,0xe994,0x23cd},
{0xe995,0xe997,0x5022},
{0xe998,0xe998,0x23d0},
{0xe999,0xe99a,0x5025},
{0xe99b,0xe99b,0x266c},
{0xe99c,0xe99c,0x5027},
{0xe99d,0xe99d,0x23c1},
{0xe99e,0xe99e,0x5028},
{0xe99f,0xe99f,0x1fee},
{0xe9a0,0xe9a0,0x23d1},
{0xe9a1,0xe9fe,0x1895},
{0xea40,0xea40,0x1ff8},
{0xea41,0xea43,0x5029},
{0xea44,0xea44,0x23d3},
{0xea45,0xea47,0x502c},
{0xea48,0xea48,0x23d2},
{0xea49,0xea49,0x23d4},
{0xea4a,0xea4a,0x1e98},
{0xea4b,0xea4f,0x502f},
{0xea50,0xea50,0x1f2c},
{0xea51,0xea51,0x5034},
{0xea52,0xea52,0x23d5},
{0xea53,0xea54,0x5035},
{0xea55,0xea55,0x1e71},
{0xea56,0xea56,0x2691},
{0xea57,0xea58,0x5037},
{0xea59,0xea59,0x23c5},
{0xea5a,0xea7e,0x5039},
{0xea80,0xea80,0x231a},
{0xea81,0xea83,0x505e},
{0xea84,0xea84,0x2114},
{0xea85,0xea86,0x5061},
{0xea87,0xea87,0x2284},
{0xea88,0xea8d,0x5063},
{0xea8e,0xea8e,0x221c},
{0xea8f,0xea8f,0x5069},
{0xea90,0xea90,0x1e7e},
{0xea91,0xea91,0x204d},
{0xea92,0xea95,0x506a},
{0xea96,0xea96,0x2201},
{0xea97,0xea9f,0x506e},
{0xeaa0,0xeaa0,0x1ed9},
{0xeaa1,0xeafe,0x18f3},
{0xeb40,0xeb40,0x5077},
{0xeb41,0xeb41,0x1fb2},
{0xeb42,0xeb44,0x5078},
{0xeb45,0xeb45,0x2252},
{0xeb46,0xeb47,0x507b},
{0xeb48,0xeb48,0x1f79},
{0xeb49,0xeb52,0x507d},
{0xeb53,0xeb53,0x2157},
{0xeb54,0xeb54,0x5087},
{0xeb55,0xeb55,0x21bf},
{0xeb56,0xeb5a,0x5088},
{0xeb5b,0xeb5b,0x221f},
{0xeb5c,0xeb5c,0x508d},
{0xeb5d,0xeb5d,0x203d},
{0xeb5e,0xeb5f,0x508e},
{0xeb60,0xeb60,0x2015},
{0xeb61,0xeb61,0x5090},
{0xeb62,0xeb62,0x26a5},
{0xeb63,0xeb6c,0x5091},
{0xeb6d,0xeb6d,0x2156},
{0xeb6e,0xeb6f,0x509b},
{0xeb70,0xeb70,0x2144},
{0xeb71,0xeb71,0x509d},
{0xeb72,0xeb72,0x1e91},
{0xeb73,0xeb73,0x2257},
{0xeb74,0xeb77,0x509e},
{0xeb78,0xeb78,0x200a},
{0xeb79,0xeb79,0x2092},
{0xeb7a,0xeb7e,0x50a2},
{0xeb80,0xeb84,0x50a7},
{0xeb85,0xeb85,0x2250},
{0xeb86,0xeb89,0x50ac},
{0xeb8a,0xeb8a,0x1ec3},
{0xeb8b,0xeba0,0x50b0},
{0xeba1,0xebfe,0x1951},
{0xec40,0xec45,0x50c6},
{0xec46,0xec46,0x21a8},
{0xec47,0xec55,0x50cc},
{0xec56,0xec56,0x260d},
{0xec57,0xec59,0x50db},
{0xec5a,0xec5a,0x260c},
{0xec5b,0xec5b,0x50de},
{0xec5c,0xec5c,0x260e},
{0xec5d,0xec5f,0x50df},
{0xec60,0xec60,0x2032},
{0xec61,0xec6d,0x50e2},
{0xec6e,0xec6e,0x260b},
{0xec6f,0xec75,0x50ef},
{0xec76,0xec76,0x22c3},
{0xec77,0xec7e,0x50f6},
{0xec80,0xec95,0x50fe},
{0xec96,0xec96,0x1f23},
{0xec97,0xeca0,0x5114},
{0xeca1,0xecfe,0x19af},
{0xed40,0xed45,0x511e},
{0xed46,0xed46,0x2695},
{0xed47,0xed57,0x5124},
{0xed58,0xed58,0x265e},
{0xed59,0xed5d,0x5135},
{0xed5e,0xed5e,0x265d},
{0xed5f,0xed60,0x513a},
{0xed61,0xed61,0x2692},
{0xed62,0xed63,0x513c},
{0xed64,0xed64,0x265f},
{0xed65,0xed65,0x513e},
{0xed66,0xed66,0x218d},
{0xed67,0xed67,0x20fd},
{0xed68,0xed6d,0x513f},
{0xed6e,0xed6e,0x1f40},
{0xed6f,0xed73,0x5145},
{0xed74,0xed74,0x2465},
{0xed75,0xed76,0x514a},
{0xed77,0xed77,0x2467},
{0xed78,0xed78,0x514c},
{0xed79,0xed79,0x2466},
{0xed7a,0xed7e,0x514d},
{0xed80,0xed90,0x5152},
{0xed91,0xed91,0x21c9},
{0xed92,0xed92,0x5163},
{0xed93,0xed93,0x2209},
{0xed94,0xed94,0x1ec9},
{0xed95,0xed95,0x20e9},
{0xed96,0xed96,0x5164},
{0xed97,0xed97,0x21ca},
{0xed98,0xed98,0x2146},
{0xed99,0xed99,0x25c5},
{0xed9a,0xed9a,0x21dc},
{0xed9b,0xed9b,0x5165},
{0xed9c,0xed9c,0x245e},
{0xed9d,0xed9d,0x5166},
{0xed9e,0xed9e,0x214f},
{0xed9f,0xed9f,0x5167},
{0xeda0,0xeda0,0x25c6},
{0xeda1,0xedfe,0x1a0d},
{0xee40,0xee40,0x25c7},
{0xee41,0xee41,0x2241},
{0xee42,0xee42,0x218a},
{0xee43,0xee43,0x1e2f},
{0xee44,0xee44,0x1edc},
{0xee45,0xee47,0x5168},
{0xee48,0xee48,0x20bf},
{0xee49,0xee49,0x2034},
{0xee4a,0xee4c,0x516b},
{0xee4d,0xee4d,0x25c9},
{0xee4e,0xee51,0x516e},
{0xee52,0xee52,0x25c8},
{0xee53,0xee54,0x5172},
{0xee55,0xee55,0x220e},
{0xee56,0xee56,0x5174},
{0xee57,0xee57,0x25cb},
{0xee58,0xee5d,0x5175},
{0xee5e,0xee5e,0x217d},
{0xee5f,0xee60,0x517b},
{0xee61,0xee61,0x1f7e},
{0xee62,0xee67,0x517d},
{0xee68,0xee68,0x25cc},
{0xee69,0xee69,0x1fc3},
{0xee6a,0xee6b,0x5183},
{0xee6c,0xee6c,0x20b9},
{0xee6d,0xee6d,0x5185},
{0xee6e,0xee6e,0x2181},
{0xee6f,0xee76,0x5186},
{0xee77,0xee77,0x1fda},
{0xee78,0xee7c,0x518e},
{0xee7d,0xee7d,0x2173},
{0xee7e,0xee7e,0x1ee1},
{0xee80,0xee80,0x25cd},
{0xee81,0xee84,0x5193},
{0xee85,0xee85,0x25ce},
{0xee86,0xee86,0x21f6},
{0xee87,0xee89,0x5197},
{0xee8a,0xee8a,0x224b},
{0xee8b,0xee8b,0x25d1},
{0xee8c,0xee8c,0x519a},
{0xee8d,0xee8d,0x1ec0},
{0xee8e,0xee8f,0x519b},
{0xee90,0xee90,0x2008},
{0xee91,0xee93,0x519d},
{0xee94,0xee94,0x25d0},
{0xee95,0xee96,0x51a0},
{0xee97,0xee97,0x25d2},
{0xee98,0xee98,0x51a2},
{0xee99,0xee99,0x1f2a},
{0xee9a,0xee9c,0x51a3},
{0xee9d,0xee9d,0x1e72},
{0xee9e,0xee9e,0x25d3},
{0xee9f,0xeea0,0x51a6},
{0xeea1,0xeefe,0x1a6b},
{0xef40,0xef40,0x21be},
{0xef41,0xef41,0x25d4},
{0xef42,0xef42,0x2044},
{0xef43,0xef43,0x51a8},
{0xef44,0xef44,0x25cf},
{0xef45,0xef45,0x20f3},
{0xef46,0xef4b,0x51a9},
{0xef4c,0xef4c,0x1f00},
{0xef4d,0xef51,0x51af},
{0xef52,0xef53,0x24cc},
{0xef54,0xef54,0x51b4},
{0xef55,0xef55,0x2698},
{0xef56,0xef56,0x51b5},
{0xef57,0xef57,0x2678},
{0xef58,0xef59,0x51b6},
{0xef5a,0xef5a,0x24ce},
{0xef5b,0xef5f,0x51b8},
{0xef60,0xef60,0x24cf},
{0xef61,0xef67,0x51bd},
{0xef68,0xef68,0x20b8},
{0xef69,0xef69,0x51c4},
{0xef6a,0xef6a,0x24d0},
{0xef6b,0xef6b,0x51c5},
{0xef6c,0xef6c,0x24d1},
{0xef6d,0xef76,0x51c6},
{0xef77,0xef77,0x1ef4},
{0xef78,0xef79,0x51d0},
{0xef7a,0xef7a,0x239b},
{0xef7b,0xef7b,0x51d2},
{0xef7c,0xef7c,0x267e},
{0xef7d,0xef7e,0x51d3},
{0xef80,0xef81,0x51d5},
{0xef82,0xef82,0x239d},
{0xef83,0xef84,0x239f},
{0xef85,0xef85,0x51d7},
{0xef86,0xef86,0x23a1},
{0xef87,0xef87,0x51d8},
{0xef88,0xef88,0x1ef1},
{0xef89,0xef8a,0x51d9},
{0xef8b,0xef8b,0x221e},
{0xef8c,0xef8c,0x51db},
{0xef8d,0xef8d,0x23a2},
{0xef8e,0xef94,0x51dc},
{0xef95,0xef95,0x214b},
{0xef96,0xef96,0x1e36},
{0xef97,0xef97,0x2135},
{0xef98,0xef9b,0x51e3},
{0xef9c,0xef9c,0x1fad},
{0xef9d,0xef9d,0x51e7},
{0xef9e,0xef9e,0x1e53},
{0xef9f,0xefa0,0x51e8},
{0xefa1,0xeffe,0x1ac9},
{0xf040,0xf040,0x51ea},
{0xf041,0xf041,0x23a3},
{0xf042,0xf042,0x2203},
{0xf043,0xf043,0x51eb},
{0xf044,0xf044,0x1ee7},
{0xf045,0xf046,0x51ec},
{0xf047,0xf047,0x23a4},
{0xf048,0xf048,0x2097},
{0xf049,0xf049,0x1ee4},
{0xf04a,0xf04d,0x51ee},
{0xf04e,0xf04e,0x2238},
{0xf04f,0xf050,0x51f2},
{0xf051,0xf051,0x23a5},
{0xf052,0xf053,0x51f4},
{0xf054,0xf054,0x1f9a},
{0xf055,0xf056,0x51f6},
{0xf057,0xf057,0x21c3},
{0xf058,0xf05d,0x51f8},
{0xf05e,0xf05e,0x1f2e},
{0xf05f,0xf067,0x51fe},
{0xf068,0xf068,0x239c},
{0xf069,0xf06b,0x5207},
{0xf06c,0xf06c,0x23a6},
{0xf06d,0xf070,0x520a},
{0xf071,0xf071,0x239e},
{0xf072,0xf072,0x520e},
{0xf073,0xf073,0x2035},
{0xf074,0xf074,0x23a7},
{0xf075,0xf077,0x520f},
{0xf078,0xf078,0x23a8},
{0xf079,0xf079,0x5212},
{0xf07a,0xf07a,0x2075},
{0xf07b,0xf07c,0x5213},
{0xf07d,0xf07e,0x23a9},
{0xf080,0xf080,0x23ab},
{0xf081,0xf081,0x1feb},
{0xf082,0xf082,0x23ac},
{0xf083,0xf086,0x5215},
{0xf087,0xf087,0x1f6a},
{0xf088,0xf088,0x20f9},
{0xf089,0xf08a,0x5219},
{0xf08b,0xf08b,0x2666},
{0xf08c,0xf08f,0x521b},
{0xf090,0xf090,0x2667},
{0xf091,0xf091,0x521f},
{0xf092,0xf092,0x1e6c},
{0xf093,0xf095,0x5220},
{0xf096,0xf096,0x23ad},
{0xf097,0xf0a0,0x5223},
{0xf0a1,0xf0fe,0x1b27},
{0xf140,0xf151,0x522d},
{0xf152,0xf152,0x206d},
{0xf153,0xf153,0x2242},
{0xf154,0xf154,0x1f02},
{0xf155,0xf156,0x523f},
{0xf157,0xf157,0x2183},
{0xf158,0xf158,0x5241},
{0xf159,0xf159,0x1e85},
{0xf15a,0xf15a,0x21e9},
{0xf15b,0xf166,0x5242},
{0xf167,0xf167,0x1e57},
{0xf168,0xf175,0x524e},
{0xf176,0xf176,0x22a5},
{0xf177,0xf177,0x2407},
{0xf178,0xf178,0x1fca},
{0xf179,0xf179,0x525c},
{0xf17a,0xf17a,0x2402},
{0xf17b,0xf17b,0x1f82},
{0xf17c,0xf17d,0x525d},
{0xf17e,0xf17e,0x2408},
{0xf180,0xf180,0x2404},
{0xf181,0xf181,0x525f},
{0xf182,0xf182,0x2131},
{0xf183,0xf183,0x5260},
{0xf184,0xf184,0x2184},
{0xf185,0xf185,0x5261},
{0xf186,0xf186,0x2403},
{0xf187,0xf187,0x5262},
{0xf188,0xf188,0x206e},
{0xf189,0xf189,0x240b},
{0xf18a,0xf193,0x5263},
{0xf194,0xf194,0x1f3f},
{0xf195,0xf197,0x526d},
{0xf198,0xf198,0x2067},
{0xf199,0xf1a0,0x5270},
{0xf1a1,0xf1fe,0x1b85},
{0xf240,0xf244,0x5278},
{0xf245,0xf245,0x1fd7},
{0xf246,0xf246,0x527d},
{0xf247,0xf247,0x1e83},
{0xf248,0xf24a,0x527e},
{0xf24b,0xf24b,0x240f},
{0xf24c,0xf252,0x5281},
{0xf253,0xf253,0x240e},
{0xf254,0xf254,0x20c7},
{0xf255,0xf255,0x240d},
{0xf256,0xf25b,0x5288},
{0xf25c,0xf25c,0x2412},
{0xf25d,0xf25e,0x528e},
{0xf25f,0xf25f,0x20b7},
{0xf260,0xf270,0x5290},
{0xf271,0xf271,0x23f0},
{0xf272,0xf272,0x52a1},
{0xf273,0xf273,0x2411},
{0xf274,0xf274,0x2414},
{0xf275,0xf275,0x52a2},
{0xf276,0xf276,0x2170},
{0xf277,0xf27b,0x52a3},
{0xf27c,0xf27c,0x2405},
{0xf27d,0xf27d,0x210c},
{0xf27e,0xf27e,0x2415},
{0xf280,0xf284,0x52a8},
{0xf285,0xf285,0x2066},
{0xf286,0xf286,0x52ad},
{0xf287,0xf287,0x2352},
{0xf288,0xf288,0x2413},
{0xf289,0xf289,0x2410},
{0xf28a,0xf28b,0x2416},
{0xf28c,0xf28c,0x20f1},
{0xf28d,0xf290,0x52ae},
{0xf291,0xf291,0x240a},
{0xf292,0xf293,0x52b2},
{0xf294,0xf294,0x2409},
{0xf295,0xf295,0x52b4},
{0xf296,0xf296,0x2418},
{0xf297,0xf29b,0x52b5},
{0xf29c,0xf29c,0x1fa7},
{0xf29d,0xf29d,0x52ba},
{0xf29e,0xf29e,0x21fc},
{0xf29f,0xf2a0,0x52bb},
{0xf2a1,0xf2fe,0x1be3},
{0xf340,0xf340,0x1fc1},
{0xf341,0xf341,0x2406},
{0xf342,0xf344,0x52bd},
{0xf345,0xf345,0x229c},
{0xf346,0xf347,0x52c0},
{0xf348,0xf348,0x204e},
{0xf349,0xf349,0x52c2},
{0xf34a,0xf34a,0x241a},
{0xf34b,0xf34b,0x2419},
{0xf34c,0xf34f,0x52c3},
{0xf350,0xf350,0x240c},
{0xf351,0xf360,0x52c7},
{0xf361,0xf361,0x1e29},
{0xf362,0xf373,0x52d7},
{0xf374,0xf374,0x2661},
{0xf375,0xf375,0x52e9},
{0xf376,0xf376,0x26a4},
{0xf377,0xf377,0x2174},
{0xf378,0xf378,0x2663},
{0xf379,0xf379,0x2662},
{0xf37a,0xf37e,0x52ea},
{0xf380,0xf38b,0x52ef},
{0xf38c,0xf38c,0x2675},
{0xf38d,0xf39f,0x52fb},
{0xf3a0,0xf3a0,0x214c},
{0xf3a1,0xf3fe,0x1c41},
{0xf440,0xf444,0x530e},
{0xf445,0xf445,0x267a},
{0xf446,0xf44f,0x5313},
{0xf450,0xf450,0x26a0},
{0xf451,0xf456,0x531d},
{0xf457,0xf457,0x2668},
{0xf458,0xf458,0x5323},
{0xf459,0xf459,0x1ed0},
{0xf45a,0xf45a,0x5324},
{0xf45b,0xf45b,0x2096},
{0xf45c,0xf45c,0x5325},
{0xf45d,0xf45d,0x23cc},
{0xf45e,0xf461,0x5326},
{0xf462,0xf462,0x23c8},
{0xf463,0xf463,0x532a},
{0xf464,0xf464,0x223e},
{0xf465,0xf474,0x532b},
{0xf475,0xf475,0x2665},
{0xf476,0xf47b,0x533b},
{0xf47c,0xf47c,0x2664},
{0xf47d,0xf47d,0x5341},
{0xf47e,0xf47e,0x2239},
{0xf480,0xf493,0x5342},
{0xf494,0xf494,0x204a},
{0xf495,0xf498,0x5356},
{0xf499,0xf499,0x261f},
{0xf49a,0xf49b,0x535a},
{0xf49c,0xf49c,0x261e},
{0xf49d,0xf4a0,0x535c},
{0xf4a1,0xf4fe,0x1c9f},
{0xf540,0xf544,0x5360},
{0xf545,0xf545,0x2620},
{0xf546,0xf546,0x5365},
{0xf547,0xf547,0x2621},
{0xf548,0xf551,0x5366},
{0xf552,0xf552,0x2622},
{0xf553,0xf553,0x5370},
{0xf554,0xf554,0x2627},
{0xf555,0xf555,0x1e39},
{0xf556,0xf556,0x2625},
{0xf557,0xf55d,0x5371},
{0xf55e,0xf55e,0x2629},
{0xf55f,0xf560,0x5378},
{0xf561,0xf561,0x262e},
{0xf562,0xf562,0x262b},
{0xf563,0xf56d,0x537a},
{0xf56e,0xf56e,0x262a},
{0xf56f,0xf56f,0x262d},
{0xf570,0xf570,0x5385},
{0xf571,0xf571,0x2628},
{0xf572,0xf572,0x21b9},
{0xf573,0xf57e,0x5386},
{0xf580,0xf584,0x5392},
{0xf585,0xf585,0x2636},
{0xf586,0xf586,0x2630},
{0xf587,0xf58b,0x5397},
{0xf58c,0xf58c,0x2638},
{0xf58d,0xf58d,0x539c},
{0xf58e,0xf58e,0x200d},
{0xf58f,0xf58f,0x2637},
{0xf590,0xf598,0x539d},
{0xf599,0xf599,0x2645},
{0xf59a,0xf59a,0x53a6},
{0xf59b,0xf59b,0x263a},
{0xf59c,0xf59f,0x53a7},
{0xf5a0,0xf5a0,0x2643},
{0xf5a1,0xf5fe,0x1cfd},
{0xf640,0xf640,0x53ab},
{0xf641,0xf641,0x2640},
{0xf642,0xf644,0x53ac},
{0xf645,0xf645,0x263d},
{0xf646,0xf646,0x2641},
{0xf647,0xf647,0x53af},
{0xf648,0xf648,0x263e},
{0xf649,0xf64a,0x53b0},
{0xf64b,0xf64b,0x263f},
{0xf64c,0xf64c,0x1fc0},
{0xf64d,0xf64d,0x53b2},
{0xf64e,0xf64f,0x263b},
{0xf650,0xf653,0x53b3},
{0xf654,0xf654,0x2642},
{0xf655,0xf657,0x53b7},
{0xf658,0xf658,0x2644},
{0xf659,0xf660,0x53ba},
{0xf661,0xf661,0x2639},
{0xf662,0xf662,0x53c2},
{0xf663,0xf663,0x264c},
{0xf664,0xf66b,0x53c3},
{0xf66c,0xf66c,0x2647},
{0xf66d,0xf66d,0x264b},
{0xf66e,0xf670,0x53cb},
{0xf671,0xf671,0x2649},
{0xf672,0xf673,0x53ce},
{0xf674,0xf674,0x2648},
{0xf675,0xf675,0x53d0},
{0xf676,0xf676,0x264a},
{0xf677,0xf677,0x2108},
{0xf678,0xf67e,0x53d1},
{0xf680,0xf684,0x53d8},
{0xf685,0xf685,0x264d},
{0xf686,0xf687,0x53dd},
{0xf688,0xf688,0x2634},
{0xf689,0xf689,0x53df},
{0xf68a,0xf68a,0x2651},
{0xf68b,0xf68c,0x53e0},
{0xf68d,0xf68d,0x2650},
{0xf68e,0xf68e,0x2652},
{0xf68f,0xf691,0x53e2},
{0xf692,0xf692,0x264f},
{0xf693,0xf695,0x53e5},
{0xf696,0xf696,0x2632},
{0xf697,0xf697,0x264e},
{0xf698,0xf698,0x2653},
{0xf699,0xf699,0x53e8},
{0xf69a,0xf69a,0x2657},
{0xf69b,0xf69b,0x53e9},
{0xf69c,0xf69c,0x2635},
{0xf69d,0xf69d,0x53ea},
{0xf69e,0xf69e,0x2633},
{0xf69f,0xf69f,0x53eb},
{0xf6a0,0xf6a0,0x2656},
{0xf6a1,0xf6fe,0x1d5b},
{0xf740,0xf741,0x53ec},
{0xf742,0xf742,0x2654},
{0xf743,0xf748,0x53ee},
{0xf749,0xf749,0x2658},
{0xf74a,0xf74b,0x53f4},
{0xf74c,0xf74c,0x2655},
{0xf74d,0xf74d,0x1e4d},
{0xf74e,0xf755,0x53f6},
{0xf756,0xf756,0x265b},
{0xf757,0xf757,0x53fe},
{0xf758,0xf758,0x265a},
{0xf759,0xf759,0x53ff},
{0xf75a,0xf75a,0x2659},
{0xf75b,0xf75b,0x202e},
{0xf75c,0xf75c,0x262f},
{0xf75d,0xf760,0x5400},
{0xf761,0xf761,0x2646},
{0xf762,0xf762,0x5404},
{0xf763,0xf763,0x2626},
{0xf764,0xf76a,0x5405},
{0xf76b,0xf76b,0x265c},
{0xf76c,0xf770,0x540c},
{0xf771,0xf771,0x262c},
{0xf772,0xf77b,0x5411},
{0xf77c,0xf77c,0x2623},
{0xf77d,0xf77d,0x541b},
{0xf77e,0xf77e,0x2631},
{0xf780,0xf7a0,0x541c},
{0xf7a1,0xf7fe,0x1db9},
{0xf840,0xf841,0x543d},
{0xf842,0xf842,0x209c},
{0xf843,0xf845,0x543f},
{0xf846,0xf846,0x2580},
{0xf847,0xf848,0x5442},
{0xf849,0xf849,0x22dc},
{0xf84a,0xf84f,0x5444},
{0xf850,0xf850,0x1f05},
{0xf851,0xf851,0x208b},
{0xf852,0xf852,0x544a},
{0xf853,0xf853,0x2581},
{0xf854,0xf862,0x544b},
{0xf863,0xf863,0x2583},
{0xf864,0xf864,0x2582},
{0xf865,0xf865,0x545a},
{0xf866,0xf866,0x21ee},
{0xf867,0xf871,0x545b},
{0xf872,0xf872,0x2182},
{0xf873,0xf877,0x5466},
{0xf878,0xf878,0x2243},
{0xf879,0xf879,0x546b},
{0xf87a,0xf87a,0x2587},
{0xf87b,0xf87b,0x546c},
{0xf87c,0xf87c,0x2588},
{0xf87d,0xf87e,0x546d},
{0xf880,0xf880,0x546f},
{0xf881,0xf881,0x2584},
{0xf882,0xf883,0x5470},
{0xf884,0xf884,0x21fd},
{0xf885,0xf885,0x5472},
{0xf886,0xf886,0x21ef},
{0xf887,0xf88c,0x5473},
{0xf88d,0xf88d,0x258a},
{0xf88e,0xf88e,0x258c},
{0xf88f,0xf898,0x5479},
{0xf899,0xf899,0x1f47},
{0xf89a,0xf89c,0x5483},
{0xf89d,0xf89d,0x1f1d},
{0xf89e,0xf89f,0x5486},
{0xf8a0,0xf8a0,0x258d},
{0xf940,0xf94d,0x5488},
{0xf94e,0xf94e,0x1fd0},
{0xf94f,0xf94f,0x2592},
{0xf950,0xf950,0x258f},
{0xf951,0xf958,0x5496},
{0xf959,0xf959,0x2594},
{0xf95a,0xf95a,0x1ee0},
{0xf95b,0xf95c,0x549e},
{0xf95d,0xf95d,0x2591},
{0xf95e,0xf95e,0x2595},
{0xf95f,0xf966,0x54a0},
{0xf967,0xf967,0x2597},
{0xf968,0xf968,0x54a8},
{0xf969,0xf969,0x20b6},
{0xf96a,0xf96b,0x54a9},
{0xf96c,0xf96c,0x2598},
{0xf96d,0xf96e,0x54ab},
{0xf96f,0xf96f,0x20f6},
{0xf970,0xf97e,0x54ad},
{0xf980,0xf984,0x54bc},
{0xf985,0xf985,0x2585},
{0xf986,0xf986,0x54c1},
{0xf987,0xf987,0x2599},
{0xf988,0xf990,0x54c2},
{0xf991,0xf991,0x2596},
{0xf992,0xf995,0x54cb},
{0xf996,0xf996,0x259a},
{0xf997,0xf997,0x54cf},
{0xf998,0xf998,0x259b},
{0xf999,0xf9a0,0x54d0},
{0xfa40,0xfa41,0x54d8},
{0xfa42,0xfa42,0x259d},
{0xfa43,0xfa45,0x54da},
{0xfa46,0xfa46,0x259e},
{0xfa47,0xfa4b,0x54dd},
{0xfa4c,0xfa4c,0x234c},
{0xfa4d,0xfa50,0x54e2},
{0xfa51,0xfa51,0x1f44},
{0xfa52,0xfa57,0x54e6},
{0xfa58,0xfa58,0x2660},
{0xfa59,0xfa59,0x25a0},
{0xfa5a,0xfa5c,0x54ec},
{0xfa5d,0xfa5d,0x259c},
{0xfa5e,0xfa5e,0x54ef},
{0xfa5f,0xfa5f,0x259f},
{0xfa60,0xfa60,0x54f0},
{0xfa61,0xfa61,0x1f6c},
{0xfa62,0xfa6f,0x54f1},
{0xfa70,0xfa70,0x25a2},
{0xfa71,0xfa73,0x54ff},
{0xfa74,0xfa74,0x20ae},
{0xfa75,0xfa75,0x5502},
{0xfa76,0xfa76,0x258b},
{0xfa77,0xfa77,0x25a3},
{0xfa78,0xfa7e,0x5503},
{0xfa80,0xfa82,0x550a},
{0xfa83,0xfa83,0x2589},
{0xfa84,0xfa84,0x25a5},
{0xfa85,0xfa8c,0x550d},
{0xfa8d,0xfa8d,0x25a4},
{0xfa8e,0xfa8f,0x5515},
{0xfa90,0xfa90,0x25a6},
{0xfa91,0xfa91,0x2593},
{0xfa92,0xfa95,0x5517},
{0xfa96,0xfa96,0x25a7},
{0xfa97,0xfa97,0x2222},
{0xfa98,0xfa98,0x25a9},
{0xfa99,0xfaa0,0x551b},
{0xfb40,0xfb48,0x5523},
{0xfb49,0xfb49,0x25a8},
{0xfb4a,0xfb51,0x552c},
{0xfb52,0xfb52,0x2586},
{0xfb53,0xfb56,0x5534},
{0xfb57,0xfb57,0x25a1},
{0xfb58,0xfb58,0x25aa},
{0xfb59,0xfb59,0x5538},
{0xfb5a,0xfb5a,0x2590},
{0xfb5b,0xfb5b,0x258e},
{0xfb5c,0xfb74,0x5539},
{0xfb75,0xfb75,0x2688},
{0xfb76,0xfb78,0x5552},
{0xfb79,0xfb79,0x269e},
{0xfb7a,0xfb7a,0x25fb},
{0xfb7b,0xfb7b,0x5555},
{0xfb7c,0xfb7c,0x1f8c},
{0xfb7d,0xfb7d,0x21f4},
{0xfb7e,0xfb7e,0x5556},
{0xfb80,0xfb8f,0x5557},
{0xfb90,0xfb90,0x200f},
{0xfb91,0xfb9b,0x5567},
{0xfb9c,0xfb9c,0x2071},
{0xfb9d,0xfb9e,0x5572},
{0xfb9f,0xfb9f,0x25f7},
{0xfba0,0xfba0,0x5574},
{0xfc40,0xfc43,0x5575},
{0xfc44,0xfc44,0x2696},
{0xfc45,0xfc48,0x5579},
{0xfc49,0xfc49,0x268f},
{0xfc4a,0xfc59,0x557d},
{0xfc5a,0xfc5a,0x22da},
{0xfc5b,0xfc62,0x558d},
{0xfc63,0xfc63,0x1ec1},
{0xfc64,0xfc67,0x5595},
{0xfc68,0xfc68,0x1eb3},
{0xfc69,0xfc6e,0x5599},
{0xfc6f,0xfc6f,0x266a},
{0xfc70,0xfc70,0x559f},
{0xfc71,0xfc71,0x268a},
{0xfc72,0xfc73,0x55a0},
{0xfc74,0xfc74,0x2669},
{0xfc75,0xfc76,0x55a2},
{0xfc77,0xfc78,0x2618},
{0xfc79,0xfc7e,0x55a4},
{0xfc80,0xfc82,0x55aa},
{0xfc83,0xfc83,0x261a},
{0xfc84,0xfc89,0x55ad},
{0xfc8a,0xfc8a,0x2673},
{0xfc8b,0xfca0,0x55b3},
{0xfd40,0xfd51,0x55c9},
{0xfd52,0xfd52,0x20c6},
{0xfd53,0xfd53,0x226b},
{0xfd54,0xfd56,0x55db},
{0xfd57,0xfd57,0x24d3},
{0xfd58,0xfd58,0x1e86},
{0xfd59,0xfd59,0x55de},
{0xfd5a,0xfd5a,0x260f},
{0xfd5b,0xfd5e,0x55df},
{0xfd5f,0xfd5f,0x2611},
{0xfd60,0xfd61,0x55e3},
{0xfd62,0xfd62,0x2613},
{0xfd63,0xfd64,0x55e5},
{0xfd65,0xfd65,0x2610},
{0xfd66,0xfd66,0x2612},
{0xfd67,0xfd67,0x2030},
{0xfd68,0xfd68,0x55e7},
{0xfd69,0xfd69,0x2671},
{0xfd6a,0xfd6b,0x55e8},
{0xfd6c,0xfd6c,0x2614},
{0xfd6d,0xfd6f,0x55ea},
{0xfd70,0xfd70,0x2616},
{0xfd71,0xfd71,0x55ed},
{0xfd72,0xfd72,0x2615},
{0xfd73,0xfd77,0x55ee},
{0xfd78,0xfd78,0x20f2},
{0xfd79,0xfd7c,0x55f3},
{0xfd7d,0xfd7d,0x2617},
{0xfd7e,0xfd7e,0x55f7},
{0xfd80,0xfd87,0x55f8},
{0xfd88,0xfd88,0x2037},
{0xfd89,0xfd8a,0x5600},
{0xfd8b,0xfd8b,0x20b3},
{0xfd8c,0xfd8e,0x5602},
{0xfd8f,0xfd8f,0x1f22},
{0xfd90,0xfd90,0x24ed},
{0xfd91,0xfd93,0x5605},
{0xfd94,0xfd94,0x1f34},
{0xfd95,0xfd9c,0x5608},
{0xfd9d,0xfd9d,0xa02},
{0xfd9e,0xfd9e,0x40d3},
{0xfd9f,0xfd9f,0x200c},
{0xfda0,0xfda0,0x5083},
{0xfe40,0xfe40,0x1259},
{0xfe41,0xfe7e,0x5610},
{0xfe80,0xfea0,0x564e},
};

static pdf_cmap cmap_GBK_X = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "GBK-X",
	/* usecmap */ "", NULL,
	/* wmode */ 0,
	/* codespaces */ 0, {
		{ 0, 0, 0 },
	},
	4069, 4069, (pdf_range*)cmap_GBK_X_ranges,
	0, 0, NULL, /* xranges */
	0, 0, NULL, /* mranges */
	0, 0, NULL, /* table */
	0, 0, 0, NULL /* splay tree */
};
