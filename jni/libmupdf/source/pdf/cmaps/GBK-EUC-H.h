/* This is an automatically generated file. Do not edit. */

/* GBK-EUC-H */

static const pdf_range cmap_GBK_EUC_H_ranges[] = {
{0x20,0x20,0x1e24},
{0x21,0x7e,0x32e},
};

static pdf_cmap cmap_GBK_EUC_H = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "GBK-EUC-H",
	/* usecmap */ "GBK-X", NULL,
	/* wmode */ 0,
	/* codespaces */ 2, {
		{ 1, 0x00, 0x80 },
		{ 2, 0x8140, 0xfefe },
	},
	2, 2, (pdf_range*)cmap_GBK_EUC_H_ranges,
	0, 0, NULL, /* xranges */
	0, 0, NULL, /* mranges */
	0, 0, NULL, /* table */
	0, 0, 0, NULL /* splay tree */
};
