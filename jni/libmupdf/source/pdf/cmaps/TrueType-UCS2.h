/* This is an automatically generated file. Do not edit. */

/* TrueType-UCS2 */

static const pdf_range cmap_TrueType_UCS2_ranges[] = {
{0x0,0x0,0x0},
{0x1,0x1,0x0},
{0x2,0x2,0x0},
{0x3,0x61,0x20},
{0x62,0x63,0xc4},
{0x64,0x64,0xc7},
{0x65,0x65,0xc9},
{0x66,0x66,0xd1},
{0x67,0x67,0xd6},
{0x68,0x68,0xdc},
{0x69,0x69,0xe1},
{0x6a,0x6a,0xe0},
{0x6b,0x6b,0xe2},
{0x6c,0x6c,0xe4},
{0x6d,0x6d,0xe3},
{0x6e,0x6e,0xe5},
{0x6f,0x6f,0xe7},
{0x70,0x70,0xe9},
{0x71,0x71,0xe8},
{0x72,0x73,0xea},
{0x74,0x74,0xed},
{0x75,0x75,0xec},
{0x76,0x77,0xee},
{0x78,0x78,0xf1},
{0x79,0x79,0xf3},
{0x7a,0x7a,0xf2},
{0x7b,0x7b,0xf4},
{0x7c,0x7c,0xf6},
{0x7d,0x7d,0xf5},
{0x7e,0x7e,0xfa},
{0x7f,0x7f,0xf9},
{0x80,0x81,0xfb},
{0x82,0x82,0x2020},
{0x83,0x83,0xb0},
{0x84,0x85,0xa2},
{0x86,0x86,0xa7},
{0x87,0x87,0x2022},
{0x88,0x88,0xb6},
{0x89,0x89,0xdf},
{0x8a,0x8a,0xae},
{0x8b,0x8b,0xa9},
{0x8c,0x8c,0x2122},
{0x8d,0x8d,0xb4},
{0x8e,0x8e,0xa8},
{0x8f,0x8f,0x2260},
{0x90,0x90,0xc6},
{0x91,0x91,0xd8},
{0x92,0x92,0x221e},
{0x93,0x93,0xb1},
{0x94,0x95,0x2264},
{0x96,0x96,0xa5},
{0x97,0x97,0xb5},
{0x98,0x98,0x2202},
{0x99,0x99,0x2211},
{0x9a,0x9a,0x220f},
{0x9b,0x9b,0x3c0},
{0x9c,0x9c,0x222b},
{0x9d,0x9d,0xaa},
{0x9e,0x9e,0xba},
{0x9f,0x9f,0x2126},
{0xa0,0xa0,0xe6},
{0xa1,0xa1,0xf8},
{0xa2,0xa2,0xbf},
{0xa3,0xa3,0xa1},
{0xa4,0xa4,0xac},
{0xa5,0xa5,0x221a},
{0xa6,0xa6,0x192},
{0xa7,0xa7,0x2248},
{0xa8,0xa8,0x2206},
{0xa9,0xa9,0xab},
{0xaa,0xaa,0xbb},
{0xab,0xab,0x2026},
{0xac,0xac,0xa0},
{0xad,0xad,0xc0},
{0xae,0xae,0xc3},
{0xaf,0xaf,0xd5},
{0xb0,0xb1,0x152},
{0xb2,0xb3,0x2013},
{0xb4,0xb5,0x201c},
{0xb6,0xb7,0x2018},
{0xb8,0xb8,0xf7},
{0xb9,0xb9,0x25ca},
{0xba,0xba,0xff},
{0xbb,0xbb,0x178},
{0xbc,0xbc,0x2044},
{0xbd,0xbd,0xa4},
{0xbe,0xbf,0x2039},
{0xc0,0xc1,0xfb01},
{0xc2,0xc2,0x2021},
{0xc3,0xc3,0xb7},
{0xc4,0xc4,0x201a},
{0xc5,0xc5,0x201e},
{0xc6,0xc6,0x2030},
{0xc7,0xc7,0xc2},
{0xc8,0xc8,0xca},
{0xc9,0xc9,0xc1},
{0xca,0xca,0xcb},
{0xcb,0xcb,0xc8},
{0xcc,0xce,0xcd},
{0xcf,0xcf,0xcc},
{0xd0,0xd1,0xd3},
{0xd2,0xd2,0xf8ff},
{0xd3,0xd3,0xd2},
{0xd4,0xd5,0xda},
{0xd6,0xd6,0xd9},
{0xd7,0xd7,0x131},
{0xd8,0xd8,0x2c6},
{0xd9,0xd9,0x2dc},
{0xda,0xda,0xaf},
{0xdb,0xdd,0x2d8},
{0xde,0xde,0xb8},
{0xdf,0xdf,0x2dd},
{0xe0,0xe0,0x2db},
{0xe1,0xe1,0x2c7},
{0xe2,0xe3,0x141},
{0xe4,0xe5,0x160},
{0xe6,0xe7,0x17d},
{0xe8,0xe8,0xa6},
{0xe9,0xe9,0xd0},
{0xea,0xea,0xf0},
{0xeb,0xeb,0xdd},
{0xec,0xec,0xfd},
{0xed,0xed,0xde},
{0xee,0xee,0xfe},
{0xef,0xef,0x2212},
{0xf0,0xf0,0xd7},
{0xf1,0xf1,0xb9},
{0xf2,0xf3,0xb2},
{0xf4,0xf4,0xbd},
{0xf5,0xf5,0xbc},
{0xf6,0xf6,0xbe},
{0xf7,0xf7,0x20a3},
{0xf8,0xf9,0x11e},
{0xfa,0xfa,0x130},
{0xfb,0xfc,0x15e},
{0xfd,0xfe,0x106},
{0xff,0x100,0x10c},
{0x101,0x101,0x111},
};

static pdf_cmap cmap_TrueType_UCS2 = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "TrueType-UCS2",
	/* usecmap */ "", NULL,
	/* wmode */ 0,
	/* codespaces */ 0, {
		{ 0, 0, 0 },
	},
	138, 138, (pdf_range*)cmap_TrueType_UCS2_ranges,
	0, 0, NULL, /* xranges */
	0, 0, NULL, /* mranges */
	0, 0, NULL, /* table */
	0, 0, 0, NULL /* splay tree */
};
