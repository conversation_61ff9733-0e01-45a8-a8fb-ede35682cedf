/* This is an automatically generated file. Do not edit. */

/* HKscs-B5-H */

static const pdf_range cmap_HKscs_B5_H_ranges[] = {
{0x20,0x7e,0x1},
{0x8740,0x8765,0x4a15},
{0x8767,0x8779,0x4a3b},
{0x877a,0x877e,0x4a90},
{0x87a1,0x87df,0x4a95},
{0x8840,0x8855,0x44c9},
{0x8856,0x887e,0x4961},
{0x88a1,0x88a8,0x498a},
{0x88a9,0x88aa,0x499c},
{0x8940,0x8941,0x4534},
{0x8943,0x8943,0x4536},
{0x8946,0x8949,0x4537},
{0x894c,0x894c,0x453b},
{0x894d,0x894d,0x43c3},
{0x894e,0x8950,0x453c},
{0x8951,0x8951,0x439a},
{0x8952,0x897e,0x453f},
{0x89a1,0x89a5,0x456c},
{0x89a6,0x89a6,0x43a2},
{0x89ab,0x89ab,0x43ec},
{0x89ac,0x89ac,0x4571},
{0x89ad,0x89ad,0x43eb},
{0x89ae,0x89ae,0x4572},
{0x89b0,0x89b2,0x4573},
{0x89b5,0x89bf,0x4576},
{0x89c1,0x89c3,0x4581},
{0x89c5,0x89ce,0x4584},
{0x89cf,0x89cf,0x43bc},
{0x89d0,0x89d8,0x458e},
{0x89d9,0x89d9,0x439c},
{0x89da,0x89da,0x4597},
{0x89db,0x89db,0x439e},
{0x89dc,0x89dc,0x4598},
{0x89dd,0x89dd,0x439f},
{0x89de,0x89e0,0x4599},
{0x89e1,0x89e1,0x43a1},
{0x89e2,0x89e2,0x459c},
{0x89e3,0x89e3,0x43a3},
{0x89e4,0x89e9,0x459d},
{0x89ea,0x89eb,0x43a5},
{0x89ec,0x89f9,0x45a3},
{0x89fa,0x89fa,0x43a9},
{0x89fb,0x89fe,0x45b1},
{0x8a40,0x8a40,0x45b5},
{0x8a41,0x8a41,0x4309},
{0x8a43,0x8a4c,0x430b},
{0x8a4d,0x8a4d,0x45b6},
{0x8a4e,0x8a59,0x4316},
{0x8a5a,0x8a5a,0x45b7},
{0x8a5b,0x8a5d,0x4323},
{0x8a5e,0x8a5e,0x45b8},
{0x8a5f,0x8a62,0x4327},
{0x8a64,0x8a70,0x432c},
{0x8a71,0x8a71,0x45b9},
{0x8a72,0x8a74,0x433a},
{0x8a76,0x8a76,0x433e},
{0x8a77,0x8a77,0x45ba},
{0x8a78,0x8a79,0x4340},
{0x8a7a,0x8a7a,0x45bb},
{0x8a7b,0x8a7b,0x4343},
{0x8a7c,0x8a7c,0x45bc},
{0x8a7d,0x8a7d,0x4345},
{0x8a7e,0x8a7e,0x45bd},
{0x8aa1,0x8aa7,0x4347},
{0x8aa8,0x8aa8,0x45be},
{0x8aa9,0x8aaa,0x434f},
{0x8aac,0x8ab0,0x4352},
{0x8ab2,0x8ab5,0x4358},
{0x8ab6,0x8ab6,0x45bf},
{0x8ab7,0x8ab7,0x435d},
{0x8ab8,0x8ab8,0x45c0},
{0x8ab9,0x8ab9,0x435f},
{0x8abb,0x8ac7,0x4361},
{0x8ac9,0x8acb,0x436f},
{0x8acc,0x8acc,0x45c1},
{0x8ace,0x8ad5,0x4374},
{0x8ad6,0x8ad7,0x45c2},
{0x8ad8,0x8adc,0x437e},
{0x8adf,0x8ae5,0x4385},
{0x8ae6,0x8ae6,0x45c4},
{0x8ae7,0x8ae7,0x43db},
{0x8ae8,0x8af4,0x45c5},
{0x8af6,0x8afe,0x45d2},
{0x8b40,0x8b40,0x45db},
{0x8b41,0x8b42,0x438c},
{0x8b43,0x8b44,0x45dc},
{0x8b45,0x8b45,0x438e},
{0x8b46,0x8b46,0x45de},
{0x8b47,0x8b47,0x438f},
{0x8b48,0x8b48,0x45df},
{0x8b49,0x8b49,0x4390},
{0x8b4a,0x8b4a,0x45e0},
{0x8b4b,0x8b4b,0x4391},
{0x8b4c,0x8b4c,0x45e1},
{0x8b4d,0x8b50,0x4392},
{0x8b51,0x8b53,0x45e2},
{0x8b55,0x8b57,0x45e5},
{0x8b58,0x8b58,0x4397},
{0x8b59,0x8b59,0x45e8},
{0x8b5a,0x8b5a,0x4398},
{0x8b5b,0x8b5b,0x43c4},
{0x8b5c,0x8b60,0x45e9},
{0x8b61,0x8b61,0x43a7},
{0x8b62,0x8b67,0x45ee},
{0x8b68,0x8b68,0x43ac},
{0x8b69,0x8b7e,0x45f4},
{0x8ba1,0x8bbf,0x460a},
{0x8bc0,0x8bdc,0x44df},
{0x8bde,0x8bfd,0x44fc},
{0x8c40,0x8c61,0x49a1},
{0x8c62,0x8c62,0x4a4e},
{0x8c63,0x8c7e,0x49c3},
{0x8ca1,0x8ca5,0x49df},
{0x8ca7,0x8cc5,0x49e4},
{0x8cc9,0x8ccc,0x4a03},
{0x8cce,0x8cda,0x4a07},
{0x8cdb,0x8cdb,0x4a4f},
{0x8cdc,0x8cdc,0x4a14},
{0x8cdd,0x8ce4,0x4a50},
{0x8ce6,0x8cfe,0x4a58},
{0x8d40,0x8d40,0x4a71},
{0x8d42,0x8d5f,0x4a72},
{0x8d60,0x8d61,0x4629},
{0x8d62,0x8d62,0x43ba},
{0x8d63,0x8d67,0x462b},
{0x8d68,0x8d68,0x43bb},
{0x8d69,0x8d69,0x43a0},
{0x8d6a,0x8d6a,0x43bd},
{0x8d6b,0x8d6d,0x4630},
{0x8d6e,0x8d6e,0x43be},
{0x8d6f,0x8d75,0x4633},
{0x8d76,0x8d76,0x43bf},
{0x8d77,0x8d79,0x463a},
{0x8d7a,0x8d7a,0x43c0},
{0x8d7b,0x8d7b,0x463d},
{0x8d7c,0x8d7c,0x43c1},
{0x8d7d,0x8d7e,0x463e},
{0x8da1,0x8da4,0x4640},
{0x8da5,0x8da5,0x43c2},
{0x8da6,0x8da7,0x4644},
{0x8da8,0x8da8,0x43b9},
{0x8da9,0x8da9,0x43ad},
{0x8daa,0x8db5,0x4646},
{0x8db6,0x8db6,0x43c7},
{0x8db7,0x8dc2,0x4652},
{0x8dc3,0x8dc3,0x43c8},
{0x8dc4,0x8df9,0x465e},
{0x8dfa,0x8dfa,0x43f9},
{0x8dfb,0x8dfe,0x4694},
{0x8e40,0x8e44,0x372b},
{0x8e45,0x8e45,0x4698},
{0x8e46,0x8e68,0x3730},
{0x8e69,0x8e69,0x1055},
{0x8e6a,0x8e6a,0x3754},
{0x8e6b,0x8e6c,0x4699},
{0x8e6d,0x8e6e,0x3756},
{0x8e6f,0x8e6f,0x2de8},
{0x8e70,0x8e75,0x3759},
{0x8e76,0x8e76,0x469b},
{0x8e77,0x8e7a,0x375f},
{0x8e7b,0x8e7b,0x469c},
{0x8e7c,0x8e7d,0x3764},
{0x8e7e,0x8e7e,0x121},
{0x8ea1,0x8ea5,0x3766},
{0x8ea6,0x8ea6,0x469d},
{0x8ea7,0x8eaa,0x376b},
{0x8eab,0x8eab,0x106b},
{0x8eac,0x8eb3,0x3770},
{0x8eb4,0x8eb4,0x1326},
{0x8eb5,0x8eb7,0x3779},
{0x8eb8,0x8eb8,0x469e},
{0x8eb9,0x8ec8,0x377d},
{0x8ec9,0x8ec9,0x469f},
{0x8eca,0x8ecc,0x378d},
{0x8ecd,0x8ecd,0x66b},
{0x8ece,0x8ecf,0x3791},
{0x8ed0,0x8ed0,0x132e},
{0x8ed1,0x8ee4,0x3794},
{0x8ee5,0x8ee5,0x46a0},
{0x8ee6,0x8eee,0x37a8},
{0x8eef,0x8eef,0x46a1},
{0x8ef0,0x8ef5,0x37b1},
{0x8ef6,0x8ef6,0x46a2},
{0x8ef7,0x8efe,0x37b8},
{0x8f40,0x8f56,0x37c0},
{0x8f57,0x8f57,0xd35},
{0x8f58,0x8f58,0x37d8},
{0x8f59,0x8f59,0x46a3},
{0x8f5a,0x8f5e,0x37d9},
{0x8f5f,0x8f5f,0x46a4},
{0x8f60,0x8f66,0x37de},
{0x8f67,0x8f67,0x46a5},
{0x8f68,0x8f68,0x37e5},
{0x8f69,0x8f69,0x27c2},
{0x8f6a,0x8f6d,0x37e7},
{0x8f6e,0x8f6e,0x22ad},
{0x8f6f,0x8f78,0x37ec},
{0x8f79,0x8f79,0x46a6},
{0x8f7a,0x8f7e,0x37f7},
{0x8fa1,0x8faf,0x37fc},
{0x8fb0,0x8fb0,0x46a7},
{0x8fb1,0x8fc4,0x380c},
{0x8fc5,0x8fc5,0x46a8},
{0x8fc6,0x8fc6,0x3820},
{0x8fc7,0x8fc7,0x46a9},
{0x8fc8,0x8fc9,0x3821},
{0x8fca,0x8fca,0x46aa},
{0x8fcb,0x8fcb,0x134c},
{0x8fcc,0x8fcc,0x3ff9},
{0x8fcd,0x8fd9,0x3826},
{0x8fda,0x8fda,0x46ab},
{0x8fdb,0x8fe2,0x3833},
{0x8fe3,0x8fe3,0x46ac},
{0x8fe4,0x8ffb,0x383c},
{0x8ffc,0x8ffc,0x46ad},
{0x8ffd,0x8ffd,0x3854},
{0x8ffe,0x8ffe,0x9ce},
{0x9040,0x9054,0x3856},
{0x9055,0x9055,0x46ae},
{0x9056,0x905b,0x386c},
{0x905c,0x905e,0x46af},
{0x905f,0x906c,0x3873},
{0x906d,0x906d,0xbc7},
{0x906e,0x906e,0x3882},
{0x906f,0x906f,0x46b2},
{0x9070,0x9079,0x3883},
{0x907a,0x907a,0x36e9},
{0x907b,0x907e,0x388d},
{0x90a1,0x90a5,0x3891},
{0x90a6,0x90a6,0x46b3},
{0x90a7,0x90b7,0x3896},
{0x90b8,0x90b8,0x46b4},
{0x90b9,0x90db,0x38a7},
{0x90dc,0x90dc,0x1391},
{0x90dd,0x90f0,0x38cb},
{0x90f1,0x90f1,0x16a4},
{0x90f2,0x90fe,0x38e0},
{0x9140,0x9164,0x38ed},
{0x9165,0x9165,0x46b5},
{0x9166,0x916d,0x3912},
{0x916e,0x916e,0x46b6},
{0x916f,0x917d,0x391a},
{0x917e,0x917e,0x46b7},
{0x91a1,0x91a1,0x3929},
{0x91a2,0x91a2,0x46b8},
{0x91a3,0x91be,0x392a},
{0x91bf,0x91bf,0x3072},
{0x91c0,0x91c7,0x3947},
{0x91c8,0x91c8,0x46b9},
{0x91c9,0x91fe,0x3950},
{0x9240,0x9243,0x3986},
{0x9244,0x9244,0x3988},
{0x9245,0x9263,0x398b},
{0x9264,0x9264,0x46ba},
{0x9265,0x926c,0x39ab},
{0x926d,0x926d,0x46bb},
{0x926e,0x927e,0x39b4},
{0x92a1,0x92ae,0x39c5},
{0x92af,0x92b0,0x119},
{0x92b1,0x92b1,0x11c},
{0x92b2,0x92b2,0x11b},
{0x92b3,0x92c7,0x39d3},
{0x92c8,0x92c8,0x3fac},
{0x92c9,0x92d0,0x39e9},
{0x92d1,0x92d1,0x297c},
{0x92d2,0x92e4,0x39f2},
{0x92e5,0x92e5,0x46bc},
{0x92e6,0x92f1,0x3a05},
{0x92f2,0x92f2,0x46bd},
{0x92f3,0x92fe,0x3a11},
{0x9340,0x9367,0x3a1d},
{0x9368,0x9368,0x46be},
{0x9369,0x937e,0x3a45},
{0x93a1,0x93a9,0x3a5b},
{0x93aa,0x93aa,0x46bf},
{0x93ab,0x93c1,0x3a64},
{0x93c2,0x93c2,0x46c0},
{0x93c3,0x93e4,0x3a7b},
{0x93e5,0x93e5,0x46c1},
{0x93e6,0x93e7,0x3a9d},
{0x93e8,0x93e8,0x46c2},
{0x93e9,0x93ea,0x3aa0},
{0x93eb,0x93eb,0x46c3},
{0x93ec,0x93fe,0x3aa2},
{0x9440,0x9445,0x3ab5},
{0x9446,0x9446,0x46c4},
{0x9447,0x9447,0x1d06},
{0x9448,0x9478,0x3abc},
{0x9479,0x9479,0x46c5},
{0x947a,0x947e,0x3aee},
{0x94a1,0x94c9,0x3af3},
{0x94ca,0x94ca,0x29a1},
{0x94cb,0x94cb,0x46c6},
{0x94cc,0x94fe,0x3b1e},
{0x9540,0x954c,0x3b51},
{0x954d,0x954d,0x46c7},
{0x954e,0x9559,0x3b5e},
{0x955a,0x955a,0x46c8},
{0x955b,0x955e,0x3b6a},
{0x955f,0x955f,0x46c9},
{0x9560,0x957e,0x3b6f},
{0x95a1,0x95c5,0x3b8e},
{0x95c6,0x95c6,0x46ca},
{0x95c7,0x95d8,0x3bb3},
{0x95d9,0x95d9,0x181b},
{0x95da,0x95fe,0x3bc6},
{0x9640,0x9643,0x3beb},
{0x9644,0x9644,0x3e2f},
{0x9645,0x9650,0x3bf0},
{0x9651,0x9651,0x46cb},
{0x9652,0x9669,0x3bfd},
{0x966a,0x966a,0x46cc},
{0x966b,0x967e,0x3c16},
{0x96a1,0x96d3,0x3c2a},
{0x96d4,0x96d4,0x46cd},
{0x96d5,0x96ec,0x3c5d},
{0x96ed,0x96ed,0x3c76},
{0x96ee,0x96fb,0x3c76},
{0x96fc,0x96fc,0x2b24},
{0x96fd,0x96fe,0x3c85},
{0x9740,0x977e,0x3c87},
{0x97a1,0x97fe,0x3cc6},
{0x9840,0x9843,0x3d24},
{0x9844,0x9845,0x46ce},
{0x9846,0x986e,0x3d2a},
{0x986f,0x986f,0x46d0},
{0x9870,0x9874,0x3d54},
{0x9875,0x9876,0x46d1},
{0x9877,0x9877,0x3d59},
{0x9878,0x9879,0x46d3},
{0x987a,0x987a,0x3d5a},
{0x987b,0x987e,0x46d5},
{0x98a1,0x98a2,0x46d9},
{0x98a3,0x98a3,0x3d5b},
{0x98a4,0x98ae,0x46db},
{0x98af,0x98af,0x3d5c},
{0x98b0,0x98b3,0x46e6},
{0x98b4,0x98b4,0x43ca},
{0x98b5,0x98b5,0x46ea},
{0x98b6,0x98b6,0x3d5d},
{0x98b7,0x98b7,0x46eb},
{0x98b8,0x98b8,0x43cc},
{0x98b9,0x98b9,0x3d5e},
{0x98ba,0x98ba,0x46ec},
{0x98bb,0x98bb,0x43fa},
{0x98bc,0x98bc,0x46ed},
{0x98bd,0x98be,0x3d5f},
{0x98bf,0x98c1,0x46ee},
{0x98c2,0x98c2,0x3d61},
{0x98c3,0x98c3,0x46f1},
{0x98c4,0x98c4,0x3d62},
{0x98c5,0x98c5,0x46f2},
{0x98c6,0x98c7,0x3d63},
{0x98c8,0x98d1,0x46f3},
{0x98d2,0x98d2,0x43cd},
{0x98d3,0x98d7,0x46fd},
{0x98d8,0x98d9,0x43ce},
{0x98da,0x98da,0x4702},
{0x98db,0x98db,0x43d1},
{0x98dc,0x98de,0x4703},
{0x98df,0x98df,0x43d4},
{0x98e0,0x98e2,0x4706},
{0x98e3,0x98e3,0x3d65},
{0x98e4,0x98e6,0x4709},
{0x98e7,0x98e7,0x3d66},
{0x98e8,0x98ec,0x470c},
{0x98ed,0x98ed,0x3d67},
{0x98ee,0x98ef,0x4711},
{0x98f0,0x98f0,0x3d68},
{0x98f1,0x98f1,0x4713},
{0x98f2,0x98f2,0x3d69},
{0x98f3,0x98f3,0x4714},
{0x98f4,0x98f5,0x43d5},
{0x98f6,0x98fb,0x4715},
{0x98fc,0x98fc,0x3d6a},
{0x98fd,0x98fd,0x471b},
{0x98fe,0x98fe,0x43d7},
{0x9940,0x9941,0x471c},
{0x9942,0x9942,0x43fc},
{0x9943,0x9943,0x3d6b},
{0x9944,0x9944,0x471e},
{0x9945,0x9945,0x3d6c},
{0x9946,0x9946,0x471f},
{0x9947,0x9947,0x43d8},
{0x9948,0x994e,0x4720},
{0x994f,0x994f,0x3d6d},
{0x9950,0x9953,0x4727},
{0x9954,0x9954,0x43d9},
{0x9955,0x995b,0x472b},
{0x995c,0x995c,0x43da},
{0x995d,0x9963,0x4732},
{0x9964,0x9964,0x43dc},
{0x9965,0x9969,0x4739},
{0x996a,0x996a,0x3d6e},
{0x996b,0x996d,0x473e},
{0x996e,0x996e,0x3d6f},
{0x996f,0x9974,0x4741},
{0x9975,0x9975,0x3d70},
{0x9976,0x9977,0x4747},
{0x9978,0x9978,0x3d71},
{0x9979,0x997e,0x4749},
{0x99a1,0x99a1,0x474f},
{0x99a2,0x99a2,0x3d72},
{0x99a3,0x99a3,0x4750},
{0x99a4,0x99a4,0x43c5},
{0x99a5,0x99a5,0x4751},
{0x99a6,0x99a6,0x43c6},
{0x99a7,0x99ad,0x4752},
{0x99ae,0x99ae,0x3d73},
{0x99af,0x99b1,0x4759},
{0x99b2,0x99b2,0x43de},
{0x99b3,0x99b5,0x475c},
{0x99b6,0x99b6,0x3d74},
{0x99b7,0x99b9,0x475f},
{0x99ba,0x99ba,0x3d75},
{0x99bb,0x99c9,0x4762},
{0x99ca,0x99ca,0x43e0},
{0x99cb,0x99cc,0x4771},
{0x99cd,0x99cd,0x43e2},
{0x99ce,0x99d2,0x4773},
{0x99d3,0x99d3,0x43e3},
{0x99d4,0x99d5,0x4778},
{0x99d6,0x99d6,0x43e5},
{0x99d7,0x99de,0x477a},
{0x99df,0x99df,0x43df},
{0x99e0,0x99e1,0x4782},
{0x99e2,0x99e2,0x3d76},
{0x99e3,0x99e3,0x4784},
{0x99e4,0x99e4,0x43ab},
{0x99e5,0x99e5,0x4785},
{0x99e6,0x99e6,0x43e7},
{0x99e7,0x99e7,0x4786},
{0x99e8,0x99e8,0x43e9},
{0x99e9,0x99ee,0x4787},
{0x99ef,0x99ef,0x43fd},
{0x99f0,0x99f3,0x478d},
{0x99f4,0x99f4,0x3d77},
{0x99f5,0x99fe,0x4791},
{0x9a40,0x9a49,0x479b},
{0x9a4a,0x9a4a,0x3d78},
{0x9a4b,0x9a4b,0x47a5},
{0x9a4c,0x9a4c,0x3d79},
{0x9a4d,0x9a58,0x47a6},
{0x9a59,0x9a59,0x3d7a},
{0x9a5a,0x9a5e,0x47b2},
{0x9a5f,0x9a5f,0x43af},
{0x9a60,0x9a60,0x47b7},
{0x9a61,0x9a61,0x3d7b},
{0x9a62,0x9a65,0x47b8},
{0x9a66,0x9a66,0x43ed},
{0x9a67,0x9a67,0x47bc},
{0x9a68,0x9a68,0x3d7c},
{0x9a69,0x9a69,0x43ee},
{0x9a6a,0x9a6a,0x47bd},
{0x9a6b,0x9a6b,0x43ff},
{0x9a6c,0x9a72,0x47be},
{0x9a73,0x9a73,0x3d7d},
{0x9a74,0x9a74,0x47c5},
{0x9a75,0x9a75,0x43f1},
{0x9a76,0x9a7d,0x47c6},
{0x9a7e,0x9a7e,0x3d7e},
{0x9aa1,0x9aa2,0x47ce},
{0x9aa3,0x9aa3,0x43f3},
{0x9aa4,0x9aa4,0x47d0},
{0x9aa5,0x9aa5,0x43f2},
{0x9aa6,0x9aa8,0x47d1},
{0x9aa9,0x9aa9,0x43f8},
{0x9aaa,0x9aaa,0x43f4},
{0x9aab,0x9ab1,0x47d4},
{0x9ab2,0x9ab2,0x3d7f},
{0x9ab3,0x9ab6,0x47db},
{0x9ab7,0x9ab7,0x3d80},
{0x9ab8,0x9ab8,0x47df},
{0x9ab9,0x9ab9,0x3d81},
{0x9aba,0x9aba,0x47e0},
{0x9abb,0x9abb,0x3d82},
{0x9abc,0x9abc,0x47e1},
{0x9abd,0x9abd,0x43b7},
{0x9abe,0x9ac6,0x47e2},
{0x9ac7,0x9ac7,0x3d83},
{0x9ac8,0x9acf,0x47eb},
{0x9ad0,0x9ad0,0x3d84},
{0x9ad1,0x9ad1,0x47f3},
{0x9ad2,0x9ad2,0x3d85},
{0x9ad3,0x9ad8,0x47f4},
{0x9ad9,0x9adb,0x3d86},
{0x9adc,0x9ae1,0x47fa},
{0x9ae2,0x9ae2,0x3d89},
{0x9ae3,0x9ae3,0x4800},
{0x9ae4,0x9ae4,0x3d8a},
{0x9ae5,0x9ae7,0x4801},
{0x9ae8,0x9ae8,0x3d8b},
{0x9ae9,0x9ae9,0x43b0},
{0x9aea,0x9aed,0x4804},
{0x9aee,0x9aee,0x43b2},
{0x9aef,0x9af1,0x4808},
{0x9af2,0x9af2,0x3d8c},
{0x9af3,0x9af5,0x480b},
{0x9af6,0x9af6,0x3d8d},
{0x9af7,0x9afa,0x480e},
{0x9afb,0x9afb,0x3d8e},
{0x9afc,0x9afe,0x4812},
{0x9b40,0x9b45,0x4815},
{0x9b46,0x9b46,0x3d8f},
{0x9b47,0x9b49,0x481b},
{0x9b4a,0x9b4a,0x3d90},
{0x9b4b,0x9b53,0x481e},
{0x9b54,0x9b54,0x3d92},
{0x9b55,0x9b57,0x4827},
{0x9b58,0x9b58,0x3d93},
{0x9b59,0x9b59,0x482a},
{0x9b5a,0x9b5a,0x3d94},
{0x9b5b,0x9b5b,0x482b},
{0x9b5c,0x9b5c,0x3d95},
{0x9b5d,0x9b5d,0x482c},
{0x9b5e,0x9b5f,0x3d96},
{0x9b60,0x9b60,0x482d},
{0x9b62,0x9b6f,0x482e},
{0x9b70,0x9b73,0x3d98},
{0x9b74,0x9b75,0x483c},
{0x9b76,0x9b76,0x2f50},
{0x9b77,0x9b77,0x3d9d},
{0x9b78,0x9b78,0x1725},
{0x9b79,0x9b7a,0x483e},
{0x9b7b,0x9b7b,0x32ed},
{0x9b7c,0x9b7c,0x3da0},
{0x9b7d,0x9b7d,0x4840},
{0x9b7e,0x9b7e,0x3da1},
{0x9ba1,0x9ba1,0x3da2},
{0x9ba2,0x9ba2,0x4841},
{0x9ba3,0x9ba4,0x3da3},
{0x9ba5,0x9ba6,0x4842},
{0x9ba7,0x9baa,0x3da5},
{0x9bab,0x9bab,0x4844},
{0x9bac,0x9bac,0x3da9},
{0x9bad,0x9bae,0x4845},
{0x9baf,0x9baf,0x3daa},
{0x9bb0,0x9bb1,0x4847},
{0x9bb2,0x9bb9,0x3dab},
{0x9bba,0x9bbd,0x4849},
{0x9bbe,0x9bbe,0x3db3},
{0x9bbf,0x9bbf,0x484d},
{0x9bc0,0x9bc5,0x3db4},
{0x9bc6,0x9bc6,0x2ad9},
{0x9bc7,0x9bc9,0x484e},
{0x9bca,0x9bca,0x3dbb},
{0x9bcb,0x9bcb,0x4851},
{0x9bcc,0x9bcc,0x3dbc},
{0x9bcd,0x9bcd,0x4852},
{0x9bce,0x9bce,0x43d0},
{0x9bcf,0x9bcf,0x4853},
{0x9bd0,0x9bd1,0x3dbd},
{0x9bd2,0x9bd2,0x4854},
{0x9bd3,0x9bd3,0x3dbf},
{0x9bd4,0x9bd4,0x4855},
{0x9bd5,0x9bd5,0x3dc0},
{0x9bd6,0x9bd7,0x4856},
{0x9bd8,0x9bda,0x3dc1},
{0x9bdb,0x9bdc,0x4858},
{0x9bdd,0x9bdd,0x3dc4},
{0x9bde,0x9bde,0x1c14},
{0x9bdf,0x9bdf,0x3dc5},
{0x9be0,0x9be0,0x485a},
{0x9be1,0x9be1,0x3dc6},
{0x9be2,0x9be2,0x485b},
{0x9be3,0x9be3,0x3dc7},
{0x9be4,0x9be6,0x485c},
{0x9be7,0x9be7,0x3dc8},
{0x9be8,0x9be8,0x485f},
{0x9be9,0x9beb,0x3dc9},
{0x9bec,0x9bec,0x41fa},
{0x9bed,0x9bed,0x4860},
{0x9bee,0x9bef,0x3dcd},
{0x9bf0,0x9bf2,0x4861},
{0x9bf3,0x9bf3,0x3dcf},
{0x9bf4,0x9bf5,0x4864},
{0x9bf6,0x9bf6,0x12e9},
{0x9bf7,0x9bf7,0x4866},
{0x9bf8,0x9bf9,0x3dd1},
{0x9bfa,0x9bfa,0x4867},
{0x9bfb,0x9bfc,0x3dd3},
{0x9bfd,0x9bfe,0x4868},
{0x9c40,0x9c41,0x3dd5},
{0x9c42,0x9c42,0x2cae},
{0x9c43,0x9c43,0x486a},
{0x9c44,0x9c46,0x3dd8},
{0x9c47,0x9c47,0x486b},
{0x9c48,0x9c48,0x3ddb},
{0x9c49,0x9c49,0x486c},
{0x9c4a,0x9c4a,0x3ddc},
{0x9c4b,0x9c4c,0x486d},
{0x9c4d,0x9c52,0x3ddd},
{0x9c53,0x9c53,0x1a64},
{0x9c54,0x9c54,0x486f},
{0x9c55,0x9c55,0x3de4},
{0x9c56,0x9c56,0x4870},
{0x9c57,0x9c5b,0x3de5},
{0x9c5c,0x9c5c,0x4871},
{0x9c5d,0x9c5d,0x3dea},
{0x9c5e,0x9c5f,0x4872},
{0x9c60,0x9c60,0x3deb},
{0x9c61,0x9c61,0x4874},
{0x9c62,0x9c62,0x1404},
{0x9c63,0x9c63,0x4875},
{0x9c64,0x9c66,0x3ded},
{0x9c67,0x9c67,0x4876},
{0x9c68,0x9c68,0x2324},
{0x9c69,0x9c69,0x4877},
{0x9c6a,0x9c6a,0x3df1},
{0x9c6b,0x9c6b,0x346a},
{0x9c6c,0x9c6c,0x4878},
{0x9c6d,0x9c6d,0x3df2},
{0x9c6e,0x9c6e,0x4879},
{0x9c6f,0x9c72,0x3df3},
{0x9c73,0x9c74,0x487a},
{0x9c75,0x9c76,0x3df7},
{0x9c77,0x9c77,0x2291},
{0x9c78,0x9c78,0x487c},
{0x9c79,0x9c79,0x3dfa},
{0x9c7a,0x9c7a,0x487d},
{0x9c7b,0x9c7c,0x3dfb},
{0x9c7d,0x9c7d,0x487e},
{0x9c7e,0x9c7e,0x3dfd},
{0x9ca1,0x9ca2,0x3dfe},
{0x9ca3,0x9ca4,0x487f},
{0x9ca5,0x9ca5,0x3e00},
{0x9ca6,0x9ca7,0x4881},
{0x9ca8,0x9ca9,0x3e01},
{0x9caa,0x9caa,0x4883},
{0x9cab,0x9cab,0x3e03},
{0x9cac,0x9cac,0x4884},
{0x9cad,0x9cae,0x3e04},
{0x9caf,0x9cb0,0x4885},
{0x9cb1,0x9cba,0x3e06},
{0x9cbb,0x9cbb,0x4887},
{0x9cbc,0x9cbc,0x1787},
{0x9cbd,0x9cbd,0x95f},
{0x9cbe,0x9cc2,0x3e12},
{0x9cc3,0x9cc5,0x4888},
{0x9cc6,0x9ccd,0x3e17},
{0x9cce,0x9cce,0x488b},
{0x9ccf,0x9ccf,0x3e1f},
{0x9cd0,0x9cd0,0x1e99},
{0x9cd1,0x9cd3,0x3e21},
{0x9cd4,0x9cd7,0x488c},
{0x9cd8,0x9cda,0x3e24},
{0x9cdb,0x9cdb,0x4890},
{0x9cdc,0x9ce5,0x3e27},
{0x9ce6,0x9ce6,0x4891},
{0x9ce7,0x9ce9,0x3e31},
{0x9cea,0x9cea,0x4892},
{0x9ceb,0x9cec,0x3e34},
{0x9ced,0x9ced,0x4893},
{0x9cee,0x9cf9,0x3e36},
{0x9cfa,0x9cfc,0x4894},
{0x9cfd,0x9cfd,0x3e42},
{0x9cfe,0x9cfe,0x4897},
{0x9d40,0x9d40,0x43e8},
{0x9d41,0x9d45,0x4898},
{0x9d46,0x9d46,0x3e43},
{0x9d47,0x9d48,0x489d},
{0x9d49,0x9d49,0x3e44},
{0x9d4a,0x9d4b,0x489f},
{0x9d4c,0x9d4d,0x3e46},
{0x9d4e,0x9d4e,0x48a1},
{0x9d4f,0x9d4f,0x3e48},
{0x9d50,0x9d50,0x48a2},
{0x9d51,0x9d51,0x3e49},
{0x9d52,0x9d54,0x48a3},
{0x9d55,0x9d55,0x3e4a},
{0x9d56,0x9d56,0x48a6},
{0x9d57,0x9d57,0x25c1},
{0x9d58,0x9d59,0x48a7},
{0x9d5a,0x9d5a,0xd0c},
{0x9d5b,0x9d60,0x48a9},
{0x9d61,0x9d61,0x43c9},
{0x9d62,0x9d62,0x3e4c},
{0x9d63,0x9d63,0x48af},
{0x9d64,0x9d64,0x3e4d},
{0x9d65,0x9d77,0x48b0},
{0x9d78,0x9d78,0x43f5},
{0x9d79,0x9d79,0x3e4e},
{0x9d7a,0x9d7d,0x48c3},
{0x9d7e,0x9d7e,0x3e4f},
{0x9da1,0x9da4,0x48c7},
{0x9da5,0x9da8,0x3e50},
{0x9da9,0x9da9,0x48cb},
{0x9daa,0x9daa,0x3e54},
{0x9dab,0x9dab,0x48cc},
{0x9dac,0x9dad,0x3e55},
{0x9dae,0x9daf,0x48cd},
{0x9db0,0x9db0,0x3e58},
{0x9db1,0x9db2,0x48cf},
{0x9db3,0x9db3,0x3e59},
{0x9db4,0x9db4,0x48d1},
{0x9db5,0x9db5,0x3e5a},
{0x9db6,0x9db6,0x48d2},
{0x9db7,0x9db7,0x3e5b},
{0x9db8,0x9dbb,0x48d3},
{0x9dbc,0x9dbd,0x3e5c},
{0x9dbe,0x9dbe,0x48d7},
{0x9dbf,0x9dc0,0x3e5e},
{0x9dc1,0x9dc2,0x48d8},
{0x9dc3,0x9dc3,0x3e60},
{0x9dc4,0x9dc4,0x5e6},
{0x9dc5,0x9dc6,0x48da},
{0x9dc7,0x9dc8,0x3e62},
{0x9dc9,0x9dc9,0x48dc},
{0x9dca,0x9dca,0x3e64},
{0x9dcb,0x9dcc,0x48dd},
{0x9dcd,0x9dd1,0x3e65},
{0x9dd2,0x9dd2,0x48df},
{0x9dd3,0x9dd5,0x3e6a},
{0x9dd6,0x9dd9,0x48e0},
{0x9dda,0x9dfb,0x3e6d},
{0x9dfc,0x9dfc,0x48e4},
{0x9dfd,0x9dfe,0x3e8f},
{0x9e40,0x9e42,0x3e91},
{0x9e43,0x9e43,0x48e5},
{0x9e44,0x9e5e,0x3e95},
{0x9e5f,0x9e5f,0x48e6},
{0x9e60,0x9e62,0x3eb1},
{0x9e63,0x9e63,0x48e7},
{0x9e64,0x9e65,0x3eb4},
{0x9e66,0x9e67,0x48e8},
{0x9e68,0x9e68,0x3eb6},
{0x9e69,0x9e69,0x48ea},
{0x9e6a,0x9e6a,0x3eb7},
{0x9e6b,0x9e70,0x48eb},
{0x9e71,0x9e71,0x3eb8},
{0x9e72,0x9e72,0x48f1},
{0x9e73,0x9e73,0x3eb9},
{0x9e74,0x9e76,0x48f2},
{0x9e77,0x9e78,0x3eba},
{0x9e79,0x9e79,0x48f5},
{0x9e7a,0x9e7a,0x3ebc},
{0x9e7b,0x9e7b,0x48f6},
{0x9e7c,0x9e7c,0x3ebd},
{0x9e7d,0x9e7d,0x48f7},
{0x9e7e,0x9e7e,0x3ebe},
{0x9ea1,0x9ea2,0x3ebf},
{0x9ea3,0x9ea3,0x48f8},
{0x9ea4,0x9ea6,0x3ec1},
{0x9ea7,0x9ea8,0x48f9},
{0x9ea9,0x9ea9,0x728},
{0x9eaa,0x9eaa,0x3ec5},
{0x9eab,0x9eab,0x48fb},
{0x9eac,0x9ead,0x3ec6},
{0x9eae,0x9eae,0x48fc},
{0x9eaf,0x9eb1,0x3ec8},
{0x9eb2,0x9eb3,0x48fd},
{0x9eb4,0x9eb4,0x3ecb},
{0x9eb5,0x9eb5,0x48ff},
{0x9eb6,0x9eb7,0x3ecc},
{0x9eb8,0x9eb8,0x4900},
{0x9eb9,0x9eb9,0x3ece},
{0x9eba,0x9ebb,0x4901},
{0x9ebc,0x9ebc,0x3ecf},
{0x9ebd,0x9ebe,0x4903},
{0x9ebf,0x9ec0,0x3ed0},
{0x9ec1,0x9ec3,0x4905},
{0x9ec4,0x9ec5,0x3ed2},
{0x9ec6,0x9ec6,0x4908},
{0x9ec7,0x9eca,0x3ed4},
{0x9ecb,0x9ecc,0x4909},
{0x9ecd,0x9ecd,0x3ed9},
{0x9ece,0x9ecf,0x490b},
{0x9ed0,0x9ed1,0x3eda},
{0x9ed2,0x9ed2,0x490d},
{0x9ed3,0x9ed3,0x3edc},
{0x9ed4,0x9ed5,0x490e},
{0x9ed6,0x9ed7,0x3edd},
{0x9ed8,0x9ed9,0x4910},
{0x9eda,0x9eee,0x3edf},
{0x9eef,0x9eef,0x24b6},
{0x9ef0,0x9ef1,0x3ef5},
{0x9ef2,0x9ef2,0x4912},
{0x9ef3,0x9ef5,0x3ef7},
{0x9ef6,0x9ef8,0x4913},
{0x9ef9,0x9efa,0x3efa},
{0x9efb,0x9efb,0x4916},
{0x9efc,0x9efc,0x3efc},
{0x9efd,0x9efd,0x1806},
{0x9efe,0x9efe,0x3efe},
{0x9f40,0x9f42,0x3eff},
{0x9f43,0x9f43,0x4917},
{0x9f44,0x9f47,0x3f02},
{0x9f48,0x9f48,0x4918},
{0x9f49,0x9f4a,0x3f06},
{0x9f4b,0x9f4c,0x4919},
{0x9f4d,0x9f5f,0x3f08},
{0x9f60,0x9f60,0x3511},
{0x9f61,0x9f65,0x3f1c},
{0x9f66,0x9f66,0x3945},
{0x9f67,0x9f68,0x491b},
{0x9f69,0x9f6f,0x3f23},
{0x9f70,0x9f70,0x491d},
{0x9f71,0x9f7e,0x3f2a},
{0x9fa1,0x9fb4,0x3f38},
{0x9fb5,0x9fb5,0x491e},
{0x9fb6,0x9fba,0x3f4c},
{0x9fbb,0x9fbb,0x491f},
{0x9fbc,0x9fbe,0x3f51},
{0x9fbf,0x9fbf,0x4920},
{0x9fc0,0x9fc0,0x3f54},
{0x9fc1,0x9fc1,0x4921},
{0x9fc2,0x9fca,0x3f55},
{0x9fcb,0x9fcb,0xf82},
{0x9fcc,0x9fcc,0x4922},
{0x9fcd,0x9fd3,0x3f60},
{0x9fd4,0x9fd4,0x4923},
{0x9fd5,0x9fd7,0x3f68},
{0x9fd8,0x9fd8,0x3a3e},
{0x9fd9,0x9fe3,0x3f6c},
{0x9fe4,0x9fe4,0x4924},
{0x9fe5,0x9ff8,0x3f77},
{0x9ff9,0x9ff9,0x4925},
{0x9ffa,0x9ffe,0x3f8b},
{0xa040,0xa040,0x4926},
{0xa041,0xa046,0x3f90},
{0xa047,0xa047,0x4927},
{0xa048,0xa054,0x3f96},
{0xa055,0xa055,0x4928},
{0xa056,0xa062,0x3fa3},
{0xa063,0xa063,0x3811},
{0xa064,0xa06c,0x3fb1},
{0xa06d,0xa06d,0x4929},
{0xa06e,0xa076,0x3fba},
{0xa077,0xa077,0x5f2},
{0xa078,0xa07a,0x3fc4},
{0xa07b,0xa07b,0x492a},
{0xa07c,0xa07e,0x3fc7},
{0xa0a1,0xa0a1,0x3fca},
{0xa0a2,0xa0a2,0x492b},
{0xa0a3,0xa0a6,0x3fcb},
{0xa0a7,0xa0a7,0x492c},
{0xa0a8,0xa0c4,0x3fcf},
{0xa0c5,0xa0c5,0x492d},
{0xa0c6,0xa0cf,0x3fec},
{0xa0d0,0xa0d0,0x492e},
{0xa0d1,0xa0d4,0x3ff6},
{0xa0d5,0xa0d5,0x3aee},
{0xa0d6,0xa0de,0x3ffb},
{0xa0df,0xa0df,0x247d},
{0xa0e0,0xa0e2,0x4005},
{0xa0e3,0xa0e3,0x492f},
{0xa0e4,0xa0e4,0x3ac9},
{0xa0e5,0xa0e6,0x4009},
{0xa0e7,0xa0ed,0x4930},
{0xa0ee,0xa0ee,0x43b4},
{0xa0ef,0xa0f1,0x4937},
{0xa0f2,0xa0f2,0x43b8},
{0xa0f3,0xa0fe,0x493a},
{0xa140,0xa158,0x63},
{0xa159,0xa15c,0x35af},
{0xa15d,0xa17e,0x80},
{0xa1a1,0xa1f5,0xa2},
{0xa1f6,0xa1f6,0xf8},
{0xa1f7,0xa1f7,0xf7},
{0xa1f8,0xa1fe,0xf9},
{0xa240,0xa27e,0x100},
{0xa2a1,0xa2fe,0x13f},
{0xa340,0xa37e,0x19d},
{0xa3a1,0xa3bb,0x1dc},
{0xa3bd,0xa3bf,0x1f7},
{0xa440,0xa47e,0x253},
{0xa4a1,0xa4fe,0x292},
{0xa540,0xa57e,0x2f0},
{0xa5a1,0xa5fe,0x32f},
{0xa640,0xa67e,0x38d},
{0xa6a1,0xa6fe,0x3cc},
{0xa740,0xa77e,0x42a},
{0xa7a1,0xa7fe,0x469},
{0xa840,0xa87e,0x4c7},
{0xa8a1,0xa8fe,0x506},
{0xa940,0xa97e,0x564},
{0xa9a1,0xa9fe,0x5a3},
{0xaa40,0xaa7e,0x601},
{0xaaa1,0xaafe,0x640},
{0xab40,0xab7e,0x69e},
{0xaba1,0xabfe,0x6dd},
{0xac40,0xac7e,0x73b},
{0xaca1,0xacfd,0x77a},
{0xacfe,0xacfe,0x97f},
{0xad40,0xad7e,0x7d7},
{0xada1,0xadfe,0x816},
{0xae40,0xae7e,0x874},
{0xaea1,0xaefe,0x8b3},
{0xaf40,0xaf7e,0x911},
{0xafa1,0xafcf,0x950},
{0xafd0,0xaffe,0x980},
{0xb040,0xb07e,0x9af},
{0xb0a1,0xb0fe,0x9ee},
{0xb140,0xb17e,0xa4c},
{0xb1a1,0xb1fe,0xa8b},
{0xb240,0xb27e,0xae9},
{0xb2a1,0xb2fe,0xb28},
{0xb340,0xb37e,0xb86},
{0xb3a1,0xb3fe,0xbc5},
{0xb440,0xb47e,0xc23},
{0xb4a1,0xb4fe,0xc62},
{0xb540,0xb57e,0xcc0},
{0xb5a1,0xb5fe,0xcff},
{0xb640,0xb67e,0xd5d},
{0xb6a1,0xb6fe,0xd9c},
{0xb740,0xb77e,0xdfa},
{0xb7a1,0xb7fe,0xe39},
{0xb840,0xb87e,0xe97},
{0xb8a1,0xb8fe,0xed6},
{0xb940,0xb97e,0xf34},
{0xb9a1,0xb9fe,0xf73},
{0xba40,0xba7e,0xfd1},
{0xbaa1,0xbafe,0x1010},
{0xbb40,0xbb7e,0x106e},
{0xbba1,0xbbc7,0x10ad},
{0xbbc8,0xbbfe,0x10d5},
{0xbc40,0xbc7e,0x110c},
{0xbca1,0xbcfe,0x114b},
{0xbd40,0xbd7e,0x11a9},
{0xbda1,0xbdfe,0x11e8},
{0xbe40,0xbe51,0x1246},
{0xbe52,0xbe52,0x10d4},
{0xbe53,0xbe7e,0x1258},
{0xbea1,0xbefe,0x1284},
{0xbf40,0xbf7e,0x12e2},
{0xbfa1,0xbffe,0x1321},
{0xc040,0xc07e,0x137f},
{0xc0a1,0xc0fe,0x13be},
{0xc140,0xc17e,0x141c},
{0xc1a1,0xc1aa,0x145b},
{0xc1ab,0xc1fe,0x1466},
{0xc240,0xc27e,0x14ba},
{0xc2a1,0xc2ca,0x14f9},
{0xc2cb,0xc2cb,0x1465},
{0xc2cc,0xc2fe,0x1523},
{0xc340,0xc360,0x1556},
{0xc361,0xc37e,0x1578},
{0xc3a1,0xc3b8,0x1596},
{0xc3b9,0xc3b9,0x15af},
{0xc3ba,0xc3ba,0x15ae},
{0xc3bb,0xc3fe,0x15b0},
{0xc440,0xc455,0x15f4},
{0xc456,0xc456,0x1577},
{0xc457,0xc47e,0x160a},
{0xc4a1,0xc4fe,0x1632},
{0xc540,0xc57e,0x1690},
{0xc5a1,0xc5fe,0x16cf},
{0xc640,0xc67e,0x172d},
{0xc6a1,0xc6be,0x1fa},
{0xc6bf,0xc6ce,0x219},
{0xc6d0,0xc6d2,0x22a},
{0xc6d4,0xc6d4,0x22e},
{0xc6d6,0xc6d6,0x230},
{0xc6d8,0xc6dd,0x35b3},
{0xc6e0,0xc6fe,0x35ba},
{0xc740,0xc77e,0x35d9},
{0xc7a1,0xc7fe,0x3618},
{0xc840,0xc87e,0x3676},
{0xc8a1,0xc8a4,0x36b5},
{0xc8cd,0xc8d3,0x36e1},
{0xc8d4,0xc8d6,0x44c6},
{0xc8d7,0xc8df,0x451c},
{0xc8e0,0xc8e0,0x499e},
{0xc8e1,0xc8e8,0x4525},
{0xc8e9,0xc8e9,0x499f},
{0xc8ea,0xc8f0,0x452d},
{0xc8f1,0xc8f1,0x49a0},
{0xc8f5,0xc8fe,0x4992},
{0xc940,0xc949,0x176c},
{0xc94a,0xc94a,0x274},
{0xc94b,0xc96b,0x1776},
{0xc96c,0xc97e,0x1798},
{0xc9a1,0xc9bd,0x17ab},
{0xc9be,0xc9be,0x1797},
{0xc9bf,0xc9ec,0x17c8},
{0xc9ed,0xc9fe,0x17f7},
{0xca40,0xca7e,0x1809},
{0xcaa1,0xcaf6,0x1848},
{0xcaf7,0xcaf7,0x17f6},
{0xcaf8,0xcafe,0x189e},
{0xcb40,0xcb7e,0x18a5},
{0xcba1,0xcbfe,0x18e4},
{0xcc40,0xcc7e,0x1942},
{0xcca1,0xccfe,0x1981},
{0xcd40,0xcd7e,0x19df},
{0xcda1,0xcdfe,0x1a1e},
{0xce40,0xce7e,0x1a7c},
{0xcea1,0xcefe,0x1abb},
{0xcf40,0xcf7e,0x1b19},
{0xcfa1,0xcffe,0x1b58},
{0xd040,0xd07e,0x1bb6},
{0xd0a1,0xd0fe,0x1bf5},
{0xd140,0xd17e,0x1c53},
{0xd1a1,0xd1fe,0x1c92},
{0xd240,0xd27e,0x1cf0},
{0xd2a1,0xd2fe,0x1d2f},
{0xd340,0xd37e,0x1d8d},
{0xd3a1,0xd3fe,0x1dcc},
{0xd440,0xd47e,0x1e2a},
{0xd4a1,0xd4fe,0x1e69},
{0xd540,0xd57e,0x1ec7},
{0xd5a1,0xd5fe,0x1f06},
{0xd640,0xd67e,0x1f64},
{0xd6a1,0xd6cb,0x1fa3},
{0xd6cc,0xd6cc,0x2254},
{0xd6cd,0xd6fe,0x1fcf},
{0xd740,0xd779,0x2001},
{0xd77a,0xd77a,0x22b9},
{0xd77b,0xd77e,0x203b},
{0xd7a1,0xd7fe,0x203f},
{0xd840,0xd87e,0x209d},
{0xd8a1,0xd8fe,0x20dc},
{0xd940,0xd97e,0x213a},
{0xd9a1,0xd9fe,0x2179},
{0xda40,0xda7e,0x21d7},
{0xdaa1,0xdade,0x2216},
{0xdadf,0xdadf,0x1fce},
{0xdae0,0xdafe,0x2255},
{0xdb40,0xdb7e,0x2274},
{0xdba1,0xdba6,0x22b3},
{0xdba7,0xdbfe,0x22ba},
{0xdc40,0xdc7e,0x2312},
{0xdca1,0xdcfe,0x2351},
{0xdd40,0xdd7e,0x23af},
{0xdda1,0xddfb,0x23ee},
{0xddfc,0xddfc,0x2381},
{0xddfd,0xddfe,0x2449},
{0xde40,0xde7e,0x244b},
{0xdea1,0xdefe,0x248a},
{0xdf40,0xdf7e,0x24e8},
{0xdfa1,0xdffe,0x2527},
{0xe040,0xe07e,0x2585},
{0xe0a1,0xe0fe,0x25c4},
{0xe140,0xe17e,0x2622},
{0xe1a1,0xe1fe,0x2661},
{0xe240,0xe27e,0x26bf},
{0xe2a1,0xe2fe,0x26fe},
{0xe340,0xe37e,0x275c},
{0xe3a1,0xe3fe,0x279b},
{0xe440,0xe47e,0x27f9},
{0xe4a1,0xe4fe,0x2838},
{0xe540,0xe57e,0x2896},
{0xe5a1,0xe5fe,0x28d5},
{0xe640,0xe67e,0x2933},
{0xe6a1,0xe6fe,0x2972},
{0xe740,0xe77e,0x29d0},
{0xe7a1,0xe7fe,0x2a0f},
{0xe840,0xe87e,0x2a6d},
{0xe8a1,0xe8a2,0x2aac},
{0xe8a3,0xe8fe,0x2aaf},
{0xe940,0xe975,0x2b0b},
{0xe976,0xe97e,0x2b42},
{0xe9a1,0xe9fe,0x2b4b},
{0xea40,0xea7e,0x2ba9},
{0xeaa1,0xeafe,0x2be8},
{0xeb40,0xeb5a,0x2c46},
{0xeb5b,0xeb7e,0x2c62},
{0xeba1,0xebf0,0x2c86},
{0xebf1,0xebf1,0x2aae},
{0xebf2,0xebfe,0x2cd6},
{0xec40,0xec7e,0x2ce3},
{0xeca1,0xecdd,0x2d22},
{0xecde,0xecde,0x2b41},
{0xecdf,0xecfe,0x2d5f},
{0xed40,0xed7e,0x2d7f},
{0xeda1,0xeda9,0x2dbe},
{0xedaa,0xedfe,0x2dc8},
{0xee40,0xee7e,0x2e1d},
{0xeea1,0xeeea,0x2e5c},
{0xeeeb,0xeeeb,0x3014},
{0xeeec,0xeefe,0x2ea6},
{0xef40,0xef7e,0x2eb9},
{0xefa1,0xeffe,0x2ef8},
{0xf040,0xf055,0x2f56},
{0xf056,0xf056,0x2dc7},
{0xf057,0xf07e,0x2f6c},
{0xf0a1,0xf0ca,0x2f94},
{0xf0cb,0xf0cb,0x2c61},
{0xf0cc,0xf0fe,0x2fbe},
{0xf140,0xf162,0x2ff1},
{0xf163,0xf16a,0x3015},
{0xf16b,0xf16b,0x3160},
{0xf16c,0xf17e,0x301d},
{0xf1a1,0xf1fe,0x3030},
{0xf240,0xf267,0x308e},
{0xf268,0xf268,0x31ef},
{0xf269,0xf27e,0x30b6},
{0xf2a1,0xf2c2,0x30cc},
{0xf2c3,0xf2fe,0x30ef},
{0xf340,0xf374,0x312b},
{0xf375,0xf37e,0x3161},
{0xf3a1,0xf3fe,0x316b},
{0xf440,0xf465,0x31c9},
{0xf466,0xf47e,0x31f0},
{0xf4a1,0xf4b4,0x3209},
{0xf4b5,0xf4b5,0x30ee},
{0xf4b6,0xf4fc,0x321d},
{0xf4fd,0xf4fe,0x3265},
{0xf540,0xf57e,0x3267},
{0xf5a1,0xf5fe,0x32a6},
{0xf640,0xf662,0x3304},
{0xf663,0xf663,0x3264},
{0xf664,0xf67e,0x3327},
{0xf6a1,0xf6fe,0x3342},
{0xf740,0xf77e,0x33a0},
{0xf7a1,0xf7fe,0x33df},
{0xf840,0xf87e,0x343d},
{0xf8a1,0xf8fe,0x347c},
{0xf940,0xf976,0x34da},
{0xf977,0xf97e,0x3512},
{0xf9a1,0xf9c3,0x351a},
{0xf9c4,0xf9c4,0x3511},
{0xf9c5,0xf9c5,0x353d},
{0xf9c6,0xf9c6,0x3549},
{0xf9c7,0xf9d1,0x353e},
{0xf9d2,0xf9d5,0x354a},
{0xf9d6,0xf9fe,0x36e8},
{0xfa40,0xfa5e,0x400b},
{0xfa5f,0xfa5f,0x83a},
{0xfa60,0xfa65,0x402b},
{0xfa66,0xfa66,0x9fd},
{0xfa67,0xfa7e,0x4032},
{0xfaa1,0xfaa8,0x404a},
{0xfaa9,0xfaaa,0x4946},
{0xfaab,0xfabc,0x4054},
{0xfabd,0xfabd,0x30d},
{0xfabe,0xfac4,0x4067},
{0xfac5,0xfac5,0x16b},
{0xfac6,0xfad4,0x406f},
{0xfad5,0xfad5,0x860},
{0xfad6,0xfafe,0x407f},
{0xfb40,0xfb47,0x40a8},
{0xfb48,0xfb48,0x3e82},
{0xfb49,0xfb52,0x40b1},
{0xfb53,0xfb53,0x4948},
{0xfb54,0xfb6d,0x40bc},
{0xfb6e,0xfb6e,0x4949},
{0xfb6f,0xfb7e,0x40d7},
{0xfba1,0xfba2,0x40e7},
{0xfba3,0xfba3,0x494a},
{0xfba4,0xfbb7,0x40ea},
{0xfbb8,0xfbb8,0xc23},
{0xfbb9,0xfbbe,0x40ff},
{0xfbbf,0xfbbf,0x494b},
{0xfbc0,0xfbcc,0x4105},
{0xfbcd,0xfbcd,0x494c},
{0xfbce,0xfbf2,0x4112},
{0xfbf3,0xfbf3,0x17e4},
{0xfbf4,0xfbf8,0x4138},
{0xfbf9,0xfbf9,0x3e8e},
{0xfbfa,0xfbfe,0x413e},
{0xfc40,0xfc49,0x4143},
{0xfc4a,0xfc4a,0x494d},
{0xfc4b,0xfc4e,0x414d},
{0xfc4f,0xfc4f,0x212f},
{0xfc50,0xfc51,0x4151},
{0xfc52,0xfc52,0x494e},
{0xfc53,0xfc62,0x4153},
{0xfc63,0xfc63,0x494f},
{0xfc64,0xfc6b,0x4163},
{0xfc6c,0xfc6c,0x4001},
{0xfc6d,0xfc6d,0x4950},
{0xfc6e,0xfc74,0x416d},
{0xfc75,0xfc75,0x4951},
{0xfc76,0xfc7e,0x4174},
{0xfca1,0xfcb8,0x417d},
{0xfcb9,0xfcb9,0x115f},
{0xfcba,0xfcbb,0x4195},
{0xfcbc,0xfcbd,0x4952},
{0xfcbe,0xfccb,0x4198},
{0xfccc,0xfccc,0x4954},
{0xfccd,0xfce1,0x41a7},
{0xfce2,0xfce2,0xc79},
{0xfce3,0xfce3,0x4955},
{0xfce4,0xfced,0x41bd},
{0xfcee,0xfcee,0x4956},
{0xfcef,0xfcf0,0x41c7},
{0xfcf1,0xfcf1,0x4c3},
{0xfcf2,0xfcfe,0x41ca},
{0xfd40,0xfd48,0x41d7},
{0xfd49,0xfd49,0x4957},
{0xfd4a,0xfd69,0x41e0},
{0xfd6a,0xfd6a,0x4958},
{0xfd6b,0xfd7e,0x4201},
{0xfda1,0xfdb6,0x4215},
{0xfdb7,0xfdb7,0x18bd},
{0xfdb8,0xfdb8,0xcbd},
{0xfdb9,0xfdba,0x422d},
{0xfdbb,0xfdbb,0xca5},
{0xfdbc,0xfde2,0x4230},
{0xfde3,0xfde3,0x4959},
{0xfde4,0xfdf0,0x4258},
{0xfdf1,0xfdf1,0xcce},
{0xfdf2,0xfdf2,0x495a},
{0xfdf3,0xfdfe,0x4266},
{0xfe40,0xfe51,0x4272},
{0xfe52,0xfe52,0x3d70},
{0xfe53,0xfe6c,0x4285},
{0xfe6d,0xfe6d,0x495b},
{0xfe6e,0xfe6e,0x429f},
{0xfe6f,0xfe6f,0xe84},
{0xfe70,0xfe77,0x42a1},
{0xfe78,0xfe78,0x495c},
{0xfe79,0xfe7e,0x42a9},
{0xfea1,0xfea9,0x42af},
{0xfeaa,0xfeaa,0x120},
{0xfeab,0xfedc,0x42b8},
{0xfedd,0xfedd,0x1ba8},
{0xfede,0xfedf,0x495d},
{0xfee0,0xfeec,0x42eb},
{0xfeed,0xfeee,0x495f},
{0xfeef,0xfefe,0x42f8},
};

static pdf_cmap cmap_HKscs_B5_H = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "HKscs-B5-H",
	/* usecmap */ "", NULL,
	/* wmode */ 0,
	/* codespaces */ 2, {
		{ 1, 0x00, 0x80 },
		{ 2, 0x8740, 0xfefe },
	},
	1212, 1212, (pdf_range*)cmap_HKscs_B5_H_ranges,
	0, 0, NULL, /* xranges */
	0, 0, NULL, /* mranges */
	0, 0, NULL, /* table */
	0, 0, 0, NULL /* splay tree */
};
