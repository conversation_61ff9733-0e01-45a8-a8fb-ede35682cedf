/* This is an automatically generated file. Do not edit. */

/* UniCNS-UTF16-V */

static const pdf_range cmap_UniCNS_UTF16_V_ranges[] = {
{0x2013,0x2013,0x78},
{0x2014,0x2014,0x7a},
{0x2025,0x2025,0x6d},
{0x3008,0x3009,0x96},
{0x300a,0x300b,0x92},
{0x300c,0x300d,0x9a},
{0x300e,0x300f,0x9e},
{0x3010,0x3011,0x8e},
{0x3014,0x3015,0x8a},
{0xfe4f,0xfe4f,0x35b1},
{0xff08,0xff09,0x82},
{0xff5b,0xff5b,0x86},
{0xff5d,0xff5d,0x87},
};

static pdf_cmap cmap_UniCNS_UTF16_V = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "UniCNS-UTF16-V",
	/* usecmap */ "UniCNS-UTF16-H", NULL,
	/* wmode */ 1,
	/* codespaces */ 0, {
		{ 0, 0, 0 },
	},
	13, 13, (pdf_range*)cmap_UniCNS_UTF16_V_ranges,
	0, 0, NULL, /* xranges */
	0, 0, NULL, /* mranges */
	0, 0, NULL, /* table */
	0, 0, 0, NULL /* splay tree */
};
