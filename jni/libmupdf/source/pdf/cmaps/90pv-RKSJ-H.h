/* This is an automatically generated file. Do not edit. */

/* 90pv-RKSJ-H */

static const pdf_range cmap_90pv_RKSJ_H_ranges[] = {
{0x20,0x7e,0x1},
{0x80,0x80,0x61},
{0xa0,0xdf,0x146},
{0xfd,0xfd,0x98},
{0xfe,0xfe,0xe4},
{0xff,0xff,0x7c},
{0x8140,0x817e,0x279},
{0x8180,0x81ac,0x2b8},
{0x81b8,0x81bf,0x2e5},
{0x81c8,0x81ce,0x2ed},
{0x81da,0x81e8,0x2f4},
{0x81f0,0x81f7,0x303},
{0x81fc,0x81fc,0x30b},
{0x824f,0x8258,0x30c},
{0x8260,0x8279,0x316},
{0x8281,0x829a,0x330},
{0x829f,0x82f1,0x34a},
{0x8340,0x837e,0x39d},
{0x8380,0x8396,0x3dc},
{0x839f,0x83b6,0x3f3},
{0x83bf,0x83d6,0x40b},
{0x8440,0x8460,0x423},
{0x8470,0x847e,0x444},
{0x8480,0x8491,0x453},
{0x849f,0x849f,0x1d37},
{0x84a0,0x84a0,0x1d39},
{0x84a1,0x84a1,0x1d43},
{0x84a2,0x84a2,0x1d47},
{0x84a3,0x84a3,0x1d4f},
{0x84a4,0x84a4,0x1d4b},
{0x84a5,0x84a5,0x1d53},
{0x84a6,0x84a6,0x1d63},
{0x84a7,0x84a7,0x1d5b},
{0x84a8,0x84a8,0x1d6b},
{0x84a9,0x84a9,0x1d73},
{0x84aa,0x84aa,0x1d38},
{0x84ab,0x84ab,0x1d3a},
{0x84ac,0x84ac,0x1d46},
{0x84ad,0x84ad,0x1d4a},
{0x84ae,0x84ae,0x1d52},
{0x84af,0x84af,0x1d4e},
{0x84b0,0x84b0,0x1d5a},
{0x84b1,0x84b1,0x1d6a},
{0x84b2,0x84b2,0x1d62},
{0x84b3,0x84b3,0x1d72},
{0x84b4,0x84b4,0x1d82},
{0x84b5,0x84b5,0x1d57},
{0x84b6,0x84b6,0x1d66},
{0x84b7,0x84b7,0x1d5f},
{0x84b8,0x84b8,0x1d6e},
{0x84b9,0x84b9,0x1d76},
{0x84ba,0x84ba,0x1d54},
{0x84bb,0x84bb,0x1d67},
{0x84bc,0x84bc,0x1d5c},
{0x84bd,0x84bd,0x1d6f},
{0x84be,0x84be,0x1d79},
{0x8540,0x8553,0x1d83},
{0x855e,0x8571,0x1f87},
{0x857c,0x857e,0x205e},
{0x8580,0x8585,0x2061},
{0x8591,0x859a,0x1f7d},
{0x859f,0x85a8,0x1d97},
{0x85a9,0x85aa,0x2021},
{0x85ab,0x85ad,0x2067},
{0x85b3,0x85bc,0x1f9c},
{0x85bd,0x85c1,0x206a},
{0x85db,0x85f4,0x1fb0},
{0x8640,0x8640,0x1db1},
{0x8641,0x8641,0x1ffa},
{0x8642,0x8642,0x1db2},
{0x8643,0x8643,0x1f54},
{0x8644,0x8644,0x1f56},
{0x8645,0x8645,0x206f},
{0x8646,0x8646,0x1db7},
{0x8647,0x8647,0x1f57},
{0x8648,0x8648,0x1db3},
{0x8649,0x8649,0x1f55},
{0x864a,0x864a,0x1db4},
{0x864b,0x864b,0x2070},
{0x864c,0x864d,0x1db5},
{0x864e,0x864e,0x1f65},
{0x864f,0x8655,0x1f58},
{0x8656,0x8656,0x2071},
{0x8657,0x8657,0x1f64},
{0x8658,0x8659,0x1f62},
{0x865a,0x865c,0x1f5f},
{0x865d,0x865d,0x2072},
{0x869b,0x869d,0x1dba},
{0x869e,0x869e,0x2073},
{0x869f,0x869f,0x1f52},
{0x86a0,0x86a1,0x1f50},
{0x86a2,0x86a2,0x1f53},
{0x86a3,0x86a3,0x2013},
{0x86a4,0x86a4,0x2015},
{0x86a5,0x86a5,0x2014},
{0x86a6,0x86a6,0x2016},
{0x86b3,0x86b3,0x1f7a},
{0x86b4,0x86b4,0x1f78},
{0x86b5,0x86b5,0x2074},
{0x86c7,0x86ca,0x201b},
{0x86cb,0x86ce,0x2075},
{0x86cf,0x86cf,0x1f4e},
{0x86d0,0x86d0,0x1f4d},
{0x86d1,0x86d1,0x1f4c},
{0x86d2,0x86d2,0x1f4b},
{0x86d3,0x86d6,0x200e},
{0x8740,0x8746,0x2005},
{0x8747,0x8747,0x1fd6},
{0x8748,0x8748,0x200c},
{0x8749,0x8749,0x1fd1},
{0x874a,0x874a,0x1fca},
{0x874b,0x874b,0x1dc4},
{0x874c,0x874c,0x1fd7},
{0x874d,0x874d,0x1dc2},
{0x874e,0x874e,0x1fd2},
{0x874f,0x874f,0x1fcd},
{0x8750,0x8750,0x1dc3},
{0x8751,0x8751,0x1fd5},
{0x8752,0x8752,0x1fd3},
{0x8753,0x8753,0x1fcf},
{0x8754,0x8754,0x1fd4},
{0x8755,0x8755,0x1fd0},
{0x8756,0x8757,0x1fcb},
{0x8758,0x8758,0x1fce},
{0x8791,0x8792,0x207d},
{0x8793,0x8797,0x1dbd},
{0x8798,0x8798,0x1fda},
{0x8799,0x8799,0x1fe5},
{0x879a,0x879a,0x207f},
{0x879b,0x879b,0x1fde},
{0x879c,0x879c,0x1fff},
{0x879d,0x879d,0x2080},
{0x879e,0x879e,0x201f},
{0x879f,0x879f,0x1da1},
{0x87a0,0x87a0,0x1f66},
{0x87a1,0x87a1,0x1da4},
{0x87a2,0x87a2,0x1da2},
{0x87a3,0x87a3,0x1f67},
{0x87a4,0x87a4,0x1ff7},
{0x87a5,0x87a6,0x2087},
{0x87a7,0x87a7,0x1f6a},
{0x87a8,0x87a8,0x1da8},
{0x87a9,0x87aa,0x1f68},
{0x87ab,0x87ab,0x1da6},
{0x87ac,0x87ac,0x1da9},
{0x87ad,0x87ad,0x1daf},
{0x87ae,0x87ae,0x1f6e},
{0x87af,0x87af,0x1f6c},
{0x87b0,0x87b0,0x1dab},
{0x87b1,0x87b1,0x1f6d},
{0x87b2,0x87b2,0x1f6b},
{0x87b3,0x87b3,0x1dac},
{0x87b4,0x87b4,0x1f6f},
{0x87b5,0x87b5,0x1dae},
{0x87bd,0x87bd,0x1f70},
{0x87be,0x87bf,0x1f73},
{0x87c0,0x87c1,0x1f71},
{0x87e5,0x87e7,0x1dc5},
{0x87e8,0x87e8,0x2083},
{0x87fa,0x87fa,0x1f76},
{0x87fb,0x87fc,0x2081},
{0x8840,0x8840,0x1dc8},
{0x8841,0x8842,0x1dcd},
{0x8854,0x8855,0x1db8},
{0x8868,0x8868,0x1f16},
{0x886a,0x886d,0x2079},
{0x889f,0x88fc,0x465},
{0x8940,0x897e,0x4c3},
{0x8980,0x89fc,0x502},
{0x8a40,0x8a7e,0x57f},
{0x8a80,0x8afc,0x5be},
{0x8b40,0x8b7e,0x63b},
{0x8b80,0x8bfc,0x67a},
{0x8c40,0x8c7e,0x6f7},
{0x8c80,0x8cfc,0x736},
{0x8d40,0x8d7e,0x7b3},
{0x8d80,0x8dfc,0x7f2},
{0x8e40,0x8e7e,0x86f},
{0x8e80,0x8efc,0x8ae},
{0x8f40,0x8f7e,0x92b},
{0x8f80,0x8ffc,0x96a},
{0x9040,0x907e,0x9e7},
{0x9080,0x90fc,0xa26},
{0x9140,0x917e,0xaa3},
{0x9180,0x91fc,0xae2},
{0x9240,0x927e,0xb5f},
{0x9280,0x92fc,0xb9e},
{0x9340,0x937e,0xc1b},
{0x9380,0x93fc,0xc5a},
{0x9440,0x947e,0xcd7},
{0x9480,0x94fc,0xd16},
{0x9540,0x957e,0xd93},
{0x9580,0x95fc,0xdd2},
{0x9640,0x967e,0xe4f},
{0x9680,0x96fc,0xe8e},
{0x9740,0x977e,0xf0b},
{0x9780,0x97fc,0xf4a},
{0x9840,0x9872,0xfc7},
{0x989f,0x98fc,0xffa},
{0x9940,0x997e,0x1058},
{0x9980,0x99fc,0x1097},
{0x9a40,0x9a7e,0x1114},
{0x9a80,0x9afc,0x1153},
{0x9b40,0x9b7e,0x11d0},
{0x9b80,0x9bfc,0x120f},
{0x9c40,0x9c7e,0x128c},
{0x9c80,0x9cfc,0x12cb},
{0x9d40,0x9d7e,0x1348},
{0x9d80,0x9dfc,0x1387},
{0x9e40,0x9e7e,0x1404},
{0x9e80,0x9efc,0x1443},
{0x9f40,0x9f7e,0x14c0},
{0x9f80,0x9ffc,0x14ff},
{0xe040,0xe07e,0x157c},
{0xe080,0xe0fc,0x15bb},
{0xe140,0xe17e,0x1638},
{0xe180,0xe1fc,0x1677},
{0xe240,0xe27e,0x16f4},
{0xe280,0xe2fc,0x1733},
{0xe340,0xe37e,0x17b0},
{0xe380,0xe3fc,0x17ef},
{0xe440,0xe47e,0x186c},
{0xe480,0xe4fc,0x18ab},
{0xe540,0xe57e,0x1928},
{0xe580,0xe5fc,0x1967},
{0xe640,0xe67e,0x19e4},
{0xe680,0xe6fc,0x1a23},
{0xe740,0xe77e,0x1aa0},
{0xe780,0xe7fc,0x1adf},
{0xe840,0xe87e,0x1b5c},
{0xe880,0xe8fc,0x1b9b},
{0xe940,0xe97e,0x1c18},
{0xe980,0xe9fc,0x1c57},
{0xea40,0xea7e,0x1cd4},
{0xea80,0xeaa2,0x1d13},
{0xeaa3,0xeaa4,0x205c},
{0xeb41,0xeb42,0x1ecf},
{0xeb50,0xeb51,0x1ed1},
{0xeb5b,0xeb5d,0x1ed3},
{0xeb60,0xeb64,0x1ed6},
{0xeb69,0xeb7a,0x1edb},
{0xeb81,0xeb81,0x1eed},
{0xec9f,0xec9f,0x1eee},
{0xeca1,0xeca1,0x1eef},
{0xeca3,0xeca3,0x1ef0},
{0xeca5,0xeca5,0x1ef1},
{0xeca7,0xeca7,0x1ef2},
{0xecc1,0xecc1,0x1ef3},
{0xece1,0xece1,0x1ef4},
{0xece3,0xece3,0x1ef5},
{0xece5,0xece5,0x1ef6},
{0xecec,0xecec,0x1ef7},
{0xed40,0xed40,0x1ef8},
{0xed42,0xed42,0x1ef9},
{0xed44,0xed44,0x1efa},
{0xed46,0xed46,0x1efb},
{0xed48,0xed48,0x1efc},
{0xed62,0xed62,0x1efd},
{0xed83,0xed83,0x1efe},
{0xed85,0xed85,0x1eff},
{0xed87,0xed87,0x1f00},
{0xed8e,0xed8e,0x1f01},
{0xed95,0xed96,0x1f02},
};

static pdf_cmap cmap_90pv_RKSJ_H = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "90pv-RKSJ-H",
	/* usecmap */ "", NULL,
	/* wmode */ 0,
	/* codespaces */ 5, {
		{ 1, 0x00, 0x80 },
		{ 2, 0x8140, 0x9ffc },
		{ 1, 0xa0, 0xdf },
		{ 2, 0xe040, 0xfcfc },
		{ 1, 0xfd, 0xff },
	},
	263, 263, (pdf_range*)cmap_90pv_RKSJ_H_ranges,
	0, 0, NULL, /* xranges */
	0, 0, NULL, /* mranges */
	0, 0, NULL, /* table */
	0, 0, 0, NULL /* splay tree */
};
