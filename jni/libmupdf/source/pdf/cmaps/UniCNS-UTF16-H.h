/* This is an automatically generated file. Do not edit. */

/* UniCNS-UTF16-H */

static const pdf_range cmap_UniCNS_UTF16_H_ranges[] = {
{0xa0,0xa0,0x1},
{0xa9,0xa9,0x60},
{0xaf,0xaf,0xc3},
{0x2cd,0x2cd,0xc6},
{0x304,0x304,0xc3},
{0x30c,0x30c,0x1f8},
{0x2002,0x2002,0x3550},
{0x2027,0x2027,0x68},
{0x2122,0x2122,0x61},
{0x2215,0x2215,0x101},
{0x2295,0x2295,0xf3},
{0x2299,0x2299,0xf4},
{0x22ef,0x22ef,0x6e},
{0x2329,0x232a,0x94},
{0x23da,0x23db,0x499c},
{0x2550,0x2550,0x370b},
{0x255e,0x255e,0x36fb},
{0x2561,0x2561,0x36fd},
{0x256a,0x256a,0x36fc},
{0x2574,0x2574,0x35b0},
{0x2e9d,0x2e9d,0x499e},
{0x2ec6,0x2ec6,0x499f},
{0x2ee3,0x2ee3,0x49a0},
{0x2f00,0x2f00,0x253},
{0x2f01,0x2f03,0x218},
{0x2f04,0x2f04,0x254},
{0x2f05,0x2f05,0x21b},
{0x2f06,0x2f06,0x25a},
{0x2f07,0x2f07,0x21c},
{0x2f08,0x2f0b,0x25b},
{0x2f0c,0x2f0e,0x21d},
{0x2f0f,0x2f0f,0x25f},
{0x2f10,0x2f10,0x176e},
{0x2f11,0x2f11,0x260},
{0x2f12,0x2f12,0x262},
{0x2f13,0x2f13,0x220},
{0x2f14,0x2f14,0x263},
{0x2f15,0x2f15,0x176f},
{0x2f16,0x2f16,0x221},
{0x2f17,0x2f18,0x264},
{0x2f19,0x2f19,0x222},
{0x2f1a,0x2f1a,0x1770},
{0x2f1b,0x2f1b,0x223},
{0x2f1c,0x2f1c,0x266},
{0x2f1d,0x2f1d,0x279},
{0x2f1e,0x2f1e,0x1775},
{0x2f1f,0x2f20,0x27a},
{0x2f21,0x2f21,0x44e0},
{0x2f22,0x2f22,0x224},
{0x2f23,0x2f26,0x27c},
{0x2f27,0x2f27,0x225},
{0x2f28,0x2f2b,0x282},
{0x2f2c,0x2f2c,0x1776},
{0x2f2d,0x2f2d,0x286},
{0x2f2e,0x2f2e,0x226},
{0x2f2f,0x2f30,0x288},
{0x2f31,0x2f32,0x28c},
{0x2f34,0x2f35,0x228},
{0x2f36,0x2f38,0x28e},
{0x2f39,0x2f3a,0x22a},
{0x2f3b,0x2f3b,0x1777},
{0x2f3c,0x2f3f,0x2d0},
{0x2f40,0x2f40,0x2d5},
{0x2f41,0x2f41,0x22c},
{0x2f42,0x2f45,0x2d6},
{0x2f46,0x2f46,0x22d},
{0x2f47,0x2f4d,0x2da},
{0x2f4e,0x2f4e,0x178a},
{0x2f4f,0x2f52,0x2e1},
{0x2f53,0x2f53,0x178c},
{0x2f54,0x2f58,0x2e5},
{0x2f59,0x2f59,0x178d},
{0x2f5a,0x2f5d,0x2ea},
{0x2f5e,0x2f64,0x356},
{0x2f65,0x2f65,0x35e},
{0x2f66,0x2f66,0x362},
{0x2f67,0x2f68,0x22e},
{0x2f69,0x2f70,0x363},
{0x2f71,0x2f71,0x17b2},
{0x2f72,0x2f74,0x36b},
{0x2f75,0x2f78,0x3f6},
{0x2f79,0x2f79,0x1812},
{0x2f7a,0x2f7c,0x3fa},
{0x2f7d,0x2f81,0x3fe},
{0x2f82,0x2f8a,0x405},
{0x2f8b,0x2f8b,0x1813},
{0x2f8c,0x2f8c,0x1818},
{0x2f8d,0x2f90,0x40f},
{0x2f91,0x2f91,0x1819},
{0x2f92,0x2f97,0x508},
{0x2f98,0x2f98,0x18e7},
{0x2f99,0x2fa0,0x50e},
{0x2fa1,0x2fa1,0x230},
{0x2fa2,0x2fa2,0x51b},
{0x2fa3,0x2fa5,0x520},
{0x2fa6,0x2fa9,0x696},
{0x2faa,0x2faa,0x231},
{0x2fab,0x2fac,0x69f},
{0x2fad,0x2fad,0x44e3},
{0x2fae,0x2fae,0x6a2},
{0x2faf,0x2fb9,0x826},
{0x2fba,0x2fbc,0x9f5},
{0x2fbd,0x2fbd,0x1e33},
{0x2fbe,0x2fbe,0x9f8},
{0x2fbf,0x2fbf,0x1e34},
{0x2fc0,0x2fc1,0x9f9},
{0x2fc2,0x2fc7,0xbe1},
{0x2fc8,0x2fca,0xdbb},
{0x2fcb,0x2fcb,0x2360},
{0x2fcc,0x2fcc,0x2612},
{0x2fcd,0x2fcf,0xf7b},
{0x2fd0,0x2fd1,0x1100},
{0x2fd2,0x2fd2,0x1289},
{0x2fd3,0x2fd4,0x13b2},
{0x2fd5,0x2fd5,0x2f0d},
{0x3038,0x303a,0x16a},
{0x31c0,0x31c4,0x44c9},
{0x31c5,0x31c5,0x44cf},
{0x31c6,0x31c7,0x44d2},
{0x31c8,0x31c8,0x44d6},
{0x31c9,0x31cc,0x44d8},
{0x31cd,0x31ce,0x44dd},
{0x31cf,0x31cf,0x36af},
{0x344a,0x344a,0x4a66},
{0x34e6,0x34e6,0x4ab2},
{0x3559,0x3559,0x49ac},
{0x361d,0x361d,0x36b7},
{0x3625,0x3625,0x4a44},
{0x3661,0x3661,0x4a8c},
{0x37d6,0x37d6,0x4a7b},
{0x3875,0x3875,0x4a90},
{0x38d4,0x38d4,0x4a82},
{0x3978,0x3978,0x4a65},
{0x3af5,0x3af5,0x4a96},
{0x3b95,0x3b95,0x4a20},
{0x3c18,0x3c18,0x4931},
{0x3c8b,0x3c8b,0x49b7},
{0x3d12,0x3d12,0x4a5c},
{0x3d88,0x3d88,0x4a52},
{0x3dc9,0x3dc9,0x49a5},
{0x3df4,0x3df4,0x4a3e},
{0x3edb,0x3edb,0x49e7},
{0x3eec,0x3eec,0x4a94},
{0x3f07,0x3f07,0x4a77},
{0x3fc8,0x3fc8,0x4a8a},
{0x4009,0x4009,0x49be},
{0x4071,0x4071,0x4a00},
{0x40b4,0x40b4,0x4acd},
{0x40f8,0x40f8,0x4a7a},
{0x4102,0x4102,0x4a74},
{0x4131,0x4131,0x4aba},
{0x417c,0x417c,0x4a7e},
{0x4181,0x4181,0x4ac2},
{0x41ed,0x41ed,0x4a8f},
{0x4223,0x4223,0x49c1},
{0x4276,0x4276,0x4a05},
{0x42a2,0x42a2,0x4a01},
{0x430a,0x430a,0x4a9e},
{0x439a,0x439a,0x4a56},
{0x43f0,0x43f0,0x4a15},
{0x4491,0x4491,0x36b4},
{0x44bd,0x44bd,0x4a8e},
{0x44c3,0x44c3,0x4a53},
{0x44de,0x44de,0x4a8d},
{0x44e1,0x44e1,0x4acf},
{0x451b,0x451b,0x4a85},
{0x4523,0x4523,0x37f6},
{0x4536,0x4536,0x4a57},
{0x4558,0x4558,0x4a61},
{0x4561,0x4561,0x4a84},
{0x456d,0x456d,0x4a81},
{0x4578,0x4578,0x4a19},
{0x45a6,0x45a6,0x4a18},
{0x45ac,0x45ac,0x43e1},
{0x45b3,0x45b3,0x4a1c},
{0x45da,0x45da,0x4a2d},
{0x45ea,0x45ea,0x4a89},
{0x4603,0x4603,0x4a17},
{0x46a1,0x46a1,0x4a32},
{0x46ae,0x46ae,0x4ac0},
{0x46bb,0x46bb,0x4a75},
{0x46f7,0x46f7,0x4a7c},
{0x4736,0x4736,0x4a21},
{0x4744,0x4744,0x4a22},
{0x474f,0x474f,0x4a2c},
{0x48b4,0x48b4,0x3bc5},
{0x492f,0x492f,0x4ab8},
{0x4930,0x4930,0x4aa2},
{0x4aa4,0x4aa4,0x4a31},
{0x4b10,0x4b10,0x49b9},
{0x4b20,0x4b20,0x49aa},
{0x4c32,0x4c32,0x4a16},
{0x4c40,0x4c40,0x4a24},
{0x4c47,0x4c47,0x4a23},
{0x4c57,0x4c57,0x4a2a},
{0x4c77,0x4c77,0x4a64},
{0x4c7b,0x4c7b,0x4a87},
{0x4c81,0x4c81,0x4a0b},
{0x4c85,0x4c85,0x4a2e},
{0x4ce2,0x4ce2,0x4a1e},
{0x4d07,0x4d07,0x4a30},
{0x4d76,0x4d76,0x4a88},
{0x4d77,0x4d77,0x4a1b},
{0x4d89,0x4d89,0x4a86},
{0x4e2f,0x4e2f,0x49d2},
{0x4ea3,0x4ea3,0x49cf},
{0x4efe,0x4efe,0x439b},
{0x4f17,0x4f17,0x49d3},
{0x4fb4,0x4fb4,0x4a6a},
{0x4ff0,0x4ff0,0x49d0},
{0x503b,0x503b,0x49a1},
{0x50bc,0x50bc,0x49d1},
{0x5151,0x5151,0x4ad4},
{0x51ae,0x51ae,0x49bc},
{0x51e2,0x51e2,0x4946},
{0x524f,0x524f,0x4ab0},
{0x5324,0x5324,0x49c8},
{0x5434,0x5434,0x49d5},
{0x543f,0x543f,0x4ad5},
{0x544c,0x544c,0x4ab9},
{0x54cc,0x54cc,0x45e2},
{0x5553,0x5553,0x494a},
{0x55b9,0x55b9,0x45e4},
{0x55de,0x55de,0x48e5},
{0x56fb,0x56fb,0x4a3a},
{0x573d,0x573d,0x4a06},
{0x577a,0x577a,0x4922},
{0x57b3,0x57b3,0x4ab7},
{0x5818,0x5818,0x4abc},
{0x581f,0x581f,0x40d6},
{0x5892,0x5892,0x49d7},
{0x5896,0x5896,0x4aa4},
{0x58d0,0x58d0,0x49d8},
{0x5902,0x5902,0x44e0},
{0x591d,0x591d,0x4a3c},
{0x59bf,0x59bf,0x4a6c},
{0x5a1a,0x5a1a,0x4a02},
{0x5aa4,0x5aa4,0x4a43},
{0x5aaa,0x5aaa,0x4ad6},
{0x5acf,0x5acf,0x3a12},
{0x5ad1,0x5ad1,0x4a46},
{0x5b15,0x5b15,0x49e9},
{0x5b96,0x5b96,0x4a12},
{0x5bb7,0x5bb7,0x4a47},
{0x5bdb,0x5bdb,0x4a41},
{0x5c78,0x5c78,0x4a5b},
{0x5cd5,0x5cd5,0x4a58},
{0x5cf5,0x5cf5,0x49a9},
{0x5cfc,0x5cfc,0x4a48},
{0x5d78,0x5d78,0x4a5e},
{0x5d7b,0x5d7b,0x4a0d},
{0x5e92,0x5e92,0x49da},
{0x5e99,0x5e99,0x49db},
{0x5ed0,0x5ed0,0x3e8e},
{0x5f3b,0x5f3b,0x4a70},
{0x5fc2,0x5fc2,0x49dc},
{0x60a6,0x60a6,0x4ad7},
{0x60b3,0x60b3,0x4950},
{0x60de,0x60de,0x41fa},
{0x60ea,0x60ea,0x416c},
{0x6120,0x6120,0x4ad8},
{0x6122,0x6122,0x49ae},
{0x62c1,0x62c1,0x4ad1},
{0x635d,0x635d,0x4ad9},
{0x655a,0x655a,0x4ada},
{0x658b,0x658b,0x49de},
{0x664d,0x664d,0x4a39},
{0x6660,0x6660,0x4aa9},
{0x66e7,0x66e7,0x4a4f},
{0x6719,0x6719,0x49b3},
{0x676b,0x676b,0x4a04},
{0x676e,0x676e,0x4a49},
{0x6782,0x6782,0x4aae},
{0x68c1,0x68c1,0x4adb},
{0x6919,0x6919,0x49e0},
{0x69e9,0x69e9,0x481f},
{0x6a29,0x6a29,0x4aaa},
{0x6a43,0x6a43,0x49e1},
{0x6a63,0x6a63,0x49bf},
{0x6c32,0x6c32,0x4adc},
{0x6cff,0x6cff,0x49e3},
{0x6d9a,0x6d9a,0x4add},
{0x6dfe,0x6dfe,0x49a2},
{0x6e57,0x6e57,0x4a50},
{0x704d,0x704d,0x49c7},
{0x706e,0x706e,0x4ad2},
{0x7077,0x7077,0x49a8},
{0x70a6,0x70a6,0x46d0},
{0x7157,0x7157,0x4a60},
{0x7174,0x7174,0x4ade},
{0x7191,0x7191,0x49b2},
{0x7200,0x7200,0x49e4},
{0x7225,0x7225,0x4a34},
{0x738c,0x738c,0x49e6},
{0x73ba,0x73ba,0x49b4},
{0x73c4,0x73c4,0x4ab3},
{0x7402,0x7402,0x49bb},
{0x744c,0x744c,0x4aac},
{0x7461,0x7461,0x4a4c},
{0x749d,0x749d,0x4a4d},
{0x74b9,0x74b9,0x49ea},
{0x74c6,0x74c6,0x4ab5},
{0x76d9,0x76d9,0x49cb},
{0x77dd,0x77dd,0x49ce},
{0x78e4,0x78e4,0x49ba},
{0x79ca,0x79ca,0x4a51},
{0x79d0,0x79d0,0x4aa7},
{0x7a0e,0x7a0e,0x4adf},
{0x7a2a,0x7a2a,0x49c3},
{0x7a2c,0x7a2c,0x4aaf},
{0x7a32,0x7a32,0x4aa8},
{0x7a72,0x7a72,0x4abd},
{0x7a93,0x7a93,0x49ee},
{0x7afc,0x7afc,0x4a97},
{0x7bae,0x7bae,0x4ac4},
{0x7bc5,0x7bc5,0x4aad},
{0x7bec,0x7bec,0x49ef},
{0x7cc3,0x7cc3,0x49f0},
{0x7d8b,0x7d8b,0x49d6},
{0x7d95,0x7d95,0x4a3b},
{0x7dfc,0x7dfc,0x4ae0},
{0x7e5b,0x7e5b,0x4a14},
{0x7e6c,0x7e6c,0x49f1},
{0x8131,0x8131,0x4ae1},
{0x816c,0x816c,0x4a6d},
{0x817d,0x817d,0x4ae2},
{0x82bf,0x82bf,0x4a09},
{0x82f8,0x82f8,0x49f2},
{0x8480,0x8480,0x46c9},
{0x8484,0x8484,0x4a9f},
{0x8495,0x8495,0x3b6e},
{0x8496,0x8496,0x46c6},
{0x8503,0x8503,0x3988},
{0x8504,0x8504,0x4acb},
{0x855f,0x855f,0x49fa},
{0x8593,0x8593,0x4a4a},
{0x8597,0x8597,0x49f3},
{0x85d6,0x85d6,0x4a07},
{0x85f4,0x85f4,0x4ae3},
{0x860f,0x860f,0x49c2},
{0x8613,0x8613,0x4aa3},
{0x8715,0x8715,0x4ae4},
{0x8771,0x8771,0x46a7},
{0x8786,0x8786,0x48e6},
{0x888f,0x888f,0x49a6},
{0x8890,0x8890,0x49f5},
{0x889d,0x889d,0x4aab},
{0x898a,0x898a,0x46aa},
{0x89a9,0x89a9,0x3ff9},
{0x8aac,0x8aac,0x4ae5},
{0x8acc,0x8acc,0x4a69},
{0x8af9,0x8af9,0x4a5a},
{0x8b4c,0x8b4c,0x46ac},
{0x8b83,0x8b83,0x49eb},
{0x8b8f,0x8b8f,0x4abf},
{0x8e80,0x8e80,0x48cd},
{0x8eb9,0x8eb9,0x49f7},
{0x8f3c,0x8f3c,0x4ae6},
{0x8fa7,0x8fa7,0x49b0},
{0x8fcf,0x8fcf,0x49f9},
{0x9046,0x9046,0x4ab1},
{0x9196,0x9196,0x4ae7},
{0x91f6,0x91f6,0x49b1},
{0x9218,0x9218,0x4aa6},
{0x9221,0x9221,0x49fc},
{0x92ed,0x92ed,0x4ae8},
{0x936e,0x936e,0x3fac},
{0x93c6,0x93c6,0x46ba},
{0x93f4,0x93f4,0x49ca},
{0x942f,0x942f,0x4aa1},
{0x95b2,0x95b2,0x4ae9},
{0x9734,0x9734,0x4a3f},
{0x974a,0x974a,0x4aa5},
{0x9755,0x9755,0x49c6},
{0x975d,0x975d,0x3e2f},
{0x9771,0x9771,0x46cb},
{0x9856,0x9856,0x4a6e},
{0x9868,0x9868,0x4a03},
{0x9962,0x9962,0x47bf},
{0x999b,0x999b,0x46cc},
{0x99e0,0x99e0,0x49fb},
{0x9ab6,0x9ab6,0x45e7},
{0x9c47,0x9c47,0x4aea},
{0x9f62,0x9f62,0x46b9},
{0x9f96,0x9f96,0x4aa0},
{0x9f97,0x9f97,0x4a98},
{0x9fa6,0x9fa6,0x49a4},
{0x9fa7,0x9fa7,0x49cd},
{0x9fa8,0x9fa8,0x49d4},
{0x9fa9,0x9fa9,0x49f4},
{0x9faa,0x9faa,0x49f6},
{0x9fab,0x9fab,0x49f8},
{0x9fac,0x9fac,0x49fd},
{0x9fad,0x9fae,0x4a10},
{0x9faf,0x9faf,0x4a13},
{0x9fb0,0x9fb0,0x36b5},
{0x9fb1,0x9fb1,0x36b7},
{0x9fb2,0x9fb2,0x4a5f},
{0x9fb3,0x9fb3,0x4a78},
{0x9fc7,0x9fc7,0x4ab6},
{0x9fc8,0x9fc8,0x4ac6},
{0x9fc9,0x9fc9,0x4aca},
{0x9fca,0x9fca,0x4ace},
{0x9fcb,0x9fcb,0x4ad3},
{0x9fd0,0x9fd0,0x48be},
{0xe001,0xe001,0x400c},
{0xe004,0xe008,0x400f},
{0xe00b,0xe00b,0x4016},
{0xe00d,0xe00d,0x4018},
{0xe00f,0xe011,0x401a},
{0xe017,0xe017,0x4022},
{0xe019,0xe019,0x4024},
{0xe01f,0xe01f,0x83a},
{0xe021,0xe021,0x402c},
{0xe024,0xe024,0x402f},
{0xe026,0xe026,0x9fd},
{0xe027,0xe02c,0x4032},
{0xe02f,0xe02f,0x403a},
{0xe031,0xe031,0x403c},
{0xe039,0xe03a,0x4044},
{0xe03d,0xe044,0x4048},
{0xe046,0xe046,0x4051},
{0xe047,0xe048,0x4946},
{0xe049,0xe04b,0x4054},
{0xe04d,0xe059,0x4058},
{0xe05b,0xe05b,0x30d},
{0xe05d,0xe05e,0x4068},
{0xe062,0xe062,0x406d},
{0xe063,0xe063,0x16b},
{0xe065,0xe065,0x4070},
{0xe067,0xe06b,0x4072},
{0xe06d,0xe070,0x4078},
{0xe072,0xe072,0x407d},
{0xe073,0xe073,0x860},
{0xe076,0xe077,0x4081},
{0xe079,0xe07c,0x4084},
{0xe07e,0xe086,0x4089},
{0xe088,0xe088,0x4093},
{0xe08b,0xe08b,0x4096},
{0xe08d,0xe092,0x4098},
{0xe095,0xe098,0x40a0},
{0xe09b,0xe09b,0x40a6},
{0xe09f,0xe0a4,0x40aa},
{0xe0a5,0xe0a5,0x3e82},
{0xe0a8,0xe0a8,0x40b3},
{0xe0aa,0xe0aa,0x40b5},
{0xe0ac,0xe0ad,0x40b7},
{0xe0af,0xe0af,0x40ba},
{0xe0b0,0xe0b0,0x4948},
{0xe0b1,0xe0b9,0x40bc},
{0xe0bb,0xe0bb,0x40c6},
{0xe0bd,0xe0bd,0x40c8},
{0xe0bf,0xe0c2,0x40ca},
{0xe0c4,0xe0c4,0x40cf},
{0xe0c6,0xe0c6,0x40d1},
{0xe0c9,0xe0c9,0x40d4},
{0xe0cb,0xe0cb,0x4949},
{0xe0cc,0xe0cf,0x40d7},
{0xe0d1,0xe0d3,0x40dc},
{0xe0d6,0xe0db,0x40e1},
{0xe0dd,0xe0dd,0x40e8},
{0xe0de,0xe0de,0x494a},
{0xe0e0,0xe0e1,0x40eb},
{0xe0e6,0xe0e6,0x40f1},
{0xe0ea,0xe0ea,0x40f5},
{0xe0ed,0xe0ed,0x40f8},
{0xe0ef,0xe0f1,0x40fa},
{0xe0f3,0xe0f3,0xc23},
{0xe0f4,0xe0f7,0x40ff},
{0xe0fa,0xe0fa,0x494b},
{0xe0fc,0xe0fc,0x4106},
{0xe0fe,0xe102,0x4108},
{0xe104,0xe105,0x410e},
{0xe107,0xe107,0x4111},
{0xe108,0xe108,0x494c},
{0xe10c,0xe10f,0x4115},
{0xe111,0xe113,0x411a},
{0xe116,0xe11b,0x411f},
{0xe11e,0xe120,0x4127},
{0xe122,0xe125,0x412b},
{0xe129,0xe12d,0x4132},
{0xe12e,0xe12e,0x17e4},
{0xe12f,0xe12f,0x4138},
{0xe132,0xe133,0x413b},
{0xe134,0xe134,0x3e8e},
{0xe135,0xe135,0x413e},
{0xe137,0xe138,0x4140},
{0xe13a,0xe140,0x4143},
{0xe143,0xe143,0x414c},
{0xe144,0xe144,0x494d},
{0xe146,0xe146,0x414e},
{0xe149,0xe149,0x212f},
{0xe14b,0xe14b,0x4152},
{0xe14c,0xe14c,0x494e},
{0xe14e,0xe14f,0x4154},
{0xe151,0xe153,0x4157},
{0xe158,0xe158,0x415e},
{0xe15b,0xe15c,0x4161},
{0xe15e,0xe161,0x4163},
{0xe163,0xe165,0x4168},
{0xe166,0xe166,0x4001},
{0xe167,0xe167,0x4950},
{0xe16b,0xe16e,0x4170},
{0xe16f,0xe16f,0x4951},
{0xe170,0xe170,0x4174},
{0xe173,0xe174,0x4177},
{0xe176,0xe177,0x417a},
{0xe17a,0xe17a,0x417e},
{0xe17d,0xe17e,0x4181},
{0xe181,0xe184,0x4185},
{0xe186,0xe186,0x418a},
{0xe188,0xe188,0x418c},
{0xe18a,0xe18a,0x418e},
{0xe18c,0xe18e,0x4190},
{0xe191,0xe191,0x115f},
{0xe192,0xe192,0x4195},
{0xe194,0xe194,0x4952},
{0xe197,0xe19e,0x4199},
{0xe1a1,0xe1a3,0x41a3},
{0xe1a5,0xe1a5,0x41a7},
{0xe1a7,0xe1a9,0x41a9},
{0xe1ab,0xe1ad,0x41ad},
{0xe1af,0xe1af,0x41b1},
{0xe1b2,0xe1b2,0x41b4},
{0xe1b5,0xe1b5,0x41b7},
{0xe1b7,0xe1b7,0x41b9},
{0xe1b9,0xe1b9,0x41bb},
{0xe1ba,0xe1ba,0xc79},
{0xe1c0,0xe1c0,0x41c1},
{0xe1c2,0xe1c3,0x41c3},
{0xe1c6,0xe1c6,0x4956},
{0xe1c7,0xe1c7,0x41c7},
{0xe1c9,0xe1c9,0x4c3},
{0xe1ca,0xe1cb,0x41ca},
{0xe1ce,0xe1d3,0x41ce},
{0xe1d6,0xe1d6,0x41d6},
{0xe1d8,0xe1d9,0x41d8},
{0xe1db,0xe1dc,0x41db},
{0xe1de,0xe1df,0x41de},
{0xe1e0,0xe1e0,0x4957},
{0xe1e1,0xe1e1,0x41e0},
{0xe1e3,0xe1e5,0x41e2},
{0xe1e7,0xe1ea,0x41e6},
{0xe1ec,0xe1ee,0x41eb},
{0xe1f1,0xe1f1,0x41f0},
{0xe1f3,0xe1f6,0x41f2},
{0xe1f8,0xe1f9,0x41f7},
{0xe1fb,0xe1fd,0x41fa},
{0xe1ff,0xe1ff,0x41fe},
{0xe203,0xe204,0x4202},
{0xe206,0xe206,0x4205},
{0xe208,0xe209,0x4207},
{0xe20d,0xe20f,0x420c},
{0xe211,0xe211,0x4210},
{0xe217,0xe218,0x4216},
{0xe21a,0xe21a,0x4219},
{0xe21d,0xe21e,0x421c},
{0xe222,0xe225,0x4221},
{0xe229,0xe229,0x4228},
{0xe22b,0xe22b,0x422a},
{0xe22c,0xe22c,0x18bd},
{0xe22d,0xe22d,0xcbd},
{0xe22e,0xe22e,0x422d},
{0xe230,0xe230,0xca5},
{0xe232,0xe232,0x4231},
{0xe234,0xe236,0x4233},
{0xe238,0xe23a,0x4237},
{0xe23c,0xe23c,0x423b},
{0xe23f,0xe244,0x423e},
{0xe246,0xe247,0x4245},
{0xe249,0xe249,0x4248},
{0xe250,0xe250,0x424f},
{0xe255,0xe257,0x4254},
{0xe258,0xe258,0x4959},
{0xe259,0xe260,0x4258},
{0xe262,0xe265,0x4261},
{0xe266,0xe266,0xcce},
{0xe267,0xe267,0x495a},
{0xe269,0xe26a,0x4267},
{0xe26c,0xe26c,0x426a},
{0xe270,0xe270,0x426e},
{0xe272,0xe279,0x4270},
{0xe27b,0xe27c,0x4279},
{0xe27e,0xe281,0x427c},
{0xe283,0xe285,0x4281},
{0xe286,0xe286,0x3d70},
{0xe287,0xe287,0x4285},
{0xe28a,0xe28a,0x4288},
{0xe28c,0xe28f,0x428a},
{0xe292,0xe293,0x4290},
{0xe297,0xe298,0x4295},
{0xe29c,0xe29e,0x429a},
{0xe2a0,0xe2a0,0x429e},
{0xe2a2,0xe2a2,0x429f},
{0xe2a3,0xe2a3,0xe84},
{0xe2a5,0xe2a5,0x42a2},
{0xe2a7,0xe2ab,0x42a4},
{0xe2ac,0xe2ac,0x495c},
{0xe2ad,0xe2b1,0x42a9},
{0xe2b6,0xe2b6,0x42b2},
{0xe2b9,0xe2b9,0x42b5},
{0xe2bb,0xe2bb,0x42b7},
{0xe2bc,0xe2bc,0x120},
{0xe2bd,0xe2bd,0x42b8},
{0xe2c0,0xe2c0,0x42bb},
{0xe2c2,0xe2c6,0x42bd},
{0xe2c8,0xe2c8,0x42c3},
{0xe2ca,0xe2d3,0x42c5},
{0xe2d5,0xe2d7,0x42d0},
{0xe2da,0xe2dd,0x42d5},
{0xe2df,0xe2df,0x42da},
{0xe2e3,0xe2e6,0x42de},
{0xe2e8,0xe2e8,0x42e3},
{0xe2ea,0xe2ea,0x42e5},
{0xe2ee,0xe2ee,0x42e9},
{0xe2ef,0xe2ef,0x1ba8},
{0xe2f2,0xe2f7,0x42eb},
{0xe2f9,0xe2fb,0x42f2},
{0xe2fd,0xe2fe,0x42f6},
{0xe302,0xe305,0x42f9},
{0xe307,0xe307,0x42fe},
{0xe309,0xe30b,0x4300},
{0xe30d,0xe30e,0x4304},
{0xe310,0xe310,0x4307},
{0xe312,0xe312,0x372c},
{0xe314,0xe314,0x372e},
{0xe316,0xe316,0x4698},
{0xe317,0xe317,0x3730},
{0xe319,0xe31a,0x3732},
{0xe31d,0xe31e,0x3736},
{0xe320,0xe324,0x3739},
{0xe326,0xe32b,0x373f},
{0xe32d,0xe32f,0x3746},
{0xe332,0xe334,0x374b},
{0xe337,0xe339,0x3750},
{0xe33a,0xe33a,0x1055},
{0xe33c,0xe33c,0x4699},
{0xe33e,0xe33f,0x3756},
{0xe340,0xe340,0x2de8},
{0xe341,0xe341,0x3759},
{0xe343,0xe344,0x375b},
{0xe346,0xe346,0x375e},
{0xe347,0xe347,0x469b},
{0xe349,0xe34b,0x3760},
{0xe34c,0xe34c,0x469c},
{0xe34d,0xe34e,0x3764},
{0xe34f,0xe34f,0x121},
{0xe350,0xe351,0x3766},
{0xe353,0xe353,0x3769},
{0xe355,0xe355,0x469d},
{0xe356,0xe359,0x376b},
{0xe35a,0xe35a,0x106b},
{0xe35d,0xe35f,0x3772},
{0xe361,0xe361,0x3776},
{0xe363,0xe363,0x1326},
{0xe366,0xe366,0x377b},
{0xe367,0xe367,0x469e},
{0xe368,0xe36e,0x377d},
{0xe370,0xe371,0x3785},
{0xe373,0xe373,0x3788},
{0xe376,0xe376,0x378b},
{0xe378,0xe378,0x469f},
{0xe37a,0xe37a,0x378e},
{0xe37c,0xe37c,0x66b},
{0xe37d,0xe37e,0x3791},
{0xe37f,0xe37f,0x132e},
{0xe380,0xe380,0x3794},
{0xe383,0xe385,0x3797},
{0xe387,0xe387,0x379b},
{0xe38b,0xe38c,0x379f},
{0xe38e,0xe390,0x37a2},
{0xe392,0xe392,0x37a6},
{0xe395,0xe395,0x37a8},
{0xe397,0xe397,0x37aa},
{0xe399,0xe399,0x37ac},
{0xe39c,0xe39d,0x37af},
{0xe3a1,0xe3a1,0x37b3},
{0xe3a3,0xe3a4,0x37b5},
{0xe3aa,0xe3ab,0x37bc},
{0xe3ad,0xe3b0,0x37bf},
{0xe3b3,0xe3b3,0x37c5},
{0xe3b6,0xe3b8,0x37c8},
{0xe3be,0xe3c2,0x37d0},
{0xe3c4,0xe3c4,0x37d6},
{0xe3c5,0xe3c5,0xd35},
{0xe3c6,0xe3c6,0x37d8},
{0xe3c7,0xe3c7,0x46a3},
{0xe3c9,0xe3cb,0x37da},
{0xe3ce,0xe3ce,0x37de},
{0xe3d0,0xe3d4,0x37e0},
{0xe3d6,0xe3d6,0x37e5},
{0xe3d7,0xe3d7,0x27c2},
{0xe3db,0xe3db,0x37ea},
{0xe3dc,0xe3dc,0x22ad},
{0xe3dd,0xe3e0,0x37ec},
{0xe3e2,0xe3e2,0x37f1},
{0xe3e4,0xe3e6,0x37f3},
{0xe3e8,0xe3ec,0x37f7},
{0xe3ef,0xe3ef,0x37fe},
{0xe3f1,0xe3f1,0x3800},
{0xe3f5,0xe3f7,0x3804},
{0xe3f9,0xe3f9,0x3808},
{0xe3fb,0xe3fb,0x380a},
{0xe3fc,0xe3fc,0x46a7},
{0xe3fe,0xe3fe,0x380d},
{0xe400,0xe408,0x380f},
{0xe40c,0xe410,0x381b},
{0xe411,0xe411,0x46a8},
{0xe412,0xe412,0x3820},
{0xe413,0xe413,0x46a9},
{0xe415,0xe415,0x3822},
{0xe416,0xe416,0x46aa},
{0xe417,0xe417,0x134c},
{0xe418,0xe418,0x3ff9},
{0xe419,0xe41a,0x3826},
{0xe41c,0xe41c,0x3829},
{0xe41f,0xe422,0x382c},
{0xe424,0xe424,0x3831},
{0xe427,0xe427,0x3833},
{0xe429,0xe42b,0x3835},
{0xe42e,0xe42e,0x383a},
{0xe42f,0xe42f,0x46ac},
{0xe430,0xe431,0x383c},
{0xe433,0xe435,0x383f},
{0xe437,0xe439,0x3843},
{0xe43d,0xe43d,0x3849},
{0xe43f,0xe43f,0x384b},
{0xe442,0xe443,0x384e},
{0xe445,0xe447,0x3851},
{0xe449,0xe449,0x3854},
{0xe44a,0xe44a,0x9ce},
{0xe44b,0xe44b,0x3856},
{0xe44f,0xe44f,0x385a},
{0xe452,0xe458,0x385d},
{0xe45b,0xe45c,0x3866},
{0xe45f,0xe45f,0x386a},
{0xe460,0xe460,0x46ae},
{0xe464,0xe464,0x386f},
{0xe466,0xe466,0x3871},
{0xe46b,0xe46b,0x3874},
{0xe46d,0xe46d,0x3876},
{0xe470,0xe470,0x3879},
{0xe473,0xe473,0x387c},
{0xe475,0xe475,0x387e},
{0xe477,0xe477,0x3880},
{0xe478,0xe478,0xbc7},
{0xe479,0xe479,0x3882},
{0xe47a,0xe47a,0x46b2},
{0xe47b,0xe47d,0x3883},
{0xe480,0xe483,0x3888},
{0xe485,0xe485,0x36e9},
{0xe48b,0xe48c,0x3892},
{0xe48f,0xe48f,0x46b3},
{0xe490,0xe490,0x3896},
{0xe493,0xe494,0x3899},
{0xe498,0xe4a0,0x389e},
{0xe4a5,0xe4aa,0x38aa},
{0xe4ac,0xe4ae,0x38b1},
{0xe4b0,0xe4b2,0x38b5},
{0xe4bb,0xe4c0,0x38c0},
{0xe4c2,0xe4c4,0x38c7},
{0xe4c5,0xe4c5,0x1391},
{0xe4c7,0xe4c8,0x38cc},
{0xe4ca,0xe4cc,0x38cf},
{0xe4cf,0xe4d5,0x38d4},
{0xe4d7,0xe4d8,0x38dc},
{0xe4da,0xe4da,0x16a4},
{0xe4db,0xe4db,0x38e0},
{0xe4de,0xe4e3,0x38e3},
{0xe4e5,0xe4e5,0x38ea},
{0xe4e9,0xe4ed,0x38ee},
{0xe4f0,0xe4f5,0x38f5},
{0xe4f7,0xe4f7,0x38fc},
{0xe4fa,0xe4fe,0x38ff},
{0xe500,0xe500,0x3905},
{0xe503,0xe504,0x3908},
{0xe506,0xe509,0x390b},
{0xe50b,0xe50c,0x3910},
{0xe50e,0xe50e,0x3912},
{0xe510,0xe511,0x3914},
{0xe513,0xe513,0x3917},
{0xe515,0xe515,0x3919},
{0xe517,0xe519,0x391a},
{0xe51b,0xe51b,0x391e},
{0xe522,0xe523,0x3925},
{0xe525,0xe525,0x3928},
{0xe526,0xe526,0x46b7},
{0xe527,0xe527,0x3929},
{0xe52d,0xe52d,0x392e},
{0xe530,0xe536,0x3931},
{0xe538,0xe53d,0x3939},
{0xe53f,0xe542,0x3940},
{0xe544,0xe544,0x3945},
{0xe545,0xe545,0x3072},
{0xe546,0xe54a,0x3947},
{0xe54c,0xe54d,0x394d},
{0xe54e,0xe54e,0x46b9},
{0xe54f,0xe553,0x3950},
{0xe556,0xe556,0x3957},
{0xe55a,0xe55a,0x395b},
{0xe55d,0xe55d,0x395e},
{0xe560,0xe561,0x3961},
{0xe563,0xe565,0x3964},
{0xe568,0xe56c,0x3969},
{0xe56f,0xe56f,0x3970},
{0xe571,0xe571,0x3972},
{0xe574,0xe574,0x3975},
{0xe578,0xe578,0x3979},
{0xe57b,0xe57b,0x397c},
{0xe57f,0xe57f,0x3980},
{0xe581,0xe581,0x3982},
{0xe587,0xe587,0x3988},
{0xe589,0xe589,0x3988},
{0xe58a,0xe58a,0x398b},
{0xe591,0xe591,0x3992},
{0xe595,0xe599,0x3996},
{0xe59b,0xe59c,0x399c},
{0xe5a2,0xe5a3,0x39a3},
{0xe5a5,0xe5a5,0x39a6},
{0xe5a9,0xe5a9,0x46ba},
{0xe5ab,0xe5ae,0x39ac},
{0xe5b0,0xe5b0,0x39b1},
{0xe5b3,0xe5b4,0x39b4},
{0xe5b6,0xe5b6,0x39b7},
{0xe5b9,0xe5b9,0x39ba},
{0xe5bc,0xe5bd,0x39bd},
{0xe5c1,0xe5c1,0x39c2},
{0xe5c3,0xe5c7,0x39c4},
{0xe5ca,0xe5d1,0x39cb},
{0xe5d2,0xe5d3,0x119},
{0xe5d4,0xe5d4,0x11c},
{0xe5d5,0xe5d5,0x11b},
{0xe5d6,0xe5d6,0x39d3},
{0xe5da,0xe5da,0x39d7},
{0xe5dd,0xe5de,0x39da},
{0xe5e0,0xe5e0,0x39dd},
{0xe5e4,0xe5e4,0x39e1},
{0xe5e7,0xe5e8,0x39e4},
{0xe5ea,0xe5ea,0x39e7},
{0xe5eb,0xe5eb,0x3fac},
{0xe5ec,0xe5ed,0x39e9},
{0xe5f2,0xe5f2,0x39ef},
{0xe5f4,0xe5f4,0x297c},
{0xe5f5,0xe5fa,0x39f2},
{0xe5fd,0xe5ff,0x39fa},
{0xe601,0xe601,0x39fe},
{0xe603,0xe603,0x3a00},
{0xe605,0xe607,0x3a02},
{0xe608,0xe608,0x46bc},
{0xe609,0xe60e,0x3a05},
{0xe613,0xe614,0x3a0f},
{0xe615,0xe615,0x46bd},
{0xe616,0xe618,0x3a11},
{0xe61d,0xe620,0x3a18},
{0xe622,0xe622,0x3a1d},
{0xe625,0xe626,0x3a20},
{0xe628,0xe62a,0x3a23},
{0xe62c,0xe631,0x3a27},
{0xe635,0xe636,0x3a30},
{0xe639,0xe639,0x3a34},
{0xe63b,0xe63b,0x3a36},
{0xe63d,0xe63e,0x3a38},
{0xe643,0xe644,0x3a3e},
{0xe647,0xe647,0x3a42},
{0xe649,0xe649,0x3a44},
{0xe64a,0xe64a,0x46be},
{0xe64c,0xe64d,0x3a46},
{0xe650,0xe651,0x3a4a},
{0xe656,0xe656,0x3a50},
{0xe65a,0xe65c,0x3a54},
{0xe661,0xe661,0x3a5b},
{0xe664,0xe664,0x3a5e},
{0xe669,0xe669,0x3a63},
{0xe66b,0xe66d,0x3a64},
{0xe670,0xe670,0x3a69},
{0xe672,0xe675,0x3a6b},
{0xe67a,0xe67a,0x3a73},
{0xe67e,0xe67e,0x3a77},
{0xe681,0xe681,0x3a7a},
{0xe682,0xe682,0x46c0},
{0xe689,0xe689,0x3a81},
{0xe68d,0xe68d,0x3a85},
{0xe691,0xe691,0x3a89},
{0xe693,0xe695,0x3a8b},
{0xe698,0xe698,0x3a90},
{0xe69b,0xe69b,0x3a93},
{0xe69d,0xe69d,0x3a95},
{0xe6a0,0xe6a0,0x3a98},
{0xe6a3,0xe6a4,0x3a9b},
{0xe6a5,0xe6a5,0x46c1},
{0xe6a7,0xe6a7,0x3a9e},
{0xe6a8,0xe6a8,0x46c2},
{0xe6a9,0xe6aa,0x3aa0},
{0xe6ab,0xe6ab,0x46c3},
{0xe6ae,0xe6b1,0x3aa4},
{0xe6b3,0xe6b8,0x3aa9},
{0xe6bb,0xe6bc,0x3ab1},
{0xe6bf,0xe6bf,0x3ab5},
{0xe6c2,0xe6c4,0x3ab8},
{0xe6c6,0xe6c6,0x1d06},
{0xe6c8,0xe6c9,0x3abd},
{0xe6cb,0xe6d0,0x3ac0},
{0xe6d3,0xe6d6,0x3ac8},
{0xe6d9,0xe6d9,0x3ace},
{0xe6dc,0xe6dc,0x3ad1},
{0xe6e2,0xe6e3,0x3ad7},
{0xe6e7,0xe6ed,0x3adc},
{0xe6ef,0xe6f1,0x3ae4},
{0xe6f4,0xe6f6,0x3ae9},
{0xe6f8,0xe6f8,0x46c5},
{0xe6f9,0xe6f9,0x3aee},
{0xe6fd,0xe6fe,0x3af2},
{0xe700,0xe700,0x3af5},
{0xe703,0xe705,0x3af8},
{0xe708,0xe708,0x3afd},
{0xe70b,0xe70b,0x3b00},
{0xe70d,0xe70f,0x3b02},
{0xe712,0xe712,0x3b07},
{0xe714,0xe716,0x3b09},
{0xe719,0xe719,0x3b0e},
{0xe71c,0xe71c,0x3b11},
{0xe71e,0xe723,0x3b13},
{0xe726,0xe726,0x3b1b},
{0xe727,0xe727,0x29a1},
{0xe728,0xe728,0x46c6},
{0xe729,0xe72a,0x3b1e},
{0xe72c,0xe72e,0x3b21},
{0xe730,0xe732,0x3b25},
{0xe738,0xe73b,0x3b2d},
{0xe73e,0xe73f,0x3b33},
{0xe743,0xe743,0x3b38},
{0xe745,0xe746,0x3b3a},
{0xe748,0xe74c,0x3b3d},
{0xe750,0xe750,0x3b45},
{0xe753,0xe75a,0x3b48},
{0xe75e,0xe75e,0x3b53},
{0xe760,0xe760,0x3b55},
{0xe763,0xe763,0x3b58},
{0xe766,0xe766,0x3b5b},
{0xe76a,0xe76b,0x3b5e},
{0xe76f,0xe76f,0x3b63},
{0xe771,0xe774,0x3b65},
{0xe776,0xe776,0x46c8},
{0xe779,0xe779,0x3b6c},
{0xe77b,0xe77b,0x46c9},
{0xe77e,0xe783,0x3b71},
{0xe785,0xe785,0x3b78},
{0xe787,0xe788,0x3b7a},
{0xe78a,0xe78a,0x3b7d},
{0xe78c,0xe78c,0x3b7f},
{0xe78e,0xe78f,0x3b81},
{0xe791,0xe791,0x3b84},
{0xe793,0xe793,0x3b86},
{0xe795,0xe795,0x3b88},
{0xe798,0xe798,0x3b8b},
{0xe79a,0xe7a1,0x3b8d},
{0xe7a3,0xe7a3,0x3b96},
{0xe7aa,0xe7b0,0x3b9d},
{0xe7b2,0xe7b3,0x3ba5},
{0xe7b5,0xe7b7,0x3ba8},
{0xe7b9,0xe7bb,0x3bac},
{0xe7bd,0xe7bf,0x3bb0},
{0xe7c0,0xe7c0,0x46ca},
{0xe7c1,0xe7c3,0x3bb3},
{0xe7c5,0xe7c9,0x3bb7},
{0xe7cb,0xe7cd,0x3bbd},
{0xe7cf,0xe7d0,0x3bc1},
{0xe7d2,0xe7d2,0x3bc4},
{0xe7d3,0xe7d3,0x181b},
{0xe7d4,0xe7d7,0x3bc6},
{0xe7d9,0xe7d9,0x3bcb},
{0xe7db,0xe7db,0x3bcd},
{0xe7de,0xe7e3,0x3bd0},
{0xe7e5,0xe7e6,0x3bd7},
{0xe7ea,0xe7ea,0x3bdc},
{0xe7ec,0xe7ec,0x3bde},
{0xe7f0,0xe7f0,0x3be2},
{0xe7f4,0xe7f7,0x3be6},
{0xe7f9,0xe7fa,0x3beb},
{0xe7fd,0xe7fd,0x3e2f},
{0xe7fe,0xe7fe,0x3bf0},
{0xe803,0xe807,0x3bf5},
{0xe809,0xe809,0x3bfb},
{0xe80a,0xe80a,0x46cb},
{0xe80b,0xe80b,0x3bfd},
{0xe80d,0xe80d,0x3bff},
{0xe810,0xe810,0x3c02},
{0xe812,0xe812,0x3c04},
{0xe814,0xe815,0x3c06},
{0xe819,0xe819,0x3c0b},
{0xe81b,0xe820,0x3c0d},
{0xe822,0xe822,0x3c14},
{0xe823,0xe823,0x46cc},
{0xe824,0xe826,0x3c16},
{0xe829,0xe82c,0x3c1b},
{0xe82e,0xe82e,0x3c20},
{0xe830,0xe834,0x3c22},
{0xe836,0xe836,0x3c28},
{0xe839,0xe839,0x3c2b},
{0xe83c,0xe840,0x3c2e},
{0xe843,0xe84a,0x3c35},
{0xe84c,0xe84c,0x3c3e},
{0xe84f,0xe850,0x3c41},
{0xe852,0xe854,0x3c44},
{0xe859,0xe85a,0x3c4b},
{0xe85e,0xe85f,0x3c50},
{0xe863,0xe864,0x3c55},
{0xe868,0xe868,0x3c5a},
{0xe86b,0xe86b,0x46cd},
{0xe86d,0xe870,0x3c5e},
{0xe872,0xe877,0x3c63},
{0xe879,0xe87c,0x3c6a},
{0xe880,0xe883,0x3c71},
{0xe884,0xe884,0x3c76},
{0xe885,0xe886,0x3c76},
{0xe888,0xe88e,0x3c79},
{0xe890,0xe890,0x3c81},
{0xe893,0xe893,0x2b24},
{0xe896,0xe899,0x3c87},
{0xe89b,0xe89c,0x3c8c},
{0xe89e,0xe89f,0x3c8f},
{0xe8a2,0xe8a2,0x3c93},
{0xe8a8,0xe8a8,0x3c99},
{0xe8ab,0xe8ab,0x3c9c},
{0xe8b1,0xe8b4,0x3ca2},
{0xe8b7,0xe8b7,0x3ca8},
{0xe8b9,0xe8bc,0x3caa},
{0xe8c4,0xe8c4,0x3cb5},
{0xe8c6,0xe8c6,0x3cb7},
{0xe8c8,0xe8ca,0x3cb9},
{0xe8cc,0xe8cc,0x3cbd},
{0xe8cf,0xe8cf,0x3cc0},
{0xe8d2,0xe8d2,0x3cc3},
{0xe8d4,0xe8d4,0x3cc5},
{0xe8d7,0xe8d7,0x3cc8},
{0xe8db,0xe8dc,0x3ccc},
{0xe8de,0xe8de,0x3ccf},
{0xe8e2,0xe8e4,0x3cd3},
{0xe8e7,0xe8e7,0x3cd8},
{0xe8eb,0xe8ec,0x3cdc},
{0xe8ee,0xe8f0,0x3cdf},
{0xe8f3,0xe8f4,0x3ce4},
{0xe8f6,0xe8fa,0x3ce7},
{0xe8fc,0xe8fd,0x3ced},
{0xe8ff,0xe8ff,0x3cf0},
{0xe901,0xe902,0x3cf2},
{0xe904,0xe906,0x3cf5},
{0xe909,0xe90b,0x3cfa},
{0xe90d,0xe916,0x3cfe},
{0xe919,0xe91b,0x3d0a},
{0xe91d,0xe91d,0x3d0e},
{0xe923,0xe924,0x3d14},
{0xe92d,0xe92d,0x3d1e},
{0xe930,0xe932,0x3d21},
{0xe937,0xe937,0x46ce},
{0xe939,0xe939,0x3d2a},
{0xe93b,0xe93b,0x3d2c},
{0xe93f,0xe940,0x3d30},
{0xe942,0xe943,0x3d33},
{0xe946,0xe947,0x3d37},
{0xe949,0xe94c,0x3d3a},
{0xe94e,0xe94f,0x3d3f},
{0xe951,0xe951,0x3d42},
{0xe956,0xe956,0x3d47},
{0xe959,0xe959,0x3d4a},
{0xe95b,0xe95b,0x3d4c},
{0xe95f,0xe95f,0x3d50},
{0xe962,0xe962,0x46d0},
{0xe963,0xe963,0x3d54},
{0xe965,0xe966,0x3d56},
{0xe969,0xe969,0x46d2},
{0xe96a,0xe96a,0x3d59},
{0xe96b,0xe96c,0x46d3},
{0xe96d,0xe96d,0x3d5a},
{0xe970,0xe972,0x46d7},
{0xe976,0xe976,0x46dc},
{0xe979,0xe979,0x46df},
{0xe97c,0xe97c,0x46e2},
{0xe97e,0xe97f,0x46e4},
{0xe980,0xe980,0x3d5c},
{0xe983,0xe984,0x46e8},
{0xe985,0xe985,0x43ca},
{0xe986,0xe986,0x46ea},
{0xe988,0xe988,0x46eb},
{0xe989,0xe989,0x43cc},
{0xe98b,0xe98b,0x46ec},
{0xe98c,0xe98c,0x43fa},
{0xe98d,0xe98d,0x46ed},
{0xe98f,0xe98f,0x3d60},
{0xe990,0xe990,0x46ee},
{0xe992,0xe992,0x46f0},
{0xe993,0xe993,0x3d61},
{0xe994,0xe994,0x46f1},
{0xe995,0xe995,0x3d62},
{0xe996,0xe996,0x46f2},
{0xe997,0xe998,0x3d63},
{0xe999,0xe9a2,0x46f3},
{0xe9a3,0xe9a3,0x43cd},
{0xe9a4,0xe9a8,0x46fd},
{0xe9a9,0xe9aa,0x43ce},
{0xe9ab,0xe9ab,0x4702},
{0xe9ac,0xe9ac,0x43d1},
{0xe9ad,0xe9af,0x4703},
{0xe9b1,0xe9b3,0x4706},
{0xe9b4,0xe9b4,0x3d65},
{0xe9b5,0xe9b7,0x4709},
{0xe9b8,0xe9b8,0x3d66},
{0xe9b9,0xe9bd,0x470c},
{0xe9be,0xe9be,0x3d67},
{0xe9bf,0xe9c0,0x4711},
{0xe9c1,0xe9c1,0x3d68},
{0xe9c2,0xe9c2,0x4713},
{0xe9c3,0xe9c3,0x3d69},
{0xe9c4,0xe9c4,0x4714},
{0xe9c5,0xe9c6,0x43d5},
{0xe9c7,0xe9ca,0x4715},
{0xe9cc,0xe9cc,0x471a},
{0xe9cd,0xe9cd,0x3d6a},
{0xe9ce,0xe9ce,0x471b},
{0xe9cf,0xe9cf,0x43d7},
{0xe9d0,0xe9d1,0x471c},
{0xe9d2,0xe9d2,0x43fc},
{0xe9d3,0xe9d3,0x3d6b},
{0xe9d4,0xe9d4,0x471e},
{0xe9d5,0xe9d5,0x3d6c},
{0xe9d6,0xe9d6,0x471f},
{0xe9d7,0xe9d7,0x43d8},
{0xe9d8,0xe9de,0x4720},
{0xe9df,0xe9df,0x3d6d},
{0xe9e0,0xe9e3,0x4727},
{0xe9e4,0xe9e4,0x43d9},
{0xe9e5,0xe9eb,0x472b},
{0xe9ec,0xe9ec,0x43da},
{0xe9ed,0xe9ee,0x4732},
{0xe9f0,0xe9f3,0x4735},
{0xe9f4,0xe9f4,0x43dc},
{0xe9f5,0xe9f9,0x4739},
{0xe9fa,0xe9fa,0x3d6e},
{0xe9fb,0xe9fd,0x473e},
{0xe9fe,0xe9fe,0x3d6f},
{0xe9ff,0xea04,0x4741},
{0xea05,0xea05,0x3d70},
{0xea06,0xea07,0x4747},
{0xea08,0xea08,0x3d71},
{0xea09,0xea0f,0x4749},
{0xea10,0xea10,0x3d72},
{0xea11,0xea11,0x4750},
{0xea12,0xea12,0x43c5},
{0xea13,0xea13,0x4751},
{0xea14,0xea14,0x43c6},
{0xea15,0xea1b,0x4752},
{0xea1c,0xea1c,0x3d73},
{0xea1d,0xea1f,0x4759},
{0xea20,0xea20,0x43de},
{0xea21,0xea23,0x475c},
{0xea24,0xea24,0x3d74},
{0xea25,0xea27,0x475f},
{0xea28,0xea28,0x3d75},
{0xea29,0xea37,0x4762},
{0xea38,0xea38,0x43e0},
{0xea39,0xea3a,0x4771},
{0xea3b,0xea3b,0x43e2},
{0xea3c,0xea40,0x4773},
{0xea41,0xea41,0x43e3},
{0xea42,0xea42,0x4778},
{0xea44,0xea44,0x43e5},
{0xea45,0xea4c,0x477a},
{0xea4d,0xea4d,0x43df},
{0xea4e,0xea4f,0x4782},
{0xea50,0xea50,0x3d76},
{0xea51,0xea51,0x4784},
{0xea52,0xea52,0x43ab},
{0xea53,0xea53,0x4785},
{0xea54,0xea54,0x43e7},
{0xea55,0xea55,0x4786},
{0xea56,0xea56,0x43e9},
{0xea57,0xea5c,0x4787},
{0xea5d,0xea5d,0x43fd},
{0xea5e,0xea61,0x478d},
{0xea62,0xea62,0x3d77},
{0xea63,0xea76,0x4791},
{0xea77,0xea77,0x3d78},
{0xea78,0xea78,0x47a5},
{0xea79,0xea79,0x3d79},
{0xea7a,0xea85,0x47a6},
{0xea86,0xea86,0x3d7a},
{0xea87,0xea8b,0x47b2},
{0xea8c,0xea8c,0x43af},
{0xea8d,0xea8d,0x47b7},
{0xea8e,0xea8e,0x3d7b},
{0xea8f,0xea92,0x47b8},
{0xea93,0xea93,0x43ed},
{0xea94,0xea94,0x47bc},
{0xea95,0xea95,0x3d7c},
{0xea96,0xea96,0x43ee},
{0xea97,0xea97,0x47bd},
{0xea98,0xea98,0x43ff},
{0xea99,0xea9f,0x47be},
{0xeaa0,0xeaa0,0x3d7d},
{0xeaa1,0xeaa1,0x47c5},
{0xeaa2,0xeaa2,0x43f1},
{0xeaa3,0xeaa8,0x47c6},
{0xeaaa,0xeaaa,0x47cd},
{0xeaab,0xeaab,0x3d7e},
{0xeaac,0xeaad,0x47ce},
{0xeaae,0xeaae,0x43f3},
{0xeaaf,0xeaaf,0x47d0},
{0xeab0,0xeab0,0x43f2},
{0xeab1,0xeab3,0x47d1},
{0xeab4,0xeab4,0x43f8},
{0xeab5,0xeab5,0x43f4},
{0xeab6,0xeabc,0x47d4},
{0xeabd,0xeabd,0x3d7f},
{0xeabe,0xeabe,0x47db},
{0xeac2,0xeac2,0x3d80},
{0xeac3,0xeac3,0x47df},
{0xeac4,0xeac4,0x3d81},
{0xeac6,0xeac6,0x3d82},
{0xeac7,0xeac7,0x47e1},
{0xeac8,0xeac8,0x43b7},
{0xeacd,0xeace,0x47e6},
{0xead2,0xead2,0x3d83},
{0xead5,0xead5,0x47ed},
{0xeadb,0xeadb,0x3d84},
{0xeadd,0xeadd,0x3d85},
{0xeae4,0xeae6,0x3d86},
{0xeaed,0xeaed,0x3d89},
{0xeaef,0xeaef,0x3d8a},
{0xeaf3,0xeaf3,0x3d8b},
{0xeaf7,0xeaf7,0x4806},
{0xeafc,0xeafc,0x480a},
{0xeafd,0xeafd,0x3d8c},
{0xeb01,0xeb01,0x3d8d},
{0xeb06,0xeb06,0x3d8e},
{0xeb07,0xeb07,0x4812},
{0xeb10,0xeb10,0x3d8f},
{0xeb13,0xeb13,0x481d},
{0xeb14,0xeb14,0x3d90},
{0xeb16,0xeb17,0x481f},
{0xeb1e,0xeb1e,0x3d92},
{0xeb22,0xeb22,0x3d93},
{0xeb24,0xeb24,0x3d94},
{0xeb26,0xeb26,0x3d95},
{0xeb28,0xeb29,0x3d96},
{0xeb3a,0xeb3c,0x3d98},
{0xeb40,0xeb40,0x2f50},
{0xeb42,0xeb42,0x1725},
{0xeb45,0xeb45,0x32ed},
{0xeb47,0xeb47,0x4840},
{0xeb48,0xeb49,0x3da1},
{0xeb51,0xeb52,0x3da7},
{0xeb5c,0xeb5c,0x3dad},
{0xeb5e,0xeb5e,0x3daf},
{0xeb60,0xeb60,0x3db1},
{0xeb64,0xeb64,0x484b},
{0xeb66,0xeb66,0x3db3},
{0xeb6a,0xeb6a,0x3db6},
{0xeb6c,0xeb6c,0x3db8},
{0xeb6e,0xeb6e,0x2ad9},
{0xeb72,0xeb72,0x3dbb},
{0xeb75,0xeb75,0x4852},
{0xeb79,0xeb79,0x3dbe},
{0xeb7b,0xeb7b,0x3dbf},
{0xeb7c,0xeb7c,0x4855},
{0xeb7d,0xeb7d,0x3dc0},
{0xeb82,0xeb82,0x3dc3},
{0xeb86,0xeb86,0x1c14},
{0xeb87,0xeb87,0x3dc5},
{0xeb8b,0xeb8b,0x3dc7},
{0xeb91,0xeb92,0x3dc9},
{0xeb94,0xeb94,0x41fa},
{0xeb96,0xeb96,0x3dcd},
{0xeb9c,0xeb9d,0x4864},
{0xeb9e,0xeb9e,0x12e9},
{0xeba1,0xeba1,0x3dd2},
{0xeba4,0xeba4,0x3dd4},
{0xeba7,0xeba7,0x3dd5},
{0xeba9,0xeba9,0x2cae},
{0xebac,0xebac,0x3dd9},
{0xebb1,0xebb1,0x3ddc},
{0xebb5,0xebb5,0x3dde},
{0xebb7,0xebb8,0x3de0},
{0xebba,0xebba,0x1a64},
{0xebbb,0xebbb,0x486f},
{0xebbe,0xebbe,0x3de5},
{0xebc0,0xebc1,0x3de7},
{0xebc3,0xebc3,0x4871},
{0xebc4,0xebc4,0x3dea},
{0xebc9,0xebc9,0x1404},
{0xebcb,0xebcb,0x3ded},
{0xebcf,0xebcf,0x2324},
{0xebd1,0xebd1,0x3df1},
{0xebd2,0xebd2,0x346a},
{0xebd4,0xebd4,0x3df2},
{0xebd6,0xebd6,0x3df3},
{0xebd9,0xebd9,0x3df6},
{0xebdc,0xebdc,0x3df7},
{0xebde,0xebde,0x2291},
{0xebe0,0xebe0,0x3dfa},
{0xebe2,0xebe3,0x3dfb},
{0xebe5,0xebe6,0x3dfd},
{0xebea,0xebea,0x3e00},
{0xebed,0xebed,0x3e01},
{0xebf0,0xebf0,0x3e03},
{0xebf3,0xebf3,0x3e05},
{0xebf4,0xebf4,0x4885},
{0xebf7,0xebf8,0x3e07},
{0xebfc,0xebfc,0x3e0c},
{0xebff,0xebff,0x3e0f},
{0xec00,0xec00,0x4887},
{0xec01,0xec01,0x1787},
{0xec02,0xec02,0x95f},
{0xec03,0xec04,0x3e12},
{0xec07,0xec07,0x3e16},
{0xec0a,0xec0a,0x488a},
{0xec0b,0xec0b,0x3e17},
{0xec0d,0xec0d,0x3e19},
{0xec11,0xec12,0x3e1d},
{0xec15,0xec15,0x1e99},
{0xec16,0xec16,0x3e21},
{0xec1e,0xec1f,0x3e25},
{0xec21,0xec21,0x3e27},
{0xec23,0xec23,0x3e29},
{0xec25,0xec25,0x3e2b},
{0xec27,0xec29,0x3e2d},
{0xec30,0xec30,0x3e34},
{0xec33,0xec34,0x3e36},
{0xec36,0xec36,0x3e39},
{0xec38,0xec3a,0x3e3b},
{0xec3d,0xec3e,0x3e40},
{0xec4a,0xec4a,0x3e43},
{0xec4d,0xec4d,0x3e44},
{0xec4e,0xec4e,0x489f},
{0xec50,0xec51,0x3e46},
{0xec53,0xec53,0x3e48},
{0xec55,0xec55,0x3e49},
{0xec59,0xec59,0x3e4a},
{0xec5b,0xec5b,0x25c1},
{0xec5e,0xec5e,0xd0c},
{0xec61,0xec61,0x48ab},
{0xec64,0xec64,0x48ae},
{0xec65,0xec65,0x43c9},
{0xec66,0xec66,0x3e4c},
{0xec74,0xec74,0x48bb},
{0xec77,0xec78,0x48be},
{0xec7a,0xec7a,0x48c1},
{0xec7c,0xec7c,0x43f5},
{0xec7d,0xec7d,0x3e4e},
{0xec7f,0xec7f,0x48c4},
{0xec81,0xec81,0x48c6},
{0xec82,0xec82,0x3e4f},
{0xec83,0xec83,0x48c7},
{0xec85,0xec85,0x48c9},
{0xec87,0xec8a,0x3e50},
{0xec8b,0xec8b,0x48cb},
{0xec8c,0xec8c,0x3e54},
{0xec8f,0xec8f,0x3e56},
{0xec90,0xec90,0x48cd},
{0xec97,0xec97,0x3e5a},
{0xec9c,0xec9c,0x48d5},
{0xec9f,0xec9f,0x3e5d},
{0xeca1,0xeca2,0x3e5e},
{0xeca5,0xeca5,0x3e60},
{0xeca6,0xeca6,0x5e6},
{0xeca9,0xecaa,0x3e62},
{0xecac,0xecac,0x3e64},
{0xecae,0xecae,0x48de},
{0xecb0,0xecb3,0x3e66},
{0xecb5,0xecb5,0x3e6a},
{0xecb7,0xecb7,0x3e6c},
{0xecb8,0xecb8,0x48e0},
{0xecbc,0xecce,0x3e6d},
{0xecd0,0xecd3,0x3e81},
{0xecd5,0xecda,0x3e86},
{0xecdd,0xecdd,0x3e8e},
{0xece2,0xece3,0x3e92},
{0xece4,0xece4,0x48e5},
{0xece6,0xecef,0x3e96},
{0xecf1,0xecf1,0x3ea1},
{0xecf3,0xecf4,0x3ea3},
{0xecf6,0xecfb,0x3ea6},
{0xecfd,0xecff,0x3ead},
{0xed00,0xed00,0x48e6},
{0xed01,0xed03,0x3eb1},
{0xed05,0xed06,0x3eb4},
{0xed08,0xed08,0x48e9},
{0xed09,0xed09,0x3eb6},
{0xed0b,0xed0b,0x3eb7},
{0xed12,0xed12,0x3eb8},
{0xed14,0xed14,0x3eb9},
{0xed19,0xed19,0x3ebb},
{0xed1b,0xed1b,0x3ebc},
{0xed1f,0xed21,0x3ebe},
{0xed23,0xed23,0x3ec1},
{0xed28,0xed28,0x728},
{0xed2b,0xed2b,0x3ec6},
{0xed2e,0xed30,0x3ec8},
{0xed31,0xed32,0x48fd},
{0xed33,0xed33,0x3ecb},
{0xed34,0xed34,0x48ff},
{0xed35,0xed36,0x3ecc},
{0xed37,0xed37,0x4900},
{0xed38,0xed38,0x3ece},
{0xed39,0xed3a,0x4901},
{0xed3c,0xed3c,0x4903},
{0xed3e,0xed3f,0x3ed0},
{0xed40,0xed40,0x4905},
{0xed43,0xed44,0x3ed2},
{0xed46,0xed46,0x3ed4},
{0xed48,0xed49,0x3ed6},
{0xed4c,0xed4c,0x3ed9},
{0xed50,0xed50,0x3edb},
{0xed55,0xed56,0x3edd},
{0xed59,0xed5e,0x3edf},
{0xed60,0xed61,0x3ee6},
{0xed63,0xed6a,0x3ee9},
{0xed6c,0xed6d,0x3ef2},
{0xed6e,0xed6e,0x24b6},
{0xed6f,0xed70,0x3ef5},
{0xed73,0xed74,0x3ef8},
{0xed76,0xed76,0x4914},
{0xed79,0xed79,0x3efb},
{0xed7b,0xed7b,0x3efc},
{0xed7c,0xed7c,0x1806},
{0xed7d,0xed80,0x3efe},
{0xed82,0xed83,0x3f02},
{0xed87,0xed87,0x3f06},
{0xed8c,0xed8d,0x3f09},
{0xed8f,0xed90,0x3f0c},
{0xed92,0xed9d,0x3f0f},
{0xed9e,0xed9e,0x3511},
{0xed9f,0xeda3,0x3f1c},
{0xeda4,0xeda4,0x3945},
{0xeda5,0xeda5,0x491b},
{0xeda7,0xedad,0x3f23},
{0xedae,0xedae,0x491d},
{0xedaf,0xedc5,0x3f2a},
{0xedc7,0xedce,0x3f42},
{0xedd0,0xedd0,0x3f4b},
{0xedd2,0xedd6,0x3f4c},
{0xedd8,0xedd9,0x3f51},
{0xeddc,0xeddc,0x3f54},
{0xedde,0xede0,0x3f55},
{0xede2,0xede6,0x3f59},
{0xede7,0xede7,0xf82},
{0xede8,0xede8,0x4922},
{0xede9,0xedec,0x3f60},
{0xedee,0xedef,0x3f65},
{0xedf0,0xedf0,0x4923},
{0xedf1,0xedf3,0x3f68},
{0xedf4,0xedf4,0x3a3e},
{0xedf5,0xedff,0x3f6c},
{0xee00,0xee00,0x4924},
{0xee01,0xee07,0x3f77},
{0xee09,0xee10,0x3f7f},
{0xee12,0xee14,0x3f88},
{0xee16,0xee1a,0x3f8b},
{0xee1c,0xee1f,0x3f90},
{0xee21,0xee21,0x3f95},
{0xee23,0xee29,0x3f96},
{0xee2b,0xee2f,0x3f9e},
{0xee32,0xee33,0x3fa4},
{0xee35,0xee3d,0x3fa7},
{0xee3e,0xee3e,0x3811},
{0xee3f,0xee47,0x3fb1},
{0xee49,0xee51,0x3fba},
{0xee52,0xee52,0x5f2},
{0xee53,0xee55,0x3fc4},
{0xee57,0xee5a,0x3fc7},
{0xee5c,0xee5c,0x3fcb},
{0xee5e,0xee5f,0x3fcd},
{0xee61,0xee63,0x3fcf},
{0xee65,0xee69,0x3fd3},
{0xee6b,0xee75,0x3fd9},
{0xee77,0xee7b,0x3fe5},
{0xee7d,0xee7d,0x3feb},
{0xee7f,0xee88,0x3fec},
{0xee89,0xee89,0x492e},
{0xee8a,0xee8d,0x3ff6},
{0xee8e,0xee8e,0x3aee},
{0xee8f,0xee90,0x3ffb},
{0xee92,0xee97,0x3ffe},
{0xee98,0xee98,0x247d},
{0xee99,0xee9b,0x4005},
{0xee9d,0xee9d,0x3ac9},
{0xee9e,0xee9f,0x4009},
{0xeea0,0xeea0,0x4930},
{0xeeab,0xeeab,0x43b8},
{0xeeb2,0xeeb3,0x4940},
{0xeeb5,0xeeb6,0x4943},
{0xf319,0xf324,0x4961},
{0xf326,0xf326,0x496e},
{0xf328,0xf343,0x4970},
{0xf345,0xf345,0x498d},
{0xf347,0xf349,0x498f},
{0xf3a3,0xf3a3,0x4536},
{0xf3a6,0xf3a9,0x4537},
{0xf3ad,0xf3ad,0x43c3},
{0xf3ae,0xf3b0,0x453c},
{0xf3b1,0xf3b1,0x439a},
{0xf3b2,0xf3e3,0x453f},
{0xf3e4,0xf3e4,0x43a2},
{0xf3e9,0xf3e9,0x43ec},
{0xf3ea,0xf3ea,0x4571},
{0xf3eb,0xf3eb,0x43eb},
{0xf3ec,0xf3ec,0x4572},
{0xf3ee,0xf3ef,0x4573},
{0xf3f3,0xf3f8,0x4576},
{0xf3fb,0xf3fb,0x457e},
{0xf3fd,0xf3fd,0x4580},
{0xf3ff,0xf401,0x4581},
{0xf403,0xf407,0x4584},
{0xf40c,0xf40c,0x458d},
{0xf40d,0xf40d,0x43bc},
{0xf40e,0xf416,0x458e},
{0xf417,0xf417,0x439c},
{0xf418,0xf418,0x4597},
{0xf419,0xf419,0x439e},
{0xf41a,0xf41a,0x4598},
{0xf41b,0xf41b,0x439f},
{0xf41c,0xf41e,0x4599},
{0xf41f,0xf41f,0x43a1},
{0xf420,0xf420,0x459c},
{0xf421,0xf421,0x43a3},
{0xf422,0xf427,0x459d},
{0xf428,0xf429,0x43a5},
{0xf42a,0xf437,0x45a3},
{0xf438,0xf438,0x43a9},
{0xf439,0xf43c,0x45b1},
{0xf43e,0xf43e,0x4309},
{0xf443,0xf443,0x430e},
{0xf445,0xf445,0x4310},
{0xf447,0xf447,0x4312},
{0xf44a,0xf44a,0x45b6},
{0xf44c,0xf44c,0x4317},
{0xf44e,0xf44f,0x4319},
{0xf453,0xf453,0x431e},
{0xf458,0xf459,0x4323},
{0xf464,0xf464,0x432f},
{0xf467,0xf468,0x4332},
{0xf46a,0xf46b,0x4335},
{0xf470,0xf471,0x433b},
{0xf473,0xf473,0x433e},
{0xf475,0xf476,0x4340},
{0xf47a,0xf47a,0x4345},
{0xf47f,0xf47f,0x434a},
{0xf482,0xf482,0x434d},
{0xf487,0xf487,0x4352},
{0xf489,0xf48a,0x4354},
{0xf492,0xf492,0x435d},
{0xf493,0xf493,0x45c0},
{0xf494,0xf494,0x435f},
{0xf496,0xf496,0x4361},
{0xf499,0xf49a,0x4364},
{0xf49c,0xf49d,0x4367},
{0xf49f,0xf4a2,0x436a},
{0xf4ab,0xf4ac,0x4376},
{0xf4ae,0xf4ae,0x4379},
{0xf4b1,0xf4b1,0x45c2},
{0xf4b5,0xf4b6,0x4380},
{0xf4c2,0xf4c2,0x43db},
{0xf4c6,0xf4c6,0x45c8},
{0xf4cb,0xf4cb,0x45cd},
{0xf4d6,0xf4d6,0x45d7},
{0xf4d8,0xf4d8,0x45d9},
{0xf4e1,0xf4e1,0x438f},
{0xf4ea,0xf4ea,0x4395},
{0xf4eb,0xf4ed,0x45e2},
{0xf4f0,0xf4f1,0x45e6},
{0xf4f4,0xf4f4,0x4398},
{0xf4f5,0xf4f5,0x43c4},
{0xf4f6,0xf4f7,0x45e9},
{0xf4f9,0xf4fa,0x45ec},
{0xf4fb,0xf4fb,0x43a7},
{0xf4fd,0xf4fd,0x45ef},
{0xf502,0xf502,0x43ac},
{0xf504,0xf504,0x45f5},
{0xf506,0xf506,0x45f7},
{0xf512,0xf512,0x4603},
{0xf517,0xf517,0x4608},
{0xf51e,0xf51e,0x460f},
{0xf527,0xf529,0x4618},
{0xf52b,0xf532,0x461c},
{0xf535,0xf537,0x4626},
{0xf538,0xf539,0x44df},
{0xf53c,0xf53c,0x44e3},
{0xf53e,0xf54d,0x44e5},
{0xf54f,0xf552,0x44f6},
{0xf554,0xf554,0x44fb},
{0xf557,0xf558,0x44fd},
{0xf55b,0xf55d,0x4501},
{0xf55f,0xf564,0x4505},
{0xf566,0xf56a,0x450c},
{0xf56c,0xf571,0x4512},
{0xf573,0xf575,0x4519},
{0xf579,0xf57b,0x49a3},
{0xf57d,0xf57d,0x49a7},
{0xf580,0xf583,0x49aa},
{0xf585,0xf585,0x49af},
{0xf58b,0xf58f,0x49b5},
{0xf593,0xf594,0x49bd},
{0xf596,0xf597,0x49c0},
{0xf59b,0xf59c,0x49c4},
{0xf5a0,0xf5a0,0x49c9},
{0xf5a3,0xf5a4,0x49cc},
{0xf5ab,0xf5ab,0x49d4},
{0xf5b0,0xf5b0,0x49d9},
{0xf5b4,0xf5b4,0x49dd},
{0xf5b6,0xf5b6,0x49df},
{0xf5b9,0xf5b9,0x49e2},
{0xf5bd,0xf5bd,0x49e5},
{0xf5bf,0xf5c0,0x49e7},
{0xf5c4,0xf5c5,0x49ec},
{0xf5cc,0xf5cc,0x49f4},
{0xf5ce,0xf5ce,0x49f6},
{0xf5d0,0xf5d0,0x49f8},
{0xf5d5,0xf5d9,0x49fd},
{0xf5e0,0xf5e0,0x4a05},
{0xf5e4,0xf5e4,0x4a08},
{0xf5e6,0xf5e8,0x4a0a},
{0xf5ea,0xf5ed,0x4a0e},
{0xf5ef,0xf5ef,0x4a13},
{0xf634,0xf635,0x4629},
{0xf636,0xf636,0x43ba},
{0xf637,0xf63b,0x462b},
{0xf63c,0xf63c,0x43bb},
{0xf63d,0xf63d,0x43a0},
{0xf63e,0xf63e,0x43bd},
{0xf63f,0xf641,0x4630},
{0xf642,0xf642,0x43be},
{0xf643,0xf649,0x4633},
{0xf64a,0xf64a,0x43bf},
{0xf64b,0xf64d,0x463a},
{0xf64e,0xf64e,0x43c0},
{0xf64f,0xf64f,0x463d},
{0xf650,0xf650,0x43c1},
{0xf651,0xf656,0x463e},
{0xf657,0xf657,0x43c2},
{0xf658,0xf659,0x4644},
{0xf65a,0xf65a,0x43b9},
{0xf65b,0xf65b,0x43ad},
{0xf65c,0xf667,0x4646},
{0xf668,0xf668,0x43c7},
{0xf669,0xf674,0x4652},
{0xf675,0xf675,0x43c8},
{0xf676,0xf693,0x465e},
{0xf695,0xf69d,0x467d},
{0xf69f,0xf6ab,0x4687},
{0xf6ac,0xf6ac,0x43f9},
{0xf6ae,0xf6af,0x4695},
{0xf6b1,0xf6ce,0x1fa},
{0xf6cf,0xf6de,0x219},
{0xf6e0,0xf6e2,0x22a},
{0xf6e4,0xf6e4,0x22e},
{0xf6e6,0xf6e6,0x230},
{0xf6e8,0xf6ed,0x35b3},
{0xf6f0,0xf7e4,0x35ba},
{0xf7e7,0xf7e7,0x36b1},
{0xf7e9,0xf7e9,0x36b3},
{0xf7ec,0xf7ec,0x36b6},
{0xf817,0xf81d,0x36e1},
{0xf81e,0xf820,0x44c6},
{0xf821,0xf829,0x451c},
{0xf82a,0xf82a,0x499e},
{0xf82b,0xf832,0x4525},
{0xf833,0xf833,0x499f},
{0xf834,0xf83a,0x452d},
{0xf83b,0xf83b,0x49a0},
{0xf83f,0xf848,0x4992},
{0xf907,0xf907,0x4516},
{0xfa12,0xfa12,0x41bc},
{0xfa26,0xfa26,0x3881},
{0xfa48,0xfa48,0x4265},
{0xfa5b,0xfa5b,0x3790},
{0xfe10,0xfe12,0x3713},
{0xfe13,0xfe13,0x3718},
{0xfe14,0xfe14,0x3717},
{0xfe15,0xfe15,0x371a},
{0xfe16,0xfe16,0x3719},
{0xfe19,0xfe19,0x354e},
{0xfe51,0xfe51,0x71},
{0xfe68,0xfe68,0x102},
{0xff5e,0xff5e,0xe4},
{0xffe0,0xffe1,0x106},
{0xffe3,0xffe3,0xc4},
{0xffe5,0xffe5,0x104},
{0xffed,0xffed,0x3710},
};

static const pdf_xrange cmap_UniCNS_UTF16_H_xranges[] = {
{0xd840dc21,0xd840dc21,0x3df5},
{0xd840dc3e,0xd840dc3e,0x3a51},
{0xd840dc46,0xd840dc46,0x3a52},
{0xd840dc4e,0xd840dc4e,0x3b59},
{0xd840dc68,0xd840dc68,0x378a},
{0xd840dc86,0xd840dc86,0x44e4},
{0xd840dc87,0xd840dc87,0x4518},
{0xd840dc8a,0xd840dc8a,0x36b2},
{0xd840dc94,0xd840dc94,0x47dc},
{0xd840dcca,0xd840dcca,0x44d7},
{0xd840dccb,0xd840dccb,0x44d4},
{0xd840dccc,0xd840dccc,0x36b0},
{0xd840dccd,0xd840dccd,0x44d1},
{0xd840dcd1,0xd840dcd1,0x44d0},
{0xd840dcee,0xd840dcee,0x493d},
{0xd840dd0c,0xd840dd0c,0x44ce},
{0xd840dd0e,0xd840dd0e,0x44dc},
{0xd840dd18,0xd840dd18,0x4189},
{0xd840dda4,0xd840dda4,0x4a67},
{0xd840dda9,0xd840dda9,0x4353},
{0xd840ddab,0xd840ddab,0x39b8},
{0xd840ddc1,0xd840ddc1,0x414a},
{0xd840ddd4,0xd840ddd4,0x3b31},
{0xd840ddf2,0xd840ddf2,0x3f64},
{0xd840de04,0xd840de04,0x4110},
{0xd840de0c,0xd840de0c,0x39c3},
{0xd840de14,0xd840de14,0x46db},
{0xd840de39,0xd840de39,0x4a6b},
{0xd840de5b,0xd840de5b,0x3b39},
{0xd840de74,0xd840de74,0x38b9},
{0xd840de75,0xd840de75,0x39c1},
{0xd840de99,0xd840de99,0x3b2a},
{0xd840de9e,0xd840de9e,0x415f},
{0xd840dea0,0xd840dea0,0x401f},
{0xd840deb7,0xd840deb7,0x47dd},
{0xd840debf,0xd840debf,0x3c62},
{0xd840dec0,0xd840dec0,0x39bf},
{0xd840dee5,0xd840dee5,0x403f},
{0xd840df0a,0xd840df0a,0x39bb},
{0xd840df25,0xd840df25,0x4694},
{0xd840df41,0xd840df41,0x4311},
{0xd840df45,0xd840df45,0x39ff},
{0xd840df46,0xd840df46,0x3da0},
{0xd840df47,0xd840df47,0x402e},
{0xd840df7e,0xd840df7e,0x402b},
{0xd840df7f,0xd840df7f,0x46bb},
{0xd840df80,0xd840df80,0x402d},
{0xd840dfa0,0xd840dfa0,0x47de},
{0xd840dfa7,0xd840dfa7,0x3cb2},
{0xd840dfb5,0xd840dfb5,0x4958},
{0xd840dfc9,0xd840dfc9,0x41ea},
{0xd840dfcb,0xd840dfcb,0x39b9},
{0xd840dff5,0xd840dff5,0x3cfd},
{0xd840dffc,0xd840dffc,0x491f},
{0xd841dc13,0xd841dc13,0x3a1f},
{0xd841dc14,0xd841dc14,0x39bc},
{0xd841dc1f,0xd841dc1f,0x4030},
{0xd841dc65,0xd841dc65,0x39b2},
{0xd841dc87,0xd841dc87,0x4039},
{0xd841dc8e,0xd841dc8e,0x3ee5},
{0xd841dc91,0xd841dc91,0x39e0},
{0xd841dc92,0xd841dc92,0x39df},
{0xd841dca3,0xd841dca3,0x39de},
{0xd841dcd7,0xd841dcd7,0x47e0},
{0xd841dcfc,0xd841dcfc,0x4359},
{0xd841dcfe,0xd841dcfe,0x3cbc},
{0xd841dd47,0xd841dd47,0x400b},
{0xd841dd8e,0xd841dd8e,0x4041},
{0xd841dda5,0xd841dda5,0x4114},
{0xd841ddb3,0xd841ddb3,0x4046},
{0xd841ddc3,0xd841ddc3,0x495b},
{0xd841ddca,0xd841ddca,0x3dac},
{0xd841ddd0,0xd841ddd0,0x458b},
{0xd841ddd5,0xd841ddd5,0x47e2},
{0xd841dddf,0xd841dddf,0x400d},
{0xd841dde0,0xd841dde0,0x39dc},
{0xd841ddeb,0xd841ddeb,0x3ad0},
{0xd841de11,0xd841de11,0x3dae},
{0xd841de15,0xd841de15,0x47e3},
{0xd841de19,0xd841de19,0x46de},
{0xd841de1a,0xd841de1a,0x3cc9},
{0xd841de30,0xd841de30,0x38eb},
{0xd841de56,0xd841de56,0x4179},
{0xd841de76,0xd841de76,0x47e4},
{0xd841df0e,0xd841df0e,0x39e3},
{0xd841df31,0xd841df31,0x4350},
{0xd841df79,0xd841df79,0x3dbd},
{0xd842dc2c,0xd842dc2c,0x3b60},
{0xd842dc73,0xd842dc73,0x39e6},
{0xd842dcd5,0xd842dcd5,0x46dd},
{0xd842dd16,0xd842dd16,0x3b57},
{0xd842dd23,0xd842dd23,0x41f9},
{0xd842dd54,0xd842dd54,0x406b},
{0xd842dd79,0xd842dd79,0x4907},
{0xd842dde7,0xd842dde7,0x4575},
{0xd842de11,0xd842de11,0x3def},
{0xd842de50,0xd842de50,0x3869},
{0xd842de6f,0xd842de6f,0x4a4e},
{0xd842de8a,0xd842de8a,0x4a9c},
{0xd842deb4,0xd842deb4,0x3ce6},
{0xd842dec2,0xd842dec2,0x47e8},
{0xd842decd,0xd842decd,0x47e9},
{0xd842df0d,0xd842df0d,0x46a1},
{0xd842df8f,0xd842df8f,0x4092},
{0xd842df9f,0xd842df9f,0x4a71},
{0xd842dfa8,0xd842dfa8,0x39a8},
{0xd842dfa9,0xd842dfa9,0x45d3},
{0xd842dfbf,0xd842dfbf,0x47ea},
{0xd842dfc6,0xd842dfc6,0x3a01},
{0xd842dfcb,0xd842dfcb,0x47ec},
{0xd842dfe2,0xd842dfe2,0x406f},
{0xd842dfeb,0xd842dfeb,0x3cca},
{0xd842dffb,0xd842dffb,0x47ee},
{0xd842dfff,0xd842dfff,0x403d},
{0xd843dc0b,0xd843dc0b,0x45b8},
{0xd843dc0d,0xd843dc0d,0x3b32},
{0xd843dc20,0xd843dc20,0x39ec},
{0xd843dc34,0xd843dc34,0x438b},
{0xd843dc3a,0xd843dc3a,0x428e},
{0xd843dc3b,0xd843dc3b,0x47ef},
{0xd843dc41,0xd843dc41,0x3e8c},
{0xd843dc42,0xd843dc42,0x430b},
{0xd843dc43,0xd843dc43,0x434c},
{0xd843dc53,0xd843dc53,0x47f0},
{0xd843dc65,0xd843dc65,0x47f1},
{0xd843dc77,0xd843dc77,0x4097},
{0xd843dc78,0xd843dc78,0x3dbc},
{0xd843dc7c,0xd843dc7c,0x47f2},
{0xd843dc8d,0xd843dc8d,0x47f3},
{0xd843dc96,0xd843dc96,0x3e8d},
{0xd843dc9c,0xd843dc9c,0x48c5},
{0xd843dcb5,0xd843dcb5,0x47f4},
{0xd843dcb8,0xd843dcb8,0x3ccb},
{0xd843dccf,0xd843dccf,0x4734},
{0xd843dcd3,0xd843dcd3,0x413a},
{0xd843dcd4,0xd843dcd4,0x3f58},
{0xd843dcd5,0xd843dcd5,0x4321},
{0xd843dcd6,0xd843dcd6,0x45f6},
{0xd843dcdd,0xd843dcdd,0x47f5},
{0xd843dced,0xd843dced,0x47f6},
{0xd843dcff,0xd843dcff,0x3cc7},
{0xd843dd15,0xd843dd15,0x430c},
{0xd843dd28,0xd843dd28,0x3f07},
{0xd843dd31,0xd843dd31,0x3891},
{0xd843dd32,0xd843dd32,0x4218},
{0xd843dd46,0xd843dd46,0x432c},
{0xd843dd47,0xd843dd47,0x45cf},
{0xd843dd48,0xd843dd48,0x45d4},
{0xd843dd49,0xd843dd49,0x48e2},
{0xd843dd4c,0xd843dd4c,0x3b28},
{0xd843dd4d,0xd843dd4d,0x40a7},
{0xd843dd4e,0xd843dd4e,0x40ed},
{0xd843dd6f,0xd843dd6f,0x47f7},
{0xd843dd71,0xd843dd71,0x3f08},
{0xd843dd74,0xd843dd74,0x409f},
{0xd843dd7c,0xd843dd7c,0x431f},
{0xd843dd7e,0xd843dd7f,0x45dc},
{0xd843dd96,0xd843dd96,0x45bf},
{0xd843dd9c,0xd843dd9c,0x4366},
{0xd843dda7,0xd843dda7,0x3ea5},
{0xd843ddb2,0xd843ddb2,0x47f8},
{0xd843ddc8,0xd843ddc8,0x47f9},
{0xd843de04,0xd843de04,0x47fa},
{0xd843de09,0xd843de09,0x4370},
{0xd843de0a,0xd843de0a,0x48fa},
{0xd843de0d,0xd843de0d,0x39a9},
{0xd843de0e,0xd843de0e,0x47fb},
{0xd843de0f,0xd843de0f,0x45f1},
{0xd843de10,0xd843de10,0x45fa},
{0xd843de11,0xd843de11,0x4609},
{0xd843de16,0xd843de16,0x37c3},
{0xd843de1d,0xd843de1d,0x39f0},
{0xd843de4c,0xd843de4c,0x45d1},
{0xd843de6d,0xd843de6d,0x48d7},
{0xd843de73,0xd843de73,0x47ff},
{0xd843de75,0xd843de75,0x41b8},
{0xd843de76,0xd843de76,0x3e8f},
{0xd843de77,0xd843de77,0x45f2},
{0xd843de78,0xd843de78,0x45fb},
{0xd843de79,0xd843de79,0x4600},
{0xd843de7a,0xd843de7a,0x438a},
{0xd843de7b,0xd843de7b,0x460d},
{0xd843de8c,0xd843de8c,0x45ca},
{0xd843de96,0xd843de96,0x4325},
{0xd843de98,0xd843de98,0x45df},
{0xd843de9d,0xd843de9d,0x3e80},
{0xd843dea2,0xd843dea2,0x3e91},
{0xd843deaa,0xd843deaa,0x45cc},
{0xd843deab,0xd843deab,0x45d2},
{0xd843deac,0xd843deac,0x48f4},
{0xd843deb6,0xd843deb6,0x3ee8},
{0xd843ded7,0xd843ded7,0x47fc},
{0xd843ded8,0xd843ded8,0x4697},
{0xd843dedd,0xd843dedd,0x40a5},
{0xd843def8,0xd843def8,0x3e06},
{0xd843def9,0xd843def9,0x3eba},
{0xd843defa,0xd843defa,0x45f0},
{0xd843defb,0xd843defb,0x45f3},
{0xd843df1d,0xd843df1d,0x3c92},
{0xd843df26,0xd843df26,0x3bfe},
{0xd843df2d,0xd843df2d,0x47fe},
{0xd843df2e,0xd843df2e,0x45e0},
{0xd843df30,0xd843df30,0x45ce},
{0xd843df31,0xd843df31,0x45c3},
{0xd843df3b,0xd843df3b,0x3fd2},
{0xd843df4c,0xd843df4c,0x435b},
{0xd843df64,0xd843df64,0x4327},
{0xd843df8d,0xd843df8d,0x45cb},
{0xd843df90,0xd843df90,0x47fd},
{0xd843dfad,0xd843dfad,0x45da},
{0xd843dfb4,0xd843dfb4,0x4320},
{0xd843dfb5,0xd843dfb5,0x460c},
{0xd843dfb6,0xd843dfb6,0x4610},
{0xd843dfbc,0xd843dfbc,0x4800},
{0xd843dfdf,0xd843dfdf,0x39a7},
{0xd843dfea,0xd843dfea,0x48e1},
{0xd843dfeb,0xd843dfeb,0x48f7},
{0xd843dfec,0xd843dfec,0x48f9},
{0xd843dfed,0xd843dfed,0x48fb},
{0xd844dc14,0xd844dc14,0x3858},
{0xd844dc1d,0xd844dc1e,0x4604},
{0xd844dc4f,0xd844dc4f,0x4802},
{0xd844dc5c,0xd844dc5c,0x4801},
{0xd844dc6f,0xd844dc6f,0x45bd},
{0xd844dc75,0xd844dc75,0x3e95},
{0xd844dc76,0xd844dc76,0x4803},
{0xd844dc77,0xd844dc77,0x45bc},
{0xd844dc78,0xd844dc78,0x45fc},
{0xd844dc7b,0xd844dc7b,0x43b0},
{0xd844dc88,0xd844dc88,0x4804},
{0xd844dc96,0xd844dc96,0x4805},
{0xd844dc9d,0xd844dc9d,0x4363},
{0xd844dcb4,0xd844dcb4,0x40b6},
{0xd844dcbf,0xd844dcbf,0x4807},
{0xd844dcc0,0xd844dcc0,0x45d5},
{0xd844dcc1,0xd844dcc1,0x48e7},
{0xd844dcc7,0xd844dcc7,0x4390},
{0xd844dcc8,0xd844dcc8,0x4374},
{0xd844dcc9,0xd844dcc9,0x45f9},
{0xd844dccf,0xd844dccf,0x45c5},
{0xd844dcd3,0xd844dcd3,0x43b2},
{0xd844dce4,0xd844dce4,0x39ee},
{0xd844dcf4,0xd844dcf4,0x45b7},
{0xd844dcf5,0xd844dcf6,0x4606},
{0xd844dd2f,0xd844dd2f,0x4808},
{0xd844dd3b,0xd844dd3b,0x4809},
{0xd844dd3d,0xd844dd3d,0x45d6},
{0xd844dd45,0xd844dd45,0x4535},
{0xd844dd48,0xd844dd48,0x45fd},
{0xd844dd4f,0xd844dd4f,0x45d0},
{0xd844dd80,0xd844dd80,0x4611},
{0xd844dd87,0xd844dd87,0x48fc},
{0xd844ddd9,0xd844ddd9,0x48f1},
{0xd844de3c,0xd844de3c,0x40db},
{0xd844de4f,0xd844de4f,0x40c7},
{0xd844de7c,0xd844de7c,0x38a8},
{0xd844dea8,0xd844dea8,0x3985},
{0xd844dea9,0xd844dea9,0x4913},
{0xd844deb0,0xd844deb0,0x3d12},
{0xd844dee3,0xd844dee3,0x480b},
{0xd844defe,0xd844defe,0x43b4},
{0xd844df02,0xd844df02,0x3c27},
{0xd844df03,0xd844df03,0x3a22},
{0xd844df04,0xd844df04,0x3d52},
{0xd844df05,0xd844df05,0x4021},
{0xd844df36,0xd844df36,0x480d},
{0xd844df3a,0xd844df3a,0x414d},
{0xd844df75,0xd844df75,0x480c},
{0xd844df76,0xd844df76,0x495d},
{0xd844df8e,0xd844df8e,0x4193},
{0xd844df98,0xd844df98,0x3d16},
{0xd844df9c,0xd844df9c,0x3cec},
{0xd844dfc5,0xd844dfc5,0x3e1c},
{0xd844dfc6,0xd844dfc6,0x3986},
{0xd844dfed,0xd844dfed,0x3e32},
{0xd844dffe,0xd844dffe,0x3984},
{0xd845dc13,0xd845dc13,0x3c95},
{0xd845dc16,0xd845dc16,0x40d0},
{0xd845dc24,0xd845dc24,0x3cb3},
{0xd845dc3f,0xd845dc3f,0x49ff},
{0xd845dc52,0xd845dc52,0x3d0d},
{0xd845dc54,0xd845dc54,0x40d2},
{0xd845dc55,0xd845dc55,0x45ff},
{0xd845dc8a,0xd845dc8a,0x37a9},
{0xd845dc97,0xd845dc97,0x423c},
{0xd845dcb6,0xd845dcb6,0x3987},
{0xd845dce8,0xd845dce8,0x3c2a},
{0xd845dcfd,0xd845dcfd,0x37b4},
{0xd845dd77,0xd845dd77,0x480e},
{0xd845dd82,0xd845dd82,0x3801},
{0xd845dd96,0xd845dd96,0x407f},
{0xd845de0a,0xd845de0a,0x3e18},
{0xd845de13,0xd845de13,0x4080},
{0xd845de19,0xd845de19,0x480f},
{0xd845de3e,0xd845de3e,0x40e7},
{0xd845de61,0xd845de61,0x3789},
{0xd845de92,0xd845de92,0x40ea},
{0xd845deb8,0xd845deb8,0x4107},
{0xd845deba,0xd845deba,0x47e5},
{0xd845dec0,0xd845dec0,0x3981},
{0xd845dec1,0xd845dec1,0x42e8},
{0xd845dec2,0xd845dec2,0x3cd0},
{0xd845ded3,0xd845ded3,0x38f4},
{0xd845ded5,0xd845ded5,0x40f7},
{0xd845dedf,0xd845dedf,0x3842},
{0xd845dee6,0xd845dee6,0x3b24},
{0xd845dee7,0xd845dee7,0x4293},
{0xd845dee8,0xd845dee8,0x4077},
{0xd845defa,0xd845defa,0x3a0c},
{0xd845defb,0xd845defb,0x397a},
{0xd845defc,0xd845defc,0x3a7e},
{0xd845defe,0xd845defe,0x3a37},
{0xd845df0d,0xd845df0d,0x40a4},
{0xd845df10,0xd845df10,0x3a0e},
{0xd845df26,0xd845df26,0x4071},
{0xd845df3a,0xd845df3a,0x3d2b},
{0xd845df3b,0xd845df3b,0x3a43},
{0xd845df3c,0xd845df3c,0x3d39},
{0xd845df57,0xd845df57,0x3c6e},
{0xd845df6c,0xd845df6c,0x4917},
{0xd845df6d,0xd845df6d,0x3f41},
{0xd845df6e,0xd845df6e,0x3b2c},
{0xd845df6f,0xd845df6f,0x3a0d},
{0xd845df70,0xd845df70,0x418b},
{0xd845df71,0xd845df71,0x413f},
{0xd845df73,0xd845df73,0x37ba},
{0xd845df74,0xd845df74,0x397d},
{0xd845dfab,0xd845dfab,0x3a41},
{0xd845dfb0,0xd845dfb0,0x3b47},
{0xd845dfb1,0xd845dfb1,0x3e0b},
{0xd845dfb2,0xd845dfb2,0x40f3},
{0xd845dfb3,0xd845dfb3,0x3d32},
{0xd845dfb4,0xd845dfb4,0x37c4},
{0xd845dfb5,0xd845dfb5,0x3c85},
{0xd845dfc3,0xd845dfc3,0x4810},
{0xd845dfc7,0xd845dfc7,0x4811},
{0xd845dfd9,0xd845dfd9,0x3cb0},
{0xd845dfda,0xd845dfda,0x3bc0},
{0xd845dfdb,0xd845dfdb,0x3d2f},
{0xd845dfdc,0xd845dfdc,0x40f0},
{0xd845dfdf,0xd845dfdf,0x46d6},
{0xd845dfef,0xd845dfef,0x3971},
{0xd845dff5,0xd845dff5,0x48ce},
{0xd845dff6,0xd845dff6,0x48da},
{0xd845dff8,0xd845dff8,0x3978},
{0xd845dff9,0xd845dff9,0x3775},
{0xd845dffa,0xd845dffa,0x3cce},
{0xd845dffb,0xd845dffb,0x40f2},
{0xd845dffc,0xd845dffc,0x4194},
{0xd846dc20,0xd846dc20,0x3e0e},
{0xd846dc28,0xd846dc28,0x40f6},
{0xd846dc29,0xd846dc29,0x41ac},
{0xd846dc2a,0xd846dc2a,0x3a83},
{0xd846dc2d,0xd846dc2d,0x4813},
{0xd846dc39,0xd846dc39,0x397b},
{0xd846dc3a,0xd846dc3a,0x4615},
{0xd846dc3b,0xd846dc3b,0x4928},
{0xd846dc40,0xd846dc40,0x3b83},
{0xd846dc45,0xd846dc45,0x3bab},
{0xd846dc52,0xd846dc52,0x3cd2},
{0xd846dc5e,0xd846dc5e,0x37ab},
{0xd846dc61,0xd846dc61,0x3a16},
{0xd846dc62,0xd846dc62,0x3ce3},
{0xd846dc63,0xd846dc63,0x3918},
{0xd846dc64,0xd846dc64,0x3afc},
{0xd846dc77,0xd846dc77,0x494f},
{0xd846dc7b,0xd846dc7b,0x48db},
{0xd846dc83,0xd846dc83,0x3cda},
{0xd846dc84,0xd846dc84,0x39d8},
{0xd846dc85,0xd846dc85,0x3976},
{0xd846dc9e,0xd846dc9e,0x42b4},
{0xd846dc9f,0xd846dc9f,0x3ca7},
{0xd846dca0,0xd846dca0,0x3a35},
{0xd846dca1,0xd846dca1,0x3b85},
{0xd846dca2,0xd846dca2,0x3807},
{0xd846dcbe,0xd846dcbe,0x3973},
{0xd846dcbf,0xd846dcbf,0x3cd9},
{0xd846dcd1,0xd846dcd1,0x397e},
{0xd846dcd6,0xd846dcd6,0x3c91},
{0xd846dcd7,0xd846dcd7,0x40b1},
{0xd846dcd8,0xd846dcd8,0x3b87},
{0xd846dcd9,0xd846dcd9,0x3a75},
{0xd846dcfa,0xd846dcfa,0x395d},
{0xd846dd03,0xd846dd04,0x3a32},
{0xd846dd05,0xd846dd05,0x3d35},
{0xd846dd10,0xd846dd10,0x3850},
{0xd846dd11,0xd846dd11,0x4105},
{0xd846dd12,0xd846dd12,0x3aa8},
{0xd846dd15,0xd846dd15,0x38be},
{0xd846dd1c,0xd846dd1c,0x3dd8},
{0xd846dd22,0xd846dd22,0x399b},
{0xd846dd27,0xd846dd27,0x3a40},
{0xd846dd3b,0xd846dd3b,0x392c},
{0xd846dd44,0xd846dd44,0x4936},
{0xd846dd58,0xd846dd58,0x4201},
{0xd846dd6a,0xd846dd6a,0x4814},
{0xd846dd7c,0xd846dd7c,0x3a2e},
{0xd846dd80,0xd846dd80,0x49b8},
{0xd846dd83,0xd846dd83,0x4043},
{0xd846dd88,0xd846dd88,0x42b6},
{0xd846dd96,0xd846dd96,0x3a2d},
{0xd846dddb,0xd846dddb,0x4017},
{0xd846ddf3,0xd846ddf3,0x3a15},
{0xd846de2d,0xd846de2d,0x4815},
{0xd846de34,0xd846de34,0x4112},
{0xd846de45,0xd846de45,0x4816},
{0xd846de4b,0xd846de4b,0x42ba},
{0xd846de63,0xd846de63,0x4a36},
{0xd846df44,0xd846df44,0x4119},
{0xd846dfc1,0xd846dfc1,0x44e1},
{0xd846dfc2,0xd846dfc2,0x47cc},
{0xd847dc2a,0xd847dc2a,0x4817},
{0xd847dc70,0xd847dc70,0x4818},
{0xd847dca2,0xd847dca2,0x48f5},
{0xd847dca5,0xd847dca5,0x411d},
{0xd847dcac,0xd847dcac,0x4819},
{0xd847dd46,0xd847dd46,0x492c},
{0xd847dd53,0xd847dd53,0x4a91},
{0xd847dd5e,0xd847dd5e,0x4a42},
{0xd847dd90,0xd847dd90,0x3dcf},
{0xd847ddb6,0xd847ddb6,0x49d9},
{0xd847ddba,0xd847ddba,0x3a3b},
{0xd847ddca,0xd847ddca,0x458a},
{0xd847ddd1,0xd847ddd1,0x3f0e},
{0xd847ddeb,0xd847ddeb,0x3a8f},
{0xd847ddf9,0xd847ddf9,0x4125},
{0xd847de1c,0xd847de1c,0x42dd},
{0xd847de23,0xd847de23,0x4a63},
{0xd847de37,0xd847de37,0x4126},
{0xd847de3d,0xd847de3d,0x4955},
{0xd847de89,0xd847de89,0x3887},
{0xd847dea4,0xd847dea4,0x412a},
{0xd847dea8,0xd847dea8,0x3db0},
{0xd847dec8,0xd847dec8,0x481a},
{0xd847ded5,0xd847ded5,0x481b},
{0xd847df0f,0xd847df0f,0x3f0b},
{0xd847df15,0xd847df15,0x481c},
{0xd847df6a,0xd847df6a,0x415b},
{0xd847df9e,0xd847df9e,0x46e0},
{0xd847dfa1,0xd847dfa1,0x3e42},
{0xd847dfe8,0xd847dfe8,0x44d5},
{0xd848dc45,0xd848dc45,0x481e},
{0xd848dc49,0xd848dc49,0x4130},
{0xd848dc7e,0xd848dc7e,0x49c9},
{0xd848dc9a,0xd848dc9a,0x3da6},
{0xd848dcc7,0xd848dcc7,0x437b},
{0xd848dcfc,0xd848dcfc,0x403e},
{0xd848dd2a,0xd848dd2a,0x41ef},
{0xd848dd5b,0xd848dd5b,0x46b8},
{0xd848dd73,0xd848dd73,0x4131},
{0xd848dd7a,0xd848dd7a,0x3e02},
{0xd848dda1,0xd848dda1,0x45be},
{0xd848ddc1,0xd848ddc1,0x4a72},
{0xd848ddc3,0xd848ddc3,0x3db7},
{0xd848de08,0xd848de08,0x4387},
{0xd848de7c,0xd848de7c,0x4821},
{0xd848df21,0xd848df21,0x3bbc},
{0xd848df25,0xd848df25,0x3873},
{0xd848dfbd,0xd848dfbd,0x3d13},
{0xd848dfd0,0xd848dfd0,0x4150},
{0xd848dfd7,0xd848dfd7,0x4822},
{0xd848dffa,0xd848dffa,0x4823},
{0xd849dc65,0xd849dc65,0x4908},
{0xd849dc71,0xd849dc71,0x4151},
{0xd849dc8b,0xd849dc8b,0x4209},
{0xd849dc91,0xd849dc91,0x421b},
{0xd849dcb0,0xd849dcb0,0x48c8},
{0xd849dcbc,0xd849dcbc,0x4ac5},
{0xd849dcc1,0xd849dcc1,0x4ac7},
{0xd849dcc9,0xd849dcc9,0x4ac8},
{0xd849dccc,0xd849dccc,0x4ac9},
{0xd849dced,0xd849dced,0x4023},
{0xd849dd13,0xd849dd13,0x4029},
{0xd849dd1b,0xd849dd1b,0x3fa6},
{0xd849dd30,0xd849dd30,0x4040},
{0xd849dd54,0xd849dd54,0x4104},
{0xd849dd8d,0xd849dd8d,0x3dff},
{0xd849ddaf,0xd849ddaf,0x3a4c},
{0xd849ddbe,0xd849ddbe,0x3a4d},
{0xd849de1b,0xd849de1b,0x3a53},
{0xd849de1c,0xd849de1c,0x42f8},
{0xd849de2b,0xd849de2b,0x3a49},
{0xd849de68,0xd849de68,0x415d},
{0xd849de7a,0xd849de7a,0x46b4},
{0xd849de96,0xd849de96,0x45d8},
{0xd849de98,0xd849de98,0x40a9},
{0xd849def4,0xd849def4,0x48eb},
{0xd849def5,0xd849def5,0x3b35},
{0xd849def6,0xd849def6,0x37a5},
{0xd849df12,0xd849df12,0x49dd},
{0xd849df14,0xd849df14,0x379a},
{0xd849df1b,0xd849df1b,0x3ddd},
{0xd849df1f,0xd849df1f,0x3c2c},
{0xd849df2a,0xd849df2a,0x4824},
{0xd849df75,0xd849df75,0x4369},
{0xd849df81,0xd849df81,0x3c33},
{0xd849df96,0xd849df96,0x4a55},
{0xd849dfb4,0xd849dfb4,0x4167},
{0xd849dfb5,0xd849dfb5,0x45f8},
{0xd849dfcd,0xd849dfcd,0x41fd},
{0xd84adc03,0xd84adc03,0x45c6},
{0xd84adc5f,0xd84adc5f,0x417d},
{0xd84adc60,0xd84adc60,0x3a48},
{0xd84adc71,0xd84adc71,0x4825},
{0xd84adcad,0xd84adcad,0x42a1},
{0xd84adcc1,0xd84adcc1,0x4184},
{0xd84adcf7,0xd84adcf7,0x41c2},
{0xd84add26,0xd84add26,0x3e09},
{0xd84add39,0xd84add39,0x45c7},
{0xd84add4f,0xd84add4f,0x4826},
{0xd84add67,0xd84add67,0x4827},
{0xd84add6b,0xd84add6b,0x3c34},
{0xd84add80,0xd84add80,0x4183},
{0xd84add93,0xd84add93,0x4828},
{0xd84ade66,0xd84ade66,0x4362},
{0xd84adecf,0xd84adecf,0x4371},
{0xd84aded5,0xd84aded5,0x4829},
{0xd84adee6,0xd84adee6,0x458c},
{0xd84adee8,0xd84adee8,0x482a},
{0xd84adf0e,0xd84adf0e,0x482b},
{0xd84adf22,0xd84adf22,0x3a5f},
{0xd84adf3f,0xd84adf3f,0x482c},
{0xd84adf43,0xd84adf43,0x3ea0},
{0xd84adf6a,0xd84adf6a,0x3a5d},
{0xd84adfca,0xd84adfca,0x4343},
{0xd84adfce,0xd84adfce,0x438d},
{0xd84bdc26,0xd84bdc26,0x3c97},
{0xd84bdc27,0xd84bdc27,0x380e},
{0xd84bdc38,0xd84bdc38,0x4318},
{0xd84bdc4c,0xd84bdc4c,0x482d},
{0xd84bdc51,0xd84bdc51,0x438e},
{0xd84bdc55,0xd84bdc55,0x45de},
{0xd84bdc62,0xd84bdc62,0x3e90},
{0xd84bdc88,0xd84bdc88,0x482e},
{0xd84bdc9b,0xd84bdc9b,0x3a57},
{0xd84bdca1,0xd84bdca1,0x3a6a},
{0xd84bdca9,0xd84bdca9,0x4328},
{0xd84bdcb2,0xd84bdcb2,0x437e},
{0xd84bdcb7,0xd84bdcb7,0x482f},
{0xd84bdcc2,0xd84bdcc2,0x4331},
{0xd84bdcc6,0xd84bdcc6,0x430f},
{0xd84bdcc9,0xd84bdcc9,0x45c1},
{0xd84bdd07,0xd84bdd07,0x3a58},
{0xd84bdd08,0xd84bdd08,0x4831},
{0xd84bdd12,0xd84bdd12,0x4832},
{0xd84bdd44,0xd84bdd44,0x433a},
{0xd84bdd4c,0xd84bdd4c,0x45bb},
{0xd84bdd67,0xd84bdd67,0x3a68},
{0xd84bdd8d,0xd84bdd8d,0x4356},
{0xd84bdd95,0xd84bdd95,0x4834},
{0xd84bdda0,0xd84bdda0,0x48f8},
{0xd84bdda3,0xd84bdda3,0x4095},
{0xd84bdda4,0xd84bdda4,0x4602},
{0xd84bddb7,0xd84bddb7,0x4833},
{0xd84bddee,0xd84bddee,0x45e8},
{0xd84bde0d,0xd84bde0d,0x418d},
{0xd84bde36,0xd84bde36,0x4196},
{0xd84bde42,0xd84bde42,0x4835},
{0xd84bde78,0xd84bde78,0x3a62},
{0xd84bde8b,0xd84bde8b,0x4386},
{0xd84bdeb3,0xd84bdeb3,0x3ea2},
{0xd84bdeef,0xd84bdeef,0x46a4},
{0xd84bdf74,0xd84bdf74,0x4836},
{0xd84bdfcc,0xd84bdfcc,0x4837},
{0xd84bdfe3,0xd84bdfe3,0x3d11},
{0xd84cdc33,0xd84cdc33,0x4838},
{0xd84cdc44,0xd84cdc44,0x4280},
{0xd84cdc4b,0xd84cdc4b,0x4236},
{0xd84cdc66,0xd84cdc66,0x4839},
{0xd84cdc7d,0xd84cdc7d,0x3913},
{0xd84cdc7e,0xd84cdc7e,0x4954},
{0xd84cdc8e,0xd84cdc8e,0x3a6f},
{0xd84cdcb7,0xd84cdcb7,0x38d2},
{0xd84cdcbc,0xd84cdcbc,0x38d3},
{0xd84cdcda,0xd84cdcda,0x4912},
{0xd84cdd03,0xd84cdd03,0x3a82},
{0xd84cdd3d,0xd84cdd3d,0x4625},
{0xd84cdd7d,0xd84cdd7d,0x37c6},
{0xd84cdd82,0xd84cdd82,0x3a87},
{0xd84cdda4,0xd84cdda5,0x41b5},
{0xd84cddb3,0xd84cddb3,0x3a79},
{0xd84cddc8,0xd84cddc8,0x41bf},
{0xd84cddc9,0xd84cddc9,0x3c6f},
{0xd84cddea,0xd84cddea,0x4a9b},
{0xd84cddf7,0xd84cddf8,0x41b2},
{0xd84cddf9,0xd84cddf9,0x421a},
{0xd84cde0f,0xd84cde0f,0x3a86},
{0xd84cde25,0xd84cde25,0x3bdd},
{0xd84cde2f,0xd84cde2f,0x3e33},
{0xd84cde31,0xd84cde31,0x41bd},
{0xd84cde32,0xd84cde32,0x3b01},
{0xd84cde33,0xd84cde33,0x4042},
{0xd84cde34,0xd84cde34,0x3a84},
{0xd84cde56,0xd84cde56,0x4a54},
{0xd84cde5e,0xd84cde5e,0x4a9d},
{0xd84cde62,0xd84cde62,0x3870},
{0xd84cde81,0xd84cde81,0x49b5},
{0xd84cde89,0xd84cde89,0x3a78},
{0xd84cde8a,0xd84cde8a,0x40b9},
{0xd84cdeab,0xd84cdeab,0x3a72},
{0xd84cdeac,0xd84cdeac,0x42e4},
{0xd84cdead,0xd84cdead,0x3a70},
{0xd84cded2,0xd84cded2,0x3a91},
{0xd84cdee0,0xd84cdee0,0x3a74},
{0xd84cdee1,0xd84cdee1,0x3a92},
{0xd84cdf00,0xd84cdf00,0x3d5b},
{0xd84cdf0a,0xd84cdf0a,0x38bf},
{0xd84cdf1f,0xd84cdf1f,0x483a},
{0xd84cdfb4,0xd84cdfb4,0x403b},
{0xd84cdfcc,0xd84cdfcc,0x3dcb},
{0xd84cdfde,0xd84cdfde,0x483b},
{0xd84cdfe6,0xd84cdfe6,0x41d4},
{0xd84cdff4,0xd84cdff4,0x45db},
{0xd84cdff5,0xd84cdff5,0x492a},
{0xd84cdff9,0xd84cdff9,0x49df},
{0xd84cdffa,0xd84cdffa,0x3d1c},
{0xd84cdffe,0xd84cdffe,0x3a9a},
{0xd84ddc00,0xd84ddc00,0x41b0},
{0xd84ddc3f,0xd84ddc3f,0x3b69},
{0xd84ddc50,0xd84ddc50,0x3a60},
{0xd84ddc6f,0xd84ddc6f,0x41d7},
{0xd84ddc72,0xd84ddc72,0x3a3c},
{0xd84ddce5,0xd84ddce5,0x3d17},
{0xd84ddd19,0xd84ddd19,0x3df8},
{0xd84ddd30,0xd84ddd30,0x37b9},
{0xd84ddd51,0xd84ddd51,0x4a5d},
{0xd84ddd5a,0xd84ddd5a,0x37c7},
{0xd84ddd67,0xd84ddd67,0x483c},
{0xd84ddd95,0xd84ddd95,0x39d4},
{0xd84ddd99,0xd84ddd99,0x3974},
{0xd84ddd9c,0xd84ddd9c,0x3c5b},
{0xd84dddbb,0xd84dddbb,0x4acc},
{0xd84dddcd,0xd84dddcd,0x41dd},
{0xd84dddce,0xd84dddce,0x37cf},
{0xd84dddcf,0xd84dddcf,0x3b5a},
{0xd84dddf3,0xd84dddf3,0x483d},
{0xd84dde00,0xd84dde00,0x4027},
{0xd84dde17,0xd84dde17,0x4a26},
{0xd84dde1a,0xd84dde1a,0x483e},
{0xd84dde3c,0xd84dde3c,0x41e1},
{0xd84dde40,0xd84dde40,0x3c5c},
{0xd84dde59,0xd84dde59,0x41ee},
{0xd84dde5f,0xd84dde5f,0x3ad3},
{0xd84dde77,0xd84dde77,0x493c},
{0xd84dde8e,0xd84dde8e,0x4abb},
{0xd84dde9e,0xd84dde9e,0x4a92},
{0xd84ddea6,0xd84ddea6,0x3989},
{0xd84ddead,0xd84ddead,0x3d1f},
{0xd84ddeba,0xd84ddeba,0x48e3},
{0xd84ddedf,0xd84ddedf,0x3d19},
{0xd84ddeee,0xd84ddeee,0x3993},
{0xd84ddf03,0xd84ddf03,0x41f1},
{0xd84ddf16,0xd84ddf16,0x483f},
{0xd84ddf20,0xd84ddf20,0x490f},
{0xd84ddf2d,0xd84ddf2d,0x4057},
{0xd84ddf2f,0xd84ddf2f,0x3e1a},
{0xd84ddf3f,0xd84ddf3f,0x399e},
{0xd84ddf66,0xd84ddf66,0x378c},
{0xd84ddf81,0xd84ddf81,0x3b06},
{0xd84ddfa2,0xd84ddfa2,0x3a99},
{0xd84ddfbc,0xd84ddfbc,0x3a97},
{0xd84ddfc2,0xd84ddfc2,0x41c8},
{0xd84ddfd5,0xd84ddfd5,0x3aa2},
{0xd84ddfd6,0xd84ddfd6,0x3d1b},
{0xd84ddfd7,0xd84ddfd7,0x3c58},
{0xd84edc3a,0xd84edc3a,0x3a96},
{0xd84eddc2,0xd84eddc2,0x4375},
{0xd84edea7,0xd84edea7,0x4841},
{0xd84ededb,0xd84ededb,0x41ff},
{0xd84edeee,0xd84edeee,0x3a2f},
{0xd84edefa,0xd84edefa,0x46da},
{0xd84edf1a,0xd84edf1a,0x4a59},
{0xd84edf5a,0xd84edf5a,0x4204},
{0xd84fdc63,0xd84fdc63,0x49e2},
{0xd84fdc99,0xd84fdc99,0x4047},
{0xd84fdc9a,0xd84fdc9a,0x3ab0},
{0xd84fdc9b,0xd84fdc9b,0x388e},
{0xd84fdcb5,0xd84fdcb5,0x46a5},
{0xd84fdcb7,0xd84fdcb7,0x3e5b},
{0xd84fdcc7,0xd84fdcc7,0x4932},
{0xd84fdcc8,0xd84fdcc8,0x4019},
{0xd84fdcc9,0xd84fdcc9,0x4215},
{0xd84fdcfc,0xd84fdcfc,0x48f2},
{0xd84fdcfd,0xd84fdcfd,0x4920},
{0xd84fdcfe,0xd84fdcfe,0x4909},
{0xd84fdcff,0xd84fdcff,0x3db2},
{0xd84fdd40,0xd84fdd40,0x3e6b},
{0xd84fdd5b,0xd84fdd5b,0x3cde},
{0xd84fdd7e,0xd84fdd7e,0x37b2},
{0xd84fdd8f,0xd84fdd8f,0x3b8a},
{0xd84fddb6,0xd84fddb6,0x490d},
{0xd84fddb7,0xd84fddb7,0x3c9a},
{0xd84fddb8,0xd84fddb8,0x3c2d},
{0xd84fddb9,0xd84fddb9,0x3819},
{0xd84fddba,0xd84fddba,0x3b2b},
{0xd84fddbb,0xd84fddbb,0x386e},
{0xd84fddbc,0xd84fddbc,0x420f},
{0xd84fddbd,0xd84fddbd,0x4211},
{0xd84fdde3,0xd84fdde3,0x379d},
{0xd84fddf8,0xd84fddf8,0x37bb},
{0xd84fde06,0xd84fde06,0x4a37},
{0xd84fde11,0xd84fde11,0x4842},
{0xd84fde2c,0xd84fde2c,0x4911},
{0xd84fde2d,0xd84fde2d,0x3cf9},
{0xd84fde2e,0xd84fde2e,0x38a9},
{0xd84fde2f,0xd84fde2f,0x4230},
{0xd84fde30,0xd84fde30,0x423a},
{0xd84fde31,0xd84fde31,0x398e},
{0xd84fde39,0xd84fde39,0x39d5},
{0xd84fde88,0xd84fde88,0x48d8},
{0xd84fde89,0xd84fde89,0x39d9},
{0xd84fde8a,0xd84fde8a,0x39ca},
{0xd84fde8b,0xd84fde8b,0x37cb},
{0xd84fdeb9,0xd84fdeb9,0x4843},
{0xd84fdebf,0xd84fdebf,0x39d6},
{0xd84fded7,0xd84fded7,0x372b},
{0xd84fdef7,0xd84fdef7,0x4910},
{0xd84fdef8,0xd84fdef8,0x3ad2},
{0xd84fdef9,0xd84fdef9,0x3d43},
{0xd84fdefa,0xd84fdefa,0x3b20},
{0xd84fdefb,0xd84fdefb,0x3990},
{0xd84fdefc,0xd84fdefc,0x4206},
{0xd84fdf35,0xd84fdf35,0x3adb},
{0xd84fdf41,0xd84fdf41,0x4686},
{0xd84fdf4a,0xd84fdf4a,0x3dc1},
{0xd84fdf61,0xd84fdf61,0x4a38},
{0xd84fdf7f,0xd84fdf7f,0x3abc},
{0xd84fdf80,0xd84fdf80,0x406a},
{0xd84fdf81,0xd84fdf81,0x3e3f},
{0xd84fdf82,0xd84fdf82,0x4232},
{0xd84fdf8f,0xd84fdf8f,0x46ab},
{0xd84fdfb4,0xd84fdfb4,0x424d},
{0xd84fdfb7,0xd84fdfb7,0x417c},
{0xd84fdfc0,0xd84fdfc0,0x3aaf},
{0xd84fdfc5,0xd84fdfc5,0x400e},
{0xd84fdfeb,0xd84fdfeb,0x4094},
{0xd84fdfec,0xd84fdfec,0x406c},
{0xd84fdfed,0xd84fdfed,0x3cf8},
{0xd84fdfee,0xd84fdfee,0x3be5},
{0xd84fdfef,0xd84fdfef,0x3ddb},
{0xd84fdff0,0xd84fdff0,0x424c},
{0xd850dc11,0xd850dc11,0x3ffd},
{0xd850dc39,0xd850dc39,0x424b},
{0xd850dc3a,0xd850dc3a,0x40b2},
{0xd850dc3b,0xd850dc3b,0x3daa},
{0xd850dc3c,0xd850dc3c,0x3abf},
{0xd850dc3d,0xd850dc3d,0x423d},
{0xd850dc57,0xd850dc57,0x3770},
{0xd850dc85,0xd850dc85,0x4226},
{0xd850dc8b,0xd850dc8b,0x39a0},
{0xd850dc8c,0xd850dc8c,0x4953},
{0xd850dc8d,0xd850dc8d,0x39f9},
{0xd850dc91,0xd850dc91,0x4249},
{0xd850dcc9,0xd850dcc9,0x39fd},
{0xd850dce1,0xd850dce1,0x399f},
{0xd850dcec,0xd850dcec,0x4a62},
{0xd850dd04,0xd850dd04,0x3c43},
{0xd850dd0f,0xd850dd0f,0x39a1},
{0xd850dd19,0xd850dd19,0x4844},
{0xd850dd3f,0xd850dd3f,0x424e},
{0xd850dd40,0xd850dd40,0x4252},
{0xd850dd44,0xd850dd44,0x4247},
{0xd850dd4e,0xd850dd4e,0x49a7},
{0xd850dd55,0xd850dd55,0x424a},
{0xd850dd56,0xd850dd57,0x4250},
{0xd850dd5c,0xd850dd5c,0x38ec},
{0xd850dd5f,0xd850dd5f,0x42af},
{0xd850dd61,0xd850dd61,0x4a99},
{0xd850dd77,0xd850dd77,0x4616},
{0xd850dd7a,0xd850dd7a,0x3e24},
{0xd850dda3,0xd850dda3,0x48ed},
{0xd850dda4,0xd850dda4,0x4212},
{0xd850dda5,0xd850dda5,0x40c9},
{0xd850ddac,0xd850ddac,0x3c80},
{0xd850ddb5,0xd850ddb5,0x3c78},
{0xd850ddcd,0xd850ddcd,0x41cc},
{0xd850dde2,0xd850dde2,0x4292},
{0xd850ddfc,0xd850ddfc,0x3d36},
{0xd850de1b,0xd850de1b,0x3bfa},
{0xd850de4b,0xd850de4b,0x46b6},
{0xd850de56,0xd850de56,0x3d51},
{0xd850de59,0xd850de59,0x3d48},
{0xd850de76,0xd850de76,0x3ac7},
{0xd850de77,0xd850de77,0x4260},
{0xd850de78,0xd850de78,0x40e0},
{0xd850de84,0xd850de84,0x3ba7},
{0xd850de93,0xd850de93,0x3d41},
{0xd850de95,0xd850de95,0x3a17},
{0xd850dea5,0xd850dea5,0x3d3e},
{0xd850debf,0xd850debf,0x4a25},
{0xd850dec1,0xd850dec1,0x4269},
{0xd850dec9,0xd850dec9,0x3a88},
{0xd850deca,0xd850deca,0x3d46},
{0xd850deee,0xd850deee,0x4845},
{0xd850defa,0xd850defa,0x3c96},
{0xd850df0d,0xd850df0d,0x4846},
{0xd850df1a,0xd850df1a,0x3ac6},
{0xd850df34,0xd850df34,0x4847},
{0xd850df48,0xd850df48,0x3959},
{0xd850df62,0xd850df62,0x3d0f},
{0xd850df63,0xd850df63,0x40d3},
{0xd850df64,0xd850df64,0x3938},
{0xd850df65,0xd850df65,0x4266},
{0xd850df8c,0xd850df8c,0x39eb},
{0xd850df96,0xd850df96,0x4848},
{0xd850df9c,0xd850df9c,0x41c5},
{0xd850dfbd,0xd850dfbd,0x3d4f},
{0xd850dfc1,0xd850dfc1,0x3c98},
{0xd850dfe9,0xd850dfe9,0x3d55},
{0xd850dfea,0xd850dfea,0x3bdb},
{0xd850dff2,0xd850dff2,0x3ef7},
{0xd850dff8,0xd850dff8,0x396f},
{0xd851dc04,0xd851dc04,0x4849},
{0xd851dc35,0xd851dc35,0x40b4},
{0xd851dc36,0xd851dc36,0x39b0},
{0xd851dc5a,0xd851dc5a,0x426b},
{0xd851dc5b,0xd851dc5b,0x3d45},
{0xd851dc73,0xd851dc73,0x396e},
{0xd851dc87,0xd851dc87,0x3aa3},
{0xd851dc88,0xd851dc88,0x3c8b},
{0xd851dcb9,0xd851dcb9,0x3968},
{0xd851dcbc,0xd851dcbc,0x4139},
{0xd851dcce,0xd851dcce,0x3d4d},
{0xd851dcd3,0xd851dcd3,0x432a},
{0xd851dcd6,0xd851dcd6,0x484a},
{0xd851dd05,0xd851dd05,0x49e5},
{0xd851dd21,0xd851dd21,0x3de4},
{0xd851dd78,0xd851dd78,0x375f},
{0xd851ddc8,0xd851ddc8,0x4358},
{0xd851de18,0xd851de18,0x3ecf},
{0xd851de2a,0xd851de2a,0x3a9d},
{0xd851de65,0xd851de65,0x3a5c},
{0xd851de74,0xd851de74,0x484c},
{0xd851de97,0xd851de97,0x3b05},
{0xd851ded4,0xd851ded4,0x3a59},
{0xd851df06,0xd851df06,0x48e8},
{0xd851df25,0xd851df25,0x3acd},
{0xd851df2f,0xd851df2f,0x484d},
{0xd851df8f,0xd851df8f,0x3acc},
{0xd851dfe0,0xd851dfe0,0x3ec2},
{0xd851dfef,0xd851dfef,0x43cb},
{0xd852dc12,0xd852dc12,0x484e},
{0xd852dc23,0xd852dc23,0x4286},
{0xd852dc82,0xd852dc82,0x375d},
{0xd852dce9,0xd852dce9,0x44f5},
{0xd852dcf0,0xd852dcf0,0x3b08},
{0xd852dcf1,0xd852dcf1,0x420a},
{0xd852dcf2,0xd852dcf2,0x46ef},
{0xd852dcf3,0xd852dcf3,0x3af7},
{0xd852dcfb,0xd852dcfb,0x484f},
{0xd852dcff,0xd852dcff,0x3bd9},
{0xd852dd00,0xd852dd00,0x4214},
{0xd852dd01,0xd852dd01,0x412f},
{0xd852dd0c,0xd852dd0c,0x4213},
{0xd852dd16,0xd852dd16,0x3cb6},
{0xd852dd17,0xd852dd17,0x3ad5},
{0xd852dd19,0xd852dd19,0x4921},
{0xd852dd2f,0xd852dd2f,0x3bc3},
{0xd852dd33,0xd852dd33,0x401d},
{0xd852dd34,0xd852dd34,0x3de6},
{0xd852dd3e,0xd852dd3e,0x491c},
{0xd852dd3f,0xd852dd3f,0x3dc8},
{0xd852dd40,0xd852dd40,0x41a8},
{0xd852dd41,0xd852dd41,0x3c70},
{0xd852dd42,0xd852dd42,0x4014},
{0xd852dd43,0xd852dd43,0x3cc6},
{0xd852dd62,0xd852dd62,0x46c7},
{0xd852dd63,0xd852dd63,0x4915},
{0xd852dd74,0xd852dd74,0x427b},
{0xd852dd75,0xd852dd75,0x3955},
{0xd852dd76,0xd852dd76,0x3cb8},
{0xd852dd7b,0xd852dd7b,0x4a08},
{0xd852dd7f,0xd852dd7f,0x4225},
{0xd852dd82,0xd852dd82,0x416d},
{0xd852dd88,0xd852dd88,0x3e3a},
{0xd852dd89,0xd852dd89,0x3a71},
{0xd852dd8a,0xd852dd8a,0x3859},
{0xd852dd8b,0xd852dd8b,0x3ca0},
{0xd852dd8c,0xd852dd8c,0x3b29},
{0xd852dd8d,0xd852dd8d,0x4298},
{0xd852dd8e,0xd852dd8e,0x3af1},
{0xd852dd8f,0xd852dd8f,0x416e},
{0xd852dd94,0xd852dd94,0x398c},
{0xd852dda4,0xd852dda4,0x4038},
{0xd852dda7,0xd852dda7,0x37b8},
{0xd852dda9,0xd852dda9,0x40fd},
{0xd852ddab,0xd852ddab,0x3af0},
{0xd852ddac,0xd852ddac,0x39ed},
{0xd852ddad,0xd852ddad,0x3cd7},
{0xd852ddb7,0xd852ddb7,0x4297},
{0xd852ddb8,0xd852ddb8,0x3c29},
{0xd852ddb9,0xd852ddb9,0x3c9f},
{0xd852ddba,0xd852ddba,0x3d9d},
{0xd852ddbb,0xd852ddbb,0x3956},
{0xd852ddc5,0xd852ddc5,0x3af6},
{0xd852ddd0,0xd852ddd0,0x3e04},
{0xd852ddda,0xd852ddda,0x4614},
{0xd852ddde,0xd852ddde,0x3c5d},
{0xd852dddf,0xd852dddf,0x418f},
{0xd852dde3,0xd852dde3,0x3a67},
{0xd852dde5,0xd852dde5,0x3cb4},
{0xd852ddec,0xd852ddec,0x46c4},
{0xd852dded,0xd852dded,0x3be3},
{0xd852ddf6,0xd852ddf6,0x3dda},
{0xd852ddf7,0xd852ddf7,0x3b6a},
{0xd852ddf8,0xd852ddf8,0x3958},
{0xd852ddf9,0xd852ddf9,0x3b46},
{0xd852ddfb,0xd852ddfb,0x4299},
{0xd852de0e,0xd852de0e,0x39c9},
{0xd852de12,0xd852de12,0x495e},
{0xd852de13,0xd852de13,0x49e8},
{0xd852de15,0xd852de15,0x4850},
{0xd852de21,0xd852de21,0x41c6},
{0xd852de22,0xd852de22,0x426d},
{0xd852de23,0xd852de23,0x41a2},
{0xd852de24,0xd852de24,0x4294},
{0xd852de25,0xd852de25,0x3983},
{0xd852de26,0xd852de26,0x429d},
{0xd852de27,0xd852de27,0x426c},
{0xd852de28,0xd852de28,0x41a1},
{0xd852de29,0xd852de29,0x38fb},
{0xd852de2a,0xd852de2a,0x3e0d},
{0xd852de3e,0xd852de3e,0x3cae},
{0xd852de42,0xd852de42,0x3bda},
{0xd852de45,0xd852de45,0x3dab},
{0xd852de4a,0xd852de4a,0x3ad4},
{0xd852de4e,0xd852de4e,0x3af4},
{0xd852de4f,0xd852de4f,0x46d1},
{0xd852de50,0xd852de50,0x3cd1},
{0xd852de51,0xd852de51,0x395a},
{0xd852de5d,0xd852de5d,0x46d5},
{0xd852de65,0xd852de65,0x37ad},
{0xd852de66,0xd852de66,0x3cb1},
{0xd852de67,0xd852de67,0x3c09},
{0xd852de71,0xd852de71,0x3d10},
{0xd852de77,0xd852de77,0x4925},
{0xd852de78,0xd852de78,0x3be4},
{0xd852de79,0xd852de79,0x42b0},
{0xd852de7a,0xd852de7a,0x383e},
{0xd852de8c,0xd852de8c,0x42ae},
{0xd852de93,0xd852de93,0x48ca},
{0xd852de94,0xd852de94,0x3caf},
{0xd852de95,0xd852de95,0x37ae},
{0xd852de96,0xd852de96,0x3b0f},
{0xd852dea4,0xd852dea4,0x3acf},
{0xd852dea5,0xd852dea5,0x3e3e},
{0xd852dea6,0xd852dea6,0x4050},
{0xd852dea7,0xd852dea7,0x3dc2},
{0xd852deb1,0xd852deb1,0x4028},
{0xd852deb2,0xd852deb2,0x3c01},
{0xd852deb3,0xd852deb3,0x3ca9},
{0xd852deba,0xd852deba,0x3b0c},
{0xd852debb,0xd852debb,0x3f9d},
{0xd852debc,0xd852debc,0x39f8},
{0xd852dec0,0xd852dec0,0x4851},
{0xd852dec7,0xd852dec7,0x3b0d},
{0xd852deca,0xd852deca,0x48ad},
{0xd852ded1,0xd852ded1,0x38bd},
{0xd852dedf,0xd852dedf,0x3ad9},
{0xd852dee2,0xd852dee2,0x491a},
{0xd852dee9,0xd852dee9,0x38c6},
{0xd852df0f,0xd852df0f,0x4a8b},
{0xd852df6e,0xd852df6e,0x4904},
{0xd852dff5,0xd852dff5,0x40d5},
{0xd853dc09,0xd853dc09,0x3bb6},
{0xd853dc9e,0xd853dc9e,0x48d9},
{0xd853dc9f,0xd853dc9f,0x3d1d},
{0xd853dcc9,0xd853dcc9,0x46ad},
{0xd853dcd9,0xd853dcd9,0x3ae7},
{0xd853dd06,0xd853dd06,0x3ae8},
{0xd853dd13,0xd853dd13,0x42c4},
{0xd853ddb8,0xd853ddb8,0x4313},
{0xd853ddea,0xd853ddea,0x431d},
{0xd853ddeb,0xd853ddeb,0x45f4},
{0xd853de3b,0xd853de3b,0x4382},
{0xd853de50,0xd853de50,0x4601},
{0xd853dea5,0xd853dea5,0x43d0},
{0xd853dea7,0xd853dea7,0x4330},
{0xd853df0e,0xd853df0e,0x493f},
{0xd853df5c,0xd853df5c,0x43d4},
{0xd853df82,0xd853df82,0x42c2},
{0xd853df86,0xd853df86,0x4853},
{0xd853df97,0xd853df97,0x3c83},
{0xd853df9a,0xd853df9a,0x3dd3},
{0xd853dfa9,0xd853dfa9,0x490b},
{0xd853dfb8,0xd853dfb8,0x46a0},
{0xd853dfc2,0xd853dfc2,0x48f6},
{0xd854dc2c,0xd854dc2c,0x4854},
{0xd854dc52,0xd854dc52,0x434b},
{0xd854dc9d,0xd854dc9d,0x42db},
{0xd854dd2b,0xd854dd2b,0x430d},
{0xd854dd48,0xd854dd48,0x48e4},
{0xd854dd7d,0xd854dd7d,0x42dc},
{0xd854dd7e,0xd854dd7e,0x460e},
{0xd854ddcd,0xd854ddcd,0x49ab},
{0xd854dde3,0xd854dde3,0x45c9},
{0xd854dde6,0xd854dde6,0x41ba},
{0xd854dde7,0xd854dde7,0x3aff},
{0xd854de20,0xd854de20,0x42e2},
{0xd854de21,0xd854de21,0x3afe},
{0xd854de50,0xd854de50,0x415a},
{0xd854de99,0xd854de99,0x4856},
{0xd854dec7,0xd854dec7,0x4337},
{0xd854ded8,0xd854ded8,0x4612},
{0xd854df0e,0xd854df0e,0x3749},
{0xd854df11,0xd854df11,0x48dd},
{0xd854df13,0xd854df13,0x41c0},
{0xd855dc19,0xd855dc19,0x4857},
{0xd855dc25,0xd855dc25,0x3a80},
{0xd855dc2f,0xd855dc2f,0x3977},
{0xd855dc30,0xd855dc30,0x3838},
{0xd855dc46,0xd855dc46,0x4858},
{0xd855dc6c,0xd855dc6c,0x38db},
{0xd855dc6e,0xd855dc6e,0x4859},
{0xd855dc9a,0xd855dc9a,0x4a83},
{0xd855dd31,0xd855dd31,0x4935},
{0xd855dd35,0xd855dd35,0x4719},
{0xd855dd3f,0xd855dd3f,0x485a},
{0xd855dd5b,0xd855dd5b,0x3a7f},
{0xd855dd5c,0xd855dd5c,0x398f},
{0xd855dd5d,0xd855dd5d,0x3c69},
{0xd855dd5e,0xd855dd5e,0x485b},
{0xd855dd62,0xd855dd62,0x485c},
{0xd855dd65,0xd855dd65,0x3839},
{0xd855dd66,0xd855dd66,0x485d},
{0xd855dd81,0xd855dd81,0x3b12},
{0xd855dd84,0xd855dd84,0x3a7b},
{0xd855dd8f,0xd855dd8f,0x3a7d},
{0xd855ddb9,0xd855ddb9,0x388c},
{0xd855ddd5,0xd855ddd5,0x3cc2},
{0xd855dddb,0xd855dddb,0x3890},
{0xd855dde0,0xd855dde0,0x42f1},
{0xd855de05,0xd855de05,0x4278},
{0xd855de35,0xd855de35,0x48d3},
{0xd855de51,0xd855de51,0x39b6},
{0xd855de83,0xd855de83,0x3b95},
{0xd855de95,0xd855de95,0x49ed},
{0xd855dee3,0xd855dee3,0x3b1a},
{0xd855def6,0xd855def6,0x3d5e},
{0xd855df06,0xd855df06,0x38ed},
{0xd855df1d,0xd855df1d,0x4198},
{0xd855df25,0xd855df25,0x3904},
{0xd855df3d,0xd855df3d,0x461b},
{0xd855df72,0xd855df72,0x4088},
{0xd855dfc7,0xd855dfc7,0x485e},
{0xd855dfdf,0xd855dfdf,0x3bee},
{0xd855dfe0,0xd855dfe0,0x372f},
{0xd855dfe1,0xd855dfe1,0x3ddf},
{0xd856dc57,0xd856dc57,0x42fd},
{0xd856dc5d,0xd856dc5d,0x485f},
{0xd856dc72,0xd856dc72,0x3a94},
{0xd856dcc8,0xd856dcc8,0x3c1a},
{0xd856dcde,0xd856dcde,0x4a95},
{0xd856dce1,0xd856dce1,0x3734},
{0xd856dd03,0xd856dd03,0x4860},
{0xd856dd46,0xd856dd46,0x3a14},
{0xd856dd56,0xd856dd56,0x48d4},
{0xd856ddac,0xd856ddac,0x3738},
{0xd856ddcc,0xd856ddcc,0x4113},
{0xd856de54,0xd856de54,0x4a35},
{0xd856de95,0xd856de95,0x4906},
{0xd856de9c,0xd856de9c,0x3b37},
{0xd856deae,0xd856deae,0x4861},
{0xd856deaf,0xd856deaf,0x3b36},
{0xd856dee9,0xd856dee9,0x3b61},
{0xd856df74,0xd856df74,0x4624},
{0xd856df89,0xd856df89,0x4862},
{0xd856dfb3,0xd856dfb3,0x3b42},
{0xd856dfb4,0xd856dfb4,0x374e},
{0xd856dfc6,0xd856dfc6,0x3b3c},
{0xd856dfe4,0xd856dfe4,0x37df},
{0xd856dfe8,0xd856dfe8,0x4830},
{0xd857dc01,0xd857dc01,0x3745},
{0xd857dc06,0xd857dc06,0x4863},
{0xd857dc21,0xd857dc21,0x3f53},
{0xd857dc4a,0xd857dc4a,0x48dc},
{0xd857dc65,0xd857dc65,0x469a},
{0xd857dc91,0xd857dc91,0x37f0},
{0xd857dca4,0xd857dca4,0x49ec},
{0xd857dcc0,0xd857dcc0,0x37f2},
{0xd857dcc1,0xd857dcc1,0x375a},
{0xd857dcfe,0xd857dcfe,0x374a},
{0xd857dd20,0xd857dd20,0x3754},
{0xd857dd30,0xd857dd30,0x49ad},
{0xd857dd43,0xd857dd43,0x3b62},
{0xd857dd99,0xd857dd99,0x4ac3},
{0xd857ddb9,0xd857ddb9,0x4ab4},
{0xd857de0e,0xd857de0e,0x3b64},
{0xd857de49,0xd857de49,0x437a},
{0xd857de81,0xd857de81,0x48d6},
{0xd857de82,0xd857de82,0x3b54},
{0xd857de83,0xd857de83,0x3a1c},
{0xd857dea6,0xd857dea6,0x3b44},
{0xd857debc,0xd857debc,0x3b43},
{0xd857ded7,0xd857ded7,0x4933},
{0xd857ded8,0xd857ded8,0x48df},
{0xd857df1a,0xd857df1a,0x3fd8},
{0xd857df4b,0xd857df4b,0x397f},
{0xd857dfe1,0xd857dfe1,0x3ad6},
{0xd857dfe2,0xd857dfe2,0x3da4},
{0xd858dc21,0xd858dc21,0x4a93},
{0xd858dc29,0xd858dc29,0x3771},
{0xd858dc48,0xd858dc48,0x3ec7},
{0xd858dc64,0xd858dc64,0x3c59},
{0xd858dc83,0xd858dc83,0x4960},
{0xd858dc97,0xd858dc97,0x435a},
{0xd858dca4,0xd858dca4,0x3c9e},
{0xd858dca5,0xd858dca5,0x45ba},
{0xd858dd02,0xd858dd02,0x4866},
{0xd858dd21,0xd858dd21,0x3779},
{0xd858dd59,0xd858dd59,0x4929},
{0xd858dd5a,0xd858dd5a,0x377a},
{0xd858dd5b,0xd858dd5b,0x3c9d},
{0xd858dd5c,0xd858dd5c,0x3b51},
{0xd858ddad,0xd858ddad,0x493a},
{0xd858ddae,0xd858ddae,0x3b10},
{0xd858ddb2,0xd858ddb2,0x4867},
{0xd858dddd,0xd858dddd,0x4253},
{0xd858de58,0xd858de58,0x3e5c},
{0xd858de61,0xd858de61,0x3b50},
{0xd858de6a,0xd858de6a,0x44fa},
{0xd858de6b,0xd858de6b,0x4945},
{0xd858ded0,0xd858ded0,0x3784},
{0xd858df35,0xd858df35,0x3f05},
{0xd858df4b,0xd858df4b,0x44fc},
{0xd858df4c,0xd858df4c,0x4942},
{0xd858df51,0xd858df51,0x3787},
{0xd858dfbe,0xd858dfbe,0x3bf4},
{0xd858dff5,0xd858dff5,0x42cf},
{0xd858dff8,0xd858dff8,0x3b5d},
{0xd859dc02,0xd859dc02,0x4868},
{0xd859dc10,0xd859dc10,0x38ba},
{0xd859dc11,0xd859dc11,0x3deb},
{0xd859dc12,0xd859dc12,0x3b5c},
{0xd859dc4a,0xd859dc4a,0x4869},
{0xd859dc69,0xd859dc69,0x3df4},
{0xd859dc84,0xd859dc84,0x486a},
{0xd859dc88,0xd859dc88,0x486b},
{0xd859dc89,0xd859dc89,0x378f},
{0xd859dc8d,0xd859dc8d,0x373e},
{0xd859dc98,0xd859dc98,0x37a7},
{0xd859dd12,0xd859dd12,0x486c},
{0xd859dd72,0xd859dd72,0x45eb},
{0xd859dda0,0xd859dda0,0x3b6d},
{0xd859ddad,0xd859ddad,0x3b6b},
{0xd859ddbf,0xd859ddbf,0x486d},
{0xd859de12,0xd859de12,0x44ff},
{0xd859de26,0xd859de26,0x379c},
{0xd859deaf,0xd859deaf,0x48d1},
{0xd859deb1,0xd859deb1,0x39a5},
{0xd859deb5,0xd859deb5,0x486e},
{0xd859deda,0xd859deda,0x4347},
{0xd859dee8,0xd859dee8,0x379e},
{0xd859defc,0xd859defc,0x4870},
{0xd859df16,0xd859df16,0x4348},
{0xd859df41,0xd859df41,0x3c9b},
{0xd859df99,0xd859df99,0x4872},
{0xd859dfb3,0xd859dfb3,0x490e},
{0xd859dfb4,0xd859dfb4,0x3b77},
{0xd859dfcc,0xd859dfcc,0x3fea},
{0xd85adc1c,0xd85adc1c,0x45c4},
{0xd85adc46,0xd85adc46,0x493e},
{0xd85adc5e,0xd85adc5e,0x4874},
{0xd85adc6e,0xd85adc6e,0x4873},
{0xd85adc88,0xd85adc88,0x414f},
{0xd85adc8a,0xd85adc8a,0x3eac},
{0xd85adc93,0xd85adc93,0x48ea},
{0xd85adcc7,0xd85adcc7,0x4875},
{0xd85add0e,0xd85add0e,0x42d4},
{0xd85add11,0xd85add11,0x42d3},
{0xd85add26,0xd85add26,0x4876},
{0xd85add39,0xd85add39,0x4877},
{0xd85add51,0xd85add51,0x4500},
{0xd85adda8,0xd85adda8,0x46b5},
{0xd85addb5,0xd85addb5,0x3e31},
{0xd85addf2,0xd85addf2,0x431c},
{0xd85addfa,0xd85addfa,0x4878},
{0xd85ade2d,0xd85ade2d,0x4879},
{0xd85ade2e,0xd85ade2e,0x428f},
{0xd85ade34,0xd85ade34,0x487a},
{0xd85ade42,0xd85ade42,0x3b79},
{0xd85ade51,0xd85ade51,0x3b7c},
{0xd85ade52,0xd85ade52,0x37b1},
{0xd85adf05,0xd85adf05,0x37cd},
{0xd85adf0a,0xd85adf0a,0x46a2},
{0xd85adf13,0xd85adf13,0x4065},
{0xd85adf15,0xd85adf15,0x4a0e},
{0xd85adf23,0xd85adf23,0x4a33},
{0xd85adf28,0xd85adf28,0x411e},
{0xd85adf50,0xd85adf50,0x45e1},
{0xd85adf51,0xd85adf51,0x420b},
{0xd85adf52,0xd85adf52,0x3dc4},
{0xd85adf53,0xd85adf53,0x409e},
{0xd85adf5b,0xd85adf5b,0x487b},
{0xd85adf75,0xd85adf75,0x3b56},
{0xd85adf82,0xd85adf82,0x3c4f},
{0xd85adf96,0xd85adf96,0x37e7},
{0xd85adf97,0xd85adf97,0x37ce},
{0xd85adf9d,0xd85adf9d,0x487c},
{0xd85adfb3,0xd85adfb3,0x4020},
{0xd85adfc0,0xd85adfc0,0x3d09},
{0xd85adff7,0xd85adff7,0x3dd6},
{0xd85bdc21,0xd85bdc21,0x39a2},
{0xd85bdc40,0xd85bdc40,0x3c82},
{0xd85bdc41,0xd85bdc41,0x4a68},
{0xd85bdc46,0xd85bdc46,0x4a7d},
{0xd85bdc7e,0xd85bdc7e,0x40ce},
{0xd85bdc7f,0xd85bdc7f,0x374f},
{0xd85bdc80,0xd85bdc80,0x3d2d},
{0xd85bdc81,0xd85bdc81,0x40ee},
{0xd85bdc82,0xd85bdc82,0x3d26},
{0xd85bdca4,0xd85bdca4,0x487d},
{0xd85bdcb7,0xd85bdcb7,0x3d20},
{0xd85bdcb8,0xd85bdcb8,0x38b8},
{0xd85bdcbd,0xd85bdcbd,0x3de9},
{0xd85bdcc0,0xd85bdcc0,0x3c94},
{0xd85bdcc3,0xd85bdcc3,0x3796},
{0xd85bdcd1,0xd85bdcd1,0x3b70},
{0xd85bdd22,0xd85bdd22,0x4919},
{0xd85bdd23,0xd85bdd23,0x491e},
{0xd85bdd24,0xd85bdd24,0x3a8a},
{0xd85bdd25,0xd85bdd25,0x421e},
{0xd85bdd26,0xd85bdd26,0x3d24},
{0xd85bdd27,0xd85bdd27,0x38f3},
{0xd85bdd28,0xd85bdd28,0x3d4b},
{0xd85bdd29,0xd85bdd29,0x4025},
{0xd85bdd2a,0xd85bdd2a,0x40ef},
{0xd85bdd51,0xd85bdd51,0x3d25},
{0xd85bdd74,0xd85bdd74,0x4a0c},
{0xd85bdda0,0xd85bdda0,0x41d5},
{0xd85bdda1,0xd85bdda1,0x421f},
{0xd85bdda2,0xd85bdda2,0x3944},
{0xd85bdda3,0xd85bdda3,0x37cc},
{0xd85bdda4,0xd85bdda4,0x3c52},
{0xd85bdda5,0xd85bdda5,0x37d5},
{0xd85bdda6,0xd85bdda6,0x40f4},
{0xd85bdda7,0xd85bdda7,0x3b7e},
{0xd85bddae,0xd85bddae,0x487e},
{0xd85bdddc,0xd85bdddc,0x4938},
{0xd85bddea,0xd85bddea,0x3ae3},
{0xd85bddeb,0xd85bddeb,0x4220},
{0xd85bddf0,0xd85bddf0,0x3c54},
{0xd85bde00,0xd85bde00,0x3c57},
{0xd85bde05,0xd85bde05,0x3fa3},
{0xd85bde07,0xd85bde07,0x3e2c},
{0xd85bde12,0xd85bde12,0x3c4d},
{0xd85bde42,0xd85bde42,0x37dd},
{0xd85bde43,0xd85bde43,0x37e9},
{0xd85bde44,0xd85bde44,0x3768},
{0xd85bde45,0xd85bde45,0x40f9},
{0xd85bde6e,0xd85bde6e,0x4153},
{0xd85bde72,0xd85bde72,0x4227},
{0xd85bde77,0xd85bde77,0x37e8},
{0xd85bde84,0xd85bde84,0x3c53},
{0xd85bde88,0xd85bde88,0x4ac1},
{0xd85bde8b,0xd85bde8b,0x4a28},
{0xd85bde99,0xd85bde99,0x46a6},
{0xd85bded0,0xd85bded0,0x48ef},
{0xd85bded1,0xd85bded1,0x492d},
{0xd85bded2,0xd85bded2,0x4176},
{0xd85bded3,0xd85bded3,0x372d},
{0xd85bded4,0xd85bded4,0x37d9},
{0xd85bded5,0xd85bded5,0x38b0},
{0xd85bded6,0xd85bded6,0x376a},
{0xd85bded7,0xd85bded7,0x3875},
{0xd85bdf26,0xd85bdf26,0x3995},
{0xd85bdf73,0xd85bdf73,0x3a4f},
{0xd85bdf74,0xd85bdf74,0x4229},
{0xd85bdf9f,0xd85bdf9f,0x42d9},
{0xd85bdfa1,0xd85bdfa1,0x4103},
{0xd85bdfbe,0xd85bdfbe,0x4a0f},
{0xd85bdfde,0xd85bdfde,0x3d27},
{0xd85bdfdf,0xd85bdfdf,0x3c4e},
{0xd85cdc0e,0xd85cdc0e,0x457d},
{0xd85cdc4b,0xd85cdc4b,0x487f},
{0xd85cdc52,0xd85cdc52,0x3991},
{0xd85cdc53,0xd85cdc53,0x3d2e},
{0xd85cdc88,0xd85cdc88,0x37ff},
{0xd85cdcad,0xd85cdcad,0x4939},
{0xd85cdcae,0xd85cdcae,0x4916},
{0xd85cdcaf,0xd85cdcaf,0x3e22},
{0xd85cdccd,0xd85cdccd,0x3802},
{0xd85cdcd2,0xd85cdcd2,0x4a29},
{0xd85cdcf0,0xd85cdcf0,0x3803},
{0xd85cdcf8,0xd85cdcf8,0x415c},
{0xd85cdd09,0xd85cdd09,0x46cf},
{0xd85cdd0c,0xd85cdd0c,0x385c},
{0xd85cdd0d,0xd85cdd0d,0x4a0a},
{0xd85cdd26,0xd85cdd26,0x4303},
{0xd85cdd27,0xd85cdd27,0x3b6f},
{0xd85cdd64,0xd85cdd64,0x4918},
{0xd85cdd65,0xd85cdd65,0x398d},
{0xd85cdd75,0xd85cdd75,0x38e9},
{0xd85cddcd,0xd85cddcd,0x4880},
{0xd85cde1b,0xd85cde1b,0x3b80},
{0xd85cde67,0xd85cde67,0x4a1a},
{0xd85cde80,0xd85cde80,0x4881},
{0xd85cde85,0xd85cde85,0x4882},
{0xd85cde8b,0xd85cde8b,0x4883},
{0xd85cdeb2,0xd85cdeb2,0x3edc},
{0xd85cdeb6,0xd85cdeb6,0x48f0},
{0xd85cdee6,0xd85cdee6,0x4884},
{0xd85cdf52,0xd85cdf52,0x4a27},
{0xd85cdf9a,0xd85cdf9a,0x3809},
{0xd85cdfff,0xd85cdfff,0x4a80},
{0xd85ddc22,0xd85ddc22,0x3b8c},
{0xd85ddc50,0xd85ddc50,0x4886},
{0xd85ddc84,0xd85ddc84,0x3cdb},
{0xd85ddc86,0xd85ddc86,0x3fe4},
{0xd85ddd74,0xd85ddd74,0x4385},
{0xd85ddda3,0xd85ddda3,0x3ec5},
{0xd85ddde0,0xd85ddde0,0x3818},
{0xd85ddde4,0xd85ddde4,0x381a},
{0xd85dddfd,0xd85dddfd,0x3a45},
{0xd85dddfe,0xd85dddfe,0x3da5},
{0xd85dde07,0xd85dde07,0x36b8},
{0xd85dde0c,0xd85dde0c,0x42f5},
{0xd85dde32,0xd85dde32,0x3dc6},
{0xd85dde39,0xd85dde39,0x438c},
{0xd85dde55,0xd85dde55,0x48d2},
{0xd85dde56,0xd85dde56,0x4934},
{0xd85dde57,0xd85dde57,0x3da3},
{0xd85dde94,0xd85dde94,0x460a},
{0xd85ddf0f,0xd85ddf0f,0x3821},
{0xd85ddf35,0xd85ddf35,0x453b},
{0xd85ddf36,0xd85ddf36,0x3d44},
{0xd85ddf41,0xd85ddf41,0x3b19},
{0xd85ddf5e,0xd85ddf5e,0x4397},
{0xd85ddf84,0xd85ddf84,0x3b98},
{0xd85ddf85,0xd85ddf85,0x3b97},
{0xd85ddfcc,0xd85ddfcc,0x4888},
{0xd85edc58,0xd85edc58,0x4889},
{0xd85edc70,0xd85edc70,0x3a8e},
{0xd85edc9d,0xd85edc9d,0x3e55},
{0xd85edcb2,0xd85edcb2,0x4504},
{0xd85edcc8,0xd85edcc8,0x45b9},
{0xd85edd24,0xd85edd24,0x382a},
{0xd85edd67,0xd85edd67,0x4779},
{0xd85edd7a,0xd85edd7a,0x38e2},
{0xd85edda0,0xd85edda0,0x4349},
{0xd85edddd,0xd85edddd,0x488b},
{0xd85eddfd,0xd85eddfd,0x488c},
{0xd85ede0a,0xd85ede0a,0x488d},
{0xd85ede0e,0xd85ede0e,0x410d},
{0xd85ede3e,0xd85ede3e,0x3e59},
{0xd85ede53,0xd85ede53,0x416f},
{0xd85ede59,0xd85ede59,0x3830},
{0xd85ede79,0xd85ede79,0x3d9b},
{0xd85ede84,0xd85ede84,0x4026},
{0xd85edebd,0xd85edebd,0x382b},
{0xd85edebe,0xd85edebe,0x3878},
{0xd85edef4,0xd85edef4,0x3d5d},
{0xd85edf06,0xd85edf06,0x3c13},
{0xd85edf0b,0xd85edf0b,0x488e},
{0xd85edf18,0xd85edf18,0x3d5f},
{0xd85edf38,0xd85edf38,0x3834},
{0xd85edf39,0xd85edf39,0x42ff},
{0xd85edf3a,0xd85edf3a,0x3832},
{0xd85edf48,0xd85edf48,0x3b52},
{0xd85edf65,0xd85edf65,0x4abe},
{0xd85edfef,0xd85edfef,0x4a40},
{0xd85edff4,0xd85edff4,0x3d18},
{0xd85fdc12,0xd85fdc12,0x3dd1},
{0xd85fdc6c,0xd85fdc6c,0x4a2f},
{0xd85fdcb1,0xd85fdcb1,0x4a1d},
{0xd85fdcc5,0xd85fdcc5,0x4a1f},
{0xd85fdd2f,0xd85fdd2f,0x48ec},
{0xd85fdd53,0xd85fdd53,0x384a},
{0xd85fdd54,0xd85fdd54,0x3846},
{0xd85fdd66,0xd85fdd66,0x488f},
{0xd85fdd73,0xd85fdd73,0x48ee},
{0xd85fdd84,0xd85fdd84,0x45b5},
{0xd85fdd8f,0xd85fdd8f,0x3847},
{0xd85fdd98,0xd85fdd98,0x384c},
{0xd85fddbd,0xd85fddbd,0x384d},
{0xd85fdddc,0xd85fdddc,0x3baf},
{0xd85fde4d,0xd85fde4d,0x3d4e},
{0xd85fde4f,0xd85fde4f,0x3cf4},
{0xd85fdf2e,0xd85fdf2e,0x3eea},
{0xd85fdff9,0xd85fdff9,0x3ebd},
{0xd860dc02,0xd860dc02,0x3857},
{0xd860dc09,0xd860dc09,0x4890},
{0xd860dc1e,0xd860dc1e,0x3ab6},
{0xd860dc23,0xd860dc23,0x4891},
{0xd860dc24,0xd860dc24,0x3e58},
{0xd860dc48,0xd860dc48,0x4892},
{0xd860dc83,0xd860dc83,0x4893},
{0xd860dc90,0xd860dc90,0x4894},
{0xd860dcbd,0xd860dcbd,0x45ee},
{0xd860dcbe,0xd860dcbe,0x4316},
{0xd860dce8,0xd860dce8,0x3e1b},
{0xd860dce9,0xd860dce9,0x432e},
{0xd860dcf4,0xd860dcf4,0x4895},
{0xd860dd2e,0xd860dd2e,0x4896},
{0xd860dd4f,0xd860dd4f,0x4897},
{0xd860dd5d,0xd860dd5d,0x431b},
{0xd860dd6f,0xd860dd6f,0x3e30},
{0xd860dd89,0xd860dd89,0x43e8},
{0xd860ddaf,0xd860ddaf,0x4898},
{0xd860ddbc,0xd860ddbc,0x385b},
{0xd860de07,0xd860de07,0x45fe},
{0xd860de18,0xd860de18,0x3cef},
{0xd860de1a,0xd860de1a,0x4899},
{0xd860de56,0xd860de56,0x4329},
{0xd860de7c,0xd860de7c,0x4617},
{0xd860de9b,0xd860de9b,0x4378},
{0xd860decd,0xd860decd,0x460b},
{0xd860dee2,0xd860dee2,0x3e85},
{0xd860df06,0xd860df06,0x489a},
{0xd860df18,0xd860df18,0x3e2a},
{0xd860df2f,0xd860df2f,0x489b},
{0xd860df3a,0xd860df3a,0x378d},
{0xd860df65,0xd860df65,0x3864},
{0xd860df6d,0xd860df6d,0x3777},
{0xd860df7d,0xd860df7d,0x3f87},
{0xd860df8a,0xd860df8a,0x489c},
{0xd861dc12,0xd861dc12,0x3865},
{0xd861dc68,0xd861dc68,0x489d},
{0xd861dc6c,0xd861dc6c,0x4589},
{0xd861dc73,0xd861dc73,0x3e0a},
{0xd861dc82,0xd861dc82,0x4142},
{0xd861dd01,0xd861dd01,0x46b1},
{0xd861dd3c,0xd861dd3c,0x3877},
{0xd861dd3d,0xd861dd3d,0x386d},
{0xd861dd6c,0xd861dd6c,0x387a},
{0xd861dde8,0xd861dde8,0x3db4},
{0xd861ddf4,0xd861ddf4,0x41be},
{0xd861de00,0xd861de00,0x390f},
{0xd861de0b,0xd861de0b,0x387b},
{0xd861de25,0xd861de25,0x37fc},
{0xd861de3b,0xd861de3b,0x37fd},
{0xd861deaa,0xd861deaa,0x489e},
{0xd861deab,0xd861deab,0x407c},
{0xd861deb2,0xd861deb2,0x4a7f},
{0xd861debc,0xd861debc,0x46b0},
{0xd861ded8,0xd861ded8,0x38a7},
{0xd861dee6,0xd861dee6,0x387f},
{0xd861df0f,0xd861df0f,0x49bd},
{0xd861df13,0xd861df13,0x387d},
{0xd862dc04,0xd862dc04,0x4289},
{0xd862dc2b,0xd862dc2b,0x4287},
{0xd862dd0d,0xd862dd0d,0x4a9a},
{0xd862dd33,0xd862dd33,0x3886},
{0xd862dd48,0xd862dd48,0x3dce},
{0xd862dd49,0xd862dd49,0x3e38},
{0xd862dd56,0xd862dd56,0x48a0},
{0xd862dd64,0xd862dd64,0x42e6},
{0xd862dd68,0xd862dd68,0x42e7},
{0xd862dd6c,0xd862dd6c,0x3967},
{0xd862dd6d,0xd862dd6d,0x4a73},
{0xd862dd7e,0xd862dd7e,0x395f},
{0xd862dd89,0xd862dd89,0x3bf1},
{0xd862dda8,0xd862dda8,0x3a3d},
{0xd862ddaa,0xd862ddaa,0x401e},
{0xd862ddab,0xd862ddab,0x3e23},
{0xd862ddb8,0xd862ddb8,0x48a1},
{0xd862ddbc,0xd862ddbc,0x40a8},
{0xd862ddc0,0xd862ddc0,0x467c},
{0xd862dddc,0xd862dddc,0x3ab7},
{0xd862ddde,0xd862ddde,0x386c},
{0xd862dde1,0xd862dde1,0x3d49},
{0xd862dde3,0xd862dde3,0x49cc},
{0xd862dde4,0xd862dde4,0x4015},
{0xd862dde7,0xd862dde8,0x48a2},
{0xd862ddf9,0xd862ddf9,0x3b9c},
{0xd862ddfa,0xd862ddfa,0x3ca1},
{0xd862ddfb,0xd862ddfb,0x3a26},
{0xd862ddfc,0xd862ddfc,0x3c8e},
{0xd862de0f,0xd862de0f,0x493b},
{0xd862de16,0xd862de16,0x3cbf},
{0xd862de25,0xd862de25,0x3828},
{0xd862de29,0xd862de29,0x4160},
{0xd862de32,0xd862de32,0x49af},
{0xd862de36,0xd862de36,0x4156},
{0xd862de44,0xd862de44,0x3963},
{0xd862de45,0xd862de45,0x3c0c},
{0xd862de46,0xd862de46,0x3a0b},
{0xd862de47,0xd862de47,0x3a3a},
{0xd862de48,0xd862de48,0x37a1},
{0xd862de49,0xd862de49,0x3c86},
{0xd862de4a,0xd862de4a,0x41e5},
{0xd862de4b,0xd862de4b,0x3bca},
{0xd862de59,0xd862de59,0x46e6},
{0xd862de5a,0xd862de5a,0x4926},
{0xd862de81,0xd862de81,0x3ce2},
{0xd862de82,0xd862de82,0x3cc4},
{0xd862de83,0xd862de83,0x3d1a},
{0xd862de9a,0xd862de9a,0x3da9},
{0xd862de9b,0xd862de9b,0x4083},
{0xd862de9c,0xd862de9c,0x39e2},
{0xd862dec0,0xd862dec0,0x3cd6},
{0xd862dec6,0xd862dec6,0x388d},
{0xd862decb,0xd862decb,0x3ab4},
{0xd862decc,0xd862decc,0x3aef},
{0xd862dece,0xd862dece,0x3cbe},
{0xd862dede,0xd862dede,0x4067},
{0xd862dedf,0xd862dedf,0x41da},
{0xd862dee0,0xd862dee0,0x38b4},
{0xd862dee1,0xd862dee1,0x3894},
{0xd862dee2,0xd862dee2,0x3897},
{0xd862dee3,0xd862dee3,0x3afb},
{0xd862dee5,0xd862dee5,0x3898},
{0xd862deea,0xd862deea,0x49c5},
{0xd862defc,0xd862defc,0x3bcc},
{0xd862df0c,0xd862df0c,0x388f},
{0xd862df13,0xd862df13,0x39ab},
{0xd862df21,0xd862df21,0x422e},
{0xd862df22,0xd862df22,0x3a7c},
{0xd862df2b,0xd862df2b,0x495f},
{0xd862df2c,0xd862df2c,0x48cc},
{0xd862df2d,0xd862df2d,0x492f},
{0xd862df2f,0xd862df2f,0x3bf3},
{0xd862df46,0xd862df46,0x48a4},
{0xd862df4c,0xd862df4c,0x434f},
{0xd862df4e,0xd862df4e,0x3db9},
{0xd862df50,0xd862df50,0x3c08},
{0xd862df63,0xd862df63,0x3ca6},
{0xd862df64,0xd862df64,0x3c0a},
{0xd862df65,0xd862df65,0x3c19},
{0xd862df66,0xd862df66,0x40c5},
{0xd862df6c,0xd862df6c,0x48cf},
{0xd862df8f,0xd862df8f,0x42b1},
{0xd862df99,0xd862df99,0x48d0},
{0xd862df9c,0xd862df9c,0x3b9b},
{0xd862df9d,0xd862df9d,0x417f},
{0xd862dfb9,0xd862dfb9,0x4a3d},
{0xd862dfc2,0xd862dfc2,0x492b},
{0xd862dfc5,0xd862dfc5,0x3c00},
{0xd862dfd4,0xd862dfd4,0x48a5},
{0xd862dfd7,0xd862dfd7,0x3a1e},
{0xd862dfd9,0xd862dfd9,0x3b9a},
{0xd862dfda,0xd862dfda,0x395c},
{0xd862dfe7,0xd862dfe7,0x3cc1},
{0xd862dfe8,0xd862dfe8,0x426f},
{0xd862dfe9,0xd862dfe9,0x3731},
{0xd862dfea,0xd862dfea,0x3ab3},
{0xd862dfeb,0xd862dfeb,0x3895},
{0xd862dfec,0xd862dfec,0x389b},
{0xd862dff5,0xd862dff5,0x3b99},
{0xd862dfff,0xd862dfff,0x389d},
{0xd863dc03,0xd863dc03,0x42b3},
{0xd863dc09,0xd863dc09,0x48a6},
{0xd863dc1c,0xd863dc1c,0x3d08},
{0xd863dc1d,0xd863dc1d,0x3bce},
{0xd863dc23,0xd863dc23,0x3ada},
{0xd863dc26,0xd863dc26,0x3bf2},
{0xd863dc2b,0xd863dc2b,0x393f},
{0xd863dc30,0xd863dc30,0x3e35},
{0xd863dc39,0xd863dc39,0x389c},
{0xd863dc3b,0xd863dc3b,0x3bcf},
{0xd863dcca,0xd863dcca,0x4394},
{0xd863dccd,0xd863dccd,0x4389},
{0xd863dcd2,0xd863dcd2,0x4392},
{0xd863dd34,0xd863dd34,0x3bd6},
{0xd863dd99,0xd863dd99,0x4393},
{0xd863ddb9,0xd863ddb9,0x49fe},
{0xd863de0f,0xd863de0f,0x450b},
{0xd863de36,0xd863de36,0x3960},
{0xd863de39,0xd863de39,0x3fcc},
{0xd863de65,0xd863de65,0x3be1},
{0xd863de66,0xd863de66,0x3be0},
{0xd863de97,0xd863de97,0x3f4a},
{0xd863deac,0xd863deac,0x42bc},
{0xd863deb2,0xd863deb2,0x42a3},
{0xd863deb3,0xd863deb3,0x39c0},
{0xd863ded9,0xd863ded9,0x42b9},
{0xd863dee7,0xd863dee7,0x3bdf},
{0xd863dfc5,0xd863dfc5,0x48a7},
{0xd864dc79,0xd864dc79,0x4a76},
{0xd864dc88,0xd864dc88,0x3a4e},
{0xd864dc8b,0xd864dc8b,0x394c},
{0xd864dc93,0xd864dc93,0x3bed},
{0xd864dcaf,0xd864dcaf,0x38bb},
{0xd864dcb0,0xd864dcb0,0x3bea},
{0xd864dcb1,0xd864dcb1,0x3906},
{0xd864dcc0,0xd864dcc0,0x3dee},
{0xd864dce4,0xd864dce4,0x3cf1},
{0xd864dce5,0xd864dce5,0x38bc},
{0xd864dcec,0xd864dcec,0x48a8},
{0xd864dced,0xd864dced,0x41cd},
{0xd864dd0d,0xd864dd0d,0x4306},
{0xd864dd10,0xd864dd10,0x48a9},
{0xd864dd3c,0xd864dd3c,0x48aa},
{0xd864dd4d,0xd864dd4d,0x3a5a},
{0xd864dd5b,0xd864dd5b,0x46e3},
{0xd864dd5e,0xd864dd5e,0x48ac},
{0xd864dd70,0xd864dd70,0x4244},
{0xd864dd9c,0xd864dd9c,0x41f6},
{0xd864dda8,0xd864dda8,0x3e1f},
{0xd864ddd5,0xd864ddd5,0x38cb},
{0xd864ddeb,0xd864ddeb,0x38ce},
{0xd865dc1d,0xd865dc1d,0x38e1},
{0xd865dc20,0xd865dc20,0x46e7},
{0xd865dc33,0xd865dc33,0x38de},
{0xd865dc3f,0xd865dc3f,0x3a76},
{0xd865dc48,0xd865dc48,0x3ba4},
{0xd865dcd0,0xd865dcd0,0x3f04},
{0xd865dcd9,0xd865dcd9,0x3735},
{0xd865dcda,0xd865dcda,0x3c03},
{0xd865dce5,0xd865dce5,0x4314},
{0xd865dce7,0xd865dce7,0x48af},
{0xd865dd9e,0xd865dd9e,0x490c},
{0xd865ddb0,0xd865ddb0,0x48b0},
{0xd865ddb8,0xd865ddb8,0x48b1},
{0xd865ddd7,0xd865ddd7,0x3c05},
{0xd865dde9,0xd865dde9,0x3e4d},
{0xd865ddf4,0xd865ddf4,0x4334},
{0xd865df20,0xd865df20,0x437f},
{0xd865df32,0xd865df32,0x48b2},
{0xd865dfd4,0xd865dfd4,0x4338},
{0xd866dc10,0xd866dc10,0x4511},
{0xd866dc57,0xd866dc57,0x38fd},
{0xd866dca4,0xd866dca4,0x3e15},
{0xd866dcd1,0xd866dcd1,0x48b3},
{0xd866dcea,0xd866dcea,0x3a61},
{0xd866dcf1,0xd866dcf1,0x3c1f},
{0xd866dcfa,0xd866dcfa,0x4a6f},
{0xd866dd03,0xd866dd03,0x4937},
{0xd866dd05,0xd866dd05,0x38fe},
{0xd866dd2f,0xd866dd2f,0x3c4a},
{0xd866dd45,0xd866dd45,0x4a4b},
{0xd866dd47,0xd866dd47,0x49c4},
{0xd866dd48,0xd866dd48,0x3c21},
{0xd866dd49,0xd866dd49,0x48b4},
{0xd866dd5d,0xd866dd5d,0x4180},
{0xd866dd6a,0xd866dd6a,0x48b5},
{0xd866dd9d,0xd866dd9d,0x3994},
{0xd866ddc3,0xd866ddc3,0x48b6},
{0xd866ddc9,0xd866ddc9,0x3db5},
{0xd866de28,0xd866de28,0x48b7},
{0xd866de4d,0xd866de4d,0x432d},
{0xd866df05,0xd866df05,0x390a},
{0xd866df0e,0xd866df0e,0x48b8},
{0xd866dfd5,0xd866dfd5,0x3907},
{0xd867dc73,0xd867dc73,0x49a3},
{0xd867dcad,0xd867dcad,0x3e14},
{0xd867dd3e,0xd867dd3e,0x3916},
{0xd867dd5a,0xd867dd5a,0x48b9},
{0xd867dd7c,0xd867dd7c,0x3ec3},
{0xd867dd98,0xd867dd98,0x3e65},
{0xd867dd9b,0xd867dd9b,0x48ba},
{0xd867ddf6,0xd867ddf6,0x457c},
{0xd867de06,0xd867de06,0x3efa},
{0xd867de2d,0xd867de2d,0x3c3d},
{0xd867de68,0xd867de68,0x391d},
{0xd867deac,0xd867deac,0x3ed5},
{0xd867deb0,0xd867deb0,0x4a45},
{0xd867dec3,0xd867dec3,0x45e5},
{0xd867def8,0xd867def8,0x48bc},
{0xd867df23,0xd867df23,0x48bd},
{0xd867df30,0xd867df30,0x490a},
{0xd867dfb7,0xd867dfb7,0x391f},
{0xd867dfde,0xd867dfde,0x3eda},
{0xd868dc14,0xd868dc14,0x46af},
{0xd868dc87,0xd868dc87,0x3795},
{0xd868dcb9,0xd868dcb9,0x392f},
{0xd868dce1,0xd868dce1,0x3922},
{0xd868dced,0xd868dced,0x3c48},
{0xd868dcf3,0xd868dcf3,0x3c49},
{0xd868dcf8,0xd868dcf8,0x380c},
{0xd868dcfe,0xd868dcfe,0x3c40},
{0xd868dd07,0xd868dd07,0x49b6},
{0xd868dd23,0xd868dd23,0x3923},
{0xd868dd33,0xd868dd33,0x457f},
{0xd868dd34,0xd868dd34,0x3927},
{0xd868dd50,0xd868dd50,0x40df},
{0xd868dd92,0xd868dd92,0x3920},
{0xd868dd93,0xd868dd93,0x392a},
{0xd868ddab,0xd868ddab,0x3921},
{0xd868ddb4,0xd868ddb4,0x3c47},
{0xd868ddb5,0xd868ddb5,0x4a79},
{0xd868dddf,0xd868dddf,0x3924},
{0xd868ddf5,0xd868ddf5,0x3c3f},
{0xd868de20,0xd868de20,0x392b},
{0xd868de33,0xd868de33,0x392d},
{0xd868de93,0xd868de93,0x48c0},
{0xd868de9f,0xd868de9f,0x3868},
{0xd868deb2,0xd868deb2,0x3f94},
{0xd868deb4,0xd868deb4,0x3930},
{0xd868deb6,0xd868deb6,0x46e1},
{0xd868deba,0xd868deba,0x49c0},
{0xd868debd,0xd868debd,0x4613},
{0xd868dedf,0xd868dedf,0x3e28},
{0xd868deff,0xd868deff,0x48c2},
{0xd868df51,0xd868df51,0x4a2b},
{0xd868dfa9,0xd868dfa9,0x4534},
{0xd868dfed,0xd868dfed,0x393c},
{0xd869dc34,0xd869dc34,0x3ef1},
{0xd869dc5b,0xd869dc5b,0x46bf},
{0xd869ddc6,0xd869ddc6,0x3aec},
{0xd869ddcb,0xd869ddcb,0x48c3},
{0xd869de01,0xd869de01,0x436f},
{0xd869de32,0xd869de32,0x4391},
{0xd869de4a,0xd869de4a,0x3f7e},
{0xd869de5b,0xd869de5b,0x4388},
{0xd869dea9,0xd869dea9,0x48f3},
{0xd86bddff,0xd86bddff,0x4ad0},
{0xd86bde67,0xd86bde67,0x3fa4},
{0xd86ddc2c,0xd86ddc2c,0x43ea},
{0xd86ddc73,0xd86ddc73,0x3f48},
{0xd86edd77,0xd86edd77,0x4346},
{0xd86edeb3,0xd86edeb3,0x4322},
{0xd86fdcd7,0xd86fdcd7,0x433d},
{0xd871ddf8,0xd871ddf8,0x4372},
{0xd872dda0,0xd872dda0,0x4383},
{0xd877df3c,0xd877df3c,0x3faf},
{0xd87edc06,0xd87edc06,0x3de3},
{0xd87edc25,0xd87edc25,0x3d58},
{0xd87edc28,0xd87edc28,0x43a4},
{0xd87edc29,0xd87edc29,0x4066},
{0xd87edc32,0xd87edc32,0x407e},
{0xd87edc3b,0xd87edc3b,0x47eb},
{0xd87edc40,0xd87edc40,0x4927},
{0xd87edc78,0xd87edc78,0x44e2},
{0xd87edc94,0xd87edc94,0x414b},
{0xd87edca6,0xd87edca6,0x4175},
{0xd87edccd,0xd87edccd,0x3de2},
{0xd87edcdb,0xd87edcdb,0x41c9},
{0xd87edd08,0xd87edd08,0x422f},
{0xd87edd22,0xd87edd22,0x4008},
{0xd87edd2f,0xd87edd2f,0x42a0},
{0xd87edd8f,0xd87edd8f,0x37b7},
{0xd87edd94,0xd87edd94,0x37be},
{0xd87eddb2,0xd87eddb2,0x3803},
{0xd87eddbc,0xd87eddbc,0x3b89},
{0xd87eddd4,0xd87eddd4,0x3848},
{0xd87eddd7,0xd87eddd7,0x3855},
{0xd87ede1b,0xd87ede1b,0x3946},
};

static pdf_cmap cmap_UniCNS_UTF16_H = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "UniCNS-UTF16-H",
	/* usecmap */ "UniCNS-X", NULL,
	/* wmode */ 0,
	/* codespaces */ 3, {
		{ 2, 0x0000, 0xd7ff },
		{ 4, 0xd800dc00, 0xdbffdfff },
		{ 2, 0xe000, 0xffff },
	},
	1687, 1687, (pdf_range*)cmap_UniCNS_UTF16_H_ranges,
	1728, 1728, (pdf_xrange*)cmap_UniCNS_UTF16_H_xranges,
	0, 0, NULL, /* mranges */
	0, 0, NULL, /* table */
	0, 0, 0, NULL /* splay tree */
};
