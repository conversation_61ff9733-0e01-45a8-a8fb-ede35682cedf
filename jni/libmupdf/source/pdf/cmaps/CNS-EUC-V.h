/* This is an automatically generated file. Do not edit. */

/* CNS-EUC-V */

static const pdf_range cmap_CNS_EUC_V_ranges[] = {
{0x20,0x7e,0x3550},
{0xa1a1,0xa1ab,0x63},
{0xa1ac,0xa1ac,0x354e},
{0xa1ad,0xa1ba,0x6f},
{0xa1bb,0xa1bb,0x7c},
{0xa1bc,0xa1bc,0x7e},
{0xa1bd,0xa1bd,0x7e},
{0xa1be,0xa1bf,0x82},
{0xa1c0,0xa1c1,0x82},
{0xa1c2,0xa1c3,0x86},
{0xa1c4,0xa1c5,0x86},
{0xa1c6,0xa1c7,0x8a},
{0xa1c8,0xa1c9,0x8a},
{0xa1ca,0xa1cb,0x8e},
{0xa1cc,0xa1cd,0x8e},
{0xa1ce,0xa1cf,0x92},
{0xa1d0,0xa1d1,0x92},
{0xa1d2,0xa1d3,0x96},
{0xa1d4,0xa1d5,0x96},
{0xa1d6,0xa1d7,0x9a},
{0xa1d8,0xa1d9,0x9a},
{0xa1da,0xa1db,0x9e},
{0xa1dc,0xa1fe,0x9e},
{0xa2a1,0xa2c3,0xc1},
{0xa2c4,0xa2c4,0x354f},
{0xa2c5,0xa2fe,0xe5},
{0xa3a1,0xa3ce,0x11f},
{0xa4a1,0xa4fe,0x14d},
{0xa5a1,0xa5ec,0x1ab},
{0xa5ee,0xa5f0,0x1f7},
{0xa6a1,0xa6be,0x1fa},
{0xa7a1,0xa7a1,0x253},
{0xa7a2,0xa7a4,0x218},
{0xa7a5,0xa7a5,0x254},
{0xa7a6,0xa7a6,0x21b},
{0xa7a7,0xa7a7,0x25a},
{0xa7a8,0xa7a8,0x21c},
{0xa7a9,0xa7ac,0x25b},
{0xa7ad,0xa7af,0x21d},
{0xa7b0,0xa7b0,0x25f},
{0xa7b1,0xa7b1,0x176e},
{0xa7b2,0xa7b2,0x260},
{0xa7b3,0xa7b3,0x262},
{0xa7b4,0xa7b4,0x220},
{0xa7b5,0xa7b5,0x263},
{0xa7b6,0xa7b6,0x176f},
{0xa7b7,0xa7b7,0x221},
{0xa7b8,0xa7b9,0x264},
{0xa7ba,0xa7ba,0x222},
{0xa7bb,0xa7bb,0x1770},
{0xa7bc,0xa7bc,0x223},
{0xa7bd,0xa7bd,0x266},
{0xa7be,0xa7be,0x279},
{0xa7bf,0xa7bf,0x1775},
{0xa7c0,0xa7c1,0x27a},
{0xa7c2,0xa7c2,0x224},
{0xa7c3,0xa7c6,0x27c},
{0xa7c7,0xa7c7,0x225},
{0xa7c8,0xa7cb,0x282},
{0xa7cc,0xa7cc,0x1776},
{0xa7cd,0xa7cd,0x286},
{0xa7ce,0xa7ce,0x226},
{0xa7cf,0xa7d0,0x288},
{0xa7d1,0xa7d2,0x28c},
{0xa7d3,0xa7d5,0x227},
{0xa7d6,0xa7d8,0x28e},
{0xa7d9,0xa7da,0x22a},
{0xa7db,0xa7db,0x1777},
{0xa7dc,0xa7df,0x2d0},
{0xa7e0,0xa7e0,0x2d5},
{0xa7e1,0xa7e1,0x22c},
{0xa7e2,0xa7e5,0x2d6},
{0xa7e6,0xa7e6,0x22d},
{0xa7e7,0xa7ed,0x2da},
{0xa7ee,0xa7ee,0x178a},
{0xa7ef,0xa7f2,0x2e1},
{0xa7f3,0xa7f3,0x178c},
{0xa7f4,0xa7f8,0x2e5},
{0xa7f9,0xa7f9,0x178d},
{0xa7fa,0xa7fd,0x2ea},
{0xa7fe,0xa7fe,0x356},
{0xa8a1,0xa8a6,0x357},
{0xa8a7,0xa8a7,0x35e},
{0xa8a8,0xa8a8,0x362},
{0xa8a9,0xa8aa,0x22e},
{0xa8ab,0xa8b2,0x363},
{0xa8b3,0xa8b3,0x17b2},
{0xa8b4,0xa8b6,0x36b},
{0xa8b7,0xa8ba,0x3f6},
{0xa8bb,0xa8bb,0x1812},
{0xa8bc,0xa8be,0x3fa},
{0xa8bf,0xa8c3,0x3fe},
{0xa8c4,0xa8cc,0x405},
{0xa8cd,0xa8cd,0x1813},
{0xa8ce,0xa8ce,0x1818},
{0xa8cf,0xa8d2,0x40f},
{0xa8d3,0xa8d3,0x1819},
{0xa8d4,0xa8d9,0x508},
{0xa8da,0xa8da,0x18e7},
{0xa8db,0xa8e2,0x50e},
{0xa8e3,0xa8e3,0x230},
{0xa8e4,0xa8e4,0x51b},
{0xa8e5,0xa8e7,0x520},
{0xa8e8,0xa8eb,0x696},
{0xa8ec,0xa8ec,0x231},
{0xa8ed,0xa8f0,0x69f},
{0xa8f1,0xa8fb,0x826},
{0xa8fc,0xa8fe,0x9f5},
{0xa9a1,0xa9a1,0x1e33},
{0xa9a2,0xa9a2,0x9f8},
{0xa9a3,0xa9a3,0x1e34},
{0xa9a4,0xa9a5,0x9f9},
{0xa9a6,0xa9ab,0xbe1},
{0xa9ac,0xa9ae,0xdbb},
{0xa9af,0xa9af,0x2360},
{0xa9b0,0xa9b0,0x2612},
{0xa9b1,0xa9b3,0xf7b},
{0xa9b4,0xa9b5,0x1100},
{0xa9b6,0xa9b6,0x1289},
{0xa9b7,0xa9b8,0x13b2},
{0xa9b9,0xa9b9,0x2f0d},
{0xc2a1,0xc2c1,0x232},
{0xc4a1,0xc4fe,0x253},
{0xc5a1,0xc5fe,0x2b1},
{0xc6a1,0xc6fe,0x30f},
{0xc7a1,0xc7fe,0x36d},
{0xc8a1,0xc8fe,0x3cb},
{0xc9a1,0xc9fe,0x429},
{0xcaa1,0xcafe,0x487},
{0xcba1,0xcbfe,0x4e5},
{0xcca1,0xccfe,0x543},
{0xcda1,0xcdfe,0x5a1},
{0xcea1,0xcefe,0x5ff},
{0xcfa1,0xcffe,0x65d},
{0xd0a1,0xd0fe,0x6bb},
{0xd1a1,0xd1fe,0x719},
{0xd2a1,0xd2fe,0x777},
{0xd3a1,0xd3fe,0x7d5},
{0xd4a1,0xd4fe,0x833},
{0xd5a1,0xd5fe,0x891},
{0xd6a1,0xd6fe,0x8ef},
{0xd7a1,0xd7fe,0x94d},
{0xd8a1,0xd8fe,0x9ab},
{0xd9a1,0xd9fe,0xa09},
{0xdaa1,0xdafe,0xa67},
{0xdba1,0xdbfe,0xac5},
{0xdca1,0xdcfe,0xb23},
{0xdda1,0xddfe,0xb81},
{0xdea1,0xdefe,0xbdf},
{0xdfa1,0xdffe,0xc3d},
{0xe0a1,0xe0fe,0xc9b},
{0xe1a1,0xe1fe,0xcf9},
{0xe2a1,0xe2fe,0xd57},
{0xe3a1,0xe3fe,0xdb5},
{0xe4a1,0xe4fe,0xe13},
{0xe5a1,0xe5fe,0xe71},
{0xe6a1,0xe6fe,0xecf},
{0xe7a1,0xe7fe,0xf2d},
{0xe8a1,0xe8fe,0xf8b},
{0xe9a1,0xe9fe,0xfe9},
{0xeaa1,0xeafe,0x1047},
{0xeba1,0xebfe,0x10a5},
{0xeca1,0xecfe,0x1103},
{0xeda1,0xedfe,0x1161},
{0xeea1,0xeefe,0x11bf},
{0xefa1,0xeffe,0x121d},
{0xf0a1,0xf0fe,0x127b},
{0xf1a1,0xf1fe,0x12d9},
{0xf2a1,0xf2fe,0x1337},
{0xf3a1,0xf3fe,0x1395},
{0xf4a1,0xf4fe,0x13f3},
{0xf5a1,0xf5fe,0x1451},
{0xf6a1,0xf6fe,0x14af},
{0xf7a1,0xf7fe,0x150d},
{0xf8a1,0xf8fe,0x156b},
{0xf9a1,0xf9fe,0x15c9},
{0xfaa1,0xfafe,0x1627},
{0xfba1,0xfbfe,0x1685},
{0xfca1,0xfcfe,0x16e3},
{0xfda1,0xfdcb,0x1741},
};

static const pdf_xrange cmap_CNS_EUC_V_xranges[] = {
{0x8ea1a1a1,0x8ea1a1ab,0x63},
{0x8ea1a1ac,0x8ea1a1ac,0x354e},
{0x8ea1a1ad,0x8ea1a1ba,0x6f},
{0x8ea1a1bb,0x8ea1a1bb,0x7c},
{0x8ea1a1bc,0x8ea1a1bc,0x7e},
{0x8ea1a1bd,0x8ea1a1bd,0x7e},
{0x8ea1a1be,0x8ea1a1bf,0x82},
{0x8ea1a1c0,0x8ea1a1c1,0x82},
{0x8ea1a1c2,0x8ea1a1c3,0x86},
{0x8ea1a1c4,0x8ea1a1c5,0x86},
{0x8ea1a1c6,0x8ea1a1c7,0x8a},
{0x8ea1a1c8,0x8ea1a1c9,0x8a},
{0x8ea1a1ca,0x8ea1a1cb,0x8e},
{0x8ea1a1cc,0x8ea1a1cd,0x8e},
{0x8ea1a1ce,0x8ea1a1cf,0x92},
{0x8ea1a1d0,0x8ea1a1d1,0x92},
{0x8ea1a1d2,0x8ea1a1d3,0x96},
{0x8ea1a1d4,0x8ea1a1d5,0x96},
{0x8ea1a1d6,0x8ea1a1d7,0x9a},
{0x8ea1a1d8,0x8ea1a1d9,0x9a},
{0x8ea1a1da,0x8ea1a1db,0x9e},
{0x8ea1a1dc,0x8ea1a1fe,0x9e},
{0x8ea1a2a1,0x8ea1a2c3,0xc1},
{0x8ea1a2c4,0x8ea1a2c4,0x354f},
{0x8ea1a2c5,0x8ea1a2fe,0xe5},
{0x8ea1a3a1,0x8ea1a3ce,0x11f},
{0x8ea1a4a1,0x8ea1a4fe,0x14d},
{0x8ea1a5a1,0x8ea1a5ec,0x1ab},
{0x8ea1a5ee,0x8ea1a5f0,0x1f7},
{0x8ea1a6a1,0x8ea1a6be,0x1fa},
{0x8ea1a7a1,0x8ea1a7a1,0x253},
{0x8ea1a7a2,0x8ea1a7a4,0x218},
{0x8ea1a7a5,0x8ea1a7a5,0x254},
{0x8ea1a7a6,0x8ea1a7a6,0x21b},
{0x8ea1a7a7,0x8ea1a7a7,0x25a},
{0x8ea1a7a8,0x8ea1a7a8,0x21c},
{0x8ea1a7a9,0x8ea1a7ac,0x25b},
{0x8ea1a7ad,0x8ea1a7af,0x21d},
{0x8ea1a7b0,0x8ea1a7b0,0x25f},
{0x8ea1a7b1,0x8ea1a7b1,0x176e},
{0x8ea1a7b2,0x8ea1a7b2,0x260},
{0x8ea1a7b3,0x8ea1a7b3,0x262},
{0x8ea1a7b4,0x8ea1a7b4,0x220},
{0x8ea1a7b5,0x8ea1a7b5,0x263},
{0x8ea1a7b6,0x8ea1a7b6,0x176f},
{0x8ea1a7b7,0x8ea1a7b7,0x221},
{0x8ea1a7b8,0x8ea1a7b9,0x264},
{0x8ea1a7ba,0x8ea1a7ba,0x222},
{0x8ea1a7bb,0x8ea1a7bb,0x1770},
{0x8ea1a7bc,0x8ea1a7bc,0x223},
{0x8ea1a7bd,0x8ea1a7bd,0x266},
{0x8ea1a7be,0x8ea1a7be,0x279},
{0x8ea1a7bf,0x8ea1a7bf,0x1775},
{0x8ea1a7c0,0x8ea1a7c1,0x27a},
{0x8ea1a7c2,0x8ea1a7c2,0x224},
{0x8ea1a7c3,0x8ea1a7c6,0x27c},
{0x8ea1a7c7,0x8ea1a7c7,0x225},
{0x8ea1a7c8,0x8ea1a7cb,0x282},
{0x8ea1a7cc,0x8ea1a7cc,0x1776},
{0x8ea1a7cd,0x8ea1a7cd,0x286},
{0x8ea1a7ce,0x8ea1a7ce,0x226},
{0x8ea1a7cf,0x8ea1a7d0,0x288},
{0x8ea1a7d1,0x8ea1a7d2,0x28c},
{0x8ea1a7d3,0x8ea1a7d5,0x227},
{0x8ea1a7d6,0x8ea1a7d8,0x28e},
{0x8ea1a7d9,0x8ea1a7da,0x22a},
{0x8ea1a7db,0x8ea1a7db,0x1777},
{0x8ea1a7dc,0x8ea1a7df,0x2d0},
{0x8ea1a7e0,0x8ea1a7e0,0x2d5},
{0x8ea1a7e1,0x8ea1a7e1,0x22c},
{0x8ea1a7e2,0x8ea1a7e5,0x2d6},
{0x8ea1a7e6,0x8ea1a7e6,0x22d},
{0x8ea1a7e7,0x8ea1a7ed,0x2da},
{0x8ea1a7ee,0x8ea1a7ee,0x178a},
{0x8ea1a7ef,0x8ea1a7f2,0x2e1},
{0x8ea1a7f3,0x8ea1a7f3,0x178c},
{0x8ea1a7f4,0x8ea1a7f8,0x2e5},
{0x8ea1a7f9,0x8ea1a7f9,0x178d},
{0x8ea1a7fa,0x8ea1a7fd,0x2ea},
{0x8ea1a7fe,0x8ea1a7fe,0x356},
{0x8ea1a8a1,0x8ea1a8a6,0x357},
{0x8ea1a8a7,0x8ea1a8a7,0x35e},
{0x8ea1a8a8,0x8ea1a8a8,0x362},
{0x8ea1a8a9,0x8ea1a8aa,0x22e},
{0x8ea1a8ab,0x8ea1a8b2,0x363},
{0x8ea1a8b3,0x8ea1a8b3,0x17b2},
{0x8ea1a8b4,0x8ea1a8b6,0x36b},
{0x8ea1a8b7,0x8ea1a8ba,0x3f6},
{0x8ea1a8bb,0x8ea1a8bb,0x1812},
{0x8ea1a8bc,0x8ea1a8be,0x3fa},
{0x8ea1a8bf,0x8ea1a8c3,0x3fe},
{0x8ea1a8c4,0x8ea1a8cc,0x405},
{0x8ea1a8cd,0x8ea1a8cd,0x1813},
{0x8ea1a8ce,0x8ea1a8ce,0x1818},
{0x8ea1a8cf,0x8ea1a8d2,0x40f},
{0x8ea1a8d3,0x8ea1a8d3,0x1819},
{0x8ea1a8d4,0x8ea1a8d9,0x508},
{0x8ea1a8da,0x8ea1a8da,0x18e7},
{0x8ea1a8db,0x8ea1a8e2,0x50e},
{0x8ea1a8e3,0x8ea1a8e3,0x230},
{0x8ea1a8e4,0x8ea1a8e4,0x51b},
{0x8ea1a8e5,0x8ea1a8e7,0x520},
{0x8ea1a8e8,0x8ea1a8eb,0x696},
{0x8ea1a8ec,0x8ea1a8ec,0x231},
{0x8ea1a8ed,0x8ea1a8f0,0x69f},
{0x8ea1a8f1,0x8ea1a8fb,0x826},
{0x8ea1a8fc,0x8ea1a8fe,0x9f5},
{0x8ea1a9a1,0x8ea1a9a1,0x1e33},
{0x8ea1a9a2,0x8ea1a9a2,0x9f8},
{0x8ea1a9a3,0x8ea1a9a3,0x1e34},
{0x8ea1a9a4,0x8ea1a9a5,0x9f9},
{0x8ea1a9a6,0x8ea1a9ab,0xbe1},
{0x8ea1a9ac,0x8ea1a9ae,0xdbb},
{0x8ea1a9af,0x8ea1a9af,0x2360},
{0x8ea1a9b0,0x8ea1a9b0,0x2612},
{0x8ea1a9b1,0x8ea1a9b3,0xf7b},
{0x8ea1a9b4,0x8ea1a9b5,0x1100},
{0x8ea1a9b6,0x8ea1a9b6,0x1289},
{0x8ea1a9b7,0x8ea1a9b8,0x13b2},
{0x8ea1a9b9,0x8ea1a9b9,0x2f0d},
{0x8ea1c2a1,0x8ea1c2c1,0x232},
{0x8ea1c4a1,0x8ea1c4fe,0x253},
{0x8ea1c5a1,0x8ea1c5fe,0x2b1},
{0x8ea1c6a1,0x8ea1c6fe,0x30f},
{0x8ea1c7a1,0x8ea1c7fe,0x36d},
{0x8ea1c8a1,0x8ea1c8fe,0x3cb},
{0x8ea1c9a1,0x8ea1c9fe,0x429},
{0x8ea1caa1,0x8ea1cafe,0x487},
{0x8ea1cba1,0x8ea1cbfe,0x4e5},
{0x8ea1cca1,0x8ea1ccfe,0x543},
{0x8ea1cda1,0x8ea1cdfe,0x5a1},
{0x8ea1cea1,0x8ea1cefe,0x5ff},
{0x8ea1cfa1,0x8ea1cffe,0x65d},
{0x8ea1d0a1,0x8ea1d0fe,0x6bb},
{0x8ea1d1a1,0x8ea1d1fe,0x719},
{0x8ea1d2a1,0x8ea1d2fe,0x777},
{0x8ea1d3a1,0x8ea1d3fe,0x7d5},
{0x8ea1d4a1,0x8ea1d4fe,0x833},
{0x8ea1d5a1,0x8ea1d5fe,0x891},
{0x8ea1d6a1,0x8ea1d6fe,0x8ef},
{0x8ea1d7a1,0x8ea1d7fe,0x94d},
{0x8ea1d8a1,0x8ea1d8fe,0x9ab},
{0x8ea1d9a1,0x8ea1d9fe,0xa09},
{0x8ea1daa1,0x8ea1dafe,0xa67},
{0x8ea1dba1,0x8ea1dbfe,0xac5},
{0x8ea1dca1,0x8ea1dcfe,0xb23},
{0x8ea1dda1,0x8ea1ddfe,0xb81},
{0x8ea1dea1,0x8ea1defe,0xbdf},
{0x8ea1dfa1,0x8ea1dffe,0xc3d},
{0x8ea1e0a1,0x8ea1e0fe,0xc9b},
{0x8ea1e1a1,0x8ea1e1fe,0xcf9},
{0x8ea1e2a1,0x8ea1e2fe,0xd57},
{0x8ea1e3a1,0x8ea1e3fe,0xdb5},
{0x8ea1e4a1,0x8ea1e4fe,0xe13},
{0x8ea1e5a1,0x8ea1e5fe,0xe71},
{0x8ea1e6a1,0x8ea1e6fe,0xecf},
{0x8ea1e7a1,0x8ea1e7fe,0xf2d},
{0x8ea1e8a1,0x8ea1e8fe,0xf8b},
{0x8ea1e9a1,0x8ea1e9fe,0xfe9},
{0x8ea1eaa1,0x8ea1eafe,0x1047},
{0x8ea1eba1,0x8ea1ebfe,0x10a5},
{0x8ea1eca1,0x8ea1ecfe,0x1103},
{0x8ea1eda1,0x8ea1edfe,0x1161},
{0x8ea1eea1,0x8ea1eefe,0x11bf},
{0x8ea1efa1,0x8ea1effe,0x121d},
{0x8ea1f0a1,0x8ea1f0fe,0x127b},
{0x8ea1f1a1,0x8ea1f1fe,0x12d9},
{0x8ea1f2a1,0x8ea1f2fe,0x1337},
{0x8ea1f3a1,0x8ea1f3fe,0x1395},
{0x8ea1f4a1,0x8ea1f4fe,0x13f3},
{0x8ea1f5a1,0x8ea1f5fe,0x1451},
{0x8ea1f6a1,0x8ea1f6fe,0x14af},
{0x8ea1f7a1,0x8ea1f7fe,0x150d},
{0x8ea1f8a1,0x8ea1f8fe,0x156b},
{0x8ea1f9a1,0x8ea1f9fe,0x15c9},
{0x8ea1faa1,0x8ea1fafe,0x1627},
{0x8ea1fba1,0x8ea1fbfe,0x1685},
{0x8ea1fca1,0x8ea1fcfe,0x16e3},
{0x8ea1fda1,0x8ea1fdcb,0x1741},
{0x8ea2a1a1,0x8ea2a1fe,0x176c},
{0x8ea2a2a1,0x8ea2a2fe,0x17ca},
{0x8ea2a3a1,0x8ea2a3fe,0x1828},
{0x8ea2a4a1,0x8ea2a4fe,0x1886},
{0x8ea2a5a1,0x8ea2a5fe,0x18e4},
{0x8ea2a6a1,0x8ea2a6fe,0x1942},
{0x8ea2a7a1,0x8ea2a7fe,0x19a0},
{0x8ea2a8a1,0x8ea2a8fe,0x19fe},
{0x8ea2a9a1,0x8ea2a9fe,0x1a5c},
{0x8ea2aaa1,0x8ea2aafe,0x1aba},
{0x8ea2aba1,0x8ea2abfe,0x1b18},
{0x8ea2aca1,0x8ea2acfe,0x1b76},
{0x8ea2ada1,0x8ea2adfe,0x1bd4},
{0x8ea2aea1,0x8ea2aefe,0x1c32},
{0x8ea2afa1,0x8ea2affe,0x1c90},
{0x8ea2b0a1,0x8ea2b0fe,0x1cee},
{0x8ea2b1a1,0x8ea2b1fe,0x1d4c},
{0x8ea2b2a1,0x8ea2b2fe,0x1daa},
{0x8ea2b3a1,0x8ea2b3fe,0x1e08},
{0x8ea2b4a1,0x8ea2b4fe,0x1e66},
{0x8ea2b5a1,0x8ea2b5fe,0x1ec4},
{0x8ea2b6a1,0x8ea2b6fe,0x1f22},
{0x8ea2b7a1,0x8ea2b7fe,0x1f80},
{0x8ea2b8a1,0x8ea2b8fe,0x1fde},
{0x8ea2b9a1,0x8ea2b9fe,0x203c},
{0x8ea2baa1,0x8ea2bafe,0x209a},
{0x8ea2bba1,0x8ea2bbfe,0x20f8},
{0x8ea2bca1,0x8ea2bcfe,0x2156},
{0x8ea2bda1,0x8ea2bdfe,0x21b4},
{0x8ea2bea1,0x8ea2befe,0x2212},
{0x8ea2bfa1,0x8ea2bffe,0x2270},
{0x8ea2c0a1,0x8ea2c0fe,0x22ce},
{0x8ea2c1a1,0x8ea2c1fe,0x232c},
{0x8ea2c2a1,0x8ea2c2fe,0x238a},
{0x8ea2c3a1,0x8ea2c3fe,0x23e8},
{0x8ea2c4a1,0x8ea2c4fe,0x2446},
{0x8ea2c5a1,0x8ea2c5fe,0x24a4},
{0x8ea2c6a1,0x8ea2c6fe,0x2502},
{0x8ea2c7a1,0x8ea2c7fe,0x2560},
{0x8ea2c8a1,0x8ea2c8fe,0x25be},
{0x8ea2c9a1,0x8ea2c9fe,0x261c},
{0x8ea2caa1,0x8ea2cafe,0x267a},
{0x8ea2cba1,0x8ea2cbfe,0x26d8},
{0x8ea2cca1,0x8ea2ccfe,0x2736},
{0x8ea2cda1,0x8ea2cdfe,0x2794},
{0x8ea2cea1,0x8ea2cefe,0x27f2},
{0x8ea2cfa1,0x8ea2cffe,0x2850},
{0x8ea2d0a1,0x8ea2d0fe,0x28ae},
{0x8ea2d1a1,0x8ea2d1fe,0x290c},
{0x8ea2d2a1,0x8ea2d2fe,0x296a},
{0x8ea2d3a1,0x8ea2d3fe,0x29c8},
{0x8ea2d4a1,0x8ea2d4fe,0x2a26},
{0x8ea2d5a1,0x8ea2d5fe,0x2a84},
{0x8ea2d6a1,0x8ea2d6fe,0x2ae2},
{0x8ea2d7a1,0x8ea2d7fe,0x2b40},
{0x8ea2d8a1,0x8ea2d8fe,0x2b9e},
{0x8ea2d9a1,0x8ea2d9fe,0x2bfc},
{0x8ea2daa1,0x8ea2dafe,0x2c5a},
{0x8ea2dba1,0x8ea2dbfe,0x2cb8},
{0x8ea2dca1,0x8ea2dcfe,0x2d16},
{0x8ea2dda1,0x8ea2ddfe,0x2d74},
{0x8ea2dea1,0x8ea2defe,0x2dd2},
{0x8ea2dfa1,0x8ea2dffe,0x2e30},
{0x8ea2e0a1,0x8ea2e0fe,0x2e8e},
{0x8ea2e1a1,0x8ea2e1fe,0x2eec},
{0x8ea2e2a1,0x8ea2e2fe,0x2f4a},
{0x8ea2e3a1,0x8ea2e3fe,0x2fa8},
{0x8ea2e4a1,0x8ea2e4fe,0x3006},
{0x8ea2e5a1,0x8ea2e5fe,0x3064},
{0x8ea2e6a1,0x8ea2e6fe,0x30c2},
{0x8ea2e7a1,0x8ea2e7fe,0x3120},
{0x8ea2e8a1,0x8ea2e8fe,0x317e},
{0x8ea2e9a1,0x8ea2e9fe,0x31dc},
{0x8ea2eaa1,0x8ea2eafe,0x323a},
{0x8ea2eba1,0x8ea2ebfe,0x3298},
{0x8ea2eca1,0x8ea2ecfe,0x32f6},
{0x8ea2eda1,0x8ea2edfe,0x3354},
{0x8ea2eea1,0x8ea2eefe,0x33b2},
{0x8ea2efa1,0x8ea2effe,0x3410},
{0x8ea2f0a1,0x8ea2f0fe,0x346e},
{0x8ea2f1a1,0x8ea2f1fe,0x34cc},
{0x8ea2f2a1,0x8ea2f2c4,0x352a},
};

static pdf_cmap cmap_CNS_EUC_V = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "CNS-EUC-V",
	/* usecmap */ "", NULL,
	/* wmode */ 1,
	/* codespaces */ 5, {
		{ 1, 0x00, 0x80 },
		{ 4, 0x8ea1a1a1, 0x8ea1fefe },
		{ 4, 0x8ea2a1a1, 0x8ea2fefe },
		{ 4, 0x8ea3a1a1, 0x8ea3fefe },
		{ 2, 0xa1a1, 0xfefe },
	},
	180, 180, (pdf_range*)cmap_CNS_EUC_V_ranges,
	261, 261, (pdf_xrange*)cmap_CNS_EUC_V_xranges,
	0, 0, NULL, /* mranges */
	0, 0, NULL, /* table */
	0, 0, 0, NULL /* splay tree */
};
