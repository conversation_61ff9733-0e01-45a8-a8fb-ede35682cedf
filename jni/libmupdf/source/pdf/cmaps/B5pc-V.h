/* This is an automatically generated file. Do not edit. */

/* B5pc-V */

static const pdf_range cmap_B5pc_V_ranges[] = {
{0xa14b,0xa14b,0x354e},
{0xa15a,0xa15a,0x35af},
{0xa15c,0xa15c,0x35b1},
{0xa15d,0xa15e,0x82},
{0xa161,0xa162,0x86},
{0xa165,0xa166,0x8a},
{0xa169,0xa16a,0x8e},
{0xa16d,0xa16e,0x92},
{0xa171,0xa172,0x96},
{0xa175,0xa176,0x9a},
{0xa179,0xa17a,0x9e},
{0xa1e3,0xa1e3,0x354f},
};

static pdf_cmap cmap_B5pc_V = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "B5pc-V",
	/* usecmap */ "B5pc-H", NULL,
	/* wmode */ 1,
	/* codespaces */ 0, {
		{ 0, 0, 0 },
	},
	12, 12, (pdf_range*)cmap_B5pc_V_ranges,
	0, 0, NULL, /* xranges */
	0, 0, NULL, /* mranges */
	0, 0, NULL, /* table */
	0, 0, 0, NULL /* splay tree */
};
