/* This is an automatically generated file. Do not edit. */

/* KSCms-UHC-V */

static const pdf_range cmap_KSCms_UHC_V_ranges[] = {
{0xa1a2,0xa1a3,0x1f78},
{0xa1a5,0xa1a5,0x1f7a},
{0xa1a6,0xa1a6,0x2080},
{0xa1a9,0xa1ab,0x1f7b},
{0xa1ad,0xa1ad,0x1f7e},
{0xa1b2,0xa1bd,0x1f7f},
{0xa1eb,0xa1eb,0x1f8b},
{0xa3a1,0xa3a1,0x1f8c},
{0xa3a8,0xa3a9,0x1f8d},
{0xa3ac,0xa3ac,0x1f8f},
{0xa3ae,0xa3ae,0x1f90},
{0xa3ba,0xa3bf,0x1f91},
{0xa3db,0xa3db,0x1f97},
{0xa3dd,0xa3dd,0x1f98},
{0xa3df,0xa3df,0x1f99},
{0xa3fb,0xa3fe,0x1f9a},
};

static pdf_cmap cmap_KSCms_UHC_V = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "KSCms-UHC-V",
	/* usecmap */ "KSCms-UHC-H", NULL,
	/* wmode */ 1,
	/* codespaces */ 0, {
		{ 0, 0, 0 },
	},
	16, 16, (pdf_range*)cmap_KSCms_UHC_V_ranges,
	0, 0, NULL, /* xranges */
	0, 0, NULL, /* mranges */
	0, 0, NULL, /* table */
	0, 0, 0, NULL /* splay tree */
};
