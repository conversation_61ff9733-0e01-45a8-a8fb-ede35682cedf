/* This is an automatically generated file. Do not edit. */

/* UniJIS-X */

static const pdf_range cmap_UniJIS_X_ranges[] = {
{0x20,0x5b,0x1},
{0x5c,0x5c,0x61},
{0x5d,0x7b,0x3e},
{0x7d,0x7d,0x5e},
{0xa1,0xa3,0x65},
{0xa4,0xa4,0x6b},
{0xa5,0xa5,0x3d},
{0xa7,0xa7,0x2d0},
{0xa8,0xa8,0x287},
{0xa9,0xa9,0x98},
{0xaa,0xaa,0x8c},
{0xab,0xab,0x6d},
{0xac,0xac,0x99},
{0xad,0xad,0x97},
{0xae,0xae,0x9a},
{0xaf,0xaf,0x81},
{0xb0,0xb0,0x2c3},
{0xb1,0xb1,0x2b6},
{0xb2,0xb3,0x9d},
{0xb4,0xb4,0x285},
{0xb5,0xb5,0x9f},
{0xb6,0xb6,0x30a},
{0xb7,0xb7,0x75},
{0xb8,0xb8,0x86},
{0xb9,0xb9,0xa0},
{0xba,0xba,0x90},
{0xbb,0xbb,0x7b},
{0xbc,0xbe,0xa1},
{0xbf,0xbf,0x7e},
{0xc0,0xc5,0xa4},
{0xc6,0xc6,0x8b},
{0xc7,0xd6,0xaa},
{0xd7,0xd7,0x2b7},
{0xd8,0xd8,0x8e},
{0xd9,0xde,0xbb},
{0xdf,0xdf,0x96},
{0xe0,0xe5,0xc1},
{0xe6,0xe6,0x91},
{0xe7,0xf6,0xc7},
{0xf7,0xf7,0x2b8},
{0xf8,0xf8,0x94},
{0xf9,0xff,0xd8},
{0x100,0x100,0x2496},
{0x101,0x101,0x2491},
{0x112,0x112,0x2499},
{0x113,0x113,0x2494},
{0x11a,0x11a,0x24b3},
{0x11b,0x11b,0x24bf},
{0x128,0x128,0x24b8},
{0x129,0x129,0x24c4},
{0x12a,0x12a,0x2497},
{0x12b,0x12b,0x2492},
{0x131,0x131,0x92},
{0x141,0x141,0x8d},
{0x142,0x142,0x93},
{0x14b,0x14b,0x24dc},
{0x14c,0x14c,0x249a},
{0x14d,0x14d,0x2495},
{0x152,0x152,0x8f},
{0x153,0x153,0x95},
{0x160,0x160,0xdf},
{0x161,0x161,0xe3},
{0x168,0x168,0x24bd},
{0x169,0x169,0x24c9},
{0x16a,0x16a,0x2498},
{0x16b,0x16b,0x2493},
{0x16e,0x16e,0x24bc},
{0x16f,0x16f,0x24c8},
{0x178,0x178,0xe0},
{0x17d,0x17d,0xe1},
{0x17e,0x17e,0xe5},
{0x1cd,0x1cd,0x24b2},
{0x1ce,0x1ce,0x24be},
{0x1cf,0x1cf,0x24b6},
{0x1d0,0x1d0,0x24c2},
{0x1d1,0x1d1,0x24b9},
{0x1d2,0x1d2,0x24c5},
{0x1d3,0x1d3,0x24bb},
{0x1d4,0x1d4,0x24c7},
{0x1fd,0x1fd,0x24cd},
{0x251,0x251,0x24ca},
{0x254,0x254,0x24cf},
{0x259,0x259,0x24d2},
{0x25a,0x25a,0x24d5},
{0x25b,0x25b,0x24d8},
{0x275,0x275,0x24dd},
{0x283,0x283,0x24e2},
{0x28c,0x28c,0x24de},
{0x292,0x292,0x24e1},
{0x2d0,0x2d0,0x24e3},
{0x300,0x300,0x41},
{0x301,0x302,0x7f},
{0x303,0x303,0x5f},
{0x304,0x304,0x81},
{0x305,0x305,0xe2},
{0x306,0x308,0x82},
{0x30a,0x30a,0x85},
{0x30b,0x30b,0x87},
{0x30c,0x30c,0x89},
{0x327,0x327,0x86},
{0x328,0x328,0x88},
{0x332,0x332,0x40},
{0x336,0x336,0x8a},
{0x391,0x3a1,0x3f3},
{0x3a3,0x3a9,0x404},
{0x3b1,0x3c1,0x40b},
{0x3c3,0x3c9,0x41c},
{0x3d0,0x3d0,0x2f3a},
{0x3d1,0x3d1,0x2f40},
{0x3db,0x3db,0x2f3f},
{0x401,0x401,0x429},
{0x410,0x415,0x423},
{0x416,0x435,0x42a},
{0x436,0x44f,0x44b},
{0x451,0x451,0x44a},
{0x1ebc,0x1ebc,0x24b5},
{0x1ebd,0x1ebd,0x24c1},
{0x2002,0x2002,0xe7},
{0x2003,0x2003,0x279},
{0x2010,0x2010,0x296},
{0x2011,0x2011,0xe},
{0x2012,0x2012,0x72},
{0x2013,0x2013,0x72},
{0x2014,0x2014,0x8a},
{0x2015,0x2015,0x295},
{0x2016,0x2016,0x29a},
{0x201a,0x201a,0x78},
{0x201e,0x201e,0x79},
{0x2020,0x2021,0x308},
{0x2022,0x2022,0x77},
{0x2025,0x2025,0x29d},
{0x2026,0x2026,0x29c},
{0x2030,0x2030,0x304},
{0x2032,0x2033,0x2c4},
{0x2039,0x203a,0x6e},
{0x203b,0x203b,0x2de},
{0x203c,0x203c,0x2f4f},
{0x2044,0x2044,0x68},
{0x2049,0x2049,0x2f50},
{0x2070,0x2070,0x24a1},
{0x2074,0x2079,0x24a2},
{0x2080,0x2089,0x24a8},
{0x20ac,0x20ac,0x248a},
{0x2100,0x2100,0x2e4f},
{0x2103,0x2103,0x2c6},
{0x2105,0x2105,0x2e53},
{0x2109,0x2109,0x2071},
{0x210a,0x210a,0x2070},
{0x210f,0x210f,0x2f3c},
{0x2113,0x2113,0x1f59},
{0x2116,0x2116,0x1dba},
{0x2121,0x2121,0x1f77},
{0x2122,0x2122,0xe4},
{0x2126,0x2126,0x248b},
{0x212b,0x212b,0x303},
{0x2135,0x2135,0x2f39},
{0x2153,0x2154,0x249f},
{0x215b,0x215e,0x249b},
{0x2160,0x2169,0x1d97},
{0x216a,0x216b,0x2021},
{0x2170,0x2179,0x1f9c},
{0x217a,0x217b,0x206a},
{0x217f,0x217f,0x206f},
{0x2190,0x2191,0x2e1},
{0x2192,0x2192,0x2e0},
{0x2193,0x2193,0x2e3},
{0x2194,0x2194,0x2fa9},
{0x2196,0x2197,0x2fac},
{0x2198,0x2199,0x2faa},
{0x21c4,0x21c5,0x2076},
{0x21c6,0x21c6,0x2075},
{0x21cc,0x21cc,0x2fae},
{0x21d0,0x21d0,0x2fa8},
{0x21d2,0x21d2,0x2f0},
{0x21d4,0x21d4,0x2f1},
{0x21e6,0x21e6,0x1f4d},
{0x21e7,0x21e7,0x1f4c},
{0x21e8,0x21e8,0x1f4e},
{0x21e9,0x21e9,0x1f4b},
{0x2200,0x2200,0x2f2},
{0x2202,0x2202,0x2f7},
{0x2203,0x2203,0x2f3},
{0x2205,0x2205,0x2f98},
{0x2207,0x2207,0x2f8},
{0x2208,0x2208,0x2e5},
{0x220a,0x220a,0x2f3b},
{0x220b,0x220b,0x2e6},
{0x2211,0x2211,0x1dc9},
{0x2212,0x2212,0x2b5},
{0x2213,0x2213,0x2f56},
{0x221a,0x221a,0x2fd},
{0x221d,0x221d,0x2ff},
{0x221e,0x221e,0x2bf},
{0x221f,0x221f,0x1dcd},
{0x2220,0x2220,0x2f4},
{0x2227,0x2228,0x2ed},
{0x2229,0x2229,0x2ec},
{0x222a,0x222a,0x2eb},
{0x222b,0x222c,0x301},
{0x222d,0x222d,0x2003},
{0x222e,0x222e,0x1dc8},
{0x2234,0x2234,0x2c0},
{0x2235,0x2235,0x300},
{0x223d,0x223d,0x2fe},
{0x2243,0x2243,0x2f58},
{0x2252,0x2252,0x2fa},
{0x2260,0x2260,0x2ba},
{0x2261,0x2261,0x2f9},
{0x2266,0x2267,0x2bd},
{0x226a,0x226b,0x2fb},
{0x2272,0x2273,0x2f59},
{0x2282,0x2283,0x2e9},
{0x2286,0x2287,0x2e7},
{0x2295,0x2295,0x2f9c},
{0x2296,0x2296,0x2f9a},
{0x2297,0x2297,0x2f9d},
{0x2298,0x2298,0x2f9b},
{0x22a0,0x22a0,0x2f99},
{0x22a5,0x22a5,0x2f5},
{0x22bf,0x22bf,0x1dce},
{0x2307,0x2307,0x2fbb},
{0x2312,0x2312,0x2f6},
{0x2460,0x2473,0x1d83},
{0x2474,0x2487,0x1f87},
{0x2488,0x2490,0x1f7e},
{0x249c,0x24b5,0x1fb0},
{0x24b6,0x24cf,0x2863},
{0x24d0,0x24e9,0x2849},
{0x24ea,0x24ea,0x2020},
{0x2500,0x254b,0x1d37},
{0x2550,0x2550,0x203b},
{0x255e,0x255e,0x203c},
{0x2561,0x2561,0x203e},
{0x256a,0x256a,0x203d},
{0x256d,0x256e,0x2037},
{0x256f,0x256f,0x203a},
{0x2570,0x2570,0x2039},
{0x2571,0x2573,0x2045},
{0x2581,0x2588,0x2026},
{0x2589,0x2589,0x2034},
{0x258a,0x258a,0x2033},
{0x258b,0x258b,0x2032},
{0x258c,0x258c,0x2031},
{0x258d,0x258d,0x2030},
{0x258e,0x258e,0x202f},
{0x258f,0x258f,0x202e},
{0x2594,0x2595,0x2035},
{0x25a0,0x25a0,0x2d9},
{0x25a1,0x25a1,0x2d8},
{0x25a2,0x25a2,0x1f4f},
{0x25aa,0x25aa,0x2fcf},
{0x25ab,0x25ab,0x2fcd},
{0x25b2,0x25b2,0x2db},
{0x25b3,0x25b3,0x2da},
{0x25b6,0x25b6,0x2fa3},
{0x25b7,0x25b7,0x1f4a},
{0x25bc,0x25bc,0x2dd},
{0x25bd,0x25bd,0x2dc},
{0x25c0,0x25c0,0x2fa2},
{0x25c1,0x25c1,0x1f49},
{0x25c6,0x25c6,0x2d7},
{0x25c7,0x25c7,0x2d6},
{0x25c9,0x25c9,0x2012},
{0x25cb,0x25cb,0x2d3},
{0x25cc,0x25cc,0x2906},
{0x25ce,0x25ce,0x2d5},
{0x25cf,0x25cf,0x2d4},
{0x25e2,0x25e3,0x203f},
{0x25e4,0x25e4,0x2042},
{0x25e5,0x25e5,0x2041},
{0x25e6,0x25e6,0x2fde},
{0x25ef,0x25ef,0x30b},
{0x2600,0x2603,0x2017},
{0x2605,0x2605,0x2d2},
{0x2606,0x2606,0x2d1},
{0x260e,0x260e,0x1f78},
{0x261c,0x261d,0x201c},
{0x261e,0x261e,0x201b},
{0x261f,0x261f,0x201e},
{0x2640,0x2640,0x2c2},
{0x2642,0x2642,0x2c1},
{0x2660,0x2660,0x2013},
{0x2661,0x2661,0x1f51},
{0x2662,0x2662,0x1f53},
{0x2663,0x2663,0x2015},
{0x2664,0x2664,0x1f52},
{0x2665,0x2665,0x2014},
{0x2666,0x2666,0x2016},
{0x2667,0x2667,0x1f50},
{0x2668,0x2669,0x2f42},
{0x266a,0x266a,0x307},
{0x266c,0x266c,0x2f44},
{0x266d,0x266d,0x306},
{0x266f,0x266f,0x305},
{0x2702,0x2702,0x2f90},
{0x271a,0x271a,0x2fd1},
{0x2756,0x2756,0x2fe3},
{0x2776,0x277e,0x205e},
{0x27a1,0x27a1,0x200e},
{0x2e83,0x2e83,0x37e1},
{0x2e85,0x2e85,0x3620},
{0x2e87,0x2e87,0x3719},
{0x2e89,0x2e89,0x3814},
{0x2e8b,0x2e8b,0x371e},
{0x2e8c,0x2e8d,0x3609},
{0x2e8e,0x2e8e,0x1071},
{0x2e8f,0x2e8f,0x388c},
{0x2e90,0x2e90,0x1226},
{0x2e92,0x2e92,0xeb2},
{0x2e93,0x2e93,0x1283},
{0x2e94,0x2e94,0x12ab},
{0x2e95,0x2e95,0x3c1f},
{0x2e96,0x2e96,0x38c2},
{0x2e97,0x2e97,0x361c},
{0x2e98,0x2e98,0x38e1},
{0x2e99,0x2e99,0x13c3},
{0x2e9b,0x2e9b,0x13e1},
{0x2e9f,0x2e9f,0xe3c},
{0x2ea0,0x2ea0,0xebd},
{0x2ea1,0x2ea2,0x3961},
{0x2ea3,0x2ea3,0x399d},
{0x2ea4,0x2ea4,0x3c26},
{0x2ea6,0x2ea6,0x374d},
{0x2ea8,0x2ea8,0x39bc},
{0x2ea9,0x2ea9,0x35a1},
{0x2eaa,0x2eaa,0x36ab},
{0x2eab,0x2eab,0x3a97},
{0x2ead,0x2ead,0x3a39},
{0x2eae,0x2eae,0x3662},
{0x2eb1,0x2eb1,0x3a98},
{0x2eb2,0x2eb2,0x3a97},
{0x2eb3,0x2eb3,0x376d},
{0x2eb7,0x2eb7,0x36fe},
{0x2eb9,0x2eb9,0x3713},
{0x2ebc,0x2ebc,0x35b3},
{0x2ebd,0x2ebd,0x354e},
{0x2ebe,0x2ec0,0x3775},
{0x2ec1,0x2ec1,0x78b},
{0x2ec2,0x2ec2,0x3b0a},
{0x2ec3,0x2ec3,0x362e},
{0x2ec4,0x2ec4,0xa62},
{0x2ec6,0x2ec6,0x3572},
{0x2eca,0x2eca,0x364a},
{0x2ecc,0x2ecc,0x3c2b},
{0x2ecd,0x2ecd,0x3b50},
{0x2ecf,0x2ecf,0x3b9e},
{0x2ed1,0x2ed1,0xbd5},
{0x2ed2,0x2ed2,0x3b97},
{0x2ed6,0x2ed6,0x3b9e},
{0x2ed7,0x2ed7,0x354d},
{0x2ed8,0x2ed8,0xa68},
{0x2edd,0x2edd,0x3617},
{0x2ede,0x2ede,0x3619},
{0x2edf,0x2edf,0x3618},
{0x2ee4,0x2ee4,0x64e},
{0x2ee8,0x2ee8,0xd34},
{0x2ee9,0x2ee9,0x52b},
{0x2eeb,0x2eeb,0xa6a},
{0x2eed,0x2eed,0x8c3},
{0x2eef,0x2eef,0xf7d},
{0x2ef2,0x2ef2,0x64f},
{0x2f00,0x2f00,0x4b0},
{0x2f01,0x2f01,0x20b3},
{0x2f02,0x2f02,0xfff},
{0x2f03,0x2f03,0x1001},
{0x2f04,0x2f04,0x535},
{0x2f05,0x2f05,0x1006},
{0x2f06,0x2f06,0xccb},
{0x2f07,0x2f07,0x100e},
{0x2f08,0x2f08,0xa13},
{0x2f09,0x2f09,0x1070},
{0x2f0a,0x2f0a,0xcd6},
{0x2f0b,0x2f0b,0xd40},
{0x2f0c,0x2f0c,0x107b},
{0x2f0d,0x2f0d,0x1083},
{0x2f0e,0x2f0e,0x1089},
{0x2f0f,0x2f0f,0x1093},
{0x2f10,0x2f10,0x1098},
{0x2f11,0x2f11,0xc5b},
{0x2f12,0x2f12,0xf97},
{0x2f13,0x2f13,0x10c6},
{0x2f14,0x2f15,0x10cd},
{0x2f16,0x2f16,0x10d3},
{0x2f17,0x2f17,0x947},
{0x2f18,0x2f18,0xe7c},
{0x2f19,0x2f19,0x10dc},
{0x2f1a,0x2f1a,0x10e1},
{0x2f1b,0x2f1b,0x10e8},
{0x2f1c,0x2f1c,0xea2},
{0x2f1d,0x2f1d,0x7b1},
{0x2f1e,0x2f1e,0x116b},
{0x2f1f,0x2f1f,0xc54},
{0x2f20,0x2f20,0x89c},
{0x2f21,0x2f22,0x11ba},
{0x2f23,0x2f23,0xf26},
{0x2f24,0x2f24,0xb47},
{0x2f25,0x2f25,0x981},
{0x2f26,0x2f26,0x8a0},
{0x2f27,0x2f27,0x120e},
{0x2f28,0x2f28,0xa47},
{0x2f29,0x2f29,0x996},
{0x2f2a,0x2f2a,0x1226},
{0x2f2b,0x2f2b,0x1228},
{0x2f2c,0x2f2c,0x1232},
{0x2f2d,0x2f2d,0x881},
{0x2f2e,0x2f2e,0x126c},
{0x2f2f,0x2f2f,0x7bb},
{0x2f30,0x2f30,0x77e},
{0x2f31,0x2f31,0x6ca},
{0x2f32,0x2f32,0x5ef},
{0x2f33,0x2f33,0x1283},
{0x2f34,0x2f34,0x1285},
{0x2f35,0x2f35,0x1299},
{0x2f36,0x2f36,0x129b},
{0x2f37,0x2f37,0x12a0},
{0x2f38,0x2f38,0x677},
{0x2f39,0x2f39,0x38b9},
{0x2f3a,0x2f3a,0x12af},
{0x2f3b,0x2f3b,0x12b1},
{0x2f3c,0x2f3c,0x9fa},
{0x2f3d,0x2f3d,0x1342},
{0x2f3e,0x2f3e,0x781},
{0x2f3f,0x2f3f,0x916},
{0x2f40,0x2f40,0x8a7},
{0x2f41,0x2f41,0x13c2},
{0x2f42,0x2f42,0xe08},
{0x2f43,0x2f43,0xc47},
{0x2f44,0x2f44,0x6cc},
{0x2f45,0x2f45,0xe4d},
{0x2f46,0x2f46,0x13e0},
{0x2f47,0x2f47,0xcd4},
{0x2f48,0x2f48,0x140c},
{0x2f49,0x2f49,0x744},
{0x2f4a,0x2f4a,0xee6},
{0x2f4b,0x2f4b,0x73d},
{0x2f4c,0x2f4c,0x8ad},
{0x2f4d,0x2f4d,0x14e5},
{0x2f4e,0x2f4e,0x14f4},
{0x2f4f,0x2f4f,0x14f8},
{0x2f50,0x2f50,0xd7a},
{0x2f51,0x2f51,0xedf},
{0x2f52,0x2f52,0x8af},
{0x2f53,0x2f53,0x1502},
{0x2f54,0x2f54,0xa2b},
{0x2f55,0x2f55,0x550},
{0x2f56,0x2f56,0xbfa},
{0x2f57,0x2f57,0xdd5},
{0x2f58,0x2f58,0x15e4},
{0x2f59,0x2f59,0x15e6},
{0x2f5a,0x2f5a,0xe22},
{0x2f5b,0x2f5b,0x567},
{0x2f5c,0x2f5c,0x687},
{0x2f5d,0x2f5d,0x758},
{0x2f5e,0x2f5e,0x770},
{0x2f5f,0x2f5f,0x6c4},
{0x2f60,0x2f60,0x4dd},
{0x2f61,0x2f61,0x5e0},
{0x2f62,0x2f62,0x601},
{0x2f63,0x2f63,0xa5c},
{0x2f64,0x2f64,0xf3b},
{0x2f65,0x2f65,0xc3e},
{0x2f66,0x2f66,0xd97},
{0x2f67,0x2f67,0x3a00},
{0x2f68,0x2f68,0x1697},
{0x2f69,0x2f69,0xd28},
{0x2f6a,0x2f6a,0xd7d},
{0x2f6b,0x2f6b,0x87c},
{0x2f6c,0x2f6c,0xee8},
{0x2f6d,0x2f6d,0xec3},
{0x2f6e,0x2f6e,0xefc},
{0x2f6f,0x2f6f,0xa74},
{0x2f70,0x2f70,0x8d4},
{0x2f71,0x2f71,0x3a41},
{0x2f72,0x2f72,0x553},
{0x2f73,0x2f73,0x740},
{0x2f74,0x2f74,0xf71},
{0x2f75,0x2f75,0xb9b},
{0x2f76,0x2f76,0xe16},
{0x2f77,0x2f77,0x8b3},
{0x2f78,0x2f78,0x608},
{0x2f79,0x2f79,0x1813},
{0x2f7a,0x2f7a,0xf3d},
{0x2f7b,0x2f7b,0x4cb},
{0x2f7c,0x2f7c,0xfdd},
{0x2f7d,0x2f7d,0x8d5},
{0x2f7e,0x2f7e,0x183d},
{0x2f7f,0x2f7f,0x8d6},
{0x2f80,0x2f80,0x1853},
{0x2f81,0x2f81,0xcd1},
{0x2f82,0x2f82,0xa09},
{0x2f83,0x2f83,0x8d7},
{0x2f84,0x2f84,0x8b8},
{0x2f85,0x2f85,0x4d3},
{0x2f86,0x2f86,0xa89},
{0x2f87,0x2f87,0xaa6},
{0x2f88,0x2f88,0x938},
{0x2f89,0x2f89,0x821},
{0x2f8a,0x2f8a,0x9ed},
{0x2f8b,0x2f8b,0x18b2},
{0x2f8c,0x2f8c,0x194f},
{0x2f8d,0x2f8d,0xbac},
{0x2f8e,0x2f8e,0x742},
{0x2f8f,0x2f8f,0x7e6},
{0x2f90,0x2f90,0x4a5},
{0x2f91,0x2f91,0x19eb},
{0x2f92,0x2f92,0x75f},
{0x2f93,0x2f93,0x5af},
{0x2f94,0x2f94,0x774},
{0x2f95,0x2f95,0xb69},
{0x2f96,0x2f96,0xc7e},
{0x2f97,0x2f97,0x1a56},
{0x2f98,0x2f98,0x1a59},
{0x2f99,0x2f99,0x58b},
{0x2f9a,0x2f9a,0xa7a},
{0x2f9b,0x2f9b,0xaf8},
{0x2f9c,0x2f9c,0xb0d},
{0x2f9d,0x2f9d,0xa0e},
{0x2f9e,0x2f9e,0x902},
{0x2f9f,0x2f9f,0xa0f},
{0x2fa0,0x2fa0,0xb62},
{0x2fa1,0x2fa1,0x3b4f},
{0x2fa2,0x2fa2,0xf22},
{0x2fa3,0x2fa3,0xcab},
{0x2fa4,0x2fa4,0xd64},
{0x2fa5,0x2fa5,0xf6c},
{0x2fa6,0x2fa6,0x6da},
{0x2fa7,0x2fa7,0xbd5},
{0x2fa8,0x2fa8,0xef3},
{0x2fa9,0x2fa9,0xdde},
{0x2faa,0x2faa,0x1bc9},
{0x2fab,0x2fab,0x1bcb},
{0x2fac,0x2fac,0x4cd},
{0x2fad,0x2fad,0x21f7},
{0x2fae,0x2fae,0xd87},
{0x2faf,0x2faf,0xed8},
{0x2fb0,0x2fb0,0x5b5},
{0x2fb1,0x2fb1,0x1c03},
{0x2fb2,0x2fb2,0x1c05},
{0x2fb3,0x2fb3,0x53b},
{0x2fb4,0x2fb4,0xe17},
{0x2fb5,0x2fb5,0xde9},
{0x2fb6,0x2fb6,0xd88},
{0x2fb7,0x2fb7,0x9ef},
{0x2fb8,0x2fb8,0x91f},
{0x2fb9,0x2fb9,0x7f3},
{0x2fba,0x2fba,0xd05},
{0x2fbb,0x2fbb,0x80e},
{0x2fbc,0x2fbc,0x7f4},
{0x2fbd,0x2fbd,0x1c6c},
{0x2fbe,0x2fbe,0x1c7d},
{0x2fbf,0x2fc0,0x1c83},
{0x2fc1,0x2fc1,0x64e},
{0x2fc2,0x2fc2,0x695},
{0x2fc3,0x2fc3,0xbd7},
{0x2fc4,0x2fc4,0x1cf6},
{0x2fc5,0x2fc5,0x8db},
{0x2fc6,0x2fc6,0x1d01},
{0x2fc7,0x2fc7,0xe91},
{0x2fc8,0x2fc8,0x340b},
{0x2fc9,0x2fc9,0x66a},
{0x2fca,0x2fca,0x807},
{0x2fcb,0x2fcb,0x1d16},
{0x2fcc,0x2fcc,0x1d19},
{0x2fcd,0x2fcd,0xc1e},
{0x2fce,0x2fce,0x791},
{0x2fcf,0x2fcf,0xacf},
{0x2fd0,0x2fd0,0xd93},
{0x2fd1,0x2fd2,0x1d21},
{0x2fd3,0x2fd3,0xf7e},
{0x2fd4,0x2fd5,0x1d30},
{0x3000,0x3002,0x279},
{0x3003,0x3003,0x28f},
{0x3004,0x3004,0x2074},
{0x3005,0x3007,0x291},
{0x3008,0x3011,0x2aa},
{0x3012,0x3012,0x2df},
{0x3013,0x3013,0x2e4},
{0x3014,0x3015,0x2a4},
{0x301c,0x301c,0x299},
{0x301d,0x301d,0x1db8},
{0x301f,0x301f,0x1db9},
{0x3020,0x3020,0x1f7a},
{0x3030,0x3030,0x2fba},
{0x3033,0x3035,0x2f4c},
{0x3036,0x3036,0x1f79},
{0x3041,0x3093,0x34a},
{0x3094,0x3094,0x1f16},
{0x309b,0x309c,0x283},
{0x309d,0x309e,0x28d},
{0x30a1,0x30f6,0x39d},
{0x30f7,0x30fa,0x2079},
{0x30fb,0x30fb,0x27e},
{0x30fc,0x30fc,0x294},
{0x30fd,0x30fe,0x28b},
{0x3220,0x3229,0x278e},
{0x322a,0x322f,0x2006},
{0x3230,0x3230,0x2005},
{0x3231,0x3232,0x1dc2},
{0x3233,0x3233,0x1fcf},
{0x3234,0x3234,0x1fcd},
{0x3235,0x3235,0x1fd4},
{0x3236,0x3236,0x1fd3},
{0x3237,0x3237,0x200c},
{0x3238,0x3238,0x1fce},
{0x3239,0x3239,0x1dc4},
{0x323a,0x323a,0x1fd7},
{0x323b,0x323b,0x1fd5},
{0x323c,0x323c,0x1fd0},
{0x323d,0x323d,0x1fcb},
{0x323e,0x323e,0x1fd2},
{0x323f,0x323f,0x1fcc},
{0x3240,0x3240,0x1fd6},
{0x3241,0x3241,0x200d},
{0x3242,0x3242,0x1fd1},
{0x3243,0x3243,0x1fca},
{0x3280,0x3289,0x28dd},
{0x328a,0x328f,0x28e8},
{0x3290,0x3290,0x28e7},
{0x3291,0x3291,0x1fe1},
{0x3292,0x3292,0x1fe0},
{0x3293,0x3293,0x1fe2},
{0x3294,0x3294,0x1fdc},
{0x3295,0x3295,0x28ff},
{0x3296,0x3296,0x1fe5},
{0x3297,0x3297,0x28fc},
{0x3298,0x3298,0x1fde},
{0x3299,0x3299,0x201f},
{0x329a,0x329a,0x28f9},
{0x329b,0x329b,0x28f8},
{0x329c,0x329c,0x28fe},
{0x329d,0x329d,0x207f},
{0x329e,0x329e,0x1fff},
{0x329f,0x329f,0x28ef},
{0x32a0,0x32a1,0x28f6},
{0x32a2,0x32a2,0x28fb},
{0x32a3,0x32a3,0x28fa},
{0x32a4,0x32a8,0x1dbd},
{0x32a9,0x32a9,0x1fda},
{0x32aa,0x32aa,0x1fdd},
{0x32ab,0x32ab,0x1fdf},
{0x32ac,0x32ac,0x1fe3},
{0x32ad,0x32ad,0x1fd9},
{0x32ae,0x32ae,0x1fe4},
{0x32af,0x32af,0x1fdb},
{0x32b0,0x32b0,0x1fd8},
{0x32d0,0x32fe,0x28ad},
{0x3300,0x3300,0x1f70},
{0x3301,0x3302,0x2e62},
{0x3303,0x3303,0x1f6a},
{0x3304,0x3304,0x2e64},
{0x3305,0x3305,0x1ff7},
{0x3306,0x3306,0x2e65},
{0x3307,0x3307,0x2e69},
{0x3308,0x3308,0x2e67},
{0x3309,0x3309,0x2e6c},
{0x330a,0x330a,0x2e6a},
{0x330b,0x330b,0x2e6e},
{0x330c,0x330c,0x2e70},
{0x330d,0x330d,0x1dab},
{0x330e,0x3313,0x2e71},
{0x3314,0x3314,0x1da2},
{0x3315,0x3315,0x1f69},
{0x3316,0x3316,0x1f67},
{0x3317,0x3317,0x2e78},
{0x3318,0x3318,0x1f68},
{0x3319,0x3319,0x2e7a},
{0x331a,0x331d,0x2e7c},
{0x331e,0x331e,0x1f73},
{0x331f,0x3321,0x2e80},
{0x3322,0x3322,0x1f66},
{0x3323,0x3323,0x1f6b},
{0x3324,0x3324,0x2e83},
{0x3325,0x3325,0x2e85},
{0x3326,0x3326,0x1dac},
{0x3327,0x3327,0x1da6},
{0x3328,0x3329,0x2e88},
{0x332a,0x332a,0x1f74},
{0x332b,0x332b,0x1dae},
{0x332d,0x332d,0x2e8b},
{0x332e,0x3330,0x2e8e},
{0x3331,0x3331,0x1f71},
{0x3332,0x3332,0x2e91},
{0x3333,0x3333,0x2087},
{0x3334,0x3335,0x2e94},
{0x3336,0x3336,0x1da8},
{0x3337,0x3337,0x2e9a},
{0x3338,0x3338,0x2e9c},
{0x3339,0x3339,0x1f6e},
{0x333a,0x333a,0x2e9d},
{0x333b,0x333b,0x1f6f},
{0x333c,0x333c,0x2e96},
{0x333d,0x333d,0x2e9e},
{0x333e,0x3340,0x2ea0},
{0x3341,0x3341,0x2e9f},
{0x3342,0x3342,0x1f6d},
{0x3343,0x3346,0x2ea3},
{0x3347,0x3347,0x1f72},
{0x3348,0x3348,0x2ea7},
{0x3349,0x3349,0x1da1},
{0x334a,0x334a,0x1daf},
{0x334b,0x334c,0x2ea8},
{0x334d,0x334d,0x1da4},
{0x334e,0x334e,0x2088},
{0x334f,0x3350,0x2eaa},
{0x3351,0x3351,0x1da9},
{0x3352,0x3352,0x2eae},
{0x3353,0x3353,0x2eb2},
{0x3354,0x3354,0x2eaf},
{0x3355,0x3356,0x2eb3},
{0x3357,0x3357,0x1f6c},
{0x3371,0x3371,0x2e55},
{0x337b,0x337b,0x2083},
{0x337c,0x337c,0x1dc7},
{0x337d,0x337d,0x1dc6},
{0x337e,0x337e,0x1dc5},
{0x337f,0x337f,0x1f76},
{0x3385,0x3387,0x1f5f},
{0x3388,0x3389,0x2000},
{0x338d,0x338d,0x2e58},
{0x338e,0x338f,0x1db4},
{0x3390,0x3390,0x1f63},
{0x3396,0x3396,0x1f65},
{0x3397,0x3397,0x1f58},
{0x3398,0x3398,0x1f5a},
{0x339b,0x339b,0x2e59},
{0x339c,0x339e,0x1db1},
{0x339f,0x339f,0x1ffa},
{0x33a0,0x33a0,0x1f54},
{0x33a1,0x33a1,0x1db7},
{0x33a2,0x33a2,0x1f55},
{0x33a3,0x33a3,0x1ffb},
{0x33a4,0x33a5,0x1f56},
{0x33a6,0x33a6,0x1ffc},
{0x33b0,0x33b0,0x1f5e},
{0x33b1,0x33b1,0x1f5d},
{0x33b2,0x33b2,0x1f5c},
{0x33b3,0x33b3,0x1f5b},
{0x33c2,0x33c2,0x2e50},
{0x33c4,0x33c4,0x1db6},
{0x33c8,0x33c8,0x2002},
{0x33cb,0x33cb,0x1f62},
{0x33cc,0x33cc,0x1ff6},
{0x33cd,0x33cd,0x1dbb},
{0x33d4,0x33d4,0x1f64},
{0x33d7,0x33d8,0x2e5d},
{0x33da,0x33da,0x2e4b},
{0x3402,0x3402,0x3582},
{0x3405,0x3405,0x3c1b},
{0x3427,0x3427,0x3656},
{0x3488,0x3488,0x3c52},
{0x34db,0x34db,0x3c41},
{0x351f,0x351f,0x3629},
{0x353e,0x353e,0x371e},
{0x378d,0x378d,0x361a},
{0x37e2,0x37e2,0x372b},
{0x3af3,0x3af3,0x3c40},
{0x3b22,0x3b22,0x3c49},
{0x3b88,0x3b88,0x368d},
{0x3e8a,0x3e8a,0x3c43},
{0x3eda,0x3eda,0x3c48},
{0x3fb1,0x3fb1,0x3754},
{0x4093,0x4093,0x3c4c},
{0x4103,0x4103,0x3c4f},
{0x4264,0x4264,0x3760},
{0x4293,0x4293,0x3c50},
{0x440c,0x440c,0x3c42},
{0x4453,0x4453,0x3773},
{0x457a,0x457a,0x3c4b},
{0x4665,0x4665,0x3c4e},
{0x46ae,0x46ae,0x3c51},
{0x4be8,0x4be8,0x3c46},
{0x4e00,0x4e00,0x4b0},
{0x4e01,0x4e01,0xbb8},
{0x4e03,0x4e03,0x8e3},
{0x4e04,0x4e05,0x37d8},
{0x4e07,0x4e07,0xeaa},
{0x4e08,0x4e08,0x9ce},
{0x4e09,0x4e09,0x87e},
{0x4e0a,0x4e0a,0x9cd},
{0x4e0b,0x4e0b,0x53c},
{0x4e0d,0x4e0d,0xdc6},
{0x4e0e,0x4e0e,0xf29},
{0x4e10,0x4e10,0xffb},
{0x4e11,0x4e11,0x4d1},
{0x4e14,0x4e14,0x5cc},
{0x4e15,0x4e15,0xffc},
{0x4e16,0x4e16,0xa48},
{0x4e17,0x4e17,0x10d7},
{0x4e18,0x4e18,0x670},
{0x4e19,0x4e19,0xe0a},
{0x4e1e,0x4e1e,0x9cf},
{0x4e1f,0x4e1f,0x37da},
{0x4e21,0x4e21,0xf86},
{0x4e26,0x4e26,0xe12},
{0x4e28,0x4e28,0x20b3},
{0x4e2a,0x4e2a,0xffd},
{0x4e2b,0x4e2b,0x37db},
{0x4e2c,0x4e2c,0x374d},
{0x4e2d,0x4e2d,0xba4},
{0x4e2f,0x4e30,0x37dc},
{0x4e31,0x4e31,0xffe},
{0x4e32,0x4e32,0x6f2},
{0x4e36,0x4e36,0xfff},
{0x4e37,0x4e37,0x369d},
{0x4e38,0x4e38,0x619},
{0x4e39,0x4e39,0xb6e},
{0x4e3b,0x4e3b,0x913},
{0x4e3c,0x4e3c,0x1000},
{0x4e3f,0x4e3f,0x1001},
{0x4e40,0x4e41,0x37de},
{0x4e42,0x4e42,0x1002},
{0x4e43,0x4e43,0xceb},
{0x4e44,0x4e44,0x37e0},
{0x4e45,0x4e45,0x671},
{0x4e48,0x4e48,0x372e},
{0x4e4b,0x4e4b,0xced},
{0x4e4d,0x4e4d,0xcbb},
{0x4e4e,0x4e4e,0x777},
{0x4e4f,0x4e4f,0xe61},
{0x4e55,0x4e55,0x1950},
{0x4e56,0x4e56,0x1003},
{0x4e57,0x4e57,0x9d0},
{0x4e58,0x4e58,0x1004},
{0x4e59,0x4e59,0x535},
{0x4e5a,0x4e5a,0x37e1},
{0x4e5d,0x4e5d,0x6dd},
{0x4e5e,0x4e5e,0x7a4},
{0x4e5f,0x4e5f,0xef5},
{0x4e62,0x4e62,0x1233},
{0x4e71,0x4e71,0xf5a},
{0x4e73,0x4e73,0xcd5},
{0x4e7e,0x4e7e,0x5e1},
{0x4e7f,0x4e7f,0x37e2},
{0x4e80,0x4e80,0x64f},
{0x4e82,0x4e82,0x1005},
{0x4e85,0x4e85,0x1006},
{0x4e86,0x4e86,0xf83},
{0x4e88,0x4e88,0xf27},
{0x4e89,0x4e89,0xaea},
{0x4e8a,0x4e8a,0x1008},
{0x4e8b,0x4e8b,0x8c4},
{0x4e8c,0x4e8c,0xccb},
{0x4e8d,0x4e8d,0x37e3},
{0x4e8e,0x4e8e,0x100b},
{0x4e91,0x4e91,0x4e0},
{0x4e92,0x4e92,0x793},
{0x4e94,0x4e94,0x792},
{0x4e95,0x4e95,0x4aa},
{0x4e96,0x4e96,0x37e4},
{0x4e98,0x4e98,0xff1},
{0x4e99,0x4e99,0xff0},
{0x4e9b,0x4e9b,0x823},
{0x4e9c,0x4e9c,0x465},
{0x4e9e,0x4ea0,0x100c},
{0x4ea1,0x4ea1,0xe62},
{0x4ea2,0x4ea2,0x100f},
{0x4ea4,0x4ea4,0x7a6},
{0x4ea5,0x4ea5,0x4ab},
{0x4ea6,0x4ea6,0xea0},
{0x4ea8,0x4ea8,0x696},
{0x4eab,0x4eac,0x697},
{0x4ead,0x4ead,0xbfe},
{0x4eae,0x4eae,0xf84},
{0x4eb0,0x4eb0,0x1010},
{0x4eb3,0x4eb3,0x1011},
{0x4eb6,0x4eb6,0x1012},
{0x4eb9,0x4eb9,0x37e5},
{0x4eba,0x4eba,0xa13},
{0x4ebb,0x4ebb,0x3620},
{0x4ec0,0x4ec0,0x944},
{0x4ec1,0x4ec1,0xa14},
{0x4ec2,0x4ec2,0x1017},
{0x4ec4,0x4ec4,0x1015},
{0x4ec6,0x4ec6,0x1016},
{0x4ec7,0x4ec7,0x672},
{0x4eca,0x4eca,0x813},
{0x4ecb,0x4ecb,0x570},
{0x4ecd,0x4ecd,0x1014},
{0x4ece,0x4ece,0x1013},
{0x4ecf,0x4ecf,0xdf9},
{0x4ed0,0x4ed0,0x37e6},
{0x4ed4,0x4ed4,0x894},
{0x4ed5,0x4ed5,0x893},
{0x4ed6,0x4ed6,0xb1e},
{0x4ed7,0x4ed7,0x1018},
{0x4ed8,0x4ed8,0xdc7},
{0x4ed9,0x4ed9,0xa8b},
{0x4edd,0x4edd,0x290},
{0x4ede,0x4ede,0x1019},
{0x4edf,0x4edf,0x101b},
{0x4ee0,0x4ee0,0x37e7},
{0x4ee1,0x4ee1,0x20b4},
{0x4ee3,0x4ee3,0xb45},
{0x4ee4,0x4ee4,0xfa9},
{0x4ee5,0x4ee5,0x48e},
{0x4eed,0x4eed,0x101a},
{0x4eee,0x4eee,0x53e},
{0x4ef0,0x4ef0,0x6bc},
{0x4ef2,0x4ef2,0xba5},
{0x4ef6,0x4ef6,0x745},
{0x4ef7,0x4ef7,0x101c},
{0x4efb,0x4efb,0xcda},
{0x4efc,0x4efc,0x20b5},
{0x4efd,0x4efd,0x37e8},
{0x4eff,0x4eff,0x37e9},
{0x4f00,0x4f00,0x20b6},
{0x4f01,0x4f01,0x627},
{0x4f03,0x4f03,0x20b7},
{0x4f09,0x4f09,0x101d},
{0x4f0a,0x4f0a,0x48f},
{0x4f0b,0x4f0b,0x37ea},
{0x4f0d,0x4f0d,0x794},
{0x4f0e,0x4f0e,0x628},
{0x4f0f,0x4f0f,0xdec},
{0x4f10,0x4f10,0xd46},
{0x4f11,0x4f11,0x673},
{0x4f15,0x4f15,0x37eb},
{0x4f1a,0x4f1a,0x571},
{0x4f1c,0x4f1c,0x1040},
{0x4f1d,0x4f1d,0xc3b},
{0x4f2f,0x4f2f,0xd22},
{0x4f30,0x4f30,0x101f},
{0x4f34,0x4f34,0xd50},
{0x4f36,0x4f36,0xfaa},
{0x4f38,0x4f38,0x9f3},
{0x4f39,0x4f39,0x20b8},
{0x4f3a,0x4f3a,0x895},
{0x4f3b,0x4f3b,0x37ed},
{0x4f3c,0x4f3c,0x8c5},
{0x4f3d,0x4f3d,0x540},
{0x4f43,0x4f43,0xbed},
{0x4f46,0x4f46,0xb60},
{0x4f47,0x4f47,0x1023},
{0x4f49,0x4f49,0x37ee},
{0x4f4d,0x4f4d,0x490},
{0x4f4e,0x4f4e,0xbff},
{0x4f4f,0x4f4f,0x945},
{0x4f50,0x4f50,0x824},
{0x4f51,0x4f51,0xf0e},
{0x4f53,0x4f53,0xb2e},
{0x4f54,0x4f54,0x37ef},
{0x4f55,0x4f55,0x53f},
{0x4f56,0x4f56,0x20b9},
{0x4f57,0x4f57,0x1022},
{0x4f59,0x4f59,0xf28},
{0x4f5a,0x4f5a,0x101e},
{0x4f5b,0x4f5b,0x1020},
{0x4f5c,0x4f5c,0x85e},
{0x4f5d,0x4f5d,0x1021},
{0x4f5e,0x4f5e,0x11d3},
{0x4f60,0x4f60,0x37ec},
{0x4f69,0x4f69,0x1029},
{0x4f6f,0x4f6f,0x102c},
{0x4f70,0x4f70,0x102a},
{0x4f73,0x4f73,0x542},
{0x4f75,0x4f75,0xe0b},
{0x4f76,0x4f76,0x1024},
{0x4f7a,0x4f7a,0x37f0},
{0x4f7b,0x4f7b,0x1028},
{0x4f7c,0x4f7c,0x7a7},
{0x4f7d,0x4f7e,0x37f1},
{0x4f7f,0x4f7f,0x896},
{0x4f83,0x4f83,0x5e2},
{0x4f86,0x4f86,0x102d},
{0x4f88,0x4f88,0x1025},
{0x4f8a,0x4f8a,0x20bb},
{0x4f8b,0x4f8b,0xfab},
{0x4f8d,0x4f8d,0x8c6},
{0x4f8f,0x4f8f,0x1026},
{0x4f91,0x4f91,0x102b},
{0x4f92,0x4f92,0x20ba},
{0x4f94,0x4f94,0x20bd},
{0x4f96,0x4f96,0x102e},
{0x4f97,0x4f97,0x37f3},
{0x4f98,0x4f98,0x1027},
{0x4f9a,0x4f9a,0x20bc},
{0x4f9b,0x4f9b,0x699},
{0x4f9d,0x4f9d,0x491},
{0x4fa0,0x4fa0,0x69a},
{0x4fa1,0x4fa1,0x541},
{0x4fab,0x4fab,0x11d4},
{0x4fad,0x4fad,0xea7},
{0x4fae,0x4fae,0xde0},
{0x4faf,0x4faf,0x7a8},
{0x4fb5,0x4fb5,0x9f5},
{0x4fb6,0x4fb6,0xf7f},
{0x4fbe,0x4fbe,0x37f4},
{0x4fbf,0x4fbf,0xe28},
{0x4fc2,0x4fc2,0x70e},
{0x4fc3,0x4fc3,0xb05},
{0x4fc4,0x4fc4,0x564},
{0x4fc9,0x4fc9,0x20ac},
{0x4fca,0x4fca,0x95d},
{0x4fcd,0x4fcd,0x20be},
{0x4fce,0x4fce,0x1032},
{0x4fcf,0x4fcf,0x37f5},
{0x4fd0,0x4fd0,0x1037},
{0x4fd1,0x4fd1,0x1035},
{0x4fd3,0x4fd3,0x3c2f},
{0x4fd4,0x4fd4,0x1030},
{0x4fd7,0x4fd7,0xb0f},
{0x4fd8,0x4fd8,0x1033},
{0x4fda,0x4fda,0x1036},
{0x4fdb,0x4fdb,0x1034},
{0x4fdd,0x4fdd,0xe2d},
{0x4fdf,0x4fdf,0x1031},
{0x4fe0,0x4fe0,0x1dec},
{0x4fe1,0x4fe1,0x9f4},
{0x4fe3,0x4fe3,0xea1},
{0x4fe4,0x4fe5,0x1038},
{0x4fee,0x4fee,0x92e},
{0x4fef,0x4fef,0x1046},
{0x4ff3,0x4ff3,0xd06},
{0x4ff5,0x4ff5,0xda8},
{0x4ff6,0x4ff6,0x1041},
{0x4ff8,0x4ff8,0xe40},
{0x4ffa,0x4ffa,0x536},
{0x4ffd,0x4ffd,0x37f6},
{0x4ffe,0x4ffe,0x1045},
{0x4fff,0x4fff,0x20c1},
{0x5000,0x5001,0x37f7},
{0x5005,0x5005,0x103f},
{0x5006,0x5006,0x1048},
{0x5009,0x5009,0xad4},
{0x500b,0x500b,0x778},
{0x500d,0x500d,0xd12},
{0x500f,0x500f,0x1600},
{0x5010,0x5010,0x37f9},
{0x5011,0x5011,0x1047},
{0x5012,0x5012,0xc57},
{0x5014,0x5014,0x103c},
{0x5016,0x5016,0x7aa},
{0x5019,0x5019,0x7a9},
{0x501a,0x501a,0x103a},
{0x501b,0x501b,0x37fa},
{0x501e,0x501e,0x20c2},
{0x501f,0x501f,0x906},
{0x5021,0x5021,0x1042},
{0x5022,0x5022,0x20c0},
{0x5023,0x5023,0xe3f},
{0x5024,0x5024,0xb8b},
{0x5025,0x5025,0x103e},
{0x5026,0x5026,0x747},
{0x5027,0x5027,0x37fb},
{0x5028,0x5028,0x103b},
{0x5029,0x5029,0x1043},
{0x502a,0x502a,0x103d},
{0x502b,0x502b,0xf99},
{0x502c,0x502c,0x1044},
{0x502d,0x502d,0xfe7},
{0x502e,0x502e,0x37fc},
{0x5036,0x5036,0x6de},
{0x5039,0x5039,0x746},
{0x503b,0x503b,0x3800},
{0x5040,0x5040,0x20bf},
{0x5042,0x5042,0x20c5},
{0x5043,0x5043,0x1049},
{0x5046,0x5046,0x20c3},
{0x5047,0x5047,0x104a},
{0x5048,0x5048,0x104e},
{0x5049,0x5049,0x492},
{0x504f,0x504f,0xe20},
{0x5050,0x5050,0x104d},
{0x5055,0x5055,0x104c},
{0x5056,0x5056,0x1050},
{0x5057,0x5057,0x37fd},
{0x505a,0x505a,0x104f},
{0x505c,0x505c,0xc00},
{0x5065,0x5065,0x748},
{0x5066,0x5066,0x37fe},
{0x506a,0x506a,0x37ff},
{0x506c,0x506c,0x1051},
{0x5070,0x5070,0x20c4},
{0x5072,0x5072,0x8f1},
{0x5074,0x5074,0xb06},
{0x5075,0x5075,0xc01},
{0x5076,0x5076,0x6ee},
{0x5078,0x5078,0x1052},
{0x507d,0x507d,0x650},
{0x5080,0x5080,0x1053},
{0x5085,0x5085,0x1055},
{0x508d,0x508d,0xe63},
{0x508f,0x508f,0x3801},
{0x5091,0x5091,0x73c},
{0x5094,0x5094,0x20c6},
{0x5096,0x5096,0x3802},
{0x5098,0x5098,0x87f},
{0x5099,0x5099,0xd8b},
{0x509a,0x509a,0x1054},
{0x509c,0x509c,0x3803},
{0x50ac,0x50ac,0x835},
{0x50ad,0x50ad,0xf2d},
{0x50b2,0x50b2,0x1057},
{0x50b3,0x50b3,0x105a},
{0x50b4,0x50b4,0x1056},
{0x50b5,0x50b5,0x834},
{0x50b7,0x50b7,0x987},
{0x50be,0x50be,0x70f},
{0x50c2,0x50c2,0x105b},
{0x50c5,0x50c5,0x6c7},
{0x50c9,0x50ca,0x1058},
{0x50cc,0x50cc,0x3804},
{0x50cd,0x50cd,0xc87},
{0x50cf,0x50cf,0xafe},
{0x50d1,0x50d1,0x69b},
{0x50d5,0x50d5,0xe7b},
{0x50d6,0x50d6,0x105c},
{0x50d8,0x50d8,0x20c8},
{0x50d9,0x50d9,0x3c30},
{0x50da,0x50da,0xf85},
{0x50de,0x50de,0x105d},
{0x50e3,0x50e3,0x1060},
{0x50e5,0x50e5,0x105e},
{0x50e6,0x50e6,0x3805},
{0x50e7,0x50e7,0xad0},
{0x50e9,0x50e9,0x3806},
{0x50ed,0x50ed,0x105f},
{0x50ee,0x50ee,0x1061},
{0x50ef,0x50ef,0x3807},
{0x50f0,0x50f0,0x3c31},
{0x50f4,0x50f4,0x20c7},
{0x50f5,0x50f5,0x1063},
{0x50f9,0x50f9,0x1062},
{0x50fb,0x50fb,0xe18},
{0x5100,0x5100,0x651},
{0x5101,0x5102,0x1065},
{0x5104,0x5104,0x52f},
{0x5108,0x5108,0x3808},
{0x5109,0x5109,0x1064},
{0x510b,0x510b,0x3809},
{0x5110,0x5110,0x380a},
{0x5112,0x5112,0x920},
{0x5114,0x5114,0x1069},
{0x5115,0x5115,0x1068},
{0x5116,0x5116,0x1067},
{0x5118,0x5118,0x102f},
{0x511a,0x511a,0x106a},
{0x511b,0x511b,0x380b},
{0x511e,0x511e,0x380c},
{0x511f,0x511f,0x988},
{0x5121,0x5121,0x106b},
{0x512a,0x512a,0xf0f},
{0x5132,0x5132,0xee5},
{0x5137,0x5137,0x106d},
{0x513a,0x513a,0x106c},
{0x513b,0x513b,0x106f},
{0x513c,0x513c,0x106e},
{0x513f,0x5140,0x1070},
{0x5141,0x5141,0x4b8},
{0x5143,0x5143,0x769},
{0x5144,0x5144,0x711},
{0x5145,0x5145,0x946},
{0x5146,0x5146,0xbb9},
{0x5147,0x5147,0x69c},
{0x5148,0x5148,0xa8c},
{0x5149,0x5149,0x7ab},
{0x514a,0x514a,0x20c9},
{0x514b,0x514b,0x800},
{0x514c,0x514c,0x1073},
{0x514d,0x514d,0xed4},
{0x514e,0x514e,0xc40},
{0x5150,0x5150,0x8c7},
{0x5152,0x5152,0x1072},
{0x5154,0x5154,0x1074},
{0x515a,0x515a,0xc58},
{0x515c,0x515c,0x5d3},
{0x515f,0x515f,0x380d},
{0x5162,0x5162,0x1075},
{0x5164,0x5164,0x20ca},
{0x5165,0x5165,0xcd6},
{0x5167,0x5167,0x368e},
{0x5168,0x5168,0xab6},
{0x5169,0x516a,0x1077},
{0x516b,0x516b,0xd40},
{0x516c,0x516c,0x7ac},
{0x516d,0x516d,0xfe1},
{0x516e,0x516e,0x1079},
{0x5171,0x5171,0x69e},
{0x5175,0x5175,0xe0c},
{0x5176,0x5176,0xb16},
{0x5177,0x5177,0x6e9},
{0x5178,0x5178,0xc2f},
{0x5179,0x5179,0x3779},
{0x517c,0x517c,0x749},
{0x5180,0x5180,0x107a},
{0x5182,0x5182,0x107b},
{0x5185,0x5185,0xcba},
{0x5186,0x5186,0x501},
{0x5189,0x5189,0x107e},
{0x518a,0x518a,0x86d},
{0x518c,0x518c,0x107d},
{0x518d,0x518d,0x836},
{0x518f,0x518f,0x107f},
{0x5190,0x5190,0x185b},
{0x5191,0x5191,0x1080},
{0x5192,0x5192,0xe6f},
{0x5193,0x5193,0x1081},
{0x5195,0x5196,0x1082},
{0x5197,0x5197,0x9d1},
{0x5199,0x5199,0x8f8},
{0x519d,0x519d,0x20cb},
{0x51a0,0x51a0,0x5e3},
{0x51a1,0x51a1,0x380e},
{0x51a2,0x51a2,0x1086},
{0x51a4,0x51a4,0x1084},
{0x51a5,0x51a5,0xec9},
{0x51a6,0x51a6,0x1085},
{0x51a8,0x51a8,0xdcc},
{0x51a9,0x51ab,0x1087},
{0x51ac,0x51ac,0xc59},
{0x51b0,0x51b0,0x108d},
{0x51b1,0x51b2,0x108b},
{0x51b3,0x51b3,0x108a},
{0x51b4,0x51b4,0x853},
{0x51b5,0x51b5,0x108e},
{0x51b6,0x51b6,0xef6},
{0x51b7,0x51b7,0xfac},
{0x51bc,0x51bc,0x380f},
{0x51bd,0x51bd,0x108f},
{0x51be,0x51be,0x20cc},
{0x51c3,0x51c3,0x3c32},
{0x51c4,0x51c4,0xa4c},
{0x51c5,0x51c5,0x1090},
{0x51c6,0x51c6,0x964},
{0x51c9,0x51c9,0x1091},
{0x51cb,0x51cb,0xbba},
{0x51cc,0x51cc,0xf87},
{0x51cd,0x51cd,0xc5a},
{0x51d6,0x51d6,0x10da},
{0x51db,0x51db,0x1092},
{0x51dc,0x51dc,0x205c},
{0x51dd,0x51dd,0x6bd},
{0x51e0,0x51e0,0x1093},
{0x51e1,0x51e1,0xe8c},
{0x51e6,0x51e6,0x972},
{0x51e7,0x51e7,0xb5c},
{0x51e9,0x51e9,0x1095},
{0x51ea,0x51ea,0xcbc},
{0x51ec,0x51ec,0x20cd},
{0x51ed,0x51ed,0x1096},
{0x51ee,0x51ee,0x3811},
{0x51f0,0x51f0,0x1097},
{0x51f1,0x51f1,0x58c},
{0x51f4,0x51f4,0x3812},
{0x51f5,0x51f5,0x1098},
{0x51f6,0x51f6,0x69f},
{0x51f8,0x51f8,0xca4},
{0x51f9,0x51f9,0x51c},
{0x51fa,0x51fa,0x95a},
{0x51fd,0x51fd,0xd35},
{0x51fe,0x51fe,0x1099},
{0x5200,0x5200,0xc5b},
{0x5201,0x5202,0x3813},
{0x5203,0x5203,0xa15},
{0x5204,0x5204,0x109a},
{0x5206,0x5206,0xdfc},
{0x5207,0x5207,0xa7e},
{0x5208,0x5208,0x5de},
{0x520a,0x520a,0x5e5},
{0x520b,0x520b,0x109b},
{0x520e,0x520e,0x109d},
{0x5211,0x5211,0x710},
{0x5213,0x5213,0x3815},
{0x5214,0x5214,0x109c},
{0x5215,0x5215,0x20ce},
{0x5217,0x5217,0xfbb},
{0x521d,0x521d,0x973},
{0x5224,0x5224,0xd51},
{0x5225,0x5225,0xe1c},
{0x5227,0x5227,0x109e},
{0x5229,0x5229,0xf62},
{0x522a,0x522a,0x109f},
{0x522e,0x522e,0x10a0},
{0x5230,0x5230,0xc78},
{0x5233,0x5233,0x10a1},
{0x5236,0x5236,0xa4d},
{0x5237,0x5237,0x86e},
{0x5238,0x5238,0x74a},
{0x5239,0x5239,0x10a2},
{0x523a,0x523a,0x897},
{0x523b,0x523b,0x801},
{0x5243,0x5243,0xc02},
{0x5244,0x5244,0x10a4},
{0x5247,0x5247,0xb07},
{0x5249,0x5249,0x3816},
{0x524a,0x524a,0x85f},
{0x524b,0x524c,0x10a5},
{0x524d,0x524d,0xab2},
{0x524f,0x524f,0x10a3},
{0x5254,0x5254,0x10a8},
{0x5256,0x5256,0xe64},
{0x525b,0x525b,0x7f6},
{0x525d,0x525d,0x1e5e},
{0x525e,0x525e,0x10a7},
{0x5261,0x5261,0x3817},
{0x5263,0x5263,0x74b},
{0x5264,0x5264,0x84e},
{0x5265,0x5265,0xd23},
{0x5266,0x5266,0x3818},
{0x5269,0x5269,0x10ab},
{0x526a,0x526a,0x10a9},
{0x526f,0x526f,0xded},
{0x5270,0x5270,0x9d2},
{0x5271,0x5271,0x10b2},
{0x5272,0x5272,0x5c2},
{0x5273,0x5273,0x10ac},
{0x5274,0x5274,0x10aa},
{0x5275,0x5275,0xad1},
{0x527d,0x527d,0x10ae},
{0x527f,0x527f,0x10ad},
{0x5283,0x5283,0x5a2},
{0x5287,0x5287,0x736},
{0x5288,0x5288,0x10b3},
{0x5289,0x5289,0xf75},
{0x528d,0x528d,0x10af},
{0x5291,0x5291,0x10b4},
{0x5292,0x5292,0x10b1},
{0x5293,0x5293,0x3819},
{0x5294,0x5294,0x10b0},
{0x529b,0x529b,0xf97},
{0x529c,0x529c,0x20cf},
{0x529f,0x529f,0x7ad},
{0x52a0,0x52a0,0x543},
{0x52a3,0x52a3,0xfbc},
{0x52a6,0x52a6,0x20d0},
{0x52a9,0x52a9,0x97f},
{0x52aa,0x52aa,0xc52},
{0x52ab,0x52ab,0x7f7},
{0x52ac,0x52ad,0x10b7},
{0x52af,0x52af,0x217d},
{0x52b1,0x52b1,0xfad},
{0x52b4,0x52b4,0xfd1},
{0x52b5,0x52b5,0x10ba},
{0x52b9,0x52b9,0x7ae},
{0x52bc,0x52bc,0x10b9},
{0x52be,0x52be,0x58d},
{0x52c0,0x52c0,0x20d1},
{0x52c1,0x52c1,0x10bb},
{0x52c3,0x52c3,0xe84},
{0x52c5,0x52c5,0xbd8},
{0x52c7,0x52c7,0xf10},
{0x52c8,0x52c8,0x381a},
{0x52c9,0x52c9,0xe29},
{0x52cd,0x52cd,0x10bc},
{0x52d0,0x52d0,0x36e8},
{0x52d2,0x52d2,0x1bee},
{0x52d5,0x52d5,0xc88},
{0x52d7,0x52d7,0x10bd},
{0x52d8,0x52d8,0x5e6},
{0x52d9,0x52d9,0xebf},
{0x52db,0x52db,0x20d2},
{0x52dd,0x52dd,0x989},
{0x52de,0x52de,0x10be},
{0x52df,0x52df,0xe37},
{0x52e0,0x52e0,0x10c2},
{0x52e2,0x52e2,0xa4e},
{0x52e3,0x52e3,0x10bf},
{0x52e4,0x52e4,0x6c8},
{0x52e6,0x52e6,0x10c0},
{0x52e7,0x52e7,0x5e7},
{0x52f0,0x52f0,0x381b},
{0x52f2,0x52f2,0x704},
{0x52f3,0x52f3,0x10c3},
{0x52f5,0x52f5,0x10c4},
{0x52f8,0x52f9,0x10c5},
{0x52fa,0x52fa,0x907},
{0x52fe,0x52fe,0x7af},
{0x52ff,0x52ff,0xeea},
{0x5300,0x5300,0x20d3},
{0x5301,0x5301,0xef4},
{0x5302,0x5302,0xccf},
{0x5305,0x5305,0xe41},
{0x5306,0x5306,0x10c7},
{0x5308,0x5308,0x10c8},
{0x530a,0x530b,0x381c},
{0x530d,0x530d,0x10ca},
{0x530f,0x530f,0x10cc},
{0x5310,0x5310,0x10cb},
{0x5315,0x5315,0x10cd},
{0x5316,0x5316,0x53d},
{0x5317,0x5317,0xe7a},
{0x5319,0x5319,0x86c},
{0x531a,0x531a,0x10ce},
{0x531d,0x531d,0xadb},
{0x5320,0x5320,0x98a},
{0x5321,0x5321,0x6a1},
{0x5323,0x5323,0x10cf},
{0x5324,0x5324,0x20d5},
{0x532a,0x532a,0xd6f},
{0x532f,0x532f,0x10d0},
{0x5331,0x5331,0x10d1},
{0x5333,0x5333,0x10d2},
{0x5338,0x5338,0x10d3},
{0x5339,0x5339,0xd96},
{0x533a,0x533a,0x6e0},
{0x533b,0x533b,0x4a9},
{0x533e,0x533e,0x381e},
{0x533f,0x533f,0xc97},
{0x5340,0x5340,0x10d4},
{0x5341,0x5341,0x947},
{0x5343,0x5343,0xa8d},
{0x5345,0x5345,0x10d6},
{0x5346,0x5346,0x10d5},
{0x5347,0x5347,0x98b},
{0x5348,0x5348,0x795},
{0x5349,0x5349,0x10d8},
{0x534a,0x534a,0xd52},
{0x534b,0x534b,0x3820},
{0x534c,0x534c,0x381f},
{0x534d,0x534d,0x10d9},
{0x5351,0x5351,0xd70},
{0x5352,0x5352,0xb14},
{0x5353,0x5353,0xb4e},
{0x5354,0x5354,0x6a0},
{0x5357,0x5357,0xcc6},
{0x5358,0x5358,0xb6f},
{0x535a,0x535a,0xd24},
{0x535c,0x535c,0xe7c},
{0x535e,0x535e,0x10db},
{0x5360,0x5360,0xa8e},
{0x5361,0x5361,0x3821},
{0x5366,0x5366,0x70b},
{0x5369,0x5369,0x10dc},
{0x536c,0x536c,0x3822},
{0x536e,0x536e,0x10dd},
{0x536f,0x536f,0x4ce},
{0x5370,0x5370,0x4b9},
{0x5371,0x5371,0x629},
{0x5372,0x5372,0x20d6},
{0x5373,0x5373,0xb08},
{0x5374,0x5374,0x66b},
{0x5375,0x5375,0xf5b},
{0x5377,0x5377,0x10e0},
{0x5378,0x5378,0x537},
{0x537b,0x537b,0x10df},
{0x537d,0x537d,0x3435},
{0x537f,0x537f,0x6a2},
{0x5382,0x5382,0x10e1},
{0x5384,0x5384,0xefd},
{0x5389,0x5389,0x37d0},
{0x5393,0x5393,0x20d7},
{0x5396,0x5396,0x10e2},
{0x5398,0x5398,0xf9a},
{0x539a,0x539a,0x7b0},
{0x539f,0x539f,0x76a},
{0x53a0,0x53a0,0x10e3},
{0x53a5,0x53a5,0x10e5},
{0x53a6,0x53a6,0x10e4},
{0x53a8,0x53a8,0xa25},
{0x53a9,0x53a9,0x4db},
{0x53ab,0x53ab,0x3823},
{0x53ad,0x53ad,0x500},
{0x53ae,0x53ae,0x10e6},
{0x53b0,0x53b0,0x10e7},
{0x53b2,0x53b2,0x20d8},
{0x53b3,0x53b3,0x76b},
{0x53b6,0x53b6,0x10e8},
{0x53bb,0x53bb,0x688},
{0x53c2,0x53c2,0x880},
{0x53c3,0x53c3,0x10e9},
{0x53c8,0x53c8,0xea2},
{0x53c9,0x53c9,0x825},
{0x53ca,0x53ca,0x674},
{0x53cb,0x53cb,0xf11},
{0x53cc,0x53cc,0xad2},
{0x53cd,0x53cd,0xd53},
{0x53ce,0x53ce,0x929},
{0x53d4,0x53d4,0x951},
{0x53d6,0x53d6,0x914},
{0x53d7,0x53d7,0x921},
{0x53d9,0x53d9,0x980},
{0x53da,0x53da,0x3824},
{0x53db,0x53db,0xd54},
{0x53dd,0x53dd,0x20d9},
{0x53df,0x53df,0x10ec},
{0x53e1,0x53e1,0x4e5},
{0x53e2,0x53e2,0xad3},
{0x53e3,0x53e3,0x7b1},
{0x53e4,0x53e4,0x779},
{0x53e5,0x53e5,0x6df},
{0x53e6,0x53e6,0x3825},
{0x53e8,0x53e8,0x10f0},
{0x53e9,0x53e9,0xb5f},
{0x53ea,0x53ea,0xb5e},
{0x53eb,0x53eb,0x6a3},
{0x53ec,0x53ec,0x98c},
{0x53ed,0x53ed,0x10f1},
{0x53ee,0x53ee,0x10ef},
{0x53ef,0x53ef,0x544},
{0x53f0,0x53f0,0xb46},
{0x53f1,0x53f1,0x8e4},
{0x53f2,0x53f2,0x899},
{0x53f3,0x53f3,0x4c8},
{0x53f5,0x53f5,0x3826},
{0x53f6,0x53f6,0x5ce},
{0x53f7,0x53f7,0x7f8},
{0x53f8,0x53f8,0x898},
{0x53fa,0x53fa,0x10f2},
{0x5401,0x5401,0x10f3},
{0x5403,0x5403,0x663},
{0x5404,0x5404,0x5a4},
{0x5408,0x5408,0x7f9},
{0x5409,0x5409,0x662},
{0x540a,0x540a,0xbfb},
{0x540b,0x540b,0x4c7},
{0x540c,0x540c,0xc89},
{0x540d,0x540d,0xeca},
{0x540e,0x540e,0x7b3},
{0x540f,0x540f,0xf63},
{0x5410,0x5410,0xc41},
{0x5411,0x5411,0x7b2},
{0x541b,0x541b,0x705},
{0x541d,0x541d,0x10fc},
{0x541f,0x541f,0x6db},
{0x5420,0x5420,0xe78},
{0x5426,0x5426,0xd71},
{0x5427,0x5427,0x3827},
{0x5429,0x5429,0x10fb},
{0x542b,0x542b,0x61a},
{0x542c,0x542d,0x10f6},
{0x542e,0x542e,0x10f9},
{0x5433,0x5433,0x35c0},
{0x5436,0x5436,0x10fa},
{0x5438,0x5438,0x675},
{0x5439,0x5439,0xa27},
{0x543b,0x543b,0xdfd},
{0x543c,0x543c,0x10f8},
{0x543d,0x543d,0x10f4},
{0x543e,0x543e,0x797},
{0x543f,0x543f,0x35cf},
{0x5440,0x5440,0x10f5},
{0x5442,0x5442,0xfca},
{0x5446,0x5446,0xe42},
{0x5448,0x5448,0xc04},
{0x5449,0x5449,0x796},
{0x544a,0x544a,0x802},
{0x544d,0x544d,0x3828},
{0x544e,0x544e,0x10fd},
{0x5451,0x5451,0xcb5},
{0x5455,0x5455,0x3723},
{0x545f,0x545f,0x1101},
{0x5466,0x5466,0x3829},
{0x5468,0x5468,0x92a},
{0x546a,0x546a,0x922},
{0x546b,0x546b,0x382a},
{0x5470,0x5470,0x1104},
{0x5471,0x5471,0x1102},
{0x5473,0x5473,0xeaf},
{0x5474,0x5474,0x382b},
{0x5475,0x5475,0x10ff},
{0x5476,0x5476,0x1108},
{0x5477,0x5477,0x1103},
{0x547b,0x547b,0x1106},
{0x547c,0x547c,0x77a},
{0x547d,0x547d,0xecb},
{0x5480,0x5480,0x1107},
{0x5484,0x5484,0x1109},
{0x5486,0x5486,0x110b},
{0x548a,0x548a,0x20dc},
{0x548b,0x548b,0x860},
{0x548c,0x548c,0xfe8},
{0x548d,0x548d,0x382c},
{0x548e,0x548e,0x1100},
{0x548f,0x548f,0x10fe},
{0x5490,0x5490,0x110a},
{0x5492,0x5492,0x1105},
{0x5496,0x5496,0x382d},
{0x549c,0x549c,0x20db},
{0x54a1,0x54a1,0x382e},
{0x54a2,0x54a2,0x110d},
{0x54a4,0x54a4,0x1116},
{0x54a5,0x54a5,0x110f},
{0x54a8,0x54a8,0x1113},
{0x54a9,0x54a9,0x20dd},
{0x54ab,0x54ab,0x1114},
{0x54ac,0x54ac,0x1110},
{0x54ad,0x54ad,0x382f},
{0x54af,0x54af,0x1131},
{0x54b2,0x54b2,0x859},
{0x54b3,0x54b3,0x58f},
{0x54b8,0x54b8,0x110e},
{0x54b9,0x54b9,0x3830},
{0x54bc,0x54bc,0x1118},
{0x54bd,0x54bd,0x4ba},
{0x54be,0x54be,0x1117},
{0x54bf,0x54bf,0x3831},
{0x54c0,0x54c0,0x469},
{0x54c1,0x54c1,0xdbc},
{0x54c2,0x54c2,0x1115},
{0x54c4,0x54c4,0x1111},
{0x54c6,0x54c6,0x3832},
{0x54c7,0x54c7,0x110c},
{0x54c8,0x54c8,0x1112},
{0x54c9,0x54c9,0x838},
{0x54cd,0x54cd,0x3833},
{0x54d8,0x54d8,0x1119},
{0x54e1,0x54e1,0x4bb},
{0x54e2,0x54e2,0x1122},
{0x54e5,0x54e6,0x111a},
{0x54e8,0x54e8,0x98d},
{0x54e9,0x54e9,0xe97},
{0x54ed,0x54ed,0x1120},
{0x54ee,0x54ee,0x111f},
{0x54f2,0x54f2,0xc29},
{0x54fa,0x54fa,0x1121},
{0x54fd,0x54fd,0x111e},
{0x54ff,0x54ff,0x20de},
{0x5504,0x5504,0x4d6},
{0x5506,0x5506,0x826},
{0x5507,0x5507,0x9f6},
{0x550e,0x550e,0x3834},
{0x550f,0x550f,0x111c},
{0x5510,0x5510,0xc5c},
{0x5514,0x5514,0x111d},
{0x5516,0x5516,0x466},
{0x552b,0x552b,0x3835},
{0x552e,0x552e,0x1127},
{0x552f,0x552f,0xf0d},
{0x5531,0x5531,0x98f},
{0x5533,0x5533,0x112d},
{0x5535,0x5535,0x3836},
{0x5538,0x5538,0x112c},
{0x5539,0x5539,0x1123},
{0x553e,0x553e,0xb23},
{0x5540,0x5540,0x1124},
{0x5544,0x5544,0xb4f},
{0x5545,0x5545,0x1129},
{0x5546,0x5546,0x98e},
{0x554a,0x554a,0x3837},
{0x554c,0x554c,0x1126},
{0x554f,0x554f,0xef0},
{0x5553,0x5553,0x712},
{0x5556,0x5557,0x112a},
{0x555c,0x555c,0x1128},
{0x555d,0x555d,0x112e},
{0x555e,0x555e,0x1dd1},
{0x5560,0x5560,0x3838},
{0x5563,0x5563,0x1125},
{0x557b,0x557b,0x1134},
{0x557c,0x557c,0x1139},
{0x557e,0x557e,0x1135},
{0x5580,0x5580,0x1130},
{0x5583,0x5583,0x113a},
{0x5584,0x5584,0xab3},
{0x5586,0x5586,0x20df},
{0x5587,0x5587,0x113c},
{0x5588,0x5588,0x383a},
{0x5589,0x5589,0x7b4},
{0x558a,0x558a,0x1132},
{0x558b,0x558b,0xbbb},
{0x558e,0x558e,0x383b},
{0x5598,0x5598,0x1136},
{0x5599,0x5599,0x112f},
{0x559a,0x559a,0x5e9},
{0x559c,0x559c,0x62a},
{0x559d,0x559d,0x5c3},
{0x559e,0x559e,0x1137},
{0x559f,0x559f,0x1133},
{0x55a7,0x55a7,0x74c},
{0x55a8,0x55a8,0x113d},
{0x55a9,0x55a9,0x113b},
{0x55aa,0x55aa,0xad5},
{0x55ab,0x55ab,0x664},
{0x55ac,0x55ac,0x6a4},
{0x55ae,0x55ae,0x1138},
{0x55b0,0x55b0,0x6ec},
{0x55b6,0x55b6,0x4e6},
{0x55c4,0x55c4,0x1141},
{0x55c5,0x55c5,0x113f},
{0x55c7,0x55c7,0x1178},
{0x55d4,0x55d4,0x1144},
{0x55da,0x55da,0x113e},
{0x55dc,0x55dc,0x1142},
{0x55df,0x55df,0x1140},
{0x55e3,0x55e3,0x89a},
{0x55e4,0x55e4,0x1143},
{0x55f7,0x55f7,0x1146},
{0x55f9,0x55f9,0x114b},
{0x55fd,0x55fd,0x1149},
{0x55fe,0x55fe,0x1148},
{0x5606,0x5606,0xb70},
{0x5608,0x5608,0x383c},
{0x5609,0x5609,0x545},
{0x560e,0x560f,0x383d},
{0x5614,0x5614,0x1145},
{0x5616,0x5616,0x1147},
{0x5617,0x5617,0x990},
{0x5618,0x5618,0x4d5},
{0x561b,0x561b,0x114a},
{0x5620,0x5620,0x3c1d},
{0x5629,0x5629,0x55e},
{0x562f,0x562f,0x1155},
{0x5631,0x5631,0x9e4},
{0x5632,0x5632,0x1151},
{0x5634,0x5634,0x114f},
{0x5636,0x5636,0x1150},
{0x5637,0x5637,0x383f},
{0x5638,0x5638,0x1152},
{0x563f,0x563f,0x3840},
{0x5642,0x5642,0x4df},
{0x5649,0x5649,0x3841},
{0x564b,0x564b,0x3842},
{0x564c,0x564c,0xabb},
{0x564e,0x564e,0x114c},
{0x564f,0x564f,0x3843},
{0x5650,0x5650,0x114d},
{0x5653,0x5653,0x1f1b},
{0x565b,0x565b,0x5d8},
{0x5664,0x5664,0x1154},
{0x5666,0x5666,0x3844},
{0x5668,0x5668,0x62b},
{0x5669,0x5669,0x3845},
{0x566a,0x566a,0x1157},
{0x566b,0x566b,0x1153},
{0x566c,0x566c,0x1156},
{0x566f,0x566f,0x3846},
{0x5671,0x5672,0x3847},
{0x5674,0x5674,0xdfe},
{0x5676,0x5676,0x3c33},
{0x5678,0x5678,0xcad},
{0x567a,0x567a,0xd4c},
{0x5680,0x5680,0x1159},
{0x5686,0x5686,0x1158},
{0x5687,0x5687,0x5a3},
{0x568a,0x568a,0x115a},
{0x568f,0x568f,0x115d},
{0x5694,0x5694,0x115c},
{0x5695,0x5695,0x3849},
{0x5699,0x5699,0x1de6},
{0x569a,0x569a,0x384a},
{0x56a0,0x56a0,0x115b},
{0x56a2,0x56a2,0xcef},
{0x56a5,0x56a5,0x115e},
{0x56ac,0x56ad,0x384b},
{0x56ae,0x56ae,0x115f},
{0x56b1,0x56b1,0x384d},
{0x56b4,0x56b4,0x1161},
{0x56b6,0x56b6,0x1160},
{0x56bc,0x56bc,0x1163},
{0x56c0,0x56c0,0x1166},
{0x56c1,0x56c1,0x1164},
{0x56c2,0x56c2,0x1162},
{0x56c3,0x56c3,0x1165},
{0x56c8,0x56c8,0x1167},
{0x56c9,0x56c9,0x384e},
{0x56ca,0x56ca,0x1e5a},
{0x56ce,0x56ce,0x1168},
{0x56d1,0x56d1,0x1169},
{0x56d3,0x56d3,0x116a},
{0x56d7,0x56d7,0x116b},
{0x56d8,0x56d8,0x107c},
{0x56da,0x56da,0x928},
{0x56db,0x56db,0x89b},
{0x56dd,0x56dd,0x384f},
{0x56de,0x56de,0x573},
{0x56e0,0x56e0,0x4bc},
{0x56e3,0x56e3,0xb82},
{0x56e4,0x56e4,0x3850},
{0x56ee,0x56ee,0x116c},
{0x56f0,0x56f0,0x814},
{0x56f2,0x56f2,0x493},
{0x56f3,0x56f3,0xa24},
{0x56f9,0x56f9,0x116d},
{0x56fa,0x56fa,0x77b},
{0x56fd,0x56fd,0x803},
{0x56ff,0x56ff,0x116f},
{0x5700,0x5700,0x116e},
{0x5703,0x5703,0xe30},
{0x5704,0x5704,0x1170},
{0x5708,0x5708,0x1172},
{0x5709,0x5709,0x1171},
{0x570a,0x570a,0x3851},
{0x570b,0x570b,0x1173},
{0x570d,0x570d,0x1174},
{0x570f,0x570f,0x74d},
{0x5712,0x5712,0x502},
{0x5713,0x5713,0x1175},
{0x5715,0x5715,0x3852},
{0x5716,0x5716,0x1177},
{0x5718,0x5718,0x1176},
{0x571c,0x571c,0x1179},
{0x571f,0x571f,0xc54},
{0x5721,0x5721,0x3680},
{0x5723,0x5723,0x3853},
{0x5726,0x5726,0x117a},
{0x5727,0x5727,0x479},
{0x5728,0x5728,0x84f},
{0x572d,0x572d,0x713},
{0x572f,0x572f,0x3854},
{0x5730,0x5730,0xb8d},
{0x5733,0x5734,0x3855},
{0x5737,0x5738,0x117b},
{0x573b,0x573b,0x117e},
{0x5740,0x5740,0x117f},
{0x5742,0x5742,0x854},
{0x5747,0x5747,0x6c9},
{0x574a,0x574a,0xe65},
{0x574c,0x574c,0x3857},
{0x574e,0x574e,0x117d},
{0x574f,0x574f,0x1180},
{0x5750,0x5750,0x831},
{0x5751,0x5751,0x7b5},
{0x5759,0x5759,0x20e0},
{0x5761,0x5761,0x1184},
{0x5764,0x5764,0x815},
{0x5765,0x5765,0x20e1},
{0x5766,0x5766,0xb71},
{0x5769,0x5769,0x1181},
{0x576a,0x576a,0xbf6},
{0x5770,0x5770,0x3858},
{0x577f,0x577f,0x1185},
{0x5782,0x5782,0xa28},
{0x5788,0x5788,0x1183},
{0x5789,0x5789,0x1186},
{0x578b,0x578b,0x715},
{0x578c,0x578c,0x3859},
{0x5793,0x5793,0x1187},
{0x579c,0x579c,0x385a},
{0x57a0,0x57a0,0x1188},
{0x57a2,0x57a2,0x7b6},
{0x57a3,0x57a3,0x59e},
{0x57a4,0x57a4,0x118a},
{0x57aa,0x57aa,0x118b},
{0x57ac,0x57ac,0x20e2},
{0x57b0,0x57b0,0x118c},
{0x57b3,0x57b3,0x1189},
{0x57b8,0x57b8,0x385b},
{0x57c0,0x57c0,0x1182},
{0x57c3,0x57c3,0x118d},
{0x57c6,0x57c6,0x118e},
{0x57c7,0x57c7,0x20e4},
{0x57c8,0x57c8,0x20e3},
{0x57cb,0x57cb,0xe92},
{0x57ce,0x57ce,0x9d3},
{0x57d2,0x57d3,0x1190},
{0x57d4,0x57d4,0x118f},
{0x57d6,0x57d6,0x1193},
{0x57dc,0x57dc,0xcee},
{0x57df,0x57df,0x4ac},
{0x57e0,0x57e0,0xdc8},
{0x57e3,0x57e3,0x1194},
{0x57e6,0x57e6,0x385c},
{0x57ed,0x57ed,0x385d},
{0x57f4,0x57f4,0x9e5},
{0x57f5,0x57f6,0x385e},
{0x57f7,0x57f7,0x8e5},
{0x57f9,0x57f9,0xd13},
{0x57fa,0x57fa,0x62c},
{0x57fc,0x57fc,0x85b},
{0x57ff,0x57ff,0x3860},
{0x5800,0x5800,0xe87},
{0x5802,0x5802,0xc8a},
{0x5805,0x5805,0x74e},
{0x5806,0x5806,0xb2f},
{0x5809,0x5809,0x3861},
{0x580a,0x580a,0x1192},
{0x580b,0x580b,0x1195},
{0x5815,0x5815,0xb24},
{0x5819,0x5819,0x1196},
{0x581d,0x581d,0x1197},
{0x5820,0x5820,0x3862},
{0x5821,0x5821,0x1199},
{0x5824,0x5824,0xc05},
{0x582a,0x582a,0x5ea},
{0x582f,0x582f,0x1d32},
{0x5830,0x5830,0x503},
{0x5831,0x5831,0xe43},
{0x5832,0x5832,0x3863},
{0x5834,0x5834,0x9d4},
{0x5835,0x5835,0xc42},
{0x583a,0x583a,0x856},
{0x583d,0x583d,0x119f},
{0x5840,0x5840,0xe0d},
{0x5841,0x5841,0xfa5},
{0x584a,0x584a,0x574},
{0x584b,0x584b,0x119b},
{0x5851,0x5851,0xabc},
{0x5852,0x5852,0x119e},
{0x5854,0x5854,0xc5d},
{0x5857,0x5857,0xc43},
{0x5858,0x5858,0xc5e},
{0x5859,0x5859,0xd4d},
{0x585a,0x585a,0xbe9},
{0x585e,0x585e,0x839},
{0x5861,0x5861,0x1e47},
{0x5862,0x5862,0x119a},
{0x5869,0x5869,0x518},
{0x586b,0x586b,0xc30},
{0x5870,0x5870,0x119c},
{0x5872,0x5872,0x1198},
{0x5875,0x5875,0xa16},
{0x5879,0x5879,0x11a0},
{0x587c,0x587c,0x3864},
{0x587e,0x587e,0x958},
{0x5880,0x5880,0x3865},
{0x5883,0x5883,0x6a5},
{0x5885,0x5885,0x11a1},
{0x5893,0x5893,0xe38},
{0x5897,0x5897,0xaff},
{0x589c,0x589c,0xbe2},
{0x589e,0x589e,0x20e7},
{0x589f,0x589f,0x11a3},
{0x58a8,0x58a8,0xe7d},
{0x58a9,0x58a9,0x3866},
{0x58ab,0x58ab,0x11a4},
{0x58ae,0x58ae,0x11a9},
{0x58b2,0x58b2,0x20e8},
{0x58b3,0x58b3,0xdff},
{0x58b8,0x58b8,0x11a8},
{0x58b9,0x58b9,0x11a2},
{0x58ba,0x58ba,0x11a5},
{0x58bb,0x58bb,0x11a7},
{0x58be,0x58be,0x816},
{0x58c1,0x58c1,0xe19},
{0x58c5,0x58c5,0x11aa},
{0x58c7,0x58c7,0xb83},
{0x58ca,0x58ca,0x575},
{0x58cc,0x58cc,0x9d5},
{0x58ce,0x58ce,0x3867},
{0x58d0,0x58d0,0x3868},
{0x58d1,0x58d1,0x11ac},
{0x58d3,0x58d3,0x11ab},
{0x58d4,0x58d4,0x3869},
{0x58d5,0x58d5,0x7fa},
{0x58d7,0x58d7,0x11ad},
{0x58d8,0x58d8,0x11af},
{0x58d9,0x58d9,0x11ae},
{0x58da,0x58da,0x386a},
{0x58dc,0x58dc,0x11b1},
{0x58de,0x58de,0x11a6},
{0x58df,0x58df,0x11b3},
{0x58e4,0x58e4,0x11b2},
{0x58e5,0x58e5,0x11b0},
{0x58e9,0x58e9,0x386b},
{0x58eb,0x58eb,0x89c},
{0x58ec,0x58ec,0xa17},
{0x58ee,0x58ee,0xad6},
{0x58ef,0x58ef,0x11b4},
{0x58f0,0x58f0,0xa60},
{0x58f1,0x58f1,0x4b1},
{0x58f2,0x58f2,0xd1a},
{0x58f7,0x58f7,0xbf7},
{0x58f9,0x58f9,0x11b6},
{0x58fa,0x58fa,0x11b5},
{0x58fb,0x58fd,0x11b7},
{0x5902,0x5902,0x11ba},
{0x5909,0x5909,0xe21},
{0x590a,0x590a,0x11bb},
{0x590b,0x590b,0x20e9},
{0x590c,0x590c,0x386c},
{0x590f,0x590f,0x546},
{0x5910,0x5910,0x11bc},
{0x5915,0x5915,0xf26},
{0x5916,0x5916,0x58e},
{0x5918,0x5918,0x10de},
{0x5919,0x5919,0x952},
{0x591a,0x591a,0xb1f},
{0x591b,0x591b,0x11bd},
{0x591c,0x591c,0xef7},
{0x5922,0x5922,0xec0},
{0x5924,0x5924,0x386d},
{0x5925,0x5925,0x11bf},
{0x5927,0x5927,0xb47},
{0x5929,0x5929,0xc31},
{0x592a,0x592a,0xb20},
{0x592b,0x592b,0xdc9},
{0x592c,0x592d,0x11c0},
{0x592e,0x592e,0x51d},
{0x592f,0x592f,0x386e},
{0x5931,0x5931,0x8e6},
{0x5932,0x5932,0x11c2},
{0x5937,0x5937,0x494},
{0x5938,0x5938,0x11c3},
{0x5939,0x5939,0x3725},
{0x593e,0x593e,0x11c4},
{0x5944,0x5944,0x504},
{0x5947,0x5947,0x62d},
{0x5948,0x5948,0xcb8},
{0x5949,0x5949,0xe44},
{0x594e,0x594e,0x11c8},
{0x594f,0x594f,0xad7},
{0x5950,0x5950,0x11c7},
{0x5951,0x5951,0x716},
{0x5953,0x5953,0x20ea},
{0x5954,0x5954,0xe89},
{0x5955,0x5955,0x11c6},
{0x5957,0x5957,0xc5f},
{0x5958,0x5958,0x11ca},
{0x595a,0x595a,0x11c9},
{0x595b,0x595b,0x20eb},
{0x595d,0x595d,0x20ec},
{0x5960,0x5960,0x11cc},
{0x5961,0x5961,0x386f},
{0x5962,0x5962,0x11cb},
{0x5963,0x5963,0x20ed},
{0x5965,0x5965,0x51e},
{0x5967,0x5967,0x11cd},
{0x5968,0x5968,0x991},
{0x5969,0x5969,0x11cf},
{0x596a,0x596a,0xb63},
{0x596c,0x596c,0x11ce},
{0x596d,0x596d,0x3870},
{0x596e,0x596e,0xe03},
{0x5973,0x5973,0x981},
{0x5974,0x5974,0xc55},
{0x5978,0x5978,0x11d0},
{0x597d,0x597d,0x7b7},
{0x5981,0x5981,0x11d1},
{0x5982,0x5982,0xcd7},
{0x5983,0x5983,0xd72},
{0x5984,0x5984,0xedd},
{0x598a,0x598a,0xcdb},
{0x598d,0x598d,0x11da},
{0x5993,0x5993,0x652},
{0x5996,0x5996,0xf2f},
{0x5999,0x5999,0xebb},
{0x599b,0x599b,0x1239},
{0x599d,0x599d,0x11d2},
{0x59a3,0x59a3,0x11d5},
{0x59a4,0x59a4,0x20ee},
{0x59a5,0x59a5,0xb25},
{0x59a8,0x59a8,0xe66},
{0x59ac,0x59ac,0xc44},
{0x59b2,0x59b2,0x11d6},
{0x59b9,0x59b9,0xe93},
{0x59ba,0x59ba,0x20ef},
{0x59bb,0x59bb,0x83a},
{0x59be,0x59be,0x992},
{0x59c6,0x59c6,0x11d7},
{0x59c9,0x59c9,0x89e},
{0x59ca,0x59ca,0x3871},
{0x59cb,0x59cb,0x89d},
{0x59d0,0x59d0,0x47d},
{0x59d1,0x59d1,0x77c},
{0x59d2,0x59d2,0x3872},
{0x59d3,0x59d3,0xa4f},
{0x59d4,0x59d4,0x495},
{0x59d9,0x59da,0x11db},
{0x59dc,0x59dc,0x11d9},
{0x59dd,0x59dd,0x3873},
{0x59e3,0x59e4,0x3874},
{0x59e5,0x59e5,0x4da},
{0x59e6,0x59e6,0x5eb},
{0x59e8,0x59e8,0x11d8},
{0x59ea,0x59ea,0xed1},
{0x59eb,0x59eb,0xda3},
{0x59ec,0x59ec,0x36ad},
{0x59f6,0x59f6,0x46c},
{0x59fb,0x59fb,0x4bd},
{0x59ff,0x59ff,0x89f},
{0x5a01,0x5a01,0x496},
{0x5a03,0x5a03,0x467},
{0x5a04,0x5a04,0x3876},
{0x5a09,0x5a09,0x11e1},
{0x5a0c,0x5a0c,0x3877},
{0x5a11,0x5a11,0x11df},
{0x5a18,0x5a18,0xec8},
{0x5a1a,0x5a1a,0x11e2},
{0x5a1b,0x5a1b,0x35c1},
{0x5a1c,0x5a1c,0x11e0},
{0x5a1f,0x5a1f,0x11de},
{0x5a20,0x5a20,0x9f7},
{0x5a23,0x5a23,0x3878},
{0x5a25,0x5a25,0x11dd},
{0x5a29,0x5a29,0xe2a},
{0x5a2f,0x5a2f,0x798},
{0x5a35,0x5a36,0x11e6},
{0x5a3c,0x5a3c,0x993},
{0x5a40,0x5a40,0x11e3},
{0x5a41,0x5a41,0xfd2},
{0x5a46,0x5a46,0xd02},
{0x5a47,0x5a47,0x3879},
{0x5a49,0x5a49,0x11e5},
{0x5a55,0x5a55,0x387a},
{0x5a5a,0x5a5a,0x817},
{0x5a62,0x5a62,0x11e8},
{0x5a63,0x5a63,0x387b},
{0x5a66,0x5a66,0xdca},
{0x5a6a,0x5a6a,0x11e9},
{0x5a6c,0x5a6c,0x11e4},
{0x5a6d,0x5a6d,0x387c},
{0x5a7e,0x5a7e,0x387d},
{0x5a7f,0x5a7f,0xec7},
{0x5a92,0x5a92,0xd14},
{0x5a9a,0x5a9a,0x11ea},
{0x5a9b,0x5a9b,0xda4},
{0x5a9e,0x5a9e,0x387e},
{0x5aa7,0x5aa7,0x387f},
{0x5aac,0x5aac,0x3880},
{0x5ab3,0x5ab3,0x3881},
{0x5abc,0x5abc,0x11eb},
{0x5abd,0x5abd,0x11ef},
{0x5abe,0x5abe,0x11ec},
{0x5ac1,0x5ac1,0x547},
{0x5ac2,0x5ac2,0x11ee},
{0x5ac9,0x5ac9,0x8e7},
{0x5acb,0x5acb,0x11ed},
{0x5acc,0x5acc,0x74f},
{0x5ad0,0x5ad0,0x11fb},
{0x5ad6,0x5ad6,0x11f4},
{0x5ad7,0x5ad7,0x11f1},
{0x5ae0,0x5ae0,0x3882},
{0x5ae1,0x5ae1,0xba2},
{0x5ae3,0x5ae3,0x11f0},
{0x5ae6,0x5ae6,0x11f2},
{0x5ae9,0x5ae9,0x11f3},
{0x5afa,0x5afb,0x11f5},
{0x5b00,0x5b00,0x3883},
{0x5b09,0x5b09,0x62e},
{0x5b0b,0x5b0b,0x11f8},
{0x5b0c,0x5b0c,0x11f7},
{0x5b16,0x5b16,0x11f9},
{0x5b19,0x5b19,0x3884},
{0x5b22,0x5b22,0x9d6},
{0x5b25,0x5b25,0x3885},
{0x5b2a,0x5b2a,0x11fc},
{0x5b2c,0x5b2c,0xbf8},
{0x5b2d,0x5b2d,0x3886},
{0x5b30,0x5b30,0x4e7},
{0x5b32,0x5b32,0x11fa},
{0x5b36,0x5b36,0x11fd},
{0x5b3e,0x5b3e,0x11fe},
{0x5b40,0x5b40,0x1201},
{0x5b41,0x5b41,0x3887},
{0x5b43,0x5b43,0x11ff},
{0x5b45,0x5b45,0x1200},
{0x5b50,0x5b50,0x8a0},
{0x5b51,0x5b51,0x1202},
{0x5b54,0x5b54,0x7b8},
{0x5b55,0x5b55,0x1203},
{0x5b56,0x5b56,0x20f0},
{0x5b57,0x5b57,0x8c8},
{0x5b58,0x5b58,0xb18},
{0x5b5a,0x5b5b,0x1204},
{0x5b5c,0x5b5c,0x8a8},
{0x5b5d,0x5b5d,0x7b9},
{0x5b5f,0x5b5f,0xede},
{0x5b63,0x5b63,0x642},
{0x5b64,0x5b64,0x77d},
{0x5b65,0x5b65,0x1206},
{0x5b66,0x5b66,0x5b6},
{0x5b69,0x5b69,0x1207},
{0x5b6b,0x5b6b,0xb19},
{0x5b70,0x5b70,0x1208},
{0x5b71,0x5b71,0x1230},
{0x5b73,0x5b73,0x1209},
{0x5b75,0x5b75,0x120a},
{0x5b78,0x5b78,0x120b},
{0x5b7a,0x5b7a,0x120d},
{0x5b7c,0x5b7c,0x3888},
{0x5b7e,0x5b7f,0x3889},
{0x5b80,0x5b80,0x120e},
{0x5b82,0x5b82,0x3610},
{0x5b83,0x5b83,0x120f},
{0x5b85,0x5b85,0xb50},
{0x5b87,0x5b87,0x4c9},
{0x5b88,0x5b88,0x915},
{0x5b89,0x5b89,0x486},
{0x5b8a,0x5b8a,0x388b},
{0x5b8b,0x5b8b,0xad9},
{0x5b8c,0x5b8c,0x5ec},
{0x5b8d,0x5b8d,0x8e1},
{0x5b8f,0x5b8f,0x7ba},
{0x5b95,0x5b95,0xc60},
{0x5b97,0x5b97,0x92b},
{0x5b98,0x5b98,0x5ed},
{0x5b99,0x5b99,0xba6},
{0x5b9a,0x5b9a,0xc06},
{0x5b9b,0x5b9b,0x47c},
{0x5b9c,0x5b9c,0x653},
{0x5b9d,0x5b9d,0xe45},
{0x5b9f,0x5b9f,0x8ee},
{0x5ba2,0x5ba2,0x66c},
{0x5ba3,0x5ba3,0xa8f},
{0x5ba4,0x5ba4,0x8e8},
{0x5ba5,0x5ba5,0xf12},
{0x5ba6,0x5ba6,0x1210},
{0x5bae,0x5bae,0x676},
{0x5bb0,0x5bb0,0x83b},
{0x5bb3,0x5bb3,0x590},
{0x5bb4,0x5bb4,0x505},
{0x5bb5,0x5bb5,0x994},
{0x5bb6,0x5bb6,0x548},
{0x5bb8,0x5bb8,0x1211},
{0x5bb9,0x5bb9,0xf30},
{0x5bbf,0x5bbf,0x953},
{0x5bc0,0x5bc0,0x20f1},
{0x5bc2,0x5bc2,0x910},
{0x5bc3,0x5bc3,0x1212},
{0x5bc4,0x5bc4,0x62f},
{0x5bc5,0x5bc5,0xcaa},
{0x5bc6,0x5bc6,0xeb5},
{0x5bc7,0x5bc7,0x1213},
{0x5bc9,0x5bc9,0x1214},
{0x5bcc,0x5bcc,0xdcb},
{0x5bd0,0x5bd0,0x1216},
{0x5bd2,0x5bd2,0x5e4},
{0x5bd3,0x5bd3,0x6ef},
{0x5bd4,0x5bd4,0x1215},
{0x5bd8,0x5bd8,0x20f3},
{0x5bdb,0x5bdb,0x5ee},
{0x5bdd,0x5bdd,0x9f8},
{0x5bde,0x5bde,0x121a},
{0x5bdf,0x5bdf,0x86f},
{0x5be1,0x5be1,0x549},
{0x5be2,0x5be2,0x1219},
{0x5be4,0x5be4,0x1217},
{0x5be5,0x5be5,0x121b},
{0x5be6,0x5be6,0x1218},
{0x5be7,0x5be7,0xce1},
{0x5be8,0x5be8,0x148e},
{0x5be9,0x5be9,0x9f9},
{0x5beb,0x5beb,0x121c},
{0x5bee,0x5bee,0xf88},
{0x5bf0,0x5bf0,0x121d},
{0x5bf3,0x5bf3,0x121f},
{0x5bf5,0x5bf5,0xbbc},
{0x5bf6,0x5bf6,0x121e},
{0x5bf8,0x5bf8,0xa47},
{0x5bfa,0x5bfa,0x8c9},
{0x5bfe,0x5bfe,0xb30},
{0x5bff,0x5bff,0x923},
{0x5c01,0x5c01,0xde7},
{0x5c02,0x5c02,0xa90},
{0x5c04,0x5c04,0x8f9},
{0x5c05,0x5c05,0x1220},
{0x5c06,0x5c06,0x995},
{0x5c07,0x5c08,0x1221},
{0x5c09,0x5c09,0x497},
{0x5c0a,0x5c0a,0xb1a},
{0x5c0b,0x5c0b,0xa18},
{0x5c0d,0x5c0d,0x1223},
{0x5c0e,0x5c0e,0xc8b},
{0x5c0f,0x5c0f,0x996},
{0x5c11,0x5c11,0x997},
{0x5c13,0x5c13,0x1224},
{0x5c14,0x5c14,0x372a},
{0x5c16,0x5c16,0xa91},
{0x5c19,0x5c19,0x360b},
{0x5c1a,0x5c1a,0x998},
{0x5c1e,0x5c1e,0x20f5},
{0x5c20,0x5c20,0x1225},
{0x5c22,0x5c22,0x1226},
{0x5c23,0x5c23,0x388c},
{0x5c24,0x5c24,0xeec},
{0x5c28,0x5c28,0x1227},
{0x5c2b,0x5c2b,0x388d},
{0x5c2d,0x5c2d,0x6be},
{0x5c30,0x5c30,0x388e},
{0x5c31,0x5c31,0x92c},
{0x5c38,0x5c39,0x1228},
{0x5c3a,0x5c3a,0x908},
{0x5c3b,0x5c3b,0x9f2},
{0x5c3c,0x5c3c,0xccc},
{0x5c3d,0x5c3d,0xa1a},
{0x5c3e,0x5c3e,0xd8c},
{0x5c3f,0x5c3f,0xcd8},
{0x5c40,0x5c40,0x6c1},
{0x5c41,0x5c41,0x122a},
{0x5c45,0x5c45,0x689},
{0x5c46,0x5c46,0x122b},
{0x5c48,0x5c48,0x6f6},
{0x5c4a,0x5c4a,0xca7},
{0x5c4b,0x5c4b,0x530},
{0x5c4d,0x5c4d,0x8a1},
{0x5c4e,0x5c4e,0x122c},
{0x5c4f,0x5c4f,0x122f},
{0x5c50,0x5c50,0x122e},
{0x5c51,0x5c51,0x6f5},
{0x5c53,0x5c53,0x122d},
{0x5c55,0x5c55,0xc32},
{0x5c5b,0x5c5b,0x1e92},
{0x5c5e,0x5c5e,0xb10},
{0x5c60,0x5c60,0xc45},
{0x5c61,0x5c61,0x8f4},
{0x5c62,0x5c62,0x1e0d},
{0x5c63,0x5c63,0x388f},
{0x5c64,0x5c64,0xada},
{0x5c65,0x5c65,0xf64},
{0x5c69,0x5c69,0x3890},
{0x5c6c,0x5c6c,0x1231},
{0x5c6e,0x5c6e,0x1232},
{0x5c6f,0x5c6f,0xcae},
{0x5c71,0x5c71,0x881},
{0x5c76,0x5c76,0x1234},
{0x5c79,0x5c79,0x1235},
{0x5c7c,0x5c7c,0x3891},
{0x5c8c,0x5c8c,0x1236},
{0x5c90,0x5c90,0x630},
{0x5c91,0x5c91,0x1237},
{0x5c94,0x5c94,0x1238},
{0x5ca1,0x5ca1,0x52c},
{0x5ca6,0x5ca6,0x20f6},
{0x5ca8,0x5ca8,0xabd},
{0x5ca9,0x5ca9,0x620},
{0x5cab,0x5cab,0x123a},
{0x5cac,0x5cac,0xeb4},
{0x5cb1,0x5cb1,0xb32},
{0x5cb3,0x5cb3,0x5b7},
{0x5cb6,0x5cb6,0x123c},
{0x5cb7,0x5cb7,0x123e},
{0x5cb8,0x5cb8,0x61b},
{0x5cba,0x5cba,0x20f7},
{0x5cbb,0x5cbb,0x123b},
{0x5cbc,0x5cbc,0x123d},
{0x5cbe,0x5cbe,0x1240},
{0x5cc5,0x5cc5,0x123f},
{0x5cc7,0x5cc7,0x1241},
{0x5ccb,0x5ccb,0x3892},
{0x5cd2,0x5cd2,0x3893},
{0x5cd9,0x5cd9,0x1242},
{0x5ce0,0x5ce0,0xc95},
{0x5ce1,0x5ce1,0x6a6},
{0x5ce6,0x5ce6,0x372c},
{0x5ce8,0x5ce8,0x565},
{0x5ce9,0x5ce9,0x1243},
{0x5cea,0x5cea,0x1248},
{0x5ced,0x5ced,0x1246},
{0x5cef,0x5cef,0xe47},
{0x5cf0,0x5cf0,0xe46},
{0x5cf4,0x5cf4,0x3894},
{0x5cf5,0x5cf5,0x20f8},
{0x5cf6,0x5cf6,0xc61},
{0x5cfa,0x5cfa,0x1245},
{0x5cfb,0x5cfb,0x95e},
{0x5cfd,0x5cfd,0x1244},
{0x5d07,0x5d07,0xa38},
{0x5d0b,0x5d0b,0x1249},
{0x5d0e,0x5d0e,0x85a},
{0x5d11,0x5d11,0x124f},
{0x5d14,0x5d14,0x1250},
{0x5d15,0x5d15,0x124a},
{0x5d16,0x5d16,0x591},
{0x5d17,0x5d17,0x124b},
{0x5d18,0x5d18,0x1254},
{0x5d19,0x5d19,0x1253},
{0x5d1a,0x5d1a,0x1252},
{0x5d1b,0x5d1b,0x124e},
{0x5d1f,0x5d1f,0x124d},
{0x5d22,0x5d22,0x1251},
{0x5d24,0x5d24,0x3895},
{0x5d26,0x5d26,0x3896},
{0x5d27,0x5d27,0x20f9},
{0x5d29,0x5d29,0xe48},
{0x5d42,0x5d42,0x20fc},
{0x5d43,0x5d43,0x3897},
{0x5d46,0x5d46,0x3898},
{0x5d4a,0x5d4a,0x3899},
{0x5d4b,0x5d4b,0x1258},
{0x5d4c,0x5d4c,0x1255},
{0x5d4e,0x5d4e,0x1257},
{0x5d50,0x5d50,0xf5c},
{0x5d52,0x5d52,0x1256},
{0x5d53,0x5d53,0x20fa},
{0x5d5c,0x5d5c,0x124c},
{0x5d69,0x5d69,0xa39},
{0x5d6c,0x5d6c,0x1259},
{0x5d6d,0x5d6d,0x20fd},
{0x5d6f,0x5d6f,0x827},
{0x5d73,0x5d73,0x125a},
{0x5d76,0x5d76,0x125b},
{0x5d82,0x5d82,0x125e},
{0x5d84,0x5d84,0x125d},
{0x5d87,0x5d87,0x125c},
{0x5d8b,0x5d8b,0xc62},
{0x5d8c,0x5d8c,0x1247},
{0x5d90,0x5d90,0x1264},
{0x5d92,0x5d92,0x389a},
{0x5d94,0x5d94,0x389b},
{0x5d99,0x5d99,0x389c},
{0x5d9d,0x5d9d,0x1260},
{0x5da0,0x5da0,0x389d},
{0x5da2,0x5da2,0x125f},
{0x5dac,0x5dac,0x1261},
{0x5dae,0x5dae,0x1262},
{0x5db7,0x5db7,0x1265},
{0x5db8,0x5db9,0x20fe},
{0x5dba,0x5dba,0xfae},
{0x5dbc,0x5dbc,0x1266},
{0x5dbd,0x5dbd,0x1263},
{0x5dc9,0x5dc9,0x1267},
{0x5dcc,0x5dcc,0x61c},
{0x5dcd,0x5dcd,0x1268},
{0x5dd0,0x5dd0,0x2100},
{0x5dd2,0x5dd2,0x126a},
{0x5dd3,0x5dd3,0x1269},
{0x5dd6,0x5dd6,0x126b},
{0x5dd8,0x5dd8,0x389e},
{0x5ddb,0x5ddb,0x126c},
{0x5ddd,0x5ddd,0xa92},
{0x5dde,0x5dde,0x92d},
{0x5de0,0x5de0,0x389f},
{0x5de1,0x5de1,0x96e},
{0x5de2,0x5de2,0x3432},
{0x5de3,0x5de3,0xae5},
{0x5de5,0x5de5,0x7bb},
{0x5de6,0x5de6,0x828},
{0x5de7,0x5de7,0x7bc},
{0x5de8,0x5de8,0x68a},
{0x5deb,0x5deb,0x126d},
{0x5dee,0x5dee,0x829},
{0x5df1,0x5df1,0x77e},
{0x5df2,0x5df2,0x126e},
{0x5df3,0x5df3,0xeb2},
{0x5df4,0x5df4,0xcf9},
{0x5df5,0x5df5,0x126f},
{0x5df7,0x5df7,0x7bd},
{0x5df8,0x5df8,0x38a0},
{0x5dfb,0x5dfb,0x5e8},
{0x5dfd,0x5dfd,0xb65},
{0x5dfe,0x5dfe,0x6ca},
{0x5dff,0x5dff,0x35e2},
{0x5e00,0x5e00,0x38a1},
{0x5e02,0x5e02,0x8a2},
{0x5e03,0x5e03,0xdcd},
{0x5e06,0x5e06,0xd55},
{0x5e0b,0x5e0b,0x1270},
{0x5e0c,0x5e0c,0x631},
{0x5e11,0x5e11,0x1273},
{0x5e12,0x5e12,0x38a2},
{0x5e14,0x5e15,0x38a3},
{0x5e16,0x5e16,0xbbd},
{0x5e18,0x5e18,0x38a5},
{0x5e19,0x5e19,0x1272},
{0x5e1a,0x5e1a,0x1271},
{0x5e1b,0x5e1b,0x1274},
{0x5e1d,0x5e1d,0xc07},
{0x5e25,0x5e25,0xa29},
{0x5e2b,0x5e2b,0x8a3},
{0x5e2d,0x5e2d,0xa6e},
{0x5e2e,0x5e2e,0x38a6},
{0x5e2f,0x5e2f,0xb33},
{0x5e30,0x5e30,0x63c},
{0x5e33,0x5e33,0xbbe},
{0x5e36,0x5e37,0x1275},
{0x5e38,0x5e38,0x9d7},
{0x5e3d,0x5e3d,0xe67},
{0x5e40,0x5e40,0x1279},
{0x5e43,0x5e43,0x1278},
{0x5e44,0x5e44,0x1277},
{0x5e45,0x5e45,0xdef},
{0x5e47,0x5e47,0x1280},
{0x5e4c,0x5e4c,0xe88},
{0x5e4e,0x5e4e,0x127a},
{0x5e54,0x5e54,0x127c},
{0x5e55,0x5e55,0xe99},
{0x5e57,0x5e57,0x127b},
{0x5e58,0x5e58,0x38a7},
{0x5e5f,0x5e5f,0x127d},
{0x5e61,0x5e61,0xd3c},
{0x5e62,0x5e62,0x127e},
{0x5e63,0x5e63,0xe0e},
{0x5e64,0x5e64,0x127f},
{0x5e6b,0x5e6c,0x38a8},
{0x5e72,0x5e72,0x5ef},
{0x5e73,0x5e73,0xe0f},
{0x5e74,0x5e74,0xce5},
{0x5e75,0x5e76,0x1281},
{0x5e78,0x5e78,0x7be},
{0x5e79,0x5e79,0x5f0},
{0x5e7a,0x5e7a,0x1283},
{0x5e7b,0x5e7b,0x76c},
{0x5e7c,0x5e7c,0xf2e},
{0x5e7d,0x5e7d,0xf13},
{0x5e7e,0x5e7e,0x632},
{0x5e7f,0x5e7f,0x1285},
{0x5e81,0x5e81,0xbbf},
{0x5e83,0x5e83,0x7bf},
{0x5e84,0x5e84,0x999},
{0x5e87,0x5e87,0xd73},
{0x5e8a,0x5e8a,0x99a},
{0x5e8f,0x5e8f,0x982},
{0x5e95,0x5e95,0xc08},
{0x5e96,0x5e96,0xe49},
{0x5e97,0x5e97,0xc33},
{0x5e99,0x5e99,0x36b0},
{0x5e9a,0x5e9a,0x7c0},
{0x5e9c,0x5e9c,0xdce},
{0x5ea0,0x5ea0,0x1286},
{0x5ea6,0x5ea6,0xc53},
{0x5ea7,0x5ea7,0x832},
{0x5ea8,0x5ea8,0x38aa},
{0x5eaa,0x5eaa,0x38ab},
{0x5eab,0x5eab,0x77f},
{0x5ead,0x5ead,0xc09},
{0x5eb5,0x5eb5,0x487},
{0x5eb6,0x5eb6,0x978},
{0x5eb7,0x5eb7,0x7c1},
{0x5eb8,0x5eb8,0xf31},
{0x5ebe,0x5ebf,0x38ac},
{0x5ec1,0x5ec2,0x1287},
{0x5ec3,0x5ec3,0xd07},
{0x5ec8,0x5ec8,0x1289},
{0x5ec9,0x5ec9,0xfbf},
{0x5eca,0x5eca,0xfd3},
{0x5ecf,0x5ecf,0x128b},
{0x5ed0,0x5ed0,0x128a},
{0x5ed2,0x5ed2,0x38af},
{0x5ed3,0x5ed3,0x5a5},
{0x5ed6,0x5ed6,0x128c},
{0x5eda,0x5edb,0x128f},
{0x5edd,0x5edd,0x128e},
{0x5edf,0x5edf,0xdb2},
{0x5ee0,0x5ee0,0x99b},
{0x5ee1,0x5ee1,0x1292},
{0x5ee2,0x5ee2,0x1291},
{0x5ee3,0x5ee3,0x128d},
{0x5ee8,0x5ee9,0x1293},
{0x5eec,0x5eec,0x1295},
{0x5ef0,0x5ef0,0x1298},
{0x5ef1,0x5ef1,0x1296},
{0x5ef3,0x5ef3,0x1297},
{0x5ef4,0x5ef4,0x1299},
{0x5ef6,0x5ef6,0x506},
{0x5ef7,0x5ef7,0xc0a},
{0x5ef8,0x5ef8,0x129a},
{0x5efa,0x5efa,0x750},
{0x5efb,0x5efb,0x576},
{0x5efc,0x5efc,0xcec},
{0x5efe,0x5efe,0x129b},
{0x5eff,0x5eff,0xcd3},
{0x5f01,0x5f01,0xe2b},
{0x5f03,0x5f03,0x129c},
{0x5f04,0x5f04,0xfd4},
{0x5f07,0x5f07,0x38b0},
{0x5f09,0x5f09,0x129d},
{0x5f0a,0x5f0a,0xe10},
{0x5f0b,0x5f0b,0x12a0},
{0x5f0c,0x5f0c,0xffa},
{0x5f0d,0x5f0d,0x100a},
{0x5f0e,0x5f0e,0x38b1},
{0x5f0f,0x5f0f,0x8dc},
{0x5f10,0x5f10,0xccd},
{0x5f11,0x5f11,0x12a1},
{0x5f13,0x5f13,0x677},
{0x5f14,0x5f14,0xbc0},
{0x5f15,0x5f15,0x4be},
{0x5f16,0x5f16,0x12a2},
{0x5f17,0x5f17,0xdf6},
{0x5f18,0x5f18,0x7c2},
{0x5f1b,0x5f1b,0xb8e},
{0x5f1c,0x5f1d,0x38b2},
{0x5f1f,0x5f1f,0xc0b},
{0x5f21,0x5f21,0x2101},
{0x5f22,0x5f22,0x38b4},
{0x5f25,0x5f25,0xefb},
{0x5f26,0x5f26,0x76d},
{0x5f27,0x5f27,0x780},
{0x5f28,0x5f28,0x38b5},
{0x5f29,0x5f29,0x12a3},
{0x5f2d,0x5f2d,0x12a4},
{0x5f2f,0x5f2f,0x12aa},
{0x5f31,0x5f31,0x911},
{0x5f34,0x5f34,0x2102},
{0x5f35,0x5f35,0xbc1},
{0x5f36,0x5f36,0x38b6},
{0x5f37,0x5f37,0x6a7},
{0x5f38,0x5f38,0x12a5},
{0x5f3a,0x5f3a,0x3598},
{0x5f3b,0x5f3b,0x38b7},
{0x5f3c,0x5f3c,0xd9d},
{0x5f3e,0x5f3e,0xb84},
{0x5f40,0x5f40,0x38b8},
{0x5f41,0x5f41,0x12a6},
{0x5f45,0x5f45,0x20b2},
{0x5f48,0x5f48,0x12a7},
{0x5f4a,0x5f4a,0x6a8},
{0x5f4c,0x5f4c,0x12a8},
{0x5f4e,0x5f4e,0x12a9},
{0x5f50,0x5f50,0x38b9},
{0x5f51,0x5f51,0x12ab},
{0x5f53,0x5f53,0xc70},
{0x5f56,0x5f57,0x12ac},
{0x5f58,0x5f58,0x38ba},
{0x5f59,0x5f59,0x12ae},
{0x5f5c,0x5f5c,0x129f},
{0x5f5d,0x5f5d,0x129e},
{0x5f61,0x5f61,0x12af},
{0x5f62,0x5f62,0x717},
{0x5f64,0x5f64,0x38bb},
{0x5f65,0x5f65,0x36ac},
{0x5f66,0x5f66,0xd99},
{0x5f67,0x5f67,0x2103},
{0x5f69,0x5f69,0x83c},
{0x5f6a,0x5f6a,0xda9},
{0x5f6b,0x5f6b,0xbc2},
{0x5f6c,0x5f6c,0xdbd},
{0x5f6d,0x5f6d,0x12b0},
{0x5f70,0x5f70,0x99c},
{0x5f71,0x5f71,0x4e8},
{0x5f73,0x5f73,0x12b1},
{0x5f77,0x5f77,0x12b2},
{0x5f79,0x5f79,0xefe},
{0x5f7c,0x5f7c,0xd74},
{0x5f7f,0x5f7f,0x12b5},
{0x5f80,0x5f80,0x51f},
{0x5f81,0x5f81,0xa50},
{0x5f82,0x5f82,0x12b4},
{0x5f83,0x5f83,0x12b3},
{0x5f84,0x5f84,0x718},
{0x5f85,0x5f85,0xb34},
{0x5f87,0x5f87,0x12b9},
{0x5f88,0x5f88,0x12b7},
{0x5f89,0x5f89,0x38bc},
{0x5f8a,0x5f8a,0x12b6},
{0x5f8b,0x5f8b,0xf6f},
{0x5f8c,0x5f8c,0x799},
{0x5f90,0x5f90,0x983},
{0x5f91,0x5f91,0x12b8},
{0x5f92,0x5f92,0xc46},
{0x5f93,0x5f93,0x948},
{0x5f97,0x5f97,0xc98},
{0x5f98,0x5f98,0x12bc},
{0x5f99,0x5f99,0x12bb},
{0x5f9c,0x5f9c,0x38bd},
{0x5f9e,0x5f9e,0x12ba},
{0x5fa0,0x5fa0,0x12bd},
{0x5fa1,0x5fa1,0x79a},
{0x5fa4,0x5fa4,0x38bf},
{0x5fa7,0x5fa7,0x38be},
{0x5fa8,0x5fa8,0x12be},
{0x5fa9,0x5fa9,0xdee},
{0x5faa,0x5faa,0x965},
{0x5fad,0x5fad,0x12bf},
{0x5fae,0x5fae,0xd8d},
{0x5faf,0x5faf,0x38c0},
{0x5fb3,0x5fb3,0xc99},
{0x5fb4,0x5fb4,0xbc3},
{0x5fb5,0x5fb5,0x3438},
{0x5fb7,0x5fb7,0x2104},
{0x5fb8,0x5fb8,0x38c1},
{0x5fb9,0x5fb9,0xc2a},
{0x5fbc,0x5fbc,0x12c0},
{0x5fbd,0x5fbd,0x645},
{0x5fc3,0x5fc3,0x9fa},
{0x5fc4,0x5fc4,0x38c2},
{0x5fc5,0x5fc5,0xd9e},
{0x5fc9,0x5fc9,0x38c3},
{0x5fcc,0x5fcc,0x633},
{0x5fcd,0x5fcd,0xcdc},
{0x5fd6,0x5fd6,0x12c1},
{0x5fd7,0x5fd7,0x8a4},
{0x5fd8,0x5fd9,0xe68},
{0x5fdc,0x5fdc,0x520},
{0x5fdd,0x5fdd,0x12c6},
{0x5fde,0x5fde,0x2105},
{0x5fe0,0x5fe0,0xba7},
{0x5fe1,0x5fe1,0x38c4},
{0x5fe4,0x5fe4,0x12c3},
{0x5fe9,0x5fe9,0x38c5},
{0x5feb,0x5feb,0x577},
{0x5fed,0x5fed,0x38c6},
{0x5ff0,0x5ff0,0x12f6},
{0x5ff1,0x5ff1,0x12c5},
{0x5ff5,0x5ff5,0xce6},
{0x5ff8,0x5ff8,0x12c4},
{0x5ffb,0x5ffb,0x12c2},
{0x5ffc,0x5ffc,0x38c7},
{0x5ffd,0x5ffd,0x80c},
{0x5fff,0x5fff,0x12c8},
{0x600e,0x600e,0x12ce},
{0x600f,0x600f,0x12d4},
{0x6010,0x6010,0x12cc},
{0x6012,0x6012,0xc56},
{0x6015,0x6015,0x12d1},
{0x6016,0x6016,0xdcf},
{0x6017,0x6017,0x38c8},
{0x6019,0x6019,0x12cb},
{0x601a,0x601a,0x38c9},
{0x601b,0x601b,0x12d0},
{0x601c,0x601c,0xfaf},
{0x601d,0x601d,0x8a5},
{0x6020,0x6020,0xb35},
{0x6021,0x6021,0x12c9},
{0x6025,0x6025,0x678},
{0x6026,0x6026,0x12d3},
{0x6027,0x6027,0xa51},
{0x6028,0x6028,0x507},
{0x6029,0x6029,0x12cd},
{0x602a,0x602a,0x578},
{0x602b,0x602b,0x12d2},
{0x602f,0x602f,0x6a9},
{0x6031,0x6031,0x12cf},
{0x6033,0x6033,0x38ca},
{0x603a,0x603a,0x12d5},
{0x6041,0x6041,0x12d7},
{0x6042,0x6042,0x12e1},
{0x6043,0x6043,0x12df},
{0x6046,0x6046,0x12dc},
{0x604a,0x604a,0x12db},
{0x604b,0x604b,0xfc0},
{0x604d,0x604d,0x12dd},
{0x6050,0x6050,0x6aa},
{0x6052,0x6052,0x7c3},
{0x6055,0x6055,0x984},
{0x6059,0x6059,0x12e4},
{0x605a,0x605a,0x12d6},
{0x605d,0x605d,0x2106},
{0x605f,0x605f,0x12da},
{0x6060,0x6060,0x12ca},
{0x6061,0x6061,0x38cb},
{0x6062,0x6062,0x57a},
{0x6063,0x6063,0x12de},
{0x6064,0x6064,0x12e0},
{0x6065,0x6065,0xb8f},
{0x6068,0x6068,0x818},
{0x6069,0x6069,0x538},
{0x606a,0x606a,0x12d8},
{0x606b,0x606b,0x12e3},
{0x606c,0x606c,0x12e2},
{0x606d,0x606d,0x6ab},
{0x606f,0x606f,0xb09},
{0x6070,0x6070,0x5c4},
{0x6075,0x6075,0x719},
{0x6077,0x6077,0x12d9},
{0x607f,0x607f,0x38cc},
{0x6081,0x6081,0x12e5},
{0x6083,0x6083,0x12e8},
{0x6084,0x6084,0x12ea},
{0x6085,0x6085,0x2107},
{0x6089,0x6089,0x8e9},
{0x608a,0x608a,0x2108},
{0x608b,0x608b,0x12f0},
{0x608c,0x608c,0xc0c},
{0x608d,0x608d,0x12e6},
{0x6092,0x6092,0x12ee},
{0x6094,0x6094,0x579},
{0x6096,0x6097,0x12ec},
{0x609a,0x609a,0x12e9},
{0x609b,0x609b,0x12eb},
{0x609e,0x609e,0x38cd},
{0x609f,0x609f,0x79b},
{0x60a0,0x60a0,0xf14},
{0x60a3,0x60a3,0x5f1},
{0x60a4,0x60a4,0x38ce},
{0x60a6,0x60a6,0x4fb},
{0x60a7,0x60a7,0x12ef},
{0x60a9,0x60a9,0xcf0},
{0x60aa,0x60aa,0x471},
{0x60b0,0x60b0,0x38cf},
{0x60b2,0x60b2,0xd75},
{0x60b3,0x60b3,0x12c7},
{0x60b4,0x60b4,0x12f5},
{0x60b5,0x60b5,0x12f9},
{0x60b6,0x60b6,0xef1},
{0x60b8,0x60b8,0x12f2},
{0x60bc,0x60bc,0xc63},
{0x60bd,0x60bd,0x12f7},
{0x60c5,0x60c5,0x9d8},
{0x60c6,0x60c6,0x12f8},
{0x60c7,0x60c7,0xcaf},
{0x60cb,0x60cb,0x38d0},
{0x60d1,0x60d1,0xfed},
{0x60d3,0x60d3,0x12f4},
{0x60d5,0x60d5,0x210a},
{0x60d8,0x60d8,0x12fa},
{0x60da,0x60da,0x80d},
{0x60db,0x60db,0x38d1},
{0x60dc,0x60dc,0xa6f},
{0x60de,0x60de,0x2109},
{0x60df,0x60df,0x498},
{0x60e0,0x60e0,0x12f3},
{0x60e1,0x60e1,0x12f1},
{0x60e3,0x60e3,0xadc},
{0x60e7,0x60e7,0x12e7},
{0x60e8,0x60e8,0x882},
{0x60f0,0x60f0,0xb26},
{0x60f1,0x60f1,0x1306},
{0x60f2,0x60f2,0x210c},
{0x60f3,0x60f3,0xadd},
{0x60f4,0x60f4,0x1301},
{0x60f6,0x60f7,0x12fe},
{0x60f8,0x60f8,0x38d2},
{0x60f9,0x60f9,0x912},
{0x60fa,0x60fa,0x1302},
{0x60fb,0x60fb,0x1305},
{0x6100,0x6100,0x1300},
{0x6101,0x6101,0x92f},
{0x6103,0x6103,0x1303},
{0x6106,0x6106,0x12fd},
{0x6108,0x6108,0xf08},
{0x6109,0x6109,0xf07},
{0x610d,0x610e,0x1307},
{0x610f,0x610f,0x499},
{0x6111,0x6111,0x210d},
{0x6112,0x6114,0x38d3},
{0x6115,0x6115,0x12fc},
{0x611a,0x611a,0x6ea},
{0x611b,0x611b,0x46a},
{0x611c,0x611c,0x38d6},
{0x611f,0x611f,0x5f2},
{0x6120,0x6120,0x210b},
{0x6121,0x6121,0x1304},
{0x6127,0x6127,0x130c},
{0x6128,0x6128,0x130b},
{0x612c,0x612c,0x1310},
{0x6130,0x6130,0x210f},
{0x6134,0x6134,0x1311},
{0x6137,0x6137,0x210e},
{0x613c,0x613c,0x130f},
{0x613d,0x613d,0x1312},
{0x613e,0x613e,0x130a},
{0x613f,0x613f,0x130e},
{0x6142,0x6142,0x1313},
{0x6144,0x6144,0x1314},
{0x6147,0x6147,0x1309},
{0x6148,0x6148,0x8ca},
{0x614a,0x614a,0x130d},
{0x614b,0x614b,0xb36},
{0x614c,0x614c,0x7c4},
{0x614d,0x614d,0x12fb},
{0x614e,0x614e,0x9fb},
{0x6153,0x6153,0x1321},
{0x6155,0x6155,0xe39},
{0x6158,0x615a,0x1317},
{0x615d,0x615d,0x1320},
{0x615f,0x615f,0x131f},
{0x6162,0x6162,0xeab},
{0x6163,0x6163,0x5f3},
{0x6165,0x6165,0x131d},
{0x6167,0x6167,0x71b},
{0x6168,0x6168,0x592},
{0x616b,0x616b,0x131a},
{0x616e,0x616e,0xf80},
{0x616f,0x616f,0x131c},
{0x6170,0x6170,0x49a},
{0x6171,0x6171,0x131e},
{0x6173,0x6173,0x1315},
{0x6174,0x6174,0x131b},
{0x6175,0x6175,0x1322},
{0x6176,0x6176,0x71a},
{0x6177,0x6177,0x1316},
{0x617c,0x617c,0x38d7},
{0x617e,0x617e,0xf47},
{0x6182,0x6182,0xf15},
{0x6187,0x6187,0x1325},
{0x618a,0x618a,0x1329},
{0x618d,0x618d,0x38d8},
{0x618e,0x618e,0xb00},
{0x6190,0x6190,0xfc1},
{0x6191,0x6191,0x132a},
{0x6194,0x6194,0x1327},
{0x6196,0x6196,0x1324},
{0x6198,0x6198,0x2110},
{0x6199,0x6199,0x1323},
{0x619a,0x619a,0x1328},
{0x619f,0x619f,0x38d9},
{0x61a4,0x61a4,0xe00},
{0x61a7,0x61a7,0xc8c},
{0x61a8,0x61a8,0x38da},
{0x61a9,0x61a9,0x71c},
{0x61ab,0x61ab,0x132b},
{0x61ac,0x61ac,0x1326},
{0x61ae,0x61ae,0x132c},
{0x61b2,0x61b2,0x751},
{0x61b6,0x61b6,0x531},
{0x61ba,0x61ba,0x1334},
{0x61be,0x61be,0x5f4},
{0x61c2,0x61c2,0x38db},
{0x61c3,0x61c3,0x1332},
{0x61c6,0x61c6,0x1333},
{0x61c7,0x61c7,0x819},
{0x61c8,0x61c8,0x1331},
{0x61c9,0x61c9,0x132f},
{0x61ca,0x61ca,0x132e},
{0x61cb,0x61cb,0x1335},
{0x61cc,0x61cc,0x132d},
{0x61cd,0x61cd,0x1337},
{0x61d0,0x61d0,0x57b},
{0x61df,0x61df,0x38dc},
{0x61e3,0x61e3,0x1339},
{0x61e6,0x61e6,0x1338},
{0x61f2,0x61f2,0xbc4},
{0x61f4,0x61f4,0x133c},
{0x61f6,0x61f6,0x133a},
{0x61f7,0x61f7,0x1330},
{0x61f8,0x61f8,0x752},
{0x61fa,0x61fa,0x133b},
{0x61fc,0x61fc,0x133f},
{0x61fd,0x61fd,0x133e},
{0x61fe,0x61fe,0x1340},
{0x61ff,0x61ff,0x133d},
{0x6200,0x6200,0x1341},
{0x6208,0x6209,0x1342},
{0x620a,0x620a,0xe3a},
{0x620c,0x620c,0x1345},
{0x620d,0x620d,0x1344},
{0x620e,0x620e,0x949},
{0x6210,0x6210,0xa52},
{0x6211,0x6211,0x566},
{0x6212,0x6212,0x57c},
{0x6213,0x6213,0x2111},
{0x6214,0x6214,0x1346},
{0x6215,0x6215,0x38dd},
{0x6216,0x6216,0x483},
{0x621a,0x621a,0xa70},
{0x621b,0x621b,0x1347},
{0x621d,0x621d,0x1a64},
{0x621e,0x621e,0x1348},
{0x621f,0x621f,0x737},
{0x6221,0x6221,0x1349},
{0x6226,0x6226,0xa93},
{0x6229,0x6229,0x38de},
{0x622a,0x622a,0x134a},
{0x622e,0x622e,0x134b},
{0x622f,0x622f,0x654},
{0x6230,0x6230,0x134c},
{0x6232,0x6233,0x134d},
{0x6234,0x6234,0xb37},
{0x6236,0x6236,0x35bd},
{0x6238,0x6238,0x781},
{0x623b,0x623b,0xeed},
{0x623e,0x623e,0x344e},
{0x623f,0x623f,0xe6a},
{0x6240,0x6240,0x974},
{0x6241,0x6241,0x134f},
{0x6243,0x6243,0x38df},
{0x6246,0x6246,0x38e0},
{0x6247,0x6247,0xa94},
{0x6248,0x6248,0x1b1a},
{0x6249,0x6249,0xd76},
{0x624b,0x624b,0x916},
{0x624c,0x624c,0x38e1},
{0x624d,0x624d,0x83d},
{0x624e,0x624e,0x1350},
{0x6251,0x6251,0x38e2},
{0x6253,0x6253,0xb27},
{0x6255,0x6255,0xdf7},
{0x6256,0x6256,0x38e3},
{0x6258,0x6258,0xb51},
{0x625b,0x625b,0x1353},
{0x625e,0x625e,0x1351},
{0x6260,0x6260,0x1354},
{0x6263,0x6263,0x1352},
{0x6268,0x6268,0x1355},
{0x626e,0x626e,0xe01},
{0x6271,0x6271,0x47b},
{0x6276,0x6276,0xdd0},
{0x6279,0x6279,0xd77},
{0x627c,0x627c,0x1356},
{0x627e,0x627e,0x1359},
{0x627f,0x627f,0x99d},
{0x6280,0x6280,0x655},
{0x6282,0x6282,0x1357},
{0x6283,0x6283,0x135e},
{0x6284,0x6284,0x99e},
{0x6285,0x6285,0x35c5},
{0x6289,0x6289,0x1358},
{0x628a,0x628a,0xcfa},
{0x6291,0x6291,0xf48},
{0x6292,0x6293,0x135a},
{0x6294,0x6294,0x135f},
{0x6295,0x6295,0xc64},
{0x6296,0x6296,0x135c},
{0x6297,0x6297,0x7c5},
{0x6298,0x6298,0xa82},
{0x629b,0x629b,0x136d},
{0x629c,0x629c,0xd48},
{0x629e,0x629e,0xb52},
{0x62a6,0x62a6,0x2112},
{0x62ab,0x62ab,0xd78},
{0x62ac,0x62ac,0x13b2},
{0x62b1,0x62b1,0xe4a},
{0x62b5,0x62b5,0xc0d},
{0x62b9,0x62b9,0xea3},
{0x62bb,0x62bb,0x1362},
{0x62bc,0x62bc,0x521},
{0x62bd,0x62bd,0xba8},
{0x62c2,0x62c2,0x136b},
{0x62c4,0x62c4,0x38e4},
{0x62c5,0x62c5,0xb72},
{0x62c6,0x62c6,0x1365},
{0x62c7,0x62c7,0x136c},
{0x62c8,0x62c8,0x1367},
{0x62c9,0x62c9,0x136e},
{0x62ca,0x62ca,0x136a},
{0x62cc,0x62cc,0x1369},
{0x62cd,0x62cd,0xd25},
{0x62cf,0x62cf,0x1363},
{0x62d0,0x62d0,0x57d},
{0x62d1,0x62d1,0x1361},
{0x62d2,0x62d2,0x68b},
{0x62d3,0x62d3,0xb53},
{0x62d4,0x62d4,0x135d},
{0x62d7,0x62d7,0x1360},
{0x62d8,0x62d8,0x7c6},
{0x62d9,0x62d9,0xa7f},
{0x62db,0x62db,0x99f},
{0x62dc,0x62dc,0x1368},
{0x62dd,0x62dd,0xd08},
{0x62e0,0x62e0,0x68c},
{0x62e1,0x62e1,0x5a6},
{0x62ec,0x62ec,0x5c5},
{0x62ed,0x62ed,0x9e7},
{0x62ee,0x62ee,0x1370},
{0x62ef,0x62ef,0x1375},
{0x62f1,0x62f1,0x1371},
{0x62f3,0x62f3,0x753},
{0x62f5,0x62f5,0x1376},
{0x62f6,0x62f6,0x870},
{0x62f7,0x62f7,0x7fb},
{0x62fc,0x62fc,0x38e5},
{0x62fe,0x62fe,0x930},
{0x62ff,0x62ff,0x1364},
{0x6301,0x6301,0x8cb},
{0x6302,0x6302,0x1373},
{0x6307,0x6307,0x8a6},
{0x6308,0x6308,0x1374},
{0x6309,0x6309,0x488},
{0x630a,0x630a,0x38e6},
{0x630c,0x630c,0x136f},
{0x630d,0x630d,0x38e7},
{0x6311,0x6311,0xbc5},
{0x6318,0x6318,0x38e8},
{0x6319,0x6319,0x68d},
{0x631b,0x631b,0x3737},
{0x631f,0x631f,0x6ac},
{0x6327,0x6327,0x1372},
{0x6328,0x6328,0x46b},
{0x632b,0x632b,0x833},
{0x632f,0x632f,0x9fc},
{0x6339,0x6339,0x38e9},
{0x633a,0x633a,0xc0e},
{0x633d,0x633d,0xd68},
{0x633e,0x633e,0x1378},
{0x633f,0x633f,0xae0},
{0x6342,0x6343,0x38ea},
{0x6349,0x6349,0xb0a},
{0x634c,0x634c,0x879},
{0x634d,0x634d,0x1379},
{0x634f,0x634f,0x137b},
{0x6350,0x6350,0x1377},
{0x6355,0x6355,0xe31},
{0x6357,0x6357,0xbd9},
{0x635c,0x635c,0xade},
{0x6365,0x6365,0x38ec},
{0x6367,0x6367,0xe4b},
{0x6368,0x6368,0x8fa},
{0x6369,0x6369,0x1387},
{0x636b,0x636b,0x1386},
{0x636e,0x636e,0xa3e},
{0x6372,0x6372,0x754},
{0x6374,0x6374,0x38ed},
{0x6376,0x6376,0x1380},
{0x6377,0x6377,0x9a1},
{0x637a,0x637a,0xcc0},
{0x637b,0x637b,0xce7},
{0x637d,0x637d,0x38ee},
{0x6380,0x6380,0x137e},
{0x6383,0x6383,0xadf},
{0x6384,0x6384,0x38ef},
{0x6387,0x6387,0x38f0},
{0x6388,0x6388,0x924},
{0x6389,0x6389,0x1383},
{0x638c,0x638c,0x9a0},
{0x638e,0x638e,0x137d},
{0x638f,0x638f,0x1382},
{0x6390,0x6390,0x38f1},
{0x6392,0x6392,0xd09},
{0x6396,0x6396,0x137c},
{0x6398,0x6398,0x6f7},
{0x639b,0x639b,0x5bb},
{0x639e,0x639e,0x38f2},
{0x639f,0x639f,0x1384},
{0x63a0,0x63a0,0xf73},
{0x63a1,0x63a1,0x83e},
{0x63a2,0x63a2,0xb73},
{0x63a3,0x63a3,0x1381},
{0x63a5,0x63a5,0xa80},
{0x63a7,0x63a7,0x7c7},
{0x63a8,0x63a8,0xa2a},
{0x63a9,0x63a9,0x508},
{0x63aa,0x63aa,0xabe},
{0x63ab,0x63ab,0x137f},
{0x63ac,0x63ac,0x65f},
{0x63b2,0x63b2,0x71d},
{0x63b4,0x63b4,0xbeb},
{0x63b5,0x63b5,0x1385},
{0x63bb,0x63bb,0xae1},
{0x63be,0x63be,0x1388},
{0x63c0,0x63c0,0x138a},
{0x63c3,0x63c3,0xb17},
{0x63c4,0x63c4,0x1390},
{0x63c6,0x63c6,0x138b},
{0x63c9,0x63c9,0x138d},
{0x63cf,0x63cf,0xdb3},
{0x63d0,0x63d0,0xc0f},
{0x63d1,0x63d1,0x38f3},
{0x63d2,0x63d2,0x138e},
{0x63d6,0x63d6,0xf16},
{0x63da,0x63da,0xf32},
{0x63db,0x63db,0x5f5},
{0x63dc,0x63dc,0x38f4},
{0x63e1,0x63e1,0x472},
{0x63e3,0x63e3,0x138c},
{0x63e9,0x63e9,0x1389},
{0x63ed,0x63ed,0x341c},
{0x63ee,0x63ee,0x634},
{0x63f4,0x63f4,0x509},
{0x63f5,0x63f5,0x2113},
{0x63f6,0x63f6,0x138f},
{0x63f7,0x63f7,0x3644},
{0x63fa,0x63fa,0xf33},
{0x6406,0x6406,0x1393},
{0x6409,0x6409,0x38f5},
{0x640d,0x640d,0xb1b},
{0x640f,0x640f,0x139a},
{0x6410,0x6410,0x38f6},
{0x6413,0x6413,0x1394},
{0x6414,0x6414,0x1e2c},
{0x6416,0x6416,0x1391},
{0x6417,0x6417,0x1398},
{0x641c,0x641c,0x137a},
{0x6422,0x6422,0x38f7},
{0x6426,0x6426,0x1395},
{0x6428,0x6428,0x1399},
{0x642c,0x642c,0xd56},
{0x642d,0x642d,0xc65},
{0x6434,0x6434,0x1392},
{0x6436,0x6436,0x1396},
{0x643a,0x643a,0x71e},
{0x643e,0x643e,0x861},
{0x6442,0x6442,0xa81},
{0x644e,0x644e,0x139e},
{0x6451,0x6451,0x1e43},
{0x6454,0x6454,0x38f8},
{0x6458,0x6458,0xc20},
{0x645b,0x645b,0x38f9},
{0x6460,0x6460,0x2114},
{0x6467,0x6467,0x139b},
{0x6469,0x6469,0xe8e},
{0x646d,0x646d,0x38fa},
{0x646f,0x646f,0x139c},
{0x6476,0x6476,0x139d},
{0x6478,0x6478,0xeda},
{0x647a,0x647a,0xa46},
{0x647b,0x647b,0x38fb},
{0x6483,0x6483,0x738},
{0x6488,0x6488,0x13a4},
{0x6492,0x6492,0x883},
{0x6493,0x6493,0x13a1},
{0x6495,0x6495,0x13a0},
{0x649a,0x649a,0xce8},
{0x649d,0x649d,0x2115},
{0x649e,0x649e,0xc8d},
{0x64a4,0x64a4,0xc2b},
{0x64a5,0x64a5,0x13a2},
{0x64a9,0x64a9,0x13a3},
{0x64ab,0x64ab,0xde1},
{0x64ad,0x64ad,0xcfb},
{0x64ae,0x64ae,0x871},
{0x64b0,0x64b0,0xa95},
{0x64b2,0x64b2,0xe7e},
{0x64b9,0x64b9,0x5a7},
{0x64bb,0x64bb,0x13aa},
{0x64bc,0x64bc,0x13a5},
{0x64be,0x64bf,0x38fc},
{0x64c1,0x64c1,0xf34},
{0x64c2,0x64c2,0x13ac},
{0x64c5,0x64c5,0x13a8},
{0x64c7,0x64c7,0x13a9},
{0x64ca,0x64ca,0x341d},
{0x64cd,0x64cd,0xae2},
{0x64ce,0x64ce,0x2116},
{0x64d2,0x64d2,0x13a7},
{0x64d4,0x64d4,0x1366},
{0x64d8,0x64d8,0x13ab},
{0x64da,0x64da,0x13a6},
{0x64e0,0x64e1,0x13b0},
{0x64e2,0x64e2,0xc21},
{0x64e3,0x64e3,0x13b3},
{0x64e5,0x64e5,0x38fe},
{0x64e6,0x64e6,0x872},
{0x64e7,0x64e7,0x13ae},
{0x64ec,0x64ec,0x656},
{0x64ef,0x64ef,0x13b4},
{0x64f1,0x64f1,0x13ad},
{0x64f2,0x64f2,0x13b8},
{0x64f4,0x64f4,0x13b7},
{0x64f6,0x64f6,0x13b6},
{0x64f7,0x64f7,0x38ff},
{0x64fa,0x64fa,0x13b9},
{0x64fb,0x64fb,0x3900},
{0x64fd,0x64fd,0x13bb},
{0x64fe,0x64fe,0x9d9},
{0x6500,0x6500,0x13ba},
{0x6504,0x6504,0x3901},
{0x6505,0x6505,0x13be},
{0x6516,0x6516,0x3902},
{0x6518,0x6518,0x13bc},
{0x6519,0x6519,0x3903},
{0x651c,0x651c,0x13bd},
{0x651d,0x651d,0x1397},
{0x6522,0x6522,0x1e97},
{0x6523,0x6523,0x13c0},
{0x6524,0x6524,0x13bf},
{0x652a,0x652a,0x139f},
{0x652b,0x652b,0x13c1},
{0x652c,0x652c,0x13b5},
{0x652f,0x652f,0x8a7},
{0x6534,0x6535,0x13c2},
{0x6536,0x6536,0x13c5},
{0x6537,0x6537,0x13c4},
{0x6538,0x6538,0x13c6},
{0x6539,0x6539,0x57e},
{0x653b,0x653b,0x7c8},
{0x653e,0x653e,0xe4c},
{0x653f,0x653f,0xa53},
{0x6545,0x6545,0x782},
{0x6547,0x6547,0x3904},
{0x6548,0x6548,0x13c8},
{0x654d,0x654d,0x13cb},
{0x654e,0x654e,0x2117},
{0x654f,0x654f,0xdc4},
{0x6551,0x6551,0x679},
{0x6555,0x6555,0x13ca},
{0x6556,0x6556,0x13c9},
{0x6557,0x6557,0xd0a},
{0x6558,0x6558,0x13cc},
{0x6559,0x6559,0x6ad},
{0x655d,0x655d,0x13ce},
{0x655e,0x655e,0x13cd},
{0x6562,0x6562,0x5f6},
{0x6563,0x6563,0x884},
{0x6566,0x6566,0xcb0},
{0x6567,0x6567,0x3905},
{0x656c,0x656c,0x71f},
{0x6570,0x6570,0xa3a},
{0x6572,0x6572,0x13cf},
{0x6574,0x6574,0xa54},
{0x6575,0x6575,0xc22},
{0x6577,0x6577,0xdd1},
{0x6578,0x6578,0x13d0},
{0x6581,0x6581,0x3906},
{0x6582,0x6583,0x13d1},
{0x6585,0x6585,0x3907},
{0x6587,0x6587,0xe08},
{0x6588,0x6588,0x120c},
{0x6589,0x6589,0xa6a},
{0x658c,0x658c,0xdbe},
{0x658e,0x658e,0x848},
{0x6590,0x6590,0xd79},
{0x6591,0x6591,0xd57},
{0x6597,0x6597,0xc47},
{0x6599,0x6599,0xf89},
{0x659b,0x659b,0x13d4},
{0x659c,0x659c,0x8fc},
{0x659f,0x659f,0x13d5},
{0x65a1,0x65a1,0x47a},
{0x65a4,0x65a4,0x6cc},
{0x65a5,0x65a5,0xa71},
{0x65a7,0x65a7,0xdd2},
{0x65ab,0x65ab,0x13d6},
{0x65ac,0x65ac,0x890},
{0x65ad,0x65ad,0xb85},
{0x65af,0x65af,0x8a9},
{0x65b0,0x65b0,0x9fd},
{0x65b7,0x65b7,0x13d7},
{0x65b9,0x65b9,0xe4d},
{0x65bc,0x65bc,0x519},
{0x65bd,0x65bd,0x8aa},
{0x65c1,0x65c1,0x13da},
{0x65c2,0x65c2,0x3908},
{0x65c3,0x65c3,0x13d8},
{0x65c4,0x65c4,0x13db},
{0x65c5,0x65c5,0xf81},
{0x65c6,0x65c6,0x13d9},
{0x65cb,0x65cb,0xa9f},
{0x65cc,0x65cc,0x13dc},
{0x65cf,0x65cf,0xb12},
{0x65d2,0x65d2,0x13dd},
{0x65d7,0x65d7,0x636},
{0x65d9,0x65d9,0x13df},
{0x65db,0x65db,0x13de},
{0x65e0,0x65e1,0x13e0},
{0x65e2,0x65e2,0x637},
{0x65e3,0x65e3,0x3585},
{0x65e5,0x65e5,0xcd4},
{0x65e6,0x65e6,0xb74},
{0x65e7,0x65e7,0x686},
{0x65e8,0x65e8,0x8ab},
{0x65e9,0x65e9,0xae3},
{0x65ec,0x65ec,0x966},
{0x65ed,0x65ed,0x474},
{0x65f0,0x65f0,0x3909},
{0x65f1,0x65f1,0x13e2},
{0x65f2,0x65f2,0x390a},
{0x65fa,0x65fa,0x522},
{0x65fb,0x65fb,0x13e6},
{0x6600,0x6600,0x2118},
{0x6602,0x6602,0x7c9},
{0x6603,0x6603,0x13e5},
{0x6606,0x6606,0x81b},
{0x6607,0x6607,0x9a2},
{0x6609,0x6609,0x211a},
{0x660a,0x660a,0x13e4},
{0x660c,0x660c,0x9a3},
{0x660e,0x660e,0xecc},
{0x660f,0x660f,0x81a},
{0x6613,0x6613,0x49b},
{0x6614,0x6614,0xa72},
{0x6615,0x6615,0x2119},
{0x661c,0x661c,0x13eb},
{0x661f,0x661f,0xa55},
{0x6620,0x6620,0x4e9},
{0x6624,0x6624,0x211d},
{0x6625,0x6625,0x95f},
{0x6627,0x6627,0xe94},
{0x6628,0x6628,0x862},
{0x662c,0x662c,0x390b},
{0x662d,0x662d,0x9a4},
{0x662e,0x662e,0x211b},
{0x662f,0x662f,0xa4b},
{0x6631,0x6631,0x20ae},
{0x6634,0x6634,0x13ea},
{0x6635,0x6636,0x13e8},
{0x663b,0x663b,0x1e00},
{0x663c,0x663c,0xba9},
{0x663f,0x663f,0x1409},
{0x6641,0x6641,0x13ef},
{0x6642,0x6642,0x8cc},
{0x6643,0x6643,0x7ca},
{0x6644,0x6644,0x13ed},
{0x6649,0x6649,0x13ee},
{0x664b,0x664b,0x9fe},
{0x664c,0x664c,0x390c},
{0x664f,0x664f,0x13ec},
{0x6652,0x6652,0x87d},
{0x6657,0x6657,0x211f},
{0x6659,0x6659,0x2120},
{0x665b,0x665c,0x390d},
{0x665d,0x665d,0x13f1},
{0x665e,0x665e,0x13f0},
{0x665f,0x665f,0x13f5},
{0x6661,0x6661,0x390f},
{0x6662,0x6662,0x13f6},
{0x6663,0x6663,0x373a},
{0x6664,0x6664,0x13f2},
{0x6665,0x6665,0x211e},
{0x6666,0x6666,0x580},
{0x6667,0x6668,0x13f3},
{0x6669,0x6669,0xd69},
{0x666b,0x666b,0x3910},
{0x666e,0x666e,0xdd3},
{0x666f,0x666f,0x720},
{0x6670,0x6670,0x13f7},
{0x6673,0x6673,0x2122},
{0x6674,0x6674,0xa56},
{0x6676,0x6676,0x9a5},
{0x667a,0x667a,0xb90},
{0x6681,0x6681,0x6bf},
{0x6683,0x6683,0x13f8},
{0x6684,0x6684,0x13fc},
{0x6687,0x6687,0x54b},
{0x6688,0x6688,0x13f9},
{0x6689,0x6689,0x13fb},
{0x668e,0x668e,0x13fa},
{0x6691,0x6691,0x975},
{0x6696,0x6696,0xb86},
{0x6697,0x6697,0x489},
{0x6698,0x6698,0x13fd},
{0x6699,0x6699,0x2123},
{0x669d,0x669d,0x13fe},
{0x66a0,0x66a0,0x2124},
{0x66a2,0x66a2,0xbc6},
{0x66a4,0x66a4,0x3912},
{0x66a6,0x66a6,0xfb9},
{0x66ab,0x66ab,0x891},
{0x66ae,0x66ae,0xe3b},
{0x66b2,0x66b2,0x2125},
{0x66b4,0x66b4,0xe6b},
{0x66b8,0x66b8,0x1405},
{0x66b9,0x66b9,0x1400},
{0x66bc,0x66bc,0x1403},
{0x66be,0x66be,0x1402},
{0x66bf,0x66bf,0x2126},
{0x66c1,0x66c1,0x13ff},
{0x66c4,0x66c4,0x1404},
{0x66c6,0x66c6,0x3455},
{0x66c7,0x66c7,0xcb6},
{0x66c8,0x66c8,0x3913},
{0x66c9,0x66c9,0x1401},
{0x66d6,0x66d6,0x1406},
{0x66d9,0x66d9,0x976},
{0x66da,0x66da,0x1407},
{0x66dc,0x66dc,0xf35},
{0x66dd,0x66dd,0xd2e},
{0x66e0,0x66e0,0x1408},
{0x66e6,0x66e6,0x140a},
{0x66e9,0x66e9,0x140b},
{0x66ec,0x66ec,0x3914},
{0x66f0,0x66f0,0x140c},
{0x66f2,0x66f2,0x6c2},
{0x66f3,0x66f3,0x4ea},
{0x66f4,0x66f4,0x7cb},
{0x66f5,0x66f5,0x140d},
{0x66f7,0x66f7,0x140e},
{0x66f8,0x66f8,0x97b},
{0x66f9,0x66f9,0xae4},
{0x66fa,0x66fa,0x2127},
{0x66fb,0x66fb,0x20b1},
{0x66fc,0x66fc,0x10ed},
{0x66fd,0x66fd,0xac0},
{0x66fe,0x66fe,0xabf},
{0x66ff,0x66ff,0xb38},
{0x6700,0x6700,0x837},
{0x6703,0x6703,0x104b},
{0x6705,0x6705,0x3915},
{0x6708,0x6708,0x744},
{0x6709,0x6709,0xf17},
{0x670b,0x670b,0xe4e},
{0x670d,0x670d,0xdf0},
{0x670e,0x670e,0x2128},
{0x670f,0x670f,0x140f},
{0x6713,0x6713,0x3916},
{0x6714,0x6714,0x863},
{0x6715,0x6715,0xbdb},
{0x6716,0x6716,0x1410},
{0x6717,0x6717,0xfd5},
{0x671b,0x671b,0xe6c},
{0x671d,0x671d,0xbc7},
{0x671e,0x671e,0x1411},
{0x671f,0x671f,0x638},
{0x6726,0x6727,0x1412},
{0x6728,0x6728,0xee6},
{0x672a,0x672a,0xeb0},
{0x672b,0x672b,0xea4},
{0x672c,0x672c,0xe8a},
{0x672d,0x672d,0x873},
{0x672e,0x672e,0x1415},
{0x6731,0x6731,0x917},
{0x6733,0x6733,0x3917},
{0x6734,0x6734,0xe7f},
{0x6736,0x6736,0x1417},
{0x6737,0x6737,0x141a},
{0x6738,0x6738,0x1419},
{0x673a,0x673a,0x635},
{0x673d,0x673d,0x67a},
{0x673f,0x673f,0x1416},
{0x6741,0x6741,0x1418},
{0x6743,0x6743,0x35b7},
{0x6746,0x6746,0x141b},
{0x6748,0x6748,0x3918},
{0x6749,0x6749,0xa3f},
{0x674c,0x674c,0x3919},
{0x674e,0x674e,0xf65},
{0x674f,0x674f,0x48d},
{0x6750,0x6750,0x850},
{0x6751,0x6751,0xb1c},
{0x6753,0x6753,0x909},
{0x6756,0x6756,0x9db},
{0x6759,0x6759,0x141e},
{0x675c,0x675c,0xc48},
{0x675e,0x675e,0x141c},
{0x675f,0x675f,0xb0b},
{0x6760,0x6760,0x141d},
{0x6761,0x6761,0x9da},
{0x6762,0x6762,0xee9},
{0x6763,0x6764,0x141f},
{0x6765,0x6765,0xf52},
{0x6766,0x6766,0x212a},
{0x676a,0x676a,0x1425},
{0x676d,0x676d,0x7cc},
{0x676e,0x676e,0x3571},
{0x676f,0x676f,0xd0b},
{0x6770,0x6770,0x1422},
{0x6771,0x6771,0xc66},
{0x6772,0x6772,0x13e3},
{0x6773,0x6773,0x13e7},
{0x6775,0x6775,0x669},
{0x6776,0x6776,0x391a},
{0x6777,0x6777,0xcfd},
{0x677b,0x677b,0x391b},
{0x677c,0x677c,0x1424},
{0x677e,0x677e,0x9a6},
{0x677f,0x677f,0xd58},
{0x6785,0x6785,0x142a},
{0x6787,0x6787,0xd8e},
{0x6789,0x6789,0x1421},
{0x678b,0x678b,0x1427},
{0x678c,0x678c,0x1426},
{0x6790,0x6790,0xa73},
{0x6795,0x6795,0xe9b},
{0x6797,0x6797,0xf9b},
{0x679a,0x679a,0xe95},
{0x679c,0x679c,0x54c},
{0x679d,0x679d,0x8ac},
{0x67a0,0x67a0,0xfee},
{0x67a1,0x67a1,0x1429},
{0x67a2,0x67a2,0xa3b},
{0x67a6,0x67a6,0x1428},
{0x67a9,0x67a9,0x1423},
{0x67af,0x67af,0x783},
{0x67b0,0x67b0,0x391c},
{0x67b2,0x67b2,0x391d},
{0x67b3,0x67b3,0x142f},
{0x67b4,0x67b4,0x142d},
{0x67b6,0x67b6,0x54d},
{0x67b7,0x67b7,0x142b},
{0x67b8,0x67b8,0x1431},
{0x67b9,0x67b9,0x1437},
{0x67bb,0x67bb,0x212b},
{0x67c0,0x67c0,0x212d},
{0x67c1,0x67c1,0xb28},
{0x67c4,0x67c4,0xe11},
{0x67c6,0x67c6,0x1439},
{0x67ca,0x67ca,0xd94},
{0x67ce,0x67ce,0x1438},
{0x67cf,0x67cf,0xd26},
{0x67d0,0x67d0,0xe6d},
{0x67d1,0x67d1,0x5f7},
{0x67d3,0x67d3,0xa9b},
{0x67d4,0x67d4,0x94a},
{0x67d7,0x67d7,0x391f},
{0x67d8,0x67d8,0xbef},
{0x67d9,0x67d9,0x3920},
{0x67da,0x67da,0xf18},
{0x67dd,0x67dd,0x1434},
{0x67de,0x67de,0x1433},
{0x67e2,0x67e2,0x1435},
{0x67e4,0x67e4,0x1432},
{0x67e7,0x67e7,0x143a},
{0x67e9,0x67e9,0x1430},
{0x67ec,0x67ec,0x142e},
{0x67ee,0x67ee,0x1436},
{0x67ef,0x67ef,0x142c},
{0x67f0,0x67f0,0x3921},
{0x67f1,0x67f1,0xbaa},
{0x67f3,0x67f3,0xf04},
{0x67f4,0x67f4,0x8f2},
{0x67f5,0x67f5,0x864},
{0x67f9,0x67f9,0x391e},
{0x67fb,0x67fb,0x82a},
{0x67fe,0x67fe,0xe9d},
{0x67ff,0x67ff,0x59f},
{0x6802,0x6802,0xbea},
{0x6803,0x6803,0xca2},
{0x6804,0x6804,0x4eb},
{0x6805,0x6805,0x1e07},
{0x6813,0x6813,0xa96},
{0x6816,0x6816,0xa58},
{0x6817,0x6817,0x700},
{0x681e,0x681e,0x143c},
{0x6821,0x6821,0x7cd},
{0x6822,0x6822,0x5da},
{0x6829,0x6829,0x143e},
{0x682a,0x682a,0x5d2},
{0x682b,0x682b,0x1444},
{0x682c,0x682c,0x3922},
{0x6830,0x6831,0x3923},
{0x6832,0x6832,0x1441},
{0x6834,0x6834,0xa97},
{0x6838,0x6838,0x5a9},
{0x6839,0x6839,0x81c},
{0x683c,0x683c,0x5a8},
{0x683d,0x683d,0x83f},
{0x6840,0x6840,0x143f},
{0x6841,0x6841,0x73b},
{0x6842,0x6842,0x721},
{0x6843,0x6843,0xc67},
{0x6844,0x6844,0x212f},
{0x6846,0x6846,0x143d},
{0x6848,0x6848,0x48a},
{0x684d,0x684d,0x1440},
{0x684e,0x684e,0x1442},
{0x6850,0x6850,0x6c5},
{0x6851,0x6851,0x702},
{0x6852,0x6852,0x212c},
{0x6853,0x6853,0x5f8},
{0x6854,0x6854,0x665},
{0x6859,0x6859,0x1445},
{0x685b,0x685b,0x3925},
{0x685c,0x685c,0x869},
{0x685d,0x685d,0xe9f},
{0x685f,0x685f,0x885},
{0x6863,0x6863,0x1446},
{0x6867,0x6867,0xda2},
{0x6872,0x6872,0x3926},
{0x6874,0x6874,0x1452},
{0x6875,0x6875,0x3927},
{0x6876,0x6876,0x533},
{0x6877,0x6877,0x1447},
{0x687a,0x687a,0x3928},
{0x687e,0x687e,0x1458},
{0x687f,0x687f,0x1448},
{0x6881,0x6881,0xf8a},
{0x6883,0x6883,0x144f},
{0x6884,0x6884,0x3929},
{0x6885,0x6885,0xd15},
{0x688d,0x688d,0x1457},
{0x688e,0x688e,0x1e9c},
{0x688f,0x688f,0x144a},
{0x6893,0x6893,0x478},
{0x6894,0x6894,0x144c},
{0x6897,0x6897,0x7ce},
{0x689b,0x689b,0x144e},
{0x689d,0x689d,0x144d},
{0x689f,0x689f,0x1449},
{0x68a0,0x68a0,0x1454},
{0x68a2,0x68a2,0x9a7},
{0x68a5,0x68a5,0x392a},
{0x68a6,0x68a6,0x11be},
{0x68a7,0x68a7,0x79c},
{0x68a8,0x68a8,0xf66},
{0x68ad,0x68ad,0x144b},
{0x68af,0x68af,0xc10},
{0x68b0,0x68b0,0x581},
{0x68b1,0x68b1,0x81d},
{0x68b2,0x68b2,0x392b},
{0x68b3,0x68b3,0x1443},
{0x68b5,0x68b5,0x1453},
{0x68b6,0x68b6,0x5bf},
{0x68b9,0x68b9,0x1451},
{0x68ba,0x68ba,0x1455},
{0x68bc,0x68bc,0xc68},
{0x68c4,0x68c4,0x63a},
{0x68c6,0x68c6,0x1473},
{0x68c8,0x68c8,0x20af},
{0x68c9,0x68c9,0xed5},
{0x68ca,0x68ca,0x145a},
{0x68cb,0x68cb,0x639},
{0x68cd,0x68cd,0x1461},
{0x68cf,0x68cf,0x2130},
{0x68d0,0x68d0,0x392c},
{0x68d2,0x68d2,0xe6e},
{0x68d4,0x68d4,0x1462},
{0x68d5,0x68d5,0x1464},
{0x68d6,0x68d6,0x392d},
{0x68d7,0x68d7,0x1468},
{0x68d8,0x68d8,0x145c},
{0x68da,0x68da,0xb68},
{0x68df,0x68df,0xc69},
{0x68e0,0x68e0,0x146c},
{0x68e1,0x68e1,0x145f},
{0x68e3,0x68e3,0x1469},
{0x68e7,0x68e7,0x1463},
{0x68e8,0x68e8,0x392e},
{0x68ed,0x68ed,0x392f},
{0x68ee,0x68ee,0x9ff},
{0x68ef,0x68ef,0x146d},
{0x68f0,0x68f1,0x3930},
{0x68f2,0x68f2,0xa57},
{0x68f9,0x68f9,0x146b},
{0x68fa,0x68fa,0x5f9},
{0x68fc,0x68fc,0x3932},
{0x6900,0x6900,0xff6},
{0x6901,0x6901,0x1459},
{0x6904,0x6904,0x1467},
{0x6905,0x6905,0x49c},
{0x6908,0x6908,0x145b},
{0x690b,0x690b,0xec6},
{0x690c,0x690c,0x1460},
{0x690d,0x690d,0x9e8},
{0x690e,0x690e,0xbe3},
{0x690f,0x690f,0x1456},
{0x6911,0x6911,0x3933},
{0x6912,0x6912,0x1466},
{0x6913,0x6913,0x3934},
{0x6919,0x6919,0xa40},
{0x691a,0x691a,0x1470},
{0x691b,0x691b,0x5cf},
{0x691c,0x691c,0x755},
{0x6921,0x6921,0x1472},
{0x6922,0x6922,0x145d},
{0x6923,0x6923,0x1471},
{0x6925,0x6925,0x146a},
{0x6926,0x6926,0x145e},
{0x6928,0x6928,0x146e},
{0x692a,0x692a,0x146f},
{0x6930,0x6930,0x1480},
{0x6934,0x6934,0xca6},
{0x6935,0x6935,0x3935},
{0x6936,0x6936,0x1465},
{0x6939,0x6939,0x147c},
{0x693b,0x693b,0x3936},
{0x693d,0x693d,0x147e},
{0x693f,0x693f,0xbf4},
{0x694a,0x694a,0xf36},
{0x6953,0x6953,0xde8},
{0x6954,0x6954,0x1479},
{0x6955,0x6955,0xb2a},
{0x6957,0x6957,0x3937},
{0x6959,0x6959,0x147f},
{0x695a,0x695a,0xac1},
{0x695c,0x695c,0x1476},
{0x695d,0x695d,0x1483},
{0x695e,0x695e,0x1482},
{0x6960,0x6960,0xcc7},
{0x6961,0x6961,0x1481},
{0x6962,0x6962,0xcc2},
{0x6963,0x6963,0x3938},
{0x6968,0x6968,0x2132},
{0x696a,0x696a,0x1485},
{0x696b,0x696b,0x1478},
{0x696d,0x696d,0x6c0},
{0x696e,0x696e,0x147b},
{0x696f,0x696f,0x967},
{0x6972,0x6972,0x3939},
{0x6973,0x6973,0xd16},
{0x6974,0x6974,0x147d},
{0x6975,0x6975,0x6c3},
{0x6977,0x6977,0x1475},
{0x6978,0x6978,0x1477},
{0x6979,0x6979,0x1474},
{0x697c,0x697c,0xfd6},
{0x697d,0x697d,0x5b8},
{0x697e,0x697e,0x147a},
{0x697f,0x6980,0x393a},
{0x6981,0x6981,0x1484},
{0x6982,0x6982,0x593},
{0x698a,0x698a,0x857},
{0x698e,0x698e,0x4ff},
{0x6991,0x6991,0x1495},
{0x6994,0x6994,0xfd7},
{0x6995,0x6995,0x1498},
{0x6998,0x6998,0x2134},
{0x699b,0x699b,0xa00},
{0x699c,0x699c,0x1497},
{0x69a0,0x69a0,0x1496},
{0x69a6,0x69a6,0x393c},
{0x69a7,0x69a7,0x1493},
{0x69ad,0x69ad,0x393d},
{0x69ae,0x69ae,0x1487},
{0x69b1,0x69b1,0x14a4},
{0x69b2,0x69b2,0x1486},
{0x69b4,0x69b4,0x1499},
{0x69b7,0x69b7,0x393e},
{0x69bb,0x69bb,0x1491},
{0x69be,0x69be,0x148c},
{0x69bf,0x69bf,0x1489},
{0x69c1,0x69c1,0x148a},
{0x69c3,0x69c3,0x1492},
{0x69c7,0x69c7,0x1d33},
{0x69ca,0x69ca,0x148f},
{0x69cb,0x69cb,0x7cf},
{0x69cc,0x69cc,0xbe4},
{0x69cd,0x69cd,0xae6},
{0x69ce,0x69ce,0x148d},
{0x69d0,0x69d0,0x1488},
{0x69d3,0x69d3,0x148b},
{0x69d6,0x69d7,0x393f},
{0x69d8,0x69d8,0xf37},
{0x69d9,0x69d9,0xe98},
{0x69dd,0x69dd,0x1490},
{0x69de,0x69de,0x149a},
{0x69e2,0x69e2,0x2135},
{0x69e7,0x69e7,0x14a2},
{0x69e8,0x69e8,0x149b},
{0x69eb,0x69eb,0x14a8},
{0x69ed,0x69ed,0x14a6},
{0x69f2,0x69f2,0x14a1},
{0x69f6,0x69f6,0x373f},
{0x69f9,0x69f9,0x14a0},
{0x69fb,0x69fb,0xbec},
{0x69fd,0x69fd,0xae7},
{0x69ff,0x69ff,0x149e},
{0x6a01,0x6a01,0x3941},
{0x6a02,0x6a02,0x149c},
{0x6a05,0x6a05,0x14a3},
{0x6a0a,0x6a0a,0x14a9},
{0x6a0b,0x6a0b,0xd89},
{0x6a0c,0x6a0c,0x14af},
{0x6a0f,0x6a0f,0x3942},
{0x6a12,0x6a12,0x14aa},
{0x6a13,0x6a13,0x14ad},
{0x6a14,0x6a14,0x14a7},
{0x6a15,0x6a15,0x3943},
{0x6a17,0x6a17,0xbb2},
{0x6a19,0x6a19,0xdaa},
{0x6a1b,0x6a1b,0x149d},
{0x6a1e,0x6a1e,0x14a5},
{0x6a1f,0x6a1f,0x9a8},
{0x6a21,0x6a21,0xedb},
{0x6a22,0x6a22,0x14b9},
{0x6a23,0x6a23,0x14ac},
{0x6a28,0x6a28,0x3944},
{0x6a29,0x6a29,0x756},
{0x6a2a,0x6a2a,0x523},
{0x6a2b,0x6a2b,0x5bd},
{0x6a2e,0x6a2e,0x1494},
{0x6a30,0x6a30,0x2136},
{0x6a34,0x6a34,0x3945},
{0x6a35,0x6a35,0x9a9},
{0x6a36,0x6a36,0x14b1},
{0x6a38,0x6a38,0x14b8},
{0x6a39,0x6a39,0x925},
{0x6a3a,0x6a3a,0x5d0},
{0x6a3d,0x6a3d,0xb6c},
{0x6a3e,0x6a3e,0x3946},
{0x6a44,0x6a44,0x14ae},
{0x6a45,0x6a45,0x3947},
{0x6a46,0x6a46,0x2138},
{0x6a47,0x6a47,0x14b3},
{0x6a48,0x6a48,0x14b7},
{0x6a4b,0x6a4b,0x6ae},
{0x6a50,0x6a51,0x3948},
{0x6a54,0x6a54,0x3c34},
{0x6a56,0x6a56,0x394a},
{0x6a58,0x6a58,0x666},
{0x6a59,0x6a59,0x14b5},
{0x6a5b,0x6a5b,0x394b},
{0x6a5f,0x6a5f,0x63b},
{0x6a61,0x6a61,0xca3},
{0x6a62,0x6a62,0x14b4},
{0x6a66,0x6a66,0x14b6},
{0x6a6b,0x6a6b,0x2137},
{0x6a72,0x6a72,0x14b0},
{0x6a73,0x6a73,0x2139},
{0x6a78,0x6a78,0x14b2},
{0x6a7e,0x6a7e,0x213a},
{0x6a7f,0x6a7f,0x5be},
{0x6a80,0x6a80,0xb87},
{0x6a83,0x6a83,0x394c},
{0x6a84,0x6a84,0x14bd},
{0x6a89,0x6a89,0x394d},
{0x6a8d,0x6a8d,0x14bb},
{0x6a8e,0x6a8e,0x79d},
{0x6a90,0x6a90,0x14ba},
{0x6a91,0x6a91,0x394e},
{0x6a97,0x6a97,0x14c0},
{0x6a9c,0x6a9c,0x143b},
{0x6a9d,0x6a9f,0x394f},
{0x6aa0,0x6aa0,0x14bc},
{0x6aa2,0x6aa3,0x14be},
{0x6aaa,0x6aaa,0x14cb},
{0x6aac,0x6aac,0x14c7},
{0x6aae,0x6aae,0x1450},
{0x6ab3,0x6ab3,0x14c6},
{0x6ab8,0x6ab8,0x14c5},
{0x6abb,0x6abb,0x14c2},
{0x6ac1,0x6ac1,0x14ab},
{0x6ac2,0x6ac2,0x14c4},
{0x6ac3,0x6ac3,0x14c3},
{0x6ad1,0x6ad1,0x14c9},
{0x6ad3,0x6ad3,0xfcc},
{0x6ada,0x6ada,0x14cc},
{0x6adb,0x6adb,0x6f3},
{0x6adc,0x6adc,0x3952},
{0x6ade,0x6ade,0x14c8},
{0x6adf,0x6adf,0x14ca},
{0x6ae2,0x6ae2,0x213b},
{0x6ae4,0x6ae4,0x213c},
{0x6ae7,0x6ae7,0x3953},
{0x6ae8,0x6ae8,0xd3b},
{0x6aea,0x6aea,0x14cd},
{0x6aec,0x6aec,0x3954},
{0x6afa,0x6afa,0x14d1},
{0x6afb,0x6afb,0x14ce},
{0x6b04,0x6b04,0xf5d},
{0x6b05,0x6b05,0x14cf},
{0x6b0a,0x6b0a,0x149f},
{0x6b12,0x6b12,0x14d2},
{0x6b16,0x6b16,0x14d3},
{0x6b1d,0x6b1d,0x4d7},
{0x6b1e,0x6b1e,0x3955},
{0x6b1f,0x6b1f,0x14d5},
{0x6b20,0x6b20,0x73d},
{0x6b21,0x6b21,0x8cd},
{0x6b23,0x6b23,0x6cd},
{0x6b24,0x6b24,0x3956},
{0x6b27,0x6b27,0x524},
{0x6b32,0x6b32,0xf49},
{0x6b35,0x6b35,0x3957},
{0x6b37,0x6b37,0x14d7},
{0x6b38,0x6b38,0x14d6},
{0x6b39,0x6b39,0x14d9},
{0x6b3a,0x6b3a,0x657},
{0x6b3d,0x6b3d,0x6ce},
{0x6b3e,0x6b3e,0x5fa},
{0x6b43,0x6b43,0x14dc},
{0x6b46,0x6b46,0x3958},
{0x6b47,0x6b47,0x14db},
{0x6b49,0x6b49,0x14dd},
{0x6b4c,0x6b4c,0x54e},
{0x6b4e,0x6b4e,0xb75},
{0x6b50,0x6b50,0x14de},
{0x6b53,0x6b53,0x5fb},
{0x6b54,0x6b54,0x14e0},
{0x6b56,0x6b56,0x3959},
{0x6b59,0x6b59,0x14df},
{0x6b5b,0x6b5b,0x14e1},
{0x6b5f,0x6b5f,0x14e2},
{0x6b60,0x6b60,0x395a},
{0x6b61,0x6b61,0x14e3},
{0x6b62,0x6b62,0x8ad},
{0x6b63,0x6b63,0xa59},
{0x6b64,0x6b64,0x811},
{0x6b65,0x6b65,0x344a},
{0x6b66,0x6b66,0xde2},
{0x6b69,0x6b69,0xe32},
{0x6b6a,0x6b6a,0xfea},
{0x6b6f,0x6b6f,0x8c3},
{0x6b72,0x6b72,0x35d9},
{0x6b73,0x6b73,0x840},
{0x6b74,0x6b74,0xfba},
{0x6b77,0x6b77,0x3456},
{0x6b78,0x6b79,0x14e4},
{0x6b7b,0x6b7b,0x8ae},
{0x6b7f,0x6b80,0x14e6},
{0x6b82,0x6b82,0x395b},
{0x6b83,0x6b83,0x14e9},
{0x6b84,0x6b84,0x14e8},
{0x6b86,0x6b86,0xe86},
{0x6b89,0x6b89,0x968},
{0x6b8a,0x6b8a,0x918},
{0x6b8b,0x6b8b,0x892},
{0x6b8d,0x6b8d,0x14ea},
{0x6b95,0x6b95,0x14ec},
{0x6b96,0x6b96,0x9e9},
{0x6b98,0x6b98,0x14eb},
{0x6b9e,0x6b9e,0x14ed},
{0x6ba4,0x6ba4,0x14ee},
{0x6baa,0x6bab,0x14ef},
{0x6baf,0x6baf,0x14f1},
{0x6bb1,0x6bb1,0x14f3},
{0x6bb2,0x6bb2,0x14f2},
{0x6bb3,0x6bb3,0x14f4},
{0x6bb4,0x6bb4,0x525},
{0x6bb5,0x6bb5,0xb88},
{0x6bb7,0x6bb7,0x14f5},
{0x6bba,0x6bba,0x874},
{0x6bbb,0x6bbb,0x5aa},
{0x6bbc,0x6bbc,0x14f6},
{0x6bbe,0x6bbe,0x395c},
{0x6bbf,0x6bbf,0xc3c},
{0x6bc0,0x6bc0,0x119d},
{0x6bc5,0x6bc5,0x63d},
{0x6bc6,0x6bc6,0x14f7},
{0x6bcb,0x6bcb,0x14f8},
{0x6bcc,0x6bcc,0x3744},
{0x6bcd,0x6bcd,0xe3c},
{0x6bce,0x6bce,0xe96},
{0x6bcf,0x6bcf,0x344c},
{0x6bd2,0x6bd2,0xc9f},
{0x6bd3,0x6bd3,0x14f9},
{0x6bd4,0x6bd4,0xd7a},
{0x6bd6,0x6bd6,0x213d},
{0x6bd8,0x6bd8,0xd8f},
{0x6bdb,0x6bdb,0xedf},
{0x6bdf,0x6bdf,0x14fa},
{0x6be1,0x6be1,0x395d},
{0x6beb,0x6beb,0x14fc},
{0x6bec,0x6bec,0x14fb},
{0x6bef,0x6bef,0x14fe},
{0x6bf1,0x6bf1,0x395e},
{0x6bf3,0x6bf3,0x14fd},
{0x6c08,0x6c08,0x1500},
{0x6c0f,0x6c0f,0x8af},
{0x6c10,0x6c10,0x395f},
{0x6c11,0x6c11,0xebd},
{0x6c13,0x6c14,0x1501},
{0x6c17,0x6c17,0x63e},
{0x6c1b,0x6c1b,0x1503},
{0x6c23,0x6c23,0x1505},
{0x6c24,0x6c24,0x1504},
{0x6c33,0x6c33,0x3960},
{0x6c34,0x6c34,0xa2b},
{0x6c35,0x6c35,0x3961},
{0x6c37,0x6c37,0xdab},
{0x6c38,0x6c38,0x4ec},
{0x6c3e,0x6c3e,0xd59},
{0x6c3f,0x6c3f,0x213e},
{0x6c40,0x6c40,0xc11},
{0x6c41,0x6c41,0x94b},
{0x6c42,0x6c42,0x67b},
{0x6c4e,0x6c4e,0xd5a},
{0x6c50,0x6c50,0x8da},
{0x6c55,0x6c55,0x1507},
{0x6c57,0x6c57,0x5fc},
{0x6c59,0x6c59,0x3963},
{0x6c5a,0x6c5a,0x51a},
{0x6c5c,0x6c5c,0x213f},
{0x6c5d,0x6c5d,0xcca},
{0x6c5e,0x6c5e,0x1506},
{0x6c5f,0x6c5f,0x7d0},
{0x6c60,0x6c60,0xb91},
{0x6c62,0x6c62,0x1508},
{0x6c68,0x6c68,0x1510},
{0x6c6a,0x6c6a,0x1509},
{0x6c6f,0x6c6f,0x2141},
{0x6c70,0x6c70,0xb21},
{0x6c72,0x6c72,0x67c},
{0x6c73,0x6c73,0x1511},
{0x6c76,0x6c76,0x3964},
{0x6c7a,0x6c7a,0x73e},
{0x6c7b,0x6c7b,0x3965},
{0x6c7d,0x6c7d,0x63f},
{0x6c7e,0x6c7e,0x150f},
{0x6c81,0x6c81,0x150d},
{0x6c82,0x6c82,0x150a},
{0x6c83,0x6c83,0xf4a},
{0x6c85,0x6c85,0x3966},
{0x6c86,0x6c86,0x2140},
{0x6c88,0x6c88,0xbdc},
{0x6c8c,0x6c8c,0xcb1},
{0x6c8d,0x6c8d,0x150b},
{0x6c90,0x6c90,0x1513},
{0x6c92,0x6c92,0x1512},
{0x6c93,0x6c93,0x6f9},
{0x6c95,0x6c95,0x3967},
{0x6c96,0x6c96,0x52d},
{0x6c99,0x6c99,0x82b},
{0x6c9a,0x6c9a,0x150c},
{0x6c9b,0x6c9b,0x150e},
{0x6c9c,0x6c9c,0x3968},
{0x6ca1,0x6ca1,0xe85},
{0x6ca2,0x6ca2,0xb54},
{0x6caa,0x6caa,0x3749},
{0x6cab,0x6cab,0xea5},
{0x6cae,0x6cae,0x151b},
{0x6cb1,0x6cb1,0x151c},
{0x6cb3,0x6cb3,0x54f},
{0x6cb8,0x6cb8,0xdf8},
{0x6cb9,0x6cb9,0xf09},
{0x6cba,0x6cba,0x151e},
{0x6cbb,0x6cbb,0x8cf},
{0x6cbc,0x6cbc,0x9aa},
{0x6cbd,0x6cbd,0x1517},
{0x6cbe,0x6cbe,0x151d},
{0x6cbf,0x6cbf,0x50a},
{0x6cc1,0x6cc1,0x6af},
{0x6cc4,0x6cc4,0x1514},
{0x6cc5,0x6cc5,0x1519},
{0x6cc9,0x6cc9,0xa98},
{0x6cca,0x6cca,0xd27},
{0x6ccc,0x6ccc,0xd7b},
{0x6cd0,0x6cd0,0x3969},
{0x6cd3,0x6cd3,0x1516},
{0x6cd4,0x6cd4,0x396a},
{0x6cd5,0x6cd5,0xe4f},
{0x6cd6,0x6cd6,0x396b},
{0x6cd7,0x6cd7,0x1518},
{0x6cd9,0x6cd9,0x1521},
{0x6cda,0x6cda,0x2142},
{0x6cdb,0x6cdb,0x151f},
{0x6cdd,0x6cdd,0x151a},
{0x6ce0,0x6ce0,0x396c},
{0x6ce1,0x6ce1,0xe50},
{0x6ce2,0x6ce2,0xcfe},
{0x6ce3,0x6ce3,0x67d},
{0x6ce5,0x6ce5,0xc1f},
{0x6ce8,0x6ce8,0xbab},
{0x6cea,0x6cea,0x1522},
{0x6ceb,0x6cec,0x396d},
{0x6cee,0x6cee,0x396f},
{0x6cef,0x6cef,0x1520},
{0x6cf0,0x6cf0,0xb39},
{0x6cf1,0x6cf1,0x1515},
{0x6cf3,0x6cf3,0x4ed},
{0x6d01,0x6d01,0x3c35},
{0x6d04,0x6d04,0x2143},
{0x6d0a,0x6d0a,0x3970},
{0x6d0b,0x6d0b,0xf38},
{0x6d0c,0x6d0c,0x152d},
{0x6d0e,0x6d0e,0x3971},
{0x6d11,0x6d11,0x3972},
{0x6d12,0x6d12,0x152c},
{0x6d17,0x6d17,0xa9a},
{0x6d19,0x6d19,0x1529},
{0x6d1b,0x6d1b,0xf56},
{0x6d1e,0x6d1e,0xc8e},
{0x6d1f,0x6d1f,0x1523},
{0x6d25,0x6d25,0xbe1},
{0x6d29,0x6d29,0x4ee},
{0x6d2a,0x6d2a,0x7d1},
{0x6d2b,0x6d2b,0x1526},
{0x6d2e,0x6d2e,0x3973},
{0x6d32,0x6d32,0x931},
{0x6d33,0x6d33,0x152b},
{0x6d35,0x6d35,0x152a},
{0x6d36,0x6d36,0x1525},
{0x6d38,0x6d38,0x1528},
{0x6d3b,0x6d3b,0x5c6},
{0x6d3d,0x6d3d,0x1527},
{0x6d3e,0x6d3e,0xcff},
{0x6d41,0x6d41,0xf76},
{0x6d44,0x6d44,0x9dc},
{0x6d45,0x6d45,0xa99},
{0x6d57,0x6d57,0x3974},
{0x6d59,0x6d59,0x1533},
{0x6d5a,0x6d5a,0x1531},
{0x6d5c,0x6d5c,0xdbf},
{0x6d5e,0x6d5e,0x3975},
{0x6d63,0x6d63,0x152e},
{0x6d64,0x6d64,0x1530},
{0x6d65,0x6d65,0x3976},
{0x6d66,0x6d66,0x4dc},
{0x6d69,0x6d69,0x7d2},
{0x6d6a,0x6d6a,0xfd8},
{0x6d6c,0x6d6c,0x59b},
{0x6d6e,0x6d6e,0xdd4},
{0x6d6f,0x6d6f,0x2145},
{0x6d74,0x6d74,0xf4b},
{0x6d77,0x6d77,0x582},
{0x6d78,0x6d78,0xa01},
{0x6d79,0x6d79,0x1532},
{0x6d82,0x6d82,0x3977},
{0x6d85,0x6d85,0x1537},
{0x6d87,0x6d87,0x2144},
{0x6d88,0x6d88,0x9ab},
{0x6d89,0x6d89,0x342a},
{0x6d8c,0x6d8c,0xf1a},
{0x6d8e,0x6d8e,0x1534},
{0x6d93,0x6d93,0x152f},
{0x6d95,0x6d95,0x1535},
{0x6d96,0x6d96,0x2146},
{0x6d99,0x6d99,0xfa6},
{0x6d9b,0x6d9b,0xc6d},
{0x6d9c,0x6d9c,0xc9a},
{0x6dac,0x6dac,0x2147},
{0x6daf,0x6daf,0x594},
{0x6db2,0x6db2,0x4f7},
{0x6db5,0x6db5,0x153b},
{0x6db8,0x6db8,0x153e},
{0x6dbc,0x6dbc,0xf8b},
{0x6dbf,0x6dbf,0x3978},
{0x6dc0,0x6dc0,0xf4e},
{0x6dc4,0x6dc4,0x3979},
{0x6dc5,0x6dc5,0x1545},
{0x6dc6,0x6dc6,0x153f},
{0x6dc7,0x6dc7,0x153c},
{0x6dca,0x6dca,0x397a},
{0x6dcb,0x6dcb,0xf9c},
{0x6dcc,0x6dcc,0x1542},
{0x6dcf,0x6dcf,0x2148},
{0x6dd0,0x6dd0,0x3c36},
{0x6dd1,0x6dd1,0x954},
{0x6dd2,0x6dd2,0x1544},
{0x6dd5,0x6dd5,0x1549},
{0x6dd6,0x6dd6,0x397b},
{0x6dd8,0x6dd8,0xc6b},
{0x6dd9,0x6dd9,0x1547},
{0x6dda,0x6dda,0x3453},
{0x6dde,0x6dde,0x1541},
{0x6de1,0x6de1,0xb76},
{0x6de4,0x6de4,0x1548},
{0x6de6,0x6de6,0x153d},
{0x6de8,0x6de8,0x1543},
{0x6de9,0x6de9,0x397c},
{0x6dea,0x6dea,0x154a},
{0x6deb,0x6deb,0x4c0},
{0x6dec,0x6dec,0x1540},
{0x6dee,0x6dee,0x154b},
{0x6df1,0x6df1,0xa02},
{0x6df2,0x6df2,0x214a},
{0x6df3,0x6df3,0x969},
{0x6df5,0x6df5,0xdf5},
{0x6df7,0x6df7,0x81e},
{0x6df8,0x6df8,0x2149},
{0x6df9,0x6df9,0x1538},
{0x6dfa,0x6dfa,0x1546},
{0x6dfb,0x6dfb,0xc34},
{0x6dfc,0x6dfc,0x214b},
{0x6e05,0x6e05,0xa5a},
{0x6e07,0x6e07,0x5c7},
{0x6e08,0x6e08,0x841},
{0x6e09,0x6e09,0x9ac},
{0x6e0a,0x6e0a,0x153a},
{0x6e0b,0x6e0b,0x94c},
{0x6e13,0x6e13,0x722},
{0x6e15,0x6e15,0x1539},
{0x6e17,0x6e17,0x3746},
{0x6e19,0x6e19,0x154f},
{0x6e1a,0x6e1a,0x977},
{0x6e1b,0x6e1b,0x76e},
{0x6e1d,0x6e1d,0x155e},
{0x6e1f,0x6e1f,0x1558},
{0x6e20,0x6e20,0x68e},
{0x6e21,0x6e21,0xc49},
{0x6e22,0x6e22,0x397d},
{0x6e23,0x6e23,0x1553},
{0x6e24,0x6e24,0x155c},
{0x6e25,0x6e25,0x473},
{0x6e26,0x6e26,0x4d4},
{0x6e27,0x6e27,0x214e},
{0x6e29,0x6e29,0x539},
{0x6e2b,0x6e2b,0x1555},
{0x6e2c,0x6e2c,0xb0c},
{0x6e2d,0x6e2d,0x154c},
{0x6e2e,0x6e2e,0x154e},
{0x6e2f,0x6e2f,0x7d3},
{0x6e34,0x6e34,0x3412},
{0x6e38,0x6e38,0x155f},
{0x6e39,0x6e39,0x214c},
{0x6e3a,0x6e3a,0x155a},
{0x6e3c,0x6e3c,0x214f},
{0x6e3e,0x6e3e,0x1552},
{0x6e42,0x6e42,0x3c37},
{0x6e43,0x6e43,0x1559},
{0x6e4a,0x6e4a,0xeb7},
{0x6e4d,0x6e4d,0x1557},
{0x6e4e,0x6e4e,0x155b},
{0x6e51,0x6e51,0x397e},
{0x6e56,0x6e56,0x784},
{0x6e58,0x6e58,0x9ad},
{0x6e5b,0x6e5b,0xb77},
{0x6e5c,0x6e5c,0x214d},
{0x6e5f,0x6e5f,0x1551},
{0x6e67,0x6e67,0xf19},
{0x6e6b,0x6e6b,0x1554},
{0x6e6e,0x6e6e,0x154d},
{0x6e6f,0x6e6f,0xc6c},
{0x6e72,0x6e72,0x1550},
{0x6e76,0x6e76,0x1556},
{0x6e7e,0x6e7e,0xff7},
{0x6e7f,0x6e7f,0x8ea},
{0x6e80,0x6e80,0xeac},
{0x6e82,0x6e82,0x1560},
{0x6e8c,0x6e8c,0xd42},
{0x6e8f,0x6e8f,0x156c},
{0x6e90,0x6e90,0x76f},
{0x6e96,0x6e96,0x96a},
{0x6e98,0x6e98,0x1562},
{0x6e9c,0x6e9c,0xf77},
{0x6e9d,0x6e9d,0x7d4},
{0x6e9f,0x6e9f,0x156f},
{0x6ea2,0x6ea2,0x4b2},
{0x6ea5,0x6ea5,0x156d},
{0x6eaa,0x6eaa,0x1561},
{0x6eab,0x6eab,0x340c},
{0x6eaf,0x6eaf,0x1567},
{0x6eb2,0x6eb2,0x1569},
{0x6eb6,0x6eb6,0xf39},
{0x6eb7,0x6eb7,0x1564},
{0x6eba,0x6eba,0xc28},
{0x6ebd,0x6ebd,0x1566},
{0x6ebf,0x6ebf,0x2150},
{0x6ec2,0x6ec2,0x156e},
{0x6ec4,0x6ec4,0x1568},
{0x6ec5,0x6ec5,0xed3},
{0x6ec7,0x6ec7,0x397f},
{0x6ec9,0x6ec9,0x1563},
{0x6eca,0x6eca,0x3980},
{0x6ecb,0x6ecb,0x8ce},
{0x6ecc,0x6ecc,0x157b},
{0x6ece,0x6ece,0x3981},
{0x6ed1,0x6ed1,0x5c8},
{0x6ed3,0x6ed3,0x1565},
{0x6ed4,0x6ed5,0x156a},
{0x6ed9,0x6ed9,0x3c38},
{0x6edd,0x6edd,0xb4c},
{0x6ede,0x6ede,0xb3a},
{0x6ee6,0x6ee6,0x3c24},
{0x6eec,0x6eec,0x1573},
{0x6eef,0x6eef,0x1579},
{0x6ef2,0x6ef2,0x1577},
{0x6ef4,0x6ef4,0xc23},
{0x6ef7,0x6ef7,0x157e},
{0x6ef8,0x6ef8,0x1574},
{0x6efd,0x6efd,0x3982},
{0x6efe,0x6efe,0x1575},
{0x6eff,0x6eff,0x155d},
{0x6f01,0x6f01,0x693},
{0x6f02,0x6f02,0xdac},
{0x6f06,0x6f06,0x8eb},
{0x6f09,0x6f09,0x809},
{0x6f0f,0x6f0f,0xfd9},
{0x6f11,0x6f11,0x1571},
{0x6f13,0x6f13,0x157d},
{0x6f14,0x6f14,0x50b},
{0x6f15,0x6f15,0xae8},
{0x6f1a,0x6f1a,0x3983},
{0x6f20,0x6f20,0xd2f},
{0x6f22,0x6f22,0x5fd},
{0x6f23,0x6f23,0xfc2},
{0x6f2a,0x6f2a,0x3984},
{0x6f2b,0x6f2b,0xead},
{0x6f2c,0x6f2c,0xbee},
{0x6f2f,0x6f2f,0x3985},
{0x6f31,0x6f31,0x1578},
{0x6f32,0x6f32,0x157a},
{0x6f33,0x6f33,0x3986},
{0x6f38,0x6f38,0xab4},
{0x6f3e,0x6f3e,0x157c},
{0x6f3f,0x6f3f,0x1576},
{0x6f41,0x6f41,0x1570},
{0x6f45,0x6f45,0x5ff},
{0x6f51,0x6f51,0x1e60},
{0x6f54,0x6f54,0x73f},
{0x6f58,0x6f58,0x158a},
{0x6f5a,0x6f5a,0x3987},
{0x6f5b,0x6f5b,0x1585},
{0x6f5c,0x6f5c,0xa9c},
{0x6f5e,0x6f5e,0x3988},
{0x6f5f,0x6f5f,0x5c1},
{0x6f62,0x6f62,0x3989},
{0x6f64,0x6f64,0x96b},
{0x6f66,0x6f66,0x158e},
{0x6f6d,0x6f6d,0x1587},
{0x6f6e,0x6f6e,0xbc8},
{0x6f6f,0x6f6f,0x1584},
{0x6f70,0x6f70,0xbf5},
{0x6f74,0x6f74,0x15a7},
{0x6f78,0x6f78,0x1581},
{0x6f7a,0x6f7a,0x1580},
{0x6f7c,0x6f7c,0x1589},
{0x6f7d,0x6f7d,0x398a},
{0x6f80,0x6f80,0x1583},
{0x6f81,0x6f81,0x1582},
{0x6f82,0x6f82,0x1588},
{0x6f84,0x6f84,0xa45},
{0x6f86,0x6f86,0x157f},
{0x6f88,0x6f88,0x2151},
{0x6f8b,0x6f8b,0x398b},
{0x6f8d,0x6f8d,0x398c},
{0x6f8e,0x6f8e,0x158b},
{0x6f91,0x6f91,0x158c},
{0x6f92,0x6f92,0x398d},
{0x6f94,0x6f94,0x398e},
{0x6f97,0x6f97,0x5fe},
{0x6f98,0x6f98,0x363c},
{0x6f9a,0x6f9a,0x398f},
{0x6fa1,0x6fa1,0x1591},
{0x6fa3,0x6fa3,0x1590},
{0x6fa4,0x6fa4,0x1592},
{0x6fa7,0x6fa8,0x3990},
{0x6faa,0x6faa,0x1595},
{0x6fb1,0x6fb1,0xc3d},
{0x6fb3,0x6fb3,0x158f},
{0x6fb5,0x6fb5,0x2152},
{0x6fb6,0x6fb6,0x3992},
{0x6fb9,0x6fb9,0x1593},
{0x6fc0,0x6fc0,0x739},
{0x6fc1,0x6fc1,0xb59},
{0x6fc2,0x6fc2,0x158d},
{0x6fc3,0x6fc3,0xcf1},
{0x6fc6,0x6fc6,0x1594},
{0x6fd4,0x6fd4,0x1599},
{0x6fd5,0x6fd5,0x1597},
{0x6fd8,0x6fd8,0x159a},
{0x6fda,0x6fda,0x3993},
{0x6fdb,0x6fdb,0x159d},
{0x6fde,0x6fde,0x3994},
{0x6fdf,0x6fdf,0x1596},
{0x6fe0,0x6fe0,0x7fc},
{0x6fe1,0x6fe1,0xcde},
{0x6fe4,0x6fe4,0x1536},
{0x6feb,0x6feb,0xf5e},
{0x6fec,0x6fec,0x1598},
{0x6fee,0x6fee,0x159c},
{0x6fef,0x6fef,0xb55},
{0x6ff1,0x6ff1,0x159b},
{0x6ff3,0x6ff3,0x1586},
{0x6ff5,0x6ff5,0x2153},
{0x6ff6,0x6ff6,0x1ba4},
{0x6ffa,0x6ffa,0x15a0},
{0x6ffe,0x6ffe,0x15a4},
{0x7001,0x7001,0x15a2},
{0x7005,0x7005,0x2154},
{0x7006,0x7006,0x1e50},
{0x7007,0x7007,0x2155},
{0x7009,0x7009,0x159e},
{0x700b,0x700b,0x159f},
{0x700f,0x700f,0x15a3},
{0x7011,0x7011,0x15a1},
{0x7015,0x7015,0xdc0},
{0x7018,0x7018,0x15a9},
{0x701a,0x701a,0x15a6},
{0x701b,0x701b,0x15a5},
{0x701d,0x701d,0x15a8},
{0x701e,0x701e,0xcac},
{0x701f,0x701f,0x15aa},
{0x7026,0x7026,0xbb3},
{0x7027,0x7027,0xb4d},
{0x7028,0x7028,0x2156},
{0x702c,0x702c,0xa49},
{0x7030,0x7030,0x15ab},
{0x7032,0x7032,0x15ad},
{0x7039,0x7039,0x3996},
{0x703c,0x703c,0x3997},
{0x703e,0x703e,0x15ac},
{0x704c,0x704c,0x1572},
{0x7051,0x7051,0x15ae},
{0x7054,0x7054,0x3999},
{0x7058,0x7058,0xcbf},
{0x705d,0x705e,0x399a},
{0x7063,0x7063,0x15af},
{0x7064,0x7064,0x399c},
{0x706b,0x706b,0x550},
{0x706c,0x706c,0x399d},
{0x706f,0x706f,0xc6e},
{0x7070,0x7070,0x583},
{0x7078,0x7078,0x67e},
{0x707c,0x707c,0x90a},
{0x707d,0x707d,0x842},
{0x707e,0x707e,0x399e},
{0x7081,0x7081,0x399f},
{0x7085,0x7085,0x2157},
{0x7089,0x7089,0xfcd},
{0x708a,0x708a,0xa2c},
{0x708e,0x708e,0x50c},
{0x7092,0x7092,0x15b1},
{0x7095,0x7095,0x39a0},
{0x7099,0x7099,0x15b0},
{0x70ab,0x70ab,0x2158},
{0x70ac,0x70ac,0x15b4},
{0x70ad,0x70ad,0xb78},
{0x70ae,0x70ae,0x15b7},
{0x70af,0x70af,0x15b2},
{0x70b3,0x70b3,0x15b6},
{0x70b7,0x70b7,0x39a1},
{0x70b8,0x70b8,0x15b5},
{0x70b9,0x70b9,0xc3a},
{0x70ba,0x70ba,0x49d},
{0x70bb,0x70bb,0x20ad},
{0x70c8,0x70c8,0xfbd},
{0x70cb,0x70cb,0x15b9},
{0x70cf,0x70cf,0x4ca},
{0x70d3,0x70d4,0x39a2},
{0x70d8,0x70d8,0x39a4},
{0x70d9,0x70d9,0x15bb},
{0x70dc,0x70dc,0x39a5},
{0x70dd,0x70dd,0x15ba},
{0x70df,0x70df,0x15b8},
{0x70f1,0x70f1,0x15b3},
{0x70f9,0x70f9,0xe51},
{0x70fd,0x70fd,0x15bd},
{0x7104,0x7104,0x215a},
{0x7107,0x7107,0x39a6},
{0x7109,0x7109,0x15bc},
{0x710f,0x710f,0x2159},
{0x7114,0x7114,0x50d},
{0x7119,0x7119,0x15bf},
{0x711a,0x711a,0xe02},
{0x711c,0x711c,0x15be},
{0x7120,0x7120,0x39a7},
{0x7121,0x7121,0xec1},
{0x7126,0x7126,0x9af},
{0x7130,0x7130,0x1ddc},
{0x7131,0x7131,0x39a8},
{0x7136,0x7136,0xab5},
{0x713c,0x713c,0x9ae},
{0x7146,0x7147,0x215c},
{0x7149,0x7149,0xfc3},
{0x714a,0x714a,0x39a9},
{0x714c,0x714c,0x15c5},
{0x714e,0x714e,0xa9d},
{0x7152,0x7152,0x39aa},
{0x7155,0x7155,0x15c1},
{0x7156,0x7156,0x15c6},
{0x7159,0x7159,0x50e},
{0x715c,0x715c,0x215b},
{0x7160,0x7160,0x39ab},
{0x7162,0x7162,0x15c4},
{0x7164,0x7164,0xd17},
{0x7165,0x7165,0x15c0},
{0x7166,0x7166,0x15c3},
{0x7167,0x7167,0x9b0},
{0x7169,0x7169,0xd65},
{0x716c,0x716c,0x15c7},
{0x716e,0x716e,0x8fd},
{0x7179,0x7179,0x39ac},
{0x717d,0x717d,0xa9e},
{0x7184,0x7184,0x15ca},
{0x7188,0x7188,0x15c2},
{0x718a,0x718a,0x6fd},
{0x718f,0x718f,0x15c8},
{0x7192,0x7192,0x39ad},
{0x7194,0x7194,0xf3a},
{0x7195,0x7195,0x15cb},
{0x7199,0x7199,0x205d},
{0x719f,0x719f,0x959},
{0x71a8,0x71a8,0x15cc},
{0x71ac,0x71ac,0x15cd},
{0x71b1,0x71b1,0xce4},
{0x71b9,0x71b9,0x15cf},
{0x71be,0x71be,0x15d0},
{0x71c1,0x71c1,0x215f},
{0x71c3,0x71c3,0xce9},
{0x71c8,0x71c8,0xc6f},
{0x71c9,0x71c9,0x15d2},
{0x71cb,0x71cb,0x39af},
{0x71ce,0x71ce,0x15d4},
{0x71d0,0x71d0,0xf9d},
{0x71d2,0x71d2,0x15d1},
{0x71d3,0x71d3,0x39b0},
{0x71d4,0x71d4,0x15d3},
{0x71d5,0x71d5,0x50f},
{0x71d6,0x71d6,0x39b1},
{0x71d7,0x71d7,0x15ce},
{0x71df,0x71df,0x114e},
{0x71e0,0x71e0,0x15d5},
{0x71e5,0x71e5,0xae9},
{0x71e6,0x71e6,0x886},
{0x71e7,0x71e7,0x15d7},
{0x71ec,0x71ec,0x15d6},
{0x71ed,0x71ed,0x9ea},
{0x71ee,0x71ee,0x10ee},
{0x71f5,0x71f5,0x15d8},
{0x71f9,0x71f9,0x15da},
{0x71fb,0x71fb,0x15c9},
{0x71fc,0x71fc,0x15d9},
{0x71fe,0x71fe,0x2160},
{0x71ff,0x71ff,0x15db},
{0x7200,0x7200,0x39b2},
{0x7206,0x7206,0xd30},
{0x720d,0x720d,0x15dc},
{0x7210,0x7210,0x15dd},
{0x721b,0x721b,0x15de},
{0x721d,0x721d,0x39b3},
{0x7228,0x7228,0x15df},
{0x722a,0x722a,0xbfa},
{0x722b,0x722b,0x39b4},
{0x722c,0x722c,0x15e1},
{0x722d,0x722d,0x15e0},
{0x7230,0x7230,0x15e2},
{0x7232,0x7232,0x15e3},
{0x7235,0x7235,0x90b},
{0x7236,0x7236,0xdd5},
{0x7238,0x7238,0x39b5},
{0x723a,0x723a,0xef8},
{0x723b,0x723c,0x15e4},
{0x723d,0x723d,0xad8},
{0x723e,0x723e,0x8d0},
{0x723f,0x7240,0x15e6},
{0x7241,0x7241,0x39b6},
{0x7246,0x7246,0x15e8},
{0x7247,0x7247,0xe22},
{0x7248,0x7248,0xd5b},
{0x724b,0x724b,0x15e9},
{0x724c,0x724c,0xd0d},
{0x7252,0x7252,0xbc9},
{0x7253,0x7253,0x39b7},
{0x7255,0x7256,0x39b8},
{0x7258,0x7258,0x15ea},
{0x7259,0x7259,0x567},
{0x725b,0x725b,0x687},
{0x725c,0x725c,0x39ba},
{0x725d,0x725d,0xed2},
{0x725f,0x725f,0xec2},
{0x7261,0x7261,0x534},
{0x7262,0x7262,0xfda},
{0x7267,0x7267,0xe80},
{0x7269,0x7269,0xdfa},
{0x7272,0x7272,0xa5b},
{0x7274,0x7274,0x15eb},
{0x7279,0x7279,0xc9b},
{0x727d,0x727d,0x757},
{0x727e,0x727e,0x15ec},
{0x7280,0x7280,0x844},
{0x7281,0x7281,0x15ee},
{0x7282,0x7282,0x15ed},
{0x7287,0x7287,0x15ef},
{0x728d,0x728d,0x39bb},
{0x7292,0x7292,0x15f0},
{0x7296,0x7296,0x15f1},
{0x72a0,0x72a0,0x658},
{0x72a2,0x72a2,0x15f2},
{0x72a7,0x72a7,0x15f3},
{0x72ac,0x72ac,0x758},
{0x72ad,0x72ad,0x39bc},
{0x72af,0x72af,0xd5c},
{0x72b1,0x72b1,0x2161},
{0x72b2,0x72b2,0x15f5},
{0x72b4,0x72b4,0x39bd},
{0x72b6,0x72b6,0x9dd},
{0x72b9,0x72b9,0x15f4},
{0x72be,0x72be,0x2162},
{0x72c0,0x72c0,0x342b},
{0x72c2,0x72c2,0x6b0},
{0x72c3,0x72c3,0x15f6},
{0x72c4,0x72c4,0x15f8},
{0x72c6,0x72c6,0x15f7},
{0x72c7,0x72c7,0x39be},
{0x72ce,0x72ce,0x15f9},
{0x72d0,0x72d0,0x785},
{0x72d2,0x72d2,0x15fa},
{0x72d7,0x72d7,0x6e1},
{0x72d9,0x72d9,0xac2},
{0x72db,0x72db,0x80f},
{0x72e0,0x72e1,0x15fc},
{0x72e2,0x72e2,0x15fb},
{0x72e9,0x72e9,0x919},
{0x72ec,0x72ec,0xca0},
{0x72ed,0x72ed,0x6b1},
{0x72f7,0x72f7,0x15ff},
{0x72f8,0x72f8,0xb6a},
{0x72f9,0x72f9,0x15fe},
{0x72fb,0x72fb,0x39bf},
{0x72fc,0x72fc,0xfdb},
{0x72fd,0x72fd,0xd18},
{0x7304,0x7305,0x39c0},
{0x730a,0x730a,0x1602},
{0x7316,0x7316,0x1604},
{0x7317,0x7317,0x1601},
{0x731b,0x731b,0xee0},
{0x731c,0x731c,0x1603},
{0x731d,0x731d,0x1605},
{0x731f,0x731f,0xf8c},
{0x7324,0x7324,0x2163},
{0x7325,0x7325,0x1609},
{0x7328,0x7328,0x39c2},
{0x7329,0x7329,0x1608},
{0x732a,0x732a,0xbb4},
{0x732b,0x732b,0xce3},
{0x732e,0x732e,0x759},
{0x732f,0x732f,0x1607},
{0x7331,0x7331,0x39c3},
{0x7334,0x7334,0x1606},
{0x7336,0x7337,0xf1b},
{0x733e,0x733e,0x160a},
{0x733f,0x733f,0x510},
{0x7343,0x7343,0x39c4},
{0x7344,0x7344,0x808},
{0x7345,0x7345,0x8b0},
{0x734e,0x734f,0x160b},
{0x7357,0x7357,0x160e},
{0x7363,0x7363,0x94d},
{0x7368,0x7368,0x1610},
{0x736a,0x736a,0x160f},
{0x736c,0x736c,0x39c5},
{0x7370,0x7370,0x1611},
{0x7372,0x7372,0x5ab},
{0x7375,0x7375,0x1613},
{0x7377,0x7377,0x2165},
{0x7378,0x7378,0x1612},
{0x737a,0x737a,0x1615},
{0x737b,0x737b,0x1614},
{0x737c,0x737c,0x39c6},
{0x7383,0x7383,0x39c7},
{0x7384,0x7384,0x770},
{0x7385,0x7386,0x39c8},
{0x7387,0x7387,0xf70},
{0x7389,0x7389,0x6c4},
{0x738b,0x738b,0x526},
{0x7395,0x7395,0x39ca},
{0x7396,0x7396,0x6e2},
{0x739e,0x73a0,0x39cb},
{0x73a6,0x73a6,0x39ce},
{0x73a8,0x73a8,0x3c3f},
{0x73a9,0x73a9,0x61d},
{0x73ab,0x73ab,0x39cf},
{0x73b2,0x73b2,0xfb0},
{0x73b3,0x73b3,0x1617},
{0x73b5,0x73b5,0x39d0},
{0x73b7,0x73b7,0x39d1},
{0x73ba,0x73ba,0x35ea},
{0x73bb,0x73bb,0x1619},
{0x73bc,0x73bc,0x39d2},
{0x73bd,0x73bd,0x2166},
{0x73c0,0x73c0,0x161a},
{0x73c2,0x73c2,0x551},
{0x73c8,0x73c8,0x1616},
{0x73c9,0x73c9,0x2167},
{0x73ca,0x73ca,0x887},
{0x73cd,0x73cd,0xbdd},
{0x73ce,0x73ce,0x1618},
{0x73cf,0x73cf,0x39d3},
{0x73d2,0x73d2,0x216a},
{0x73d6,0x73d6,0x2168},
{0x73d9,0x73d9,0x39d4},
{0x73de,0x73de,0x161d},
{0x73e0,0x73e0,0x91a},
{0x73e3,0x73e3,0x2169},
{0x73e4,0x73e4,0x3c39},
{0x73e5,0x73e5,0x161b},
{0x73e9,0x73e9,0x39d5},
{0x73ea,0x73ea,0x714},
{0x73ed,0x73ed,0xd5d},
{0x73ee,0x73ee,0x161c},
{0x73f1,0x73f1,0x1637},
{0x73f4,0x73f4,0x39d6},
{0x73f5,0x73f5,0x216c},
{0x73f8,0x73f8,0x1622},
{0x73fd,0x73fd,0x39d7},
{0x73fe,0x73fe,0x771},
{0x7403,0x7403,0x67f},
{0x7404,0x7404,0x39d8},
{0x7405,0x7405,0x161f},
{0x7406,0x7406,0xf67},
{0x7407,0x7407,0x216b},
{0x7409,0x7409,0xf78},
{0x740a,0x740a,0x39d9},
{0x741a,0x741b,0x39da},
{0x7421,0x7421,0x3c3a},
{0x7422,0x7422,0xb56},
{0x7424,0x7424,0x39dc},
{0x7425,0x7425,0x1621},
{0x7426,0x7426,0x216d},
{0x7428,0x7428,0x39dd},
{0x7429,0x7429,0x216f},
{0x742a,0x742a,0x216e},
{0x742c,0x742c,0x39de},
{0x742e,0x742e,0x2170},
{0x742f,0x7431,0x39df},
{0x7432,0x7432,0x1623},
{0x7433,0x7433,0xf9e},
{0x7434,0x7434,0x6cf},
{0x7435,0x7435,0xd90},
{0x7436,0x7436,0xd00},
{0x7439,0x7439,0x39e2},
{0x743a,0x743a,0x1624},
{0x743f,0x743f,0x1626},
{0x7441,0x7441,0x1629},
{0x7444,0x7444,0x39e3},
{0x7447,0x7447,0x39e4},
{0x744b,0x744b,0x39e5},
{0x744d,0x744d,0x39e6},
{0x7451,0x7451,0x39e7},
{0x7455,0x7455,0x1625},
{0x7457,0x7457,0x39e8},
{0x7459,0x7459,0x1628},
{0x745a,0x745a,0x79e},
{0x745b,0x745b,0x4ef},
{0x745c,0x745c,0x162a},
{0x745e,0x745e,0xa36},
{0x745f,0x745f,0x1627},
{0x7460,0x7460,0xfa4},
{0x7463,0x7463,0x162d},
{0x7464,0x7464,0x1d35},
{0x7466,0x7466,0x39e9},
{0x7469,0x7469,0x162b},
{0x746a,0x746a,0x162e},
{0x746b,0x746b,0x39ea},
{0x746f,0x746f,0x1620},
{0x7470,0x7470,0x162c},
{0x7471,0x7471,0x39eb},
{0x7473,0x7473,0x82c},
{0x7476,0x7476,0x162f},
{0x747e,0x747e,0x1630},
{0x7480,0x7480,0x39ec},
{0x7483,0x7483,0xf68},
{0x7485,0x7487,0x39ed},
{0x7489,0x7489,0x2172},
{0x748b,0x748b,0x1631},
{0x7490,0x7490,0x39f0},
{0x7498,0x7498,0x39f1},
{0x749c,0x749c,0x39f2},
{0x749e,0x749e,0x1632},
{0x749f,0x749f,0x2173},
{0x74a0,0x74a0,0x39f3},
{0x74a2,0x74a2,0x161e},
{0x74a3,0x74a3,0x39f4},
{0x74a7,0x74a7,0x1633},
{0x74a8,0x74a8,0x39f5},
{0x74ab,0x74ab,0x39f6},
{0x74b0,0x74b0,0x600},
{0x74b5,0x74b5,0x39f7},
{0x74bd,0x74bd,0x8d1},
{0x74bf,0x74bf,0x39f8},
{0x74c8,0x74c8,0x39f9},
{0x74ca,0x74ca,0x1634},
{0x74cf,0x74cf,0x1635},
{0x74d4,0x74d4,0x1636},
{0x74da,0x74da,0x39fa},
{0x74dc,0x74dc,0x4dd},
{0x74de,0x74de,0x39fb},
{0x74e0,0x74e0,0x1638},
{0x74e2,0x74e2,0xdad},
{0x74e3,0x74e3,0x1639},
{0x74e6,0x74e6,0x5e0},
{0x74e7,0x74e7,0x163a},
{0x74e9,0x74e9,0x163b},
{0x74ee,0x74ee,0x163c},
{0x74ef,0x74ef,0x3750},
{0x74f0,0x74f1,0x163e},
{0x74f2,0x74f2,0x163d},
{0x74f6,0x74f6,0xdc5},
{0x74f7,0x74f7,0x1641},
{0x74f8,0x74f8,0x1640},
{0x7501,0x7501,0x2174},
{0x7503,0x7503,0x1643},
{0x7504,0x7504,0x1642},
{0x7505,0x7505,0x1644},
{0x750c,0x750c,0x1645},
{0x750d,0x750d,0x1647},
{0x750e,0x750e,0x1646},
{0x7511,0x7511,0x80b},
{0x7513,0x7513,0x1649},
{0x7515,0x7515,0x1648},
{0x7518,0x7518,0x601},
{0x751a,0x751a,0xa19},
{0x751c,0x751c,0xc36},
{0x751e,0x751e,0x164a},
{0x751f,0x751f,0xa5c},
{0x7522,0x7522,0x35de},
{0x7523,0x7523,0x888},
{0x7525,0x7525,0x51b},
{0x7526,0x7526,0x164b},
{0x7528,0x7528,0xf3b},
{0x752b,0x752b,0xe33},
{0x752c,0x752c,0x164c},
{0x752f,0x752f,0x20f2},
{0x7530,0x7530,0xc3e},
{0x7531,0x7531,0xf1d},
{0x7532,0x7532,0x7d5},
{0x7533,0x7533,0xa03},
{0x7537,0x7537,0xb89},
{0x7538,0x7538,0x10c9},
{0x753a,0x753a,0xbca},
{0x753b,0x753b,0x568},
{0x753c,0x753c,0x164d},
{0x7544,0x7544,0x164e},
{0x7546,0x7546,0x1653},
{0x7549,0x7549,0x1651},
{0x754a,0x754a,0x1650},
{0x754b,0x754b,0x13c7},
{0x754c,0x754c,0x584},
{0x754d,0x754d,0x164f},
{0x754e,0x754e,0x39fc},
{0x754f,0x754f,0x49e},
{0x7551,0x7551,0xd3e},
{0x7554,0x7554,0xd5e},
{0x7559,0x7559,0xf79},
{0x755a,0x755a,0x1654},
{0x755b,0x755b,0x1652},
{0x755c,0x755c,0xb9a},
{0x755d,0x755d,0xa4a},
{0x7560,0x7560,0xd3f},
{0x7562,0x7562,0xd9f},
{0x7564,0x7564,0x1656},
{0x7565,0x7565,0xf74},
{0x7566,0x7566,0x723},
{0x7567,0x7567,0x1657},
{0x7569,0x7569,0x1655},
{0x756a,0x756a,0xd6a},
{0x756b,0x756b,0x1658},
{0x756c,0x756c,0x3c3b},
{0x756d,0x756d,0x1659},
{0x756f,0x756f,0x2175},
{0x7570,0x7570,0x49f},
{0x7573,0x7573,0x9de},
{0x7574,0x7574,0x165e},
{0x7575,0x7575,0x3751},
{0x7576,0x7576,0x165b},
{0x7577,0x7577,0xcc5},
{0x7578,0x7578,0x165a},
{0x7579,0x7579,0x39fd},
{0x757f,0x757f,0x640},
{0x7581,0x7581,0x39fe},
{0x7582,0x7582,0x1661},
{0x7586,0x7587,0x165c},
{0x7589,0x7589,0x1660},
{0x758a,0x758a,0x165f},
{0x758b,0x758b,0xd97},
{0x758e,0x758e,0xac4},
{0x758f,0x758f,0xac3},
{0x7590,0x7590,0x39ff},
{0x7591,0x7591,0x659},
{0x7592,0x7593,0x3a00},
{0x7594,0x7594,0x1662},
{0x759a,0x759a,0x1663},
{0x759d,0x759d,0x1664},
{0x75a3,0x75a3,0x1666},
{0x75a5,0x75a5,0x1665},
{0x75ab,0x75ab,0x4f8},
{0x75b1,0x75b1,0x166e},
{0x75b2,0x75b2,0xd7c},
{0x75b3,0x75b3,0x1668},
{0x75b4,0x75b4,0x3a02},
{0x75b5,0x75b5,0x166a},
{0x75b8,0x75b8,0x166c},
{0x75b9,0x75b9,0xa04},
{0x75bc,0x75bc,0x166d},
{0x75bd,0x75bd,0x166b},
{0x75be,0x75be,0x8ec},
{0x75c2,0x75c2,0x1667},
{0x75c3,0x75c3,0x1669},
{0x75c5,0x75c5,0xdb4},
{0x75c7,0x75c7,0x9b1},
{0x75ca,0x75ca,0x1670},
{0x75cd,0x75cd,0x166f},
{0x75d2,0x75d2,0x1671},
{0x75d4,0x75d4,0x8d2},
{0x75d5,0x75d5,0x81f},
{0x75d8,0x75d8,0xc71},
{0x75d9,0x75d9,0x1672},
{0x75db,0x75db,0xbe7},
{0x75de,0x75de,0x1674},
{0x75e2,0x75e2,0xf69},
{0x75e3,0x75e3,0x1673},
{0x75e4,0x75e4,0x3a03},
{0x75e9,0x75e9,0xaeb},
{0x75ec,0x75ec,0x3752},
{0x75f0,0x75f0,0x1679},
{0x75f2,0x75f3,0x167b},
{0x75f4,0x75f4,0xb92},
{0x75f9,0x75f9,0x3a04},
{0x75fa,0x75fa,0x167a},
{0x75fc,0x75fc,0x1677},
{0x75fe,0x75ff,0x1675},
{0x7600,0x7600,0x3a05},
{0x7601,0x7601,0x1678},
{0x7609,0x7609,0x167f},
{0x760a,0x760a,0x3a06},
{0x760b,0x760b,0x167d},
{0x760d,0x760d,0x167e},
{0x7615,0x7616,0x3a07},
{0x7619,0x7619,0x3a09},
{0x761e,0x761e,0x3a0a},
{0x761f,0x761f,0x1680},
{0x7620,0x7622,0x1682},
{0x7624,0x7624,0x1685},
{0x7627,0x7627,0x1681},
{0x762d,0x762d,0x3a0b},
{0x7630,0x7630,0x1687},
{0x7634,0x7634,0x1686},
{0x7635,0x7635,0x3a0c},
{0x763b,0x763b,0x1688},
{0x7642,0x7642,0xf8d},
{0x7643,0x7643,0x3a0d},
{0x7646,0x7646,0x168b},
{0x7647,0x7648,0x1689},
{0x764b,0x764b,0x3a0e},
{0x764c,0x764c,0x61e},
{0x764e,0x764e,0x3753},
{0x7652,0x7652,0xf0a},
{0x7656,0x7656,0xe1a},
{0x7658,0x7658,0x168d},
{0x765c,0x765c,0x168c},
{0x7661,0x7662,0x168e},
{0x7665,0x7665,0x3a0f},
{0x7667,0x7667,0x1693},
{0x7668,0x766a,0x1690},
{0x766c,0x766c,0x1694},
{0x766d,0x766d,0x3a10},
{0x766f,0x766f,0x3a11},
{0x7670,0x7670,0x1695},
{0x7671,0x7671,0x3a12},
{0x7672,0x7672,0x1696},
{0x7674,0x7674,0x3a13},
{0x7676,0x7676,0x1697},
{0x7678,0x7678,0x1698},
{0x767a,0x767a,0xd43},
{0x767b,0x767b,0xc4a},
{0x767c,0x767c,0x1699},
{0x767d,0x767d,0xd28},
{0x767e,0x767e,0xda6},
{0x7680,0x7680,0x169a},
{0x7682,0x7682,0x2176},
{0x7683,0x7683,0x169b},
{0x7684,0x7684,0xc24},
{0x7686,0x7686,0x585},
{0x7687,0x7687,0x7d6},
{0x7688,0x7688,0x169c},
{0x768b,0x768b,0x169d},
{0x768e,0x768e,0x169e},
{0x7690,0x7690,0x877},
{0x7693,0x7693,0x16a0},
{0x7696,0x7696,0x169f},
{0x7699,0x769a,0x16a1},
{0x769b,0x769b,0x2179},
{0x769c,0x769c,0x2177},
{0x769e,0x769e,0x2178},
{0x76a4,0x76a5,0x3a14},
{0x76a6,0x76a6,0x217a},
{0x76ae,0x76ae,0xd7d},
{0x76b0,0x76b0,0x16a3},
{0x76b4,0x76b4,0x16a4},
{0x76b7,0x76b7,0x1d1c},
{0x76b8,0x76ba,0x16a5},
{0x76bf,0x76bf,0x87c},
{0x76c2,0x76c2,0x16a8},
{0x76c3,0x76c3,0xd0c},
{0x76c5,0x76c5,0x3a16},
{0x76c6,0x76c6,0xe8d},
{0x76c8,0x76c8,0x4f0},
{0x76ca,0x76ca,0x4f9},
{0x76cc,0x76cc,0x3a17},
{0x76cd,0x76cd,0x16a9},
{0x76d2,0x76d2,0x16ab},
{0x76d6,0x76d6,0x16aa},
{0x76d7,0x76d7,0xc6a},
{0x76db,0x76db,0xa5d},
{0x76dc,0x76dc,0x14d8},
{0x76de,0x76de,0x16ac},
{0x76df,0x76df,0xecd},
{0x76e1,0x76e1,0x16ad},
{0x76e3,0x76e3,0x602},
{0x76e4,0x76e4,0xd6b},
{0x76e5,0x76e5,0x16ae},
{0x76e7,0x76e7,0x16af},
{0x76ea,0x76ea,0x16b0},
{0x76ec,0x76ec,0x3a18},
{0x76ee,0x76ee,0xee8},
{0x76f2,0x76f2,0xee1},
{0x76f4,0x76f4,0xbda},
{0x76f8,0x76f8,0xaec},
{0x76fb,0x76fb,0x16b2},
{0x76fc,0x76fc,0x3a19},
{0x76fe,0x76fe,0x96c},
{0x7701,0x7701,0x9b2},
{0x7704,0x7704,0x16b5},
{0x7707,0x7707,0x16b4},
{0x7708,0x7708,0x16b3},
{0x7709,0x7709,0xd91},
{0x770b,0x770b,0x603},
{0x770c,0x770c,0x75d},
{0x771b,0x771b,0x16bb},
{0x771e,0x771e,0x16b8},
{0x771f,0x771f,0xa05},
{0x7720,0x7720,0xebe},
{0x7724,0x7724,0x16b7},
{0x7725,0x7726,0x16b9},
{0x7729,0x7729,0x16b6},
{0x7734,0x7734,0x3a1a},
{0x7736,0x7736,0x3a1b},
{0x7737,0x7738,0x16bc},
{0x773a,0x773a,0xbcb},
{0x773c,0x773c,0x61f},
{0x7740,0x7740,0xba3},
{0x7746,0x7746,0x217c},
{0x7747,0x7747,0x16be},
{0x775a,0x775a,0x16bf},
{0x775b,0x775b,0x16c2},
{0x775c,0x775c,0x3a1c},
{0x775f,0x7760,0x3a1d},
{0x7761,0x7761,0xa2d},
{0x7762,0x7762,0x1ec5},
{0x7763,0x7763,0xc9c},
{0x7765,0x7765,0x16c3},
{0x7766,0x7766,0xe81},
{0x7768,0x7768,0x16c0},
{0x776a,0x776a,0x3755},
{0x776b,0x776b,0x16c1},
{0x7772,0x7772,0x3a1f},
{0x7779,0x7779,0x16c6},
{0x777d,0x777d,0x3a20},
{0x777e,0x777e,0x16c5},
{0x777f,0x777f,0x16c4},
{0x778b,0x778b,0x16c8},
{0x778e,0x778e,0x16c7},
{0x7791,0x7791,0x16c9},
{0x7795,0x7795,0x3a21},
{0x779e,0x779e,0x16cb},
{0x77a0,0x77a0,0x16ca},
{0x77a5,0x77a5,0xe1d},
{0x77a9,0x77a9,0x3756},
{0x77aa,0x77aa,0x3a22},
{0x77ac,0x77ac,0x960},
{0x77ad,0x77ad,0xf8e},
{0x77b0,0x77b0,0x16cc},
{0x77b3,0x77b3,0xc8f},
{0x77b6,0x77b6,0x16cd},
{0x77b9,0x77b9,0x16ce},
{0x77bb,0x77bb,0x16d2},
{0x77bc,0x77bd,0x16d0},
{0x77bf,0x77bf,0x16cf},
{0x77c7,0x77c7,0x16d3},
{0x77cd,0x77cd,0x16d4},
{0x77d7,0x77d7,0x16d5},
{0x77da,0x77da,0x16d6},
{0x77db,0x77db,0xec3},
{0x77dc,0x77dc,0x16d7},
{0x77e2,0x77e2,0xefc},
{0x77e3,0x77e3,0x16d8},
{0x77e5,0x77e5,0xb8c},
{0x77e6,0x77e6,0x3a23},
{0x77e7,0x77e7,0xd20},
{0x77e9,0x77e9,0x6e3},
{0x77ed,0x77ed,0xb79},
{0x77ee,0x77ee,0x16d9},
{0x77ef,0x77ef,0x6b2},
{0x77f0,0x77f0,0x3a24},
{0x77f3,0x77f3,0xa74},
{0x77f4,0x77f4,0x3a25},
{0x77fc,0x77fc,0x16da},
{0x7802,0x7802,0x82d},
{0x7806,0x7806,0x3a26},
{0x780c,0x780c,0x16db},
{0x7812,0x7812,0x16dc},
{0x7814,0x7814,0x75a},
{0x7815,0x7815,0x845},
{0x7820,0x7820,0x16de},
{0x7821,0x7821,0x217e},
{0x7822,0x7822,0x3a27},
{0x7825,0x7825,0xc50},
{0x7826,0x7826,0x846},
{0x7827,0x7827,0x668},
{0x782d,0x782e,0x3a28},
{0x7830,0x7830,0x3a2a},
{0x7832,0x7832,0xe52},
{0x7834,0x7834,0xd01},
{0x7835,0x7835,0x3a2b},
{0x783a,0x783a,0xc51},
{0x783f,0x783f,0x7ee},
{0x7845,0x7845,0x16e0},
{0x784e,0x784e,0x217f},
{0x784f,0x784f,0x341e},
{0x7851,0x7851,0x3c3c},
{0x785d,0x785d,0x9b3},
{0x7864,0x7864,0x2180},
{0x7868,0x7868,0x3a2c},
{0x786b,0x786b,0xf7a},
{0x786c,0x786c,0x7d7},
{0x786f,0x786f,0x75b},
{0x7872,0x7872,0xd37},
{0x7874,0x7874,0x16e2},
{0x787a,0x787a,0x2181},
{0x787c,0x787c,0x16e4},
{0x7881,0x7881,0x79f},
{0x7886,0x7886,0x16e3},
{0x7887,0x7887,0xc12},
{0x788c,0x788c,0x16e6},
{0x788d,0x788d,0x595},
{0x788e,0x788e,0x16e1},
{0x7891,0x7891,0xd7e},
{0x7893,0x7893,0x4d2},
{0x7895,0x7895,0x85c},
{0x7897,0x7897,0xff8},
{0x789a,0x789a,0x16e5},
{0x789e,0x789e,0x3a2d},
{0x78a3,0x78a3,0x16e7},
{0x78a7,0x78a7,0xe1b},
{0x78a9,0x78a9,0xa7d},
{0x78aa,0x78aa,0x16e9},
{0x78af,0x78af,0x16ea},
{0x78b5,0x78b5,0x16e8},
{0x78ba,0x78ba,0x5ac},
{0x78bc,0x78bc,0x16f0},
{0x78be,0x78be,0x16ef},
{0x78c1,0x78c1,0x8d3},
{0x78c5,0x78c5,0x16f1},
{0x78c6,0x78c6,0x16ec},
{0x78c8,0x78c8,0x3a2e},
{0x78ca,0x78ca,0x16f2},
{0x78cb,0x78cb,0x16ed},
{0x78cc,0x78cc,0x3a2f},
{0x78ce,0x78ce,0x3a30},
{0x78d0,0x78d0,0xd6c},
{0x78d1,0x78d1,0x16eb},
{0x78d4,0x78d4,0x16ee},
{0x78da,0x78da,0x16f5},
{0x78e0,0x78e1,0x3a32},
{0x78e4,0x78e4,0x3a31},
{0x78e7,0x78e7,0x16f4},
{0x78e8,0x78e8,0xe8f},
{0x78ec,0x78ec,0x16f3},
{0x78ef,0x78ef,0x4af},
{0x78f2,0x78f2,0x3a34},
{0x78f4,0x78f4,0x16f7},
{0x78f7,0x78f7,0x3a35},
{0x78fb,0x78fb,0x3a36},
{0x78fd,0x78fd,0x16f6},
{0x7901,0x7901,0x9b4},
{0x7907,0x7907,0x16f8},
{0x790e,0x790e,0xac5},
{0x7911,0x7911,0x16fa},
{0x7912,0x7912,0x16f9},
{0x7919,0x7919,0x16fb},
{0x7926,0x7926,0x16dd},
{0x792a,0x792a,0x16df},
{0x792b,0x792b,0x16fd},
{0x792c,0x792c,0x16fc},
{0x7930,0x7930,0x2182},
{0x7931,0x7931,0x3a37},
{0x793a,0x793a,0x8d4},
{0x793b,0x793b,0x3a39},
{0x793c,0x793c,0xfb1},
{0x793d,0x793d,0x3a3a},
{0x793e,0x793e,0x8fe},
{0x7940,0x7940,0x16fe},
{0x7941,0x7941,0x70d},
{0x7945,0x7945,0x3a3b},
{0x7947,0x7947,0x65a},
{0x7948,0x7948,0x641},
{0x7949,0x7949,0x8b1},
{0x7950,0x7950,0xf1e},
{0x7953,0x7953,0x1704},
{0x7955,0x7955,0x1703},
{0x7956,0x7956,0xac6},
{0x7957,0x7957,0x1700},
{0x795a,0x795a,0x1702},
{0x795b,0x795c,0x3a3c},
{0x795d,0x795d,0x955},
{0x795e,0x795e,0xa06},
{0x795f,0x795f,0x1701},
{0x7960,0x7960,0x16ff},
{0x7962,0x7962,0xce0},
{0x7965,0x7965,0x9b5},
{0x7968,0x7968,0xdae},
{0x796d,0x796d,0x847},
{0x7977,0x7977,0xc72},
{0x797a,0x797a,0x1705},
{0x797f,0x797f,0x1706},
{0x7980,0x7980,0x171c},
{0x7981,0x7981,0x6d0},
{0x7984,0x7984,0xfe3},
{0x7985,0x7985,0xab7},
{0x798a,0x798a,0x1707},
{0x798b,0x798b,0x3a3e},
{0x798d,0x798d,0x552},
{0x798e,0x798e,0xc13},
{0x798f,0x798f,0xdf1},
{0x7994,0x7994,0x2186},
{0x7996,0x7996,0x3a3f},
{0x7998,0x7998,0x3a40},
{0x799b,0x799b,0x2188},
{0x799d,0x799d,0x1708},
{0x79a6,0x79a6,0x694},
{0x79a7,0x79a7,0x1709},
{0x79aa,0x79aa,0x170b},
{0x79ae,0x79ae,0x170c},
{0x79b0,0x79b0,0xcdf},
{0x79b1,0x79b1,0x1e4e},
{0x79b3,0x79b3,0x170d},
{0x79b8,0x79b8,0x3a41},
{0x79b9,0x79ba,0x170e},
{0x79bb,0x79bb,0x3a42},
{0x79bd,0x79bd,0x6d1},
{0x79be,0x79be,0x553},
{0x79bf,0x79bf,0xc9d},
{0x79c0,0x79c0,0x932},
{0x79c1,0x79c1,0x8b2},
{0x79c9,0x79c9,0x1710},
{0x79ca,0x79ca,0x3a43},
{0x79cb,0x79cb,0x933},
{0x79d1,0x79d1,0x54a},
{0x79d2,0x79d2,0xdb5},
{0x79d5,0x79d5,0x1711},
{0x79d8,0x79d8,0xd7f},
{0x79da,0x79da,0x3a44},
{0x79df,0x79df,0xac7},
{0x79e1,0x79e1,0x1714},
{0x79e3,0x79e3,0x1715},
{0x79e4,0x79e4,0xd1f},
{0x79e6,0x79e6,0xa07},
{0x79e7,0x79e7,0x1712},
{0x79e9,0x79e9,0xb9f},
{0x79ec,0x79ec,0x1713},
{0x79f0,0x79f0,0x9b6},
{0x79fb,0x79fb,0x4a0},
{0x7a00,0x7a00,0x643},
{0x7a03,0x7a03,0x3a45},
{0x7a05,0x7a05,0x3633},
{0x7a08,0x7a08,0x1716},
{0x7a09,0x7a09,0x3a46},
{0x7a0b,0x7a0b,0xc14},
{0x7a0d,0x7a0d,0x1717},
{0x7a0e,0x7a0e,0xa6b},
{0x7a11,0x7a11,0x3a47},
{0x7a14,0x7a14,0xeb9},
{0x7a17,0x7a17,0xd95},
{0x7a18,0x7a19,0x1718},
{0x7a1a,0x7a1a,0xb93},
{0x7a1c,0x7a1c,0xf8f},
{0x7a1e,0x7a1e,0x3a48},
{0x7a1f,0x7a1f,0x171b},
{0x7a20,0x7a20,0x171a},
{0x7a2d,0x7a2d,0x3a49},
{0x7a2e,0x7a2e,0x91b},
{0x7a31,0x7a31,0x171d},
{0x7a32,0x7a32,0x4b4},
{0x7a37,0x7a37,0x1720},
{0x7a39,0x7a39,0x3a4a},
{0x7a3b,0x7a3b,0x171e},
{0x7a3c,0x7a3c,0x554},
{0x7a3d,0x7a3d,0x724},
{0x7a3e,0x7a3e,0x171f},
{0x7a3f,0x7a3f,0x7d8},
{0x7a40,0x7a40,0x804},
{0x7a42,0x7a42,0xe36},
{0x7a43,0x7a43,0x1721},
{0x7a45,0x7a45,0x3a4b},
{0x7a46,0x7a46,0xe82},
{0x7a49,0x7a49,0x1723},
{0x7a4c,0x7a4c,0x3a4c},
{0x7a4d,0x7a4d,0xa75},
{0x7a4e,0x7a4e,0x4f1},
{0x7a4f,0x7a4f,0x53a},
{0x7a50,0x7a50,0x470},
{0x7a57,0x7a57,0x1722},
{0x7a5d,0x7a5d,0x3a4d},
{0x7a61,0x7a62,0x1724},
{0x7a63,0x7a63,0x9df},
{0x7a69,0x7a69,0x1726},
{0x7a6b,0x7a6b,0x5ad},
{0x7a6d,0x7a6d,0x3a4f},
{0x7a70,0x7a70,0x1728},
{0x7a74,0x7a74,0x740},
{0x7a76,0x7a76,0x680},
{0x7a78,0x7a78,0x3a50},
{0x7a79,0x7a79,0x1729},
{0x7a7a,0x7a7a,0x6ed},
{0x7a7d,0x7a7d,0x172a},
{0x7a7f,0x7a7f,0xaa0},
{0x7a81,0x7a81,0xca5},
{0x7a83,0x7a83,0xa84},
{0x7a84,0x7a84,0x865},
{0x7a88,0x7a88,0x172b},
{0x7a92,0x7a92,0xba0},
{0x7a93,0x7a93,0xaed},
{0x7a95,0x7a95,0x172d},
{0x7a96,0x7a96,0x172f},
{0x7a97,0x7a97,0x172c},
{0x7a98,0x7a98,0x172e},
{0x7a9f,0x7a9f,0x6f8},
{0x7aa0,0x7aa0,0x3a51},
{0x7aa3,0x7aa3,0x3a52},
{0x7aa9,0x7aa9,0x1730},
{0x7aaa,0x7aaa,0x6fc},
{0x7aae,0x7aae,0x681},
{0x7aaf,0x7aaf,0xf3c},
{0x7ab0,0x7ab0,0x1732},
{0x7ab3,0x7ab3,0x3a53},
{0x7ab6,0x7ab6,0x1733},
{0x7aba,0x7aba,0x4d0},
{0x7abb,0x7abc,0x3a54},
{0x7abf,0x7abf,0x1736},
{0x7ac3,0x7ac3,0x5d4},
{0x7ac4,0x7ac4,0x1735},
{0x7ac5,0x7ac5,0x1734},
{0x7ac6,0x7ac6,0x3a56},
{0x7ac7,0x7ac7,0x1738},
{0x7ac8,0x7ac8,0x1731},
{0x7aca,0x7aca,0x1739},
{0x7acb,0x7acb,0xf71},
{0x7acd,0x7acd,0x173a},
{0x7acf,0x7acf,0x173b},
{0x7ad1,0x7ad1,0x2189},
{0x7ad2,0x7ad2,0x11c5},
{0x7ad3,0x7ad3,0x173d},
{0x7ad5,0x7ad5,0x173c},
{0x7ad9,0x7ada,0x173e},
{0x7adc,0x7adc,0xf7d},
{0x7add,0x7add,0x1740},
{0x7adf,0x7adf,0x1c08},
{0x7ae0,0x7ae0,0x9b7},
{0x7ae1,0x7ae2,0x1741},
{0x7ae3,0x7ae3,0x961},
{0x7ae5,0x7ae5,0xc90},
{0x7ae6,0x7ae6,0x1743},
{0x7ae7,0x7ae7,0x218a},
{0x7aea,0x7aea,0xb66},
{0x7aeb,0x7aeb,0x218c},
{0x7aed,0x7aed,0x1744},
{0x7aef,0x7aef,0xb7a},
{0x7af0,0x7af0,0x1745},
{0x7af6,0x7af6,0x69d},
{0x7af8,0x7af8,0x1076},
{0x7af9,0x7af9,0xb9b},
{0x7afa,0x7afa,0x8df},
{0x7aff,0x7aff,0x604},
{0x7b02,0x7b02,0x1746},
{0x7b04,0x7b04,0x1753},
{0x7b06,0x7b06,0x1749},
{0x7b07,0x7b07,0x3a57},
{0x7b08,0x7b08,0x682},
{0x7b0a,0x7b0a,0x1748},
{0x7b0b,0x7b0b,0x1755},
{0x7b0f,0x7b0f,0x1747},
{0x7b11,0x7b11,0x9b8},
{0x7b14,0x7b14,0x3a58},
{0x7b18,0x7b19,0x174b},
{0x7b1b,0x7b1b,0xc25},
{0x7b1e,0x7b1e,0x174d},
{0x7b20,0x7b20,0x5bc},
{0x7b25,0x7b25,0xa20},
{0x7b26,0x7b26,0xdd6},
{0x7b27,0x7b27,0x3a59},
{0x7b28,0x7b28,0x174f},
{0x7b2c,0x7b2c,0xb48},
{0x7b31,0x7b31,0x3a5a},
{0x7b33,0x7b33,0x174a},
{0x7b35,0x7b35,0x174e},
{0x7b36,0x7b36,0x1750},
{0x7b39,0x7b39,0x86b},
{0x7b45,0x7b45,0x1757},
{0x7b46,0x7b46,0xda0},
{0x7b47,0x7b47,0x3a5b},
{0x7b48,0x7b48,0xd3a},
{0x7b49,0x7b49,0xc73},
{0x7b4b,0x7b4b,0x6d2},
{0x7b4c,0x7b4c,0x1756},
{0x7b4d,0x7b4d,0x1754},
{0x7b4e,0x7b4e,0x3a5c},
{0x7b4f,0x7b4f,0xd49},
{0x7b50,0x7b50,0x1751},
{0x7b51,0x7b51,0xb9c},
{0x7b52,0x7b52,0xc75},
{0x7b53,0x7b53,0x375d},
{0x7b54,0x7b54,0xc74},
{0x7b56,0x7b56,0x866},
{0x7b5d,0x7b5d,0x1769},
{0x7b60,0x7b60,0x3a5d},
{0x7b65,0x7b65,0x1759},
{0x7b67,0x7b67,0x175b},
{0x7b69,0x7b69,0x3a5e},
{0x7b6c,0x7b6c,0x175e},
{0x7b6d,0x7b6d,0x3a5f},
{0x7b6e,0x7b6e,0x175f},
{0x7b70,0x7b71,0x175c},
{0x7b72,0x7b72,0x3a60},
{0x7b74,0x7b74,0x175a},
{0x7b75,0x7b75,0x1758},
{0x7b7a,0x7b7a,0x1752},
{0x7b86,0x7b86,0xe1f},
{0x7b87,0x7b87,0x555},
{0x7b8b,0x7b8b,0x1766},
{0x7b8d,0x7b8d,0x1763},
{0x7b8f,0x7b8f,0x1768},
{0x7b91,0x7b91,0x3a61},
{0x7b92,0x7b92,0x1767},
{0x7b94,0x7b94,0xd29},
{0x7b95,0x7b95,0xeb3},
{0x7b97,0x7b97,0x889},
{0x7b98,0x7b98,0x1761},
{0x7b99,0x7b99,0x176a},
{0x7b9a,0x7b9a,0x1765},
{0x7b9c,0x7b9c,0x1764},
{0x7b9d,0x7b9d,0x1760},
{0x7b9e,0x7b9e,0x218d},
{0x7b9f,0x7b9f,0x1762},
{0x7ba1,0x7ba1,0x605},
{0x7baa,0x7baa,0xb7b},
{0x7bad,0x7bad,0xaa1},
{0x7baf,0x7baf,0x3a62},
{0x7bb1,0x7bb1,0xd36},
{0x7bb4,0x7bb4,0x176f},
{0x7bb8,0x7bb8,0xd38},
{0x7bc0,0x7bc0,0xa85},
{0x7bc1,0x7bc1,0x176c},
{0x7bc4,0x7bc4,0xd63},
{0x7bc6,0x7bc6,0x1770},
{0x7bc7,0x7bc7,0xe23},
{0x7bc9,0x7bc9,0xb99},
{0x7bcb,0x7bcb,0x176b},
{0x7bcc,0x7bcc,0x176d},
{0x7bcf,0x7bcf,0x176e},
{0x7bd7,0x7bd7,0x3a63},
{0x7bd9,0x7bd9,0x3a64},
{0x7bdd,0x7bdd,0x1771},
{0x7be0,0x7be0,0x8f0},
{0x7be4,0x7be4,0xc9e},
{0x7be5,0x7be5,0x1776},
{0x7be6,0x7be6,0x1775},
{0x7be9,0x7be9,0x1772},
{0x7bed,0x7bed,0xfdc},
{0x7bf3,0x7bf3,0x177b},
{0x7bf6,0x7bf6,0x177f},
{0x7bf7,0x7bf7,0x177c},
{0x7c00,0x7c00,0x1778},
{0x7c07,0x7c07,0x1779},
{0x7c0b,0x7c0b,0x3a65},
{0x7c0d,0x7c0d,0x177e},
{0x7c0f,0x7c0f,0x3a66},
{0x7c11,0x7c11,0x1773},
{0x7c12,0x7c12,0x10ea},
{0x7c13,0x7c13,0x177a},
{0x7c14,0x7c14,0x1774},
{0x7c17,0x7c17,0x177d},
{0x7c1e,0x7c1e,0x1e3b},
{0x7c1f,0x7c1f,0x1783},
{0x7c20,0x7c20,0x3a67},
{0x7c21,0x7c21,0x606},
{0x7c23,0x7c23,0x1780},
{0x7c26,0x7c26,0x3a68},
{0x7c27,0x7c27,0x1781},
{0x7c2a,0x7c2a,0x1782},
{0x7c2b,0x7c2b,0x1785},
{0x7c31,0x7c31,0x3a69},
{0x7c36,0x7c36,0x3a6a},
{0x7c37,0x7c37,0x1784},
{0x7c38,0x7c38,0xd8a},
{0x7c3d,0x7c3d,0x1786},
{0x7c3e,0x7c3e,0xfc4},
{0x7c3f,0x7c3f,0xe3d},
{0x7c40,0x7c40,0x178b},
{0x7c43,0x7c43,0x1788},
{0x7c4c,0x7c4c,0x1787},
{0x7c4d,0x7c4d,0xa76},
{0x7c4f,0x7c4f,0x178a},
{0x7c50,0x7c50,0x178c},
{0x7c51,0x7c51,0x3a6b},
{0x7c54,0x7c54,0x1789},
{0x7c56,0x7c56,0x1790},
{0x7c58,0x7c58,0x178d},
{0x7c59,0x7c59,0x3a6c},
{0x7c5f,0x7c5f,0x178e},
{0x7c60,0x7c60,0x1777},
{0x7c64,0x7c64,0x178f},
{0x7c65,0x7c65,0x1791},
{0x7c67,0x7c67,0x3a6d},
{0x7c6c,0x7c6c,0x1792},
{0x7c6e,0x7c6e,0x3a6e},
{0x7c70,0x7c70,0x3a6f},
{0x7c73,0x7c73,0xe16},
{0x7c75,0x7c75,0x1793},
{0x7c7b,0x7c7b,0x370e},
{0x7c7e,0x7c7e,0xeee},
{0x7c81,0x7c81,0x6c6},
{0x7c82,0x7c82,0x6ff},
{0x7c83,0x7c83,0x1794},
{0x7c89,0x7c89,0xe04},
{0x7c8b,0x7c8b,0xa2e},
{0x7c8d,0x7c8d,0xebc},
{0x7c90,0x7c90,0x1795},
{0x7c92,0x7c92,0xf7b},
{0x7c95,0x7c95,0xd2a},
{0x7c97,0x7c97,0xac8},
{0x7c98,0x7c98,0xcea},
{0x7c9b,0x7c9b,0x957},
{0x7c9f,0x7c9f,0x484},
{0x7ca1,0x7ca1,0x179a},
{0x7ca2,0x7ca2,0x1798},
{0x7ca4,0x7ca4,0x1796},
{0x7ca5,0x7ca5,0x5dd},
{0x7ca7,0x7ca7,0x9b9},
{0x7ca8,0x7ca8,0x179b},
{0x7cab,0x7cab,0x1799},
{0x7cad,0x7cad,0x1797},
{0x7cae,0x7cae,0x179f},
{0x7cb1,0x7cb1,0x179e},
{0x7cb2,0x7cb2,0x179d},
{0x7cb3,0x7cb3,0x179c},
{0x7cb9,0x7cb9,0x17a0},
{0x7cbc,0x7cbc,0x3a70},
{0x7cbd,0x7cbd,0x17a1},
{0x7cbe,0x7cbe,0xa5e},
{0x7cbf,0x7cbf,0x3a71},
{0x7cc0,0x7cc0,0x17a2},
{0x7cc2,0x7cc2,0x17a4},
{0x7cc5,0x7cc5,0x17a3},
{0x7cc8,0x7cc9,0x3a72},
{0x7cca,0x7cca,0x786},
{0x7cce,0x7cce,0xaba},
{0x7cd2,0x7cd2,0x17a6},
{0x7cd6,0x7cd6,0xc76},
{0x7cd7,0x7cd7,0x3a74},
{0x7cd8,0x7cd8,0x17a5},
{0x7cd9,0x7cd9,0x3a75},
{0x7cdc,0x7cdc,0x17a7},
{0x7cdd,0x7cdd,0x3a76},
{0x7cde,0x7cde,0xe05},
{0x7cdf,0x7cdf,0xaee},
{0x7ce0,0x7ce0,0x7d9},
{0x7ce2,0x7ce2,0x17a8},
{0x7ce7,0x7ce7,0xf90},
{0x7ceb,0x7ceb,0x3a77},
{0x7cef,0x7cef,0x17aa},
{0x7cf2,0x7cf2,0x17ab},
{0x7cf4,0x7cf4,0x17ac},
{0x7cf6,0x7cf6,0x17ad},
{0x7cf8,0x7cf8,0x8b3},
{0x7cfa,0x7cfa,0x17ae},
{0x7cfb,0x7cfb,0x725},
{0x7cfe,0x7cfe,0x684},
{0x7d00,0x7d00,0x644},
{0x7d02,0x7d02,0x17b0},
{0x7d04,0x7d04,0xeff},
{0x7d05,0x7d05,0x7da},
{0x7d06,0x7d06,0x17af},
{0x7d07,0x7d09,0x3a78},
{0x7d0a,0x7d0a,0x17b3},
{0x7d0b,0x7d0b,0xef2},
{0x7d0d,0x7d0d,0xcf2},
{0x7d10,0x7d10,0xda5},
{0x7d13,0x7d13,0x3a7b},
{0x7d14,0x7d14,0x96d},
{0x7d15,0x7d15,0x17b2},
{0x7d17,0x7d17,0x8ff},
{0x7d18,0x7d18,0x7db},
{0x7d19,0x7d19,0x8b4},
{0x7d1a,0x7d1a,0x683},
{0x7d1b,0x7d1b,0xe06},
{0x7d1c,0x7d1c,0x17b1},
{0x7d1d,0x7d1d,0x3a7c},
{0x7d20,0x7d20,0xac9},
{0x7d21,0x7d21,0xe70},
{0x7d22,0x7d22,0x867},
{0x7d23,0x7d23,0x3a7d},
{0x7d2b,0x7d2b,0x8b5},
{0x7d2c,0x7d2c,0xbf9},
{0x7d2e,0x7d2e,0x17b6},
{0x7d2f,0x7d2f,0xfa7},
{0x7d30,0x7d30,0x849},
{0x7d32,0x7d32,0x17b7},
{0x7d33,0x7d33,0xa08},
{0x7d35,0x7d35,0x17b9},
{0x7d39,0x7d39,0x9ba},
{0x7d3a,0x7d3a,0x820},
{0x7d3f,0x7d3f,0x17b8},
{0x7d41,0x7d41,0x3a7e},
{0x7d42,0x7d42,0x934},
{0x7d43,0x7d43,0x772},
{0x7d44,0x7d44,0xaca},
{0x7d45,0x7d45,0x17b4},
{0x7d46,0x7d46,0x17ba},
{0x7d48,0x7d48,0x218f},
{0x7d4b,0x7d4b,0x17b5},
{0x7d4c,0x7d4c,0x726},
{0x7d4e,0x7d4e,0x17bd},
{0x7d4f,0x7d4f,0x17c1},
{0x7d50,0x7d50,0x741},
{0x7d53,0x7d53,0x3a7f},
{0x7d56,0x7d56,0x17bc},
{0x7d59,0x7d59,0x3a80},
{0x7d5b,0x7d5b,0x17c5},
{0x7d5d,0x7d5d,0x3a81},
{0x7d5e,0x7d5e,0x7dc},
{0x7d61,0x7d61,0xf57},
{0x7d62,0x7d62,0x480},
{0x7d63,0x7d63,0x17c2},
{0x7d66,0x7d66,0x685},
{0x7d68,0x7d68,0x17bf},
{0x7d6e,0x7d6e,0x17c0},
{0x7d71,0x7d71,0xc77},
{0x7d72,0x7d72,0x17be},
{0x7d73,0x7d73,0x17bb},
{0x7d75,0x7d75,0x586},
{0x7d76,0x7d76,0xa88},
{0x7d79,0x7d79,0x75c},
{0x7d7a,0x7d7a,0x3a82},
{0x7d7d,0x7d7d,0x17c7},
{0x7d86,0x7d86,0x3a83},
{0x7d89,0x7d89,0x17c4},
{0x7d8b,0x7d8c,0x3a84},
{0x7d8f,0x7d8f,0x17c6},
{0x7d93,0x7d93,0x17c3},
{0x7d99,0x7d99,0x727},
{0x7d9a,0x7d9a,0xb13},
{0x7d9b,0x7d9b,0x17c8},
{0x7d9c,0x7d9c,0xaf0},
{0x7d9f,0x7d9f,0x17d5},
{0x7da0,0x7da0,0x2192},
{0x7da2,0x7da2,0x17d1},
{0x7da3,0x7da3,0x17cb},
{0x7dab,0x7dab,0x17cf},
{0x7dac,0x7dac,0x926},
{0x7dad,0x7dad,0x4a1},
{0x7dae,0x7dae,0x17ca},
{0x7daf,0x7daf,0x17d2},
{0x7db0,0x7db0,0x17d6},
{0x7db1,0x7db1,0x7dd},
{0x7db2,0x7db2,0xee2},
{0x7db4,0x7db4,0xbf2},
{0x7db5,0x7db5,0x17cc},
{0x7db7,0x7db7,0x2191},
{0x7db8,0x7db8,0x17d4},
{0x7dba,0x7dba,0x17c9},
{0x7dbb,0x7dbb,0xb7c},
{0x7dbd,0x7dbd,0x17ce},
{0x7dbe,0x7dbe,0x481},
{0x7dbf,0x7dbf,0xed6},
{0x7dc7,0x7dc7,0x17cd},
{0x7dca,0x7dca,0x6d3},
{0x7dcb,0x7dcb,0xd80},
{0x7dcc,0x7dcc,0x3a86},
{0x7dcf,0x7dcf,0xaef},
{0x7dd1,0x7dd1,0xf98},
{0x7dd2,0x7dd2,0x979},
{0x7dd5,0x7dd5,0x17fd},
{0x7dd6,0x7dd6,0x2193},
{0x7dd8,0x7dd8,0x17d7},
{0x7dda,0x7dda,0xaa2},
{0x7ddc,0x7ddc,0x17d3},
{0x7ddd,0x7ddd,0x17d8},
{0x7dde,0x7dde,0x17da},
{0x7de0,0x7de0,0xc15},
{0x7de1,0x7de1,0x17dd},
{0x7de3,0x7de3,0x340a},
{0x7de4,0x7de4,0x17d9},
{0x7de8,0x7de8,0xe24},
{0x7de9,0x7de9,0x607},
{0x7deb,0x7deb,0x3a87},
{0x7dec,0x7dec,0xed7},
{0x7def,0x7def,0x4a2},
{0x7df1,0x7df1,0x3a88},
{0x7df2,0x7df2,0x17dc},
{0x7df4,0x7df4,0xfc5},
{0x7df9,0x7df9,0x3a89},
{0x7dfb,0x7dfb,0x17db},
{0x7e01,0x7e01,0x511},
{0x7e04,0x7e04,0xcc4},
{0x7e05,0x7e05,0x17de},
{0x7e08,0x7e08,0x3a8a},
{0x7e09,0x7e09,0x17e5},
{0x7e0a,0x7e0a,0x17df},
{0x7e0b,0x7e0b,0x17e6},
{0x7e11,0x7e11,0x3a8b},
{0x7e12,0x7e12,0x17e2},
{0x7e15,0x7e15,0x3a8c},
{0x7e1b,0x7e1b,0xd31},
{0x7e1e,0x7e1e,0x8f6},
{0x7e1f,0x7e1f,0x17e4},
{0x7e20,0x7e20,0x3a8d},
{0x7e21,0x7e21,0x17e1},
{0x7e22,0x7e22,0x17e7},
{0x7e23,0x7e23,0x17e0},
{0x7e26,0x7e26,0x94e},
{0x7e2b,0x7e2b,0xe53},
{0x7e2e,0x7e2e,0x956},
{0x7e31,0x7e31,0x17e3},
{0x7e32,0x7e32,0x17ef},
{0x7e35,0x7e35,0x17eb},
{0x7e37,0x7e37,0x17ee},
{0x7e39,0x7e39,0x17ec},
{0x7e3a,0x7e3a,0x17f0},
{0x7e3b,0x7e3b,0x17ea},
{0x7e3d,0x7e3d,0x17d0},
{0x7e3e,0x7e3e,0xa77},
{0x7e41,0x7e41,0xd5f},
{0x7e43,0x7e43,0x17ed},
{0x7e46,0x7e46,0x17e8},
{0x7e47,0x7e47,0x3a8e},
{0x7e48,0x7e48,0x3767},
{0x7e4a,0x7e4a,0xaa3},
{0x7e4b,0x7e4b,0x728},
{0x7e4d,0x7e4d,0x935},
{0x7e52,0x7e52,0x2194},
{0x7e54,0x7e54,0x9eb},
{0x7e55,0x7e55,0xab8},
{0x7e56,0x7e56,0x17f3},
{0x7e59,0x7e5a,0x17f5},
{0x7e5d,0x7e5d,0x17f2},
{0x7e5e,0x7e5e,0x17f4},
{0x7e61,0x7e61,0x1e11},
{0x7e62,0x7e62,0x3a8f},
{0x7e66,0x7e66,0x17e9},
{0x7e67,0x7e67,0x17f1},
{0x7e69,0x7e69,0x17f9},
{0x7e6a,0x7e6a,0x17f8},
{0x7e6b,0x7e6b,0x1df7},
{0x7e6d,0x7e6d,0xea8},
{0x7e6e,0x7e6e,0x3a90},
{0x7e70,0x7e70,0x701},
{0x7e73,0x7e73,0x3a91},
{0x7e79,0x7e79,0x17f7},
{0x7e7b,0x7e7b,0x17fb},
{0x7e7c,0x7e7c,0x17fa},
{0x7e7d,0x7e7d,0x17fe},
{0x7e7f,0x7e7f,0x1800},
{0x7e82,0x7e82,0x88a},
{0x7e83,0x7e83,0x17fc},
{0x7e88,0x7e89,0x1801},
{0x7e8a,0x7e8a,0x20a7},
{0x7e8c,0x7e8c,0x1803},
{0x7e8d,0x7e8d,0x3a92},
{0x7e8e,0x7e8e,0x1809},
{0x7e8f,0x7e8f,0xc35},
{0x7e90,0x7e90,0x1805},
{0x7e91,0x7e91,0x3a93},
{0x7e92,0x7e92,0x1804},
{0x7e93,0x7e94,0x1806},
{0x7e96,0x7e96,0x1808},
{0x7e98,0x7e98,0x3a94},
{0x7e9b,0x7e9c,0x180a},
{0x7f36,0x7f36,0x608},
{0x7f38,0x7f38,0x180c},
{0x7f3a,0x7f3a,0x180d},
{0x7f44,0x7f44,0x3a95},
{0x7f45,0x7f45,0x180e},
{0x7f47,0x7f47,0x2195},
{0x7f4c,0x7f4e,0x180f},
{0x7f4f,0x7f4f,0x3a96},
{0x7f50,0x7f51,0x1812},
{0x7f52,0x7f53,0x3a97},
{0x7f54,0x7f54,0x1815},
{0x7f55,0x7f55,0x1814},
{0x7f58,0x7f58,0x1816},
{0x7f5f,0x7f60,0x1817},
{0x7f61,0x7f61,0x3a99},
{0x7f67,0x7f67,0x181b},
{0x7f68,0x7f69,0x1819},
{0x7f6a,0x7f6a,0x851},
{0x7f6b,0x7f6b,0x729},
{0x7f6e,0x7f6e,0xb94},
{0x7f70,0x7f70,0xd47},
{0x7f72,0x7f72,0x97a},
{0x7f75,0x7f75,0xd03},
{0x7f77,0x7f77,0xd81},
{0x7f78,0x7f78,0x181c},
{0x7f79,0x7f79,0x1336},
{0x7f82,0x7f82,0x181d},
{0x7f83,0x7f83,0x181f},
{0x7f85,0x7f85,0xf4f},
{0x7f86,0x7f86,0x181e},
{0x7f87,0x7f87,0x1821},
{0x7f88,0x7f88,0x1820},
{0x7f8a,0x7f8a,0xf3d},
{0x7f8c,0x7f8c,0x1822},
{0x7f8e,0x7f8e,0xd92},
{0x7f91,0x7f91,0x3a9a},
{0x7f94,0x7f94,0x1823},
{0x7f9a,0x7f9a,0x1826},
{0x7f9d,0x7f9d,0x1825},
{0x7f9e,0x7f9e,0x1824},
{0x7fa1,0x7fa1,0x2196},
{0x7fa3,0x7fa3,0x1827},
{0x7fa4,0x7fa4,0x708},
{0x7fa8,0x7fa8,0xaa4},
{0x7fa9,0x7fa9,0x65b},
{0x7fae,0x7fae,0x182b},
{0x7faf,0x7faf,0x1828},
{0x7fb2,0x7fb2,0x1829},
{0x7fb6,0x7fb6,0x182c},
{0x7fb8,0x7fb8,0x182d},
{0x7fb9,0x7fb9,0x182a},
{0x7fbd,0x7fbd,0x4cb},
{0x7fbf,0x7fbf,0x3a9b},
{0x7fc1,0x7fc1,0x527},
{0x7fc5,0x7fc6,0x182f},
{0x7fca,0x7fca,0x1831},
{0x7fcc,0x7fcc,0xf4c},
{0x7fce,0x7fce,0x3a9c},
{0x7fd2,0x7fd2,0x936},
{0x7fd4,0x7fd4,0x1833},
{0x7fd5,0x7fd5,0x1832},
{0x7fdf,0x7fdf,0x3a9d},
{0x7fe0,0x7fe0,0xa2f},
{0x7fe1,0x7fe1,0x1834},
{0x7fe5,0x7fe5,0x3a9e},
{0x7fe6,0x7fe6,0x1835},
{0x7fe9,0x7fe9,0x1836},
{0x7feb,0x7feb,0x621},
{0x7fec,0x7fec,0x3a9f},
{0x7fee,0x7fef,0x3aa0},
{0x7ff0,0x7ff0,0x609},
{0x7ff3,0x7ff3,0x1837},
{0x7ff9,0x7ff9,0x1838},
{0x7ffa,0x7ffa,0x3aa2},
{0x7ffb,0x7ffb,0xe8b},
{0x7ffc,0x7ffc,0xf4d},
{0x8000,0x8000,0xf3e},
{0x8001,0x8001,0xfdd},
{0x8002,0x8002,0x3713},
{0x8003,0x8003,0x7df},
{0x8004,0x8004,0x183b},
{0x8005,0x8005,0x900},
{0x8006,0x8006,0x183a},
{0x800b,0x800b,0x183c},
{0x800c,0x800c,0x8d5},
{0x800e,0x800e,0x3aa3},
{0x8010,0x8010,0xb31},
{0x8011,0x8011,0x3aa4},
{0x8012,0x8012,0x183d},
{0x8014,0x8014,0x3aa5},
{0x8015,0x8015,0x7de},
{0x8017,0x8017,0xee3},
{0x8018,0x8019,0x183e},
{0x801c,0x801c,0x1840},
{0x8021,0x8021,0x1841},
{0x8024,0x8024,0x3aa6},
{0x8026,0x8026,0x3aa7},
{0x8028,0x8028,0x1842},
{0x8033,0x8033,0x8d6},
{0x8036,0x8036,0xef9},
{0x803a,0x803a,0x3aa8},
{0x803b,0x803b,0x1844},
{0x803c,0x803c,0x3aa9},
{0x803d,0x803d,0xb7d},
{0x803f,0x803f,0x1843},
{0x8046,0x8046,0x1846},
{0x804a,0x804a,0x1845},
{0x8052,0x8052,0x1847},
{0x8056,0x8056,0xa5f},
{0x8058,0x8058,0x1848},
{0x805a,0x805a,0x1849},
{0x805e,0x805e,0xe09},
{0x805f,0x805f,0x184a},
{0x8060,0x8060,0x3aaa},
{0x8061,0x8061,0xaf1},
{0x8062,0x8062,0x184b},
{0x8068,0x8068,0x184c},
{0x806f,0x806f,0xfc6},
{0x8070,0x8070,0x184f},
{0x8071,0x8071,0x3aab},
{0x8072,0x8072,0x184e},
{0x8073,0x8073,0x184d},
{0x8074,0x8074,0xbcc},
{0x8075,0x8075,0x3aac},
{0x8076,0x8076,0x1850},
{0x8077,0x8077,0x9ec},
{0x8079,0x8079,0x1851},
{0x807d,0x807d,0x1852},
{0x807e,0x807e,0xfde},
{0x807f,0x807f,0x1853},
{0x8084,0x8084,0x1854},
{0x8085,0x8085,0x1856},
{0x8086,0x8086,0x1855},
{0x8087,0x8087,0xd39},
{0x8089,0x8089,0xcd1},
{0x808b,0x808b,0xfe4},
{0x808c,0x808c,0xd3d},
{0x8093,0x8093,0x1858},
{0x8096,0x8096,0x9bb},
{0x8098,0x8098,0xd9c},
{0x809a,0x809a,0x1859},
{0x809b,0x809b,0x1857},
{0x809d,0x809d,0x60a},
{0x809e,0x809e,0x3aad},
{0x80a1,0x80a1,0x788},
{0x80a2,0x80a2,0x8b6},
{0x80a5,0x80a5,0xd82},
{0x80a6,0x80a6,0x3aae},
{0x80a9,0x80a9,0x75e},
{0x80aa,0x80aa,0xe71},
{0x80ab,0x80ab,0x3aaf},
{0x80ac,0x80ac,0x185c},
{0x80ad,0x80ad,0x185a},
{0x80af,0x80af,0x7e0},
{0x80b1,0x80b1,0x7e1},
{0x80b2,0x80b2,0x4ad},
{0x80b4,0x80b4,0x858},
{0x80ba,0x80ba,0xd0f},
{0x80c3,0x80c3,0x4a3},
{0x80c4,0x80c4,0x1861},
{0x80c6,0x80c6,0xb7e},
{0x80cc,0x80cc,0xd0e},
{0x80ce,0x80ce,0xb3b},
{0x80d6,0x80d6,0x1863},
{0x80d7,0x80d8,0x3ab0},
{0x80d9,0x80d9,0x185f},
{0x80da,0x80da,0x1862},
{0x80db,0x80db,0x185d},
{0x80dd,0x80dd,0x1860},
{0x80de,0x80de,0xe54},
{0x80e1,0x80e1,0x789},
{0x80e4,0x80e4,0x4c1},
{0x80e5,0x80e5,0x185e},
{0x80ef,0x80ef,0x1865},
{0x80f1,0x80f1,0x1866},
{0x80f4,0x80f4,0xc91},
{0x80f8,0x80f8,0x6b3},
{0x80fc,0x80fc,0x1871},
{0x80fd,0x80fd,0xcf3},
{0x8102,0x8102,0x8b7},
{0x8105,0x8105,0x6b4},
{0x8106,0x8106,0xa6c},
{0x8107,0x8107,0xfec},
{0x8108,0x8108,0xeba},
{0x8109,0x8109,0x1864},
{0x810a,0x810a,0xa78},
{0x8116,0x8116,0x3ab2},
{0x8118,0x8118,0x3ab3},
{0x811a,0x811a,0x66d},
{0x811b,0x811b,0x1867},
{0x8123,0x8123,0x1869},
{0x8129,0x8129,0x1868},
{0x812b,0x812b,0x3659},
{0x812f,0x812f,0x186a},
{0x8131,0x8131,0xb64},
{0x8133,0x8133,0xcf4},
{0x8139,0x8139,0xbcd},
{0x813a,0x813a,0x3ab4},
{0x813e,0x813e,0x186e},
{0x8141,0x8141,0x3772},
{0x8146,0x8146,0x186d},
{0x814a,0x814a,0x3ab5},
{0x814b,0x814b,0x186b},
{0x814c,0x814c,0x3ab6},
{0x814e,0x814e,0xa1b},
{0x8150,0x8150,0xdd7},
{0x8151,0x8151,0x1870},
{0x8153,0x8153,0x186f},
{0x8154,0x8154,0x7e2},
{0x8155,0x8155,0xff9},
{0x815f,0x815f,0x1880},
{0x8165,0x8166,0x1874},
{0x816b,0x816b,0x91c},
{0x816e,0x816e,0x1873},
{0x8170,0x8170,0x80a},
{0x8171,0x8171,0x1872},
{0x8174,0x8174,0x1876},
{0x8178,0x8178,0xbce},
{0x8179,0x8179,0xdf2},
{0x817a,0x817a,0xaa5},
{0x817f,0x817f,0xb3c},
{0x8180,0x8180,0x187a},
{0x8181,0x8181,0x3ab7},
{0x8182,0x8182,0x187b},
{0x8183,0x8183,0x1877},
{0x8184,0x8184,0x3ab8},
{0x8188,0x8188,0x1878},
{0x818a,0x818a,0x1879},
{0x818f,0x818f,0x7e3},
{0x8193,0x8193,0x1881},
{0x8195,0x8195,0x187d},
{0x819a,0x819a,0xdd8},
{0x819c,0x819c,0xe9a},
{0x819d,0x819d,0xd9a},
{0x81a0,0x81a0,0x187c},
{0x81a3,0x81a3,0x187f},
{0x81a4,0x81a4,0x187e},
{0x81a8,0x81a8,0xe72},
{0x81a9,0x81a9,0x1882},
{0x81b0,0x81b0,0x1883},
{0x81b3,0x81b3,0xab9},
{0x81b4,0x81b4,0x3ab9},
{0x81b5,0x81b5,0x1884},
{0x81b8,0x81b8,0x1886},
{0x81ba,0x81ba,0x188a},
{0x81bd,0x81bd,0x1887},
{0x81be,0x81be,0x1885},
{0x81bf,0x81bf,0xcf5},
{0x81c0,0x81c0,0x1888},
{0x81c2,0x81c2,0x1889},
{0x81c6,0x81c6,0x532},
{0x81c8,0x81c8,0x1890},
{0x81c9,0x81c9,0x188b},
{0x81cd,0x81cd,0x188c},
{0x81cf,0x81cf,0x3aba},
{0x81d1,0x81d1,0x188d},
{0x81d3,0x81d3,0xb01},
{0x81d8,0x81d8,0x188f},
{0x81d9,0x81d9,0x188e},
{0x81da,0x81da,0x1891},
{0x81df,0x81e0,0x1892},
{0x81e3,0x81e3,0xa09},
{0x81e5,0x81e5,0x569},
{0x81e7,0x81e7,0x1894},
{0x81e8,0x81e8,0xf9f},
{0x81ea,0x81ea,0x8d7},
{0x81ed,0x81ed,0x937},
{0x81f3,0x81f3,0x8b8},
{0x81f4,0x81f4,0xb95},
{0x81f9,0x81f9,0x3abb},
{0x81fa,0x81fb,0x1895},
{0x81fc,0x81fc,0x4d3},
{0x81fe,0x81fe,0x1897},
{0x8201,0x8202,0x1898},
{0x8203,0x8203,0x3abc},
{0x8205,0x8205,0x189a},
{0x8207,0x8207,0x189b},
{0x8208,0x8208,0x6b5},
{0x8209,0x8209,0x13af},
{0x820a,0x820a,0x189c},
{0x820c,0x820c,0xa89},
{0x820d,0x820d,0x189d},
{0x820e,0x820e,0x8f7},
{0x8210,0x8210,0x189e},
{0x8212,0x8212,0x1009},
{0x8216,0x8216,0x189f},
{0x8217,0x8217,0xe2e},
{0x8218,0x8218,0x618},
{0x821b,0x821b,0xaa6},
{0x821c,0x821c,0x962},
{0x821e,0x821e,0xde3},
{0x821f,0x821f,0x938},
{0x8221,0x8221,0x3abd},
{0x8229,0x8229,0x18a0},
{0x822a,0x822a,0x7e4},
{0x822b,0x822b,0x18a1},
{0x822c,0x822c,0xd60},
{0x822e,0x822e,0x18af},
{0x8232,0x8232,0x3abe},
{0x8233,0x8233,0x18a3},
{0x8234,0x8234,0x3abf},
{0x8235,0x8235,0xb29},
{0x8236,0x8236,0xd2b},
{0x8237,0x8237,0x773},
{0x8238,0x8238,0x18a2},
{0x8239,0x8239,0xaa7},
{0x8240,0x8240,0x18a4},
{0x8246,0x8246,0x3ac0},
{0x8247,0x8247,0xc16},
{0x824b,0x824b,0x3ac1},
{0x824f,0x824f,0x3ac2},
{0x8258,0x8258,0x18a6},
{0x8259,0x8259,0x18a5},
{0x825a,0x825a,0x18a8},
{0x825d,0x825d,0x18a7},
{0x825f,0x825f,0x18a9},
{0x8262,0x8262,0x18ab},
{0x8264,0x8264,0x18aa},
{0x8266,0x8266,0x60b},
{0x8268,0x8268,0x18ac},
{0x826a,0x826b,0x18ad},
{0x826e,0x826e,0x821},
{0x826f,0x826f,0xf91},
{0x8271,0x8271,0x18b0},
{0x8272,0x8272,0x9ed},
{0x8276,0x8276,0x512},
{0x8277,0x8278,0x18b1},
{0x8279,0x8279,0x3775},
{0x827e,0x827e,0x18b3},
{0x828b,0x828b,0x4b6},
{0x828d,0x828d,0x18b4},
{0x828e,0x828e,0x3ac3},
{0x8292,0x8292,0x18b5},
{0x8299,0x8299,0xdd9},
{0x829d,0x829d,0x8f3},
{0x829f,0x829f,0x18b7},
{0x82a5,0x82a5,0x587},
{0x82a6,0x82a6,0x476},
{0x82ab,0x82ab,0x18b6},
{0x82ac,0x82ac,0x18b9},
{0x82ad,0x82ad,0xd04},
{0x82ae,0x82ae,0x3ac4},
{0x82af,0x82af,0xa0a},
{0x82b1,0x82b1,0x556},
{0x82b3,0x82b3,0xe55},
{0x82b7,0x82b7,0x3ac5},
{0x82b8,0x82b8,0x733},
{0x82b9,0x82b9,0x6d4},
{0x82bb,0x82bb,0x18b8},
{0x82bd,0x82bd,0x56a},
{0x82be,0x82be,0x3ac6},
{0x82c5,0x82c5,0x5df},
{0x82c6,0x82c6,0x3ac7},
{0x82d1,0x82d1,0x513},
{0x82d2,0x82d2,0x18bd},
{0x82d3,0x82d3,0xfb2},
{0x82d4,0x82d4,0xb3d},
{0x82d7,0x82d7,0xdb6},
{0x82d9,0x82d9,0x18c9},
{0x82db,0x82db,0x557},
{0x82dc,0x82dc,0x18c7},
{0x82de,0x82de,0x18c5},
{0x82df,0x82df,0x18bc},
{0x82e1,0x82e1,0x18ba},
{0x82e3,0x82e3,0x18bb},
{0x82e5,0x82e5,0x90f},
{0x82e6,0x82e6,0x6e4},
{0x82e7,0x82e7,0xbb5},
{0x82eb,0x82eb,0xca9},
{0x82f1,0x82f1,0x4f3},
{0x82f3,0x82f3,0x18bf},
{0x82f4,0x82f4,0x18be},
{0x82f9,0x82f9,0x18c4},
{0x82fa,0x82fa,0x18c0},
{0x82fb,0x82fb,0x18c3},
{0x82fe,0x82fe,0x3ac8},
{0x8301,0x8301,0x2198},
{0x8302,0x8302,0xedc},
{0x8303,0x8303,0x18c2},
{0x8304,0x8304,0x558},
{0x8305,0x8305,0x5db},
{0x8306,0x8306,0x18c6},
{0x8309,0x8309,0x18c8},
{0x830e,0x830e,0x72a},
{0x8316,0x8316,0x18cc},
{0x8317,0x8318,0x18d5},
{0x831c,0x831c,0x46f},
{0x8323,0x8323,0x18dd},
{0x8328,0x8328,0x4b5},
{0x832b,0x832b,0x18d4},
{0x832f,0x832f,0x18d3},
{0x8331,0x8331,0x18ce},
{0x8332,0x8332,0x18cd},
{0x8334,0x8334,0x18cb},
{0x8335,0x8335,0x18ca},
{0x8336,0x8336,0xba1},
{0x8338,0x8338,0xb5b},
{0x8339,0x8339,0x18d0},
{0x8340,0x8340,0x18cf},
{0x8343,0x8343,0x3ac9},
{0x8345,0x8345,0x18d2},
{0x8349,0x8349,0xaf2},
{0x834a,0x834a,0x72b},
{0x834f,0x834f,0x4e3},
{0x8350,0x8350,0x18d1},
{0x8351,0x8351,0x3aca},
{0x8352,0x8352,0x7e5},
{0x8355,0x8355,0x3acb},
{0x8358,0x8358,0xaf3},
{0x8362,0x8362,0x2199},
{0x8373,0x8373,0x18e3},
{0x8375,0x8375,0x18e4},
{0x8377,0x8377,0x559},
{0x837b,0x837b,0x52e},
{0x837c,0x837c,0x18e1},
{0x837f,0x837f,0x219a},
{0x8385,0x8385,0x18d7},
{0x8386,0x8386,0x3acc},
{0x8387,0x8387,0x18df},
{0x8389,0x8389,0x18e6},
{0x838a,0x838a,0x18e0},
{0x838d,0x838d,0x3acd},
{0x838e,0x838e,0x18de},
{0x8392,0x8392,0x3ace},
{0x8393,0x8393,0x18c1},
{0x8396,0x8396,0x18dc},
{0x8398,0x8398,0x3acf},
{0x839a,0x839a,0x18d8},
{0x839e,0x839e,0x60c},
{0x839f,0x839f,0x18da},
{0x83a0,0x83a0,0x18e5},
{0x83a2,0x83a2,0x18db},
{0x83a8,0x83a8,0x18e7},
{0x83a9,0x83a9,0x3ad0},
{0x83aa,0x83aa,0x18d9},
{0x83ab,0x83ab,0xd32},
{0x83b1,0x83b1,0xf53},
{0x83b5,0x83b5,0x18e2},
{0x83bd,0x83bd,0x18f8},
{0x83bf,0x83c0,0x3ad1},
{0x83c1,0x83c1,0x18f0},
{0x83c5,0x83c5,0xa41},
{0x83c7,0x83c7,0x219b},
{0x83ca,0x83ca,0x660},
{0x83cc,0x83cc,0x6d5},
{0x83ce,0x83ce,0x18eb},
{0x83d3,0x83d3,0x55b},
{0x83d6,0x83d6,0x9bc},
{0x83d8,0x83d8,0x18ee},
{0x83dc,0x83dc,0x84a},
{0x83df,0x83df,0xc4b},
{0x83e0,0x83e0,0x18f3},
{0x83e9,0x83e9,0xe3e},
{0x83ea,0x83ea,0x3ad3},
{0x83eb,0x83eb,0x18ea},
{0x83ef,0x83ef,0x55a},
{0x83f0,0x83f0,0x78a},
{0x83f1,0x83f1,0xd9b},
{0x83f2,0x83f2,0x18f4},
{0x83f4,0x83f4,0x18e8},
{0x83f6,0x83f6,0x219c},
{0x83f7,0x83f7,0x18f1},
{0x83fb,0x83fb,0x18fb},
{0x83fd,0x83fd,0x18ec},
{0x8403,0x8403,0x18ed},
{0x8404,0x8404,0xc92},
{0x8407,0x8407,0x18f2},
{0x840a,0x840a,0x1e7f},
{0x840b,0x840b,0x18ef},
{0x840c,0x840c,0xe56},
{0x840d,0x840d,0x18f5},
{0x840e,0x840e,0x4a4},
{0x840f,0x840f,0x3ad4},
{0x8411,0x8411,0x3ad5},
{0x8413,0x8413,0x18e9},
{0x8420,0x8420,0x18f7},
{0x8422,0x8422,0x18f6},
{0x8429,0x8429,0xd21},
{0x842a,0x842a,0x18fd},
{0x842c,0x842c,0x1908},
{0x8431,0x8431,0x5dc},
{0x8435,0x8435,0x190b},
{0x8438,0x8438,0x18f9},
{0x843c,0x843c,0x18fe},
{0x843d,0x843d,0xf58},
{0x8446,0x8446,0x1907},
{0x8448,0x8448,0x219d},
{0x8449,0x8449,0xf3f},
{0x844a,0x844a,0x3ad6},
{0x844e,0x844e,0xf72},
{0x8457,0x8457,0xbb6},
{0x845b,0x845b,0x5c9},
{0x8461,0x8461,0xde4},
{0x8462,0x8462,0x190d},
{0x8463,0x8463,0xc79},
{0x8466,0x8466,0x475},
{0x8469,0x8469,0x1906},
{0x846b,0x846b,0x1902},
{0x846c,0x846c,0xaf4},
{0x846d,0x846d,0x18fc},
{0x846e,0x846e,0x1904},
{0x846f,0x846f,0x1909},
{0x8471,0x8471,0xce2},
{0x8475,0x8475,0x46e},
{0x8476,0x8476,0x3ad7},
{0x8477,0x8477,0x1901},
{0x8479,0x8479,0x190a},
{0x847a,0x847a,0xdea},
{0x8482,0x8482,0x1905},
{0x8484,0x8484,0x1900},
{0x848b,0x848b,0x9bd},
{0x8490,0x8490,0x939},
{0x8494,0x8494,0x8d8},
{0x8499,0x8499,0xee4},
{0x849c,0x849c,0xdb9},
{0x849f,0x849f,0x1910},
{0x84a1,0x84a1,0x1919},
{0x84a8,0x84a8,0x3ad8},
{0x84ad,0x84ad,0x1903},
{0x84af,0x84af,0x3ad9},
{0x84b2,0x84b2,0x5d5},
{0x84b4,0x84b4,0x219e},
{0x84b8,0x84b8,0x9e0},
{0x84b9,0x84b9,0x190e},
{0x84bb,0x84bb,0x1913},
{0x84bc,0x84bc,0xaf5},
{0x84bf,0x84bf,0x190f},
{0x84c0,0x84c0,0x3ada},
{0x84c1,0x84c1,0x1916},
{0x84c2,0x84c2,0x3adb},
{0x84c4,0x84c4,0xb9d},
{0x84c6,0x84c6,0x1917},
{0x84c9,0x84c9,0xf40},
{0x84ca,0x84ca,0x190c},
{0x84cb,0x84cb,0x596},
{0x84cd,0x84cd,0x1912},
{0x84d0,0x84d0,0x1915},
{0x84d1,0x84d1,0xeb8},
{0x84d6,0x84d6,0x1918},
{0x84d9,0x84d9,0x1911},
{0x84da,0x84da,0x1914},
{0x84dc,0x84dc,0x20ab},
{0x84ec,0x84ec,0xe57},
{0x84ee,0x84ee,0xfc7},
{0x84f0,0x84f0,0x3adc},
{0x84f4,0x84f4,0x191c},
{0x84fc,0x84fc,0x1923},
{0x84fd,0x84fd,0x3add},
{0x84ff,0x84ff,0x191b},
{0x8500,0x8500,0x8ef},
{0x8506,0x8506,0x18fa},
{0x850c,0x850c,0x3ade},
{0x8511,0x8511,0xe1e},
{0x8513,0x8513,0xeae},
{0x8514,0x8514,0x1922},
{0x8515,0x8515,0x1921},
{0x8517,0x8518,0x191d},
{0x851a,0x851a,0x4d8},
{0x851f,0x851f,0x1920},
{0x8521,0x8521,0x191a},
{0x8523,0x8523,0x1e1a},
{0x8526,0x8526,0xbf1},
{0x852c,0x852c,0x191f},
{0x852d,0x852d,0x4c2},
{0x8534,0x8534,0x3adf},
{0x8535,0x8535,0xb02},
{0x853d,0x853d,0xe13},
{0x853e,0x853e,0x1eb5},
{0x8540,0x8540,0x1924},
{0x8541,0x8541,0x1928},
{0x8543,0x8543,0xd6d},
{0x8548,0x8548,0x1927},
{0x8549,0x8549,0x9be},
{0x854a,0x854a,0x8f5},
{0x854b,0x854b,0x192a},
{0x854e,0x854e,0x6b6},
{0x8553,0x8553,0x219f},
{0x8555,0x8555,0x192b},
{0x8557,0x8557,0xdeb},
{0x8558,0x8558,0x1926},
{0x8559,0x8559,0x21a0},
{0x855a,0x855a,0x18ff},
{0x855e,0x855e,0x3ae0},
{0x8563,0x8563,0x1925},
{0x8568,0x8568,0xff5},
{0x8569,0x8569,0xc7a},
{0x856a,0x856a,0xde5},
{0x856b,0x856b,0x21a1},
{0x856d,0x856d,0x1932},
{0x8577,0x8577,0x1938},
{0x857e,0x857e,0x1939},
{0x8580,0x8580,0x192c},
{0x8584,0x8584,0xd2c},
{0x8587,0x8587,0x1936},
{0x8588,0x8588,0x192e},
{0x858a,0x858a,0x1930},
{0x858f,0x858f,0x3ae1},
{0x8590,0x8590,0x193a},
{0x8591,0x8591,0x192f},
{0x8594,0x8594,0x1933},
{0x8597,0x8597,0x514},
{0x8599,0x8599,0xcbd},
{0x859b,0x859b,0x1934},
{0x859c,0x859c,0x1937},
{0x85a4,0x85a4,0x192d},
{0x85a6,0x85a6,0xaa8},
{0x85a8,0x85a8,0x1931},
{0x85a9,0x85a9,0x875},
{0x85aa,0x85aa,0xa0b},
{0x85ab,0x85ab,0x706},
{0x85ac,0x85ac,0xf00},
{0x85ad,0x85ad,0x3ae4},
{0x85ae,0x85ae,0xf05},
{0x85af,0x85af,0x97c},
{0x85b0,0x85b0,0x21a3},
{0x85b7,0x85b7,0x3ae2},
{0x85b9,0x85b9,0x193e},
{0x85ba,0x85ba,0x193c},
{0x85c1,0x85c1,0xff4},
{0x85c9,0x85c9,0x193b},
{0x85cd,0x85cd,0xf5f},
{0x85ce,0x85ce,0x3ae3},
{0x85cf,0x85cf,0x193d},
{0x85d0,0x85d0,0x193f},
{0x85d5,0x85d5,0x1940},
{0x85dc,0x85dc,0x1943},
{0x85dd,0x85dd,0x1941},
{0x85e4,0x85e4,0xc7b},
{0x85e5,0x85e5,0x1942},
{0x85e9,0x85e9,0xd61},
{0x85ea,0x85ea,0x1935},
{0x85f4,0x85f4,0x3780},
{0x85f7,0x85f7,0x97d},
{0x85f9,0x85f9,0x1944},
{0x85fa,0x85fa,0x1949},
{0x85fb,0x85fb,0xaf6},
{0x85fe,0x85fe,0x1948},
{0x8602,0x8602,0x1929},
{0x8606,0x8606,0x194a},
{0x8607,0x8607,0xacb},
{0x860a,0x860a,0x1945},
{0x860b,0x860b,0x1947},
{0x8612,0x8612,0x3ae5},
{0x8613,0x8613,0x1946},
{0x8616,0x8616,0x14d0},
{0x8617,0x8617,0x14c1},
{0x861a,0x861a,0x194c},
{0x8622,0x8622,0x194b},
{0x8629,0x8629,0x3ae6},
{0x862d,0x862d,0xf60},
{0x862f,0x862f,0x16b1},
{0x8630,0x8630,0x194d},
{0x863f,0x863f,0x194e},
{0x864d,0x864d,0x194f},
{0x864e,0x864e,0x78b},
{0x8650,0x8650,0x66e},
{0x8652,0x8652,0x3ae7},
{0x8654,0x8654,0x1951},
{0x8655,0x8655,0x1094},
{0x865a,0x865a,0x68f},
{0x865b,0x865b,0x3418},
{0x865c,0x865c,0xf82},
{0x865e,0x865e,0x6eb},
{0x865f,0x865f,0x1952},
{0x8663,0x8663,0x3ae8},
{0x8667,0x8667,0x1953},
{0x866b,0x866b,0xbac},
{0x866c,0x866c,0x3ae9},
{0x866f,0x866f,0x3aea},
{0x8671,0x8671,0x1954},
{0x8679,0x8679,0xcd2},
{0x867a,0x867a,0x3aeb},
{0x867b,0x867b,0x47e},
{0x867d,0x867d,0x3786},
{0x868a,0x868a,0x563},
{0x868b,0x868c,0x1959},
{0x868d,0x868d,0x3aec},
{0x8691,0x8691,0x3aed},
{0x8693,0x8693,0x1955},
{0x8695,0x8695,0x88b},
{0x8698,0x8698,0x3aee},
{0x86a3,0x86a3,0x1956},
{0x86a4,0x86a4,0xcf8},
{0x86a7,0x86a8,0x3aef},
{0x86a9,0x86aa,0x1957},
{0x86ab,0x86ab,0x1962},
{0x86af,0x86af,0x195c},
{0x86b0,0x86b0,0x195f},
{0x86b6,0x86b6,0x195b},
{0x86c4,0x86c4,0x195d},
{0x86c6,0x86c6,0x195e},
{0x86c7,0x86c7,0x904},
{0x86c9,0x86c9,0x1960},
{0x86cb,0x86cb,0xb7f},
{0x86cd,0x86cd,0x72c},
{0x86ce,0x86ce,0x5a0},
{0x86d4,0x86d4,0x1963},
{0x86d9,0x86d9,0x59d},
{0x86db,0x86db,0x1968},
{0x86de,0x86de,0x1964},
{0x86df,0x86df,0x1967},
{0x86e4,0x86e4,0xd4e},
{0x86e9,0x86e9,0x1965},
{0x86ec,0x86ec,0x1966},
{0x86ed,0x86ed,0xdba},
{0x86ee,0x86ee,0xd6e},
{0x86ef,0x86ef,0x1969},
{0x86f8,0x86f8,0xb5d},
{0x86f9,0x86f9,0x1973},
{0x86fa,0x86fa,0x3af1},
{0x86fb,0x86fb,0x196f},
{0x86fd,0x86fd,0x3af2},
{0x86fe,0x86fe,0x56b},
{0x8700,0x8700,0x196d},
{0x8702,0x8702,0xe58},
{0x8703,0x8703,0x196e},
{0x8706,0x8706,0x196b},
{0x8708,0x8708,0x196c},
{0x8709,0x8709,0x1971},
{0x870a,0x870a,0x1974},
{0x870b,0x870b,0x3af3},
{0x870d,0x870d,0x1972},
{0x8711,0x8711,0x1970},
{0x8712,0x8712,0x196a},
{0x8713,0x8713,0x3af4},
{0x8718,0x8718,0xb96},
{0x8719,0x8719,0x3af5},
{0x871a,0x871a,0x197b},
{0x871c,0x871c,0xeb6},
{0x871e,0x871e,0x3af6},
{0x8725,0x8725,0x1979},
{0x8728,0x8728,0x3af7},
{0x8729,0x8729,0x197a},
{0x8734,0x8734,0x1975},
{0x8737,0x8737,0x1977},
{0x873b,0x873b,0x1978},
{0x873e,0x873e,0x3af8},
{0x873f,0x873f,0x1976},
{0x8749,0x8749,0xa8a},
{0x874b,0x874b,0xfdf},
{0x874c,0x874c,0x197f},
{0x874e,0x874e,0x1980},
{0x8753,0x8753,0x1986},
{0x8755,0x8755,0x9f0},
{0x8757,0x8757,0x1982},
{0x8759,0x8759,0x1985},
{0x875f,0x875f,0x197d},
{0x8760,0x8760,0x197c},
{0x8763,0x8763,0x1987},
{0x8766,0x8766,0x55c},
{0x8768,0x8768,0x1983},
{0x876a,0x876a,0x1988},
{0x876e,0x876e,0x1984},
{0x8771,0x8771,0x3af9},
{0x8774,0x8774,0x1981},
{0x8776,0x8776,0xbcf},
{0x8778,0x8778,0x197e},
{0x877f,0x877f,0xd1e},
{0x8782,0x8782,0x198c},
{0x8788,0x8788,0x3afa},
{0x878d,0x878d,0xf25},
{0x8799,0x8799,0x3afb},
{0x879f,0x879f,0x198b},
{0x87a2,0x87a2,0x198a},
{0x87ab,0x87ab,0x1993},
{0x87ac,0x87ad,0x3afc},
{0x87af,0x87af,0x198d},
{0x87b3,0x87b3,0x1995},
{0x87b5,0x87b5,0x3afe},
{0x87ba,0x87ba,0xf50},
{0x87bb,0x87bb,0x1998},
{0x87bd,0x87bd,0x198f},
{0x87c0,0x87c0,0x1990},
{0x87c4,0x87c4,0x1994},
{0x87c6,0x87c6,0x1997},
{0x87c7,0x87c7,0x1996},
{0x87cb,0x87cb,0x198e},
{0x87d0,0x87d0,0x1991},
{0x87d2,0x87d2,0x19a2},
{0x87d6,0x87d6,0x3aff},
{0x87e0,0x87e0,0x199b},
{0x87eb,0x87eb,0x3b00},
{0x87ec,0x87ec,0x1e23},
{0x87ed,0x87ed,0x3b01},
{0x87ef,0x87ef,0x1999},
{0x87f2,0x87f2,0x199a},
{0x87f5,0x87f5,0x3c3d},
{0x87f6,0x87f7,0x199f},
{0x87f9,0x87f9,0x588},
{0x87fb,0x87fb,0x65c},
{0x87fe,0x87fe,0x199e},
{0x8801,0x8801,0x3b02},
{0x8805,0x8805,0x1989},
{0x8806,0x8806,0x3b04},
{0x8807,0x8807,0x21a6},
{0x880b,0x880b,0x3b05},
{0x880d,0x880d,0x199d},
{0x880e,0x880e,0x19a1},
{0x880f,0x880f,0x199c},
{0x8811,0x8811,0x19a3},
{0x8814,0x8814,0x3b06},
{0x8815,0x8815,0x19a5},
{0x8816,0x8816,0x19a4},
{0x881c,0x881c,0x3b07},
{0x881f,0x881f,0x1e85},
{0x8821,0x8821,0x19a7},
{0x8822,0x8822,0x19a6},
{0x8823,0x8823,0x1961},
{0x8827,0x8827,0x19ab},
{0x8831,0x8831,0x19a8},
{0x8836,0x8836,0x19a9},
{0x8839,0x8839,0x19aa},
{0x883b,0x883b,0x19ac},
{0x8840,0x8840,0x742},
{0x8842,0x8842,0x19ae},
{0x8844,0x8844,0x19ad},
{0x8846,0x8846,0x93a},
{0x884c,0x884c,0x7e6},
{0x884d,0x884d,0x1524},
{0x8852,0x8852,0x19af},
{0x8853,0x8853,0x95b},
{0x8856,0x8856,0x3b08},
{0x8857,0x8857,0x597},
{0x8859,0x8859,0x19b0},
{0x885b,0x885b,0x4f4},
{0x885d,0x885d,0x9bf},
{0x885e,0x885e,0x19b1},
{0x885f,0x885f,0x3b09},
{0x8861,0x8861,0x7e7},
{0x8862,0x8862,0x19b2},
{0x8863,0x8863,0x4a5},
{0x8864,0x8864,0x3b0a},
{0x8868,0x8868,0xdaf},
{0x886b,0x886b,0x19b3},
{0x8870,0x8870,0xa30},
{0x8872,0x8872,0x19ba},
{0x8875,0x8875,0x19b7},
{0x8877,0x8877,0xbad},
{0x887d,0x887d,0x19b8},
{0x887e,0x887e,0x19b5},
{0x887f,0x887f,0x6d6},
{0x8881,0x8881,0x19b4},
{0x8882,0x8882,0x19bb},
{0x8888,0x8888,0x70c},
{0x888b,0x888b,0xb3e},
{0x888d,0x888d,0x19c1},
{0x8892,0x8892,0x19bd},
{0x8896,0x8896,0xb15},
{0x8897,0x8897,0x19bc},
{0x8898,0x8898,0x3b0b},
{0x8899,0x8899,0x19bf},
{0x889e,0x889e,0x19b6},
{0x88a2,0x88a2,0x19c0},
{0x88a4,0x88a4,0x19c2},
{0x88aa,0x88aa,0x3b0c},
{0x88ab,0x88ab,0xd83},
{0x88ae,0x88ae,0x19be},
{0x88b0,0x88b0,0x19c3},
{0x88b1,0x88b1,0x19c5},
{0x88b4,0x88b4,0x787},
{0x88b5,0x88b5,0x19b9},
{0x88b7,0x88b7,0x485},
{0x88bd,0x88be,0x3b0d},
{0x88bf,0x88bf,0x19c4},
{0x88c1,0x88c1,0x84b},
{0x88c2,0x88c2,0xfbe},
{0x88c3,0x88c4,0x19c6},
{0x88c5,0x88c5,0xaf7},
{0x88c6,0x88c6,0x3789},
{0x88ca,0x88ca,0x3b0f},
{0x88cf,0x88cf,0xf6a},
{0x88d2,0x88d2,0x3b10},
{0x88d4,0x88d4,0x19c8},
{0x88d5,0x88d5,0xf1f},
{0x88d8,0x88d9,0x19c9},
{0x88db,0x88db,0x3b11},
{0x88dc,0x88dc,0xe34},
{0x88dd,0x88dd,0x19cb},
{0x88df,0x88df,0x830},
{0x88e1,0x88e1,0xf6b},
{0x88e8,0x88e8,0x19d0},
{0x88f0,0x88f1,0x3b12},
{0x88f2,0x88f2,0x19d1},
{0x88f3,0x88f3,0x9c0},
{0x88f4,0x88f4,0x19cf},
{0x88f5,0x88f5,0x21a7},
{0x88f8,0x88f8,0xf51},
{0x88f9,0x88f9,0x19cc},
{0x88fc,0x88fc,0x19ce},
{0x88fd,0x88fd,0xa61},
{0x88fe,0x88fe,0xa44},
{0x8902,0x8902,0x19cd},
{0x8904,0x8904,0x19d2},
{0x8906,0x8906,0x3b14},
{0x8907,0x8907,0xdf3},
{0x890a,0x890a,0x19d4},
{0x890c,0x890c,0x19d3},
{0x8910,0x8910,0x5ca},
{0x8912,0x8912,0xe59},
{0x8913,0x8913,0x19d5},
{0x8918,0x891a,0x3b15},
{0x891c,0x891c,0x20a8},
{0x891d,0x891d,0x19e1},
{0x891e,0x891e,0x19d7},
{0x8925,0x8925,0x19d8},
{0x8927,0x8927,0x3b18},
{0x892a,0x892b,0x19d9},
{0x8930,0x8930,0x3b19},
{0x8936,0x8936,0x19de},
{0x8938,0x8938,0x19df},
{0x893b,0x893b,0x19dd},
{0x893e,0x893e,0x3b1a},
{0x8941,0x8941,0x19db},
{0x8943,0x8943,0x19d6},
{0x8944,0x8944,0x19dc},
{0x894c,0x894c,0x19e0},
{0x894d,0x894d,0x1bd0},
{0x8956,0x8956,0x528},
{0x895e,0x895e,0x19e3},
{0x895f,0x895f,0x6d7},
{0x8960,0x8960,0x19e2},
{0x8964,0x8964,0x19e5},
{0x8966,0x8966,0x19e4},
{0x896a,0x896a,0x19e7},
{0x896d,0x896d,0x19e6},
{0x896f,0x896f,0x19e8},
{0x8972,0x8972,0x93b},
{0x8974,0x8974,0x19e9},
{0x8977,0x8977,0x19ea},
{0x897b,0x897b,0x3b1b},
{0x897e,0x897e,0x19eb},
{0x897f,0x897f,0xa62},
{0x8980,0x8980,0x362e},
{0x8981,0x8981,0xf41},
{0x8983,0x8983,0x19ec},
{0x8986,0x8986,0xdf4},
{0x8987,0x8987,0xcfc},
{0x8988,0x8988,0x19ed},
{0x898a,0x898a,0x19ee},
{0x898b,0x898b,0x75f},
{0x898f,0x898f,0x646},
{0x8993,0x8993,0x19ef},
{0x8996,0x8996,0x8b9},
{0x8997,0x8997,0xcf7},
{0x8998,0x8998,0x19f0},
{0x899a,0x899a,0x5ae},
{0x89a1,0x89a1,0x19f1},
{0x89a6,0x89a6,0x19f3},
{0x89a7,0x89a7,0xf61},
{0x89a9,0x89a9,0x19f2},
{0x89aa,0x89aa,0xa0c},
{0x89ac,0x89ac,0x19f4},
{0x89af,0x89af,0x19f5},
{0x89b2,0x89b2,0x19f6},
{0x89b3,0x89b3,0x60d},
{0x89ba,0x89ba,0x19f7},
{0x89bd,0x89bd,0x19f8},
{0x89bf,0x89c0,0x19f9},
{0x89d2,0x89d2,0x5af},
{0x89d4,0x89d4,0x3b1c},
{0x89d6,0x89d6,0x3b1d},
{0x89da,0x89da,0x19fb},
{0x89dc,0x89dd,0x19fc},
{0x89e3,0x89e3,0x572},
{0x89e5,0x89e5,0x3b1e},
{0x89e6,0x89e6,0x9ee},
{0x89e7,0x89e7,0x19fe},
{0x89f1,0x89f1,0x3b1f},
{0x89f4,0x89f4,0x19ff},
{0x89f8,0x89f8,0x1a00},
{0x8a00,0x8a00,0x774},
{0x8a01,0x8a01,0x35bc},
{0x8a02,0x8a02,0xc17},
{0x8a03,0x8a03,0x1a01},
{0x8a07,0x8a07,0x3b20},
{0x8a08,0x8a08,0x72d},
{0x8a0a,0x8a0a,0xa1c},
{0x8a0c,0x8a0c,0x1a04},
{0x8a0e,0x8a0e,0xc7c},
{0x8a0f,0x8a0f,0x3b21},
{0x8a10,0x8a10,0x1a03},
{0x8a12,0x8a12,0x21a8},
{0x8a13,0x8a13,0x707},
{0x8a15,0x8a15,0x3b22},
{0x8a16,0x8a16,0x1a02},
{0x8a17,0x8a17,0xb57},
{0x8a18,0x8a18,0x647},
{0x8a1b,0x8a1b,0x1a05},
{0x8a1d,0x8a1d,0x1a06},
{0x8a1f,0x8a1f,0x9c1},
{0x8a22,0x8a22,0x3b23},
{0x8a23,0x8a23,0x743},
{0x8a25,0x8a25,0x1a07},
{0x8a2a,0x8a2a,0xe5a},
{0x8a2d,0x8a2d,0xa83},
{0x8a31,0x8a31,0x690},
{0x8a33,0x8a33,0xf01},
{0x8a34,0x8a34,0xacc},
{0x8a36,0x8a36,0x1a08},
{0x8a37,0x8a37,0x21a9},
{0x8a3a,0x8a3a,0xa0d},
{0x8a3b,0x8a3b,0xbae},
{0x8a3c,0x8a3c,0x9c2},
{0x8a41,0x8a41,0x1a09},
{0x8a46,0x8a46,0x1a0c},
{0x8a48,0x8a48,0x1a0d},
{0x8a4e,0x8a4e,0x3b24},
{0x8a50,0x8a50,0x82e},
{0x8a51,0x8a51,0xb22},
{0x8a52,0x8a52,0x1a0b},
{0x8a54,0x8a54,0x9c3},
{0x8a55,0x8a55,0xdb0},
{0x8a5b,0x8a5b,0x1a0a},
{0x8a5e,0x8a5e,0x8ba},
{0x8a60,0x8a60,0x4f5},
{0x8a62,0x8a62,0x1a11},
{0x8a63,0x8a63,0x72e},
{0x8a66,0x8a66,0x8bc},
{0x8a69,0x8a69,0x8bb},
{0x8a6b,0x8a6b,0xff3},
{0x8a6c,0x8a6c,0x1a10},
{0x8a6d,0x8a6d,0x1a0f},
{0x8a6e,0x8a6e,0xaa9},
{0x8a70,0x8a70,0x667},
{0x8a71,0x8a71,0xfe9},
{0x8a72,0x8a72,0x598},
{0x8a73,0x8a73,0x9c4},
{0x8a79,0x8a79,0x21aa},
{0x8a7c,0x8a7c,0x1a0e},
{0x8a7f,0x8a7f,0x3b25},
{0x8a82,0x8a82,0x1a13},
{0x8a84,0x8a84,0x1a14},
{0x8a85,0x8a85,0x1a12},
{0x8a87,0x8a87,0x78c},
{0x8a89,0x8a89,0xf2a},
{0x8a8c,0x8a8c,0x8bd},
{0x8a8d,0x8a8d,0xcdd},
{0x8a91,0x8a91,0x1a17},
{0x8a93,0x8a93,0xa64},
{0x8a95,0x8a95,0xb80},
{0x8a98,0x8a98,0xf20},
{0x8a9a,0x8a9a,0x1a1a},
{0x8a9e,0x8a9e,0x7a0},
{0x8aa0,0x8aa0,0xa63},
{0x8aa1,0x8aa1,0x1a16},
{0x8aa3,0x8aa3,0x1a1b},
{0x8aa4,0x8aa4,0x7a1},
{0x8aa5,0x8aa6,0x1a18},
{0x8aa7,0x8aa7,0x21ab},
{0x8aa8,0x8aa8,0x1a15},
{0x8aaa,0x8aaa,0x3638},
{0x8aac,0x8aac,0xa86},
{0x8aad,0x8aad,0xca1},
{0x8ab0,0x8ab0,0xb6d},
{0x8ab2,0x8ab2,0x55d},
{0x8ab9,0x8ab9,0xd84},
{0x8abc,0x8abc,0x65d},
{0x8abe,0x8abe,0x21ac},
{0x8abf,0x8abf,0xbd0},
{0x8ac2,0x8ac2,0x1a1e},
{0x8ac4,0x8ac4,0x1a1c},
{0x8ac7,0x8ac7,0xb8a},
{0x8acb,0x8acb,0xa65},
{0x8acc,0x8acc,0x60e},
{0x8acd,0x8acd,0x1a1d},
{0x8acf,0x8acf,0xa21},
{0x8ad2,0x8ad2,0xf92},
{0x8ad6,0x8ad6,0xfe6},
{0x8ada,0x8ada,0x1a1f},
{0x8adb,0x8adb,0x1a2a},
{0x8adc,0x8adc,0xbd1},
{0x8ade,0x8ade,0x1a29},
{0x8adf,0x8adf,0x21ad},
{0x8ae0,0x8ae0,0x1a26},
{0x8ae1,0x8ae1,0x1a2e},
{0x8ae2,0x8ae2,0x1a27},
{0x8ae4,0x8ae4,0x1a23},
{0x8ae6,0x8ae6,0xc18},
{0x8ae7,0x8ae7,0x1a22},
{0x8aeb,0x8aeb,0x1a20},
{0x8aed,0x8aed,0xf0b},
{0x8aee,0x8aee,0x8be},
{0x8af1,0x8af1,0x1a24},
{0x8af3,0x8af3,0x1a21},
{0x8af4,0x8af4,0x3b26},
{0x8af6,0x8af6,0x21af},
{0x8af7,0x8af7,0x1a28},
{0x8af8,0x8af8,0x97e},
{0x8afa,0x8afa,0x775},
{0x8afe,0x8afe,0xb5a},
{0x8b00,0x8b00,0xe73},
{0x8b01,0x8b01,0x4fc},
{0x8b02,0x8b02,0x4a6},
{0x8b04,0x8b04,0xc7d},
{0x8b07,0x8b07,0x1a2c},
{0x8b0c,0x8b0c,0x1a2b},
{0x8b0e,0x8b0e,0xcbe},
{0x8b10,0x8b10,0x1a30},
{0x8b14,0x8b14,0x1a25},
{0x8b16,0x8b16,0x1a2f},
{0x8b17,0x8b17,0x1a31},
{0x8b19,0x8b19,0x760},
{0x8b1a,0x8b1a,0x1a2d},
{0x8b1b,0x8b1b,0x7e8},
{0x8b1d,0x8b1d,0x901},
{0x8b1f,0x8b1f,0x3b27},
{0x8b20,0x8b20,0x1a32},
{0x8b21,0x8b21,0xf42},
{0x8b26,0x8b26,0x1a35},
{0x8b28,0x8b28,0x1a38},
{0x8b2b,0x8b2b,0x1a36},
{0x8b2c,0x8b2c,0xda7},
{0x8b33,0x8b33,0x1a33},
{0x8b37,0x8b37,0x3b28},
{0x8b39,0x8b39,0x6d8},
{0x8b3e,0x8b3e,0x1a37},
{0x8b41,0x8b41,0x1a39},
{0x8b43,0x8b44,0x3b29},
{0x8b49,0x8b49,0x1a3d},
{0x8b4c,0x8b4c,0x1a3a},
{0x8b4e,0x8b4e,0x1a3c},
{0x8b4f,0x8b4f,0x1a3b},
{0x8b53,0x8b53,0x21b0},
{0x8b54,0x8b54,0x3b2b},
{0x8b56,0x8b56,0x1a3e},
{0x8b58,0x8b58,0x8dd},
{0x8b5a,0x8b5a,0x1a40},
{0x8b5b,0x8b5b,0x1a3f},
{0x8b5c,0x8b5c,0xdda},
{0x8b5f,0x8b5f,0x1a42},
{0x8b66,0x8b66,0x72f},
{0x8b6b,0x8b6b,0x1a41},
{0x8b6c,0x8b6c,0x1a43},
{0x8b6f,0x8b6f,0x1a44},
{0x8b70,0x8b70,0x65e},
{0x8b71,0x8b71,0x182e},
{0x8b72,0x8b72,0x9e1},
{0x8b74,0x8b74,0x1a45},
{0x8b77,0x8b77,0x7a2},
{0x8b7d,0x8b7d,0x1a46},
{0x8b7f,0x8b7f,0x21b1},
{0x8b80,0x8b80,0x1a47},
{0x8b83,0x8b83,0x88c},
{0x8b8a,0x8b8a,0x13d3},
{0x8b8c,0x8b8c,0x1a48},
{0x8b8e,0x8b8e,0x1a49},
{0x8b90,0x8b90,0x93c},
{0x8b92,0x8b93,0x1a4a},
{0x8b96,0x8b96,0x1a4c},
{0x8b99,0x8b9a,0x1a4d},
{0x8b9c,0x8b9c,0x3b2c},
{0x8b9e,0x8b9e,0x3b2d},
{0x8c37,0x8c37,0xb69},
{0x8c3a,0x8c3a,0x1a4f},
{0x8c3f,0x8c3f,0x1a51},
{0x8c41,0x8c41,0x1a50},
{0x8c46,0x8c46,0xc7e},
{0x8c47,0x8c47,0x3b2e},
{0x8c48,0x8c48,0x1a52},
{0x8c4a,0x8c4a,0xe5b},
{0x8c4c,0x8c4c,0x1a53},
{0x8c4e,0x8c4e,0x1a54},
{0x8c50,0x8c50,0x1a55},
{0x8c54,0x8c54,0x3b2f},
{0x8c55,0x8c55,0x1a56},
{0x8c5a,0x8c5a,0xcb2},
{0x8c61,0x8c61,0x9c5},
{0x8c62,0x8c62,0x1a57},
{0x8c6a,0x8c6a,0x7fd},
{0x8c6b,0x8c6b,0x1007},
{0x8c6c,0x8c6c,0x1a58},
{0x8c73,0x8c73,0x3b30},
{0x8c78,0x8c78,0x1a59},
{0x8c79,0x8c79,0xdb1},
{0x8c7a,0x8c7a,0x1a5a},
{0x8c7c,0x8c7c,0x1a62},
{0x8c82,0x8c82,0x1a5b},
{0x8c85,0x8c85,0x1a5d},
{0x8c89,0x8c89,0x1a5c},
{0x8c8a,0x8c8a,0x1a5e},
{0x8c8c,0x8c8c,0xe74},
{0x8c8d,0x8c8e,0x1a5f},
{0x8c94,0x8c94,0x1a61},
{0x8c98,0x8c98,0x1a63},
{0x8c9d,0x8c9d,0x58b},
{0x8c9e,0x8c9e,0xc03},
{0x8ca0,0x8ca0,0xddb},
{0x8ca1,0x8ca1,0x852},
{0x8ca2,0x8ca2,0x7e9},
{0x8ca4,0x8ca4,0x3b31},
{0x8ca7,0x8ca7,0xdc1},
{0x8ca8,0x8ca8,0x55f},
{0x8ca9,0x8ca9,0xd62},
{0x8caa,0x8caa,0x1a66},
{0x8cab,0x8cab,0x60f},
{0x8cac,0x8cac,0xa79},
{0x8cad,0x8cad,0x1a65},
{0x8cae,0x8cae,0x1a6a},
{0x8caf,0x8caf,0xbb7},
{0x8cb0,0x8cb0,0xeef},
{0x8cb2,0x8cb3,0x1a68},
{0x8cb4,0x8cb4,0x648},
{0x8cb6,0x8cb6,0x1a6b},
{0x8cb7,0x8cb7,0xd19},
{0x8cb8,0x8cb8,0xb3f},
{0x8cbb,0x8cbb,0xd85},
{0x8cbc,0x8cbc,0xc37},
{0x8cbd,0x8cbd,0x1a67},
{0x8cbf,0x8cbf,0xe75},
{0x8cc0,0x8cc0,0x56c},
{0x8cc1,0x8cc1,0x1a6d},
{0x8cc2,0x8cc2,0xfce},
{0x8cc3,0x8cc3,0xbde},
{0x8cc4,0x8cc4,0xfeb},
{0x8cc7,0x8cc7,0x8bf},
{0x8cc8,0x8cc8,0x1a6c},
{0x8cca,0x8cca,0xb11},
{0x8ccd,0x8ccd,0x1a7d},
{0x8cce,0x8cce,0xaaa},
{0x8cd1,0x8cd1,0xcd0},
{0x8cd3,0x8cd3,0xdc2},
{0x8cd9,0x8cd9,0x3b32},
{0x8cda,0x8cda,0x1a70},
{0x8cdb,0x8cdb,0x88d},
{0x8cdc,0x8cdc,0x8c0},
{0x8cde,0x8cde,0x9c6},
{0x8ce0,0x8ce0,0xd1b},
{0x8ce1,0x8ce1,0x3b33},
{0x8ce2,0x8ce2,0x761},
{0x8ce3,0x8ce3,0x1a6f},
{0x8ce4,0x8ce4,0x1a6e},
{0x8ce6,0x8ce6,0xddc},
{0x8cea,0x8cea,0x8ed},
{0x8ced,0x8ced,0xc4c},
{0x8cf0,0x8cf0,0x21b2},
{0x8cf4,0x8cf4,0x21b3},
{0x8cf8,0x8cf8,0x3b34},
{0x8cfa,0x8cfb,0x1a72},
{0x8cfc,0x8cfc,0x7ea},
{0x8cfd,0x8cfd,0x1a71},
{0x8cfe,0x8cfe,0x3b35},
{0x8d04,0x8d05,0x1a74},
{0x8d07,0x8d07,0x1a77},
{0x8d08,0x8d08,0xb03},
{0x8d0a,0x8d0a,0x1a76},
{0x8d0b,0x8d0b,0x622},
{0x8d0d,0x8d0d,0x1a79},
{0x8d0f,0x8d0f,0x1a78},
{0x8d10,0x8d10,0x1a7a},
{0x8d12,0x8d12,0x21b4},
{0x8d13,0x8d13,0x1a7c},
{0x8d14,0x8d14,0x1a7e},
{0x8d16,0x8d16,0x1a7f},
{0x8d1b,0x8d1b,0x3b36},
{0x8d64,0x8d64,0xa7a},
{0x8d66,0x8d66,0x8fb},
{0x8d67,0x8d67,0x1a80},
{0x8d69,0x8d69,0x3b37},
{0x8d6b,0x8d6b,0x5b0},
{0x8d6c,0x8d6c,0x3b38},
{0x8d6d,0x8d6d,0x1a81},
{0x8d70,0x8d70,0xaf8},
{0x8d71,0x8d71,0x1a82},
{0x8d73,0x8d73,0x1a83},
{0x8d74,0x8d74,0xddd},
{0x8d76,0x8d76,0x21b5},
{0x8d77,0x8d77,0x649},
{0x8d81,0x8d81,0x1a84},
{0x8d84,0x8d84,0x3b39},
{0x8d85,0x8d85,0xbd2},
{0x8d8a,0x8d8a,0x4fd},
{0x8d8d,0x8d8d,0x3b3a},
{0x8d95,0x8d95,0x3b3b},
{0x8d99,0x8d99,0x1a85},
{0x8da3,0x8da3,0x91d},
{0x8da6,0x8da6,0x3b3c},
{0x8da8,0x8da8,0xa3c},
{0x8db3,0x8db3,0xb0d},
{0x8dba,0x8dba,0x1a88},
{0x8dbe,0x8dbe,0x1a87},
{0x8dc2,0x8dc2,0x1a86},
{0x8dc6,0x8dc6,0x3b3d},
{0x8dcb,0x8dcb,0x1a8e},
{0x8dcc,0x8dcc,0x1a8c},
{0x8dce,0x8dce,0x3b3e},
{0x8dcf,0x8dcf,0x1a89},
{0x8dd6,0x8dd6,0x1a8b},
{0x8dda,0x8dda,0x1a8a},
{0x8ddb,0x8ddb,0x1a8d},
{0x8ddd,0x8ddd,0x691},
{0x8ddf,0x8ddf,0x1a91},
{0x8de1,0x8de1,0xa7b},
{0x8de3,0x8de3,0x1a92},
{0x8de4,0x8de4,0x3b3f},
{0x8de8,0x8de8,0x78d},
{0x8dea,0x8deb,0x1a8f},
{0x8dec,0x8dec,0x3b40},
{0x8def,0x8def,0xfcf},
{0x8df3,0x8df3,0xbd3},
{0x8df5,0x8df5,0xaab},
{0x8dfc,0x8dfc,0x1a93},
{0x8dff,0x8dff,0x1a96},
{0x8e08,0x8e09,0x1a94},
{0x8e0a,0x8e0a,0xf43},
{0x8e0f,0x8e0f,0xc7f},
{0x8e10,0x8e10,0x1a99},
{0x8e1d,0x8e1e,0x1a97},
{0x8e1f,0x8e1f,0x1a9a},
{0x8e20,0x8e20,0x3b41},
{0x8e2a,0x8e2a,0x1aa8},
{0x8e30,0x8e30,0x1a9d},
{0x8e34,0x8e34,0x1a9e},
{0x8e35,0x8e35,0x1a9c},
{0x8e42,0x8e42,0x1a9b},
{0x8e44,0x8e44,0xc19},
{0x8e47,0x8e47,0x1aa0},
{0x8e48,0x8e48,0x1aa4},
{0x8e49,0x8e49,0x1aa1},
{0x8e4a,0x8e4a,0x1a9f},
{0x8e4b,0x8e4b,0x3b42},
{0x8e4c,0x8e4c,0x1aa2},
{0x8e50,0x8e50,0x1aa3},
{0x8e55,0x8e55,0x1aaa},
{0x8e59,0x8e59,0x1aa5},
{0x8e5f,0x8e5f,0xa7c},
{0x8e60,0x8e60,0x1aa7},
{0x8e63,0x8e63,0x1aa9},
{0x8e64,0x8e64,0x1aa6},
{0x8e6c,0x8e6c,0x3b43},
{0x8e70,0x8e70,0x3b44},
{0x8e72,0x8e72,0x1aac},
{0x8e74,0x8e74,0x93d},
{0x8e76,0x8e76,0x1aab},
{0x8e7a,0x8e7a,0x3b45},
{0x8e7c,0x8e7c,0x1aad},
{0x8e81,0x8e81,0x1aae},
{0x8e84,0x8e84,0x1ab1},
{0x8e85,0x8e85,0x1ab0},
{0x8e87,0x8e87,0x1aaf},
{0x8e8a,0x8e8a,0x1ab3},
{0x8e8b,0x8e8b,0x1ab2},
{0x8e8d,0x8e8d,0xf02},
{0x8e91,0x8e91,0x1ab5},
{0x8e92,0x8e92,0x3b46},
{0x8e93,0x8e93,0x1ab4},
{0x8e94,0x8e94,0x1ab6},
{0x8e99,0x8e99,0x1ab7},
{0x8ea1,0x8ea1,0x1ab9},
{0x8eaa,0x8eaa,0x1ab8},
{0x8eab,0x8eab,0xa0e},
{0x8eac,0x8eac,0x1aba},
{0x8eae,0x8eae,0x3b47},
{0x8eaf,0x8eaf,0x6e5},
{0x8eb0,0x8eb0,0x1abb},
{0x8eb1,0x8eb1,0x1abd},
{0x8eb3,0x8eb3,0x3b48},
{0x8eb6,0x8eb6,0x3703},
{0x8ebe,0x8ebe,0x1abe},
{0x8ec0,0x8ec0,0x1def},
{0x8ec5,0x8ec5,0x1abf},
{0x8ec6,0x8ec6,0x1abc},
{0x8ec8,0x8ec8,0x1ac0},
{0x8eca,0x8eca,0x902},
{0x8ecb,0x8ecb,0x1ac1},
{0x8ecc,0x8ecc,0x64a},
{0x8ecd,0x8ecd,0x709},
{0x8ecf,0x8ecf,0x21b7},
{0x8ed1,0x8ed1,0x3b49},
{0x8ed2,0x8ed2,0x762},
{0x8ed4,0x8ed4,0x3b4a},
{0x8edb,0x8edb,0x1ac2},
{0x8edf,0x8edf,0xcc8},
{0x8ee2,0x8ee2,0xc38},
{0x8ee3,0x8ee3,0x1ac3},
{0x8eeb,0x8eeb,0x1ac6},
{0x8ef8,0x8ef8,0x8e0},
{0x8ef9,0x8ef9,0x3b4b},
{0x8efb,0x8efb,0x1ac5},
{0x8efc,0x8efc,0x1ac4},
{0x8efd,0x8efd,0x730},
{0x8efe,0x8efe,0x1ac7},
{0x8f03,0x8f03,0x5b1},
{0x8f05,0x8f05,0x1ac9},
{0x8f09,0x8f09,0x84c},
{0x8f0a,0x8f0a,0x1ac8},
{0x8f0c,0x8f0c,0x1ad1},
{0x8f12,0x8f12,0x1acb},
{0x8f13,0x8f13,0x1acd},
{0x8f14,0x8f14,0xe35},
{0x8f15,0x8f15,0x1aca},
{0x8f17,0x8f17,0x3b4c},
{0x8f19,0x8f19,0x1acc},
{0x8f1b,0x8f1b,0x1ad0},
{0x8f1c,0x8f1c,0x1ace},
{0x8f1d,0x8f1d,0x64b},
{0x8f1f,0x8f1f,0x1acf},
{0x8f26,0x8f26,0x1ad2},
{0x8f29,0x8f29,0xd10},
{0x8f2a,0x8f2a,0xfa0},
{0x8f2f,0x8f2f,0x93e},
{0x8f33,0x8f33,0x1ad3},
{0x8f36,0x8f36,0x3b4d},
{0x8f38,0x8f38,0xf0c},
{0x8f39,0x8f39,0x1ad5},
{0x8f3b,0x8f3b,0x1ad4},
{0x8f3e,0x8f3e,0x1ad8},
{0x8f3f,0x8f3f,0xf2b},
{0x8f42,0x8f42,0x1ad7},
{0x8f44,0x8f44,0x5cb},
{0x8f45,0x8f45,0x1ad6},
{0x8f46,0x8f46,0x1adb},
{0x8f49,0x8f49,0x1ada},
{0x8f4c,0x8f4c,0x1ad9},
{0x8f4d,0x8f4d,0xc2c},
{0x8f4e,0x8f4e,0x1adc},
{0x8f57,0x8f57,0x1add},
{0x8f5c,0x8f5c,0x1ade},
{0x8f5f,0x8f5f,0x7fe},
{0x8f61,0x8f61,0x6fb},
{0x8f62,0x8f64,0x1adf},
{0x8f9b,0x8f9b,0xa0f},
{0x8f9c,0x8f9c,0x1ae2},
{0x8f9e,0x8f9e,0x8d9},
{0x8f9f,0x8f9f,0x1ae3},
{0x8fa3,0x8fa3,0x1ae4},
{0x8fa6,0x8fa6,0x3b4e},
{0x8fa7,0x8fa7,0x10b6},
{0x8fa8,0x8fa8,0x10b5},
{0x8fad,0x8fad,0x1ae5},
{0x8fae,0x8fae,0x17ff},
{0x8faf,0x8faf,0x1ae6},
{0x8fb0,0x8fb0,0xb62},
{0x8fb1,0x8fb1,0x9f1},
{0x8fb2,0x8fb2,0xcf6},
{0x8fb5,0x8fb6,0x3b4f},
{0x8fb7,0x8fb7,0x1ae7},
{0x8fba,0x8fba,0xe25},
{0x8fbb,0x8fbb,0xbf0},
{0x8fbc,0x8fbc,0x810},
{0x8fbf,0x8fbf,0xb67},
{0x8fc2,0x8fc2,0x4cc},
{0x8fc4,0x8fc4,0xea6},
{0x8fc5,0x8fc5,0xa1d},
{0x8fc8,0x8fc8,0x3799},
{0x8fce,0x8fce,0x734},
{0x8fd1,0x8fd1,0x6d9},
{0x8fd4,0x8fd4,0xe26},
{0x8fda,0x8fda,0x1ae8},
{0x8fe0,0x8fe0,0x3b52},
{0x8fe2,0x8fe2,0x1aea},
{0x8fe4,0x8fe4,0x3b53},
{0x8fe5,0x8fe5,0x1ae9},
{0x8fe6,0x8fe6,0x560},
{0x8fe9,0x8fe9,0xcce},
{0x8fea,0x8fea,0x1aeb},
{0x8feb,0x8feb,0xd2d},
{0x8fed,0x8fed,0xc2d},
{0x8fef,0x8fef,0x1aec},
{0x8ff0,0x8ff0,0x95c},
{0x8ff4,0x8ff4,0x1aee},
{0x8ff6,0x8ff6,0x3b54},
{0x8ff7,0x8ff7,0xece},
{0x8ff8,0x8ff8,0x1afd},
{0x8ff9,0x8ffa,0x1af0},
{0x8ffd,0x8ffd,0xbe5},
{0x9000,0x9000,0xb40},
{0x9001,0x9001,0xaf9},
{0x9002,0x9002,0x3b55},
{0x9003,0x9003,0xc80},
{0x9005,0x9005,0x1aef},
{0x9006,0x9006,0x66f},
{0x900b,0x900b,0x1af8},
{0x900d,0x900d,0x1af5},
{0x900e,0x900e,0x1b02},
{0x900f,0x900f,0xc81},
{0x9010,0x9010,0xb9e},
{0x9011,0x9011,0x1af2},
{0x9013,0x9013,0xc1a},
{0x9014,0x9014,0xc4d},
{0x9015,0x9015,0x1af3},
{0x9016,0x9016,0x1af7},
{0x9017,0x9017,0xa26},
{0x9019,0x9019,0xd1d},
{0x901a,0x901a,0xbe8},
{0x901d,0x901d,0xa66},
{0x901e,0x901e,0x1af6},
{0x901f,0x901f,0xb0e},
{0x9020,0x9020,0xb04},
{0x9021,0x9021,0x1af4},
{0x9022,0x9022,0x46d},
{0x9023,0x9023,0xfc8},
{0x9027,0x9027,0x1af9},
{0x902c,0x902c,0x3b56},
{0x902e,0x902e,0xb41},
{0x9031,0x9031,0x93f},
{0x9032,0x9032,0xa10},
{0x9035,0x9035,0x1afb},
{0x9036,0x9036,0x1afa},
{0x9038,0x9038,0x4b3},
{0x9039,0x9039,0x1afc},
{0x903c,0x903c,0xda1},
{0x903e,0x903e,0x1b04},
{0x9041,0x9041,0xcb3},
{0x9042,0x9042,0xa31},
{0x9044,0x9044,0x3b57},
{0x9045,0x9045,0xb97},
{0x9047,0x9047,0x6f0},
{0x9049,0x9049,0x1b03},
{0x904a,0x904a,0xf21},
{0x904b,0x904b,0x4e1},
{0x904d,0x904d,0xe27},
{0x904e,0x904e,0x561},
{0x904f,0x9052,0x1afe},
{0x9053,0x9053,0xc93},
{0x9054,0x9054,0xb61},
{0x9055,0x9055,0x4a7},
{0x9056,0x9056,0x1b05},
{0x9058,0x9058,0x1b06},
{0x9059,0x9059,0x1d34},
{0x905c,0x905c,0xb1d},
{0x905e,0x905e,0x1b07},
{0x9060,0x9060,0x515},
{0x9061,0x9061,0xace},
{0x9063,0x9063,0x763},
{0x9065,0x9065,0xf44},
{0x9067,0x9067,0x21ba},
{0x9068,0x9068,0x1b08},
{0x9069,0x9069,0xc26},
{0x906d,0x906d,0xafa},
{0x906e,0x906e,0x903},
{0x906f,0x906f,0x1b09},
{0x9072,0x9072,0x1b0c},
{0x9075,0x9075,0x96f},
{0x9076,0x9076,0x1b0a},
{0x9077,0x9077,0xaad},
{0x9078,0x9078,0xaac},
{0x907a,0x907a,0x4a8},
{0x907c,0x907c,0xf93},
{0x907d,0x907d,0x1b0e},
{0x907f,0x907f,0xd86},
{0x9080,0x9080,0x1b10},
{0x9081,0x9081,0x1b0f},
{0x9082,0x9082,0x1b0d},
{0x9083,0x9083,0x1737},
{0x9084,0x9084,0x610},
{0x9087,0x9087,0x1aed},
{0x9088,0x9088,0x3b58},
{0x9089,0x9089,0x1b12},
{0x908a,0x908a,0x1b11},
{0x908f,0x908f,0x1b13},
{0x9091,0x9091,0xf22},
{0x9095,0x9095,0x3b59},
{0x9099,0x9099,0x3b5a},
{0x909b,0x909b,0x3b5b},
{0x90a2,0x90a2,0x3b5c},
{0x90a3,0x90a3,0xcb9},
{0x90a6,0x90a6,0xe5c},
{0x90a8,0x90a8,0x1b14},
{0x90aa,0x90aa,0x905},
{0x90af,0x90af,0x1b15},
{0x90b1,0x90b1,0x1b16},
{0x90b4,0x90b4,0x3b5d},
{0x90b5,0x90b5,0x1b17},
{0x90b8,0x90b8,0xc1b},
{0x90c1,0x90c1,0x4ae},
{0x90ca,0x90ca,0x7eb},
{0x90ce,0x90ce,0xfe0},
{0x90d7,0x90d7,0x3b5e},
{0x90db,0x90db,0x1b1b},
{0x90dd,0x90dd,0x3b5f},
{0x90de,0x90de,0x21bb},
{0x90e1,0x90e1,0x70a},
{0x90e2,0x90e2,0x1b18},
{0x90e4,0x90e4,0x1b19},
{0x90e8,0x90e8,0xde6},
{0x90ed,0x90ed,0x5b2},
{0x90f4,0x90f4,0x3b60},
{0x90f5,0x90f5,0xf23},
{0x90f7,0x90f7,0x6b7},
{0x90fd,0x90fd,0xc4e},
{0x9102,0x9102,0x1b1c},
{0x9112,0x9112,0x1b1d},
{0x9115,0x9115,0x21bd},
{0x9117,0x9117,0x3b61},
{0x9119,0x9119,0x1b1e},
{0x911c,0x911c,0x3b62},
{0x9127,0x9127,0x21be},
{0x912d,0x912d,0xc1c},
{0x9130,0x9130,0x1b20},
{0x9131,0x9131,0x3b63},
{0x9132,0x9132,0x1b1f},
{0x913a,0x913a,0x3b64},
{0x913d,0x913d,0x3b65},
{0x9148,0x9148,0x3b66},
{0x9149,0x9149,0xcab},
{0x914a,0x914a,0x1b21},
{0x914b,0x914b,0x940},
{0x914c,0x914c,0x90c},
{0x914d,0x914d,0xd11},
{0x914e,0x914e,0xbaf},
{0x9152,0x9152,0x91e},
{0x9154,0x9154,0xa32},
{0x9156,0x9156,0x1b22},
{0x9158,0x9158,0x1b23},
{0x915b,0x915b,0x3b67},
{0x9161,0x9161,0x3b68},
{0x9162,0x9162,0xa23},
{0x9163,0x9163,0x1b24},
{0x9164,0x9164,0x3b69},
{0x9165,0x9165,0x1b25},
{0x9169,0x9169,0x1b26},
{0x916a,0x916a,0xf59},
{0x916c,0x916c,0x941},
{0x9172,0x9172,0x1b28},
{0x9173,0x9173,0x1b27},
{0x9175,0x9175,0x7ec},
{0x9177,0x9177,0x805},
{0x9178,0x9178,0x88e},
{0x9182,0x9182,0x1b2b},
{0x9187,0x9187,0x970},
{0x9189,0x9189,0x1b2a},
{0x918b,0x918b,0x1b29},
{0x918d,0x918d,0xb49},
{0x918e,0x918e,0x3b6a},
{0x9190,0x9190,0x7a3},
{0x9192,0x9192,0xa67},
{0x9197,0x9197,0xd44},
{0x919c,0x919c,0x943},
{0x919e,0x919e,0x3b6b},
{0x91a2,0x91a2,0x1b2c},
{0x91a4,0x91a4,0x9c7},
{0x91a8,0x91a8,0x3b6c},
{0x91aa,0x91aa,0x1b2f},
{0x91ab,0x91ab,0x1b2d},
{0x91ac,0x91ac,0x1e1b},
{0x91ad,0x91ae,0x3b6d},
{0x91af,0x91af,0x1b2e},
{0x91b1,0x91b1,0x1e61},
{0x91b2,0x91b2,0x3b6f},
{0x91b4,0x91b4,0x1b31},
{0x91b5,0x91b5,0x1b30},
{0x91b8,0x91b8,0x9e2},
{0x91ba,0x91ba,0x1b32},
{0x91bc,0x91bc,0x3b70},
{0x91c0,0x91c1,0x1b33},
{0x91c6,0x91c6,0xd64},
{0x91c7,0x91c7,0x843},
{0x91c8,0x91c8,0x90d},
{0x91c9,0x91c9,0x1b35},
{0x91cb,0x91cb,0x1b36},
{0x91cc,0x91cc,0xf6c},
{0x91cd,0x91cd,0x94f},
{0x91ce,0x91ce,0xefa},
{0x91cf,0x91cf,0xf94},
{0x91d0,0x91d0,0x1b37},
{0x91d1,0x91d1,0x6da},
{0x91d6,0x91d6,0x1b38},
{0x91d7,0x91d7,0x21c0},
{0x91d8,0x91d8,0xc1d},
{0x91da,0x91da,0x21bf},
{0x91db,0x91db,0x1b3b},
{0x91dc,0x91dc,0x5d6},
{0x91dd,0x91dd,0xa11},
{0x91de,0x91de,0x21c1},
{0x91df,0x91df,0x1b39},
{0x91e1,0x91e1,0x1b3a},
{0x91e3,0x91e3,0xbfc},
{0x91e4,0x91e5,0x21c4},
{0x91e6,0x91e6,0xe83},
{0x91e7,0x91e7,0x6f4},
{0x91ed,0x91ee,0x21c2},
{0x91f0,0x91f0,0x3b71},
{0x91f5,0x91f6,0x1b3d},
{0x91f7,0x91f7,0x3b72},
{0x91fb,0x91fb,0x3b73},
{0x91fc,0x91fc,0x1b3c},
{0x91ff,0x91ff,0x1b40},
{0x9206,0x9206,0x21c6},
{0x9207,0x9207,0x3b74},
{0x920a,0x920a,0x21c8},
{0x920d,0x920d,0xcb7},
{0x920e,0x920e,0x5a1},
{0x9210,0x9210,0x21c7},
{0x9211,0x9211,0x1b44},
{0x9214,0x9214,0x1b41},
{0x9215,0x9215,0x1b43},
{0x921e,0x921e,0x1b3f},
{0x9228,0x9228,0x3b75},
{0x9229,0x9229,0x1b8a},
{0x922c,0x922c,0x1b42},
{0x9233,0x9233,0x3b76},
{0x9234,0x9234,0xfb3},
{0x9237,0x9237,0x78e},
{0x9238,0x9238,0x3b77},
{0x9239,0x9239,0x21cf},
{0x923a,0x923a,0x21c9},
{0x923c,0x923c,0x21cb},
{0x923f,0x923f,0x1b4c},
{0x9240,0x9240,0x21ca},
{0x9243,0x9243,0x3b78},
{0x9244,0x9244,0xc2e},
{0x9245,0x9245,0x1b47},
{0x9247,0x9247,0x3b79},
{0x9248,0x9248,0x1b4a},
{0x9249,0x9249,0x1b48},
{0x924b,0x924b,0x1b4d},
{0x924e,0x924e,0x21cc},
{0x924f,0x924f,0x3b7a},
{0x9250,0x9250,0x1b4e},
{0x9251,0x9251,0x21ce},
{0x9257,0x9257,0x1b46},
{0x9259,0x9259,0x21cd},
{0x925a,0x925a,0x1b53},
{0x925b,0x925b,0x516},
{0x925e,0x925e,0x1b45},
{0x9260,0x9260,0x3b7b},
{0x9262,0x9262,0xd41},
{0x9264,0x9264,0x1b49},
{0x9266,0x9266,0x9c8},
{0x9267,0x9267,0x21d0},
{0x9271,0x9271,0x7ed},
{0x9277,0x9278,0x21d2},
{0x927e,0x927e,0xe76},
{0x9280,0x9280,0x6dc},
{0x9283,0x9283,0x950},
{0x9285,0x9285,0xc94},
{0x9288,0x9288,0x20aa},
{0x9291,0x9291,0xaaf},
{0x9293,0x9293,0x1b51},
{0x9295,0x9295,0x1b4b},
{0x9296,0x9296,0x1b50},
{0x9298,0x9298,0xecf},
{0x929a,0x929a,0xbd4},
{0x929b,0x929b,0x1b52},
{0x929c,0x929c,0x1b4f},
{0x92a7,0x92a7,0x21d1},
{0x92ad,0x92ad,0xaae},
{0x92b3,0x92b3,0x3554},
{0x92b7,0x92b7,0x1b56},
{0x92b9,0x92b9,0x1b55},
{0x92c2,0x92c2,0x3b7c},
{0x92cb,0x92cc,0x3b7d},
{0x92cf,0x92cf,0x1b54},
{0x92d0,0x92d0,0x21d7},
{0x92d2,0x92d2,0xe5d},
{0x92d3,0x92d3,0x21db},
{0x92d5,0x92d5,0x21d9},
{0x92d7,0x92d7,0x21d5},
{0x92d9,0x92d9,0x21d6},
{0x92df,0x92df,0x3b7f},
{0x92e0,0x92e0,0x21da},
{0x92e4,0x92e4,0x985},
{0x92e7,0x92e7,0x21d4},
{0x92e9,0x92e9,0x1b57},
{0x92ea,0x92ea,0xe2f},
{0x92ed,0x92ed,0x4f6},
{0x92f2,0x92f2,0xdb8},
{0x92f3,0x92f3,0xbb0},
{0x92f8,0x92f8,0x692},
{0x92f9,0x92f9,0x20b0},
{0x92fa,0x92fa,0x1b59},
{0x92fb,0x92fb,0x21de},
{0x92fc,0x92fc,0x7ef},
{0x92ff,0x92ff,0x21e1},
{0x9302,0x9302,0x21e3},
{0x9304,0x9304,0x345a},
{0x9306,0x9306,0x87a},
{0x930d,0x930d,0x3b80},
{0x930f,0x930f,0x1b58},
{0x9310,0x9310,0xa33},
{0x9315,0x9315,0x3b81},
{0x9318,0x9318,0xa34},
{0x9319,0x9319,0x1b5c},
{0x931a,0x931a,0x1b5e},
{0x931d,0x931d,0x21e2},
{0x931e,0x931e,0x21e0},
{0x931f,0x931f,0x3b82},
{0x9320,0x9320,0x9e3},
{0x9321,0x9321,0x21dd},
{0x9322,0x9322,0x1b5d},
{0x9323,0x9323,0x1b5f},
{0x9325,0x9325,0x21dc},
{0x9326,0x9326,0x6cb},
{0x9327,0x9327,0x3b83},
{0x9328,0x9328,0xdb7},
{0x932b,0x932b,0x90e},
{0x932c,0x932c,0xfc9},
{0x932e,0x932e,0x1b5b},
{0x932f,0x932f,0x868},
{0x9332,0x9332,0xfe5},
{0x9335,0x9335,0x1b61},
{0x933a,0x933a,0x1b60},
{0x933b,0x933b,0x1b62},
{0x9344,0x9344,0x1b5a},
{0x9347,0x9347,0x3b84},
{0x9348,0x9348,0x20a9},
{0x934a,0x934a,0x3458},
{0x934b,0x934b,0xcc1},
{0x934d,0x934d,0xc4f},
{0x9352,0x9352,0x3b85},
{0x9354,0x9354,0xbf3},
{0x9356,0x9356,0x1b67},
{0x9357,0x9357,0x21e5},
{0x935b,0x935b,0xb81},
{0x935c,0x935c,0x1b63},
{0x9360,0x9360,0x1b64},
{0x936a,0x936a,0x3b87},
{0x936c,0x936c,0x703},
{0x936d,0x936d,0x3b88},
{0x936e,0x936e,0x1b66},
{0x9370,0x9370,0x21e4},
{0x9375,0x9375,0x764},
{0x937c,0x937c,0x1b65},
{0x937e,0x937e,0x9c9},
{0x938c,0x938c,0x5d7},
{0x9394,0x9394,0x1b6b},
{0x9396,0x9396,0x82f},
{0x9397,0x9397,0xafb},
{0x939a,0x939a,0xbe6},
{0x939b,0x939b,0x3b89},
{0x93a4,0x93a4,0x21e6},
{0x93a7,0x93a7,0x599},
{0x93a9,0x93a9,0x3b8b},
{0x93ac,0x93ad,0x1b69},
{0x93ae,0x93ae,0xbdf},
{0x93b0,0x93b0,0x1b68},
{0x93b9,0x93b9,0x1b6c},
{0x93ba,0x93ba,0x3b8a},
{0x93c1,0x93c1,0x3b8c},
{0x93c3,0x93c3,0x1b72},
{0x93c6,0x93c6,0x21e7},
{0x93c8,0x93c8,0x1b75},
{0x93ca,0x93ca,0x3b8d},
{0x93d0,0x93d0,0x1b74},
{0x93d1,0x93d1,0xc27},
{0x93d6,0x93d7,0x1b6d},
{0x93d8,0x93d8,0x1b71},
{0x93dd,0x93dd,0x1b73},
{0x93de,0x93de,0x21e8},
{0x93e1,0x93e1,0x6b8},
{0x93e2,0x93e2,0x3b8e},
{0x93e4,0x93e4,0x1b76},
{0x93e5,0x93e5,0x1b70},
{0x93e8,0x93e8,0x1b6f},
{0x93f8,0x93f8,0x21e9},
{0x93fa,0x93fa,0x3b8f},
{0x93fd,0x93fd,0x3b90},
{0x9403,0x9403,0x1b7a},
{0x9407,0x9407,0x1b7b},
{0x940f,0x940f,0x3b91},
{0x9410,0x9410,0x1b7c},
{0x9413,0x9413,0x1b79},
{0x9414,0x9414,0x1b78},
{0x9418,0x9418,0x9ca},
{0x9419,0x9419,0xc82},
{0x941a,0x941a,0x1b77},
{0x9421,0x9421,0x1b80},
{0x942b,0x942b,0x1b7e},
{0x9431,0x9431,0x21ea},
{0x9434,0x9434,0x3b92},
{0x9435,0x9435,0x1b7f},
{0x9436,0x9436,0x1b7d},
{0x9438,0x9438,0xb58},
{0x943a,0x943a,0x1b81},
{0x943f,0x943f,0x3b93},
{0x9441,0x9441,0x1b82},
{0x9444,0x9444,0x1b84},
{0x9445,0x9445,0x21eb},
{0x9448,0x9448,0x21ec},
{0x9451,0x9451,0x611},
{0x9452,0x9452,0x1b83},
{0x9453,0x9453,0xf06},
{0x9455,0x9455,0x3b94},
{0x945a,0x945a,0x1b8f},
{0x945b,0x945b,0x1b85},
{0x945e,0x945e,0x1b88},
{0x9460,0x9460,0x1b86},
{0x9462,0x9462,0x1b87},
{0x946a,0x946a,0x1b89},
{0x946b,0x946b,0x3b95},
{0x9470,0x9470,0x1b8b},
{0x9472,0x9472,0x3b96},
{0x9475,0x9475,0x1b8c},
{0x9477,0x9477,0x1b8d},
{0x947c,0x947c,0x1b90},
{0x947d,0x947d,0x1b8e},
{0x947e,0x947e,0x1b91},
{0x947f,0x947f,0x1b93},
{0x9481,0x9481,0x1b92},
{0x9577,0x9577,0xbd5},
{0x9578,0x9578,0x3b97},
{0x9580,0x9580,0xef3},
{0x9582,0x9582,0x1b94},
{0x9583,0x9583,0xab0},
{0x9587,0x9587,0x1b95},
{0x9589,0x9589,0xe14},
{0x958a,0x958a,0x1b96},
{0x958b,0x958b,0x589},
{0x958f,0x958f,0x4de},
{0x9591,0x9591,0x613},
{0x9592,0x9592,0x21ed},
{0x9593,0x9593,0x612},
{0x9594,0x9594,0x1b97},
{0x9596,0x9596,0x1b98},
{0x9598,0x9599,0x1b99},
{0x95a0,0x95a0,0x1b9b},
{0x95a2,0x95a2,0x614},
{0x95a3,0x95a3,0x5b3},
{0x95a4,0x95a4,0x7f0},
{0x95a5,0x95a5,0xd4a},
{0x95a6,0x95a6,0x3b98},
{0x95a7,0x95a7,0x1b9d},
{0x95a8,0x95a8,0x1b9c},
{0x95a9,0x95a9,0x3b99},
{0x95ad,0x95ad,0x1b9e},
{0x95b1,0x95b1,0x3555},
{0x95b2,0x95b2,0x4fe},
{0x95b4,0x95b4,0x3b9b},
{0x95b9,0x95b9,0x1ba1},
{0x95bb,0x95bb,0x1ba0},
{0x95bc,0x95bc,0x1b9f},
{0x95bd,0x95bd,0x3b9c},
{0x95be,0x95be,0x1ba2},
{0x95c3,0x95c3,0x1ba5},
{0x95c7,0x95c7,0x48b},
{0x95ca,0x95ca,0x1ba3},
{0x95cc,0x95cc,0x1ba7},
{0x95cd,0x95cd,0x1ba6},
{0x95d4,0x95d4,0x1ba9},
{0x95d5,0x95d5,0x1ba8},
{0x95d6,0x95d6,0x1baa},
{0x95d8,0x95d8,0xc86},
{0x95da,0x95da,0x3b9d},
{0x95dc,0x95dc,0x1bab},
{0x95e1,0x95e1,0x1bac},
{0x95e2,0x95e2,0x1bae},
{0x95e5,0x95e5,0x1bad},
{0x961c,0x961c,0xdde},
{0x961d,0x961d,0x3b9e},
{0x9621,0x9621,0x1baf},
{0x9628,0x9628,0x1bb0},
{0x962a,0x962a,0x855},
{0x962e,0x962f,0x1bb1},
{0x9632,0x9632,0xe77},
{0x963b,0x963b,0xacd},
{0x963f,0x963f,0x468},
{0x9640,0x9640,0xb2b},
{0x9641,0x9641,0x3b9f},
{0x9642,0x9642,0x1bb3},
{0x9644,0x9644,0xddf},
{0x964b,0x964b,0x1bb6},
{0x964c,0x964c,0x1bb4},
{0x964d,0x964d,0x7f1},
{0x964f,0x964f,0x1bb5},
{0x9650,0x9650,0x776},
{0x9658,0x9658,0x3ba0},
{0x965b,0x965b,0xe15},
{0x965c,0x965c,0x1bb8},
{0x965d,0x965d,0x1bba},
{0x965e,0x965e,0x1bb9},
{0x965f,0x965f,0x1bbb},
{0x9662,0x9662,0x4c3},
{0x9663,0x9663,0xa1e},
{0x9664,0x9664,0x986},
{0x9665,0x9665,0x615},
{0x9666,0x9666,0x1bbc},
{0x966a,0x966a,0xd1c},
{0x966c,0x966c,0x1bbe},
{0x9670,0x9670,0x4c4},
{0x9672,0x9672,0x1bbd},
{0x9673,0x9673,0xbe0},
{0x9675,0x9675,0xf95},
{0x9676,0x9676,0xc83},
{0x9677,0x9677,0x1bb7},
{0x9678,0x9678,0xf6e},
{0x967a,0x967a,0x765},
{0x967d,0x967d,0xf45},
{0x9684,0x9684,0x3ba1},
{0x9685,0x9685,0x6f1},
{0x9686,0x9686,0xf7c},
{0x9688,0x9688,0x6fe},
{0x968a,0x968a,0xb42},
{0x968b,0x968b,0x186c},
{0x968d,0x968d,0x1bbf},
{0x968e,0x968e,0x58a},
{0x968f,0x968f,0xa35},
{0x9694,0x9694,0x5b4},
{0x9695,0x9695,0x1bc1},
{0x9697,0x9697,0x1bc2},
{0x9698,0x9698,0x1bc0},
{0x9699,0x9699,0x73a},
{0x969b,0x969b,0x84d},
{0x969c,0x969c,0x9cb},
{0x969d,0x969d,0x21f0},
{0x96a0,0x96a0,0x4c5},
{0x96a3,0x96a3,0xfa1},
{0x96a4,0x96a4,0x3ba2},
{0x96a7,0x96a7,0x1bc4},
{0x96a8,0x96a8,0x1b0b},
{0x96a9,0x96a9,0x3ba3},
{0x96aa,0x96aa,0x1bc3},
{0x96af,0x96af,0x21f1},
{0x96b0,0x96b0,0x1bc7},
{0x96b1,0x96b2,0x1bc5},
{0x96b4,0x96b4,0x1bc8},
{0x96b6,0x96b6,0x1bc9},
{0x96b7,0x96b7,0xfb4},
{0x96b8,0x96b9,0x1bca},
{0x96bb,0x96bb,0xa6d},
{0x96bc,0x96bc,0xd4f},
{0x96c0,0x96c0,0xa43},
{0x96c1,0x96c1,0x623},
{0x96c4,0x96c4,0xf24},
{0x96c5,0x96c5,0x56d},
{0x96c6,0x96c6,0x942},
{0x96c7,0x96c7,0x78f},
{0x96c9,0x96c9,0x1bce},
{0x96cb,0x96cb,0x1bcd},
{0x96cc,0x96cc,0x8c1},
{0x96cd,0x96cd,0x1bcf},
{0x96ce,0x96ce,0x1bcc},
{0x96d1,0x96d1,0x876},
{0x96d2,0x96d2,0x3ba4},
{0x96d5,0x96d5,0x1bd3},
{0x96d6,0x96d6,0x1992},
{0x96d9,0x96d9,0x10eb},
{0x96db,0x96db,0xa3d},
{0x96dc,0x96dc,0x1bd1},
{0x96de,0x96de,0x3ba6},
{0x96e2,0x96e2,0xf6d},
{0x96e3,0x96e3,0xcc9},
{0x96e8,0x96e8,0x4cd},
{0x96e9,0x96e9,0x3ba7},
{0x96ea,0x96ea,0xa87},
{0x96eb,0x96eb,0x8e2},
{0x96f0,0x96f0,0xe07},
{0x96f1,0x96f1,0x3ba8},
{0x96f2,0x96f2,0x4e2},
{0x96f6,0x96f6,0xfb5},
{0x96f7,0x96f7,0xf55},
{0x96f9,0x96f9,0x1bd4},
{0x96fb,0x96fb,0xc3f},
{0x9700,0x9700,0x927},
{0x9702,0x9702,0x3ba9},
{0x9704,0x9704,0x1bd5},
{0x9706,0x9706,0x1bd6},
{0x9707,0x9707,0xa12},
{0x9708,0x9708,0x1bd7},
{0x9709,0x9709,0x3baa},
{0x970a,0x970a,0xfb6},
{0x970d,0x970d,0x1bd2},
{0x970e,0x970e,0x1bd9},
{0x970f,0x970f,0x1bdb},
{0x9711,0x9711,0x1bda},
{0x9713,0x9713,0x1bd8},
{0x9716,0x9716,0x1bdc},
{0x9719,0x9719,0x1bdd},
{0x971c,0x971c,0xafc},
{0x971e,0x971e,0x562},
{0x9724,0x9724,0x1bde},
{0x9727,0x9727,0xec4},
{0x972a,0x972a,0x1bdf},
{0x9730,0x9730,0x1be0},
{0x9732,0x9732,0xfd0},
{0x9733,0x9733,0x21f2},
{0x9738,0x9738,0x1414},
{0x9739,0x9739,0x1be1},
{0x973b,0x973b,0x21f3},
{0x973d,0x973e,0x1be2},
{0x9742,0x9742,0x1be7},
{0x9743,0x9743,0x21f4},
{0x9744,0x9744,0x1be4},
{0x9746,0x9746,0x1be5},
{0x9748,0x9748,0x1be6},
{0x9749,0x9749,0x1be8},
{0x974d,0x974d,0x21f5},
{0x974f,0x974f,0x21f6},
{0x9751,0x9751,0x21f7},
{0x9752,0x9752,0xa68},
{0x9756,0x9756,0xf03},
{0x9759,0x9759,0xa69},
{0x975a,0x975a,0x3bab},
{0x975c,0x975c,0x1be9},
{0x975e,0x975e,0xd87},
{0x9760,0x9760,0x1bea},
{0x9761,0x9761,0x1d06},
{0x9762,0x9762,0xed8},
{0x9763,0x9763,0x3bac},
{0x9764,0x9764,0x1beb},
{0x9766,0x9766,0x1bec},
{0x9768,0x9768,0x1bed},
{0x9769,0x9769,0x5b5},
{0x976b,0x976b,0x1bef},
{0x976d,0x976d,0xa1f},
{0x976e,0x976e,0x3bad},
{0x9771,0x9771,0x1bf0},
{0x9773,0x9773,0x3bae},
{0x9774,0x9774,0x6fa},
{0x9779,0x9779,0x1bf1},
{0x977a,0x977a,0x1bf5},
{0x977c,0x977c,0x1bf3},
{0x9781,0x9781,0x1bf4},
{0x9784,0x9784,0x5d1},
{0x9785,0x9785,0x1bf2},
{0x9786,0x9786,0x1bf6},
{0x978b,0x978b,0x1bf7},
{0x978d,0x978d,0x48c},
{0x978f,0x9790,0x1bf8},
{0x9798,0x9798,0x9cc},
{0x979a,0x979a,0x3baf},
{0x979c,0x979c,0x1bfa},
{0x97a0,0x97a0,0x661},
{0x97a2,0x97a2,0x3bb0},
{0x97a3,0x97a3,0x1bfd},
{0x97a6,0x97a6,0x1bfc},
{0x97a8,0x97a8,0x1bfb},
{0x97ab,0x97ab,0x1a34},
{0x97ad,0x97ad,0xe2c},
{0x97b3,0x97b4,0x1bfe},
{0x97b5,0x97b6,0x3bb1},
{0x97c3,0x97c3,0x1c00},
{0x97c6,0x97c6,0x1c01},
{0x97c8,0x97c8,0x1c02},
{0x97cb,0x97cb,0x1c03},
{0x97d3,0x97d3,0x616},
{0x97d9,0x97d9,0x3bb3},
{0x97dc,0x97dc,0x1c04},
{0x97de,0x97de,0x3bb4},
{0x97ed,0x97ed,0x1c05},
{0x97ee,0x97ee,0xcd9},
{0x97f2,0x97f2,0x1c07},
{0x97f3,0x97f3,0x53b},
{0x97f4,0x97f4,0x3bb5},
{0x97f5,0x97f5,0x1c0a},
{0x97f6,0x97f6,0x1c09},
{0x97fb,0x97fb,0x4c6},
{0x97ff,0x97ff,0x6b9},
{0x9801,0x9801,0xe17},
{0x9802,0x9802,0xbd6},
{0x9803,0x9803,0x812},
{0x9805,0x9805,0x7f2},
{0x9806,0x9806,0x971},
{0x9808,0x9808,0xa22},
{0x980a,0x980a,0x3bb6},
{0x980c,0x980c,0x1c0c},
{0x980e,0x980e,0x3bb7},
{0x980f,0x980f,0x1c0b},
{0x9810,0x9810,0xf2c},
{0x9811,0x9811,0x624},
{0x9812,0x9812,0xd66},
{0x9813,0x9813,0xcb4},
{0x9817,0x9817,0xa42},
{0x9818,0x9818,0xf96},
{0x981a,0x981a,0x731},
{0x981e,0x981e,0x3bb8},
{0x9821,0x9821,0x1c0f},
{0x9823,0x9823,0x3bb9},
{0x9824,0x9824,0x1c0e},
{0x982b,0x982b,0x3bba},
{0x982c,0x982c,0xe79},
{0x982d,0x982d,0xc84},
{0x9830,0x9830,0x1e73},
{0x9834,0x9834,0x4f2},
{0x9837,0x9837,0x1c10},
{0x9838,0x9838,0x1c0d},
{0x9839,0x9839,0x37b3},
{0x983b,0x983b,0xdc3},
{0x983c,0x983c,0xf54},
{0x983d,0x983d,0x1c11},
{0x983e,0x983e,0x3bbb},
{0x9846,0x9846,0x1c12},
{0x984b,0x984b,0x1c14},
{0x984c,0x984c,0xb4a},
{0x984d,0x984e,0x5b9},
{0x984f,0x984f,0x1c13},
{0x9852,0x9853,0x3bbc},
{0x9854,0x9854,0x625},
{0x9855,0x9855,0x766},
{0x9857,0x9857,0x21f9},
{0x9858,0x9858,0x626},
{0x9859,0x9859,0x3bbe},
{0x985a,0x985a,0x1e48},
{0x985b,0x985b,0xc39},
{0x985e,0x985e,0xfa8},
{0x9865,0x9865,0x21fa},
{0x9867,0x9867,0x790},
{0x986b,0x986b,0x1c15},
{0x986c,0x986c,0x3bbf},
{0x986f,0x9871,0x1c16},
{0x9873,0x9873,0x1c1a},
{0x9874,0x9874,0x1c19},
{0x98a8,0x98a8,0xde9},
{0x98aa,0x98aa,0x1c1b},
{0x98af,0x98af,0x1c1c},
{0x98b1,0x98b1,0x1c1d},
{0x98b6,0x98b6,0x1c1e},
{0x98b8,0x98b8,0x3bc0},
{0x98ba,0x98ba,0x3bc1},
{0x98bf,0x98bf,0x3bc2},
{0x98c3,0x98c3,0x1c20},
{0x98c4,0x98c4,0x1c1f},
{0x98c6,0x98c6,0x1c21},
{0x98c8,0x98c8,0x3bc3},
{0x98db,0x98db,0xd88},
{0x98dc,0x98dc,0x1839},
{0x98df,0x98df,0x9ef},
{0x98e0,0x98e0,0x3618},
{0x98e2,0x98e2,0x64c},
{0x98e5,0x98e5,0x3bc4},
{0x98e9,0x98e9,0x1c22},
{0x98eb,0x98eb,0x1c23},
{0x98ed,0x98ed,0x10c1},
{0x98ee,0x98ee,0x14da},
{0x98ef,0x98ef,0xd67},
{0x98f2,0x98f2,0x4bf},
{0x98f4,0x98f4,0x47f},
{0x98fc,0x98fc,0x8c2},
{0x98fd,0x98fd,0xe5e},
{0x98fe,0x98fe,0x9e6},
{0x9903,0x9903,0x1c24},
{0x9905,0x9905,0xeeb},
{0x9909,0x9909,0x1c25},
{0x990a,0x990a,0xf46},
{0x990c,0x990c,0x4e4},
{0x9910,0x9910,0x88f},
{0x9912,0x9912,0x1c26},
{0x9913,0x9913,0x56e},
{0x9914,0x9914,0x1c27},
{0x9918,0x9918,0x1c28},
{0x991d,0x991e,0x1c2a},
{0x9920,0x9920,0x1c2d},
{0x9921,0x9921,0x1c29},
{0x9924,0x9924,0x1c2c},
{0x9927,0x9927,0x21fd},
{0x9928,0x9928,0x617},
{0x992c,0x992c,0x1c2e},
{0x992e,0x992e,0x1c2f},
{0x9932,0x9933,0x3bc5},
{0x993d,0x993e,0x1c30},
{0x9940,0x9940,0x3bc7},
{0x9942,0x9942,0x1c32},
{0x9945,0x9945,0x1c34},
{0x9949,0x9949,0x1c33},
{0x994b,0x994b,0x1c36},
{0x994c,0x994c,0x1c39},
{0x994d,0x994d,0x3bc8},
{0x9950,0x9950,0x1c35},
{0x9951,0x9952,0x1c37},
{0x9955,0x9955,0x1c3a},
{0x9957,0x9957,0x6ba},
{0x995c,0x995c,0x3bc9},
{0x995f,0x995f,0x3bca},
{0x9996,0x9996,0x91f},
{0x9997,0x9998,0x1c3b},
{0x9999,0x9999,0x7f3},
{0x999e,0x999e,0x21ff},
{0x99a5,0x99a5,0x1c3d},
{0x99a8,0x99a8,0x59c},
{0x99ac,0x99ac,0xd05},
{0x99ad,0x99ae,0x1c3e},
{0x99b1,0x99b1,0x3bcb},
{0x99b3,0x99b3,0xb98},
{0x99b4,0x99b4,0xcc3},
{0x99b9,0x99ba,0x3bcc},
{0x99bc,0x99bc,0x1c40},
{0x99c1,0x99c1,0xd33},
{0x99c4,0x99c4,0xb2c},
{0x99c5,0x99c5,0x4fa},
{0x99c6,0x99c6,0x6e6},
{0x99c8,0x99c8,0x6e7},
{0x99c9,0x99c9,0x3bce},
{0x99d0,0x99d0,0xbb1},
{0x99d1,0x99d1,0x1c45},
{0x99d2,0x99d2,0x6e8},
{0x99d5,0x99d5,0x56f},
{0x99d8,0x99d8,0x1c44},
{0x99db,0x99db,0x1c42},
{0x99dd,0x99dd,0x1c43},
{0x99df,0x99df,0x1c41},
{0x99e2,0x99e2,0x1c4f},
{0x99ed,0x99ee,0x1c46},
{0x99f1,0x99f2,0x1c48},
{0x99f8,0x99f8,0x1c4b},
{0x99fb,0x99fb,0x1c4a},
{0x99ff,0x99ff,0x963},
{0x9a01,0x9a01,0x1c4c},
{0x9a02,0x9a02,0x3bcf},
{0x9a05,0x9a05,0x1c4e},
{0x9a08,0x9a08,0x37ba},
{0x9a0e,0x9a0e,0x64d},
{0x9a0f,0x9a0f,0x1c4d},
{0x9a12,0x9a12,0xafd},
{0x9a13,0x9a13,0x767},
{0x9a16,0x9a16,0x3bd0},
{0x9a19,0x9a19,0x1c50},
{0x9a24,0x9a24,0x3bd1},
{0x9a27,0x9a27,0x3bd2},
{0x9a28,0x9a28,0xb2d},
{0x9a2b,0x9a2b,0x1c51},
{0x9a2d,0x9a2e,0x3bd3},
{0x9a30,0x9a30,0xc85},
{0x9a36,0x9a36,0x3bd5},
{0x9a37,0x9a37,0x1c52},
{0x9a38,0x9a38,0x3bd6},
{0x9a3e,0x9a3e,0x1c57},
{0x9a40,0x9a40,0x1c55},
{0x9a42,0x9a42,0x1c54},
{0x9a43,0x9a43,0x1c56},
{0x9a45,0x9a45,0x1c53},
{0x9a4a,0x9a4a,0x3bd7},
{0x9a4d,0x9a4d,0x1c59},
{0x9a4e,0x9a4e,0x2200},
{0x9a52,0x9a52,0x1e2f},
{0x9a55,0x9a55,0x1c58},
{0x9a56,0x9a56,0x3bd8},
{0x9a57,0x9a57,0x1c5b},
{0x9a5a,0x9a5a,0x6bb},
{0x9a5b,0x9a5b,0x1c5a},
{0x9a5f,0x9a5f,0x1c5c},
{0x9a62,0x9a62,0x1c5d},
{0x9a64,0x9a64,0x1c5f},
{0x9a65,0x9a65,0x1c5e},
{0x9a69,0x9a69,0x1c60},
{0x9a6a,0x9a6a,0x1c62},
{0x9a6b,0x9a6b,0x1c61},
{0x9aa8,0x9aa8,0x80e},
{0x9aad,0x9aad,0x1c63},
{0x9ab0,0x9ab0,0x1c64},
{0x9ab5,0x9ab6,0x3bd9},
{0x9ab8,0x9ab8,0x59a},
{0x9abc,0x9abc,0x1c65},
{0x9ac0,0x9ac0,0x1c66},
{0x9ac4,0x9ac4,0xa37},
{0x9acf,0x9acf,0x1c67},
{0x9ad1,0x9ad1,0x1c68},
{0x9ad3,0x9ad4,0x1c69},
{0x9ad8,0x9ad8,0x7f4},
{0x9ad9,0x9ad9,0x2201},
{0x9adc,0x9adc,0x2202},
{0x9ade,0x9adf,0x1c6b},
{0x9ae2,0x9ae3,0x1c6d},
{0x9ae6,0x9ae6,0x1c6f},
{0x9aea,0x9aea,0xd45},
{0x9aeb,0x9aeb,0x1c71},
{0x9aed,0x9aed,0xd98},
{0x9aee,0x9aee,0x1c72},
{0x9aef,0x9aef,0x1c70},
{0x9af1,0x9af1,0x1c74},
{0x9af4,0x9af4,0x1c73},
{0x9af7,0x9af7,0x1c75},
{0x9af9,0x9af9,0x3bdb},
{0x9afb,0x9afb,0x1c76},
{0x9b03,0x9b03,0x3bdc},
{0x9b06,0x9b06,0x1c77},
{0x9b18,0x9b18,0x1c78},
{0x9b1a,0x9b1a,0x1c79},
{0x9b1f,0x9b1f,0x1c7a},
{0x9b20,0x9b20,0x3bdd},
{0x9b22,0x9b23,0x1c7b},
{0x9b25,0x9b25,0x1c7d},
{0x9b27,0x9b2a,0x1c7e},
{0x9b2e,0x9b2f,0x1c82},
{0x9b31,0x9b31,0x14d4},
{0x9b32,0x9b32,0x1c84},
{0x9b33,0x9b34,0x3bde},
{0x9b3b,0x9b3b,0x17a9},
{0x9b3c,0x9b3c,0x64e},
{0x9b41,0x9b41,0x57f},
{0x9b42,0x9b42,0x822},
{0x9b43,0x9b43,0x1c86},
{0x9b44,0x9b44,0x1c85},
{0x9b45,0x9b45,0xeb1},
{0x9b4d,0x9b4e,0x1c88},
{0x9b4f,0x9b4f,0x1c87},
{0x9b51,0x9b51,0x1c8a},
{0x9b54,0x9b54,0xe90},
{0x9b58,0x9b58,0x1c8b},
{0x9b5a,0x9b5a,0x695},
{0x9b6f,0x9b6f,0xfcb},
{0x9b72,0x9b72,0x2204},
{0x9b73,0x9b73,0x3be0},
{0x9b74,0x9b74,0x1c8c},
{0x9b75,0x9b75,0x2203},
{0x9b79,0x9b79,0x3be1},
{0x9b83,0x9b83,0x1c8e},
{0x9b8e,0x9b8e,0x482},
{0x9b8f,0x9b8f,0x2205},
{0x9b91,0x9b91,0x1c8f},
{0x9b92,0x9b92,0xdfb},
{0x9b93,0x9b93,0x1c8d},
{0x9b96,0x9b97,0x1c90},
{0x9b9f,0x9ba0,0x1c92},
{0x9ba7,0x9ba7,0x3be2},
{0x9ba8,0x9ba8,0x1c94},
{0x9baa,0x9baa,0xe9c},
{0x9bab,0x9bab,0x87b},
{0x9bad,0x9bad,0x86a},
{0x9bae,0x9bae,0xab1},
{0x9bb1,0x9bb1,0x2206},
{0x9bb4,0x9bb4,0x1c95},
{0x9bb9,0x9bb9,0x1c98},
{0x9bbb,0x9bbb,0x2207},
{0x9bc0,0x9bc0,0x1c96},
{0x9bc1,0x9bc1,0x3be3},
{0x9bc6,0x9bc6,0x1c99},
{0x9bc7,0x9bc7,0x3be4},
{0x9bc9,0x9bc9,0x7a5},
{0x9bca,0x9bca,0x1c97},
{0x9bcf,0x9bcf,0x1c9a},
{0x9bd1,0x9bd2,0x1c9b},
{0x9bd4,0x9bd4,0x1ca0},
{0x9bd6,0x9bd6,0x878},
{0x9bd7,0x9bd7,0x3be5},
{0x9bdb,0x9bdb,0xb44},
{0x9bdf,0x9bdf,0x37bf},
{0x9be1,0x9be1,0x1ca1},
{0x9be2,0x9be2,0x1c9e},
{0x9be3,0x9be3,0x1c9d},
{0x9be4,0x9be4,0x1c9f},
{0x9be7,0x9be7,0x3be6},
{0x9be8,0x9be8,0x735},
{0x9beb,0x9beb,0x3be7},
{0x9bf0,0x9bf0,0x1ca5},
{0x9bf1,0x9bf1,0x1ca4},
{0x9bf2,0x9bf2,0x1ca3},
{0x9bf5,0x9bf5,0x477},
{0x9bf7,0x9bf7,0x3be8},
{0x9bfa,0x9bfa,0x3be9},
{0x9bfd,0x9bfd,0x3bea},
{0x9c00,0x9c00,0x2208},
{0x9c04,0x9c04,0x1caf},
{0x9c06,0x9c06,0x1cab},
{0x9c08,0x9c08,0x1cac},
{0x9c09,0x9c09,0x1ca8},
{0x9c0a,0x9c0a,0x1cae},
{0x9c0b,0x9c0b,0x3beb},
{0x9c0c,0x9c0c,0x1caa},
{0x9c0d,0x9c0d,0x5c0},
{0x9c10,0x9c10,0xff2},
{0x9c12,0x9c12,0x1cad},
{0x9c13,0x9c13,0x1ca9},
{0x9c14,0x9c14,0x1ca7},
{0x9c15,0x9c15,0x1ca6},
{0x9c1b,0x9c1b,0x1cb1},
{0x9c21,0x9c21,0x1cb4},
{0x9c24,0x9c24,0x1cb3},
{0x9c25,0x9c25,0x1cb2},
{0x9c27,0x9c27,0x3bec},
{0x9c2a,0x9c2a,0x3bed},
{0x9c2d,0x9c2d,0xdbb},
{0x9c2e,0x9c2e,0x1cb0},
{0x9c2f,0x9c2f,0x4b7},
{0x9c30,0x9c30,0x1cb5},
{0x9c32,0x9c32,0x1cb7},
{0x9c36,0x9c36,0x3bee},
{0x9c39,0x9c39,0x5cd},
{0x9c3a,0x9c3a,0x1ca2},
{0x9c3b,0x9c3b,0x4d9},
{0x9c3e,0x9c3e,0x1cb9},
{0x9c41,0x9c41,0x3bef},
{0x9c46,0x9c46,0x1cb8},
{0x9c47,0x9c47,0x1cb6},
{0x9c48,0x9c48,0xb6b},
{0x9c52,0x9c52,0xe9e},
{0x9c53,0x9c53,0x3bf0},
{0x9c57,0x9c57,0xfa2},
{0x9c5a,0x9c5a,0x1cba},
{0x9c60,0x9c60,0x1cbb},
{0x9c63,0x9c63,0x3bf1},
{0x9c67,0x9c67,0x1cbc},
{0x9c76,0x9c76,0x1cbd},
{0x9c77,0x9c77,0x3bf3},
{0x9c78,0x9c78,0x1cbe},
{0x9ce5,0x9ce5,0xbd7},
{0x9ce7,0x9ce7,0x1cbf},
{0x9ce9,0x9ce9,0xd4b},
{0x9ceb,0x9ceb,0x1cc4},
{0x9cec,0x9cec,0x1cc0},
{0x9cf0,0x9cf0,0x1cc1},
{0x9cf3,0x9cf3,0xe5f},
{0x9cf4,0x9cf4,0xed0},
{0x9cf6,0x9cf6,0xca8},
{0x9d02,0x9d02,0x3bf4},
{0x9d03,0x9d03,0x1cc5},
{0x9d06,0x9d06,0x1cc6},
{0x9d07,0x9d07,0xc96},
{0x9d08,0x9d08,0x1cc3},
{0x9d09,0x9d09,0x1cc2},
{0x9d0e,0x9d0e,0x52a},
{0x9d12,0x9d12,0x1cce},
{0x9d15,0x9d15,0x1ccd},
{0x9d1b,0x9d1b,0x517},
{0x9d1f,0x9d1f,0x1ccb},
{0x9d23,0x9d23,0x1cca},
{0x9d26,0x9d26,0x1cc8},
{0x9d28,0x9d28,0x5d9},
{0x9d2a,0x9d2a,0x1cc7},
{0x9d2b,0x9d2b,0x8de},
{0x9d2c,0x9d2c,0x529},
{0x9d3b,0x9d3b,0x7f5},
{0x9d3e,0x9d3e,0x1cd1},
{0x9d3f,0x9d3f,0x1cd0},
{0x9d41,0x9d41,0x1ccf},
{0x9d42,0x9d42,0x3bf5},
{0x9d44,0x9d44,0x1ccc},
{0x9d46,0x9d46,0x1cd2},
{0x9d47,0x9d47,0x3bf6},
{0x9d48,0x9d48,0x1cd3},
{0x9d50,0x9d50,0x1cd8},
{0x9d51,0x9d51,0x1cd7},
{0x9d59,0x9d59,0x1cd9},
{0x9d5c,0x9d5c,0x4cf},
{0x9d5d,0x9d5e,0x1cd4},
{0x9d60,0x9d60,0x806},
{0x9d61,0x9d61,0xec5},
{0x9d63,0x9d63,0x3bf7},
{0x9d64,0x9d64,0x1cd6},
{0x9d69,0x9d69,0x3bf8},
{0x9d6b,0x9d6b,0x220a},
{0x9d6c,0x9d6c,0xe60},
{0x9d6f,0x9d6f,0x1cde},
{0x9d70,0x9d70,0x2209},
{0x9d72,0x9d72,0x1cda},
{0x9d7a,0x9d7a,0x1cdf},
{0x9d7c,0x9d7c,0x3bf9},
{0x9d7e,0x9d7e,0x3bfa},
{0x9d87,0x9d87,0x1cdc},
{0x9d89,0x9d89,0x1cdb},
{0x9d8d,0x9d8d,0x3bfb},
{0x9d8f,0x9d8f,0x732},
{0x9d9a,0x9d9a,0x1ce0},
{0x9da4,0x9da4,0x1ce1},
{0x9da9,0x9da9,0x1ce2},
{0x9dab,0x9dab,0x1cdd},
{0x9daf,0x9daf,0x1cc9},
{0x9db1,0x9db1,0x3bfc},
{0x9db2,0x9db2,0x1ce3},
{0x9db4,0x9db4,0xbfd},
{0x9db8,0x9db8,0x1ce7},
{0x9dba,0x9dba,0x1ce8},
{0x9dbb,0x9dbb,0x1ce6},
{0x9dc1,0x9dc1,0x1ce5},
{0x9dc2,0x9dc2,0x1ceb},
{0x9dc3,0x9dc3,0x3bfd},
{0x9dc4,0x9dc4,0x1ce4},
{0x9dc6,0x9dc6,0x1ce9},
{0x9dc7,0x9dc7,0x3bfe},
{0x9dcf,0x9dcf,0x1cea},
{0x9dd3,0x9dd3,0x1ced},
{0x9dd6,0x9dd6,0x3bff},
{0x9dd7,0x9dd7,0x1dde},
{0x9dd9,0x9dd9,0x1cec},
{0x9ddf,0x9ddf,0x3c00},
{0x9de6,0x9de6,0x1cef},
{0x9deb,0x9deb,0x3c01},
{0x9ded,0x9ded,0x1cf0},
{0x9def,0x9def,0x1cf1},
{0x9df2,0x9df2,0xfef},
{0x9df4,0x9df4,0x3c02},
{0x9df8,0x9df8,0x1cee},
{0x9df9,0x9df9,0xb4b},
{0x9dfa,0x9dfa,0x85d},
{0x9dfd,0x9dfd,0x1cf2},
{0x9e15,0x9e15,0x3c03},
{0x9e19,0x9e19,0x220c},
{0x9e1a,0x9e1b,0x1cf3},
{0x9e1d,0x9e1d,0x3c04},
{0x9e1e,0x9e1e,0x1cf5},
{0x9e75,0x9e75,0x1cf6},
{0x9e78,0x9e78,0x768},
{0x9e79,0x9e79,0x1cf7},
{0x9e7c,0x9e7c,0x1dfd},
{0x9e7d,0x9e7d,0x1cf8},
{0x9e7f,0x9e7f,0x8db},
{0x9e81,0x9e81,0x1cf9},
{0x9e88,0x9e88,0x1cfa},
{0x9e8b,0x9e8c,0x1cfb},
{0x9e91,0x9e91,0x1cff},
{0x9e92,0x9e92,0x1cfd},
{0x9e93,0x9e93,0xfe2},
{0x9e95,0x9e95,0x1cfe},
{0x9e97,0x9e97,0xfb7},
{0x9e9d,0x9e9d,0x1d00},
{0x9e9f,0x9e9f,0xfa3},
{0x9ea4,0x9ea4,0x3c05},
{0x9ea5,0x9ea5,0x1d01},
{0x9ea6,0x9ea6,0xd34},
{0x9ea8,0x9ea8,0x3c06},
{0x9ea9,0x9ea9,0x1d02},
{0x9eaa,0x9eaa,0x1d04},
{0x9eac,0x9eac,0x3c07},
{0x9ead,0x9ead,0x1d05},
{0x9eb4,0x9eb4,0x1e02},
{0x9eb5,0x9eb5,0x1e75},
{0x9eb8,0x9eb8,0x1d03},
{0x9eb9,0x9eb9,0x7ff},
{0x9eba,0x9eba,0xed9},
{0x9ebb,0x9ebb,0xe91},
{0x9ebc,0x9ebc,0x1284},
{0x9ebe,0x9ebe,0x14ff},
{0x9ebf,0x9ebf,0xea9},
{0x9ec3,0x9ec3,0x340b},
{0x9ec4,0x9ec4,0x52b},
{0x9ecc,0x9ecc,0x1d07},
{0x9ecd,0x9ecd,0x66a},
{0x9ece,0x9ed0,0x1d08},
{0x9ed1,0x9ed1,0x220d},
{0x9ed2,0x9ed2,0x807},
{0x9ed4,0x9ed4,0x1d0b},
{0x9ed8,0x9ed8,0x160d},
{0x9ed9,0x9ed9,0xee7},
{0x9edb,0x9edb,0xb43},
{0x9edc,0x9edc,0x1d0c},
{0x9edd,0x9edd,0x1d0e},
{0x9ede,0x9ede,0x1d0d},
{0x9ee0,0x9ee0,0x1d0f},
{0x9ee5,0x9ee5,0x1d10},
{0x9ee7,0x9ee7,0x3c08},
{0x9ee8,0x9ee8,0x1d11},
{0x9eee,0x9eee,0x3c09},
{0x9eef,0x9eef,0x1d12},
{0x9ef4,0x9ef4,0x1d13},
{0x9ef6,0x9ef7,0x1d14},
{0x9ef9,0x9ef9,0x1d16},
{0x9efb,0x9efd,0x1d17},
{0x9f07,0x9f08,0x1d1a},
{0x9f0e,0x9f0e,0xc1e},
{0x9f10,0x9f10,0x3c0a},
{0x9f12,0x9f12,0x3c0b},
{0x9f13,0x9f13,0x791},
{0x9f15,0x9f15,0x1d1d},
{0x9f17,0x9f17,0x3c0c},
{0x9f19,0x9f19,0x3c0d},
{0x9f20,0x9f20,0xacf},
{0x9f21,0x9f21,0x1d1e},
{0x9f2c,0x9f2c,0x1d1f},
{0x9f2f,0x9f2f,0x3c0e},
{0x9f37,0x9f37,0x3c0f},
{0x9f39,0x9f39,0x3c10},
{0x9f3b,0x9f3b,0xd93},
{0x9f3e,0x9f3e,0x1d20},
{0x9f41,0x9f41,0x3c11},
{0x9f45,0x9f45,0x3c12},
{0x9f4a,0x9f4a,0x1d21},
{0x9f4b,0x9f4b,0x170a},
{0x9f4e,0x9f4e,0x1a7b},
{0x9f4f,0x9f4f,0x1c06},
{0x9f52,0x9f52,0x1d22},
{0x9f54,0x9f54,0x1d23},
{0x9f57,0x9f57,0x3c13},
{0x9f5f,0x9f61,0x1d25},
{0x9f62,0x9f62,0xfb8},
{0x9f63,0x9f63,0x1d24},
{0x9f66,0x9f67,0x1d28},
{0x9f68,0x9f68,0x3c14},
{0x9f6a,0x9f6a,0x1d2b},
{0x9f6c,0x9f6c,0x1d2a},
{0x9f71,0x9f71,0x3c15},
{0x9f72,0x9f72,0x1d2d},
{0x9f75,0x9f75,0x3c16},
{0x9f76,0x9f76,0x1d2e},
{0x9f77,0x9f77,0x1d2c},
{0x9f8d,0x9f8d,0xf7e},
{0x9f90,0x9f90,0x3c17},
{0x9f94,0x9f94,0x3c18},
{0x9f95,0x9f95,0x1d2f},
{0x9f9c,0x9f9c,0x1d30},
{0x9f9d,0x9f9d,0x1727},
{0x9fa0,0x9fa0,0x1d31},
{0x9fa2,0x9fa2,0x3c19},
{0xfa0e,0xfa0e,0x20da},
{0xfa0f,0xfa0f,0x20e5},
{0xfa12,0xfa12,0x2121},
{0xfa13,0xfa13,0x2131},
{0xfa14,0xfa14,0x2133},
{0xfa16,0xfa16,0x2164},
{0xfa17,0xfa17,0x217b},
{0xfa18,0xfa1a,0x2183},
{0xfa1b,0xfa1b,0x2187},
{0xfa1c,0xfa1c,0x218b},
{0xfa1d,0xfa1d,0x218e},
{0xfa1e,0xfa1e,0x2197},
{0xfa1f,0xfa1f,0x21a2},
{0xfa20,0xfa21,0x21a4},
{0xfa22,0xfa22,0x21ae},
{0xfa23,0xfa23,0x21b6},
{0xfa25,0xfa25,0x21b9},
{0xfa26,0xfa26,0x21bc},
{0xfa27,0xfa27,0x21d8},
{0xfa28,0xfa28,0x21df},
{0xfa29,0xfa29,0x21ef},
{0xfa2a,0xfa2b,0x21fb},
{0xfa2c,0xfa2c,0x21fe},
{0xfa2d,0xfa2d,0x220b},
{0xfb00,0xfb00,0x248e},
{0xfb01,0xfb02,0x70},
{0xfb03,0xfb04,0x248f},
{0xfe30,0xfe30,0x1eda},
{0xfe31,0xfe32,0x1ed4},
{0xfe33,0xfe33,0x1ed2},
{0xfe35,0xfe36,0x1edb},
{0xfe37,0xfe38,0x1ee1},
{0xfe39,0xfe3a,0x1edd},
{0xfe3b,0xfe3c,0x1eeb},
{0xfe3d,0xfe3e,0x1ee5},
{0xfe3f,0xfe40,0x1ee3},
{0xfe41,0xfe44,0x1ee7},
{0xff01,0xff01,0x282},
{0xff02,0xff02,0x1f47},
{0xff03,0xff03,0x2cc},
{0xff04,0xff04,0x2c8},
{0xff05,0xff05,0x2cb},
{0xff06,0xff06,0x2cd},
{0xff07,0xff07,0x1f46},
{0xff08,0xff09,0x2a2},
{0xff0a,0xff0a,0x2ce},
{0xff0b,0xff0b,0x2b4},
{0xff0c,0xff0c,0x27c},
{0xff0d,0xff0d,0x2b5},
{0xff0e,0xff0e,0x27d},
{0xff0f,0xff0f,0x297},
{0xff10,0xff19,0x30c},
{0xff1a,0xff1b,0x27f},
{0xff1c,0xff1c,0x2bb},
{0xff1d,0xff1d,0x2b9},
{0xff1e,0xff1e,0x2bc},
{0xff1f,0xff1f,0x281},
{0xff20,0xff20,0x2cf},
{0xff21,0xff3a,0x316},
{0xff3b,0xff3b,0x2a6},
{0xff3c,0xff3c,0x298},
{0xff3d,0xff3d,0x2a7},
{0xff3e,0xff3e,0x288},
{0xff3f,0xff3f,0x28a},
{0xff40,0xff40,0x286},
{0xff41,0xff5a,0x330},
{0xff5b,0xff5b,0x2a8},
{0xff5c,0xff5c,0x29b},
{0xff5d,0xff5d,0x2a9},
{0xff5e,0xff5e,0x299},
{0xff61,0xff9f,0x147},
{0xffe0,0xffe1,0x2c9},
{0xffe2,0xffe2,0x2ef},
{0xffe3,0xffe3,0x289},
{0xffe4,0xffe4,0x1f45},
{0xffe5,0xffe5,0x2c7},
{0xffe8,0xffe8,0x143},
};

static pdf_cmap cmap_UniJIS_X = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "UniJIS-X",
	/* usecmap */ "", NULL,
	/* wmode */ 0,
	/* codespaces */ 0, {
		{ 0, 0, 0 },
	},
	8484, 8484, (pdf_range*)cmap_UniJIS_X_ranges,
	0, 0, NULL, /* xranges */
	0, 0, NULL, /* mranges */
	0, 0, NULL, /* table */
	0, 0, 0, NULL /* splay tree */
};
