/* This is an automatically generated file. Do not edit. */

/* Adobe-Japan1-UCS2 */

static const pdf_range cmap_Adobe_Japan1_UCS2_ranges[] = {
{0x0,0x0,0xfffd},
{0x1,0x3c,0x20},
{0x3d,0x3d,0xa5},
{0x3e,0x5c,0x5d},
{0x5d,0x5d,0xa6},
{0x5e,0x5e,0x7d},
{0x5f,0x5f,0x303},
{0x60,0x60,0x2019},
{0x61,0x61,0x5c},
{0x62,0x62,0x2018},
{0x63,0x63,0x7c},
{0x64,0x64,0x7e},
{0x65,0x67,0xa1},
{0x68,0x68,0x2044},
{0x69,0x69,0x192},
{0x6a,0x6a,0xa7},
{0x6b,0x6b,0xa4},
{0x6c,0x6c,0x201c},
{0x6d,0x6d,0xab},
{0x6e,0x6f,0x2039},
{0x70,0x71,0xfb01},
{0x72,0x72,0x2012},
{0x73,0x74,0x2020},
{0x75,0x75,0xb7},
{0x76,0x76,0xb6},
{0x77,0x77,0x2022},
{0x78,0x78,0x201a},
{0x79,0x79,0x201e},
{0x7a,0x7a,0x201d},
{0x7b,0x7b,0xbb},
{0x7c,0x7c,0x2026},
{0x7d,0x7d,0x2030},
{0x7e,0x7e,0xbf},
{0x7f,0x80,0x301},
{0x81,0x81,0xaf},
{0x82,0x84,0x306},
{0x85,0x85,0x30a},
{0x86,0x86,0xb8},
{0x87,0x87,0x30b},
{0x88,0x88,0x328},
{0x89,0x89,0x30c},
{0x8a,0x8a,0x336},
{0x8b,0x8b,0xc6},
{0x8c,0x8c,0xaa},
{0x8d,0x8d,0x141},
{0x8e,0x8e,0xd8},
{0x8f,0x8f,0x152},
{0x90,0x90,0xba},
{0x91,0x91,0xe6},
{0x92,0x92,0x131},
{0x93,0x93,0x142},
{0x94,0x94,0xf8},
{0x95,0x95,0x153},
{0x96,0x96,0xdf},
{0x97,0x97,0x2d},
{0x98,0x98,0xa9},
{0x99,0x99,0xac},
{0x9a,0x9a,0xae},
{0x9b,0x9e,0xb0},
{0x9f,0x9f,0xb5},
{0xa0,0xa0,0xb9},
{0xa1,0xa3,0xbc},
{0xa4,0xa9,0xc0},
{0xaa,0xba,0xc7},
{0xbb,0xc0,0xd9},
{0xc1,0xc6,0xe0},
{0xc7,0xd7,0xe7},
{0xd8,0xde,0xf9},
{0xdf,0xdf,0x160},
{0xe0,0xe0,0x178},
{0xe1,0xe1,0x17d},
{0xe2,0xe2,0x305},
{0xe3,0xe3,0x161},
{0xe4,0xe4,0x2122},
{0xe5,0xe5,0x17e},
{0xe6,0xe6,0x30},
{0xe7,0x122,0x20},
{0x123,0x123,0xa5},
{0x124,0x144,0x5d},
{0x145,0x145,0x203e},
{0x146,0x185,0xff60},
{0x186,0x186,0xff40},
{0x187,0x188,0x30f0},
{0x189,0x189,0x30ee},
{0x18a,0x18a,0x30ab},
{0x18b,0x18b,0x30b1},
{0x18c,0x18c,0x30f4},
{0x18d,0x18d,0x30ac},
{0x18e,0x18e,0x30ae},
{0x18f,0x18f,0x30b0},
{0x190,0x190,0x30b2},
{0x191,0x191,0x30b4},
{0x192,0x192,0x30b6},
{0x193,0x193,0x30b8},
{0x194,0x194,0x30ba},
{0x195,0x195,0x30bc},
{0x196,0x196,0x30be},
{0x197,0x197,0x30c0},
{0x198,0x198,0x30c2},
{0x199,0x199,0x30c5},
{0x19a,0x19a,0x30c7},
{0x19b,0x19b,0x30c9},
{0x19c,0x19d,0x30d0},
{0x19e,0x19f,0x30d3},
{0x1a0,0x1a1,0x30d6},
{0x1a2,0x1a3,0x30d9},
{0x1a4,0x1a5,0x30dc},
{0x1a6,0x1a6,0xff60},
{0x1a7,0x1a8,0x301d},
{0x1a9,0x1f4,0x2500},
{0x1f5,0x1f5,0x2032},
{0x1f6,0x1f6,0xa8},
{0x1f7,0x1f7,0x2036},
{0x1f8,0x1f8,0x5b},
{0x1f9,0x1f9,0x5d},
{0x1fa,0x1fd,0x3008},
{0x1fe,0x201,0x300e},
{0x202,0x202,0x2012},
{0x203,0x203,0xff60},
{0x204,0x204,0x3092},
{0x205,0x205,0x3041},
{0x206,0x206,0x3043},
{0x207,0x207,0x3045},
{0x208,0x208,0x3047},
{0x209,0x209,0x3049},
{0x20a,0x20a,0x3083},
{0x20b,0x20b,0x3085},
{0x20c,0x20c,0x3087},
{0x20d,0x20d,0x3063},
{0x20e,0x20e,0x3042},
{0x20f,0x20f,0x3044},
{0x210,0x210,0x3046},
{0x211,0x211,0x3048},
{0x212,0x213,0x304a},
{0x214,0x214,0x304d},
{0x215,0x215,0x304f},
{0x216,0x216,0x3051},
{0x217,0x217,0x3053},
{0x218,0x218,0x3055},
{0x219,0x219,0x3057},
{0x21a,0x21a,0x3059},
{0x21b,0x21b,0x305b},
{0x21c,0x21c,0x305d},
{0x21d,0x21d,0x305f},
{0x21e,0x21e,0x3061},
{0x21f,0x21f,0x3064},
{0x220,0x220,0x3066},
{0x221,0x221,0x3068},
{0x222,0x227,0x306a},
{0x228,0x228,0x3072},
{0x229,0x229,0x3075},
{0x22a,0x22a,0x3078},
{0x22b,0x22b,0x307b},
{0x22c,0x230,0x307e},
{0x231,0x231,0x3084},
{0x232,0x232,0x3086},
{0x233,0x238,0x3088},
{0x239,0x239,0x308f},
{0x23a,0x23a,0x3093},
{0x23b,0x23c,0x3090},
{0x23d,0x23d,0x308e},
{0x23e,0x23e,0x304c},
{0x23f,0x23f,0x304e},
{0x240,0x240,0x3050},
{0x241,0x241,0x3052},
{0x242,0x242,0x3054},
{0x243,0x243,0x3056},
{0x244,0x244,0x3068},
{0x245,0x245,0x305a},
{0x246,0x246,0x305c},
{0x247,0x247,0x305e},
{0x248,0x248,0x3060},
{0x249,0x249,0x3062},
{0x24a,0x24a,0x3065},
{0x24b,0x24b,0x3067},
{0x24c,0x24c,0x3069},
{0x24d,0x24e,0x3070},
{0x24f,0x250,0x3073},
{0x251,0x252,0x3076},
{0x253,0x254,0x3079},
{0x255,0x256,0x307c},
{0x257,0x257,0xc4},
{0x258,0x258,0xf9},
{0x259,0x259,0xe9},
{0x25a,0x25a,0xed},
{0x25b,0x25b,0xdf},
{0x25c,0x25c,0xe7},
{0x25d,0x25d,0xc7},
{0x25e,0x25e,0xd1},
{0x25f,0x25f,0xf1},
{0x260,0x261,0xa2},
{0x262,0x262,0xf3},
{0x263,0x263,0xfa},
{0x264,0x264,0xa1},
{0x265,0x265,0xbf},
{0x266,0x266,0xbd},
{0x267,0x267,0xd6},
{0x268,0x268,0xdc},
{0x269,0x269,0xe4},
{0x26a,0x26a,0xeb},
{0x26b,0x26b,0xef},
{0x26c,0x26c,0xf6},
{0x26d,0x26d,0xdc},
{0x26e,0x26e,0xe2},
{0x26f,0x26f,0xea},
{0x270,0x270,0xee},
{0x271,0x271,0xf4},
{0x272,0x272,0xfc},
{0x273,0x273,0xe0},
{0x274,0x274,0xe9},
{0x275,0x275,0x25b2},
{0x276,0x276,0xe1},
{0x277,0x277,0x7e},
{0x278,0x278,0x30},
{0x279,0x27b,0x3000},
{0x27c,0x27c,0xff0c},
{0x27d,0x27d,0xff0e},
{0x27e,0x27e,0x30fb},
{0x27f,0x280,0xff1a},
{0x281,0x281,0xff1f},
{0x282,0x282,0xff01},
{0x283,0x284,0x309b},
{0x285,0x285,0xb4},
{0x286,0x286,0xff40},
{0x287,0x287,0xa8},
{0x288,0x288,0xff3e},
{0x289,0x289,0x203e},
{0x28a,0x28a,0xff3f},
{0x28b,0x28c,0x30fd},
{0x28d,0x28e,0x309d},
{0x28f,0x28f,0x3003},
{0x290,0x290,0x4edd},
{0x291,0x293,0x3005},
{0x294,0x294,0x30fc},
{0x295,0x295,0x2015},
{0x296,0x296,0x2010},
{0x297,0x297,0xff0f},
{0x298,0x298,0xff3c},
{0x299,0x299,0xff5e},
{0x29a,0x29a,0x2016},
{0x29b,0x29b,0xff5c},
{0x29c,0x29c,0x2026},
{0x29d,0x29d,0x2025},
{0x29e,0x29f,0x2018},
{0x2a0,0x2a1,0x201c},
{0x2a2,0x2a3,0xff08},
{0x2a4,0x2a5,0x3014},
{0x2a6,0x2a6,0xff3b},
{0x2a7,0x2a7,0xff3d},
{0x2a8,0x2a8,0xff5b},
{0x2a9,0x2a9,0xff5d},
{0x2aa,0x2b3,0x3008},
{0x2b4,0x2b4,0xff0b},
{0x2b5,0x2b5,0xff0d},
{0x2b6,0x2b6,0xb1},
{0x2b7,0x2b7,0xd7},
{0x2b8,0x2b8,0xf7},
{0x2b9,0x2b9,0xff1d},
{0x2ba,0x2ba,0x2260},
{0x2bb,0x2bb,0xff1c},
{0x2bc,0x2bc,0xff1e},
{0x2bd,0x2be,0x2266},
{0x2bf,0x2bf,0x221e},
{0x2c0,0x2c0,0x2234},
{0x2c1,0x2c1,0x2642},
{0x2c2,0x2c2,0x2640},
{0x2c3,0x2c3,0xb0},
{0x2c4,0x2c5,0x2032},
{0x2c6,0x2c6,0x2103},
{0x2c7,0x2c7,0xffe5},
{0x2c8,0x2c8,0xff04},
{0x2c9,0x2ca,0xa2},
{0x2cb,0x2cb,0xff05},
{0x2cc,0x2cc,0xff03},
{0x2cd,0x2cd,0xff06},
{0x2ce,0x2ce,0xff0a},
{0x2cf,0x2cf,0xff20},
{0x2d0,0x2d0,0xa7},
{0x2d1,0x2d1,0x2606},
{0x2d2,0x2d2,0x2605},
{0x2d3,0x2d3,0x25cb},
{0x2d4,0x2d4,0x25cf},
{0x2d5,0x2d5,0x25ce},
{0x2d6,0x2d6,0x25c7},
{0x2d7,0x2d7,0x25c6},
{0x2d8,0x2d8,0x25a1},
{0x2d9,0x2d9,0x25a0},
{0x2da,0x2da,0x25b3},
{0x2db,0x2db,0x25b2},
{0x2dc,0x2dc,0x25bd},
{0x2dd,0x2dd,0x25bc},
{0x2de,0x2de,0x203b},
{0x2df,0x2df,0x3012},
{0x2e0,0x2e0,0x2192},
{0x2e1,0x2e2,0x2190},
{0x2e3,0x2e3,0x2193},
{0x2e4,0x2e4,0x3013},
{0x2e5,0x2e5,0x2208},
{0x2e6,0x2e6,0x220b},
{0x2e7,0x2e8,0x2286},
{0x2e9,0x2ea,0x2282},
{0x2eb,0x2eb,0x222a},
{0x2ec,0x2ec,0x2229},
{0x2ed,0x2ee,0x2227},
{0x2ef,0x2ef,0xac},
{0x2f0,0x2f0,0x21d2},
{0x2f1,0x2f1,0x21d4},
{0x2f2,0x2f2,0x2200},
{0x2f3,0x2f3,0x2203},
{0x2f4,0x2f4,0x2220},
{0x2f5,0x2f5,0x22a5},
{0x2f6,0x2f6,0x2312},
{0x2f7,0x2f7,0x2202},
{0x2f8,0x2f8,0x2207},
{0x2f9,0x2f9,0x2261},
{0x2fa,0x2fa,0x2252},
{0x2fb,0x2fc,0x226a},
{0x2fd,0x2fd,0x221a},
{0x2fe,0x2fe,0x223d},
{0x2ff,0x2ff,0x221d},
{0x300,0x300,0x2235},
{0x301,0x302,0x222b},
{0x303,0x303,0x212b},
{0x304,0x304,0x2030},
{0x305,0x305,0x266f},
{0x306,0x306,0x266d},
{0x307,0x307,0x266a},
{0x308,0x309,0x2020},
{0x30a,0x30a,0xb6},
{0x30b,0x30b,0x25ef},
{0x30c,0x315,0xff10},
{0x316,0x32f,0xff21},
{0x330,0x349,0xff41},
{0x34a,0x39c,0x3041},
{0x39d,0x3f2,0x30a1},
{0x3f3,0x403,0x391},
{0x404,0x40a,0x3a3},
{0x40b,0x41b,0x3b1},
{0x41c,0x422,0x3c3},
{0x423,0x428,0x410},
{0x429,0x429,0x401},
{0x42a,0x449,0x416},
{0x44a,0x44a,0x451},
{0x44b,0x464,0x436},
{0x465,0x465,0x4e9c},
{0x466,0x466,0x5516},
{0x467,0x467,0x5a03},
{0x468,0x468,0x963f},
{0x469,0x469,0x54c0},
{0x46a,0x46a,0x611b},
{0x46b,0x46b,0x6328},
{0x46c,0x46c,0x59f6},
{0x46d,0x46d,0x9022},
{0x46e,0x46e,0x8475},
{0x46f,0x46f,0x831c},
{0x470,0x470,0x7a50},
{0x471,0x471,0x60aa},
{0x472,0x472,0x63e1},
{0x473,0x473,0x6e25},
{0x474,0x474,0x65ed},
{0x475,0x475,0x8466},
{0x476,0x476,0x82a6},
{0x477,0x477,0x9bf5},
{0x478,0x478,0x6893},
{0x479,0x479,0x5727},
{0x47a,0x47a,0x65a1},
{0x47b,0x47b,0x6271},
{0x47c,0x47c,0x5b9b},
{0x47d,0x47d,0x59d0},
{0x47e,0x47e,0x867b},
{0x47f,0x47f,0x98f4},
{0x480,0x480,0x7d62},
{0x481,0x481,0x7dbe},
{0x482,0x482,0x9b8e},
{0x483,0x483,0x6216},
{0x484,0x484,0x7c9f},
{0x485,0x485,0x88b7},
{0x486,0x486,0x5b89},
{0x487,0x487,0x5eb5},
{0x488,0x488,0x6309},
{0x489,0x489,0x6697},
{0x48a,0x48a,0x6848},
{0x48b,0x48b,0x95c7},
{0x48c,0x48c,0x978d},
{0x48d,0x48d,0x674f},
{0x48e,0x48e,0x4ee5},
{0x48f,0x48f,0x4f0a},
{0x490,0x490,0x4f4d},
{0x491,0x491,0x4f9d},
{0x492,0x492,0x5049},
{0x493,0x493,0x56f2},
{0x494,0x494,0x5937},
{0x495,0x495,0x59d4},
{0x496,0x496,0x5a01},
{0x497,0x497,0x5c09},
{0x498,0x498,0x60df},
{0x499,0x499,0x610f},
{0x49a,0x49a,0x6170},
{0x49b,0x49b,0x6613},
{0x49c,0x49c,0x6905},
{0x49d,0x49d,0x70ba},
{0x49e,0x49e,0x754f},
{0x49f,0x49f,0x7570},
{0x4a0,0x4a0,0x79fb},
{0x4a1,0x4a1,0x7dad},
{0x4a2,0x4a2,0x7def},
{0x4a3,0x4a3,0x80c3},
{0x4a4,0x4a4,0x840e},
{0x4a5,0x4a5,0x8863},
{0x4a6,0x4a6,0x8b02},
{0x4a7,0x4a7,0x9055},
{0x4a8,0x4a8,0x907a},
{0x4a9,0x4a9,0x533b},
{0x4aa,0x4aa,0x4e95},
{0x4ab,0x4ab,0x4ea5},
{0x4ac,0x4ac,0x57df},
{0x4ad,0x4ad,0x80b2},
{0x4ae,0x4ae,0x90c1},
{0x4af,0x4af,0x78ef},
{0x4b0,0x4b0,0x4e00},
{0x4b1,0x4b1,0x58f1},
{0x4b2,0x4b2,0x6ea2},
{0x4b3,0x4b3,0x9038},
{0x4b4,0x4b4,0x7a32},
{0x4b5,0x4b5,0x8328},
{0x4b6,0x4b6,0x828b},
{0x4b7,0x4b7,0x9c2f},
{0x4b8,0x4b8,0x5141},
{0x4b9,0x4b9,0x5370},
{0x4ba,0x4ba,0x54bd},
{0x4bb,0x4bb,0x54e1},
{0x4bc,0x4bc,0x56e0},
{0x4bd,0x4bd,0x59fb},
{0x4be,0x4be,0x5f15},
{0x4bf,0x4bf,0x98f2},
{0x4c0,0x4c0,0x6deb},
{0x4c1,0x4c1,0x80e4},
{0x4c2,0x4c2,0x852d},
{0x4c3,0x4c3,0x9662},
{0x4c4,0x4c4,0x9670},
{0x4c5,0x4c5,0x96a0},
{0x4c6,0x4c6,0x97fb},
{0x4c7,0x4c7,0x540b},
{0x4c8,0x4c8,0x53f3},
{0x4c9,0x4c9,0x5b87},
{0x4ca,0x4ca,0x70cf},
{0x4cb,0x4cb,0x7fbd},
{0x4cc,0x4cc,0x8fc2},
{0x4cd,0x4cd,0x96e8},
{0x4ce,0x4ce,0x536f},
{0x4cf,0x4cf,0x9d5c},
{0x4d0,0x4d0,0x7aba},
{0x4d1,0x4d1,0x4e11},
{0x4d2,0x4d2,0x7893},
{0x4d3,0x4d3,0x81fc},
{0x4d4,0x4d4,0x6e26},
{0x4d5,0x4d5,0x5618},
{0x4d6,0x4d6,0x5504},
{0x4d7,0x4d7,0x6b1d},
{0x4d8,0x4d8,0x851a},
{0x4d9,0x4d9,0x9c3b},
{0x4da,0x4da,0x59e5},
{0x4db,0x4db,0x53a9},
{0x4dc,0x4dc,0x6d66},
{0x4dd,0x4dd,0x74dc},
{0x4de,0x4de,0x958f},
{0x4df,0x4df,0x5642},
{0x4e0,0x4e0,0x4e91},
{0x4e1,0x4e1,0x904b},
{0x4e2,0x4e2,0x96f2},
{0x4e3,0x4e3,0x834f},
{0x4e4,0x4e4,0x990c},
{0x4e5,0x4e5,0x53e1},
{0x4e6,0x4e6,0x55b6},
{0x4e7,0x4e7,0x5b30},
{0x4e8,0x4e8,0x5f71},
{0x4e9,0x4e9,0x6620},
{0x4ea,0x4ea,0x66f3},
{0x4eb,0x4eb,0x6804},
{0x4ec,0x4ec,0x6c38},
{0x4ed,0x4ed,0x6cf3},
{0x4ee,0x4ee,0x6d29},
{0x4ef,0x4ef,0x745b},
{0x4f0,0x4f0,0x76c8},
{0x4f1,0x4f1,0x7a4e},
{0x4f2,0x4f2,0x9834},
{0x4f3,0x4f3,0x82f1},
{0x4f4,0x4f4,0x885b},
{0x4f5,0x4f5,0x8a60},
{0x4f6,0x4f6,0x92ed},
{0x4f7,0x4f7,0x6db2},
{0x4f8,0x4f8,0x75ab},
{0x4f9,0x4f9,0x76ca},
{0x4fa,0x4fa,0x99c5},
{0x4fb,0x4fb,0x60a6},
{0x4fc,0x4fc,0x8b01},
{0x4fd,0x4fd,0x8d8a},
{0x4fe,0x4fe,0x95b2},
{0x4ff,0x4ff,0x698e},
{0x500,0x500,0x53ad},
{0x501,0x501,0x5186},
{0x502,0x502,0x5712},
{0x503,0x503,0x5830},
{0x504,0x504,0x5944},
{0x505,0x505,0x5bb4},
{0x506,0x506,0x5ef6},
{0x507,0x507,0x6028},
{0x508,0x508,0x63a9},
{0x509,0x509,0x63f4},
{0x50a,0x50a,0x6cbf},
{0x50b,0x50b,0x6f14},
{0x50c,0x50c,0x708e},
{0x50d,0x50d,0x7114},
{0x50e,0x50e,0x7159},
{0x50f,0x50f,0x71d5},
{0x510,0x510,0x733f},
{0x511,0x511,0x7e01},
{0x512,0x512,0x8276},
{0x513,0x513,0x82d1},
{0x514,0x514,0x8597},
{0x515,0x515,0x9060},
{0x516,0x516,0x925b},
{0x517,0x517,0x9d1b},
{0x518,0x518,0x5869},
{0x519,0x519,0x65bc},
{0x51a,0x51a,0x6c5a},
{0x51b,0x51b,0x7525},
{0x51c,0x51c,0x51f9},
{0x51d,0x51d,0x592e},
{0x51e,0x51e,0x5965},
{0x51f,0x51f,0x5f80},
{0x520,0x520,0x5fdc},
{0x521,0x521,0x62bc},
{0x522,0x522,0x65fa},
{0x523,0x523,0x6a2a},
{0x524,0x524,0x6b27},
{0x525,0x525,0x6bb4},
{0x526,0x526,0x738b},
{0x527,0x527,0x7fc1},
{0x528,0x528,0x8956},
{0x529,0x529,0x9d2c},
{0x52a,0x52a,0x9d0e},
{0x52b,0x52b,0x9ec4},
{0x52c,0x52c,0x5ca1},
{0x52d,0x52d,0x6c96},
{0x52e,0x52e,0x837b},
{0x52f,0x52f,0x5104},
{0x530,0x530,0x5c4b},
{0x531,0x531,0x61b6},
{0x532,0x532,0x81c6},
{0x533,0x533,0x6876},
{0x534,0x534,0x7261},
{0x535,0x535,0x4e59},
{0x536,0x536,0x4ffa},
{0x537,0x537,0x5378},
{0x538,0x538,0x6069},
{0x539,0x539,0x6e29},
{0x53a,0x53a,0x7a4f},
{0x53b,0x53b,0x97f3},
{0x53c,0x53c,0x4e0b},
{0x53d,0x53d,0x5316},
{0x53e,0x53e,0x4eee},
{0x53f,0x53f,0x4f55},
{0x540,0x540,0x4f3d},
{0x541,0x541,0x4fa1},
{0x542,0x542,0x4f73},
{0x543,0x543,0x52a0},
{0x544,0x544,0x53ef},
{0x545,0x545,0x5609},
{0x546,0x546,0x590f},
{0x547,0x547,0x5ac1},
{0x548,0x548,0x5bb6},
{0x549,0x549,0x5be1},
{0x54a,0x54a,0x79d1},
{0x54b,0x54b,0x6687},
{0x54c,0x54c,0x679c},
{0x54d,0x54d,0x67b6},
{0x54e,0x54e,0x6b4c},
{0x54f,0x54f,0x6cb3},
{0x550,0x550,0x706b},
{0x551,0x551,0x73c2},
{0x552,0x552,0x798d},
{0x553,0x553,0x79be},
{0x554,0x554,0x7a3c},
{0x555,0x555,0x7b87},
{0x556,0x556,0x82b1},
{0x557,0x557,0x82db},
{0x558,0x558,0x8304},
{0x559,0x559,0x8377},
{0x55a,0x55a,0x83ef},
{0x55b,0x55b,0x83d3},
{0x55c,0x55c,0x8766},
{0x55d,0x55d,0x8ab2},
{0x55e,0x55e,0x5629},
{0x55f,0x55f,0x8ca8},
{0x560,0x560,0x8fe6},
{0x561,0x561,0x904e},
{0x562,0x562,0x971e},
{0x563,0x563,0x868a},
{0x564,0x564,0x4fc4},
{0x565,0x565,0x5ce8},
{0x566,0x566,0x6211},
{0x567,0x567,0x7259},
{0x568,0x568,0x753b},
{0x569,0x569,0x81e5},
{0x56a,0x56a,0x82bd},
{0x56b,0x56b,0x86fe},
{0x56c,0x56c,0x8cc0},
{0x56d,0x56d,0x96c5},
{0x56e,0x56e,0x9913},
{0x56f,0x56f,0x99d5},
{0x570,0x570,0x4ecb},
{0x571,0x571,0x4f1a},
{0x572,0x572,0x89e3},
{0x573,0x573,0x56de},
{0x574,0x574,0x584a},
{0x575,0x575,0x58ca},
{0x576,0x576,0x5efb},
{0x577,0x577,0x5feb},
{0x578,0x578,0x602a},
{0x579,0x579,0x6094},
{0x57a,0x57a,0x6062},
{0x57b,0x57b,0x61d0},
{0x57c,0x57c,0x6212},
{0x57d,0x57d,0x62d0},
{0x57e,0x57e,0x6539},
{0x57f,0x57f,0x9b41},
{0x580,0x580,0x6666},
{0x581,0x581,0x68b0},
{0x582,0x582,0x6d77},
{0x583,0x583,0x7070},
{0x584,0x584,0x754c},
{0x585,0x585,0x7686},
{0x586,0x586,0x7d75},
{0x587,0x587,0x82a5},
{0x588,0x588,0x87f9},
{0x589,0x589,0x958b},
{0x58a,0x58a,0x968e},
{0x58b,0x58b,0x8c9d},
{0x58c,0x58c,0x51f1},
{0x58d,0x58d,0x52be},
{0x58e,0x58e,0x5916},
{0x58f,0x58f,0x54b3},
{0x590,0x590,0x5bb3},
{0x591,0x591,0x5d16},
{0x592,0x592,0x6168},
{0x593,0x593,0x6982},
{0x594,0x594,0x6daf},
{0x595,0x595,0x788d},
{0x596,0x596,0x84cb},
{0x597,0x597,0x8857},
{0x598,0x598,0x8a72},
{0x599,0x599,0x93a7},
{0x59a,0x59a,0x9ab8},
{0x59b,0x59b,0x6d6c},
{0x59c,0x59c,0x99a8},
{0x59d,0x59d,0x86d9},
{0x59e,0x59e,0x57a3},
{0x59f,0x59f,0x67ff},
{0x5a0,0x5a0,0x86ce},
{0x5a1,0x5a1,0x920e},
{0x5a2,0x5a2,0x5283},
{0x5a3,0x5a3,0x5687},
{0x5a4,0x5a4,0x5404},
{0x5a5,0x5a5,0x5ed3},
{0x5a6,0x5a6,0x62e1},
{0x5a7,0x5a7,0x64b9},
{0x5a8,0x5a8,0x683c},
{0x5a9,0x5a9,0x6838},
{0x5aa,0x5aa,0x6bbb},
{0x5ab,0x5ab,0x7372},
{0x5ac,0x5ac,0x78ba},
{0x5ad,0x5ad,0x7a6b},
{0x5ae,0x5ae,0x899a},
{0x5af,0x5af,0x89d2},
{0x5b0,0x5b0,0x8d6b},
{0x5b1,0x5b1,0x8f03},
{0x5b2,0x5b2,0x90ed},
{0x5b3,0x5b3,0x95a3},
{0x5b4,0x5b4,0x9694},
{0x5b5,0x5b5,0x9769},
{0x5b6,0x5b6,0x5b66},
{0x5b7,0x5b7,0x5cb3},
{0x5b8,0x5b8,0x697d},
{0x5b9,0x5ba,0x984d},
{0x5bb,0x5bb,0x639b},
{0x5bc,0x5bc,0x7b20},
{0x5bd,0x5bd,0x6a2b},
{0x5be,0x5be,0x6a7f},
{0x5bf,0x5bf,0x68b6},
{0x5c0,0x5c0,0x9c0d},
{0x5c1,0x5c1,0x6f5f},
{0x5c2,0x5c2,0x5272},
{0x5c3,0x5c3,0x559d},
{0x5c4,0x5c4,0x6070},
{0x5c5,0x5c5,0x62ec},
{0x5c6,0x5c6,0x6d3b},
{0x5c7,0x5c7,0x6e07},
{0x5c8,0x5c8,0x6ed1},
{0x5c9,0x5c9,0x845b},
{0x5ca,0x5ca,0x8910},
{0x5cb,0x5cb,0x8f44},
{0x5cc,0x5cc,0x4e14},
{0x5cd,0x5cd,0x9c39},
{0x5ce,0x5ce,0x53f6},
{0x5cf,0x5cf,0x691b},
{0x5d0,0x5d0,0x6a3a},
{0x5d1,0x5d1,0x9784},
{0x5d2,0x5d2,0x682a},
{0x5d3,0x5d3,0x515c},
{0x5d4,0x5d4,0x7ac3},
{0x5d5,0x5d5,0x84b2},
{0x5d6,0x5d6,0x91dc},
{0x5d7,0x5d7,0x938c},
{0x5d8,0x5d8,0x565b},
{0x5d9,0x5d9,0x9d28},
{0x5da,0x5da,0x6822},
{0x5db,0x5db,0x8305},
{0x5dc,0x5dc,0x8431},
{0x5dd,0x5dd,0x7ca5},
{0x5de,0x5de,0x5208},
{0x5df,0x5df,0x82c5},
{0x5e0,0x5e0,0x74e6},
{0x5e1,0x5e1,0x4e7e},
{0x5e2,0x5e2,0x4f83},
{0x5e3,0x5e3,0x51a0},
{0x5e4,0x5e4,0x5bd2},
{0x5e5,0x5e5,0x520a},
{0x5e6,0x5e6,0x52d8},
{0x5e7,0x5e7,0x52e7},
{0x5e8,0x5e8,0x5dfb},
{0x5e9,0x5e9,0x559a},
{0x5ea,0x5ea,0x582a},
{0x5eb,0x5eb,0x59e6},
{0x5ec,0x5ec,0x5b8c},
{0x5ed,0x5ed,0x5b98},
{0x5ee,0x5ee,0x5bdb},
{0x5ef,0x5ef,0x5e72},
{0x5f0,0x5f0,0x5e79},
{0x5f1,0x5f1,0x60a3},
{0x5f2,0x5f2,0x611f},
{0x5f3,0x5f3,0x6163},
{0x5f4,0x5f4,0x61be},
{0x5f5,0x5f5,0x63db},
{0x5f6,0x5f6,0x6562},
{0x5f7,0x5f7,0x67d1},
{0x5f8,0x5f8,0x6853},
{0x5f9,0x5f9,0x68fa},
{0x5fa,0x5fa,0x6b3e},
{0x5fb,0x5fb,0x6b53},
{0x5fc,0x5fc,0x6c57},
{0x5fd,0x5fd,0x6f22},
{0x5fe,0x5fe,0x6f97},
{0x5ff,0x5ff,0x6f45},
{0x600,0x600,0x74b0},
{0x601,0x601,0x7518},
{0x602,0x602,0x76e3},
{0x603,0x603,0x770b},
{0x604,0x604,0x7aff},
{0x605,0x605,0x7ba1},
{0x606,0x606,0x7c21},
{0x607,0x607,0x7de9},
{0x608,0x608,0x7f36},
{0x609,0x609,0x7ff0},
{0x60a,0x60a,0x809d},
{0x60b,0x60b,0x8266},
{0x60c,0x60c,0x839e},
{0x60d,0x60d,0x89b3},
{0x60e,0x60e,0x8acc},
{0x60f,0x60f,0x8cab},
{0x610,0x610,0x9084},
{0x611,0x611,0x9451},
{0x612,0x612,0x9593},
{0x613,0x613,0x9591},
{0x614,0x614,0x95a2},
{0x615,0x615,0x9665},
{0x616,0x616,0x97d3},
{0x617,0x617,0x9928},
{0x618,0x618,0x8218},
{0x619,0x619,0x4e38},
{0x61a,0x61a,0x542b},
{0x61b,0x61b,0x5cb8},
{0x61c,0x61c,0x5dcc},
{0x61d,0x61d,0x73a9},
{0x61e,0x61e,0x764c},
{0x61f,0x61f,0x773c},
{0x620,0x620,0x5ca9},
{0x621,0x621,0x7feb},
{0x622,0x622,0x8d0b},
{0x623,0x623,0x96c1},
{0x624,0x624,0x9811},
{0x625,0x625,0x9854},
{0x626,0x626,0x9858},
{0x627,0x627,0x4f01},
{0x628,0x628,0x4f0e},
{0x629,0x629,0x5371},
{0x62a,0x62a,0x559c},
{0x62b,0x62b,0x5668},
{0x62c,0x62c,0x57fa},
{0x62d,0x62d,0x5947},
{0x62e,0x62e,0x5b09},
{0x62f,0x62f,0x5bc4},
{0x630,0x630,0x5c90},
{0x631,0x631,0x5e0c},
{0x632,0x632,0x5e7e},
{0x633,0x633,0x5fcc},
{0x634,0x634,0x63ee},
{0x635,0x635,0x673a},
{0x636,0x636,0x65d7},
{0x637,0x637,0x65e2},
{0x638,0x638,0x671f},
{0x639,0x639,0x68cb},
{0x63a,0x63a,0x68c4},
{0x63b,0x63b,0x6a5f},
{0x63c,0x63c,0x5e30},
{0x63d,0x63d,0x6bc5},
{0x63e,0x63e,0x6c17},
{0x63f,0x63f,0x6c7d},
{0x640,0x640,0x757f},
{0x641,0x641,0x7948},
{0x642,0x642,0x5b63},
{0x643,0x643,0x7a00},
{0x644,0x644,0x7d00},
{0x645,0x645,0x5fbd},
{0x646,0x646,0x898f},
{0x647,0x647,0x8a18},
{0x648,0x648,0x8cb4},
{0x649,0x649,0x8d77},
{0x64a,0x64a,0x8ecc},
{0x64b,0x64b,0x8f1d},
{0x64c,0x64c,0x98e2},
{0x64d,0x64d,0x9a0e},
{0x64e,0x64e,0x9b3c},
{0x64f,0x64f,0x4e80},
{0x650,0x650,0x507d},
{0x651,0x651,0x5100},
{0x652,0x652,0x5993},
{0x653,0x653,0x5b9c},
{0x654,0x654,0x622f},
{0x655,0x655,0x6280},
{0x656,0x656,0x64ec},
{0x657,0x657,0x6b3a},
{0x658,0x658,0x72a0},
{0x659,0x659,0x7591},
{0x65a,0x65a,0x7947},
{0x65b,0x65b,0x7fa9},
{0x65c,0x65c,0x87fb},
{0x65d,0x65d,0x8abc},
{0x65e,0x65e,0x8b70},
{0x65f,0x65f,0x63ac},
{0x660,0x660,0x83ca},
{0x661,0x661,0x97a0},
{0x662,0x662,0x5409},
{0x663,0x663,0x5403},
{0x664,0x664,0x55ab},
{0x665,0x665,0x6854},
{0x666,0x666,0x6a58},
{0x667,0x667,0x8a70},
{0x668,0x668,0x7827},
{0x669,0x669,0x6775},
{0x66a,0x66a,0x9ecd},
{0x66b,0x66b,0x5374},
{0x66c,0x66c,0x5ba2},
{0x66d,0x66d,0x811a},
{0x66e,0x66e,0x8650},
{0x66f,0x66f,0x9006},
{0x670,0x670,0x4e18},
{0x671,0x671,0x4e45},
{0x672,0x672,0x4ec7},
{0x673,0x673,0x4f11},
{0x674,0x674,0x53ca},
{0x675,0x675,0x5438},
{0x676,0x676,0x5bae},
{0x677,0x677,0x5f13},
{0x678,0x678,0x6025},
{0x679,0x679,0x6551},
{0x67a,0x67a,0x673d},
{0x67b,0x67b,0x6c42},
{0x67c,0x67c,0x6c72},
{0x67d,0x67d,0x6ce3},
{0x67e,0x67e,0x7078},
{0x67f,0x67f,0x7403},
{0x680,0x680,0x7a76},
{0x681,0x681,0x7aae},
{0x682,0x682,0x7b08},
{0x683,0x683,0x7d1a},
{0x684,0x684,0x7cfe},
{0x685,0x685,0x7d66},
{0x686,0x686,0x65e7},
{0x687,0x687,0x725b},
{0x688,0x688,0x53bb},
{0x689,0x689,0x5c45},
{0x68a,0x68a,0x5de8},
{0x68b,0x68b,0x62d2},
{0x68c,0x68c,0x62e0},
{0x68d,0x68d,0x6319},
{0x68e,0x68e,0x6e20},
{0x68f,0x68f,0x865a},
{0x690,0x690,0x8a31},
{0x691,0x691,0x8ddd},
{0x692,0x692,0x92f8},
{0x693,0x693,0x6f01},
{0x694,0x694,0x79a6},
{0x695,0x695,0x9b5a},
{0x696,0x696,0x4ea8},
{0x697,0x698,0x4eab},
{0x699,0x699,0x4f9b},
{0x69a,0x69a,0x4fa0},
{0x69b,0x69b,0x50d1},
{0x69c,0x69c,0x5147},
{0x69d,0x69d,0x7af6},
{0x69e,0x69e,0x5171},
{0x69f,0x69f,0x51f6},
{0x6a0,0x6a0,0x5354},
{0x6a1,0x6a1,0x5321},
{0x6a2,0x6a2,0x537f},
{0x6a3,0x6a3,0x53eb},
{0x6a4,0x6a4,0x55ac},
{0x6a5,0x6a5,0x5883},
{0x6a6,0x6a6,0x5ce1},
{0x6a7,0x6a7,0x5f37},
{0x6a8,0x6a8,0x5f4a},
{0x6a9,0x6a9,0x602f},
{0x6aa,0x6aa,0x6050},
{0x6ab,0x6ab,0x606d},
{0x6ac,0x6ac,0x631f},
{0x6ad,0x6ad,0x6559},
{0x6ae,0x6ae,0x6a4b},
{0x6af,0x6af,0x6cc1},
{0x6b0,0x6b0,0x72c2},
{0x6b1,0x6b1,0x72ed},
{0x6b2,0x6b2,0x77ef},
{0x6b3,0x6b3,0x80f8},
{0x6b4,0x6b4,0x8105},
{0x6b5,0x6b5,0x8208},
{0x6b6,0x6b6,0x854e},
{0x6b7,0x6b7,0x90f7},
{0x6b8,0x6b8,0x93e1},
{0x6b9,0x6b9,0x97ff},
{0x6ba,0x6ba,0x9957},
{0x6bb,0x6bb,0x9a5a},
{0x6bc,0x6bc,0x4ef0},
{0x6bd,0x6bd,0x51dd},
{0x6be,0x6be,0x5c2d},
{0x6bf,0x6bf,0x6681},
{0x6c0,0x6c0,0x696d},
{0x6c1,0x6c1,0x5c40},
{0x6c2,0x6c2,0x66f2},
{0x6c3,0x6c3,0x6975},
{0x6c4,0x6c4,0x7389},
{0x6c5,0x6c5,0x6850},
{0x6c6,0x6c6,0x7c81},
{0x6c7,0x6c7,0x50c5},
{0x6c8,0x6c8,0x52e4},
{0x6c9,0x6c9,0x5747},
{0x6ca,0x6ca,0x5dfe},
{0x6cb,0x6cb,0x9326},
{0x6cc,0x6cc,0x65a4},
{0x6cd,0x6cd,0x6b23},
{0x6ce,0x6ce,0x6b3d},
{0x6cf,0x6cf,0x7434},
{0x6d0,0x6d0,0x7981},
{0x6d1,0x6d1,0x79bd},
{0x6d2,0x6d2,0x7b4b},
{0x6d3,0x6d3,0x7dca},
{0x6d4,0x6d4,0x82b9},
{0x6d5,0x6d5,0x83cc},
{0x6d6,0x6d6,0x887f},
{0x6d7,0x6d7,0x895f},
{0x6d8,0x6d8,0x8b39},
{0x6d9,0x6d9,0x8fd1},
{0x6da,0x6da,0x91d1},
{0x6db,0x6db,0x541f},
{0x6dc,0x6dc,0x9280},
{0x6dd,0x6dd,0x4e5d},
{0x6de,0x6de,0x5036},
{0x6df,0x6df,0x53e5},
{0x6e0,0x6e0,0x533a},
{0x6e1,0x6e1,0x72d7},
{0x6e2,0x6e2,0x7396},
{0x6e3,0x6e3,0x77e9},
{0x6e4,0x6e4,0x82e6},
{0x6e5,0x6e5,0x8eaf},
{0x6e6,0x6e6,0x99c6},
{0x6e7,0x6e7,0x99c8},
{0x6e8,0x6e8,0x99d2},
{0x6e9,0x6e9,0x5177},
{0x6ea,0x6ea,0x611a},
{0x6eb,0x6eb,0x865e},
{0x6ec,0x6ec,0x55b0},
{0x6ed,0x6ed,0x7a7a},
{0x6ee,0x6ee,0x5076},
{0x6ef,0x6ef,0x5bd3},
{0x6f0,0x6f0,0x9047},
{0x6f1,0x6f1,0x9685},
{0x6f2,0x6f2,0x4e32},
{0x6f3,0x6f3,0x6adb},
{0x6f4,0x6f4,0x91e7},
{0x6f5,0x6f5,0x5c51},
{0x6f6,0x6f6,0x5c48},
{0x6f7,0x6f7,0x6398},
{0x6f8,0x6f8,0x7a9f},
{0x6f9,0x6f9,0x6c93},
{0x6fa,0x6fa,0x9774},
{0x6fb,0x6fb,0x8f61},
{0x6fc,0x6fc,0x7aaa},
{0x6fd,0x6fd,0x718a},
{0x6fe,0x6fe,0x9688},
{0x6ff,0x6ff,0x7c82},
{0x700,0x700,0x6817},
{0x701,0x701,0x7e70},
{0x702,0x702,0x6851},
{0x703,0x703,0x936c},
{0x704,0x704,0x52f2},
{0x705,0x705,0x541b},
{0x706,0x706,0x85ab},
{0x707,0x707,0x8a13},
{0x708,0x708,0x7fa4},
{0x709,0x709,0x8ecd},
{0x70a,0x70a,0x90e1},
{0x70b,0x70b,0x5366},
{0x70c,0x70c,0x8888},
{0x70d,0x70d,0x7941},
{0x70e,0x70e,0x4fc2},
{0x70f,0x70f,0x50be},
{0x710,0x710,0x5211},
{0x711,0x711,0x5144},
{0x712,0x712,0x5553},
{0x713,0x713,0x572d},
{0x714,0x714,0x73ea},
{0x715,0x715,0x578b},
{0x716,0x716,0x5951},
{0x717,0x717,0x5f62},
{0x718,0x718,0x5f84},
{0x719,0x719,0x6075},
{0x71a,0x71a,0x6176},
{0x71b,0x71b,0x6167},
{0x71c,0x71c,0x61a9},
{0x71d,0x71d,0x63b2},
{0x71e,0x71e,0x643a},
{0x71f,0x71f,0x656c},
{0x720,0x720,0x666f},
{0x721,0x721,0x6842},
{0x722,0x722,0x6e13},
{0x723,0x723,0x7566},
{0x724,0x724,0x7a3d},
{0x725,0x725,0x7cfb},
{0x726,0x726,0x7d4c},
{0x727,0x727,0x7d99},
{0x728,0x728,0x7e4b},
{0x729,0x729,0x7f6b},
{0x72a,0x72a,0x830e},
{0x72b,0x72b,0x834a},
{0x72c,0x72c,0x86cd},
{0x72d,0x72d,0x8a08},
{0x72e,0x72e,0x8a63},
{0x72f,0x72f,0x8b66},
{0x730,0x730,0x8efd},
{0x731,0x731,0x981a},
{0x732,0x732,0x9d8f},
{0x733,0x733,0x82b8},
{0x734,0x734,0x8fce},
{0x735,0x735,0x9be8},
{0x736,0x736,0x5287},
{0x737,0x737,0x621f},
{0x738,0x738,0x6483},
{0x739,0x739,0x6fc0},
{0x73a,0x73a,0x9699},
{0x73b,0x73b,0x6841},
{0x73c,0x73c,0x5091},
{0x73d,0x73d,0x6b20},
{0x73e,0x73e,0x6c7a},
{0x73f,0x73f,0x6f54},
{0x740,0x740,0x7a74},
{0x741,0x741,0x7d50},
{0x742,0x742,0x8840},
{0x743,0x743,0x8a23},
{0x744,0x744,0x6708},
{0x745,0x745,0x4ef6},
{0x746,0x746,0x5039},
{0x747,0x747,0x5026},
{0x748,0x748,0x5065},
{0x749,0x749,0x517c},
{0x74a,0x74a,0x5238},
{0x74b,0x74b,0x5263},
{0x74c,0x74c,0x55a7},
{0x74d,0x74d,0x570f},
{0x74e,0x74e,0x5805},
{0x74f,0x74f,0x5acc},
{0x750,0x750,0x5efa},
{0x751,0x751,0x61b2},
{0x752,0x752,0x61f8},
{0x753,0x753,0x62f3},
{0x754,0x754,0x6372},
{0x755,0x755,0x691c},
{0x756,0x756,0x6a29},
{0x757,0x757,0x727d},
{0x758,0x758,0x72ac},
{0x759,0x759,0x732e},
{0x75a,0x75a,0x7814},
{0x75b,0x75b,0x786f},
{0x75c,0x75c,0x7d79},
{0x75d,0x75d,0x770c},
{0x75e,0x75e,0x80a9},
{0x75f,0x75f,0x898b},
{0x760,0x760,0x8b19},
{0x761,0x761,0x8ce2},
{0x762,0x762,0x8ed2},
{0x763,0x763,0x9063},
{0x764,0x764,0x9375},
{0x765,0x765,0x967a},
{0x766,0x766,0x9855},
{0x767,0x767,0x9a13},
{0x768,0x768,0x9e78},
{0x769,0x769,0x5143},
{0x76a,0x76a,0x539f},
{0x76b,0x76b,0x53b3},
{0x76c,0x76c,0x5e7b},
{0x76d,0x76d,0x5f26},
{0x76e,0x76e,0x6e1b},
{0x76f,0x76f,0x6e90},
{0x770,0x770,0x7384},
{0x771,0x771,0x73fe},
{0x772,0x772,0x7d43},
{0x773,0x773,0x8237},
{0x774,0x774,0x8a00},
{0x775,0x775,0x8afa},
{0x776,0x776,0x9650},
{0x777,0x777,0x4e4e},
{0x778,0x778,0x500b},
{0x779,0x779,0x53e4},
{0x77a,0x77a,0x547c},
{0x77b,0x77b,0x56fa},
{0x77c,0x77c,0x59d1},
{0x77d,0x77d,0x5b64},
{0x77e,0x77e,0x5df1},
{0x77f,0x77f,0x5eab},
{0x780,0x780,0x5f27},
{0x781,0x781,0x6238},
{0x782,0x782,0x6545},
{0x783,0x783,0x67af},
{0x784,0x784,0x6e56},
{0x785,0x785,0x72d0},
{0x786,0x786,0x7cca},
{0x787,0x787,0x88b4},
{0x788,0x788,0x80a1},
{0x789,0x789,0x80e1},
{0x78a,0x78a,0x83f0},
{0x78b,0x78b,0x864e},
{0x78c,0x78c,0x8a87},
{0x78d,0x78d,0x8de8},
{0x78e,0x78e,0x9237},
{0x78f,0x78f,0x96c7},
{0x790,0x790,0x9867},
{0x791,0x791,0x9f13},
{0x792,0x792,0x4e94},
{0x793,0x793,0x4e92},
{0x794,0x794,0x4f0d},
{0x795,0x795,0x5348},
{0x796,0x796,0x5449},
{0x797,0x797,0x543e},
{0x798,0x798,0x5a2f},
{0x799,0x799,0x5f8c},
{0x79a,0x79a,0x5fa1},
{0x79b,0x79b,0x609f},
{0x79c,0x79c,0x68a7},
{0x79d,0x79d,0x6a8e},
{0x79e,0x79e,0x745a},
{0x79f,0x79f,0x7881},
{0x7a0,0x7a0,0x8a9e},
{0x7a1,0x7a1,0x8aa4},
{0x7a2,0x7a2,0x8b77},
{0x7a3,0x7a3,0x9190},
{0x7a4,0x7a4,0x4e5e},
{0x7a5,0x7a5,0x9bc9},
{0x7a6,0x7a6,0x4ea4},
{0x7a7,0x7a7,0x4f7c},
{0x7a8,0x7a8,0x4faf},
{0x7a9,0x7a9,0x5019},
{0x7aa,0x7aa,0x5016},
{0x7ab,0x7ab,0x5149},
{0x7ac,0x7ac,0x516c},
{0x7ad,0x7ad,0x529f},
{0x7ae,0x7ae,0x52b9},
{0x7af,0x7af,0x52fe},
{0x7b0,0x7b0,0x539a},
{0x7b1,0x7b1,0x53e3},
{0x7b2,0x7b2,0x5411},
{0x7b3,0x7b3,0x540e},
{0x7b4,0x7b4,0x5589},
{0x7b5,0x7b5,0x5751},
{0x7b6,0x7b6,0x57a2},
{0x7b7,0x7b7,0x597d},
{0x7b8,0x7b8,0x5b54},
{0x7b9,0x7b9,0x5b5d},
{0x7ba,0x7ba,0x5b8f},
{0x7bb,0x7bb,0x5de5},
{0x7bc,0x7bc,0x5de7},
{0x7bd,0x7bd,0x5df7},
{0x7be,0x7be,0x5e78},
{0x7bf,0x7bf,0x5e83},
{0x7c0,0x7c0,0x5e9a},
{0x7c1,0x7c1,0x5eb7},
{0x7c2,0x7c2,0x5f18},
{0x7c3,0x7c3,0x6052},
{0x7c4,0x7c4,0x614c},
{0x7c5,0x7c5,0x6297},
{0x7c6,0x7c6,0x62d8},
{0x7c7,0x7c7,0x63a7},
{0x7c8,0x7c8,0x653b},
{0x7c9,0x7c9,0x6602},
{0x7ca,0x7ca,0x6643},
{0x7cb,0x7cb,0x66f4},
{0x7cc,0x7cc,0x676d},
{0x7cd,0x7cd,0x6821},
{0x7ce,0x7ce,0x6897},
{0x7cf,0x7cf,0x69cb},
{0x7d0,0x7d0,0x6c5f},
{0x7d1,0x7d1,0x6d2a},
{0x7d2,0x7d2,0x6d69},
{0x7d3,0x7d3,0x6e2f},
{0x7d4,0x7d4,0x6e9d},
{0x7d5,0x7d5,0x7532},
{0x7d6,0x7d6,0x7687},
{0x7d7,0x7d7,0x786c},
{0x7d8,0x7d8,0x7a3f},
{0x7d9,0x7d9,0x7ce0},
{0x7da,0x7da,0x7d05},
{0x7db,0x7db,0x7d18},
{0x7dc,0x7dc,0x7d5e},
{0x7dd,0x7dd,0x7db1},
{0x7de,0x7de,0x8015},
{0x7df,0x7df,0x8003},
{0x7e0,0x7e0,0x80af},
{0x7e1,0x7e1,0x80b1},
{0x7e2,0x7e2,0x8154},
{0x7e3,0x7e3,0x818f},
{0x7e4,0x7e4,0x822a},
{0x7e5,0x7e5,0x8352},
{0x7e6,0x7e6,0x884c},
{0x7e7,0x7e7,0x8861},
{0x7e8,0x7e8,0x8b1b},
{0x7e9,0x7e9,0x8ca2},
{0x7ea,0x7ea,0x8cfc},
{0x7eb,0x7eb,0x90ca},
{0x7ec,0x7ec,0x9175},
{0x7ed,0x7ed,0x9271},
{0x7ee,0x7ee,0x783f},
{0x7ef,0x7ef,0x92fc},
{0x7f0,0x7f0,0x95a4},
{0x7f1,0x7f1,0x964d},
{0x7f2,0x7f2,0x9805},
{0x7f3,0x7f3,0x9999},
{0x7f4,0x7f4,0x9ad8},
{0x7f5,0x7f5,0x9d3b},
{0x7f6,0x7f6,0x525b},
{0x7f7,0x7f7,0x52ab},
{0x7f8,0x7f8,0x53f7},
{0x7f9,0x7f9,0x5408},
{0x7fa,0x7fa,0x58d5},
{0x7fb,0x7fb,0x62f7},
{0x7fc,0x7fc,0x6fe0},
{0x7fd,0x7fd,0x8c6a},
{0x7fe,0x7fe,0x8f5f},
{0x7ff,0x7ff,0x9eb9},
{0x800,0x800,0x514b},
{0x801,0x801,0x523b},
{0x802,0x802,0x544a},
{0x803,0x803,0x56fd},
{0x804,0x804,0x7a40},
{0x805,0x805,0x9177},
{0x806,0x806,0x9d60},
{0x807,0x807,0x9ed2},
{0x808,0x808,0x7344},
{0x809,0x809,0x6f09},
{0x80a,0x80a,0x8170},
{0x80b,0x80b,0x7511},
{0x80c,0x80c,0x5ffd},
{0x80d,0x80d,0x60da},
{0x80e,0x80e,0x9aa8},
{0x80f,0x80f,0x72db},
{0x810,0x810,0x8fbc},
{0x811,0x811,0x6b64},
{0x812,0x812,0x9803},
{0x813,0x813,0x4eca},
{0x814,0x814,0x56f0},
{0x815,0x815,0x5764},
{0x816,0x816,0x58be},
{0x817,0x817,0x5a5a},
{0x818,0x818,0x6068},
{0x819,0x819,0x61c7},
{0x81a,0x81a,0x660f},
{0x81b,0x81b,0x6606},
{0x81c,0x81c,0x6839},
{0x81d,0x81d,0x68b1},
{0x81e,0x81e,0x6df7},
{0x81f,0x81f,0x75d5},
{0x820,0x820,0x7d3a},
{0x821,0x821,0x826e},
{0x822,0x822,0x9b42},
{0x823,0x823,0x4e9b},
{0x824,0x824,0x4f50},
{0x825,0x825,0x53c9},
{0x826,0x826,0x5506},
{0x827,0x827,0x5d6f},
{0x828,0x828,0x5de6},
{0x829,0x829,0x5dee},
{0x82a,0x82a,0x67fb},
{0x82b,0x82b,0x6c99},
{0x82c,0x82c,0x7473},
{0x82d,0x82d,0x7802},
{0x82e,0x82e,0x8a50},
{0x82f,0x82f,0x9396},
{0x830,0x830,0x88df},
{0x831,0x831,0x5750},
{0x832,0x832,0x5ea7},
{0x833,0x833,0x632b},
{0x834,0x834,0x50b5},
{0x835,0x835,0x50ac},
{0x836,0x836,0x518d},
{0x837,0x837,0x6700},
{0x838,0x838,0x54c9},
{0x839,0x839,0x585e},
{0x83a,0x83a,0x59bb},
{0x83b,0x83b,0x5bb0},
{0x83c,0x83c,0x5f69},
{0x83d,0x83d,0x624d},
{0x83e,0x83e,0x63a1},
{0x83f,0x83f,0x683d},
{0x840,0x840,0x6b73},
{0x841,0x841,0x6e08},
{0x842,0x842,0x707d},
{0x843,0x843,0x91c7},
{0x844,0x844,0x7280},
{0x845,0x845,0x7815},
{0x846,0x846,0x7826},
{0x847,0x847,0x796d},
{0x848,0x848,0x658e},
{0x849,0x849,0x7d30},
{0x84a,0x84a,0x83dc},
{0x84b,0x84b,0x88c1},
{0x84c,0x84c,0x8f09},
{0x84d,0x84d,0x969b},
{0x84e,0x84e,0x5264},
{0x84f,0x84f,0x5728},
{0x850,0x850,0x6750},
{0x851,0x851,0x7f6a},
{0x852,0x852,0x8ca1},
{0x853,0x853,0x51b4},
{0x854,0x854,0x5742},
{0x855,0x855,0x962a},
{0x856,0x856,0x583a},
{0x857,0x857,0x698a},
{0x858,0x858,0x80b4},
{0x859,0x859,0x54b2},
{0x85a,0x85a,0x5d0e},
{0x85b,0x85b,0x57fc},
{0x85c,0x85c,0x7895},
{0x85d,0x85d,0x9dfa},
{0x85e,0x85e,0x4f5c},
{0x85f,0x85f,0x524a},
{0x860,0x860,0x548b},
{0x861,0x861,0x643e},
{0x862,0x862,0x6628},
{0x863,0x863,0x6714},
{0x864,0x864,0x67f5},
{0x865,0x865,0x7a84},
{0x866,0x866,0x7b56},
{0x867,0x867,0x7d22},
{0x868,0x868,0x932f},
{0x869,0x869,0x685c},
{0x86a,0x86a,0x9bad},
{0x86b,0x86b,0x7b39},
{0x86c,0x86c,0x5319},
{0x86d,0x86d,0x518a},
{0x86e,0x86e,0x5237},
{0x86f,0x86f,0x5bdf},
{0x870,0x870,0x62f6},
{0x871,0x871,0x64ae},
{0x872,0x872,0x64e6},
{0x873,0x873,0x672d},
{0x874,0x874,0x6bba},
{0x875,0x875,0x85a9},
{0x876,0x876,0x96d1},
{0x877,0x877,0x7690},
{0x878,0x878,0x9bd6},
{0x879,0x879,0x634c},
{0x87a,0x87a,0x9306},
{0x87b,0x87b,0x9bab},
{0x87c,0x87c,0x76bf},
{0x87d,0x87d,0x6652},
{0x87e,0x87e,0x4e09},
{0x87f,0x87f,0x5098},
{0x880,0x880,0x53c2},
{0x881,0x881,0x5c71},
{0x882,0x882,0x60e8},
{0x883,0x883,0x6492},
{0x884,0x884,0x6563},
{0x885,0x885,0x685f},
{0x886,0x886,0x71e6},
{0x887,0x887,0x73ca},
{0x888,0x888,0x7523},
{0x889,0x889,0x7b97},
{0x88a,0x88a,0x7e82},
{0x88b,0x88b,0x8695},
{0x88c,0x88c,0x8b83},
{0x88d,0x88d,0x8cdb},
{0x88e,0x88e,0x9178},
{0x88f,0x88f,0x9910},
{0x890,0x890,0x65ac},
{0x891,0x891,0x66ab},
{0x892,0x892,0x6b8b},
{0x893,0x893,0x4ed5},
{0x894,0x894,0x4ed4},
{0x895,0x895,0x4f3a},
{0x896,0x896,0x4f7f},
{0x897,0x897,0x523a},
{0x898,0x898,0x53f8},
{0x899,0x899,0x53f2},
{0x89a,0x89a,0x55e3},
{0x89b,0x89b,0x56db},
{0x89c,0x89c,0x58eb},
{0x89d,0x89d,0x59cb},
{0x89e,0x89e,0x59c9},
{0x89f,0x89f,0x59ff},
{0x8a0,0x8a0,0x5b50},
{0x8a1,0x8a1,0x5c4d},
{0x8a2,0x8a2,0x5e02},
{0x8a3,0x8a3,0x5e2b},
{0x8a4,0x8a4,0x5fd7},
{0x8a5,0x8a5,0x601d},
{0x8a6,0x8a6,0x6307},
{0x8a7,0x8a7,0x652f},
{0x8a8,0x8a8,0x5b5c},
{0x8a9,0x8a9,0x65af},
{0x8aa,0x8aa,0x65bd},
{0x8ab,0x8ab,0x65e8},
{0x8ac,0x8ac,0x679d},
{0x8ad,0x8ad,0x6b62},
{0x8ae,0x8ae,0x6b7b},
{0x8af,0x8af,0x6c0f},
{0x8b0,0x8b0,0x7345},
{0x8b1,0x8b1,0x7949},
{0x8b2,0x8b2,0x79c1},
{0x8b3,0x8b3,0x7cf8},
{0x8b4,0x8b4,0x7d19},
{0x8b5,0x8b5,0x7d2b},
{0x8b6,0x8b6,0x80a2},
{0x8b7,0x8b7,0x8102},
{0x8b8,0x8b8,0x81f3},
{0x8b9,0x8b9,0x8996},
{0x8ba,0x8ba,0x8a5e},
{0x8bb,0x8bb,0x8a69},
{0x8bc,0x8bc,0x8a66},
{0x8bd,0x8bd,0x8a8c},
{0x8be,0x8be,0x8aee},
{0x8bf,0x8bf,0x8cc7},
{0x8c0,0x8c0,0x8cdc},
{0x8c1,0x8c1,0x96cc},
{0x8c2,0x8c2,0x98fc},
{0x8c3,0x8c3,0x6b6f},
{0x8c4,0x8c4,0x4e8b},
{0x8c5,0x8c5,0x4f3c},
{0x8c6,0x8c6,0x4f8d},
{0x8c7,0x8c7,0x5150},
{0x8c8,0x8c8,0x5b57},
{0x8c9,0x8c9,0x5bfa},
{0x8ca,0x8ca,0x6148},
{0x8cb,0x8cb,0x6301},
{0x8cc,0x8cc,0x6642},
{0x8cd,0x8cd,0x6b21},
{0x8ce,0x8ce,0x6ecb},
{0x8cf,0x8cf,0x6cbb},
{0x8d0,0x8d0,0x723e},
{0x8d1,0x8d1,0x74bd},
{0x8d2,0x8d2,0x75d4},
{0x8d3,0x8d3,0x78c1},
{0x8d4,0x8d4,0x793a},
{0x8d5,0x8d5,0x800c},
{0x8d6,0x8d6,0x8033},
{0x8d7,0x8d7,0x81ea},
{0x8d8,0x8d8,0x8494},
{0x8d9,0x8d9,0x8f9e},
{0x8da,0x8da,0x6c50},
{0x8db,0x8db,0x9e7f},
{0x8dc,0x8dc,0x5f0f},
{0x8dd,0x8dd,0x8b58},
{0x8de,0x8de,0x9d2b},
{0x8df,0x8df,0x7afa},
{0x8e0,0x8e0,0x8ef8},
{0x8e1,0x8e1,0x5b8d},
{0x8e2,0x8e2,0x96eb},
{0x8e3,0x8e3,0x4e03},
{0x8e4,0x8e4,0x53f1},
{0x8e5,0x8e5,0x57f7},
{0x8e6,0x8e6,0x5931},
{0x8e7,0x8e7,0x5ac9},
{0x8e8,0x8e8,0x5ba4},
{0x8e9,0x8e9,0x6089},
{0x8ea,0x8ea,0x6e7f},
{0x8eb,0x8eb,0x6f06},
{0x8ec,0x8ec,0x75be},
{0x8ed,0x8ed,0x8cea},
{0x8ee,0x8ee,0x5b9f},
{0x8ef,0x8ef,0x8500},
{0x8f0,0x8f0,0x7be0},
{0x8f1,0x8f1,0x5072},
{0x8f2,0x8f2,0x67f4},
{0x8f3,0x8f3,0x829d},
{0x8f4,0x8f4,0x5c61},
{0x8f5,0x8f5,0x854a},
{0x8f6,0x8f6,0x7e1e},
{0x8f7,0x8f7,0x820e},
{0x8f8,0x8f8,0x5199},
{0x8f9,0x8f9,0x5c04},
{0x8fa,0x8fa,0x6368},
{0x8fb,0x8fb,0x8d66},
{0x8fc,0x8fc,0x659c},
{0x8fd,0x8fd,0x716e},
{0x8fe,0x8fe,0x793e},
{0x8ff,0x8ff,0x7d17},
{0x900,0x900,0x8005},
{0x901,0x901,0x8b1d},
{0x902,0x902,0x8eca},
{0x903,0x903,0x906e},
{0x904,0x904,0x86c7},
{0x905,0x905,0x90aa},
{0x906,0x906,0x501f},
{0x907,0x907,0x52fa},
{0x908,0x908,0x5c3a},
{0x909,0x909,0x6753},
{0x90a,0x90a,0x707c},
{0x90b,0x90b,0x7235},
{0x90c,0x90c,0x914c},
{0x90d,0x90d,0x91c8},
{0x90e,0x90e,0x932b},
{0x90f,0x90f,0x82e5},
{0x910,0x910,0x5bc2},
{0x911,0x911,0x5f31},
{0x912,0x912,0x60f9},
{0x913,0x913,0x4e3b},
{0x914,0x914,0x53d6},
{0x915,0x915,0x5b88},
{0x916,0x916,0x624b},
{0x917,0x917,0x6731},
{0x918,0x918,0x6b8a},
{0x919,0x919,0x72e9},
{0x91a,0x91a,0x73e0},
{0x91b,0x91b,0x7a2e},
{0x91c,0x91c,0x816b},
{0x91d,0x91d,0x8da3},
{0x91e,0x91e,0x9152},
{0x91f,0x91f,0x9996},
{0x920,0x920,0x5112},
{0x921,0x921,0x53d7},
{0x922,0x922,0x546a},
{0x923,0x923,0x5bff},
{0x924,0x924,0x6388},
{0x925,0x925,0x6a39},
{0x926,0x926,0x7dac},
{0x927,0x927,0x9700},
{0x928,0x928,0x56da},
{0x929,0x929,0x53ce},
{0x92a,0x92a,0x5468},
{0x92b,0x92b,0x5b97},
{0x92c,0x92c,0x5c31},
{0x92d,0x92d,0x5dde},
{0x92e,0x92e,0x4fee},
{0x92f,0x92f,0x6101},
{0x930,0x930,0x62fe},
{0x931,0x931,0x6d32},
{0x932,0x932,0x79c0},
{0x933,0x933,0x79cb},
{0x934,0x934,0x7d42},
{0x935,0x935,0x7e4d},
{0x936,0x936,0x7fd2},
{0x937,0x937,0x81ed},
{0x938,0x938,0x821f},
{0x939,0x939,0x8490},
{0x93a,0x93a,0x8846},
{0x93b,0x93b,0x8972},
{0x93c,0x93c,0x8b90},
{0x93d,0x93d,0x8e74},
{0x93e,0x93e,0x8f2f},
{0x93f,0x93f,0x9031},
{0x940,0x940,0x914b},
{0x941,0x941,0x916c},
{0x942,0x942,0x96c6},
{0x943,0x943,0x919c},
{0x944,0x944,0x4ec0},
{0x945,0x945,0x4f4f},
{0x946,0x946,0x5145},
{0x947,0x947,0x5341},
{0x948,0x948,0x5f93},
{0x949,0x949,0x620e},
{0x94a,0x94a,0x67d4},
{0x94b,0x94b,0x6c41},
{0x94c,0x94c,0x6e0b},
{0x94d,0x94d,0x7363},
{0x94e,0x94e,0x7e26},
{0x94f,0x94f,0x91cd},
{0x950,0x950,0x9283},
{0x951,0x951,0x53d4},
{0x952,0x952,0x5919},
{0x953,0x953,0x5bbf},
{0x954,0x954,0x6dd1},
{0x955,0x955,0x795d},
{0x956,0x956,0x7e2e},
{0x957,0x957,0x7c9b},
{0x958,0x958,0x587e},
{0x959,0x959,0x719f},
{0x95a,0x95a,0x51fa},
{0x95b,0x95b,0x8853},
{0x95c,0x95c,0x8ff0},
{0x95d,0x95d,0x4fca},
{0x95e,0x95e,0x5cfb},
{0x95f,0x95f,0x6625},
{0x960,0x960,0x77ac},
{0x961,0x961,0x7ae3},
{0x962,0x962,0x821c},
{0x963,0x963,0x99ff},
{0x964,0x964,0x51c6},
{0x965,0x965,0x5faa},
{0x966,0x966,0x65ec},
{0x967,0x967,0x696f},
{0x968,0x968,0x6b89},
{0x969,0x969,0x6df3},
{0x96a,0x96a,0x6e96},
{0x96b,0x96b,0x6f64},
{0x96c,0x96c,0x76fe},
{0x96d,0x96d,0x7d14},
{0x96e,0x96e,0x5de1},
{0x96f,0x96f,0x9075},
{0x970,0x970,0x9187},
{0x971,0x971,0x9806},
{0x972,0x972,0x51e6},
{0x973,0x973,0x521d},
{0x974,0x974,0x6240},
{0x975,0x975,0x6691},
{0x976,0x976,0x66d9},
{0x977,0x977,0x6e1a},
{0x978,0x978,0x5eb6},
{0x979,0x979,0x7dd2},
{0x97a,0x97a,0x7f72},
{0x97b,0x97b,0x66f8},
{0x97c,0x97c,0x85af},
{0x97d,0x97d,0x85f7},
{0x97e,0x97e,0x8af8},
{0x97f,0x97f,0x52a9},
{0x980,0x980,0x53d9},
{0x981,0x981,0x5973},
{0x982,0x982,0x5e8f},
{0x983,0x983,0x5f90},
{0x984,0x984,0x6055},
{0x985,0x985,0x92e4},
{0x986,0x986,0x9664},
{0x987,0x987,0x50b7},
{0x988,0x988,0x511f},
{0x989,0x989,0x52dd},
{0x98a,0x98a,0x5320},
{0x98b,0x98b,0x5347},
{0x98c,0x98c,0x53ec},
{0x98d,0x98d,0x54e8},
{0x98e,0x98e,0x5546},
{0x98f,0x98f,0x5531},
{0x990,0x990,0x5617},
{0x991,0x991,0x5968},
{0x992,0x992,0x59be},
{0x993,0x993,0x5a3c},
{0x994,0x994,0x5bb5},
{0x995,0x995,0x5c06},
{0x996,0x996,0x5c0f},
{0x997,0x997,0x5c11},
{0x998,0x998,0x5c1a},
{0x999,0x999,0x5e84},
{0x99a,0x99a,0x5e8a},
{0x99b,0x99b,0x5ee0},
{0x99c,0x99c,0x5f70},
{0x99d,0x99d,0x627f},
{0x99e,0x99e,0x6284},
{0x99f,0x99f,0x62db},
{0x9a0,0x9a0,0x638c},
{0x9a1,0x9a1,0x6377},
{0x9a2,0x9a2,0x6607},
{0x9a3,0x9a3,0x660c},
{0x9a4,0x9a4,0x662d},
{0x9a5,0x9a5,0x6676},
{0x9a6,0x9a6,0x677e},
{0x9a7,0x9a7,0x68a2},
{0x9a8,0x9a8,0x6a1f},
{0x9a9,0x9a9,0x6a35},
{0x9aa,0x9aa,0x6cbc},
{0x9ab,0x9ab,0x6d88},
{0x9ac,0x9ac,0x6e09},
{0x9ad,0x9ad,0x6e58},
{0x9ae,0x9ae,0x713c},
{0x9af,0x9af,0x7126},
{0x9b0,0x9b0,0x7167},
{0x9b1,0x9b1,0x75c7},
{0x9b2,0x9b2,0x7701},
{0x9b3,0x9b3,0x785d},
{0x9b4,0x9b4,0x7901},
{0x9b5,0x9b5,0x7965},
{0x9b6,0x9b6,0x79f0},
{0x9b7,0x9b7,0x7ae0},
{0x9b8,0x9b8,0x7b11},
{0x9b9,0x9b9,0x7ca7},
{0x9ba,0x9ba,0x7d39},
{0x9bb,0x9bb,0x8096},
{0x9bc,0x9bc,0x83d6},
{0x9bd,0x9bd,0x848b},
{0x9be,0x9be,0x8549},
{0x9bf,0x9bf,0x885d},
{0x9c0,0x9c0,0x88f3},
{0x9c1,0x9c1,0x8a1f},
{0x9c2,0x9c2,0x8a3c},
{0x9c3,0x9c3,0x8a54},
{0x9c4,0x9c4,0x8a73},
{0x9c5,0x9c5,0x8c61},
{0x9c6,0x9c6,0x8cde},
{0x9c7,0x9c7,0x91a4},
{0x9c8,0x9c8,0x9266},
{0x9c9,0x9c9,0x937e},
{0x9ca,0x9ca,0x9418},
{0x9cb,0x9cb,0x969c},
{0x9cc,0x9cc,0x9798},
{0x9cd,0x9cd,0x4e0a},
{0x9ce,0x9ce,0x4e08},
{0x9cf,0x9cf,0x4e1e},
{0x9d0,0x9d0,0x4e57},
{0x9d1,0x9d1,0x5197},
{0x9d2,0x9d2,0x5270},
{0x9d3,0x9d3,0x57ce},
{0x9d4,0x9d4,0x5834},
{0x9d5,0x9d5,0x58cc},
{0x9d6,0x9d6,0x5b22},
{0x9d7,0x9d7,0x5e38},
{0x9d8,0x9d8,0x60c5},
{0x9d9,0x9d9,0x64fe},
{0x9da,0x9da,0x6761},
{0x9db,0x9db,0x6756},
{0x9dc,0x9dc,0x6d44},
{0x9dd,0x9dd,0x72b6},
{0x9de,0x9de,0x7573},
{0x9df,0x9df,0x7a63},
{0x9e0,0x9e0,0x84b8},
{0x9e1,0x9e1,0x8b72},
{0x9e2,0x9e2,0x91b8},
{0x9e3,0x9e3,0x9320},
{0x9e4,0x9e4,0x5631},
{0x9e5,0x9e5,0x57f4},
{0x9e6,0x9e6,0x98fe},
{0x9e7,0x9e7,0x62ed},
{0x9e8,0x9e8,0x690d},
{0x9e9,0x9e9,0x6b96},
{0x9ea,0x9ea,0x71ed},
{0x9eb,0x9eb,0x7e54},
{0x9ec,0x9ec,0x8077},
{0x9ed,0x9ed,0x8272},
{0x9ee,0x9ee,0x89e6},
{0x9ef,0x9ef,0x98df},
{0x9f0,0x9f0,0x8755},
{0x9f1,0x9f1,0x8fb1},
{0x9f2,0x9f2,0x5c3b},
{0x9f3,0x9f3,0x4f38},
{0x9f4,0x9f4,0x4fe1},
{0x9f5,0x9f5,0x4fb5},
{0x9f6,0x9f6,0x5507},
{0x9f7,0x9f7,0x5a20},
{0x9f8,0x9f8,0x5bdd},
{0x9f9,0x9f9,0x5be9},
{0x9fa,0x9fa,0x5fc3},
{0x9fb,0x9fb,0x614e},
{0x9fc,0x9fc,0x632f},
{0x9fd,0x9fd,0x65b0},
{0x9fe,0x9fe,0x664b},
{0x9ff,0x9ff,0x68ee},
{0xa00,0xa00,0x699b},
{0xa01,0xa01,0x6d78},
{0xa02,0xa02,0x6df1},
{0xa03,0xa03,0x7533},
{0xa04,0xa04,0x75b9},
{0xa05,0xa05,0x771f},
{0xa06,0xa06,0x795e},
{0xa07,0xa07,0x79e6},
{0xa08,0xa08,0x7d33},
{0xa09,0xa09,0x81e3},
{0xa0a,0xa0a,0x82af},
{0xa0b,0xa0b,0x85aa},
{0xa0c,0xa0c,0x89aa},
{0xa0d,0xa0d,0x8a3a},
{0xa0e,0xa0e,0x8eab},
{0xa0f,0xa0f,0x8f9b},
{0xa10,0xa10,0x9032},
{0xa11,0xa11,0x91dd},
{0xa12,0xa12,0x9707},
{0xa13,0xa13,0x4eba},
{0xa14,0xa14,0x4ec1},
{0xa15,0xa15,0x5203},
{0xa16,0xa16,0x5875},
{0xa17,0xa17,0x58ec},
{0xa18,0xa18,0x5c0b},
{0xa19,0xa19,0x751a},
{0xa1a,0xa1a,0x5c3d},
{0xa1b,0xa1b,0x814e},
{0xa1c,0xa1c,0x8a0a},
{0xa1d,0xa1d,0x8fc5},
{0xa1e,0xa1e,0x9663},
{0xa1f,0xa1f,0x976d},
{0xa20,0xa20,0x7b25},
{0xa21,0xa21,0x8acf},
{0xa22,0xa22,0x9808},
{0xa23,0xa23,0x9162},
{0xa24,0xa24,0x56f3},
{0xa25,0xa25,0x53a8},
{0xa26,0xa26,0x9017},
{0xa27,0xa27,0x5439},
{0xa28,0xa28,0x5782},
{0xa29,0xa29,0x5e25},
{0xa2a,0xa2a,0x63a8},
{0xa2b,0xa2b,0x6c34},
{0xa2c,0xa2c,0x708a},
{0xa2d,0xa2d,0x7761},
{0xa2e,0xa2e,0x7c8b},
{0xa2f,0xa2f,0x7fe0},
{0xa30,0xa30,0x8870},
{0xa31,0xa31,0x9042},
{0xa32,0xa32,0x9154},
{0xa33,0xa33,0x9310},
{0xa34,0xa34,0x9318},
{0xa35,0xa35,0x968f},
{0xa36,0xa36,0x745e},
{0xa37,0xa37,0x9ac4},
{0xa38,0xa38,0x5d07},
{0xa39,0xa39,0x5d69},
{0xa3a,0xa3a,0x6570},
{0xa3b,0xa3b,0x67a2},
{0xa3c,0xa3c,0x8da8},
{0xa3d,0xa3d,0x96db},
{0xa3e,0xa3e,0x636e},
{0xa3f,0xa3f,0x6749},
{0xa40,0xa40,0x6919},
{0xa41,0xa41,0x83c5},
{0xa42,0xa42,0x9817},
{0xa43,0xa43,0x96c0},
{0xa44,0xa44,0x88fe},
{0xa45,0xa45,0x6f84},
{0xa46,0xa46,0x647a},
{0xa47,0xa47,0x5bf8},
{0xa48,0xa48,0x4e16},
{0xa49,0xa49,0x702c},
{0xa4a,0xa4a,0x755d},
{0xa4b,0xa4b,0x662f},
{0xa4c,0xa4c,0x51c4},
{0xa4d,0xa4d,0x5236},
{0xa4e,0xa4e,0x52e2},
{0xa4f,0xa4f,0x59d3},
{0xa50,0xa50,0x5f81},
{0xa51,0xa51,0x6027},
{0xa52,0xa52,0x6210},
{0xa53,0xa53,0x653f},
{0xa54,0xa54,0x6574},
{0xa55,0xa55,0x661f},
{0xa56,0xa56,0x6674},
{0xa57,0xa57,0x68f2},
{0xa58,0xa58,0x6816},
{0xa59,0xa59,0x6b63},
{0xa5a,0xa5a,0x6e05},
{0xa5b,0xa5b,0x7272},
{0xa5c,0xa5c,0x751f},
{0xa5d,0xa5d,0x76db},
{0xa5e,0xa5e,0x7cbe},
{0xa5f,0xa5f,0x8056},
{0xa60,0xa60,0x58f0},
{0xa61,0xa61,0x88fd},
{0xa62,0xa62,0x897f},
{0xa63,0xa63,0x8aa0},
{0xa64,0xa64,0x8a93},
{0xa65,0xa65,0x8acb},
{0xa66,0xa66,0x901d},
{0xa67,0xa67,0x9192},
{0xa68,0xa68,0x9752},
{0xa69,0xa69,0x9759},
{0xa6a,0xa6a,0x6589},
{0xa6b,0xa6b,0x7a0e},
{0xa6c,0xa6c,0x8106},
{0xa6d,0xa6d,0x96bb},
{0xa6e,0xa6e,0x5e2d},
{0xa6f,0xa6f,0x60dc},
{0xa70,0xa70,0x621a},
{0xa71,0xa71,0x65a5},
{0xa72,0xa72,0x6614},
{0xa73,0xa73,0x6790},
{0xa74,0xa74,0x77f3},
{0xa75,0xa75,0x7a4d},
{0xa76,0xa76,0x7c4d},
{0xa77,0xa77,0x7e3e},
{0xa78,0xa78,0x810a},
{0xa79,0xa79,0x8cac},
{0xa7a,0xa7a,0x8d64},
{0xa7b,0xa7b,0x8de1},
{0xa7c,0xa7c,0x8e5f},
{0xa7d,0xa7d,0x78a9},
{0xa7e,0xa7e,0x5207},
{0xa7f,0xa7f,0x62d9},
{0xa80,0xa80,0x63a5},
{0xa81,0xa81,0x6442},
{0xa82,0xa82,0x6298},
{0xa83,0xa83,0x8a2d},
{0xa84,0xa84,0x7a83},
{0xa85,0xa85,0x7bc0},
{0xa86,0xa86,0x8aac},
{0xa87,0xa87,0x96ea},
{0xa88,0xa88,0x7d76},
{0xa89,0xa89,0x820c},
{0xa8a,0xa8a,0x8749},
{0xa8b,0xa8b,0x4ed9},
{0xa8c,0xa8c,0x5148},
{0xa8d,0xa8d,0x5343},
{0xa8e,0xa8e,0x5360},
{0xa8f,0xa8f,0x5ba3},
{0xa90,0xa90,0x5c02},
{0xa91,0xa91,0x5c16},
{0xa92,0xa92,0x5ddd},
{0xa93,0xa93,0x6226},
{0xa94,0xa94,0x6247},
{0xa95,0xa95,0x64b0},
{0xa96,0xa96,0x6813},
{0xa97,0xa97,0x6834},
{0xa98,0xa98,0x6cc9},
{0xa99,0xa99,0x6d45},
{0xa9a,0xa9a,0x6d17},
{0xa9b,0xa9b,0x67d3},
{0xa9c,0xa9c,0x6f5c},
{0xa9d,0xa9d,0x714e},
{0xa9e,0xa9e,0x717d},
{0xa9f,0xa9f,0x65cb},
{0xaa0,0xaa0,0x7a7f},
{0xaa1,0xaa1,0x7bad},
{0xaa2,0xaa2,0x7dda},
{0xaa3,0xaa3,0x7e4a},
{0xaa4,0xaa4,0x7fa8},
{0xaa5,0xaa5,0x817a},
{0xaa6,0xaa6,0x821b},
{0xaa7,0xaa7,0x8239},
{0xaa8,0xaa8,0x85a6},
{0xaa9,0xaa9,0x8a6e},
{0xaaa,0xaaa,0x8cce},
{0xaab,0xaab,0x8df5},
{0xaac,0xaac,0x9078},
{0xaad,0xaad,0x9077},
{0xaae,0xaae,0x92ad},
{0xaaf,0xaaf,0x9291},
{0xab0,0xab0,0x9583},
{0xab1,0xab1,0x9bae},
{0xab2,0xab2,0x524d},
{0xab3,0xab3,0x5584},
{0xab4,0xab4,0x6f38},
{0xab5,0xab5,0x7136},
{0xab6,0xab6,0x5168},
{0xab7,0xab7,0x7985},
{0xab8,0xab8,0x7e55},
{0xab9,0xab9,0x81b3},
{0xaba,0xaba,0x7cce},
{0xabb,0xabb,0x564c},
{0xabc,0xabc,0x5851},
{0xabd,0xabd,0x5ca8},
{0xabe,0xabe,0x63aa},
{0xabf,0xabf,0x66fe},
{0xac0,0xac0,0x66fd},
{0xac1,0xac1,0x695a},
{0xac2,0xac2,0x72d9},
{0xac3,0xac3,0x758f},
{0xac4,0xac4,0x758e},
{0xac5,0xac5,0x790e},
{0xac6,0xac6,0x7956},
{0xac7,0xac7,0x79df},
{0xac8,0xac8,0x7c97},
{0xac9,0xac9,0x7d20},
{0xaca,0xaca,0x7d44},
{0xacb,0xacb,0x8607},
{0xacc,0xacc,0x8a34},
{0xacd,0xacd,0x963b},
{0xace,0xace,0x9061},
{0xacf,0xacf,0x9f20},
{0xad0,0xad0,0x50e7},
{0xad1,0xad1,0x5275},
{0xad2,0xad2,0x53cc},
{0xad3,0xad3,0x53e2},
{0xad4,0xad4,0x5009},
{0xad5,0xad5,0x55aa},
{0xad6,0xad6,0x58ee},
{0xad7,0xad7,0x594f},
{0xad8,0xad8,0x723d},
{0xad9,0xad9,0x5b8b},
{0xada,0xada,0x5c64},
{0xadb,0xadb,0x531d},
{0xadc,0xadc,0x60e3},
{0xadd,0xadd,0x60f3},
{0xade,0xade,0x635c},
{0xadf,0xadf,0x6383},
{0xae0,0xae0,0x633f},
{0xae1,0xae1,0x63bb},
{0xae2,0xae2,0x64cd},
{0xae3,0xae3,0x65e9},
{0xae4,0xae4,0x66f9},
{0xae5,0xae5,0x5de3},
{0xae6,0xae6,0x69cd},
{0xae7,0xae7,0x69fd},
{0xae8,0xae8,0x6f15},
{0xae9,0xae9,0x71e5},
{0xaea,0xaea,0x4e89},
{0xaeb,0xaeb,0x75e9},
{0xaec,0xaec,0x76f8},
{0xaed,0xaed,0x7a93},
{0xaee,0xaee,0x7cdf},
{0xaef,0xaef,0x7dcf},
{0xaf0,0xaf0,0x7d9c},
{0xaf1,0xaf1,0x8061},
{0xaf2,0xaf2,0x8349},
{0xaf3,0xaf3,0x8358},
{0xaf4,0xaf4,0x846c},
{0xaf5,0xaf5,0x84bc},
{0xaf6,0xaf6,0x85fb},
{0xaf7,0xaf7,0x88c5},
{0xaf8,0xaf8,0x8d70},
{0xaf9,0xaf9,0x9001},
{0xafa,0xafa,0x906d},
{0xafb,0xafb,0x9397},
{0xafc,0xafc,0x971c},
{0xafd,0xafd,0x9a12},
{0xafe,0xafe,0x50cf},
{0xaff,0xaff,0x5897},
{0xb00,0xb00,0x618e},
{0xb01,0xb01,0x81d3},
{0xb02,0xb02,0x8535},
{0xb03,0xb03,0x8d08},
{0xb04,0xb04,0x9020},
{0xb05,0xb05,0x4fc3},
{0xb06,0xb06,0x5074},
{0xb07,0xb07,0x5247},
{0xb08,0xb08,0x5373},
{0xb09,0xb09,0x606f},
{0xb0a,0xb0a,0x6349},
{0xb0b,0xb0b,0x675f},
{0xb0c,0xb0c,0x6e2c},
{0xb0d,0xb0d,0x8db3},
{0xb0e,0xb0e,0x901f},
{0xb0f,0xb0f,0x4fd7},
{0xb10,0xb10,0x5c5e},
{0xb11,0xb11,0x8cca},
{0xb12,0xb12,0x65cf},
{0xb13,0xb13,0x7d9a},
{0xb14,0xb14,0x5352},
{0xb15,0xb15,0x8896},
{0xb16,0xb16,0x5176},
{0xb17,0xb17,0x63c3},
{0xb18,0xb18,0x5b58},
{0xb19,0xb19,0x5b6b},
{0xb1a,0xb1a,0x5c0a},
{0xb1b,0xb1b,0x640d},
{0xb1c,0xb1c,0x6751},
{0xb1d,0xb1d,0x905c},
{0xb1e,0xb1e,0x4ed6},
{0xb1f,0xb1f,0x591a},
{0xb20,0xb20,0x592a},
{0xb21,0xb21,0x6c70},
{0xb22,0xb22,0x8a51},
{0xb23,0xb23,0x553e},
{0xb24,0xb24,0x5815},
{0xb25,0xb25,0x59a5},
{0xb26,0xb26,0x60f0},
{0xb27,0xb27,0x6253},
{0xb28,0xb28,0x67c1},
{0xb29,0xb29,0x8235},
{0xb2a,0xb2a,0x6955},
{0xb2b,0xb2b,0x9640},
{0xb2c,0xb2c,0x99c4},
{0xb2d,0xb2d,0x9a28},
{0xb2e,0xb2e,0x4f53},
{0xb2f,0xb2f,0x5806},
{0xb30,0xb30,0x5bfe},
{0xb31,0xb31,0x8010},
{0xb32,0xb32,0x5cb1},
{0xb33,0xb33,0x5e2f},
{0xb34,0xb34,0x5f85},
{0xb35,0xb35,0x6020},
{0xb36,0xb36,0x614b},
{0xb37,0xb37,0x6234},
{0xb38,0xb38,0x66ff},
{0xb39,0xb39,0x6cf0},
{0xb3a,0xb3a,0x6ede},
{0xb3b,0xb3b,0x80ce},
{0xb3c,0xb3c,0x817f},
{0xb3d,0xb3d,0x82d4},
{0xb3e,0xb3e,0x888b},
{0xb3f,0xb3f,0x8cb8},
{0xb40,0xb40,0x9000},
{0xb41,0xb41,0x902e},
{0xb42,0xb42,0x968a},
{0xb43,0xb43,0x9edb},
{0xb44,0xb44,0x9bdb},
{0xb45,0xb45,0x4ee3},
{0xb46,0xb46,0x53f0},
{0xb47,0xb47,0x5927},
{0xb48,0xb48,0x7b2c},
{0xb49,0xb49,0x918d},
{0xb4a,0xb4a,0x984c},
{0xb4b,0xb4b,0x9df9},
{0xb4c,0xb4c,0x6edd},
{0xb4d,0xb4d,0x7027},
{0xb4e,0xb4e,0x5353},
{0xb4f,0xb4f,0x5544},
{0xb50,0xb50,0x5b85},
{0xb51,0xb51,0x6258},
{0xb52,0xb52,0x629e},
{0xb53,0xb53,0x62d3},
{0xb54,0xb54,0x6ca2},
{0xb55,0xb55,0x6fef},
{0xb56,0xb56,0x7422},
{0xb57,0xb57,0x8a17},
{0xb58,0xb58,0x9438},
{0xb59,0xb59,0x6fc1},
{0xb5a,0xb5a,0x8afe},
{0xb5b,0xb5b,0x8338},
{0xb5c,0xb5c,0x51e7},
{0xb5d,0xb5d,0x86f8},
{0xb5e,0xb5e,0x53ea},
{0xb5f,0xb5f,0x53e9},
{0xb60,0xb60,0x4f46},
{0xb61,0xb61,0x9054},
{0xb62,0xb62,0x8fb0},
{0xb63,0xb63,0x596a},
{0xb64,0xb64,0x8131},
{0xb65,0xb65,0x5dfd},
{0xb66,0xb66,0x7aea},
{0xb67,0xb67,0x8fbf},
{0xb68,0xb68,0x68da},
{0xb69,0xb69,0x8c37},
{0xb6a,0xb6a,0x72f8},
{0xb6b,0xb6b,0x9c48},
{0xb6c,0xb6c,0x6a3d},
{0xb6d,0xb6d,0x8ab0},
{0xb6e,0xb6e,0x4e39},
{0xb6f,0xb6f,0x5358},
{0xb70,0xb70,0x5606},
{0xb71,0xb71,0x5766},
{0xb72,0xb72,0x62c5},
{0xb73,0xb73,0x63a2},
{0xb74,0xb74,0x65e6},
{0xb75,0xb75,0x6b4e},
{0xb76,0xb76,0x6de1},
{0xb77,0xb77,0x6e5b},
{0xb78,0xb78,0x70ad},
{0xb79,0xb79,0x77ed},
{0xb7a,0xb7a,0x7aef},
{0xb7b,0xb7b,0x7baa},
{0xb7c,0xb7c,0x7dbb},
{0xb7d,0xb7d,0x803d},
{0xb7e,0xb7e,0x80c6},
{0xb7f,0xb7f,0x86cb},
{0xb80,0xb80,0x8a95},
{0xb81,0xb81,0x935b},
{0xb82,0xb82,0x56e3},
{0xb83,0xb83,0x58c7},
{0xb84,0xb84,0x5f3e},
{0xb85,0xb85,0x65ad},
{0xb86,0xb86,0x6696},
{0xb87,0xb87,0x6a80},
{0xb88,0xb88,0x6bb5},
{0xb89,0xb89,0x7537},
{0xb8a,0xb8a,0x8ac7},
{0xb8b,0xb8b,0x5024},
{0xb8c,0xb8c,0x77e5},
{0xb8d,0xb8d,0x5730},
{0xb8e,0xb8e,0x5f1b},
{0xb8f,0xb8f,0x6065},
{0xb90,0xb90,0x667a},
{0xb91,0xb91,0x6c60},
{0xb92,0xb92,0x75f4},
{0xb93,0xb93,0x7a1a},
{0xb94,0xb94,0x7f6e},
{0xb95,0xb95,0x81f4},
{0xb96,0xb96,0x8718},
{0xb97,0xb97,0x9045},
{0xb98,0xb98,0x99b3},
{0xb99,0xb99,0x7bc9},
{0xb9a,0xb9a,0x755c},
{0xb9b,0xb9b,0x7af9},
{0xb9c,0xb9c,0x7b51},
{0xb9d,0xb9d,0x84c4},
{0xb9e,0xb9e,0x9010},
{0xb9f,0xb9f,0x79e9},
{0xba0,0xba0,0x7a92},
{0xba1,0xba1,0x8336},
{0xba2,0xba2,0x5ae1},
{0xba3,0xba3,0x7740},
{0xba4,0xba4,0x4e2d},
{0xba5,0xba5,0x4ef2},
{0xba6,0xba6,0x5b99},
{0xba7,0xba7,0x5fe0},
{0xba8,0xba8,0x62bd},
{0xba9,0xba9,0x663c},
{0xbaa,0xbaa,0x67f1},
{0xbab,0xbab,0x6ce8},
{0xbac,0xbac,0x866b},
{0xbad,0xbad,0x8877},
{0xbae,0xbae,0x8a3b},
{0xbaf,0xbaf,0x914e},
{0xbb0,0xbb0,0x92f3},
{0xbb1,0xbb1,0x99d0},
{0xbb2,0xbb2,0x6a17},
{0xbb3,0xbb3,0x7026},
{0xbb4,0xbb4,0x732a},
{0xbb5,0xbb5,0x82e7},
{0xbb6,0xbb6,0x8457},
{0xbb7,0xbb7,0x8caf},
{0xbb8,0xbb8,0x4e01},
{0xbb9,0xbb9,0x5146},
{0xbba,0xbba,0x51cb},
{0xbbb,0xbbb,0x558b},
{0xbbc,0xbbc,0x5bf5},
{0xbbd,0xbbd,0x5e16},
{0xbbe,0xbbe,0x5e33},
{0xbbf,0xbbf,0x5e81},
{0xbc0,0xbc0,0x5f14},
{0xbc1,0xbc1,0x5f35},
{0xbc2,0xbc2,0x5f6b},
{0xbc3,0xbc3,0x5fb4},
{0xbc4,0xbc4,0x61f2},
{0xbc5,0xbc5,0x6311},
{0xbc6,0xbc6,0x66a2},
{0xbc7,0xbc7,0x671d},
{0xbc8,0xbc8,0x6f6e},
{0xbc9,0xbc9,0x7252},
{0xbca,0xbca,0x753a},
{0xbcb,0xbcb,0x773a},
{0xbcc,0xbcc,0x8074},
{0xbcd,0xbcd,0x8139},
{0xbce,0xbce,0x8178},
{0xbcf,0xbcf,0x8776},
{0xbd0,0xbd0,0x8abf},
{0xbd1,0xbd1,0x8adc},
{0xbd2,0xbd2,0x8d85},
{0xbd3,0xbd3,0x8df3},
{0xbd4,0xbd4,0x929a},
{0xbd5,0xbd5,0x9577},
{0xbd6,0xbd6,0x9802},
{0xbd7,0xbd7,0x9ce5},
{0xbd8,0xbd8,0x52c5},
{0xbd9,0xbd9,0x6357},
{0xbda,0xbda,0x76f4},
{0xbdb,0xbdb,0x6715},
{0xbdc,0xbdc,0x6c88},
{0xbdd,0xbdd,0x73cd},
{0xbde,0xbde,0x8cc3},
{0xbdf,0xbdf,0x93ae},
{0xbe0,0xbe0,0x9673},
{0xbe1,0xbe1,0x6d25},
{0xbe2,0xbe2,0x589c},
{0xbe3,0xbe3,0x690e},
{0xbe4,0xbe4,0x69cc},
{0xbe5,0xbe5,0x8ffd},
{0xbe6,0xbe6,0x939a},
{0xbe7,0xbe7,0x75db},
{0xbe8,0xbe8,0x901a},
{0xbe9,0xbe9,0x585a},
{0xbea,0xbea,0x6802},
{0xbeb,0xbeb,0x63b4},
{0xbec,0xbec,0x69fb},
{0xbed,0xbed,0x4f43},
{0xbee,0xbee,0x6f2c},
{0xbef,0xbef,0x67d8},
{0xbf0,0xbf0,0x8fbb},
{0xbf1,0xbf1,0x8526},
{0xbf2,0xbf2,0x7db4},
{0xbf3,0xbf3,0x9354},
{0xbf4,0xbf4,0x693f},
{0xbf5,0xbf5,0x6f70},
{0xbf6,0xbf6,0x576a},
{0xbf7,0xbf7,0x58f7},
{0xbf8,0xbf8,0x5b2c},
{0xbf9,0xbf9,0x7d2c},
{0xbfa,0xbfa,0x722a},
{0xbfb,0xbfb,0x540a},
{0xbfc,0xbfc,0x91e3},
{0xbfd,0xbfd,0x9db4},
{0xbfe,0xbfe,0x4ead},
{0xbff,0xbff,0x4f4e},
{0xc00,0xc00,0x505c},
{0xc01,0xc01,0x5075},
{0xc02,0xc02,0x5243},
{0xc03,0xc03,0x8c9e},
{0xc04,0xc04,0x5448},
{0xc05,0xc05,0x5824},
{0xc06,0xc06,0x5b9a},
{0xc07,0xc07,0x5e1d},
{0xc08,0xc08,0x5e95},
{0xc09,0xc09,0x5ead},
{0xc0a,0xc0a,0x5ef7},
{0xc0b,0xc0b,0x5f1f},
{0xc0c,0xc0c,0x608c},
{0xc0d,0xc0d,0x62b5},
{0xc0e,0xc0e,0x633a},
{0xc0f,0xc0f,0x63d0},
{0xc10,0xc10,0x68af},
{0xc11,0xc11,0x6c40},
{0xc12,0xc12,0x7887},
{0xc13,0xc13,0x798e},
{0xc14,0xc14,0x7a0b},
{0xc15,0xc15,0x7de0},
{0xc16,0xc16,0x8247},
{0xc17,0xc17,0x8a02},
{0xc18,0xc18,0x8ae6},
{0xc19,0xc19,0x8e44},
{0xc1a,0xc1a,0x9013},
{0xc1b,0xc1b,0x90b8},
{0xc1c,0xc1c,0x912d},
{0xc1d,0xc1d,0x91d8},
{0xc1e,0xc1e,0x9f0e},
{0xc1f,0xc1f,0x6ce5},
{0xc20,0xc20,0x6458},
{0xc21,0xc21,0x64e2},
{0xc22,0xc22,0x6575},
{0xc23,0xc23,0x6ef4},
{0xc24,0xc24,0x7684},
{0xc25,0xc25,0x7b1b},
{0xc26,0xc26,0x9069},
{0xc27,0xc27,0x93d1},
{0xc28,0xc28,0x6eba},
{0xc29,0xc29,0x54f2},
{0xc2a,0xc2a,0x5fb9},
{0xc2b,0xc2b,0x64a4},
{0xc2c,0xc2c,0x8f4d},
{0xc2d,0xc2d,0x8fed},
{0xc2e,0xc2e,0x9244},
{0xc2f,0xc2f,0x5178},
{0xc30,0xc30,0x586b},
{0xc31,0xc31,0x5929},
{0xc32,0xc32,0x5c55},
{0xc33,0xc33,0x5e97},
{0xc34,0xc34,0x6dfb},
{0xc35,0xc35,0x7e8f},
{0xc36,0xc36,0x751c},
{0xc37,0xc37,0x8cbc},
{0xc38,0xc38,0x8ee2},
{0xc39,0xc39,0x985b},
{0xc3a,0xc3a,0x70b9},
{0xc3b,0xc3b,0x4f1d},
{0xc3c,0xc3c,0x6bbf},
{0xc3d,0xc3d,0x6fb1},
{0xc3e,0xc3e,0x7530},
{0xc3f,0xc3f,0x96fb},
{0xc40,0xc40,0x514e},
{0xc41,0xc41,0x5410},
{0xc42,0xc42,0x5835},
{0xc43,0xc43,0x5857},
{0xc44,0xc44,0x59ac},
{0xc45,0xc45,0x5c60},
{0xc46,0xc46,0x5f92},
{0xc47,0xc47,0x6597},
{0xc48,0xc48,0x675c},
{0xc49,0xc49,0x6e21},
{0xc4a,0xc4a,0x767b},
{0xc4b,0xc4b,0x83df},
{0xc4c,0xc4c,0x8ced},
{0xc4d,0xc4d,0x9014},
{0xc4e,0xc4e,0x90fd},
{0xc4f,0xc4f,0x934d},
{0xc50,0xc50,0x7825},
{0xc51,0xc51,0x783a},
{0xc52,0xc52,0x52aa},
{0xc53,0xc53,0x5ea6},
{0xc54,0xc54,0x571f},
{0xc55,0xc55,0x5974},
{0xc56,0xc56,0x6012},
{0xc57,0xc57,0x5012},
{0xc58,0xc58,0x515a},
{0xc59,0xc59,0x51ac},
{0xc5a,0xc5a,0x51cd},
{0xc5b,0xc5b,0x5200},
{0xc5c,0xc5c,0x5510},
{0xc5d,0xc5d,0x5854},
{0xc5e,0xc5e,0x5858},
{0xc5f,0xc5f,0x5957},
{0xc60,0xc60,0x5b95},
{0xc61,0xc61,0x5cf6},
{0xc62,0xc62,0x5d8b},
{0xc63,0xc63,0x60bc},
{0xc64,0xc64,0x6295},
{0xc65,0xc65,0x642d},
{0xc66,0xc66,0x6771},
{0xc67,0xc67,0x6843},
{0xc68,0xc68,0x68bc},
{0xc69,0xc69,0x68df},
{0xc6a,0xc6a,0x76d7},
{0xc6b,0xc6b,0x6dd8},
{0xc6c,0xc6c,0x6e6f},
{0xc6d,0xc6d,0x6d9b},
{0xc6e,0xc6e,0x706f},
{0xc6f,0xc6f,0x71c8},
{0xc70,0xc70,0x5f53},
{0xc71,0xc71,0x75d8},
{0xc72,0xc72,0x7977},
{0xc73,0xc73,0x7b49},
{0xc74,0xc74,0x7b54},
{0xc75,0xc75,0x7b52},
{0xc76,0xc76,0x7cd6},
{0xc77,0xc77,0x7d71},
{0xc78,0xc78,0x5230},
{0xc79,0xc79,0x8463},
{0xc7a,0xc7a,0x8569},
{0xc7b,0xc7b,0x85e4},
{0xc7c,0xc7c,0x8a0e},
{0xc7d,0xc7d,0x8b04},
{0xc7e,0xc7e,0x8c46},
{0xc7f,0xc7f,0x8e0f},
{0xc80,0xc80,0x9003},
{0xc81,0xc81,0x900f},
{0xc82,0xc82,0x9419},
{0xc83,0xc83,0x9676},
{0xc84,0xc84,0x982d},
{0xc85,0xc85,0x9a30},
{0xc86,0xc86,0x95d8},
{0xc87,0xc87,0x50cd},
{0xc88,0xc88,0x52d5},
{0xc89,0xc89,0x540c},
{0xc8a,0xc8a,0x5802},
{0xc8b,0xc8b,0x5c0e},
{0xc8c,0xc8c,0x61a7},
{0xc8d,0xc8d,0x649e},
{0xc8e,0xc8e,0x6d1e},
{0xc8f,0xc8f,0x77b3},
{0xc90,0xc90,0x7ae5},
{0xc91,0xc91,0x80f4},
{0xc92,0xc92,0x8404},
{0xc93,0xc93,0x9053},
{0xc94,0xc94,0x9285},
{0xc95,0xc95,0x5ce0},
{0xc96,0xc96,0x9d07},
{0xc97,0xc97,0x533f},
{0xc98,0xc98,0x5f97},
{0xc99,0xc99,0x5fb3},
{0xc9a,0xc9a,0x6d9c},
{0xc9b,0xc9b,0x7279},
{0xc9c,0xc9c,0x7763},
{0xc9d,0xc9d,0x79bf},
{0xc9e,0xc9e,0x7be4},
{0xc9f,0xc9f,0x6bd2},
{0xca0,0xca0,0x72ec},
{0xca1,0xca1,0x8aad},
{0xca2,0xca2,0x6803},
{0xca3,0xca3,0x6a61},
{0xca4,0xca4,0x51f8},
{0xca5,0xca5,0x7a81},
{0xca6,0xca6,0x6934},
{0xca7,0xca7,0x5c4a},
{0xca8,0xca8,0x9cf6},
{0xca9,0xca9,0x82eb},
{0xcaa,0xcaa,0x5bc5},
{0xcab,0xcab,0x9149},
{0xcac,0xcac,0x701e},
{0xcad,0xcad,0x5678},
{0xcae,0xcae,0x5c6f},
{0xcaf,0xcaf,0x60c7},
{0xcb0,0xcb0,0x6566},
{0xcb1,0xcb1,0x6c8c},
{0xcb2,0xcb2,0x8c5a},
{0xcb3,0xcb3,0x9041},
{0xcb4,0xcb4,0x9813},
{0xcb5,0xcb5,0x5451},
{0xcb6,0xcb6,0x66c7},
{0xcb7,0xcb7,0x920d},
{0xcb8,0xcb8,0x5948},
{0xcb9,0xcb9,0x90a3},
{0xcba,0xcba,0x5185},
{0xcbb,0xcbb,0x4e4d},
{0xcbc,0xcbc,0x51ea},
{0xcbd,0xcbd,0x8599},
{0xcbe,0xcbe,0x8b0e},
{0xcbf,0xcbf,0x7058},
{0xcc0,0xcc0,0x637a},
{0xcc1,0xcc1,0x934b},
{0xcc2,0xcc2,0x6962},
{0xcc3,0xcc3,0x99b4},
{0xcc4,0xcc4,0x7e04},
{0xcc5,0xcc5,0x7577},
{0xcc6,0xcc6,0x5357},
{0xcc7,0xcc7,0x6960},
{0xcc8,0xcc8,0x8edf},
{0xcc9,0xcc9,0x96e3},
{0xcca,0xcca,0x6c5d},
{0xccb,0xccb,0x4e8c},
{0xccc,0xccc,0x5c3c},
{0xccd,0xccd,0x5f10},
{0xcce,0xcce,0x8fe9},
{0xccf,0xccf,0x5302},
{0xcd0,0xcd0,0x8cd1},
{0xcd1,0xcd1,0x8089},
{0xcd2,0xcd2,0x8679},
{0xcd3,0xcd3,0x5eff},
{0xcd4,0xcd4,0x65e5},
{0xcd5,0xcd5,0x4e73},
{0xcd6,0xcd6,0x5165},
{0xcd7,0xcd7,0x5982},
{0xcd8,0xcd8,0x5c3f},
{0xcd9,0xcd9,0x97ee},
{0xcda,0xcda,0x4efb},
{0xcdb,0xcdb,0x598a},
{0xcdc,0xcdc,0x5fcd},
{0xcdd,0xcdd,0x8a8d},
{0xcde,0xcde,0x6fe1},
{0xcdf,0xcdf,0x79b0},
{0xce0,0xce0,0x7962},
{0xce1,0xce1,0x5be7},
{0xce2,0xce2,0x8471},
{0xce3,0xce3,0x732b},
{0xce4,0xce4,0x71b1},
{0xce5,0xce5,0x5e74},
{0xce6,0xce6,0x5ff5},
{0xce7,0xce7,0x637b},
{0xce8,0xce8,0x649a},
{0xce9,0xce9,0x71c3},
{0xcea,0xcea,0x7c98},
{0xceb,0xceb,0x4e43},
{0xcec,0xcec,0x5efc},
{0xced,0xced,0x4e4b},
{0xcee,0xcee,0x57dc},
{0xcef,0xcef,0x56a2},
{0xcf0,0xcf0,0x60a9},
{0xcf1,0xcf1,0x6fc3},
{0xcf2,0xcf2,0x7d0d},
{0xcf3,0xcf3,0x80fd},
{0xcf4,0xcf4,0x8133},
{0xcf5,0xcf5,0x81bf},
{0xcf6,0xcf6,0x8fb2},
{0xcf7,0xcf7,0x8997},
{0xcf8,0xcf8,0x86a4},
{0xcf9,0xcf9,0x5df4},
{0xcfa,0xcfa,0x628a},
{0xcfb,0xcfb,0x64ad},
{0xcfc,0xcfc,0x8987},
{0xcfd,0xcfd,0x6777},
{0xcfe,0xcfe,0x6ce2},
{0xcff,0xcff,0x6d3e},
{0xd00,0xd00,0x7436},
{0xd01,0xd01,0x7834},
{0xd02,0xd02,0x5a46},
{0xd03,0xd03,0x7f75},
{0xd04,0xd04,0x82ad},
{0xd05,0xd05,0x99ac},
{0xd06,0xd06,0x4ff3},
{0xd07,0xd07,0x5ec3},
{0xd08,0xd08,0x62dd},
{0xd09,0xd09,0x6392},
{0xd0a,0xd0a,0x6557},
{0xd0b,0xd0b,0x676f},
{0xd0c,0xd0c,0x76c3},
{0xd0d,0xd0d,0x724c},
{0xd0e,0xd0e,0x80cc},
{0xd0f,0xd0f,0x80ba},
{0xd10,0xd10,0x8f29},
{0xd11,0xd11,0x914d},
{0xd12,0xd12,0x500d},
{0xd13,0xd13,0x57f9},
{0xd14,0xd14,0x5a92},
{0xd15,0xd15,0x6885},
{0xd16,0xd16,0x6973},
{0xd17,0xd17,0x7164},
{0xd18,0xd18,0x72fd},
{0xd19,0xd19,0x8cb7},
{0xd1a,0xd1a,0x58f2},
{0xd1b,0xd1b,0x8ce0},
{0xd1c,0xd1c,0x966a},
{0xd1d,0xd1d,0x9019},
{0xd1e,0xd1e,0x877f},
{0xd1f,0xd1f,0x79e4},
{0xd20,0xd20,0x77e7},
{0xd21,0xd21,0x8429},
{0xd22,0xd22,0x4f2f},
{0xd23,0xd23,0x5265},
{0xd24,0xd24,0x535a},
{0xd25,0xd25,0x62cd},
{0xd26,0xd26,0x67cf},
{0xd27,0xd27,0x6cca},
{0xd28,0xd28,0x767d},
{0xd29,0xd29,0x7b94},
{0xd2a,0xd2a,0x7c95},
{0xd2b,0xd2b,0x8236},
{0xd2c,0xd2c,0x8584},
{0xd2d,0xd2d,0x8feb},
{0xd2e,0xd2e,0x66dd},
{0xd2f,0xd2f,0x6f20},
{0xd30,0xd30,0x7206},
{0xd31,0xd31,0x7e1b},
{0xd32,0xd32,0x83ab},
{0xd33,0xd33,0x99c1},
{0xd34,0xd34,0x9ea6},
{0xd35,0xd35,0x51fd},
{0xd36,0xd36,0x7bb1},
{0xd37,0xd37,0x7872},
{0xd38,0xd38,0x7bb8},
{0xd39,0xd39,0x8087},
{0xd3a,0xd3a,0x7b48},
{0xd3b,0xd3b,0x6ae8},
{0xd3c,0xd3c,0x5e61},
{0xd3d,0xd3d,0x808c},
{0xd3e,0xd3e,0x7551},
{0xd3f,0xd3f,0x7560},
{0xd40,0xd40,0x516b},
{0xd41,0xd41,0x9262},
{0xd42,0xd42,0x6e8c},
{0xd43,0xd43,0x767a},
{0xd44,0xd44,0x9197},
{0xd45,0xd45,0x9aea},
{0xd46,0xd46,0x4f10},
{0xd47,0xd47,0x7f70},
{0xd48,0xd48,0x629c},
{0xd49,0xd49,0x7b4f},
{0xd4a,0xd4a,0x95a5},
{0xd4b,0xd4b,0x9ce9},
{0xd4c,0xd4c,0x567a},
{0xd4d,0xd4d,0x5859},
{0xd4e,0xd4e,0x86e4},
{0xd4f,0xd4f,0x96bc},
{0xd50,0xd50,0x4f34},
{0xd51,0xd51,0x5224},
{0xd52,0xd52,0x534a},
{0xd53,0xd53,0x53cd},
{0xd54,0xd54,0x53db},
{0xd55,0xd55,0x5e06},
{0xd56,0xd56,0x642c},
{0xd57,0xd57,0x6591},
{0xd58,0xd58,0x677f},
{0xd59,0xd59,0x6c3e},
{0xd5a,0xd5a,0x6c4e},
{0xd5b,0xd5b,0x7248},
{0xd5c,0xd5c,0x72af},
{0xd5d,0xd5d,0x73ed},
{0xd5e,0xd5e,0x7554},
{0xd5f,0xd5f,0x7e41},
{0xd60,0xd60,0x822c},
{0xd61,0xd61,0x85e9},
{0xd62,0xd62,0x8ca9},
{0xd63,0xd63,0x7bc4},
{0xd64,0xd64,0x91c6},
{0xd65,0xd65,0x7169},
{0xd66,0xd66,0x9812},
{0xd67,0xd67,0x98ef},
{0xd68,0xd68,0x633d},
{0xd69,0xd69,0x6669},
{0xd6a,0xd6a,0x756a},
{0xd6b,0xd6b,0x76e4},
{0xd6c,0xd6c,0x78d0},
{0xd6d,0xd6d,0x8543},
{0xd6e,0xd6e,0x86ee},
{0xd6f,0xd6f,0x532a},
{0xd70,0xd70,0x5351},
{0xd71,0xd71,0x5426},
{0xd72,0xd72,0x5983},
{0xd73,0xd73,0x5e87},
{0xd74,0xd74,0x5f7c},
{0xd75,0xd75,0x60b2},
{0xd76,0xd76,0x6249},
{0xd77,0xd77,0x6279},
{0xd78,0xd78,0x62ab},
{0xd79,0xd79,0x6590},
{0xd7a,0xd7a,0x6bd4},
{0xd7b,0xd7b,0x6ccc},
{0xd7c,0xd7c,0x75b2},
{0xd7d,0xd7d,0x76ae},
{0xd7e,0xd7e,0x7891},
{0xd7f,0xd7f,0x79d8},
{0xd80,0xd80,0x7dcb},
{0xd81,0xd81,0x7f77},
{0xd82,0xd82,0x80a5},
{0xd83,0xd83,0x88ab},
{0xd84,0xd84,0x8ab9},
{0xd85,0xd85,0x8cbb},
{0xd86,0xd86,0x907f},
{0xd87,0xd87,0x975e},
{0xd88,0xd88,0x98db},
{0xd89,0xd89,0x6a0b},
{0xd8a,0xd8a,0x7c38},
{0xd8b,0xd8b,0x5099},
{0xd8c,0xd8c,0x5c3e},
{0xd8d,0xd8d,0x5fae},
{0xd8e,0xd8e,0x6787},
{0xd8f,0xd8f,0x6bd8},
{0xd90,0xd90,0x7435},
{0xd91,0xd91,0x7709},
{0xd92,0xd92,0x7f8e},
{0xd93,0xd93,0x9f3b},
{0xd94,0xd94,0x67ca},
{0xd95,0xd95,0x7a17},
{0xd96,0xd96,0x5339},
{0xd97,0xd97,0x758b},
{0xd98,0xd98,0x9aed},
{0xd99,0xd99,0x5f66},
{0xd9a,0xd9a,0x819d},
{0xd9b,0xd9b,0x83f1},
{0xd9c,0xd9c,0x8098},
{0xd9d,0xd9d,0x5f3c},
{0xd9e,0xd9e,0x5fc5},
{0xd9f,0xd9f,0x7562},
{0xda0,0xda0,0x7b46},
{0xda1,0xda1,0x903c},
{0xda2,0xda2,0x6867},
{0xda3,0xda3,0x59eb},
{0xda4,0xda4,0x5a9b},
{0xda5,0xda5,0x7d10},
{0xda6,0xda6,0x767e},
{0xda7,0xda7,0x8b2c},
{0xda8,0xda8,0x4ff5},
{0xda9,0xda9,0x5f6a},
{0xdaa,0xdaa,0x6a19},
{0xdab,0xdab,0x6c37},
{0xdac,0xdac,0x6f02},
{0xdad,0xdad,0x74e2},
{0xdae,0xdae,0x7968},
{0xdaf,0xdaf,0x8868},
{0xdb0,0xdb0,0x8a55},
{0xdb1,0xdb1,0x8c79},
{0xdb2,0xdb2,0x5edf},
{0xdb3,0xdb3,0x63cf},
{0xdb4,0xdb4,0x75c5},
{0xdb5,0xdb5,0x79d2},
{0xdb6,0xdb6,0x82d7},
{0xdb7,0xdb7,0x9328},
{0xdb8,0xdb8,0x92f2},
{0xdb9,0xdb9,0x849c},
{0xdba,0xdba,0x86ed},
{0xdbb,0xdbb,0x9c2d},
{0xdbc,0xdbc,0x54c1},
{0xdbd,0xdbd,0x5f6c},
{0xdbe,0xdbe,0x658c},
{0xdbf,0xdbf,0x6d5c},
{0xdc0,0xdc0,0x7015},
{0xdc1,0xdc1,0x8ca7},
{0xdc2,0xdc2,0x8cd3},
{0xdc3,0xdc3,0x983b},
{0xdc4,0xdc4,0x654f},
{0xdc5,0xdc5,0x74f6},
{0xdc6,0xdc6,0x4e0d},
{0xdc7,0xdc7,0x4ed8},
{0xdc8,0xdc8,0x57e0},
{0xdc9,0xdc9,0x592b},
{0xdca,0xdca,0x5a66},
{0xdcb,0xdcb,0x5bcc},
{0xdcc,0xdcc,0x51a8},
{0xdcd,0xdcd,0x5e03},
{0xdce,0xdce,0x5e9c},
{0xdcf,0xdcf,0x6016},
{0xdd0,0xdd0,0x6276},
{0xdd1,0xdd1,0x6577},
{0xdd2,0xdd2,0x65a7},
{0xdd3,0xdd3,0x666e},
{0xdd4,0xdd4,0x6d6e},
{0xdd5,0xdd5,0x7236},
{0xdd6,0xdd6,0x7b26},
{0xdd7,0xdd7,0x8150},
{0xdd8,0xdd8,0x819a},
{0xdd9,0xdd9,0x8299},
{0xdda,0xdda,0x8b5c},
{0xddb,0xddb,0x8ca0},
{0xddc,0xddc,0x8ce6},
{0xddd,0xddd,0x8d74},
{0xdde,0xdde,0x961c},
{0xddf,0xddf,0x9644},
{0xde0,0xde0,0x4fae},
{0xde1,0xde1,0x64ab},
{0xde2,0xde2,0x6b66},
{0xde3,0xde3,0x821e},
{0xde4,0xde4,0x8461},
{0xde5,0xde5,0x856a},
{0xde6,0xde6,0x90e8},
{0xde7,0xde7,0x5c01},
{0xde8,0xde8,0x6953},
{0xde9,0xde9,0x98a8},
{0xdea,0xdea,0x847a},
{0xdeb,0xdeb,0x8557},
{0xdec,0xdec,0x4f0f},
{0xded,0xded,0x526f},
{0xdee,0xdee,0x5fa9},
{0xdef,0xdef,0x5e45},
{0xdf0,0xdf0,0x670d},
{0xdf1,0xdf1,0x798f},
{0xdf2,0xdf2,0x8179},
{0xdf3,0xdf3,0x8907},
{0xdf4,0xdf4,0x8986},
{0xdf5,0xdf5,0x6df5},
{0xdf6,0xdf6,0x5f17},
{0xdf7,0xdf7,0x6255},
{0xdf8,0xdf8,0x6cb8},
{0xdf9,0xdf9,0x4ecf},
{0xdfa,0xdfa,0x7269},
{0xdfb,0xdfb,0x9b92},
{0xdfc,0xdfc,0x5206},
{0xdfd,0xdfd,0x543b},
{0xdfe,0xdfe,0x5674},
{0xdff,0xdff,0x58b3},
{0xe00,0xe00,0x61a4},
{0xe01,0xe01,0x626e},
{0xe02,0xe02,0x711a},
{0xe03,0xe03,0x596e},
{0xe04,0xe04,0x7c89},
{0xe05,0xe05,0x7cde},
{0xe06,0xe06,0x7d1b},
{0xe07,0xe07,0x96f0},
{0xe08,0xe08,0x6587},
{0xe09,0xe09,0x805e},
{0xe0a,0xe0a,0x4e19},
{0xe0b,0xe0b,0x4f75},
{0xe0c,0xe0c,0x5175},
{0xe0d,0xe0d,0x5840},
{0xe0e,0xe0e,0x5e63},
{0xe0f,0xe0f,0x5e73},
{0xe10,0xe10,0x5f0a},
{0xe11,0xe11,0x67c4},
{0xe12,0xe12,0x4e26},
{0xe13,0xe13,0x853d},
{0xe14,0xe14,0x9589},
{0xe15,0xe15,0x965b},
{0xe16,0xe16,0x7c73},
{0xe17,0xe17,0x9801},
{0xe18,0xe18,0x50fb},
{0xe19,0xe19,0x58c1},
{0xe1a,0xe1a,0x7656},
{0xe1b,0xe1b,0x78a7},
{0xe1c,0xe1c,0x5225},
{0xe1d,0xe1d,0x77a5},
{0xe1e,0xe1e,0x8511},
{0xe1f,0xe1f,0x7b86},
{0xe20,0xe20,0x504f},
{0xe21,0xe21,0x5909},
{0xe22,0xe22,0x7247},
{0xe23,0xe23,0x7bc7},
{0xe24,0xe24,0x7de8},
{0xe25,0xe25,0x8fba},
{0xe26,0xe26,0x8fd4},
{0xe27,0xe27,0x904d},
{0xe28,0xe28,0x4fbf},
{0xe29,0xe29,0x52c9},
{0xe2a,0xe2a,0x5a29},
{0xe2b,0xe2b,0x5f01},
{0xe2c,0xe2c,0x97ad},
{0xe2d,0xe2d,0x4fdd},
{0xe2e,0xe2e,0x8217},
{0xe2f,0xe2f,0x92ea},
{0xe30,0xe30,0x5703},
{0xe31,0xe31,0x6355},
{0xe32,0xe32,0x6b69},
{0xe33,0xe33,0x752b},
{0xe34,0xe34,0x88dc},
{0xe35,0xe35,0x8f14},
{0xe36,0xe36,0x7a42},
{0xe37,0xe37,0x52df},
{0xe38,0xe38,0x5893},
{0xe39,0xe39,0x6155},
{0xe3a,0xe3a,0x620a},
{0xe3b,0xe3b,0x66ae},
{0xe3c,0xe3c,0x6bcd},
{0xe3d,0xe3d,0x7c3f},
{0xe3e,0xe3e,0x83e9},
{0xe3f,0xe3f,0x5023},
{0xe40,0xe40,0x4ff8},
{0xe41,0xe41,0x5305},
{0xe42,0xe42,0x5446},
{0xe43,0xe43,0x5831},
{0xe44,0xe44,0x5949},
{0xe45,0xe45,0x5b9d},
{0xe46,0xe46,0x5cf0},
{0xe47,0xe47,0x5cef},
{0xe48,0xe48,0x5d29},
{0xe49,0xe49,0x5e96},
{0xe4a,0xe4a,0x62b1},
{0xe4b,0xe4b,0x6367},
{0xe4c,0xe4c,0x653e},
{0xe4d,0xe4d,0x65b9},
{0xe4e,0xe4e,0x670b},
{0xe4f,0xe4f,0x6cd5},
{0xe50,0xe50,0x6ce1},
{0xe51,0xe51,0x70f9},
{0xe52,0xe52,0x7832},
{0xe53,0xe53,0x7e2b},
{0xe54,0xe54,0x80de},
{0xe55,0xe55,0x82b3},
{0xe56,0xe56,0x840c},
{0xe57,0xe57,0x84ec},
{0xe58,0xe58,0x8702},
{0xe59,0xe59,0x8912},
{0xe5a,0xe5a,0x8a2a},
{0xe5b,0xe5b,0x8c4a},
{0xe5c,0xe5c,0x90a6},
{0xe5d,0xe5d,0x92d2},
{0xe5e,0xe5e,0x98fd},
{0xe5f,0xe5f,0x9cf3},
{0xe60,0xe60,0x9d6c},
{0xe61,0xe61,0x4e4f},
{0xe62,0xe62,0x4ea1},
{0xe63,0xe63,0x508d},
{0xe64,0xe64,0x5256},
{0xe65,0xe65,0x574a},
{0xe66,0xe66,0x59a8},
{0xe67,0xe67,0x5e3d},
{0xe68,0xe69,0x5fd8},
{0xe6a,0xe6a,0x623f},
{0xe6b,0xe6b,0x66b4},
{0xe6c,0xe6c,0x671b},
{0xe6d,0xe6d,0x67d0},
{0xe6e,0xe6e,0x68d2},
{0xe6f,0xe6f,0x5192},
{0xe70,0xe70,0x7d21},
{0xe71,0xe71,0x80aa},
{0xe72,0xe72,0x81a8},
{0xe73,0xe73,0x8b00},
{0xe74,0xe74,0x8c8c},
{0xe75,0xe75,0x8cbf},
{0xe76,0xe76,0x927e},
{0xe77,0xe77,0x9632},
{0xe78,0xe78,0x5420},
{0xe79,0xe79,0x982c},
{0xe7a,0xe7a,0x5317},
{0xe7b,0xe7b,0x50d5},
{0xe7c,0xe7c,0x535c},
{0xe7d,0xe7d,0x58a8},
{0xe7e,0xe7e,0x64b2},
{0xe7f,0xe7f,0x6734},
{0xe80,0xe80,0x7267},
{0xe81,0xe81,0x7766},
{0xe82,0xe82,0x7a46},
{0xe83,0xe83,0x91e6},
{0xe84,0xe84,0x52c3},
{0xe85,0xe85,0x6ca1},
{0xe86,0xe86,0x6b86},
{0xe87,0xe87,0x5800},
{0xe88,0xe88,0x5e4c},
{0xe89,0xe89,0x5954},
{0xe8a,0xe8a,0x672c},
{0xe8b,0xe8b,0x7ffb},
{0xe8c,0xe8c,0x51e1},
{0xe8d,0xe8d,0x76c6},
{0xe8e,0xe8e,0x6469},
{0xe8f,0xe8f,0x78e8},
{0xe90,0xe90,0x9b54},
{0xe91,0xe91,0x9ebb},
{0xe92,0xe92,0x57cb},
{0xe93,0xe93,0x59b9},
{0xe94,0xe94,0x6627},
{0xe95,0xe95,0x679a},
{0xe96,0xe96,0x6bce},
{0xe97,0xe97,0x54e9},
{0xe98,0xe98,0x69d9},
{0xe99,0xe99,0x5e55},
{0xe9a,0xe9a,0x819c},
{0xe9b,0xe9b,0x6795},
{0xe9c,0xe9c,0x9baa},
{0xe9d,0xe9d,0x67fe},
{0xe9e,0xe9e,0x9c52},
{0xe9f,0xe9f,0x685d},
{0xea0,0xea0,0x4ea6},
{0xea1,0xea1,0x4fe3},
{0xea2,0xea2,0x53c8},
{0xea3,0xea3,0x62b9},
{0xea4,0xea4,0x672b},
{0xea5,0xea5,0x6cab},
{0xea6,0xea6,0x8fc4},
{0xea7,0xea7,0x4fad},
{0xea8,0xea8,0x7e6d},
{0xea9,0xea9,0x9ebf},
{0xeaa,0xeaa,0x4e07},
{0xeab,0xeab,0x6162},
{0xeac,0xeac,0x6e80},
{0xead,0xead,0x6f2b},
{0xeae,0xeae,0x8513},
{0xeaf,0xeaf,0x5473},
{0xeb0,0xeb0,0x672a},
{0xeb1,0xeb1,0x9b45},
{0xeb2,0xeb2,0x5df3},
{0xeb3,0xeb3,0x7b95},
{0xeb4,0xeb4,0x5cac},
{0xeb5,0xeb5,0x5bc6},
{0xeb6,0xeb6,0x871c},
{0xeb7,0xeb7,0x6e4a},
{0xeb8,0xeb8,0x84d1},
{0xeb9,0xeb9,0x7a14},
{0xeba,0xeba,0x8108},
{0xebb,0xebb,0x5999},
{0xebc,0xebc,0x7c8d},
{0xebd,0xebd,0x6c11},
{0xebe,0xebe,0x7720},
{0xebf,0xebf,0x52d9},
{0xec0,0xec0,0x5922},
{0xec1,0xec1,0x7121},
{0xec2,0xec2,0x725f},
{0xec3,0xec3,0x77db},
{0xec4,0xec4,0x9727},
{0xec5,0xec5,0x9d61},
{0xec6,0xec6,0x690b},
{0xec7,0xec7,0x5a7f},
{0xec8,0xec8,0x5a18},
{0xec9,0xec9,0x51a5},
{0xeca,0xeca,0x540d},
{0xecb,0xecb,0x547d},
{0xecc,0xecc,0x660e},
{0xecd,0xecd,0x76df},
{0xece,0xece,0x8ff7},
{0xecf,0xecf,0x9298},
{0xed0,0xed0,0x9cf4},
{0xed1,0xed1,0x59ea},
{0xed2,0xed2,0x725d},
{0xed3,0xed3,0x6ec5},
{0xed4,0xed4,0x514d},
{0xed5,0xed5,0x68c9},
{0xed6,0xed6,0x7dbf},
{0xed7,0xed7,0x7dec},
{0xed8,0xed8,0x9762},
{0xed9,0xed9,0x9eba},
{0xeda,0xeda,0x6478},
{0xedb,0xedb,0x6a21},
{0xedc,0xedc,0x8302},
{0xedd,0xedd,0x5984},
{0xede,0xede,0x5b5f},
{0xedf,0xedf,0x6bdb},
{0xee0,0xee0,0x731b},
{0xee1,0xee1,0x76f2},
{0xee2,0xee2,0x7db2},
{0xee3,0xee3,0x8017},
{0xee4,0xee4,0x8499},
{0xee5,0xee5,0x5132},
{0xee6,0xee6,0x6728},
{0xee7,0xee7,0x9ed9},
{0xee8,0xee8,0x76ee},
{0xee9,0xee9,0x6762},
{0xeea,0xeea,0x52ff},
{0xeeb,0xeeb,0x9905},
{0xeec,0xeec,0x5c24},
{0xeed,0xeed,0x623b},
{0xeee,0xeee,0x7c7e},
{0xeef,0xeef,0x8cb0},
{0xef0,0xef0,0x554f},
{0xef1,0xef1,0x60b6},
{0xef2,0xef2,0x7d0b},
{0xef3,0xef3,0x9580},
{0xef4,0xef4,0x5301},
{0xef5,0xef5,0x4e5f},
{0xef6,0xef6,0x51b6},
{0xef7,0xef7,0x591c},
{0xef8,0xef8,0x723a},
{0xef9,0xef9,0x8036},
{0xefa,0xefa,0x91ce},
{0xefb,0xefb,0x5f25},
{0xefc,0xefc,0x77e2},
{0xefd,0xefd,0x5384},
{0xefe,0xefe,0x5f79},
{0xeff,0xeff,0x7d04},
{0xf00,0xf00,0x85ac},
{0xf01,0xf01,0x8a33},
{0xf02,0xf02,0x8e8d},
{0xf03,0xf03,0x9756},
{0xf04,0xf04,0x67f3},
{0xf05,0xf05,0x85ae},
{0xf06,0xf06,0x9453},
{0xf07,0xf07,0x6109},
{0xf08,0xf08,0x6108},
{0xf09,0xf09,0x6cb9},
{0xf0a,0xf0a,0x7652},
{0xf0b,0xf0b,0x8aed},
{0xf0c,0xf0c,0x8f38},
{0xf0d,0xf0d,0x552f},
{0xf0e,0xf0e,0x4f51},
{0xf0f,0xf0f,0x512a},
{0xf10,0xf10,0x52c7},
{0xf11,0xf11,0x53cb},
{0xf12,0xf12,0x5ba5},
{0xf13,0xf13,0x5e7d},
{0xf14,0xf14,0x60a0},
{0xf15,0xf15,0x6182},
{0xf16,0xf16,0x63d6},
{0xf17,0xf17,0x6709},
{0xf18,0xf18,0x67da},
{0xf19,0xf19,0x6e67},
{0xf1a,0xf1a,0x6d8c},
{0xf1b,0xf1c,0x7336},
{0xf1d,0xf1d,0x7531},
{0xf1e,0xf1e,0x7950},
{0xf1f,0xf1f,0x88d5},
{0xf20,0xf20,0x8a98},
{0xf21,0xf21,0x904a},
{0xf22,0xf22,0x9091},
{0xf23,0xf23,0x90f5},
{0xf24,0xf24,0x96c4},
{0xf25,0xf25,0x878d},
{0xf26,0xf26,0x5915},
{0xf27,0xf27,0x4e88},
{0xf28,0xf28,0x4f59},
{0xf29,0xf29,0x4e0e},
{0xf2a,0xf2a,0x8a89},
{0xf2b,0xf2b,0x8f3f},
{0xf2c,0xf2c,0x9810},
{0xf2d,0xf2d,0x50ad},
{0xf2e,0xf2e,0x5e7c},
{0xf2f,0xf2f,0x5996},
{0xf30,0xf30,0x5bb9},
{0xf31,0xf31,0x5eb8},
{0xf32,0xf32,0x63da},
{0xf33,0xf33,0x63fa},
{0xf34,0xf34,0x64c1},
{0xf35,0xf35,0x66dc},
{0xf36,0xf36,0x694a},
{0xf37,0xf37,0x69d8},
{0xf38,0xf38,0x6d0b},
{0xf39,0xf39,0x6eb6},
{0xf3a,0xf3a,0x7194},
{0xf3b,0xf3b,0x7528},
{0xf3c,0xf3c,0x7aaf},
{0xf3d,0xf3d,0x7f8a},
{0xf3e,0xf3e,0x8000},
{0xf3f,0xf3f,0x8449},
{0xf40,0xf40,0x84c9},
{0xf41,0xf41,0x8981},
{0xf42,0xf42,0x8b21},
{0xf43,0xf43,0x8e0a},
{0xf44,0xf44,0x9065},
{0xf45,0xf45,0x967d},
{0xf46,0xf46,0x990a},
{0xf47,0xf47,0x617e},
{0xf48,0xf48,0x6291},
{0xf49,0xf49,0x6b32},
{0xf4a,0xf4a,0x6c83},
{0xf4b,0xf4b,0x6d74},
{0xf4c,0xf4c,0x7fcc},
{0xf4d,0xf4d,0x7ffc},
{0xf4e,0xf4e,0x6dc0},
{0xf4f,0xf4f,0x7f85},
{0xf50,0xf50,0x87ba},
{0xf51,0xf51,0x88f8},
{0xf52,0xf52,0x6765},
{0xf53,0xf53,0x83b1},
{0xf54,0xf54,0x983c},
{0xf55,0xf55,0x96f7},
{0xf56,0xf56,0x6d1b},
{0xf57,0xf57,0x7d61},
{0xf58,0xf58,0x843d},
{0xf59,0xf59,0x916a},
{0xf5a,0xf5a,0x4e71},
{0xf5b,0xf5b,0x5375},
{0xf5c,0xf5c,0x5d50},
{0xf5d,0xf5d,0x6b04},
{0xf5e,0xf5e,0x6feb},
{0xf5f,0xf5f,0x85cd},
{0xf60,0xf60,0x862d},
{0xf61,0xf61,0x89a7},
{0xf62,0xf62,0x5229},
{0xf63,0xf63,0x540f},
{0xf64,0xf64,0x5c65},
{0xf65,0xf65,0x674e},
{0xf66,0xf66,0x68a8},
{0xf67,0xf67,0x7406},
{0xf68,0xf68,0x7483},
{0xf69,0xf69,0x75e2},
{0xf6a,0xf6a,0x88cf},
{0xf6b,0xf6b,0x88e1},
{0xf6c,0xf6c,0x91cc},
{0xf6d,0xf6d,0x96e2},
{0xf6e,0xf6e,0x9678},
{0xf6f,0xf6f,0x5f8b},
{0xf70,0xf70,0x7387},
{0xf71,0xf71,0x7acb},
{0xf72,0xf72,0x844e},
{0xf73,0xf73,0x63a0},
{0xf74,0xf74,0x7565},
{0xf75,0xf75,0x5289},
{0xf76,0xf76,0x6d41},
{0xf77,0xf77,0x6e9c},
{0xf78,0xf78,0x7409},
{0xf79,0xf79,0x7559},
{0xf7a,0xf7a,0x786b},
{0xf7b,0xf7b,0x7c92},
{0xf7c,0xf7c,0x9686},
{0xf7d,0xf7d,0x7adc},
{0xf7e,0xf7e,0x9f8d},
{0xf7f,0xf7f,0x4fb6},
{0xf80,0xf80,0x616e},
{0xf81,0xf81,0x65c5},
{0xf82,0xf82,0x865c},
{0xf83,0xf83,0x4e86},
{0xf84,0xf84,0x4eae},
{0xf85,0xf85,0x50da},
{0xf86,0xf86,0x4e21},
{0xf87,0xf87,0x51cc},
{0xf88,0xf88,0x5bee},
{0xf89,0xf89,0x6599},
{0xf8a,0xf8a,0x6881},
{0xf8b,0xf8b,0x6dbc},
{0xf8c,0xf8c,0x731f},
{0xf8d,0xf8d,0x7642},
{0xf8e,0xf8e,0x77ad},
{0xf8f,0xf8f,0x7a1c},
{0xf90,0xf90,0x7ce7},
{0xf91,0xf91,0x826f},
{0xf92,0xf92,0x8ad2},
{0xf93,0xf93,0x907c},
{0xf94,0xf94,0x91cf},
{0xf95,0xf95,0x9675},
{0xf96,0xf96,0x9818},
{0xf97,0xf97,0x529b},
{0xf98,0xf98,0x7dd1},
{0xf99,0xf99,0x502b},
{0xf9a,0xf9a,0x5398},
{0xf9b,0xf9b,0x6797},
{0xf9c,0xf9c,0x6dcb},
{0xf9d,0xf9d,0x71d0},
{0xf9e,0xf9e,0x7433},
{0xf9f,0xf9f,0x81e8},
{0xfa0,0xfa0,0x8f2a},
{0xfa1,0xfa1,0x96a3},
{0xfa2,0xfa2,0x9c57},
{0xfa3,0xfa3,0x9e9f},
{0xfa4,0xfa4,0x7460},
{0xfa5,0xfa5,0x5841},
{0xfa6,0xfa6,0x6d99},
{0xfa7,0xfa7,0x7d2f},
{0xfa8,0xfa8,0x985e},
{0xfa9,0xfa9,0x4ee4},
{0xfaa,0xfaa,0x4f36},
{0xfab,0xfab,0x4f8b},
{0xfac,0xfac,0x51b7},
{0xfad,0xfad,0x52b1},
{0xfae,0xfae,0x5dba},
{0xfaf,0xfaf,0x601c},
{0xfb0,0xfb0,0x73b2},
{0xfb1,0xfb1,0x793c},
{0xfb2,0xfb2,0x82d3},
{0xfb3,0xfb3,0x9234},
{0xfb4,0xfb4,0x96b7},
{0xfb5,0xfb5,0x96f6},
{0xfb6,0xfb6,0x970a},
{0xfb7,0xfb7,0x9e97},
{0xfb8,0xfb8,0x9f62},
{0xfb9,0xfb9,0x66a6},
{0xfba,0xfba,0x6b74},
{0xfbb,0xfbb,0x5217},
{0xfbc,0xfbc,0x52a3},
{0xfbd,0xfbd,0x70c8},
{0xfbe,0xfbe,0x88c2},
{0xfbf,0xfbf,0x5ec9},
{0xfc0,0xfc0,0x604b},
{0xfc1,0xfc1,0x6190},
{0xfc2,0xfc2,0x6f23},
{0xfc3,0xfc3,0x7149},
{0xfc4,0xfc4,0x7c3e},
{0xfc5,0xfc5,0x7df4},
{0xfc6,0xfc6,0x806f},
{0xfc7,0xfc7,0x84ee},
{0xfc8,0xfc8,0x9023},
{0xfc9,0xfc9,0x932c},
{0xfca,0xfca,0x5442},
{0xfcb,0xfcb,0x9b6f},
{0xfcc,0xfcc,0x6ad3},
{0xfcd,0xfcd,0x7089},
{0xfce,0xfce,0x8cc2},
{0xfcf,0xfcf,0x8def},
{0xfd0,0xfd0,0x9732},
{0xfd1,0xfd1,0x52b4},
{0xfd2,0xfd2,0x5a41},
{0xfd3,0xfd3,0x5eca},
{0xfd4,0xfd4,0x5f04},
{0xfd5,0xfd5,0x6717},
{0xfd6,0xfd6,0x697c},
{0xfd7,0xfd7,0x6994},
{0xfd8,0xfd8,0x6d6a},
{0xfd9,0xfd9,0x6f0f},
{0xfda,0xfda,0x7262},
{0xfdb,0xfdb,0x72fc},
{0xfdc,0xfdc,0x7bed},
{0xfdd,0xfdd,0x8001},
{0xfde,0xfde,0x807e},
{0xfdf,0xfdf,0x874b},
{0xfe0,0xfe0,0x90ce},
{0xfe1,0xfe1,0x516d},
{0xfe2,0xfe2,0x9e93},
{0xfe3,0xfe3,0x7984},
{0xfe4,0xfe4,0x808b},
{0xfe5,0xfe5,0x9332},
{0xfe6,0xfe6,0x8ad6},
{0xfe7,0xfe7,0x502d},
{0xfe8,0xfe8,0x548c},
{0xfe9,0xfe9,0x8a71},
{0xfea,0xfea,0x6b6a},
{0xfeb,0xfeb,0x8cc4},
{0xfec,0xfec,0x8107},
{0xfed,0xfed,0x60d1},
{0xfee,0xfee,0x67a0},
{0xfef,0xfef,0x9df2},
{0xff0,0xff0,0x4e99},
{0xff1,0xff1,0x4e98},
{0xff2,0xff2,0x9c10},
{0xff3,0xff3,0x8a6b},
{0xff4,0xff4,0x85c1},
{0xff5,0xff5,0x8568},
{0xff6,0xff6,0x6900},
{0xff7,0xff7,0x6e7e},
{0xff8,0xff8,0x7897},
{0xff9,0xff9,0x8155},
{0xffa,0xffa,0x5f0c},
{0xffb,0xffb,0x4e10},
{0xffc,0xffc,0x4e15},
{0xffd,0xffd,0x4e2a},
{0xffe,0xffe,0x4e31},
{0xfff,0xfff,0x4e36},
{0x1000,0x1000,0x4e3c},
{0x1001,0x1001,0x4e3f},
{0x1002,0x1002,0x4e42},
{0x1003,0x1003,0x4e56},
{0x1004,0x1004,0x4e58},
{0x1005,0x1005,0x4e82},
{0x1006,0x1006,0x4e85},
{0x1007,0x1007,0x8c6b},
{0x1008,0x1008,0x4e8a},
{0x1009,0x1009,0x8212},
{0x100a,0x100a,0x5f0d},
{0x100b,0x100b,0x4e8e},
{0x100c,0x100e,0x4e9e},
{0x100f,0x100f,0x4ea2},
{0x1010,0x1010,0x4eb0},
{0x1011,0x1011,0x4eb3},
{0x1012,0x1012,0x4eb6},
{0x1013,0x1013,0x4ece},
{0x1014,0x1014,0x4ecd},
{0x1015,0x1015,0x4ec4},
{0x1016,0x1016,0x4ec6},
{0x1017,0x1017,0x4ec2},
{0x1018,0x1018,0x4ed7},
{0x1019,0x1019,0x4ede},
{0x101a,0x101a,0x4eed},
{0x101b,0x101b,0x4edf},
{0x101c,0x101c,0x4ef7},
{0x101d,0x101d,0x4f09},
{0x101e,0x101e,0x4f5a},
{0x101f,0x101f,0x4f30},
{0x1020,0x1020,0x4f5b},
{0x1021,0x1021,0x4f5d},
{0x1022,0x1022,0x4f57},
{0x1023,0x1023,0x4f47},
{0x1024,0x1024,0x4f76},
{0x1025,0x1025,0x4f88},
{0x1026,0x1026,0x4f8f},
{0x1027,0x1027,0x4f98},
{0x1028,0x1028,0x4f7b},
{0x1029,0x1029,0x4f69},
{0x102a,0x102a,0x4f70},
{0x102b,0x102b,0x4f91},
{0x102c,0x102c,0x4f6f},
{0x102d,0x102d,0x4f86},
{0x102e,0x102e,0x4f96},
{0x102f,0x102f,0x5118},
{0x1030,0x1030,0x4fd4},
{0x1031,0x1031,0x4fdf},
{0x1032,0x1032,0x4fce},
{0x1033,0x1033,0x4fd8},
{0x1034,0x1034,0x4fdb},
{0x1035,0x1035,0x4fd1},
{0x1036,0x1036,0x4fda},
{0x1037,0x1037,0x4fd0},
{0x1038,0x1039,0x4fe4},
{0x103a,0x103a,0x501a},
{0x103b,0x103b,0x5028},
{0x103c,0x103c,0x5014},
{0x103d,0x103d,0x502a},
{0x103e,0x103e,0x5025},
{0x103f,0x103f,0x5005},
{0x1040,0x1040,0x4f1c},
{0x1041,0x1041,0x4ff6},
{0x1042,0x1042,0x5021},
{0x1043,0x1043,0x5029},
{0x1044,0x1044,0x502c},
{0x1045,0x1045,0x4ffe},
{0x1046,0x1046,0x4fef},
{0x1047,0x1047,0x5011},
{0x1048,0x1048,0x5006},
{0x1049,0x1049,0x5043},
{0x104a,0x104a,0x5047},
{0x104b,0x104b,0x6703},
{0x104c,0x104c,0x5055},
{0x104d,0x104d,0x5050},
{0x104e,0x104e,0x5048},
{0x104f,0x104f,0x505a},
{0x1050,0x1050,0x5056},
{0x1051,0x1051,0x506c},
{0x1052,0x1052,0x5078},
{0x1053,0x1053,0x5080},
{0x1054,0x1054,0x509a},
{0x1055,0x1055,0x5085},
{0x1056,0x1056,0x50b4},
{0x1057,0x1057,0x50b2},
{0x1058,0x1059,0x50c9},
{0x105a,0x105a,0x50b3},
{0x105b,0x105b,0x50c2},
{0x105c,0x105c,0x50d6},
{0x105d,0x105d,0x50de},
{0x105e,0x105e,0x50e5},
{0x105f,0x105f,0x50ed},
{0x1060,0x1060,0x50e3},
{0x1061,0x1061,0x50ee},
{0x1062,0x1062,0x50f9},
{0x1063,0x1063,0x50f5},
{0x1064,0x1064,0x5109},
{0x1065,0x1066,0x5101},
{0x1067,0x1067,0x5116},
{0x1068,0x1068,0x5115},
{0x1069,0x1069,0x5114},
{0x106a,0x106a,0x511a},
{0x106b,0x106b,0x5121},
{0x106c,0x106c,0x513a},
{0x106d,0x106d,0x5137},
{0x106e,0x106e,0x513c},
{0x106f,0x106f,0x513b},
{0x1070,0x1071,0x513f},
{0x1072,0x1072,0x5152},
{0x1073,0x1073,0x514c},
{0x1074,0x1074,0x5154},
{0x1075,0x1075,0x5162},
{0x1076,0x1076,0x7af8},
{0x1077,0x1078,0x5169},
{0x1079,0x1079,0x516e},
{0x107a,0x107a,0x5180},
{0x107b,0x107b,0x5182},
{0x107c,0x107c,0x56d8},
{0x107d,0x107d,0x518c},
{0x107e,0x107e,0x5189},
{0x107f,0x107f,0x518f},
{0x1080,0x1080,0x5191},
{0x1081,0x1081,0x5193},
{0x1082,0x1083,0x5195},
{0x1084,0x1084,0x51a4},
{0x1085,0x1085,0x51a6},
{0x1086,0x1086,0x51a2},
{0x1087,0x1089,0x51a9},
{0x108a,0x108a,0x51b3},
{0x108b,0x108c,0x51b1},
{0x108d,0x108d,0x51b0},
{0x108e,0x108e,0x51b5},
{0x108f,0x108f,0x51bd},
{0x1090,0x1090,0x51c5},
{0x1091,0x1091,0x51c9},
{0x1092,0x1092,0x51db},
{0x1093,0x1093,0x51e0},
{0x1094,0x1094,0x8655},
{0x1095,0x1095,0x51e9},
{0x1096,0x1096,0x51ed},
{0x1097,0x1097,0x51f0},
{0x1098,0x1098,0x51f5},
{0x1099,0x1099,0x51fe},
{0x109a,0x109a,0x5204},
{0x109b,0x109b,0x520b},
{0x109c,0x109c,0x5214},
{0x109d,0x109d,0x520e},
{0x109e,0x109e,0x5227},
{0x109f,0x109f,0x522a},
{0x10a0,0x10a0,0x522e},
{0x10a1,0x10a1,0x5233},
{0x10a2,0x10a2,0x5239},
{0x10a3,0x10a3,0x524f},
{0x10a4,0x10a4,0x5244},
{0x10a5,0x10a6,0x524b},
{0x10a7,0x10a7,0x525e},
{0x10a8,0x10a8,0x5254},
{0x10a9,0x10a9,0x526a},
{0x10aa,0x10aa,0x5274},
{0x10ab,0x10ab,0x5269},
{0x10ac,0x10ac,0x5273},
{0x10ad,0x10ad,0x527f},
{0x10ae,0x10ae,0x527d},
{0x10af,0x10af,0x528d},
{0x10b0,0x10b0,0x5294},
{0x10b1,0x10b1,0x5292},
{0x10b2,0x10b2,0x5271},
{0x10b3,0x10b3,0x5288},
{0x10b4,0x10b4,0x5291},
{0x10b5,0x10b5,0x8fa8},
{0x10b6,0x10b6,0x8fa7},
{0x10b7,0x10b8,0x52ac},
{0x10b9,0x10b9,0x52bc},
{0x10ba,0x10ba,0x52b5},
{0x10bb,0x10bb,0x52c1},
{0x10bc,0x10bc,0x52cd},
{0x10bd,0x10bd,0x52d7},
{0x10be,0x10be,0x52de},
{0x10bf,0x10bf,0x52e3},
{0x10c0,0x10c0,0x52e6},
{0x10c1,0x10c1,0x98ed},
{0x10c2,0x10c2,0x52e0},
{0x10c3,0x10c3,0x52f3},
{0x10c4,0x10c4,0x52f5},
{0x10c5,0x10c6,0x52f8},
{0x10c7,0x10c7,0x5306},
{0x10c8,0x10c8,0x5308},
{0x10c9,0x10c9,0x7538},
{0x10ca,0x10ca,0x530d},
{0x10cb,0x10cb,0x5310},
{0x10cc,0x10cc,0x530f},
{0x10cd,0x10cd,0x5315},
{0x10ce,0x10ce,0x531a},
{0x10cf,0x10cf,0x5323},
{0x10d0,0x10d0,0x532f},
{0x10d1,0x10d1,0x5331},
{0x10d2,0x10d2,0x5333},
{0x10d3,0x10d3,0x5338},
{0x10d4,0x10d4,0x5340},
{0x10d5,0x10d5,0x5346},
{0x10d6,0x10d6,0x5345},
{0x10d7,0x10d7,0x4e17},
{0x10d8,0x10d8,0x5349},
{0x10d9,0x10d9,0x534d},
{0x10da,0x10da,0x51d6},
{0x10db,0x10db,0x535e},
{0x10dc,0x10dc,0x5369},
{0x10dd,0x10dd,0x536e},
{0x10de,0x10de,0x5918},
{0x10df,0x10df,0x537b},
{0x10e0,0x10e0,0x5377},
{0x10e1,0x10e1,0x5382},
{0x10e2,0x10e2,0x5396},
{0x10e3,0x10e3,0x53a0},
{0x10e4,0x10e4,0x53a6},
{0x10e5,0x10e5,0x53a5},
{0x10e6,0x10e6,0x53ae},
{0x10e7,0x10e7,0x53b0},
{0x10e8,0x10e8,0x53b6},
{0x10e9,0x10e9,0x53c3},
{0x10ea,0x10ea,0x7c12},
{0x10eb,0x10eb,0x96d9},
{0x10ec,0x10ec,0x53df},
{0x10ed,0x10ed,0x66fc},
{0x10ee,0x10ee,0x71ee},
{0x10ef,0x10ef,0x53ee},
{0x10f0,0x10f0,0x53e8},
{0x10f1,0x10f1,0x53ed},
{0x10f2,0x10f2,0x53fa},
{0x10f3,0x10f3,0x5401},
{0x10f4,0x10f4,0x543d},
{0x10f5,0x10f5,0x5440},
{0x10f6,0x10f7,0x542c},
{0x10f8,0x10f8,0x543c},
{0x10f9,0x10f9,0x542e},
{0x10fa,0x10fa,0x5436},
{0x10fb,0x10fb,0x5429},
{0x10fc,0x10fc,0x541d},
{0x10fd,0x10fd,0x544e},
{0x10fe,0x10fe,0x548f},
{0x10ff,0x10ff,0x5475},
{0x1100,0x1100,0x548e},
{0x1101,0x1101,0x545f},
{0x1102,0x1102,0x5471},
{0x1103,0x1103,0x5477},
{0x1104,0x1104,0x5470},
{0x1105,0x1105,0x5492},
{0x1106,0x1106,0x547b},
{0x1107,0x1107,0x5480},
{0x1108,0x1108,0x5476},
{0x1109,0x1109,0x5484},
{0x110a,0x110a,0x5490},
{0x110b,0x110b,0x5486},
{0x110c,0x110c,0x54c7},
{0x110d,0x110d,0x54a2},
{0x110e,0x110e,0x54b8},
{0x110f,0x110f,0x54a5},
{0x1110,0x1110,0x54ac},
{0x1111,0x1111,0x54c4},
{0x1112,0x1112,0x54c8},
{0x1113,0x1113,0x54a8},
{0x1114,0x1114,0x54ab},
{0x1115,0x1115,0x54c2},
{0x1116,0x1116,0x54a4},
{0x1117,0x1117,0x54be},
{0x1118,0x1118,0x54bc},
{0x1119,0x1119,0x54d8},
{0x111a,0x111b,0x54e5},
{0x111c,0x111c,0x550f},
{0x111d,0x111d,0x5514},
{0x111e,0x111e,0x54fd},
{0x111f,0x111f,0x54ee},
{0x1120,0x1120,0x54ed},
{0x1121,0x1121,0x54fa},
{0x1122,0x1122,0x54e2},
{0x1123,0x1123,0x5539},
{0x1124,0x1124,0x5540},
{0x1125,0x1125,0x5563},
{0x1126,0x1126,0x554c},
{0x1127,0x1127,0x552e},
{0x1128,0x1128,0x555c},
{0x1129,0x1129,0x5545},
{0x112a,0x112b,0x5556},
{0x112c,0x112c,0x5538},
{0x112d,0x112d,0x5533},
{0x112e,0x112e,0x555d},
{0x112f,0x112f,0x5599},
{0x1130,0x1130,0x5580},
{0x1131,0x1131,0x54af},
{0x1132,0x1132,0x558a},
{0x1133,0x1133,0x559f},
{0x1134,0x1134,0x557b},
{0x1135,0x1135,0x557e},
{0x1136,0x1136,0x5598},
{0x1137,0x1137,0x559e},
{0x1138,0x1138,0x55ae},
{0x1139,0x1139,0x557c},
{0x113a,0x113a,0x5583},
{0x113b,0x113b,0x55a9},
{0x113c,0x113c,0x5587},
{0x113d,0x113d,0x55a8},
{0x113e,0x113e,0x55da},
{0x113f,0x113f,0x55c5},
{0x1140,0x1140,0x55df},
{0x1141,0x1141,0x55c4},
{0x1142,0x1142,0x55dc},
{0x1143,0x1143,0x55e4},
{0x1144,0x1144,0x55d4},
{0x1145,0x1145,0x5614},
{0x1146,0x1146,0x55f7},
{0x1147,0x1147,0x5616},
{0x1148,0x1148,0x55fe},
{0x1149,0x1149,0x55fd},
{0x114a,0x114a,0x561b},
{0x114b,0x114b,0x55f9},
{0x114c,0x114c,0x564e},
{0x114d,0x114d,0x5650},
{0x114e,0x114e,0x71df},
{0x114f,0x114f,0x5634},
{0x1150,0x1150,0x5636},
{0x1151,0x1151,0x5632},
{0x1152,0x1152,0x5638},
{0x1153,0x1153,0x566b},
{0x1154,0x1154,0x5664},
{0x1155,0x1155,0x562f},
{0x1156,0x1156,0x566c},
{0x1157,0x1157,0x566a},
{0x1158,0x1158,0x5686},
{0x1159,0x1159,0x5680},
{0x115a,0x115a,0x568a},
{0x115b,0x115b,0x56a0},
{0x115c,0x115c,0x5694},
{0x115d,0x115d,0x568f},
{0x115e,0x115e,0x56a5},
{0x115f,0x115f,0x56ae},
{0x1160,0x1160,0x56b6},
{0x1161,0x1161,0x56b4},
{0x1162,0x1162,0x56c2},
{0x1163,0x1163,0x56bc},
{0x1164,0x1164,0x56c1},
{0x1165,0x1165,0x56c3},
{0x1166,0x1166,0x56c0},
{0x1167,0x1167,0x56c8},
{0x1168,0x1168,0x56ce},
{0x1169,0x1169,0x56d1},
{0x116a,0x116a,0x56d3},
{0x116b,0x116b,0x56d7},
{0x116c,0x116c,0x56ee},
{0x116d,0x116d,0x56f9},
{0x116e,0x116e,0x5700},
{0x116f,0x116f,0x56ff},
{0x1170,0x1170,0x5704},
{0x1171,0x1171,0x5709},
{0x1172,0x1172,0x5708},
{0x1173,0x1173,0x570b},
{0x1174,0x1174,0x570d},
{0x1175,0x1175,0x5713},
{0x1176,0x1176,0x5718},
{0x1177,0x1177,0x5716},
{0x1178,0x1178,0x55c7},
{0x1179,0x1179,0x571c},
{0x117a,0x117a,0x5726},
{0x117b,0x117c,0x5737},
{0x117d,0x117d,0x574e},
{0x117e,0x117e,0x573b},
{0x117f,0x117f,0x5740},
{0x1180,0x1180,0x574f},
{0x1181,0x1181,0x5769},
{0x1182,0x1182,0x57c0},
{0x1183,0x1183,0x5788},
{0x1184,0x1184,0x5761},
{0x1185,0x1185,0x577f},
{0x1186,0x1186,0x5789},
{0x1187,0x1187,0x5793},
{0x1188,0x1188,0x57a0},
{0x1189,0x1189,0x57b3},
{0x118a,0x118a,0x57a4},
{0x118b,0x118b,0x57aa},
{0x118c,0x118c,0x57b0},
{0x118d,0x118d,0x57c3},
{0x118e,0x118e,0x57c6},
{0x118f,0x118f,0x57d4},
{0x1190,0x1191,0x57d2},
{0x1192,0x1192,0x580a},
{0x1193,0x1193,0x57d6},
{0x1194,0x1194,0x57e3},
{0x1195,0x1195,0x580b},
{0x1196,0x1196,0x5819},
{0x1197,0x1197,0x581d},
{0x1198,0x1198,0x5872},
{0x1199,0x1199,0x5821},
{0x119a,0x119a,0x5862},
{0x119b,0x119b,0x584b},
{0x119c,0x119c,0x5870},
{0x119d,0x119d,0x6bc0},
{0x119e,0x119e,0x5852},
{0x119f,0x119f,0x583d},
{0x11a0,0x11a0,0x5879},
{0x11a1,0x11a1,0x5885},
{0x11a2,0x11a2,0x58b9},
{0x11a3,0x11a3,0x589f},
{0x11a4,0x11a4,0x58ab},
{0x11a5,0x11a5,0x58ba},
{0x11a6,0x11a6,0x58de},
{0x11a7,0x11a7,0x58bb},
{0x11a8,0x11a8,0x58b8},
{0x11a9,0x11a9,0x58ae},
{0x11aa,0x11aa,0x58c5},
{0x11ab,0x11ab,0x58d3},
{0x11ac,0x11ac,0x58d1},
{0x11ad,0x11ad,0x58d7},
{0x11ae,0x11ae,0x58d9},
{0x11af,0x11af,0x58d8},
{0x11b0,0x11b0,0x58e5},
{0x11b1,0x11b1,0x58dc},
{0x11b2,0x11b2,0x58e4},
{0x11b3,0x11b3,0x58df},
{0x11b4,0x11b4,0x58ef},
{0x11b5,0x11b5,0x58fa},
{0x11b6,0x11b6,0x58f9},
{0x11b7,0x11b9,0x58fb},
{0x11ba,0x11ba,0x5902},
{0x11bb,0x11bb,0x590a},
{0x11bc,0x11bc,0x5910},
{0x11bd,0x11bd,0x591b},
{0x11be,0x11be,0x68a6},
{0x11bf,0x11bf,0x5925},
{0x11c0,0x11c1,0x592c},
{0x11c2,0x11c2,0x5932},
{0x11c3,0x11c3,0x5938},
{0x11c4,0x11c4,0x593e},
{0x11c5,0x11c5,0x7ad2},
{0x11c6,0x11c6,0x5955},
{0x11c7,0x11c7,0x5950},
{0x11c8,0x11c8,0x594e},
{0x11c9,0x11c9,0x595a},
{0x11ca,0x11ca,0x5958},
{0x11cb,0x11cb,0x5962},
{0x11cc,0x11cc,0x5960},
{0x11cd,0x11cd,0x5967},
{0x11ce,0x11ce,0x596c},
{0x11cf,0x11cf,0x5969},
{0x11d0,0x11d0,0x5978},
{0x11d1,0x11d1,0x5981},
{0x11d2,0x11d2,0x599d},
{0x11d3,0x11d3,0x4f5e},
{0x11d4,0x11d4,0x4fab},
{0x11d5,0x11d5,0x59a3},
{0x11d6,0x11d6,0x59b2},
{0x11d7,0x11d7,0x59c6},
{0x11d8,0x11d8,0x59e8},
{0x11d9,0x11d9,0x59dc},
{0x11da,0x11da,0x598d},
{0x11db,0x11dc,0x59d9},
{0x11dd,0x11dd,0x5a25},
{0x11de,0x11de,0x5a1f},
{0x11df,0x11df,0x5a11},
{0x11e0,0x11e0,0x5a1c},
{0x11e1,0x11e1,0x5a09},
{0x11e2,0x11e2,0x5a1a},
{0x11e3,0x11e3,0x5a40},
{0x11e4,0x11e4,0x5a6c},
{0x11e5,0x11e5,0x5a49},
{0x11e6,0x11e7,0x5a35},
{0x11e8,0x11e8,0x5a62},
{0x11e9,0x11e9,0x5a6a},
{0x11ea,0x11ea,0x5a9a},
{0x11eb,0x11eb,0x5abc},
{0x11ec,0x11ec,0x5abe},
{0x11ed,0x11ed,0x5acb},
{0x11ee,0x11ee,0x5ac2},
{0x11ef,0x11ef,0x5abd},
{0x11f0,0x11f0,0x5ae3},
{0x11f1,0x11f1,0x5ad7},
{0x11f2,0x11f2,0x5ae6},
{0x11f3,0x11f3,0x5ae9},
{0x11f4,0x11f4,0x5ad6},
{0x11f5,0x11f6,0x5afa},
{0x11f7,0x11f7,0x5b0c},
{0x11f8,0x11f8,0x5b0b},
{0x11f9,0x11f9,0x5b16},
{0x11fa,0x11fa,0x5b32},
{0x11fb,0x11fb,0x5ad0},
{0x11fc,0x11fc,0x5b2a},
{0x11fd,0x11fd,0x5b36},
{0x11fe,0x11fe,0x5b3e},
{0x11ff,0x11ff,0x5b43},
{0x1200,0x1200,0x5b45},
{0x1201,0x1201,0x5b40},
{0x1202,0x1202,0x5b51},
{0x1203,0x1203,0x5b55},
{0x1204,0x1205,0x5b5a},
{0x1206,0x1206,0x5b65},
{0x1207,0x1207,0x5b69},
{0x1208,0x1208,0x5b70},
{0x1209,0x1209,0x5b73},
{0x120a,0x120a,0x5b75},
{0x120b,0x120b,0x5b78},
{0x120c,0x120c,0x6588},
{0x120d,0x120d,0x5b7a},
{0x120e,0x120e,0x5b80},
{0x120f,0x120f,0x5b83},
{0x1210,0x1210,0x5ba6},
{0x1211,0x1211,0x5bb8},
{0x1212,0x1212,0x5bc3},
{0x1213,0x1213,0x5bc7},
{0x1214,0x1214,0x5bc9},
{0x1215,0x1215,0x5bd4},
{0x1216,0x1216,0x5bd0},
{0x1217,0x1217,0x5be4},
{0x1218,0x1218,0x5be6},
{0x1219,0x1219,0x5be2},
{0x121a,0x121a,0x5bde},
{0x121b,0x121b,0x5be5},
{0x121c,0x121c,0x5beb},
{0x121d,0x121d,0x5bf0},
{0x121e,0x121e,0x5bf6},
{0x121f,0x121f,0x5bf3},
{0x1220,0x1220,0x5c05},
{0x1221,0x1222,0x5c07},
{0x1223,0x1223,0x5c0d},
{0x1224,0x1224,0x5c13},
{0x1225,0x1225,0x5c20},
{0x1226,0x1226,0x5c22},
{0x1227,0x1227,0x5c28},
{0x1228,0x1229,0x5c38},
{0x122a,0x122a,0x5c41},
{0x122b,0x122b,0x5c46},
{0x122c,0x122c,0x5c4e},
{0x122d,0x122d,0x5c53},
{0x122e,0x122e,0x5c50},
{0x122f,0x122f,0x5c4f},
{0x1230,0x1230,0x5b71},
{0x1231,0x1231,0x5c6c},
{0x1232,0x1232,0x5c6e},
{0x1233,0x1233,0x4e62},
{0x1234,0x1234,0x5c76},
{0x1235,0x1235,0x5c79},
{0x1236,0x1236,0x5c8c},
{0x1237,0x1237,0x5c91},
{0x1238,0x1238,0x5c94},
{0x1239,0x1239,0x599b},
{0x123a,0x123a,0x5cab},
{0x123b,0x123b,0x5cbb},
{0x123c,0x123c,0x5cb6},
{0x123d,0x123d,0x5cbc},
{0x123e,0x123e,0x5cb7},
{0x123f,0x123f,0x5cc5},
{0x1240,0x1240,0x5cbe},
{0x1241,0x1241,0x5cc7},
{0x1242,0x1242,0x5cd9},
{0x1243,0x1243,0x5ce9},
{0x1244,0x1244,0x5cfd},
{0x1245,0x1245,0x5cfa},
{0x1246,0x1246,0x5ced},
{0x1247,0x1247,0x5d8c},
{0x1248,0x1248,0x5cea},
{0x1249,0x1249,0x5d0b},
{0x124a,0x124a,0x5d15},
{0x124b,0x124b,0x5d17},
{0x124c,0x124c,0x5d5c},
{0x124d,0x124d,0x5d1f},
{0x124e,0x124e,0x5d1b},
{0x124f,0x124f,0x5d11},
{0x1250,0x1250,0x5d14},
{0x1251,0x1251,0x5d22},
{0x1252,0x1252,0x5d1a},
{0x1253,0x1253,0x5d19},
{0x1254,0x1254,0x5d18},
{0x1255,0x1255,0x5d4c},
{0x1256,0x1256,0x5d52},
{0x1257,0x1257,0x5d4e},
{0x1258,0x1258,0x5d4b},
{0x1259,0x1259,0x5d6c},
{0x125a,0x125a,0x5d73},
{0x125b,0x125b,0x5d76},
{0x125c,0x125c,0x5d87},
{0x125d,0x125d,0x5d84},
{0x125e,0x125e,0x5d82},
{0x125f,0x125f,0x5da2},
{0x1260,0x1260,0x5d9d},
{0x1261,0x1261,0x5dac},
{0x1262,0x1262,0x5dae},
{0x1263,0x1263,0x5dbd},
{0x1264,0x1264,0x5d90},
{0x1265,0x1265,0x5db7},
{0x1266,0x1266,0x5dbc},
{0x1267,0x1267,0x5dc9},
{0x1268,0x1268,0x5dcd},
{0x1269,0x1269,0x5dd3},
{0x126a,0x126a,0x5dd2},
{0x126b,0x126b,0x5dd6},
{0x126c,0x126c,0x5ddb},
{0x126d,0x126d,0x5deb},
{0x126e,0x126e,0x5df2},
{0x126f,0x126f,0x5df5},
{0x1270,0x1270,0x5e0b},
{0x1271,0x1271,0x5e1a},
{0x1272,0x1272,0x5e19},
{0x1273,0x1273,0x5e11},
{0x1274,0x1274,0x5e1b},
{0x1275,0x1276,0x5e36},
{0x1277,0x1277,0x5e44},
{0x1278,0x1278,0x5e43},
{0x1279,0x1279,0x5e40},
{0x127a,0x127a,0x5e4e},
{0x127b,0x127b,0x5e57},
{0x127c,0x127c,0x5e54},
{0x127d,0x127d,0x5e5f},
{0x127e,0x127e,0x5e62},
{0x127f,0x127f,0x5e64},
{0x1280,0x1280,0x5e47},
{0x1281,0x1282,0x5e75},
{0x1283,0x1283,0x5e7a},
{0x1284,0x1284,0x9ebc},
{0x1285,0x1285,0x5e7f},
{0x1286,0x1286,0x5ea0},
{0x1287,0x1288,0x5ec1},
{0x1289,0x1289,0x5ec8},
{0x128a,0x128a,0x5ed0},
{0x128b,0x128b,0x5ecf},
{0x128c,0x128c,0x5ed6},
{0x128d,0x128d,0x5ee3},
{0x128e,0x128e,0x5edd},
{0x128f,0x1290,0x5eda},
{0x1291,0x1291,0x5ee2},
{0x1292,0x1292,0x5ee1},
{0x1293,0x1294,0x5ee8},
{0x1295,0x1295,0x5eec},
{0x1296,0x1296,0x5ef1},
{0x1297,0x1297,0x5ef3},
{0x1298,0x1298,0x5ef0},
{0x1299,0x1299,0x5ef4},
{0x129a,0x129a,0x5ef8},
{0x129b,0x129b,0x5efe},
{0x129c,0x129c,0x5f03},
{0x129d,0x129d,0x5f09},
{0x129e,0x129e,0x5f5d},
{0x129f,0x129f,0x5f5c},
{0x12a0,0x12a0,0x5f0b},
{0x12a1,0x12a1,0x5f11},
{0x12a2,0x12a2,0x5f16},
{0x12a3,0x12a3,0x5f29},
{0x12a4,0x12a4,0x5f2d},
{0x12a5,0x12a5,0x5f38},
{0x12a6,0x12a6,0x5f41},
{0x12a7,0x12a7,0x5f48},
{0x12a8,0x12a8,0x5f4c},
{0x12a9,0x12a9,0x5f4e},
{0x12aa,0x12aa,0x5f2f},
{0x12ab,0x12ab,0x5f51},
{0x12ac,0x12ad,0x5f56},
{0x12ae,0x12ae,0x5f59},
{0x12af,0x12af,0x5f61},
{0x12b0,0x12b0,0x5f6d},
{0x12b1,0x12b1,0x5f73},
{0x12b2,0x12b2,0x5f77},
{0x12b3,0x12b3,0x5f83},
{0x12b4,0x12b4,0x5f82},
{0x12b5,0x12b5,0x5f7f},
{0x12b6,0x12b6,0x5f8a},
{0x12b7,0x12b7,0x5f88},
{0x12b8,0x12b8,0x5f91},
{0x12b9,0x12b9,0x5f87},
{0x12ba,0x12ba,0x5f9e},
{0x12bb,0x12bb,0x5f99},
{0x12bc,0x12bc,0x5f98},
{0x12bd,0x12bd,0x5fa0},
{0x12be,0x12be,0x5fa8},
{0x12bf,0x12bf,0x5fad},
{0x12c0,0x12c0,0x5fbc},
{0x12c1,0x12c1,0x5fd6},
{0x12c2,0x12c2,0x5ffb},
{0x12c3,0x12c3,0x5fe4},
{0x12c4,0x12c4,0x5ff8},
{0x12c5,0x12c5,0x5ff1},
{0x12c6,0x12c6,0x5fdd},
{0x12c7,0x12c7,0x60b3},
{0x12c8,0x12c8,0x5fff},
{0x12c9,0x12c9,0x6021},
{0x12ca,0x12ca,0x6060},
{0x12cb,0x12cb,0x6019},
{0x12cc,0x12cc,0x6010},
{0x12cd,0x12cd,0x6029},
{0x12ce,0x12ce,0x600e},
{0x12cf,0x12cf,0x6031},
{0x12d0,0x12d0,0x601b},
{0x12d1,0x12d1,0x6015},
{0x12d2,0x12d2,0x602b},
{0x12d3,0x12d3,0x6026},
{0x12d4,0x12d4,0x600f},
{0x12d5,0x12d5,0x603a},
{0x12d6,0x12d6,0x605a},
{0x12d7,0x12d7,0x6041},
{0x12d8,0x12d8,0x606a},
{0x12d9,0x12d9,0x6077},
{0x12da,0x12da,0x605f},
{0x12db,0x12db,0x604a},
{0x12dc,0x12dc,0x6046},
{0x12dd,0x12dd,0x604d},
{0x12de,0x12de,0x6063},
{0x12df,0x12df,0x6043},
{0x12e0,0x12e0,0x6064},
{0x12e1,0x12e1,0x6042},
{0x12e2,0x12e2,0x606c},
{0x12e3,0x12e3,0x606b},
{0x12e4,0x12e4,0x6059},
{0x12e5,0x12e5,0x6081},
{0x12e6,0x12e6,0x608d},
{0x12e7,0x12e7,0x60e7},
{0x12e8,0x12e8,0x6083},
{0x12e9,0x12e9,0x609a},
{0x12ea,0x12ea,0x6084},
{0x12eb,0x12eb,0x609b},
{0x12ec,0x12ed,0x6096},
{0x12ee,0x12ee,0x6092},
{0x12ef,0x12ef,0x60a7},
{0x12f0,0x12f0,0x608b},
{0x12f1,0x12f1,0x60e1},
{0x12f2,0x12f2,0x60b8},
{0x12f3,0x12f3,0x60e0},
{0x12f4,0x12f4,0x60d3},
{0x12f5,0x12f5,0x60b4},
{0x12f6,0x12f6,0x5ff0},
{0x12f7,0x12f7,0x60bd},
{0x12f8,0x12f8,0x60c6},
{0x12f9,0x12f9,0x60b5},
{0x12fa,0x12fa,0x60d8},
{0x12fb,0x12fb,0x614d},
{0x12fc,0x12fc,0x6115},
{0x12fd,0x12fd,0x6106},
{0x12fe,0x12ff,0x60f6},
{0x1300,0x1300,0x6100},
{0x1301,0x1301,0x60f4},
{0x1302,0x1302,0x60fa},
{0x1303,0x1303,0x6103},
{0x1304,0x1304,0x6121},
{0x1305,0x1305,0x60fb},
{0x1306,0x1306,0x60f1},
{0x1307,0x1308,0x610d},
{0x1309,0x1309,0x6147},
{0x130a,0x130a,0x613e},
{0x130b,0x130b,0x6128},
{0x130c,0x130c,0x6127},
{0x130d,0x130d,0x614a},
{0x130e,0x130e,0x613f},
{0x130f,0x130f,0x613c},
{0x1310,0x1310,0x612c},
{0x1311,0x1311,0x6134},
{0x1312,0x1312,0x613d},
{0x1313,0x1313,0x6142},
{0x1314,0x1314,0x6144},
{0x1315,0x1315,0x6173},
{0x1316,0x1316,0x6177},
{0x1317,0x1319,0x6158},
{0x131a,0x131a,0x616b},
{0x131b,0x131b,0x6174},
{0x131c,0x131c,0x616f},
{0x131d,0x131d,0x6165},
{0x131e,0x131e,0x6171},
{0x131f,0x131f,0x615f},
{0x1320,0x1320,0x615d},
{0x1321,0x1321,0x6153},
{0x1322,0x1322,0x6175},
{0x1323,0x1323,0x6199},
{0x1324,0x1324,0x6196},
{0x1325,0x1325,0x6187},
{0x1326,0x1326,0x61ac},
{0x1327,0x1327,0x6194},
{0x1328,0x1328,0x619a},
{0x1329,0x1329,0x618a},
{0x132a,0x132a,0x6191},
{0x132b,0x132b,0x61ab},
{0x132c,0x132c,0x61ae},
{0x132d,0x132d,0x61cc},
{0x132e,0x132e,0x61ca},
{0x132f,0x132f,0x61c9},
{0x1330,0x1330,0x61f7},
{0x1331,0x1331,0x61c8},
{0x1332,0x1332,0x61c3},
{0x1333,0x1333,0x61c6},
{0x1334,0x1334,0x61ba},
{0x1335,0x1335,0x61cb},
{0x1336,0x1336,0x7f79},
{0x1337,0x1337,0x61cd},
{0x1338,0x1338,0x61e6},
{0x1339,0x1339,0x61e3},
{0x133a,0x133a,0x61f6},
{0x133b,0x133b,0x61fa},
{0x133c,0x133c,0x61f4},
{0x133d,0x133d,0x61ff},
{0x133e,0x133e,0x61fd},
{0x133f,0x133f,0x61fc},
{0x1340,0x1340,0x61fe},
{0x1341,0x1341,0x6200},
{0x1342,0x1343,0x6208},
{0x1344,0x1344,0x620d},
{0x1345,0x1345,0x620c},
{0x1346,0x1346,0x6214},
{0x1347,0x1347,0x621b},
{0x1348,0x1348,0x621e},
{0x1349,0x1349,0x6221},
{0x134a,0x134a,0x622a},
{0x134b,0x134b,0x622e},
{0x134c,0x134c,0x6230},
{0x134d,0x134e,0x6232},
{0x134f,0x134f,0x6241},
{0x1350,0x1350,0x624e},
{0x1351,0x1351,0x625e},
{0x1352,0x1352,0x6263},
{0x1353,0x1353,0x625b},
{0x1354,0x1354,0x6260},
{0x1355,0x1355,0x6268},
{0x1356,0x1356,0x627c},
{0x1357,0x1357,0x6282},
{0x1358,0x1358,0x6289},
{0x1359,0x1359,0x627e},
{0x135a,0x135b,0x6292},
{0x135c,0x135c,0x6296},
{0x135d,0x135d,0x62d4},
{0x135e,0x135e,0x6283},
{0x135f,0x135f,0x6294},
{0x1360,0x1360,0x62d7},
{0x1361,0x1361,0x62d1},
{0x1362,0x1362,0x62bb},
{0x1363,0x1363,0x62cf},
{0x1364,0x1364,0x62ff},
{0x1365,0x1365,0x62c6},
{0x1366,0x1366,0x64d4},
{0x1367,0x1367,0x62c8},
{0x1368,0x1368,0x62dc},
{0x1369,0x1369,0x62cc},
{0x136a,0x136a,0x62ca},
{0x136b,0x136b,0x62c2},
{0x136c,0x136c,0x62c7},
{0x136d,0x136d,0x629b},
{0x136e,0x136e,0x62c9},
{0x136f,0x136f,0x630c},
{0x1370,0x1370,0x62ee},
{0x1371,0x1371,0x62f1},
{0x1372,0x1372,0x6327},
{0x1373,0x1373,0x6302},
{0x1374,0x1374,0x6308},
{0x1375,0x1375,0x62ef},
{0x1376,0x1376,0x62f5},
{0x1377,0x1377,0x6350},
{0x1378,0x1378,0x633e},
{0x1379,0x1379,0x634d},
{0x137a,0x137a,0x641c},
{0x137b,0x137b,0x634f},
{0x137c,0x137c,0x6396},
{0x137d,0x137d,0x638e},
{0x137e,0x137e,0x6380},
{0x137f,0x137f,0x63ab},
{0x1380,0x1380,0x6376},
{0x1381,0x1381,0x63a3},
{0x1382,0x1382,0x638f},
{0x1383,0x1383,0x6389},
{0x1384,0x1384,0x639f},
{0x1385,0x1385,0x63b5},
{0x1386,0x1386,0x636b},
{0x1387,0x1387,0x6369},
{0x1388,0x1388,0x63be},
{0x1389,0x1389,0x63e9},
{0x138a,0x138a,0x63c0},
{0x138b,0x138b,0x63c6},
{0x138c,0x138c,0x63e3},
{0x138d,0x138d,0x63c9},
{0x138e,0x138e,0x63d2},
{0x138f,0x138f,0x63f6},
{0x1390,0x1390,0x63c4},
{0x1391,0x1391,0x6416},
{0x1392,0x1392,0x6434},
{0x1393,0x1393,0x6406},
{0x1394,0x1394,0x6413},
{0x1395,0x1395,0x6426},
{0x1396,0x1396,0x6436},
{0x1397,0x1397,0x651d},
{0x1398,0x1398,0x6417},
{0x1399,0x1399,0x6428},
{0x139a,0x139a,0x640f},
{0x139b,0x139b,0x6467},
{0x139c,0x139c,0x646f},
{0x139d,0x139d,0x6476},
{0x139e,0x139e,0x644e},
{0x139f,0x139f,0x652a},
{0x13a0,0x13a0,0x6495},
{0x13a1,0x13a1,0x6493},
{0x13a2,0x13a2,0x64a5},
{0x13a3,0x13a3,0x64a9},
{0x13a4,0x13a4,0x6488},
{0x13a5,0x13a5,0x64bc},
{0x13a6,0x13a6,0x64da},
{0x13a7,0x13a7,0x64d2},
{0x13a8,0x13a8,0x64c5},
{0x13a9,0x13a9,0x64c7},
{0x13aa,0x13aa,0x64bb},
{0x13ab,0x13ab,0x64d8},
{0x13ac,0x13ac,0x64c2},
{0x13ad,0x13ad,0x64f1},
{0x13ae,0x13ae,0x64e7},
{0x13af,0x13af,0x8209},
{0x13b0,0x13b1,0x64e0},
{0x13b2,0x13b2,0x62ac},
{0x13b3,0x13b3,0x64e3},
{0x13b4,0x13b4,0x64ef},
{0x13b5,0x13b5,0x652c},
{0x13b6,0x13b6,0x64f6},
{0x13b7,0x13b7,0x64f4},
{0x13b8,0x13b8,0x64f2},
{0x13b9,0x13b9,0x64fa},
{0x13ba,0x13ba,0x6500},
{0x13bb,0x13bb,0x64fd},
{0x13bc,0x13bc,0x6518},
{0x13bd,0x13bd,0x651c},
{0x13be,0x13be,0x6505},
{0x13bf,0x13bf,0x6524},
{0x13c0,0x13c0,0x6523},
{0x13c1,0x13c1,0x652b},
{0x13c2,0x13c3,0x6534},
{0x13c4,0x13c4,0x6537},
{0x13c5,0x13c5,0x6536},
{0x13c6,0x13c6,0x6538},
{0x13c7,0x13c7,0x754b},
{0x13c8,0x13c8,0x6548},
{0x13c9,0x13c9,0x6556},
{0x13ca,0x13ca,0x6555},
{0x13cb,0x13cb,0x654d},
{0x13cc,0x13cc,0x6558},
{0x13cd,0x13cd,0x655e},
{0x13ce,0x13ce,0x655d},
{0x13cf,0x13cf,0x6572},
{0x13d0,0x13d0,0x6578},
{0x13d1,0x13d2,0x6582},
{0x13d3,0x13d3,0x8b8a},
{0x13d4,0x13d4,0x659b},
{0x13d5,0x13d5,0x659f},
{0x13d6,0x13d6,0x65ab},
{0x13d7,0x13d7,0x65b7},
{0x13d8,0x13d8,0x65c3},
{0x13d9,0x13d9,0x65c6},
{0x13da,0x13da,0x65c1},
{0x13db,0x13db,0x65c4},
{0x13dc,0x13dc,0x65cc},
{0x13dd,0x13dd,0x65d2},
{0x13de,0x13de,0x65db},
{0x13df,0x13df,0x65d9},
{0x13e0,0x13e1,0x65e0},
{0x13e2,0x13e2,0x65f1},
{0x13e3,0x13e3,0x6772},
{0x13e4,0x13e4,0x660a},
{0x13e5,0x13e5,0x6603},
{0x13e6,0x13e6,0x65fb},
{0x13e7,0x13e7,0x6773},
{0x13e8,0x13e9,0x6635},
{0x13ea,0x13ea,0x6634},
{0x13eb,0x13eb,0x661c},
{0x13ec,0x13ec,0x664f},
{0x13ed,0x13ed,0x6644},
{0x13ee,0x13ee,0x6649},
{0x13ef,0x13ef,0x6641},
{0x13f0,0x13f0,0x665e},
{0x13f1,0x13f1,0x665d},
{0x13f2,0x13f2,0x6664},
{0x13f3,0x13f4,0x6667},
{0x13f5,0x13f5,0x665f},
{0x13f6,0x13f6,0x6662},
{0x13f7,0x13f7,0x6670},
{0x13f8,0x13f8,0x6683},
{0x13f9,0x13f9,0x6688},
{0x13fa,0x13fa,0x668e},
{0x13fb,0x13fb,0x6689},
{0x13fc,0x13fc,0x6684},
{0x13fd,0x13fd,0x6698},
{0x13fe,0x13fe,0x669d},
{0x13ff,0x13ff,0x66c1},
{0x1400,0x1400,0x66b9},
{0x1401,0x1401,0x66c9},
{0x1402,0x1402,0x66be},
{0x1403,0x1403,0x66bc},
{0x1404,0x1404,0x66c4},
{0x1405,0x1405,0x66b8},
{0x1406,0x1406,0x66d6},
{0x1407,0x1407,0x66da},
{0x1408,0x1408,0x66e0},
{0x1409,0x1409,0x663f},
{0x140a,0x140a,0x66e6},
{0x140b,0x140b,0x66e9},
{0x140c,0x140c,0x66f0},
{0x140d,0x140d,0x66f5},
{0x140e,0x140e,0x66f7},
{0x140f,0x140f,0x670f},
{0x1410,0x1410,0x6716},
{0x1411,0x1411,0x671e},
{0x1412,0x1413,0x6726},
{0x1414,0x1414,0x9738},
{0x1415,0x1415,0x672e},
{0x1416,0x1416,0x673f},
{0x1417,0x1417,0x6736},
{0x1418,0x1418,0x6741},
{0x1419,0x1419,0x6738},
{0x141a,0x141a,0x6737},
{0x141b,0x141b,0x6746},
{0x141c,0x141c,0x675e},
{0x141d,0x141d,0x6760},
{0x141e,0x141e,0x6759},
{0x141f,0x1420,0x6763},
{0x1421,0x1421,0x6789},
{0x1422,0x1422,0x6770},
{0x1423,0x1423,0x67a9},
{0x1424,0x1424,0x677c},
{0x1425,0x1425,0x676a},
{0x1426,0x1426,0x678c},
{0x1427,0x1427,0x678b},
{0x1428,0x1428,0x67a6},
{0x1429,0x1429,0x67a1},
{0x142a,0x142a,0x6785},
{0x142b,0x142b,0x67b7},
{0x142c,0x142c,0x67ef},
{0x142d,0x142d,0x67b4},
{0x142e,0x142e,0x67ec},
{0x142f,0x142f,0x67b3},
{0x1430,0x1430,0x67e9},
{0x1431,0x1431,0x67b8},
{0x1432,0x1432,0x67e4},
{0x1433,0x1433,0x67de},
{0x1434,0x1434,0x67dd},
{0x1435,0x1435,0x67e2},
{0x1436,0x1436,0x67ee},
{0x1437,0x1437,0x67b9},
{0x1438,0x1438,0x67ce},
{0x1439,0x1439,0x67c6},
{0x143a,0x143a,0x67e7},
{0x143b,0x143b,0x6a9c},
{0x143c,0x143c,0x681e},
{0x143d,0x143d,0x6846},
{0x143e,0x143e,0x6829},
{0x143f,0x143f,0x6840},
{0x1440,0x1440,0x684d},
{0x1441,0x1441,0x6832},
{0x1442,0x1442,0x684e},
{0x1443,0x1443,0x68b3},
{0x1444,0x1444,0x682b},
{0x1445,0x1445,0x6859},
{0x1446,0x1446,0x6863},
{0x1447,0x1447,0x6877},
{0x1448,0x1448,0x687f},
{0x1449,0x1449,0x689f},
{0x144a,0x144a,0x688f},
{0x144b,0x144b,0x68ad},
{0x144c,0x144c,0x6894},
{0x144d,0x144d,0x689d},
{0x144e,0x144e,0x689b},
{0x144f,0x144f,0x6883},
{0x1450,0x1450,0x6aae},
{0x1451,0x1451,0x68b9},
{0x1452,0x1452,0x6874},
{0x1453,0x1453,0x68b5},
{0x1454,0x1454,0x68a0},
{0x1455,0x1455,0x68ba},
{0x1456,0x1456,0x690f},
{0x1457,0x1457,0x688d},
{0x1458,0x1458,0x687e},
{0x1459,0x1459,0x6901},
{0x145a,0x145a,0x68ca},
{0x145b,0x145b,0x6908},
{0x145c,0x145c,0x68d8},
{0x145d,0x145d,0x6922},
{0x145e,0x145e,0x6926},
{0x145f,0x145f,0x68e1},
{0x1460,0x1460,0x690c},
{0x1461,0x1461,0x68cd},
{0x1462,0x1462,0x68d4},
{0x1463,0x1463,0x68e7},
{0x1464,0x1464,0x68d5},
{0x1465,0x1465,0x6936},
{0x1466,0x1466,0x6912},
{0x1467,0x1467,0x6904},
{0x1468,0x1468,0x68d7},
{0x1469,0x1469,0x68e3},
{0x146a,0x146a,0x6925},
{0x146b,0x146b,0x68f9},
{0x146c,0x146c,0x68e0},
{0x146d,0x146d,0x68ef},
{0x146e,0x146e,0x6928},
{0x146f,0x146f,0x692a},
{0x1470,0x1470,0x691a},
{0x1471,0x1471,0x6923},
{0x1472,0x1472,0x6921},
{0x1473,0x1473,0x68c6},
{0x1474,0x1474,0x6979},
{0x1475,0x1475,0x6977},
{0x1476,0x1476,0x695c},
{0x1477,0x1477,0x6978},
{0x1478,0x1478,0x696b},
{0x1479,0x1479,0x6954},
{0x147a,0x147a,0x697e},
{0x147b,0x147b,0x696e},
{0x147c,0x147c,0x6939},
{0x147d,0x147d,0x6974},
{0x147e,0x147e,0x693d},
{0x147f,0x147f,0x6959},
{0x1480,0x1480,0x6930},
{0x1481,0x1481,0x6961},
{0x1482,0x1482,0x695e},
{0x1483,0x1483,0x695d},
{0x1484,0x1484,0x6981},
{0x1485,0x1485,0x696a},
{0x1486,0x1486,0x69b2},
{0x1487,0x1487,0x69ae},
{0x1488,0x1488,0x69d0},
{0x1489,0x1489,0x69bf},
{0x148a,0x148a,0x69c1},
{0x148b,0x148b,0x69d3},
{0x148c,0x148c,0x69be},
{0x148d,0x148d,0x69ce},
{0x148e,0x148e,0x5be8},
{0x148f,0x148f,0x69ca},
{0x1490,0x1490,0x69dd},
{0x1491,0x1491,0x69bb},
{0x1492,0x1492,0x69c3},
{0x1493,0x1493,0x69a7},
{0x1494,0x1494,0x6a2e},
{0x1495,0x1495,0x6991},
{0x1496,0x1496,0x69a0},
{0x1497,0x1497,0x699c},
{0x1498,0x1498,0x6995},
{0x1499,0x1499,0x69b4},
{0x149a,0x149a,0x69de},
{0x149b,0x149b,0x69e8},
{0x149c,0x149c,0x6a02},
{0x149d,0x149d,0x6a1b},
{0x149e,0x149e,0x69ff},
{0x149f,0x149f,0x6b0a},
{0x14a0,0x14a0,0x69f9},
{0x14a1,0x14a1,0x69f2},
{0x14a2,0x14a2,0x69e7},
{0x14a3,0x14a3,0x6a05},
{0x14a4,0x14a4,0x69b1},
{0x14a5,0x14a5,0x6a1e},
{0x14a6,0x14a6,0x69ed},
{0x14a7,0x14a7,0x6a14},
{0x14a8,0x14a8,0x69eb},
{0x14a9,0x14a9,0x6a0a},
{0x14aa,0x14aa,0x6a12},
{0x14ab,0x14ab,0x6ac1},
{0x14ac,0x14ac,0x6a23},
{0x14ad,0x14ad,0x6a13},
{0x14ae,0x14ae,0x6a44},
{0x14af,0x14af,0x6a0c},
{0x14b0,0x14b0,0x6a72},
{0x14b1,0x14b1,0x6a36},
{0x14b2,0x14b2,0x6a78},
{0x14b3,0x14b3,0x6a47},
{0x14b4,0x14b4,0x6a62},
{0x14b5,0x14b5,0x6a59},
{0x14b6,0x14b6,0x6a66},
{0x14b7,0x14b7,0x6a48},
{0x14b8,0x14b8,0x6a38},
{0x14b9,0x14b9,0x6a22},
{0x14ba,0x14ba,0x6a90},
{0x14bb,0x14bb,0x6a8d},
{0x14bc,0x14bc,0x6aa0},
{0x14bd,0x14bd,0x6a84},
{0x14be,0x14bf,0x6aa2},
{0x14c0,0x14c0,0x6a97},
{0x14c1,0x14c1,0x8617},
{0x14c2,0x14c2,0x6abb},
{0x14c3,0x14c3,0x6ac3},
{0x14c4,0x14c4,0x6ac2},
{0x14c5,0x14c5,0x6ab8},
{0x14c6,0x14c6,0x6ab3},
{0x14c7,0x14c7,0x6aac},
{0x14c8,0x14c8,0x6ade},
{0x14c9,0x14c9,0x6ad1},
{0x14ca,0x14ca,0x6adf},
{0x14cb,0x14cb,0x6aaa},
{0x14cc,0x14cc,0x6ada},
{0x14cd,0x14cd,0x6aea},
{0x14ce,0x14ce,0x6afb},
{0x14cf,0x14cf,0x6b05},
{0x14d0,0x14d0,0x8616},
{0x14d1,0x14d1,0x6afa},
{0x14d2,0x14d2,0x6b12},
{0x14d3,0x14d3,0x6b16},
{0x14d4,0x14d4,0x9b31},
{0x14d5,0x14d5,0x6b1f},
{0x14d6,0x14d6,0x6b38},
{0x14d7,0x14d7,0x6b37},
{0x14d8,0x14d8,0x76dc},
{0x14d9,0x14d9,0x6b39},
{0x14da,0x14da,0x98ee},
{0x14db,0x14db,0x6b47},
{0x14dc,0x14dc,0x6b43},
{0x14dd,0x14dd,0x6b49},
{0x14de,0x14de,0x6b50},
{0x14df,0x14df,0x6b59},
{0x14e0,0x14e0,0x6b54},
{0x14e1,0x14e1,0x6b5b},
{0x14e2,0x14e2,0x6b5f},
{0x14e3,0x14e3,0x6b61},
{0x14e4,0x14e5,0x6b78},
{0x14e6,0x14e7,0x6b7f},
{0x14e8,0x14e8,0x6b84},
{0x14e9,0x14e9,0x6b83},
{0x14ea,0x14ea,0x6b8d},
{0x14eb,0x14eb,0x6b98},
{0x14ec,0x14ec,0x6b95},
{0x14ed,0x14ed,0x6b9e},
{0x14ee,0x14ee,0x6ba4},
{0x14ef,0x14f0,0x6baa},
{0x14f1,0x14f1,0x6baf},
{0x14f2,0x14f2,0x6bb2},
{0x14f3,0x14f3,0x6bb1},
{0x14f4,0x14f4,0x6bb3},
{0x14f5,0x14f5,0x6bb7},
{0x14f6,0x14f6,0x6bbc},
{0x14f7,0x14f7,0x6bc6},
{0x14f8,0x14f8,0x6bcb},
{0x14f9,0x14f9,0x6bd3},
{0x14fa,0x14fa,0x6bdf},
{0x14fb,0x14fb,0x6bec},
{0x14fc,0x14fc,0x6beb},
{0x14fd,0x14fd,0x6bf3},
{0x14fe,0x14fe,0x6bef},
{0x14ff,0x14ff,0x9ebe},
{0x1500,0x1500,0x6c08},
{0x1501,0x1502,0x6c13},
{0x1503,0x1503,0x6c1b},
{0x1504,0x1504,0x6c24},
{0x1505,0x1505,0x6c23},
{0x1506,0x1506,0x6c5e},
{0x1507,0x1507,0x6c55},
{0x1508,0x1508,0x6c62},
{0x1509,0x1509,0x6c6a},
{0x150a,0x150a,0x6c82},
{0x150b,0x150b,0x6c8d},
{0x150c,0x150c,0x6c9a},
{0x150d,0x150d,0x6c81},
{0x150e,0x150e,0x6c9b},
{0x150f,0x150f,0x6c7e},
{0x1510,0x1510,0x6c68},
{0x1511,0x1511,0x6c73},
{0x1512,0x1512,0x6c92},
{0x1513,0x1513,0x6c90},
{0x1514,0x1514,0x6cc4},
{0x1515,0x1515,0x6cf1},
{0x1516,0x1516,0x6cd3},
{0x1517,0x1517,0x6cbd},
{0x1518,0x1518,0x6cd7},
{0x1519,0x1519,0x6cc5},
{0x151a,0x151a,0x6cdd},
{0x151b,0x151b,0x6cae},
{0x151c,0x151c,0x6cb1},
{0x151d,0x151d,0x6cbe},
{0x151e,0x151e,0x6cba},
{0x151f,0x151f,0x6cdb},
{0x1520,0x1520,0x6cef},
{0x1521,0x1521,0x6cd9},
{0x1522,0x1522,0x6cea},
{0x1523,0x1523,0x6d1f},
{0x1524,0x1524,0x884d},
{0x1525,0x1525,0x6d36},
{0x1526,0x1526,0x6d2b},
{0x1527,0x1527,0x6d3d},
{0x1528,0x1528,0x6d38},
{0x1529,0x1529,0x6d19},
{0x152a,0x152a,0x6d35},
{0x152b,0x152b,0x6d33},
{0x152c,0x152c,0x6d12},
{0x152d,0x152d,0x6d0c},
{0x152e,0x152e,0x6d63},
{0x152f,0x152f,0x6d93},
{0x1530,0x1530,0x6d64},
{0x1531,0x1531,0x6d5a},
{0x1532,0x1532,0x6d79},
{0x1533,0x1533,0x6d59},
{0x1534,0x1534,0x6d8e},
{0x1535,0x1535,0x6d95},
{0x1536,0x1536,0x6fe4},
{0x1537,0x1537,0x6d85},
{0x1538,0x1538,0x6df9},
{0x1539,0x1539,0x6e15},
{0x153a,0x153a,0x6e0a},
{0x153b,0x153b,0x6db5},
{0x153c,0x153c,0x6dc7},
{0x153d,0x153d,0x6de6},
{0x153e,0x153e,0x6db8},
{0x153f,0x153f,0x6dc6},
{0x1540,0x1540,0x6dec},
{0x1541,0x1541,0x6dde},
{0x1542,0x1542,0x6dcc},
{0x1543,0x1543,0x6de8},
{0x1544,0x1544,0x6dd2},
{0x1545,0x1545,0x6dc5},
{0x1546,0x1546,0x6dfa},
{0x1547,0x1547,0x6dd9},
{0x1548,0x1548,0x6de4},
{0x1549,0x1549,0x6dd5},
{0x154a,0x154a,0x6dea},
{0x154b,0x154b,0x6dee},
{0x154c,0x154c,0x6e2d},
{0x154d,0x154d,0x6e6e},
{0x154e,0x154e,0x6e2e},
{0x154f,0x154f,0x6e19},
{0x1550,0x1550,0x6e72},
{0x1551,0x1551,0x6e5f},
{0x1552,0x1552,0x6e3e},
{0x1553,0x1553,0x6e23},
{0x1554,0x1554,0x6e6b},
{0x1555,0x1555,0x6e2b},
{0x1556,0x1556,0x6e76},
{0x1557,0x1557,0x6e4d},
{0x1558,0x1558,0x6e1f},
{0x1559,0x1559,0x6e43},
{0x155a,0x155a,0x6e3a},
{0x155b,0x155b,0x6e4e},
{0x155c,0x155c,0x6e24},
{0x155d,0x155d,0x6eff},
{0x155e,0x155e,0x6e1d},
{0x155f,0x155f,0x6e38},
{0x1560,0x1560,0x6e82},
{0x1561,0x1561,0x6eaa},
{0x1562,0x1562,0x6e98},
{0x1563,0x1563,0x6ec9},
{0x1564,0x1564,0x6eb7},
{0x1565,0x1565,0x6ed3},
{0x1566,0x1566,0x6ebd},
{0x1567,0x1567,0x6eaf},
{0x1568,0x1568,0x6ec4},
{0x1569,0x1569,0x6eb2},
{0x156a,0x156b,0x6ed4},
{0x156c,0x156c,0x6e8f},
{0x156d,0x156d,0x6ea5},
{0x156e,0x156e,0x6ec2},
{0x156f,0x156f,0x6e9f},
{0x1570,0x1570,0x6f41},
{0x1571,0x1571,0x6f11},
{0x1572,0x1572,0x704c},
{0x1573,0x1573,0x6eec},
{0x1574,0x1574,0x6ef8},
{0x1575,0x1575,0x6efe},
{0x1576,0x1576,0x6f3f},
{0x1577,0x1577,0x6ef2},
{0x1578,0x1578,0x6f31},
{0x1579,0x1579,0x6eef},
{0x157a,0x157a,0x6f32},
{0x157b,0x157b,0x6ecc},
{0x157c,0x157c,0x6f3e},
{0x157d,0x157d,0x6f13},
{0x157e,0x157e,0x6ef7},
{0x157f,0x157f,0x6f86},
{0x1580,0x1580,0x6f7a},
{0x1581,0x1581,0x6f78},
{0x1582,0x1582,0x6f81},
{0x1583,0x1583,0x6f80},
{0x1584,0x1584,0x6f6f},
{0x1585,0x1585,0x6f5b},
{0x1586,0x1586,0x6ff3},
{0x1587,0x1587,0x6f6d},
{0x1588,0x1588,0x6f82},
{0x1589,0x1589,0x6f7c},
{0x158a,0x158a,0x6f58},
{0x158b,0x158b,0x6f8e},
{0x158c,0x158c,0x6f91},
{0x158d,0x158d,0x6fc2},
{0x158e,0x158e,0x6f66},
{0x158f,0x158f,0x6fb3},
{0x1590,0x1590,0x6fa3},
{0x1591,0x1591,0x6fa1},
{0x1592,0x1592,0x6fa4},
{0x1593,0x1593,0x6fb9},
{0x1594,0x1594,0x6fc6},
{0x1595,0x1595,0x6faa},
{0x1596,0x1596,0x6fdf},
{0x1597,0x1597,0x6fd5},
{0x1598,0x1598,0x6fec},
{0x1599,0x1599,0x6fd4},
{0x159a,0x159a,0x6fd8},
{0x159b,0x159b,0x6ff1},
{0x159c,0x159c,0x6fee},
{0x159d,0x159d,0x6fdb},
{0x159e,0x159e,0x7009},
{0x159f,0x159f,0x700b},
{0x15a0,0x15a0,0x6ffa},
{0x15a1,0x15a1,0x7011},
{0x15a2,0x15a2,0x7001},
{0x15a3,0x15a3,0x700f},
{0x15a4,0x15a4,0x6ffe},
{0x15a5,0x15a5,0x701b},
{0x15a6,0x15a6,0x701a},
{0x15a7,0x15a7,0x6f74},
{0x15a8,0x15a8,0x701d},
{0x15a9,0x15a9,0x7018},
{0x15aa,0x15aa,0x701f},
{0x15ab,0x15ab,0x7030},
{0x15ac,0x15ac,0x703e},
{0x15ad,0x15ad,0x7032},
{0x15ae,0x15ae,0x7051},
{0x15af,0x15af,0x7063},
{0x15b0,0x15b0,0x7099},
{0x15b1,0x15b1,0x7092},
{0x15b2,0x15b2,0x70af},
{0x15b3,0x15b3,0x70f1},
{0x15b4,0x15b4,0x70ac},
{0x15b5,0x15b5,0x70b8},
{0x15b6,0x15b6,0x70b3},
{0x15b7,0x15b7,0x70ae},
{0x15b8,0x15b8,0x70df},
{0x15b9,0x15b9,0x70cb},
{0x15ba,0x15ba,0x70dd},
{0x15bb,0x15bb,0x70d9},
{0x15bc,0x15bc,0x7109},
{0x15bd,0x15bd,0x70fd},
{0x15be,0x15be,0x711c},
{0x15bf,0x15bf,0x7119},
{0x15c0,0x15c0,0x7165},
{0x15c1,0x15c1,0x7155},
{0x15c2,0x15c2,0x7188},
{0x15c3,0x15c3,0x7166},
{0x15c4,0x15c4,0x7162},
{0x15c5,0x15c5,0x714c},
{0x15c6,0x15c6,0x7156},
{0x15c7,0x15c7,0x716c},
{0x15c8,0x15c8,0x718f},
{0x15c9,0x15c9,0x71fb},
{0x15ca,0x15ca,0x7184},
{0x15cb,0x15cb,0x7195},
{0x15cc,0x15cc,0x71a8},
{0x15cd,0x15cd,0x71ac},
{0x15ce,0x15ce,0x71d7},
{0x15cf,0x15cf,0x71b9},
{0x15d0,0x15d0,0x71be},
{0x15d1,0x15d1,0x71d2},
{0x15d2,0x15d2,0x71c9},
{0x15d3,0x15d3,0x71d4},
{0x15d4,0x15d4,0x71ce},
{0x15d5,0x15d5,0x71e0},
{0x15d6,0x15d6,0x71ec},
{0x15d7,0x15d7,0x71e7},
{0x15d8,0x15d8,0x71f5},
{0x15d9,0x15d9,0x71fc},
{0x15da,0x15da,0x71f9},
{0x15db,0x15db,0x71ff},
{0x15dc,0x15dc,0x720d},
{0x15dd,0x15dd,0x7210},
{0x15de,0x15de,0x721b},
{0x15df,0x15df,0x7228},
{0x15e0,0x15e0,0x722d},
{0x15e1,0x15e1,0x722c},
{0x15e2,0x15e2,0x7230},
{0x15e3,0x15e3,0x7232},
{0x15e4,0x15e5,0x723b},
{0x15e6,0x15e7,0x723f},
{0x15e8,0x15e8,0x7246},
{0x15e9,0x15e9,0x724b},
{0x15ea,0x15ea,0x7258},
{0x15eb,0x15eb,0x7274},
{0x15ec,0x15ec,0x727e},
{0x15ed,0x15ed,0x7282},
{0x15ee,0x15ee,0x7281},
{0x15ef,0x15ef,0x7287},
{0x15f0,0x15f0,0x7292},
{0x15f1,0x15f1,0x7296},
{0x15f2,0x15f2,0x72a2},
{0x15f3,0x15f3,0x72a7},
{0x15f4,0x15f4,0x72b9},
{0x15f5,0x15f5,0x72b2},
{0x15f6,0x15f6,0x72c3},
{0x15f7,0x15f7,0x72c6},
{0x15f8,0x15f8,0x72c4},
{0x15f9,0x15f9,0x72ce},
{0x15fa,0x15fa,0x72d2},
{0x15fb,0x15fb,0x72e2},
{0x15fc,0x15fd,0x72e0},
{0x15fe,0x15fe,0x72f9},
{0x15ff,0x15ff,0x72f7},
{0x1600,0x1600,0x500f},
{0x1601,0x1601,0x7317},
{0x1602,0x1602,0x730a},
{0x1603,0x1603,0x731c},
{0x1604,0x1604,0x7316},
{0x1605,0x1605,0x731d},
{0x1606,0x1606,0x7334},
{0x1607,0x1607,0x732f},
{0x1608,0x1608,0x7329},
{0x1609,0x1609,0x7325},
{0x160a,0x160a,0x733e},
{0x160b,0x160c,0x734e},
{0x160d,0x160d,0x9ed8},
{0x160e,0x160e,0x7357},
{0x160f,0x160f,0x736a},
{0x1610,0x1610,0x7368},
{0x1611,0x1611,0x7370},
{0x1612,0x1612,0x7378},
{0x1613,0x1613,0x7375},
{0x1614,0x1614,0x737b},
{0x1615,0x1615,0x737a},
{0x1616,0x1616,0x73c8},
{0x1617,0x1617,0x73b3},
{0x1618,0x1618,0x73ce},
{0x1619,0x1619,0x73bb},
{0x161a,0x161a,0x73c0},
{0x161b,0x161b,0x73e5},
{0x161c,0x161c,0x73ee},
{0x161d,0x161d,0x73de},
{0x161e,0x161e,0x74a2},
{0x161f,0x161f,0x7405},
{0x1620,0x1620,0x746f},
{0x1621,0x1621,0x7425},
{0x1622,0x1622,0x73f8},
{0x1623,0x1623,0x7432},
{0x1624,0x1624,0x743a},
{0x1625,0x1625,0x7455},
{0x1626,0x1626,0x743f},
{0x1627,0x1627,0x745f},
{0x1628,0x1628,0x7459},
{0x1629,0x1629,0x7441},
{0x162a,0x162a,0x745c},
{0x162b,0x162b,0x7469},
{0x162c,0x162c,0x7470},
{0x162d,0x162d,0x7463},
{0x162e,0x162e,0x746a},
{0x162f,0x162f,0x7476},
{0x1630,0x1630,0x747e},
{0x1631,0x1631,0x748b},
{0x1632,0x1632,0x749e},
{0x1633,0x1633,0x74a7},
{0x1634,0x1634,0x74ca},
{0x1635,0x1635,0x74cf},
{0x1636,0x1636,0x74d4},
{0x1637,0x1637,0x73f1},
{0x1638,0x1638,0x74e0},
{0x1639,0x1639,0x74e3},
{0x163a,0x163a,0x74e7},
{0x163b,0x163b,0x74e9},
{0x163c,0x163c,0x74ee},
{0x163d,0x163d,0x74f2},
{0x163e,0x163f,0x74f0},
{0x1640,0x1640,0x74f8},
{0x1641,0x1641,0x74f7},
{0x1642,0x1642,0x7504},
{0x1643,0x1643,0x7503},
{0x1644,0x1644,0x7505},
{0x1645,0x1645,0x750c},
{0x1646,0x1646,0x750e},
{0x1647,0x1647,0x750d},
{0x1648,0x1648,0x7515},
{0x1649,0x1649,0x7513},
{0x164a,0x164a,0x751e},
{0x164b,0x164b,0x7526},
{0x164c,0x164c,0x752c},
{0x164d,0x164d,0x753c},
{0x164e,0x164e,0x7544},
{0x164f,0x164f,0x754d},
{0x1650,0x1650,0x754a},
{0x1651,0x1651,0x7549},
{0x1652,0x1652,0x755b},
{0x1653,0x1653,0x7546},
{0x1654,0x1654,0x755a},
{0x1655,0x1655,0x7569},
{0x1656,0x1656,0x7564},
{0x1657,0x1657,0x7567},
{0x1658,0x1658,0x756b},
{0x1659,0x1659,0x756d},
{0x165a,0x165a,0x7578},
{0x165b,0x165b,0x7576},
{0x165c,0x165d,0x7586},
{0x165e,0x165e,0x7574},
{0x165f,0x165f,0x758a},
{0x1660,0x1660,0x7589},
{0x1661,0x1661,0x7582},
{0x1662,0x1662,0x7594},
{0x1663,0x1663,0x759a},
{0x1664,0x1664,0x759d},
{0x1665,0x1665,0x75a5},
{0x1666,0x1666,0x75a3},
{0x1667,0x1667,0x75c2},
{0x1668,0x1668,0x75b3},
{0x1669,0x1669,0x75c3},
{0x166a,0x166a,0x75b5},
{0x166b,0x166b,0x75bd},
{0x166c,0x166c,0x75b8},
{0x166d,0x166d,0x75bc},
{0x166e,0x166e,0x75b1},
{0x166f,0x166f,0x75cd},
{0x1670,0x1670,0x75ca},
{0x1671,0x1671,0x75d2},
{0x1672,0x1672,0x75d9},
{0x1673,0x1673,0x75e3},
{0x1674,0x1674,0x75de},
{0x1675,0x1676,0x75fe},
{0x1677,0x1677,0x75fc},
{0x1678,0x1678,0x7601},
{0x1679,0x1679,0x75f0},
{0x167a,0x167a,0x75fa},
{0x167b,0x167c,0x75f2},
{0x167d,0x167d,0x760b},
{0x167e,0x167e,0x760d},
{0x167f,0x167f,0x7609},
{0x1680,0x1680,0x761f},
{0x1681,0x1681,0x7627},
{0x1682,0x1684,0x7620},
{0x1685,0x1685,0x7624},
{0x1686,0x1686,0x7634},
{0x1687,0x1687,0x7630},
{0x1688,0x1688,0x763b},
{0x1689,0x168a,0x7647},
{0x168b,0x168b,0x7646},
{0x168c,0x168c,0x765c},
{0x168d,0x168d,0x7658},
{0x168e,0x168f,0x7661},
{0x1690,0x1692,0x7668},
{0x1693,0x1693,0x7667},
{0x1694,0x1694,0x766c},
{0x1695,0x1695,0x7670},
{0x1696,0x1696,0x7672},
{0x1697,0x1697,0x7676},
{0x1698,0x1698,0x7678},
{0x1699,0x1699,0x767c},
{0x169a,0x169a,0x7680},
{0x169b,0x169b,0x7683},
{0x169c,0x169c,0x7688},
{0x169d,0x169d,0x768b},
{0x169e,0x169e,0x768e},
{0x169f,0x169f,0x7696},
{0x16a0,0x16a0,0x7693},
{0x16a1,0x16a2,0x7699},
{0x16a3,0x16a3,0x76b0},
{0x16a4,0x16a4,0x76b4},
{0x16a5,0x16a7,0x76b8},
{0x16a8,0x16a8,0x76c2},
{0x16a9,0x16a9,0x76cd},
{0x16aa,0x16aa,0x76d6},
{0x16ab,0x16ab,0x76d2},
{0x16ac,0x16ac,0x76de},
{0x16ad,0x16ad,0x76e1},
{0x16ae,0x16ae,0x76e5},
{0x16af,0x16af,0x76e7},
{0x16b0,0x16b0,0x76ea},
{0x16b1,0x16b1,0x862f},
{0x16b2,0x16b2,0x76fb},
{0x16b3,0x16b3,0x7708},
{0x16b4,0x16b4,0x7707},
{0x16b5,0x16b5,0x7704},
{0x16b6,0x16b6,0x7729},
{0x16b7,0x16b7,0x7724},
{0x16b8,0x16b8,0x771e},
{0x16b9,0x16ba,0x7725},
{0x16bb,0x16bb,0x771b},
{0x16bc,0x16bd,0x7737},
{0x16be,0x16be,0x7747},
{0x16bf,0x16bf,0x775a},
{0x16c0,0x16c0,0x7768},
{0x16c1,0x16c1,0x776b},
{0x16c2,0x16c2,0x775b},
{0x16c3,0x16c3,0x7765},
{0x16c4,0x16c4,0x777f},
{0x16c5,0x16c5,0x777e},
{0x16c6,0x16c6,0x7779},
{0x16c7,0x16c7,0x778e},
{0x16c8,0x16c8,0x778b},
{0x16c9,0x16c9,0x7791},
{0x16ca,0x16ca,0x77a0},
{0x16cb,0x16cb,0x779e},
{0x16cc,0x16cc,0x77b0},
{0x16cd,0x16cd,0x77b6},
{0x16ce,0x16ce,0x77b9},
{0x16cf,0x16cf,0x77bf},
{0x16d0,0x16d1,0x77bc},
{0x16d2,0x16d2,0x77bb},
{0x16d3,0x16d3,0x77c7},
{0x16d4,0x16d4,0x77cd},
{0x16d5,0x16d5,0x77d7},
{0x16d6,0x16d6,0x77da},
{0x16d7,0x16d7,0x77dc},
{0x16d8,0x16d8,0x77e3},
{0x16d9,0x16d9,0x77ee},
{0x16da,0x16da,0x77fc},
{0x16db,0x16db,0x780c},
{0x16dc,0x16dc,0x7812},
{0x16dd,0x16dd,0x7926},
{0x16de,0x16de,0x7820},
{0x16df,0x16df,0x792a},
{0x16e0,0x16e0,0x7845},
{0x16e1,0x16e1,0x788e},
{0x16e2,0x16e2,0x7874},
{0x16e3,0x16e3,0x7886},
{0x16e4,0x16e4,0x787c},
{0x16e5,0x16e5,0x789a},
{0x16e6,0x16e6,0x788c},
{0x16e7,0x16e7,0x78a3},
{0x16e8,0x16e8,0x78b5},
{0x16e9,0x16e9,0x78aa},
{0x16ea,0x16ea,0x78af},
{0x16eb,0x16eb,0x78d1},
{0x16ec,0x16ec,0x78c6},
{0x16ed,0x16ed,0x78cb},
{0x16ee,0x16ee,0x78d4},
{0x16ef,0x16ef,0x78be},
{0x16f0,0x16f0,0x78bc},
{0x16f1,0x16f1,0x78c5},
{0x16f2,0x16f2,0x78ca},
{0x16f3,0x16f3,0x78ec},
{0x16f4,0x16f4,0x78e7},
{0x16f5,0x16f5,0x78da},
{0x16f6,0x16f6,0x78fd},
{0x16f7,0x16f7,0x78f4},
{0x16f8,0x16f8,0x7907},
{0x16f9,0x16f9,0x7912},
{0x16fa,0x16fa,0x7911},
{0x16fb,0x16fb,0x7919},
{0x16fc,0x16fc,0x792c},
{0x16fd,0x16fd,0x792b},
{0x16fe,0x16fe,0x7940},
{0x16ff,0x16ff,0x7960},
{0x1700,0x1700,0x7957},
{0x1701,0x1701,0x795f},
{0x1702,0x1702,0x795a},
{0x1703,0x1703,0x7955},
{0x1704,0x1704,0x7953},
{0x1705,0x1705,0x797a},
{0x1706,0x1706,0x797f},
{0x1707,0x1707,0x798a},
{0x1708,0x1708,0x799d},
{0x1709,0x1709,0x79a7},
{0x170a,0x170a,0x9f4b},
{0x170b,0x170b,0x79aa},
{0x170c,0x170c,0x79ae},
{0x170d,0x170d,0x79b3},
{0x170e,0x170f,0x79b9},
{0x1710,0x1710,0x79c9},
{0x1711,0x1711,0x79d5},
{0x1712,0x1712,0x79e7},
{0x1713,0x1713,0x79ec},
{0x1714,0x1714,0x79e1},
{0x1715,0x1715,0x79e3},
{0x1716,0x1716,0x7a08},
{0x1717,0x1717,0x7a0d},
{0x1718,0x1719,0x7a18},
{0x171a,0x171a,0x7a20},
{0x171b,0x171b,0x7a1f},
{0x171c,0x171c,0x7980},
{0x171d,0x171d,0x7a31},
{0x171e,0x171e,0x7a3b},
{0x171f,0x171f,0x7a3e},
{0x1720,0x1720,0x7a37},
{0x1721,0x1721,0x7a43},
{0x1722,0x1722,0x7a57},
{0x1723,0x1723,0x7a49},
{0x1724,0x1725,0x7a61},
{0x1726,0x1726,0x7a69},
{0x1727,0x1727,0x9f9d},
{0x1728,0x1728,0x7a70},
{0x1729,0x1729,0x7a79},
{0x172a,0x172a,0x7a7d},
{0x172b,0x172b,0x7a88},
{0x172c,0x172c,0x7a97},
{0x172d,0x172d,0x7a95},
{0x172e,0x172e,0x7a98},
{0x172f,0x172f,0x7a96},
{0x1730,0x1730,0x7aa9},
{0x1731,0x1731,0x7ac8},
{0x1732,0x1732,0x7ab0},
{0x1733,0x1733,0x7ab6},
{0x1734,0x1734,0x7ac5},
{0x1735,0x1735,0x7ac4},
{0x1736,0x1736,0x7abf},
{0x1737,0x1737,0x9083},
{0x1738,0x1738,0x7ac7},
{0x1739,0x1739,0x7aca},
{0x173a,0x173a,0x7acd},
{0x173b,0x173b,0x7acf},
{0x173c,0x173c,0x7ad5},
{0x173d,0x173d,0x7ad3},
{0x173e,0x173f,0x7ad9},
{0x1740,0x1740,0x7add},
{0x1741,0x1742,0x7ae1},
{0x1743,0x1743,0x7ae6},
{0x1744,0x1744,0x7aed},
{0x1745,0x1745,0x7af0},
{0x1746,0x1746,0x7b02},
{0x1747,0x1747,0x7b0f},
{0x1748,0x1748,0x7b0a},
{0x1749,0x1749,0x7b06},
{0x174a,0x174a,0x7b33},
{0x174b,0x174c,0x7b18},
{0x174d,0x174d,0x7b1e},
{0x174e,0x174e,0x7b35},
{0x174f,0x174f,0x7b28},
{0x1750,0x1750,0x7b36},
{0x1751,0x1751,0x7b50},
{0x1752,0x1752,0x7b7a},
{0x1753,0x1753,0x7b04},
{0x1754,0x1754,0x7b4d},
{0x1755,0x1755,0x7b0b},
{0x1756,0x1756,0x7b4c},
{0x1757,0x1757,0x7b45},
{0x1758,0x1758,0x7b75},
{0x1759,0x1759,0x7b65},
{0x175a,0x175a,0x7b74},
{0x175b,0x175b,0x7b67},
{0x175c,0x175d,0x7b70},
{0x175e,0x175e,0x7b6c},
{0x175f,0x175f,0x7b6e},
{0x1760,0x1760,0x7b9d},
{0x1761,0x1761,0x7b98},
{0x1762,0x1762,0x7b9f},
{0x1763,0x1763,0x7b8d},
{0x1764,0x1764,0x7b9c},
{0x1765,0x1765,0x7b9a},
{0x1766,0x1766,0x7b8b},
{0x1767,0x1767,0x7b92},
{0x1768,0x1768,0x7b8f},
{0x1769,0x1769,0x7b5d},
{0x176a,0x176a,0x7b99},
{0x176b,0x176b,0x7bcb},
{0x176c,0x176c,0x7bc1},
{0x176d,0x176d,0x7bcc},
{0x176e,0x176e,0x7bcf},
{0x176f,0x176f,0x7bb4},
{0x1770,0x1770,0x7bc6},
{0x1771,0x1771,0x7bdd},
{0x1772,0x1772,0x7be9},
{0x1773,0x1773,0x7c11},
{0x1774,0x1774,0x7c14},
{0x1775,0x1775,0x7be6},
{0x1776,0x1776,0x7be5},
{0x1777,0x1777,0x7c60},
{0x1778,0x1778,0x7c00},
{0x1779,0x1779,0x7c07},
{0x177a,0x177a,0x7c13},
{0x177b,0x177b,0x7bf3},
{0x177c,0x177c,0x7bf7},
{0x177d,0x177d,0x7c17},
{0x177e,0x177e,0x7c0d},
{0x177f,0x177f,0x7bf6},
{0x1780,0x1780,0x7c23},
{0x1781,0x1781,0x7c27},
{0x1782,0x1782,0x7c2a},
{0x1783,0x1783,0x7c1f},
{0x1784,0x1784,0x7c37},
{0x1785,0x1785,0x7c2b},
{0x1786,0x1786,0x7c3d},
{0x1787,0x1787,0x7c4c},
{0x1788,0x1788,0x7c43},
{0x1789,0x1789,0x7c54},
{0x178a,0x178a,0x7c4f},
{0x178b,0x178b,0x7c40},
{0x178c,0x178c,0x7c50},
{0x178d,0x178d,0x7c58},
{0x178e,0x178e,0x7c5f},
{0x178f,0x178f,0x7c64},
{0x1790,0x1790,0x7c56},
{0x1791,0x1791,0x7c65},
{0x1792,0x1792,0x7c6c},
{0x1793,0x1793,0x7c75},
{0x1794,0x1794,0x7c83},
{0x1795,0x1795,0x7c90},
{0x1796,0x1796,0x7ca4},
{0x1797,0x1797,0x7cad},
{0x1798,0x1798,0x7ca2},
{0x1799,0x1799,0x7cab},
{0x179a,0x179a,0x7ca1},
{0x179b,0x179b,0x7ca8},
{0x179c,0x179c,0x7cb3},
{0x179d,0x179d,0x7cb2},
{0x179e,0x179e,0x7cb1},
{0x179f,0x179f,0x7cae},
{0x17a0,0x17a0,0x7cb9},
{0x17a1,0x17a1,0x7cbd},
{0x17a2,0x17a2,0x7cc0},
{0x17a3,0x17a3,0x7cc5},
{0x17a4,0x17a4,0x7cc2},
{0x17a5,0x17a5,0x7cd8},
{0x17a6,0x17a6,0x7cd2},
{0x17a7,0x17a7,0x7cdc},
{0x17a8,0x17a8,0x7ce2},
{0x17a9,0x17a9,0x9b3b},
{0x17aa,0x17aa,0x7cef},
{0x17ab,0x17ab,0x7cf2},
{0x17ac,0x17ac,0x7cf4},
{0x17ad,0x17ad,0x7cf6},
{0x17ae,0x17ae,0x7cfa},
{0x17af,0x17af,0x7d06},
{0x17b0,0x17b0,0x7d02},
{0x17b1,0x17b1,0x7d1c},
{0x17b2,0x17b2,0x7d15},
{0x17b3,0x17b3,0x7d0a},
{0x17b4,0x17b4,0x7d45},
{0x17b5,0x17b5,0x7d4b},
{0x17b6,0x17b6,0x7d2e},
{0x17b7,0x17b7,0x7d32},
{0x17b8,0x17b8,0x7d3f},
{0x17b9,0x17b9,0x7d35},
{0x17ba,0x17ba,0x7d46},
{0x17bb,0x17bb,0x7d73},
{0x17bc,0x17bc,0x7d56},
{0x17bd,0x17bd,0x7d4e},
{0x17be,0x17be,0x7d72},
{0x17bf,0x17bf,0x7d68},
{0x17c0,0x17c0,0x7d6e},
{0x17c1,0x17c1,0x7d4f},
{0x17c2,0x17c2,0x7d63},
{0x17c3,0x17c3,0x7d93},
{0x17c4,0x17c4,0x7d89},
{0x17c5,0x17c5,0x7d5b},
{0x17c6,0x17c6,0x7d8f},
{0x17c7,0x17c7,0x7d7d},
{0x17c8,0x17c8,0x7d9b},
{0x17c9,0x17c9,0x7dba},
{0x17ca,0x17ca,0x7dae},
{0x17cb,0x17cb,0x7da3},
{0x17cc,0x17cc,0x7db5},
{0x17cd,0x17cd,0x7dc7},
{0x17ce,0x17ce,0x7dbd},
{0x17cf,0x17cf,0x7dab},
{0x17d0,0x17d0,0x7e3d},
{0x17d1,0x17d1,0x7da2},
{0x17d2,0x17d2,0x7daf},
{0x17d3,0x17d3,0x7ddc},
{0x17d4,0x17d4,0x7db8},
{0x17d5,0x17d5,0x7d9f},
{0x17d6,0x17d6,0x7db0},
{0x17d7,0x17d7,0x7dd8},
{0x17d8,0x17d8,0x7ddd},
{0x17d9,0x17d9,0x7de4},
{0x17da,0x17da,0x7dde},
{0x17db,0x17db,0x7dfb},
{0x17dc,0x17dc,0x7df2},
{0x17dd,0x17dd,0x7de1},
{0x17de,0x17de,0x7e05},
{0x17df,0x17df,0x7e0a},
{0x17e0,0x17e0,0x7e23},
{0x17e1,0x17e1,0x7e21},
{0x17e2,0x17e2,0x7e12},
{0x17e3,0x17e3,0x7e31},
{0x17e4,0x17e4,0x7e1f},
{0x17e5,0x17e5,0x7e09},
{0x17e6,0x17e6,0x7e0b},
{0x17e7,0x17e7,0x7e22},
{0x17e8,0x17e8,0x7e46},
{0x17e9,0x17e9,0x7e66},
{0x17ea,0x17ea,0x7e3b},
{0x17eb,0x17eb,0x7e35},
{0x17ec,0x17ec,0x7e39},
{0x17ed,0x17ed,0x7e43},
{0x17ee,0x17ee,0x7e37},
{0x17ef,0x17ef,0x7e32},
{0x17f0,0x17f0,0x7e3a},
{0x17f1,0x17f1,0x7e67},
{0x17f2,0x17f2,0x7e5d},
{0x17f3,0x17f3,0x7e56},
{0x17f4,0x17f4,0x7e5e},
{0x17f5,0x17f6,0x7e59},
{0x17f7,0x17f7,0x7e79},
{0x17f8,0x17f8,0x7e6a},
{0x17f9,0x17f9,0x7e69},
{0x17fa,0x17fa,0x7e7c},
{0x17fb,0x17fb,0x7e7b},
{0x17fc,0x17fc,0x7e83},
{0x17fd,0x17fd,0x7dd5},
{0x17fe,0x17fe,0x7e7d},
{0x17ff,0x17ff,0x8fae},
{0x1800,0x1800,0x7e7f},
{0x1801,0x1802,0x7e88},
{0x1803,0x1803,0x7e8c},
{0x1804,0x1804,0x7e92},
{0x1805,0x1805,0x7e90},
{0x1806,0x1807,0x7e93},
{0x1808,0x1808,0x7e96},
{0x1809,0x1809,0x7e8e},
{0x180a,0x180b,0x7e9b},
{0x180c,0x180c,0x7f38},
{0x180d,0x180d,0x7f3a},
{0x180e,0x180e,0x7f45},
{0x180f,0x1811,0x7f4c},
{0x1812,0x1813,0x7f50},
{0x1814,0x1814,0x7f55},
{0x1815,0x1815,0x7f54},
{0x1816,0x1816,0x7f58},
{0x1817,0x1818,0x7f5f},
{0x1819,0x181a,0x7f68},
{0x181b,0x181b,0x7f67},
{0x181c,0x181c,0x7f78},
{0x181d,0x181d,0x7f82},
{0x181e,0x181e,0x7f86},
{0x181f,0x181f,0x7f83},
{0x1820,0x1820,0x7f88},
{0x1821,0x1821,0x7f87},
{0x1822,0x1822,0x7f8c},
{0x1823,0x1823,0x7f94},
{0x1824,0x1824,0x7f9e},
{0x1825,0x1825,0x7f9d},
{0x1826,0x1826,0x7f9a},
{0x1827,0x1827,0x7fa3},
{0x1828,0x1828,0x7faf},
{0x1829,0x1829,0x7fb2},
{0x182a,0x182a,0x7fb9},
{0x182b,0x182b,0x7fae},
{0x182c,0x182c,0x7fb6},
{0x182d,0x182d,0x7fb8},
{0x182e,0x182e,0x8b71},
{0x182f,0x1830,0x7fc5},
{0x1831,0x1831,0x7fca},
{0x1832,0x1832,0x7fd5},
{0x1833,0x1833,0x7fd4},
{0x1834,0x1834,0x7fe1},
{0x1835,0x1835,0x7fe6},
{0x1836,0x1836,0x7fe9},
{0x1837,0x1837,0x7ff3},
{0x1838,0x1838,0x7ff9},
{0x1839,0x1839,0x98dc},
{0x183a,0x183a,0x8006},
{0x183b,0x183b,0x8004},
{0x183c,0x183c,0x800b},
{0x183d,0x183d,0x8012},
{0x183e,0x183f,0x8018},
{0x1840,0x1840,0x801c},
{0x1841,0x1841,0x8021},
{0x1842,0x1842,0x8028},
{0x1843,0x1843,0x803f},
{0x1844,0x1844,0x803b},
{0x1845,0x1845,0x804a},
{0x1846,0x1846,0x8046},
{0x1847,0x1847,0x8052},
{0x1848,0x1848,0x8058},
{0x1849,0x1849,0x805a},
{0x184a,0x184a,0x805f},
{0x184b,0x184b,0x8062},
{0x184c,0x184c,0x8068},
{0x184d,0x184d,0x8073},
{0x184e,0x184e,0x8072},
{0x184f,0x184f,0x8070},
{0x1850,0x1850,0x8076},
{0x1851,0x1851,0x8079},
{0x1852,0x1852,0x807d},
{0x1853,0x1853,0x807f},
{0x1854,0x1854,0x8084},
{0x1855,0x1855,0x8086},
{0x1856,0x1856,0x8085},
{0x1857,0x1857,0x809b},
{0x1858,0x1858,0x8093},
{0x1859,0x1859,0x809a},
{0x185a,0x185a,0x80ad},
{0x185b,0x185b,0x5190},
{0x185c,0x185c,0x80ac},
{0x185d,0x185d,0x80db},
{0x185e,0x185e,0x80e5},
{0x185f,0x185f,0x80d9},
{0x1860,0x1860,0x80dd},
{0x1861,0x1861,0x80c4},
{0x1862,0x1862,0x80da},
{0x1863,0x1863,0x80d6},
{0x1864,0x1864,0x8109},
{0x1865,0x1865,0x80ef},
{0x1866,0x1866,0x80f1},
{0x1867,0x1867,0x811b},
{0x1868,0x1868,0x8129},
{0x1869,0x1869,0x8123},
{0x186a,0x186a,0x812f},
{0x186b,0x186b,0x814b},
{0x186c,0x186c,0x968b},
{0x186d,0x186d,0x8146},
{0x186e,0x186e,0x813e},
{0x186f,0x186f,0x8153},
{0x1870,0x1870,0x8151},
{0x1871,0x1871,0x80fc},
{0x1872,0x1872,0x8171},
{0x1873,0x1873,0x816e},
{0x1874,0x1875,0x8165},
{0x1876,0x1876,0x8174},
{0x1877,0x1877,0x8183},
{0x1878,0x1878,0x8188},
{0x1879,0x1879,0x818a},
{0x187a,0x187a,0x8180},
{0x187b,0x187b,0x8182},
{0x187c,0x187c,0x81a0},
{0x187d,0x187d,0x8195},
{0x187e,0x187e,0x81a4},
{0x187f,0x187f,0x81a3},
{0x1880,0x1880,0x815f},
{0x1881,0x1881,0x8193},
{0x1882,0x1882,0x81a9},
{0x1883,0x1883,0x81b0},
{0x1884,0x1884,0x81b5},
{0x1885,0x1885,0x81be},
{0x1886,0x1886,0x81b8},
{0x1887,0x1887,0x81bd},
{0x1888,0x1888,0x81c0},
{0x1889,0x1889,0x81c2},
{0x188a,0x188a,0x81ba},
{0x188b,0x188b,0x81c9},
{0x188c,0x188c,0x81cd},
{0x188d,0x188d,0x81d1},
{0x188e,0x188e,0x81d9},
{0x188f,0x188f,0x81d8},
{0x1890,0x1890,0x81c8},
{0x1891,0x1891,0x81da},
{0x1892,0x1893,0x81df},
{0x1894,0x1894,0x81e7},
{0x1895,0x1896,0x81fa},
{0x1897,0x1897,0x81fe},
{0x1898,0x1899,0x8201},
{0x189a,0x189a,0x8205},
{0x189b,0x189b,0x8207},
{0x189c,0x189c,0x820a},
{0x189d,0x189d,0x820d},
{0x189e,0x189e,0x8210},
{0x189f,0x189f,0x8216},
{0x18a0,0x18a0,0x8229},
{0x18a1,0x18a1,0x822b},
{0x18a2,0x18a2,0x8238},
{0x18a3,0x18a3,0x8233},
{0x18a4,0x18a4,0x8240},
{0x18a5,0x18a5,0x8259},
{0x18a6,0x18a6,0x8258},
{0x18a7,0x18a7,0x825d},
{0x18a8,0x18a8,0x825a},
{0x18a9,0x18a9,0x825f},
{0x18aa,0x18aa,0x8264},
{0x18ab,0x18ab,0x8262},
{0x18ac,0x18ac,0x8268},
{0x18ad,0x18ae,0x826a},
{0x18af,0x18af,0x822e},
{0x18b0,0x18b0,0x8271},
{0x18b1,0x18b2,0x8277},
{0x18b3,0x18b3,0x827e},
{0x18b4,0x18b4,0x828d},
{0x18b5,0x18b5,0x8292},
{0x18b6,0x18b6,0x82ab},
{0x18b7,0x18b7,0x829f},
{0x18b8,0x18b8,0x82bb},
{0x18b9,0x18b9,0x82ac},
{0x18ba,0x18ba,0x82e1},
{0x18bb,0x18bb,0x82e3},
{0x18bc,0x18bc,0x82df},
{0x18bd,0x18bd,0x82d2},
{0x18be,0x18be,0x82f4},
{0x18bf,0x18bf,0x82f3},
{0x18c0,0x18c0,0x82fa},
{0x18c1,0x18c1,0x8393},
{0x18c2,0x18c2,0x8303},
{0x18c3,0x18c3,0x82fb},
{0x18c4,0x18c4,0x82f9},
{0x18c5,0x18c5,0x82de},
{0x18c6,0x18c6,0x8306},
{0x18c7,0x18c7,0x82dc},
{0x18c8,0x18c8,0x8309},
{0x18c9,0x18c9,0x82d9},
{0x18ca,0x18ca,0x8335},
{0x18cb,0x18cb,0x8334},
{0x18cc,0x18cc,0x8316},
{0x18cd,0x18cd,0x8332},
{0x18ce,0x18ce,0x8331},
{0x18cf,0x18cf,0x8340},
{0x18d0,0x18d0,0x8339},
{0x18d1,0x18d1,0x8350},
{0x18d2,0x18d2,0x8345},
{0x18d3,0x18d3,0x832f},
{0x18d4,0x18d4,0x832b},
{0x18d5,0x18d6,0x8317},
{0x18d7,0x18d7,0x8385},
{0x18d8,0x18d8,0x839a},
{0x18d9,0x18d9,0x83aa},
{0x18da,0x18da,0x839f},
{0x18db,0x18db,0x83a2},
{0x18dc,0x18dc,0x8396},
{0x18dd,0x18dd,0x8323},
{0x18de,0x18de,0x838e},
{0x18df,0x18df,0x8387},
{0x18e0,0x18e0,0x838a},
{0x18e1,0x18e1,0x837c},
{0x18e2,0x18e2,0x83b5},
{0x18e3,0x18e3,0x8373},
{0x18e4,0x18e4,0x8375},
{0x18e5,0x18e5,0x83a0},
{0x18e6,0x18e6,0x8389},
{0x18e7,0x18e7,0x83a8},
{0x18e8,0x18e8,0x83f4},
{0x18e9,0x18e9,0x8413},
{0x18ea,0x18ea,0x83eb},
{0x18eb,0x18eb,0x83ce},
{0x18ec,0x18ec,0x83fd},
{0x18ed,0x18ed,0x8403},
{0x18ee,0x18ee,0x83d8},
{0x18ef,0x18ef,0x840b},
{0x18f0,0x18f0,0x83c1},
{0x18f1,0x18f1,0x83f7},
{0x18f2,0x18f2,0x8407},
{0x18f3,0x18f3,0x83e0},
{0x18f4,0x18f4,0x83f2},
{0x18f5,0x18f5,0x840d},
{0x18f6,0x18f6,0x8422},
{0x18f7,0x18f7,0x8420},
{0x18f8,0x18f8,0x83bd},
{0x18f9,0x18f9,0x8438},
{0x18fa,0x18fa,0x8506},
{0x18fb,0x18fb,0x83fb},
{0x18fc,0x18fc,0x846d},
{0x18fd,0x18fd,0x842a},
{0x18fe,0x18fe,0x843c},
{0x18ff,0x18ff,0x855a},
{0x1900,0x1900,0x8484},
{0x1901,0x1901,0x8477},
{0x1902,0x1902,0x846b},
{0x1903,0x1903,0x84ad},
{0x1904,0x1904,0x846e},
{0x1905,0x1905,0x8482},
{0x1906,0x1906,0x8469},
{0x1907,0x1907,0x8446},
{0x1908,0x1908,0x842c},
{0x1909,0x1909,0x846f},
{0x190a,0x190a,0x8479},
{0x190b,0x190b,0x8435},
{0x190c,0x190c,0x84ca},
{0x190d,0x190d,0x8462},
{0x190e,0x190e,0x84b9},
{0x190f,0x190f,0x84bf},
{0x1910,0x1910,0x849f},
{0x1911,0x1911,0x84d9},
{0x1912,0x1912,0x84cd},
{0x1913,0x1913,0x84bb},
{0x1914,0x1914,0x84da},
{0x1915,0x1915,0x84d0},
{0x1916,0x1916,0x84c1},
{0x1917,0x1917,0x84c6},
{0x1918,0x1918,0x84d6},
{0x1919,0x1919,0x84a1},
{0x191a,0x191a,0x8521},
{0x191b,0x191b,0x84ff},
{0x191c,0x191c,0x84f4},
{0x191d,0x191e,0x8517},
{0x191f,0x191f,0x852c},
{0x1920,0x1920,0x851f},
{0x1921,0x1921,0x8515},
{0x1922,0x1922,0x8514},
{0x1923,0x1923,0x84fc},
{0x1924,0x1924,0x8540},
{0x1925,0x1925,0x8563},
{0x1926,0x1926,0x8558},
{0x1927,0x1927,0x8548},
{0x1928,0x1928,0x8541},
{0x1929,0x1929,0x8602},
{0x192a,0x192a,0x854b},
{0x192b,0x192b,0x8555},
{0x192c,0x192c,0x8580},
{0x192d,0x192d,0x85a4},
{0x192e,0x192e,0x8588},
{0x192f,0x192f,0x8591},
{0x1930,0x1930,0x858a},
{0x1931,0x1931,0x85a8},
{0x1932,0x1932,0x856d},
{0x1933,0x1933,0x8594},
{0x1934,0x1934,0x859b},
{0x1935,0x1935,0x85ea},
{0x1936,0x1936,0x8587},
{0x1937,0x1937,0x859c},
{0x1938,0x1938,0x8577},
{0x1939,0x1939,0x857e},
{0x193a,0x193a,0x8590},
{0x193b,0x193b,0x85c9},
{0x193c,0x193c,0x85ba},
{0x193d,0x193d,0x85cf},
{0x193e,0x193e,0x85b9},
{0x193f,0x193f,0x85d0},
{0x1940,0x1940,0x85d5},
{0x1941,0x1941,0x85dd},
{0x1942,0x1942,0x85e5},
{0x1943,0x1943,0x85dc},
{0x1944,0x1944,0x85f9},
{0x1945,0x1945,0x860a},
{0x1946,0x1946,0x8613},
{0x1947,0x1947,0x860b},
{0x1948,0x1948,0x85fe},
{0x1949,0x1949,0x85fa},
{0x194a,0x194a,0x8606},
{0x194b,0x194b,0x8622},
{0x194c,0x194c,0x861a},
{0x194d,0x194d,0x8630},
{0x194e,0x194e,0x863f},
{0x194f,0x194f,0x864d},
{0x1950,0x1950,0x4e55},
{0x1951,0x1951,0x8654},
{0x1952,0x1952,0x865f},
{0x1953,0x1953,0x8667},
{0x1954,0x1954,0x8671},
{0x1955,0x1955,0x8693},
{0x1956,0x1956,0x86a3},
{0x1957,0x1958,0x86a9},
{0x1959,0x195a,0x868b},
{0x195b,0x195b,0x86b6},
{0x195c,0x195c,0x86af},
{0x195d,0x195d,0x86c4},
{0x195e,0x195e,0x86c6},
{0x195f,0x195f,0x86b0},
{0x1960,0x1960,0x86c9},
{0x1961,0x1961,0x8823},
{0x1962,0x1962,0x86ab},
{0x1963,0x1963,0x86d4},
{0x1964,0x1964,0x86de},
{0x1965,0x1965,0x86e9},
{0x1966,0x1966,0x86ec},
{0x1967,0x1967,0x86df},
{0x1968,0x1968,0x86db},
{0x1969,0x1969,0x86ef},
{0x196a,0x196a,0x8712},
{0x196b,0x196b,0x8706},
{0x196c,0x196c,0x8708},
{0x196d,0x196d,0x8700},
{0x196e,0x196e,0x8703},
{0x196f,0x196f,0x86fb},
{0x1970,0x1970,0x8711},
{0x1971,0x1971,0x8709},
{0x1972,0x1972,0x870d},
{0x1973,0x1973,0x86f9},
{0x1974,0x1974,0x870a},
{0x1975,0x1975,0x8734},
{0x1976,0x1976,0x873f},
{0x1977,0x1977,0x8737},
{0x1978,0x1978,0x873b},
{0x1979,0x1979,0x8725},
{0x197a,0x197a,0x8729},
{0x197b,0x197b,0x871a},
{0x197c,0x197c,0x8760},
{0x197d,0x197d,0x875f},
{0x197e,0x197e,0x8778},
{0x197f,0x197f,0x874c},
{0x1980,0x1980,0x874e},
{0x1981,0x1981,0x8774},
{0x1982,0x1982,0x8757},
{0x1983,0x1983,0x8768},
{0x1984,0x1984,0x876e},
{0x1985,0x1985,0x8759},
{0x1986,0x1986,0x8753},
{0x1987,0x1987,0x8763},
{0x1988,0x1988,0x876a},
{0x1989,0x1989,0x8805},
{0x198a,0x198a,0x87a2},
{0x198b,0x198b,0x879f},
{0x198c,0x198c,0x8782},
{0x198d,0x198d,0x87af},
{0x198e,0x198e,0x87cb},
{0x198f,0x198f,0x87bd},
{0x1990,0x1990,0x87c0},
{0x1991,0x1991,0x87d0},
{0x1992,0x1992,0x96d6},
{0x1993,0x1993,0x87ab},
{0x1994,0x1994,0x87c4},
{0x1995,0x1995,0x87b3},
{0x1996,0x1996,0x87c7},
{0x1997,0x1997,0x87c6},
{0x1998,0x1998,0x87bb},
{0x1999,0x1999,0x87ef},
{0x199a,0x199a,0x87f2},
{0x199b,0x199b,0x87e0},
{0x199c,0x199c,0x880f},
{0x199d,0x199d,0x880d},
{0x199e,0x199e,0x87fe},
{0x199f,0x19a0,0x87f6},
{0x19a1,0x19a1,0x880e},
{0x19a2,0x19a2,0x87d2},
{0x19a3,0x19a3,0x8811},
{0x19a4,0x19a4,0x8816},
{0x19a5,0x19a5,0x8815},
{0x19a6,0x19a6,0x8822},
{0x19a7,0x19a7,0x8821},
{0x19a8,0x19a8,0x8831},
{0x19a9,0x19a9,0x8836},
{0x19aa,0x19aa,0x8839},
{0x19ab,0x19ab,0x8827},
{0x19ac,0x19ac,0x883b},
{0x19ad,0x19ad,0x8844},
{0x19ae,0x19ae,0x8842},
{0x19af,0x19af,0x8852},
{0x19b0,0x19b0,0x8859},
{0x19b1,0x19b1,0x885e},
{0x19b2,0x19b2,0x8862},
{0x19b3,0x19b3,0x886b},
{0x19b4,0x19b4,0x8881},
{0x19b5,0x19b5,0x887e},
{0x19b6,0x19b6,0x889e},
{0x19b7,0x19b7,0x8875},
{0x19b8,0x19b8,0x887d},
{0x19b9,0x19b9,0x88b5},
{0x19ba,0x19ba,0x8872},
{0x19bb,0x19bb,0x8882},
{0x19bc,0x19bc,0x8897},
{0x19bd,0x19bd,0x8892},
{0x19be,0x19be,0x88ae},
{0x19bf,0x19bf,0x8899},
{0x19c0,0x19c0,0x88a2},
{0x19c1,0x19c1,0x888d},
{0x19c2,0x19c2,0x88a4},
{0x19c3,0x19c3,0x88b0},
{0x19c4,0x19c4,0x88bf},
{0x19c5,0x19c5,0x88b1},
{0x19c6,0x19c7,0x88c3},
{0x19c8,0x19c8,0x88d4},
{0x19c9,0x19ca,0x88d8},
{0x19cb,0x19cb,0x88dd},
{0x19cc,0x19cc,0x88f9},
{0x19cd,0x19cd,0x8902},
{0x19ce,0x19ce,0x88fc},
{0x19cf,0x19cf,0x88f4},
{0x19d0,0x19d0,0x88e8},
{0x19d1,0x19d1,0x88f2},
{0x19d2,0x19d2,0x8904},
{0x19d3,0x19d3,0x890c},
{0x19d4,0x19d4,0x890a},
{0x19d5,0x19d5,0x8913},
{0x19d6,0x19d6,0x8943},
{0x19d7,0x19d7,0x891e},
{0x19d8,0x19d8,0x8925},
{0x19d9,0x19da,0x892a},
{0x19db,0x19db,0x8941},
{0x19dc,0x19dc,0x8944},
{0x19dd,0x19dd,0x893b},
{0x19de,0x19de,0x8936},
{0x19df,0x19df,0x8938},
{0x19e0,0x19e0,0x894c},
{0x19e1,0x19e1,0x891d},
{0x19e2,0x19e2,0x8960},
{0x19e3,0x19e3,0x895e},
{0x19e4,0x19e4,0x8966},
{0x19e5,0x19e5,0x8964},
{0x19e6,0x19e6,0x896d},
{0x19e7,0x19e7,0x896a},
{0x19e8,0x19e8,0x896f},
{0x19e9,0x19e9,0x8974},
{0x19ea,0x19ea,0x8977},
{0x19eb,0x19eb,0x897e},
{0x19ec,0x19ec,0x8983},
{0x19ed,0x19ed,0x8988},
{0x19ee,0x19ee,0x898a},
{0x19ef,0x19ef,0x8993},
{0x19f0,0x19f0,0x8998},
{0x19f1,0x19f1,0x89a1},
{0x19f2,0x19f2,0x89a9},
{0x19f3,0x19f3,0x89a6},
{0x19f4,0x19f4,0x89ac},
{0x19f5,0x19f5,0x89af},
{0x19f6,0x19f6,0x89b2},
{0x19f7,0x19f7,0x89ba},
{0x19f8,0x19f8,0x89bd},
{0x19f9,0x19fa,0x89bf},
{0x19fb,0x19fb,0x89da},
{0x19fc,0x19fd,0x89dc},
{0x19fe,0x19fe,0x89e7},
{0x19ff,0x19ff,0x89f4},
{0x1a00,0x1a00,0x89f8},
{0x1a01,0x1a01,0x8a03},
{0x1a02,0x1a02,0x8a16},
{0x1a03,0x1a03,0x8a10},
{0x1a04,0x1a04,0x8a0c},
{0x1a05,0x1a05,0x8a1b},
{0x1a06,0x1a06,0x8a1d},
{0x1a07,0x1a07,0x8a25},
{0x1a08,0x1a08,0x8a36},
{0x1a09,0x1a09,0x8a41},
{0x1a0a,0x1a0a,0x8a5b},
{0x1a0b,0x1a0b,0x8a52},
{0x1a0c,0x1a0c,0x8a46},
{0x1a0d,0x1a0d,0x8a48},
{0x1a0e,0x1a0e,0x8a7c},
{0x1a0f,0x1a0f,0x8a6d},
{0x1a10,0x1a10,0x8a6c},
{0x1a11,0x1a11,0x8a62},
{0x1a12,0x1a12,0x8a85},
{0x1a13,0x1a13,0x8a82},
{0x1a14,0x1a14,0x8a84},
{0x1a15,0x1a15,0x8aa8},
{0x1a16,0x1a16,0x8aa1},
{0x1a17,0x1a17,0x8a91},
{0x1a18,0x1a19,0x8aa5},
{0x1a1a,0x1a1a,0x8a9a},
{0x1a1b,0x1a1b,0x8aa3},
{0x1a1c,0x1a1c,0x8ac4},
{0x1a1d,0x1a1d,0x8acd},
{0x1a1e,0x1a1e,0x8ac2},
{0x1a1f,0x1a1f,0x8ada},
{0x1a20,0x1a20,0x8aeb},
{0x1a21,0x1a21,0x8af3},
{0x1a22,0x1a22,0x8ae7},
{0x1a23,0x1a23,0x8ae4},
{0x1a24,0x1a24,0x8af1},
{0x1a25,0x1a25,0x8b14},
{0x1a26,0x1a26,0x8ae0},
{0x1a27,0x1a27,0x8ae2},
{0x1a28,0x1a28,0x8af7},
{0x1a29,0x1a29,0x8ade},
{0x1a2a,0x1a2a,0x8adb},
{0x1a2b,0x1a2b,0x8b0c},
{0x1a2c,0x1a2c,0x8b07},
{0x1a2d,0x1a2d,0x8b1a},
{0x1a2e,0x1a2e,0x8ae1},
{0x1a2f,0x1a2f,0x8b16},
{0x1a30,0x1a30,0x8b10},
{0x1a31,0x1a31,0x8b17},
{0x1a32,0x1a32,0x8b20},
{0x1a33,0x1a33,0x8b33},
{0x1a34,0x1a34,0x97ab},
{0x1a35,0x1a35,0x8b26},
{0x1a36,0x1a36,0x8b2b},
{0x1a37,0x1a37,0x8b3e},
{0x1a38,0x1a38,0x8b28},
{0x1a39,0x1a39,0x8b41},
{0x1a3a,0x1a3a,0x8b4c},
{0x1a3b,0x1a3b,0x8b4f},
{0x1a3c,0x1a3c,0x8b4e},
{0x1a3d,0x1a3d,0x8b49},
{0x1a3e,0x1a3e,0x8b56},
{0x1a3f,0x1a3f,0x8b5b},
{0x1a40,0x1a40,0x8b5a},
{0x1a41,0x1a41,0x8b6b},
{0x1a42,0x1a42,0x8b5f},
{0x1a43,0x1a43,0x8b6c},
{0x1a44,0x1a44,0x8b6f},
{0x1a45,0x1a45,0x8b74},
{0x1a46,0x1a46,0x8b7d},
{0x1a47,0x1a47,0x8b80},
{0x1a48,0x1a48,0x8b8c},
{0x1a49,0x1a49,0x8b8e},
{0x1a4a,0x1a4b,0x8b92},
{0x1a4c,0x1a4c,0x8b96},
{0x1a4d,0x1a4e,0x8b99},
{0x1a4f,0x1a4f,0x8c3a},
{0x1a50,0x1a50,0x8c41},
{0x1a51,0x1a51,0x8c3f},
{0x1a52,0x1a52,0x8c48},
{0x1a53,0x1a53,0x8c4c},
{0x1a54,0x1a54,0x8c4e},
{0x1a55,0x1a55,0x8c50},
{0x1a56,0x1a56,0x8c55},
{0x1a57,0x1a57,0x8c62},
{0x1a58,0x1a58,0x8c6c},
{0x1a59,0x1a59,0x8c78},
{0x1a5a,0x1a5a,0x8c7a},
{0x1a5b,0x1a5b,0x8c82},
{0x1a5c,0x1a5c,0x8c89},
{0x1a5d,0x1a5d,0x8c85},
{0x1a5e,0x1a5e,0x8c8a},
{0x1a5f,0x1a60,0x8c8d},
{0x1a61,0x1a61,0x8c94},
{0x1a62,0x1a62,0x8c7c},
{0x1a63,0x1a63,0x8c98},
{0x1a64,0x1a64,0x621d},
{0x1a65,0x1a65,0x8cad},
{0x1a66,0x1a66,0x8caa},
{0x1a67,0x1a67,0x8cbd},
{0x1a68,0x1a69,0x8cb2},
{0x1a6a,0x1a6a,0x8cae},
{0x1a6b,0x1a6b,0x8cb6},
{0x1a6c,0x1a6c,0x8cc8},
{0x1a6d,0x1a6d,0x8cc1},
{0x1a6e,0x1a6e,0x8ce4},
{0x1a6f,0x1a6f,0x8ce3},
{0x1a70,0x1a70,0x8cda},
{0x1a71,0x1a71,0x8cfd},
{0x1a72,0x1a73,0x8cfa},
{0x1a74,0x1a75,0x8d04},
{0x1a76,0x1a76,0x8d0a},
{0x1a77,0x1a77,0x8d07},
{0x1a78,0x1a78,0x8d0f},
{0x1a79,0x1a79,0x8d0d},
{0x1a7a,0x1a7a,0x8d10},
{0x1a7b,0x1a7b,0x9f4e},
{0x1a7c,0x1a7c,0x8d13},
{0x1a7d,0x1a7d,0x8ccd},
{0x1a7e,0x1a7e,0x8d14},
{0x1a7f,0x1a7f,0x8d16},
{0x1a80,0x1a80,0x8d67},
{0x1a81,0x1a81,0x8d6d},
{0x1a82,0x1a82,0x8d71},
{0x1a83,0x1a83,0x8d73},
{0x1a84,0x1a84,0x8d81},
{0x1a85,0x1a85,0x8d99},
{0x1a86,0x1a86,0x8dc2},
{0x1a87,0x1a87,0x8dbe},
{0x1a88,0x1a88,0x8dba},
{0x1a89,0x1a89,0x8dcf},
{0x1a8a,0x1a8a,0x8dda},
{0x1a8b,0x1a8b,0x8dd6},
{0x1a8c,0x1a8c,0x8dcc},
{0x1a8d,0x1a8d,0x8ddb},
{0x1a8e,0x1a8e,0x8dcb},
{0x1a8f,0x1a90,0x8dea},
{0x1a91,0x1a91,0x8ddf},
{0x1a92,0x1a92,0x8de3},
{0x1a93,0x1a93,0x8dfc},
{0x1a94,0x1a95,0x8e08},
{0x1a96,0x1a96,0x8dff},
{0x1a97,0x1a98,0x8e1d},
{0x1a99,0x1a99,0x8e10},
{0x1a9a,0x1a9a,0x8e1f},
{0x1a9b,0x1a9b,0x8e42},
{0x1a9c,0x1a9c,0x8e35},
{0x1a9d,0x1a9d,0x8e30},
{0x1a9e,0x1a9e,0x8e34},
{0x1a9f,0x1a9f,0x8e4a},
{0x1aa0,0x1aa0,0x8e47},
{0x1aa1,0x1aa1,0x8e49},
{0x1aa2,0x1aa2,0x8e4c},
{0x1aa3,0x1aa3,0x8e50},
{0x1aa4,0x1aa4,0x8e48},
{0x1aa5,0x1aa5,0x8e59},
{0x1aa6,0x1aa6,0x8e64},
{0x1aa7,0x1aa7,0x8e60},
{0x1aa8,0x1aa8,0x8e2a},
{0x1aa9,0x1aa9,0x8e63},
{0x1aaa,0x1aaa,0x8e55},
{0x1aab,0x1aab,0x8e76},
{0x1aac,0x1aac,0x8e72},
{0x1aad,0x1aad,0x8e7c},
{0x1aae,0x1aae,0x8e81},
{0x1aaf,0x1aaf,0x8e87},
{0x1ab0,0x1ab0,0x8e85},
{0x1ab1,0x1ab1,0x8e84},
{0x1ab2,0x1ab2,0x8e8b},
{0x1ab3,0x1ab3,0x8e8a},
{0x1ab4,0x1ab4,0x8e93},
{0x1ab5,0x1ab5,0x8e91},
{0x1ab6,0x1ab6,0x8e94},
{0x1ab7,0x1ab7,0x8e99},
{0x1ab8,0x1ab8,0x8eaa},
{0x1ab9,0x1ab9,0x8ea1},
{0x1aba,0x1aba,0x8eac},
{0x1abb,0x1abb,0x8eb0},
{0x1abc,0x1abc,0x8ec6},
{0x1abd,0x1abd,0x8eb1},
{0x1abe,0x1abe,0x8ebe},
{0x1abf,0x1abf,0x8ec5},
{0x1ac0,0x1ac0,0x8ec8},
{0x1ac1,0x1ac1,0x8ecb},
{0x1ac2,0x1ac2,0x8edb},
{0x1ac3,0x1ac3,0x8ee3},
{0x1ac4,0x1ac4,0x8efc},
{0x1ac5,0x1ac5,0x8efb},
{0x1ac6,0x1ac6,0x8eeb},
{0x1ac7,0x1ac7,0x8efe},
{0x1ac8,0x1ac8,0x8f0a},
{0x1ac9,0x1ac9,0x8f05},
{0x1aca,0x1aca,0x8f15},
{0x1acb,0x1acb,0x8f12},
{0x1acc,0x1acc,0x8f19},
{0x1acd,0x1acd,0x8f13},
{0x1ace,0x1ace,0x8f1c},
{0x1acf,0x1acf,0x8f1f},
{0x1ad0,0x1ad0,0x8f1b},
{0x1ad1,0x1ad1,0x8f0c},
{0x1ad2,0x1ad2,0x8f26},
{0x1ad3,0x1ad3,0x8f33},
{0x1ad4,0x1ad4,0x8f3b},
{0x1ad5,0x1ad5,0x8f39},
{0x1ad6,0x1ad6,0x8f45},
{0x1ad7,0x1ad7,0x8f42},
{0x1ad8,0x1ad8,0x8f3e},
{0x1ad9,0x1ad9,0x8f4c},
{0x1ada,0x1ada,0x8f49},
{0x1adb,0x1adb,0x8f46},
{0x1adc,0x1adc,0x8f4e},
{0x1add,0x1add,0x8f57},
{0x1ade,0x1ade,0x8f5c},
{0x1adf,0x1ae1,0x8f62},
{0x1ae2,0x1ae2,0x8f9c},
{0x1ae3,0x1ae3,0x8f9f},
{0x1ae4,0x1ae4,0x8fa3},
{0x1ae5,0x1ae5,0x8fad},
{0x1ae6,0x1ae6,0x8faf},
{0x1ae7,0x1ae7,0x8fb7},
{0x1ae8,0x1ae8,0x8fda},
{0x1ae9,0x1ae9,0x8fe5},
{0x1aea,0x1aea,0x8fe2},
{0x1aeb,0x1aeb,0x8fea},
{0x1aec,0x1aec,0x8fef},
{0x1aed,0x1aed,0x9087},
{0x1aee,0x1aee,0x8ff4},
{0x1aef,0x1aef,0x9005},
{0x1af0,0x1af1,0x8ff9},
{0x1af2,0x1af2,0x9011},
{0x1af3,0x1af3,0x9015},
{0x1af4,0x1af4,0x9021},
{0x1af5,0x1af5,0x900d},
{0x1af6,0x1af6,0x901e},
{0x1af7,0x1af7,0x9016},
{0x1af8,0x1af8,0x900b},
{0x1af9,0x1af9,0x9027},
{0x1afa,0x1afa,0x9036},
{0x1afb,0x1afb,0x9035},
{0x1afc,0x1afc,0x9039},
{0x1afd,0x1afd,0x8ff8},
{0x1afe,0x1b01,0x904f},
{0x1b02,0x1b02,0x900e},
{0x1b03,0x1b03,0x9049},
{0x1b04,0x1b04,0x903e},
{0x1b05,0x1b05,0x9056},
{0x1b06,0x1b06,0x9058},
{0x1b07,0x1b07,0x905e},
{0x1b08,0x1b08,0x9068},
{0x1b09,0x1b09,0x906f},
{0x1b0a,0x1b0a,0x9076},
{0x1b0b,0x1b0b,0x96a8},
{0x1b0c,0x1b0c,0x9072},
{0x1b0d,0x1b0d,0x9082},
{0x1b0e,0x1b0e,0x907d},
{0x1b0f,0x1b0f,0x9081},
{0x1b10,0x1b10,0x9080},
{0x1b11,0x1b11,0x908a},
{0x1b12,0x1b12,0x9089},
{0x1b13,0x1b13,0x908f},
{0x1b14,0x1b14,0x90a8},
{0x1b15,0x1b15,0x90af},
{0x1b16,0x1b16,0x90b1},
{0x1b17,0x1b17,0x90b5},
{0x1b18,0x1b18,0x90e2},
{0x1b19,0x1b19,0x90e4},
{0x1b1a,0x1b1a,0x6248},
{0x1b1b,0x1b1b,0x90db},
{0x1b1c,0x1b1c,0x9102},
{0x1b1d,0x1b1d,0x9112},
{0x1b1e,0x1b1e,0x9119},
{0x1b1f,0x1b1f,0x9132},
{0x1b20,0x1b20,0x9130},
{0x1b21,0x1b21,0x914a},
{0x1b22,0x1b22,0x9156},
{0x1b23,0x1b23,0x9158},
{0x1b24,0x1b24,0x9163},
{0x1b25,0x1b25,0x9165},
{0x1b26,0x1b26,0x9169},
{0x1b27,0x1b27,0x9173},
{0x1b28,0x1b28,0x9172},
{0x1b29,0x1b29,0x918b},
{0x1b2a,0x1b2a,0x9189},
{0x1b2b,0x1b2b,0x9182},
{0x1b2c,0x1b2c,0x91a2},
{0x1b2d,0x1b2d,0x91ab},
{0x1b2e,0x1b2e,0x91af},
{0x1b2f,0x1b2f,0x91aa},
{0x1b30,0x1b30,0x91b5},
{0x1b31,0x1b31,0x91b4},
{0x1b32,0x1b32,0x91ba},
{0x1b33,0x1b34,0x91c0},
{0x1b35,0x1b35,0x91c9},
{0x1b36,0x1b36,0x91cb},
{0x1b37,0x1b37,0x91d0},
{0x1b38,0x1b38,0x91d6},
{0x1b39,0x1b39,0x91df},
{0x1b3a,0x1b3a,0x91e1},
{0x1b3b,0x1b3b,0x91db},
{0x1b3c,0x1b3c,0x91fc},
{0x1b3d,0x1b3e,0x91f5},
{0x1b3f,0x1b3f,0x921e},
{0x1b40,0x1b40,0x91ff},
{0x1b41,0x1b41,0x9214},
{0x1b42,0x1b42,0x922c},
{0x1b43,0x1b43,0x9215},
{0x1b44,0x1b44,0x9211},
{0x1b45,0x1b45,0x925e},
{0x1b46,0x1b46,0x9257},
{0x1b47,0x1b47,0x9245},
{0x1b48,0x1b48,0x9249},
{0x1b49,0x1b49,0x9264},
{0x1b4a,0x1b4a,0x9248},
{0x1b4b,0x1b4b,0x9295},
{0x1b4c,0x1b4c,0x923f},
{0x1b4d,0x1b4d,0x924b},
{0x1b4e,0x1b4e,0x9250},
{0x1b4f,0x1b4f,0x929c},
{0x1b50,0x1b50,0x9296},
{0x1b51,0x1b51,0x9293},
{0x1b52,0x1b52,0x929b},
{0x1b53,0x1b53,0x925a},
{0x1b54,0x1b54,0x92cf},
{0x1b55,0x1b55,0x92b9},
{0x1b56,0x1b56,0x92b7},
{0x1b57,0x1b57,0x92e9},
{0x1b58,0x1b58,0x930f},
{0x1b59,0x1b59,0x92fa},
{0x1b5a,0x1b5a,0x9344},
{0x1b5b,0x1b5b,0x932e},
{0x1b5c,0x1b5c,0x9319},
{0x1b5d,0x1b5d,0x9322},
{0x1b5e,0x1b5e,0x931a},
{0x1b5f,0x1b5f,0x9323},
{0x1b60,0x1b60,0x933a},
{0x1b61,0x1b61,0x9335},
{0x1b62,0x1b62,0x933b},
{0x1b63,0x1b63,0x935c},
{0x1b64,0x1b64,0x9360},
{0x1b65,0x1b65,0x937c},
{0x1b66,0x1b66,0x936e},
{0x1b67,0x1b67,0x9356},
{0x1b68,0x1b68,0x93b0},
{0x1b69,0x1b6a,0x93ac},
{0x1b6b,0x1b6b,0x9394},
{0x1b6c,0x1b6c,0x93b9},
{0x1b6d,0x1b6e,0x93d6},
{0x1b6f,0x1b6f,0x93e8},
{0x1b70,0x1b70,0x93e5},
{0x1b71,0x1b71,0x93d8},
{0x1b72,0x1b72,0x93c3},
{0x1b73,0x1b73,0x93dd},
{0x1b74,0x1b74,0x93d0},
{0x1b75,0x1b75,0x93c8},
{0x1b76,0x1b76,0x93e4},
{0x1b77,0x1b77,0x941a},
{0x1b78,0x1b78,0x9414},
{0x1b79,0x1b79,0x9413},
{0x1b7a,0x1b7a,0x9403},
{0x1b7b,0x1b7b,0x9407},
{0x1b7c,0x1b7c,0x9410},
{0x1b7d,0x1b7d,0x9436},
{0x1b7e,0x1b7e,0x942b},
{0x1b7f,0x1b7f,0x9435},
{0x1b80,0x1b80,0x9421},
{0x1b81,0x1b81,0x943a},
{0x1b82,0x1b82,0x9441},
{0x1b83,0x1b83,0x9452},
{0x1b84,0x1b84,0x9444},
{0x1b85,0x1b85,0x945b},
{0x1b86,0x1b86,0x9460},
{0x1b87,0x1b87,0x9462},
{0x1b88,0x1b88,0x945e},
{0x1b89,0x1b89,0x946a},
{0x1b8a,0x1b8a,0x9229},
{0x1b8b,0x1b8b,0x9470},
{0x1b8c,0x1b8c,0x9475},
{0x1b8d,0x1b8d,0x9477},
{0x1b8e,0x1b8e,0x947d},
{0x1b8f,0x1b8f,0x945a},
{0x1b90,0x1b90,0x947c},
{0x1b91,0x1b91,0x947e},
{0x1b92,0x1b92,0x9481},
{0x1b93,0x1b93,0x947f},
{0x1b94,0x1b94,0x9582},
{0x1b95,0x1b95,0x9587},
{0x1b96,0x1b96,0x958a},
{0x1b97,0x1b97,0x9594},
{0x1b98,0x1b98,0x9596},
{0x1b99,0x1b9a,0x9598},
{0x1b9b,0x1b9b,0x95a0},
{0x1b9c,0x1b9c,0x95a8},
{0x1b9d,0x1b9d,0x95a7},
{0x1b9e,0x1b9e,0x95ad},
{0x1b9f,0x1b9f,0x95bc},
{0x1ba0,0x1ba0,0x95bb},
{0x1ba1,0x1ba1,0x95b9},
{0x1ba2,0x1ba2,0x95be},
{0x1ba3,0x1ba3,0x95ca},
{0x1ba4,0x1ba4,0x6ff6},
{0x1ba5,0x1ba5,0x95c3},
{0x1ba6,0x1ba6,0x95cd},
{0x1ba7,0x1ba7,0x95cc},
{0x1ba8,0x1ba8,0x95d5},
{0x1ba9,0x1ba9,0x95d4},
{0x1baa,0x1baa,0x95d6},
{0x1bab,0x1bab,0x95dc},
{0x1bac,0x1bac,0x95e1},
{0x1bad,0x1bad,0x95e5},
{0x1bae,0x1bae,0x95e2},
{0x1baf,0x1baf,0x9621},
{0x1bb0,0x1bb0,0x9628},
{0x1bb1,0x1bb2,0x962e},
{0x1bb3,0x1bb3,0x9642},
{0x1bb4,0x1bb4,0x964c},
{0x1bb5,0x1bb5,0x964f},
{0x1bb6,0x1bb6,0x964b},
{0x1bb7,0x1bb7,0x9677},
{0x1bb8,0x1bb8,0x965c},
{0x1bb9,0x1bb9,0x965e},
{0x1bba,0x1bba,0x965d},
{0x1bbb,0x1bbb,0x965f},
{0x1bbc,0x1bbc,0x9666},
{0x1bbd,0x1bbd,0x9672},
{0x1bbe,0x1bbe,0x966c},
{0x1bbf,0x1bbf,0x968d},
{0x1bc0,0x1bc0,0x9698},
{0x1bc1,0x1bc1,0x9695},
{0x1bc2,0x1bc2,0x9697},
{0x1bc3,0x1bc3,0x96aa},
{0x1bc4,0x1bc4,0x96a7},
{0x1bc5,0x1bc6,0x96b1},
{0x1bc7,0x1bc7,0x96b0},
{0x1bc8,0x1bc8,0x96b4},
{0x1bc9,0x1bc9,0x96b6},
{0x1bca,0x1bcb,0x96b8},
{0x1bcc,0x1bcc,0x96ce},
{0x1bcd,0x1bcd,0x96cb},
{0x1bce,0x1bce,0x96c9},
{0x1bcf,0x1bcf,0x96cd},
{0x1bd0,0x1bd0,0x894d},
{0x1bd1,0x1bd1,0x96dc},
{0x1bd2,0x1bd2,0x970d},
{0x1bd3,0x1bd3,0x96d5},
{0x1bd4,0x1bd4,0x96f9},
{0x1bd5,0x1bd5,0x9704},
{0x1bd6,0x1bd6,0x9706},
{0x1bd7,0x1bd7,0x9708},
{0x1bd8,0x1bd8,0x9713},
{0x1bd9,0x1bd9,0x970e},
{0x1bda,0x1bda,0x9711},
{0x1bdb,0x1bdb,0x970f},
{0x1bdc,0x1bdc,0x9716},
{0x1bdd,0x1bdd,0x9719},
{0x1bde,0x1bde,0x9724},
{0x1bdf,0x1bdf,0x972a},
{0x1be0,0x1be0,0x9730},
{0x1be1,0x1be1,0x9739},
{0x1be2,0x1be3,0x973d},
{0x1be4,0x1be4,0x9744},
{0x1be5,0x1be5,0x9746},
{0x1be6,0x1be6,0x9748},
{0x1be7,0x1be7,0x9742},
{0x1be8,0x1be8,0x9749},
{0x1be9,0x1be9,0x975c},
{0x1bea,0x1bea,0x9760},
{0x1beb,0x1beb,0x9764},
{0x1bec,0x1bec,0x9766},
{0x1bed,0x1bed,0x9768},
{0x1bee,0x1bee,0x52d2},
{0x1bef,0x1bef,0x976b},
{0x1bf0,0x1bf0,0x9771},
{0x1bf1,0x1bf1,0x9779},
{0x1bf2,0x1bf2,0x9785},
{0x1bf3,0x1bf3,0x977c},
{0x1bf4,0x1bf4,0x9781},
{0x1bf5,0x1bf5,0x977a},
{0x1bf6,0x1bf6,0x9786},
{0x1bf7,0x1bf7,0x978b},
{0x1bf8,0x1bf9,0x978f},
{0x1bfa,0x1bfa,0x979c},
{0x1bfb,0x1bfb,0x97a8},
{0x1bfc,0x1bfc,0x97a6},
{0x1bfd,0x1bfd,0x97a3},
{0x1bfe,0x1bff,0x97b3},
{0x1c00,0x1c00,0x97c3},
{0x1c01,0x1c01,0x97c6},
{0x1c02,0x1c02,0x97c8},
{0x1c03,0x1c03,0x97cb},
{0x1c04,0x1c04,0x97dc},
{0x1c05,0x1c05,0x97ed},
{0x1c06,0x1c06,0x9f4f},
{0x1c07,0x1c07,0x97f2},
{0x1c08,0x1c08,0x7adf},
{0x1c09,0x1c09,0x97f6},
{0x1c0a,0x1c0a,0x97f5},
{0x1c0b,0x1c0b,0x980f},
{0x1c0c,0x1c0c,0x980c},
{0x1c0d,0x1c0d,0x9838},
{0x1c0e,0x1c0e,0x9824},
{0x1c0f,0x1c0f,0x9821},
{0x1c10,0x1c10,0x9837},
{0x1c11,0x1c11,0x983d},
{0x1c12,0x1c12,0x9846},
{0x1c13,0x1c13,0x984f},
{0x1c14,0x1c14,0x984b},
{0x1c15,0x1c15,0x986b},
{0x1c16,0x1c18,0x986f},
{0x1c19,0x1c19,0x9874},
{0x1c1a,0x1c1a,0x9873},
{0x1c1b,0x1c1b,0x98aa},
{0x1c1c,0x1c1c,0x98af},
{0x1c1d,0x1c1d,0x98b1},
{0x1c1e,0x1c1e,0x98b6},
{0x1c1f,0x1c1f,0x98c4},
{0x1c20,0x1c20,0x98c3},
{0x1c21,0x1c21,0x98c6},
{0x1c22,0x1c22,0x98e9},
{0x1c23,0x1c23,0x98eb},
{0x1c24,0x1c24,0x9903},
{0x1c25,0x1c25,0x9909},
{0x1c26,0x1c26,0x9912},
{0x1c27,0x1c27,0x9914},
{0x1c28,0x1c28,0x9918},
{0x1c29,0x1c29,0x9921},
{0x1c2a,0x1c2b,0x991d},
{0x1c2c,0x1c2c,0x9924},
{0x1c2d,0x1c2d,0x9920},
{0x1c2e,0x1c2e,0x992c},
{0x1c2f,0x1c2f,0x992e},
{0x1c30,0x1c31,0x993d},
{0x1c32,0x1c32,0x9942},
{0x1c33,0x1c33,0x9949},
{0x1c34,0x1c34,0x9945},
{0x1c35,0x1c35,0x9950},
{0x1c36,0x1c36,0x994b},
{0x1c37,0x1c38,0x9951},
{0x1c39,0x1c39,0x994c},
{0x1c3a,0x1c3a,0x9955},
{0x1c3b,0x1c3c,0x9997},
{0x1c3d,0x1c3d,0x99a5},
{0x1c3e,0x1c3f,0x99ad},
{0x1c40,0x1c40,0x99bc},
{0x1c41,0x1c41,0x99df},
{0x1c42,0x1c42,0x99db},
{0x1c43,0x1c43,0x99dd},
{0x1c44,0x1c44,0x99d8},
{0x1c45,0x1c45,0x99d1},
{0x1c46,0x1c47,0x99ed},
{0x1c48,0x1c49,0x99f1},
{0x1c4a,0x1c4a,0x99fb},
{0x1c4b,0x1c4b,0x99f8},
{0x1c4c,0x1c4c,0x9a01},
{0x1c4d,0x1c4d,0x9a0f},
{0x1c4e,0x1c4e,0x9a05},
{0x1c4f,0x1c4f,0x99e2},
{0x1c50,0x1c50,0x9a19},
{0x1c51,0x1c51,0x9a2b},
{0x1c52,0x1c52,0x9a37},
{0x1c53,0x1c53,0x9a45},
{0x1c54,0x1c54,0x9a42},
{0x1c55,0x1c55,0x9a40},
{0x1c56,0x1c56,0x9a43},
{0x1c57,0x1c57,0x9a3e},
{0x1c58,0x1c58,0x9a55},
{0x1c59,0x1c59,0x9a4d},
{0x1c5a,0x1c5a,0x9a5b},
{0x1c5b,0x1c5b,0x9a57},
{0x1c5c,0x1c5c,0x9a5f},
{0x1c5d,0x1c5d,0x9a62},
{0x1c5e,0x1c5e,0x9a65},
{0x1c5f,0x1c5f,0x9a64},
{0x1c60,0x1c60,0x9a69},
{0x1c61,0x1c61,0x9a6b},
{0x1c62,0x1c62,0x9a6a},
{0x1c63,0x1c63,0x9aad},
{0x1c64,0x1c64,0x9ab0},
{0x1c65,0x1c65,0x9abc},
{0x1c66,0x1c66,0x9ac0},
{0x1c67,0x1c67,0x9acf},
{0x1c68,0x1c68,0x9ad1},
{0x1c69,0x1c6a,0x9ad3},
{0x1c6b,0x1c6c,0x9ade},
{0x1c6d,0x1c6e,0x9ae2},
{0x1c6f,0x1c6f,0x9ae6},
{0x1c70,0x1c70,0x9aef},
{0x1c71,0x1c71,0x9aeb},
{0x1c72,0x1c72,0x9aee},
{0x1c73,0x1c73,0x9af4},
{0x1c74,0x1c74,0x9af1},
{0x1c75,0x1c75,0x9af7},
{0x1c76,0x1c76,0x9afb},
{0x1c77,0x1c77,0x9b06},
{0x1c78,0x1c78,0x9b18},
{0x1c79,0x1c79,0x9b1a},
{0x1c7a,0x1c7a,0x9b1f},
{0x1c7b,0x1c7c,0x9b22},
{0x1c7d,0x1c7d,0x9b25},
{0x1c7e,0x1c81,0x9b27},
{0x1c82,0x1c83,0x9b2e},
{0x1c84,0x1c84,0x9b32},
{0x1c85,0x1c85,0x9b44},
{0x1c86,0x1c86,0x9b43},
{0x1c87,0x1c87,0x9b4f},
{0x1c88,0x1c89,0x9b4d},
{0x1c8a,0x1c8a,0x9b51},
{0x1c8b,0x1c8b,0x9b58},
{0x1c8c,0x1c8c,0x9b74},
{0x1c8d,0x1c8d,0x9b93},
{0x1c8e,0x1c8e,0x9b83},
{0x1c8f,0x1c8f,0x9b91},
{0x1c90,0x1c91,0x9b96},
{0x1c92,0x1c93,0x9b9f},
{0x1c94,0x1c94,0x9ba8},
{0x1c95,0x1c95,0x9bb4},
{0x1c96,0x1c96,0x9bc0},
{0x1c97,0x1c97,0x9bca},
{0x1c98,0x1c98,0x9bb9},
{0x1c99,0x1c99,0x9bc6},
{0x1c9a,0x1c9a,0x9bcf},
{0x1c9b,0x1c9c,0x9bd1},
{0x1c9d,0x1c9d,0x9be3},
{0x1c9e,0x1c9e,0x9be2},
{0x1c9f,0x1c9f,0x9be4},
{0x1ca0,0x1ca0,0x9bd4},
{0x1ca1,0x1ca1,0x9be1},
{0x1ca2,0x1ca2,0x9c3a},
{0x1ca3,0x1ca3,0x9bf2},
{0x1ca4,0x1ca4,0x9bf1},
{0x1ca5,0x1ca5,0x9bf0},
{0x1ca6,0x1ca6,0x9c15},
{0x1ca7,0x1ca7,0x9c14},
{0x1ca8,0x1ca8,0x9c09},
{0x1ca9,0x1ca9,0x9c13},
{0x1caa,0x1caa,0x9c0c},
{0x1cab,0x1cab,0x9c06},
{0x1cac,0x1cac,0x9c08},
{0x1cad,0x1cad,0x9c12},
{0x1cae,0x1cae,0x9c0a},
{0x1caf,0x1caf,0x9c04},
{0x1cb0,0x1cb0,0x9c2e},
{0x1cb1,0x1cb1,0x9c1b},
{0x1cb2,0x1cb2,0x9c25},
{0x1cb3,0x1cb3,0x9c24},
{0x1cb4,0x1cb4,0x9c21},
{0x1cb5,0x1cb5,0x9c30},
{0x1cb6,0x1cb6,0x9c47},
{0x1cb7,0x1cb7,0x9c32},
{0x1cb8,0x1cb8,0x9c46},
{0x1cb9,0x1cb9,0x9c3e},
{0x1cba,0x1cba,0x9c5a},
{0x1cbb,0x1cbb,0x9c60},
{0x1cbc,0x1cbc,0x9c67},
{0x1cbd,0x1cbd,0x9c76},
{0x1cbe,0x1cbe,0x9c78},
{0x1cbf,0x1cbf,0x9ce7},
{0x1cc0,0x1cc0,0x9cec},
{0x1cc1,0x1cc1,0x9cf0},
{0x1cc2,0x1cc2,0x9d09},
{0x1cc3,0x1cc3,0x9d08},
{0x1cc4,0x1cc4,0x9ceb},
{0x1cc5,0x1cc5,0x9d03},
{0x1cc6,0x1cc6,0x9d06},
{0x1cc7,0x1cc7,0x9d2a},
{0x1cc8,0x1cc8,0x9d26},
{0x1cc9,0x1cc9,0x9daf},
{0x1cca,0x1cca,0x9d23},
{0x1ccb,0x1ccb,0x9d1f},
{0x1ccc,0x1ccc,0x9d44},
{0x1ccd,0x1ccd,0x9d15},
{0x1cce,0x1cce,0x9d12},
{0x1ccf,0x1ccf,0x9d41},
{0x1cd0,0x1cd0,0x9d3f},
{0x1cd1,0x1cd1,0x9d3e},
{0x1cd2,0x1cd2,0x9d46},
{0x1cd3,0x1cd3,0x9d48},
{0x1cd4,0x1cd5,0x9d5d},
{0x1cd6,0x1cd6,0x9d64},
{0x1cd7,0x1cd7,0x9d51},
{0x1cd8,0x1cd8,0x9d50},
{0x1cd9,0x1cd9,0x9d59},
{0x1cda,0x1cda,0x9d72},
{0x1cdb,0x1cdb,0x9d89},
{0x1cdc,0x1cdc,0x9d87},
{0x1cdd,0x1cdd,0x9dab},
{0x1cde,0x1cde,0x9d6f},
{0x1cdf,0x1cdf,0x9d7a},
{0x1ce0,0x1ce0,0x9d9a},
{0x1ce1,0x1ce1,0x9da4},
{0x1ce2,0x1ce2,0x9da9},
{0x1ce3,0x1ce3,0x9db2},
{0x1ce4,0x1ce4,0x9dc4},
{0x1ce5,0x1ce5,0x9dc1},
{0x1ce6,0x1ce6,0x9dbb},
{0x1ce7,0x1ce7,0x9db8},
{0x1ce8,0x1ce8,0x9dba},
{0x1ce9,0x1ce9,0x9dc6},
{0x1cea,0x1cea,0x9dcf},
{0x1ceb,0x1ceb,0x9dc2},
{0x1cec,0x1cec,0x9dd9},
{0x1ced,0x1ced,0x9dd3},
{0x1cee,0x1cee,0x9df8},
{0x1cef,0x1cef,0x9de6},
{0x1cf0,0x1cf0,0x9ded},
{0x1cf1,0x1cf1,0x9def},
{0x1cf2,0x1cf2,0x9dfd},
{0x1cf3,0x1cf4,0x9e1a},
{0x1cf5,0x1cf5,0x9e1e},
{0x1cf6,0x1cf6,0x9e75},
{0x1cf7,0x1cf7,0x9e79},
{0x1cf8,0x1cf8,0x9e7d},
{0x1cf9,0x1cf9,0x9e81},
{0x1cfa,0x1cfa,0x9e88},
{0x1cfb,0x1cfc,0x9e8b},
{0x1cfd,0x1cfd,0x9e92},
{0x1cfe,0x1cfe,0x9e95},
{0x1cff,0x1cff,0x9e91},
{0x1d00,0x1d00,0x9e9d},
{0x1d01,0x1d01,0x9ea5},
{0x1d02,0x1d02,0x9ea9},
{0x1d03,0x1d03,0x9eb8},
{0x1d04,0x1d04,0x9eaa},
{0x1d05,0x1d05,0x9ead},
{0x1d06,0x1d06,0x9761},
{0x1d07,0x1d07,0x9ecc},
{0x1d08,0x1d0a,0x9ece},
{0x1d0b,0x1d0b,0x9ed4},
{0x1d0c,0x1d0c,0x9edc},
{0x1d0d,0x1d0d,0x9ede},
{0x1d0e,0x1d0e,0x9edd},
{0x1d0f,0x1d0f,0x9ee0},
{0x1d10,0x1d10,0x9ee5},
{0x1d11,0x1d11,0x9ee8},
{0x1d12,0x1d12,0x9eef},
{0x1d13,0x1d13,0x9ef4},
{0x1d14,0x1d15,0x9ef6},
{0x1d16,0x1d16,0x9ef9},
{0x1d17,0x1d19,0x9efb},
{0x1d1a,0x1d1b,0x9f07},
{0x1d1c,0x1d1c,0x76b7},
{0x1d1d,0x1d1d,0x9f15},
{0x1d1e,0x1d1e,0x9f21},
{0x1d1f,0x1d1f,0x9f2c},
{0x1d20,0x1d20,0x9f3e},
{0x1d21,0x1d21,0x9f4a},
{0x1d22,0x1d22,0x9f52},
{0x1d23,0x1d23,0x9f54},
{0x1d24,0x1d24,0x9f63},
{0x1d25,0x1d27,0x9f5f},
{0x1d28,0x1d29,0x9f66},
{0x1d2a,0x1d2a,0x9f6c},
{0x1d2b,0x1d2b,0x9f6a},
{0x1d2c,0x1d2c,0x9f77},
{0x1d2d,0x1d2d,0x9f72},
{0x1d2e,0x1d2e,0x9f76},
{0x1d2f,0x1d2f,0x9f95},
{0x1d30,0x1d30,0x9f9c},
{0x1d31,0x1d31,0x9fa0},
{0x1d32,0x1d32,0x582f},
{0x1d33,0x1d33,0x69c7},
{0x1d34,0x1d34,0x9059},
{0x1d35,0x1d35,0x7464},
{0x1d36,0x1d36,0x2642},
{0x1d37,0x1d82,0x2500},
{0x1d83,0x1d96,0x2460},
{0x1d97,0x1da0,0x2160},
{0x1da1,0x1da1,0x3349},
{0x1da2,0x1da2,0x3314},
{0x1da3,0x1da3,0x3322},
{0x1da4,0x1da4,0x334d},
{0x1da5,0x1da5,0x3318},
{0x1da6,0x1da6,0x3327},
{0x1da7,0x1da7,0x3303},
{0x1da8,0x1da8,0x3336},
{0x1da9,0x1da9,0x3351},
{0x1daa,0x1daa,0x3357},
{0x1dab,0x1dab,0x330d},
{0x1dac,0x1dac,0x3326},
{0x1dad,0x1dad,0x3323},
{0x1dae,0x1dae,0x332b},
{0x1daf,0x1daf,0x334a},
{0x1db0,0x1db0,0x333b},
{0x1db1,0x1db3,0x339c},
{0x1db4,0x1db5,0x338e},
{0x1db6,0x1db6,0x33c4},
{0x1db7,0x1db7,0x33a1},
{0x1db8,0x1db8,0x301d},
{0x1db9,0x1db9,0x301f},
{0x1dba,0x1dba,0x2116},
{0x1dbb,0x1dbb,0x33cd},
{0x1dbc,0x1dbc,0x2121},
{0x1dbd,0x1dc1,0x32a4},
{0x1dc2,0x1dc3,0x3231},
{0x1dc4,0x1dc4,0x3239},
{0x1dc5,0x1dc5,0x337e},
{0x1dc6,0x1dc6,0x337d},
{0x1dc7,0x1dc7,0x337c},
{0x1dc8,0x1dc8,0x222e},
{0x1dc9,0x1dc9,0x2211},
{0x1dca,0x1dca,0x221a},
{0x1dcb,0x1dcb,0x22a5},
{0x1dcc,0x1dcc,0x2220},
{0x1dcd,0x1dcd,0x221f},
{0x1dce,0x1dce,0x22bf},
{0x1dcf,0x1dd0,0x2229},
{0x1dd1,0x1dd1,0x555e},
{0x1dd2,0x1dd2,0x98f4},
{0x1dd3,0x1dd3,0x6ea2},
{0x1dd4,0x1dd4,0x9c2f},
{0x1dd5,0x1dd5,0x6deb},
{0x1dd6,0x1dd6,0x8fc2},
{0x1dd7,0x1dd7,0x6b1d},
{0x1dd8,0x1dd8,0x53a9},
{0x1dda,0x1dda,0x5642},
{0x1ddb,0x1ddb,0x990c},
{0x1ddc,0x1ddc,0x7130},
{0x1ddd,0x1ddd,0x8956},
{0x1dde,0x1dde,0x9dd7},
{0x1ddf,0x1ddf,0x8fe6},
{0x1de0,0x1de0,0x6062},
{0x1de1,0x1de1,0x62d0},
{0x1de2,0x1de2,0x6666},
{0x1de3,0x1de3,0xfa36},
{0x1de4,0x1de4,0x845b},
{0x1de5,0x1de5,0x9784},
{0x1de6,0x1de6,0x5699},
{0x1de7,0x1de7,0x3d4e},
{0x1de8,0x1de8,0x7ff0},
{0x1de9,0x1de9,0x7feb},
{0x1dea,0x1dea,0x5fbd},
{0x1deb,0x1deb,0x7947},
{0x1dec,0x1dec,0x4fe0},
{0x1ded,0x1ded,0x537f},
{0x1dee,0x1dee,0x50c5},
{0x1def,0x1def,0x8ec0},
{0x1df0,0x1df0,0x55b0},
{0x1df2,0x1df2,0x5c51},
{0x1df3,0x1df3,0x9774},
{0x1df4,0x1df4,0x7941},
{0x1df5,0x1df5,0x6167},
{0x1df7,0x1df7,0x7e6b},
{0x1df8,0x1df8,0x8346},
{0x1dfa,0x1dfa,0x5026},
{0x1dfb,0x1dfb,0x5acc},
{0x1dfc,0x1dfc,0x6372},
{0x1dfd,0x1dfd,0x9e7c},
{0x1dfe,0x1dfe,0x8afa},
{0x1dff,0x1dff,0x5df7},
{0x1e00,0x1e00,0x663b},
{0x1e01,0x1e01,0x6e9d},
{0x1e02,0x1e02,0x9eb4},
{0x1e03,0x1e03,0x9d60},
{0x1e04,0x1e04,0x7511},
{0x1e05,0x1e05,0x91c7},
{0x1e06,0x1e06,0x698a},
{0x1e07,0x1e07,0x6805},
{0x1e08,0x1e08,0x85a9},
{0x1e09,0x1e09,0x9bd6},
{0x1e0a,0x1e0a,0x9306},
{0x1e0b,0x1e0b,0x73ca},
{0x1e0c,0x1e0c,0x53f1},
{0x1e0d,0x1e0d,0x5c62},
{0x1e0e,0x1e0e,0x906e},
{0x1e10,0x1e10,0x707c},
{0x1e11,0x1e11,0x7e61},
{0x1e12,0x1e12,0x914b},
{0x1e13,0x1e13,0x66d9},
{0x1e14,0x1e14,0xfa46},
{0x1e15,0x1e15,0x85af},
{0x1e16,0x1e16,0x85f7},
{0x1e17,0x1e17,0x54e8},
{0x1e18,0x1e18,0x5ee0},
{0x1e19,0x1e19,0x68a2},
{0x1e1a,0x1e1a,0x8523},
{0x1e1b,0x1e1b,0x91ac},
{0x1e1c,0x1e1c,0x9798},
{0x1e1d,0x1e1d,0x8755},
{0x1e1e,0x1e1e,0x976d},
{0x1e1f,0x1e1f,0x9017},
{0x1e20,0x1e20,0x7fe0},
{0x1e21,0x1e21,0x647a},
{0x1e22,0x1e22,0x901d},
{0x1e23,0x1e23,0x87ec},
{0x1e24,0x1e24,0x64b0},
{0x1e25,0x1e25,0x6813},
{0x1e26,0x1e26,0x714e},
{0x1e27,0x1e27,0x717d},
{0x1e28,0x1e28,0x8a6e},
{0x1e29,0x1e29,0x564c},
{0x1e2a,0x1e2a,0x9061},
{0x1e2b,0x1e2b,0x5275},
{0x1e2c,0x1e2c,0x6414},
{0x1e2d,0x1e2d,0x75e9},
{0x1e2e,0x1e2e,0x905c},
{0x1e2f,0x1e2f,0x9a52},
{0x1e30,0x1e30,0x817f},
{0x1e31,0x1e31,0x9edb},
{0x1e32,0x1e32,0x5544},
{0x1e33,0x1e33,0x6fef},
{0x1e34,0x1e34,0xfa4a},
{0x1e35,0x1e35,0x86f8},
{0x1e37,0x1e37,0x8fbf},
{0x1e38,0x1e38,0x68da},
{0x1e39,0x1e39,0x9c48},
{0x1e3a,0x1e3a,0x6a3d},
{0x1e3b,0x1e3b,0x7c1e},
{0x1e3c,0x1e3c,0x8a3b},
{0x1e3d,0x1e3d,0x7026},
{0x1e3e,0x1e3e,0x51cb},
{0x1e3f,0x1e3f,0x6357},
{0x1e40,0x1e40,0x69cc},
{0x1e41,0x1e41,0x939a},
{0x1e42,0x1e42,0xfa10},
{0x1e43,0x1e43,0x6451},
{0x1e44,0x1e44,0x912d},
{0x1e45,0x1e45,0x64e2},
{0x1e46,0x1e46,0xf9ec},
{0x1e47,0x1e47,0x5861},
{0x1e48,0x1e48,0x985a},
{0x1e49,0x1e49,0x5835},
{0x1e4b,0x1e4b,0x83df},
{0x1e4c,0x1e4c,0x8ced},
{0x1e4d,0x1e4d,0x5858},
{0x1e4e,0x1e4e,0x79b1},
{0x1e4f,0x1e4f,0x9d07},
{0x1e50,0x1e50,0x7006},
{0x1e51,0x1e51,0x701e},
{0x1e52,0x1e52,0x5678},
{0x1e53,0x1e53,0x9041},
{0x1e54,0x1e54,0x9813},
{0x1e55,0x1e55,0x90a3},
{0x1e56,0x1e56,0x8b0e},
{0x1e57,0x1e57,0x7058},
{0x1e58,0x1e58,0x6962},
{0x1e59,0x1e59,0x79b0},
{0x1e5a,0x1e5a,0x56ca},
{0x1e5b,0x1e5b,0x724c},
{0x1e5c,0x1e5c,0x9019},
{0x1e5d,0x1e5d,0x79e4},
{0x1e5e,0x1e5e,0x525d},
{0x1e5f,0x1e5f,0x7bb8},
{0x1e60,0x1e60,0x6f51},
{0x1e61,0x1e61,0x91b1},
{0x1e62,0x1e62,0x633d},
{0x1e63,0x1e63,0x6249},
{0x1e64,0x1e64,0x6a0b},
{0x1e65,0x1e65,0x67ca},
{0x1e66,0x1e66,0x7a17},
{0x1e67,0x1e67,0x903c},
{0x1e68,0x1e68,0x5a9b},
{0x1e69,0x1e69,0x8b2c},
{0x1e6a,0x1e6a,0x5edf},
{0x1e6b,0x1e6b,0x7015},
{0x1e6c,0x1e6c,0xfa6a},
{0x1e6d,0x1e6d,0x853d},
{0x1e6e,0x1e6e,0x77a5},
{0x1e6f,0x1e6f,0x5a29},
{0x1e70,0x1e70,0x5e96},
{0x1e71,0x1e71,0x6ce1},
{0x1e72,0x1e72,0x84ec},
{0x1e73,0x1e73,0x9830},
{0x1e74,0x1e74,0x9c52},
{0x1e75,0x1e75,0x9eb5},
{0x1e76,0x1e76,0x5132},
{0x1e77,0x1e77,0x9905},
{0x1e78,0x1e78,0x7c7e},
{0x1e79,0x1e79,0x9453},
{0x1e7a,0x1e7a,0x6108},
{0x1e7b,0x1e7b,0x7652},
{0x1e7c,0x1e7c,0x7337},
{0x1e7d,0x1e7d,0x7194},
{0x1e7e,0x1e7e,0x8000},
{0x1e7f,0x1e7f,0x840a},
{0x1e80,0x1e80,0xf9c3},
{0x1e81,0x1e82,0xf992},
{0x1e83,0x1e83,0xf999},
{0x1e84,0x1e84,0x6994},
{0x1e85,0x1e85,0x881f},
{0x1e87,0x1e87,0x5189},
{0x1e8a,0x1e8a,0x5539},
{0x1e8b,0x1e8b,0x5533},
{0x1e8c,0x1e8c,0x55e4},
{0x1e8d,0x1e8d,0x5632},
{0x1e8e,0x1e8e,0x56a5},
{0x1e8f,0x1e8f,0x580b},
{0x1e90,0x1e90,0x5abe},
{0x1e92,0x1e92,0x5c5b},
{0x1e93,0x1e93,0x5e64},
{0x1e94,0x1e94,0x6097},
{0x1e95,0x1e95,0x6369},
{0x1e96,0x1e96,0x6406},
{0x1e97,0x1e97,0x6522},
{0x1e98,0x1e98,0x6583},
{0x1e99,0x1e99,0x67a6},
{0x1e9a,0x1e9a,0x67fa},
{0x1e9b,0x1e9b,0x689b},
{0x1e9c,0x1e9c,0x688e},
{0x1e9d,0x1e9d,0x6e6e},
{0x1ea0,0x1ea0,0x73ce},
{0x1ea1,0x1ea1,0x7504},
{0x1ea2,0x1ea2,0x750d},
{0x1ea3,0x1ea3,0x7515},
{0x1ea4,0x1ea4,0x7693},
{0x1ea5,0x1ea5,0x787c},
{0x1ea6,0x1ea6,0x7a31},
{0x1ea7,0x1ea7,0x9f9d},
{0x1ea8,0x1ea8,0x7b99},
{0x1ea9,0x1ea9,0x7c90},
{0x1eaa,0x1eaa,0x7cae},
{0x1eab,0x1eab,0x7d9b},
{0x1eac,0x1eac,0x7dae},
{0x1ead,0x1ead,0x7d9f},
{0x1eae,0x1eae,0x7fd4},
{0x1eaf,0x1eaf,0x822e},
{0x1eb0,0x1eb0,0x828d},
{0x1eb1,0x1eb1,0x82d2},
{0x1eb2,0x1eb2,0x8323},
{0x1eb3,0x1eb3,0x8375},
{0x1eb4,0x1eb4,0x8517},
{0x1eb5,0x1eb5,0x853e},
{0x1eb6,0x1eb6,0x8782},
{0x1eb7,0x1eb7,0x87d2},
{0x1eb8,0x1eb8,0x890a},
{0x1eb9,0x1eb9,0x89af},
{0x1eba,0x1eba,0x8ade},
{0x1ebb,0x1ebb,0x8b41},
{0x1ebc,0x1ebc,0x8dda},
{0x1ebd,0x1ebd,0x8e09},
{0x1ebe,0x1ebe,0x8f13},
{0x1ebf,0x1ebf,0x8fea},
{0x1ec0,0x1ec0,0x9087},
{0x1ec1,0x1ec1,0x9058},
{0x1ec2,0x1ec2,0x6248},
{0x1ec3,0x1ec3,0x91c1},
{0x1ec4,0x1ec4,0x95bb},
{0x1ec5,0x1ec5,0x7762},
{0x1ec6,0x1ec6,0x9724},
{0x1ec7,0x1ec7,0x9760},
{0x1ec8,0x1ec8,0x9771},
{0x1ec9,0x1ec9,0x9824},
{0x1eca,0x1eca,0x9b2e},
{0x1ecb,0x1ecb,0x9b97},
{0x1ecc,0x1ecc,0x9bf2},
{0x1ecd,0x1ecd,0x9eaa},
{0x1ece,0x1ece,0x9f9c},
{0x1ecf,0x1ed0,0x3001},
{0x1ed1,0x1ed1,0x203e},
{0x1ed2,0x1ed2,0xff3f},
{0x1ed3,0x1ed3,0x30fc},
{0x1ed4,0x1ed4,0x2015},
{0x1ed5,0x1ed5,0x2010},
{0x1ed6,0x1ed6,0x301c},
{0x1ed7,0x1ed7,0x2016},
{0x1ed8,0x1ed8,0xff5c},
{0x1ed9,0x1ed9,0x2026},
{0x1eda,0x1eda,0x2025},
{0x1edb,0x1edc,0xff08},
{0x1edd,0x1ede,0x3014},
{0x1edf,0x1ee0,0xfe47},
{0x1ee1,0x1ee1,0xff5b},
{0x1ee2,0x1ee2,0xff5d},
{0x1ee3,0x1eec,0x3008},
{0x1eed,0x1eed,0xff1d},
{0x1eee,0x1eee,0x3041},
{0x1eef,0x1eef,0x3043},
{0x1ef0,0x1ef0,0x3045},
{0x1ef1,0x1ef1,0x3047},
{0x1ef2,0x1ef2,0x3049},
{0x1ef3,0x1ef3,0x3063},
{0x1ef4,0x1ef4,0x3083},
{0x1ef5,0x1ef5,0x3085},
{0x1ef6,0x1ef6,0x3087},
{0x1ef7,0x1ef7,0x308e},
{0x1ef8,0x1ef8,0x30a1},
{0x1ef9,0x1ef9,0x30a3},
{0x1efa,0x1efa,0x30a5},
{0x1efb,0x1efb,0x30a7},
{0x1efc,0x1efc,0x30a9},
{0x1efd,0x1efd,0x30c3},
{0x1efe,0x1efe,0x30e3},
{0x1eff,0x1eff,0x30e5},
{0x1f00,0x1f00,0x30e7},
{0x1f01,0x1f01,0x30ee},
{0x1f02,0x1f03,0x30f5},
{0x1f04,0x1f04,0x3349},
{0x1f05,0x1f05,0x3314},
{0x1f06,0x1f06,0x3322},
{0x1f07,0x1f07,0x334d},
{0x1f08,0x1f08,0x3318},
{0x1f09,0x1f09,0x3327},
{0x1f0a,0x1f0a,0x3303},
{0x1f0b,0x1f0b,0x3336},
{0x1f0c,0x1f0c,0x3351},
{0x1f0d,0x1f0d,0x3357},
{0x1f0e,0x1f0e,0x330d},
{0x1f0f,0x1f0f,0x3326},
{0x1f10,0x1f10,0x3323},
{0x1f11,0x1f11,0x332b},
{0x1f12,0x1f12,0x334a},
{0x1f13,0x1f13,0x333b},
{0x1f14,0x1f15,0x301d},
{0x1f16,0x1f18,0x3094},
{0x1f19,0x1f19,0x82a6},
{0x1f1a,0x1f1a,0x8328},
{0x1f1b,0x1f1b,0x5653},
{0x1f1c,0x1f1c,0x53a9},
{0x1f1d,0x1f1d,0x7259},
{0x1f1e,0x1f1e,0x6c72},
{0x1f1f,0x1f1f,0x7b08},
{0x1f20,0x1f20,0x9957},
{0x1f21,0x1f21,0x62f3},
{0x1f22,0x1f22,0x9910},
{0x1f23,0x1f23,0x976d},
{0x1f24,0x1f24,0x717d},
{0x1f25,0x1f25,0x7a7f},
{0x1f26,0x1f26,0x7bad},
{0x1f27,0x1f27,0x63c3},
{0x1f28,0x1f28,0x83df},
{0x1f29,0x1f29,0x7962},
{0x1f2a,0x1f2a,0x53db},
{0x1f2b,0x1f2b,0x7bc7},
{0x1f2c,0x1f2c,0x8fc4},
{0x1f2d,0x1f2d,0x7c3e},
{0x1f2e,0x1f2e,0x50ca},
{0x1f2f,0x1f2f,0x5315},
{0x1f30,0x1f30,0x55a9},
{0x1f31,0x1f31,0x56ae},
{0x1f32,0x1f32,0x5819},
{0x1f33,0x1f33,0x591b},
{0x1f34,0x1f34,0x5c28},
{0x1f35,0x1f35,0x5ed0},
{0x1f36,0x1f36,0x5ecf},
{0x1f37,0x1f37,0x6241},
{0x1f38,0x1f38,0x66c1},
{0x1f39,0x1f39,0x6c08},
{0x1f3a,0x1f3a,0x6e23},
{0x1f3b,0x1f3b,0x6eec},
{0x1f3c,0x1f3c,0x7a97},
{0x1f3d,0x1f3d,0x7bdd},
{0x1f3e,0x1f3e,0x7fe9},
{0x1f3f,0x1f3f,0x8422},
{0x1f40,0x1f40,0x8759},
{0x1f41,0x1f41,0x880e},
{0x1f42,0x1f42,0x87d2},
{0x1f43,0x1f43,0x9a19},
{0x1f44,0x1f44,0x9ead},
{0x1f45,0x1f45,0xffe4},
{0x1f46,0x1f46,0xff07},
{0x1f47,0x1f47,0xff02},
{0x1f48,0x1f48,0x11a8},
{0x1f49,0x1f49,0x25c1},
{0x1f4a,0x1f4a,0x25b7},
{0x1f4b,0x1f4b,0x21e9},
{0x1f4c,0x1f4c,0x21e7},
{0x1f4d,0x1f4d,0x21e6},
{0x1f4e,0x1f4e,0x21e8},
{0x1f4f,0x1f4f,0x25a2},
{0x1f50,0x1f50,0x2667},
{0x1f51,0x1f51,0x2661},
{0x1f52,0x1f52,0x2664},
{0x1f53,0x1f53,0x2662},
{0x1f54,0x1f54,0x33a0},
{0x1f55,0x1f55,0x33a2},
{0x1f56,0x1f57,0x33a4},
{0x1f58,0x1f58,0x3397},
{0x1f59,0x1f59,0x2113},
{0x1f5a,0x1f5a,0x3398},
{0x1f5b,0x1f5b,0x33b3},
{0x1f5c,0x1f5c,0x33b2},
{0x1f5d,0x1f5d,0x33b1},
{0x1f5e,0x1f5e,0x33b0},
{0x1f5f,0x1f61,0x3385},
{0x1f62,0x1f62,0x33cb},
{0x1f63,0x1f63,0x3390},
{0x1f64,0x1f64,0x33d4},
{0x1f65,0x1f65,0x3396},
{0x1f66,0x1f66,0x3322},
{0x1f67,0x1f67,0x3316},
{0x1f68,0x1f68,0x3318},
{0x1f69,0x1f69,0x3315},
{0x1f6a,0x1f6a,0x3303},
{0x1f6b,0x1f6b,0x3323},
{0x1f6c,0x1f6c,0x3357},
{0x1f6d,0x1f6d,0x3342},
{0x1f6e,0x1f6e,0x3339},
{0x1f6f,0x1f6f,0x333b},
{0x1f70,0x1f70,0x3300},
{0x1f71,0x1f71,0x3331},
{0x1f72,0x1f72,0x3347},
{0x1f73,0x1f73,0x331e},
{0x1f74,0x1f74,0x332a},
{0x1f75,0x1f75,0x33cd},
{0x1f76,0x1f76,0x337f},
{0x1f77,0x1f77,0x2121},
{0x1f78,0x1f78,0x260e},
{0x1f79,0x1f79,0x3036},
{0x1f7a,0x1f7a,0x3020},
{0x1f7b,0x1f7b,0xa9},
{0x1f7c,0x1f7c,0xae},
{0x1f7e,0x1f86,0x2488},
{0x1f87,0x1f9a,0x2474},
{0x1f9b,0x1f9b,0x3251},
{0x1f9c,0x1fa5,0x2170},
{0x1fa6,0x1faf,0x3252},
{0x1fb0,0x1fc9,0x249c},
{0x1fca,0x1fca,0x3243},
{0x1fcb,0x1fcb,0x323d},
{0x1fcc,0x1fcc,0x323f},
{0x1fcd,0x1fcd,0x3234},
{0x1fce,0x1fce,0x3238},
{0x1fcf,0x1fcf,0x3233},
{0x1fd0,0x1fd0,0x323c},
{0x1fd1,0x1fd1,0x3242},
{0x1fd2,0x1fd2,0x323e},
{0x1fd3,0x1fd3,0x3236},
{0x1fd4,0x1fd4,0x3235},
{0x1fd5,0x1fd5,0x323b},
{0x1fd6,0x1fd6,0x3240},
{0x1fd7,0x1fd7,0x323a},
{0x1fd8,0x1fd8,0x32b0},
{0x1fd9,0x1fd9,0x32ad},
{0x1fda,0x1fda,0x32a9},
{0x1fdb,0x1fdb,0x32af},
{0x1fdc,0x1fdc,0x3294},
{0x1fdd,0x1fdd,0x32aa},
{0x1fde,0x1fde,0x3298},
{0x1fdf,0x1fdf,0x32ab},
{0x1fe0,0x1fe0,0x3292},
{0x1fe1,0x1fe1,0x3291},
{0x1fe2,0x1fe2,0x3293},
{0x1fe3,0x1fe3,0x32ac},
{0x1fe4,0x1fe4,0x32ae},
{0x1fe5,0x1fe5,0x3296},
{0x1fe6,0x1fe6,0x23a9},
{0x1fe7,0x1fe7,0x23a8},
{0x1fe8,0x1fe8,0x23a7},
{0x1fe9,0x1fe9,0xfe38},
{0x1fea,0x1fea,0x23ad},
{0x1feb,0x1feb,0x23ac},
{0x1fec,0x1fec,0x23ab},
{0x1fed,0x1fed,0xfe38},
{0x1fee,0x1ff0,0x23ab},
{0x1ff1,0x1ff1,0xfe38},
{0x1ff2,0x1ff4,0x23a7},
{0x1ff5,0x1ff5,0xfe38},
{0x1ff6,0x1ff6,0x33cc},
{0x1ff7,0x1ff7,0x3305},
{0x1ff8,0x1ff8,0xbd},
{0x1ff9,0x1ff9,0xbc},
{0x1ffa,0x1ffa,0x339f},
{0x1ffb,0x1ffb,0x33a3},
{0x1ffc,0x1ffc,0x33a6},
{0x1fff,0x1fff,0x329e},
{0x2000,0x2001,0x3388},
{0x2002,0x2002,0x33c8},
{0x2003,0x2003,0x222d},
{0x2004,0x2004,0x5370},
{0x2005,0x2005,0x3230},
{0x2006,0x200b,0x322a},
{0x200c,0x200c,0x3237},
{0x200d,0x200d,0x3241},
{0x200e,0x200e,0x27a1},
{0x200f,0x2011,0x2b05},
{0x2012,0x2012,0x25c9},
{0x2013,0x2013,0x2660},
{0x2014,0x2014,0x2665},
{0x2015,0x2015,0x2663},
{0x2016,0x2016,0x2666},
{0x2017,0x201a,0x2600},
{0x201b,0x201b,0x261e},
{0x201c,0x201d,0x261c},
{0x201e,0x201e,0x261f},
{0x201f,0x201f,0x3299},
{0x2020,0x2020,0x24ea},
{0x2021,0x2022,0x216a},
{0x2023,0x2023,0xff10},
{0x2024,0x2024,0xff10},
{0x2025,0x2025,0x3000},
{0x2026,0x202d,0x2581},
{0x202e,0x202e,0x258f},
{0x202f,0x202f,0x258e},
{0x2030,0x2030,0x258d},
{0x2031,0x2031,0x258c},
{0x2032,0x2032,0x258b},
{0x2033,0x2033,0x258a},
{0x2034,0x2034,0x2589},
{0x2035,0x2036,0x2594},
{0x2037,0x2038,0x256d},
{0x2039,0x2039,0x2570},
{0x203a,0x203a,0x256f},
{0x203b,0x203b,0x2550},
{0x203c,0x203c,0x255e},
{0x203d,0x203d,0x256a},
{0x203e,0x203e,0x2561},
{0x203f,0x2040,0x25e2},
{0x2041,0x2041,0x25e5},
{0x2042,0x2042,0x25e4},
{0x2043,0x2043,0x25cf},
{0x2044,0x2044,0xed0},
{0x2045,0x2047,0x2571},
{0x2048,0x2049,0x3095},
{0x204a,0x204a,0x9022},
{0x204b,0x204b,0x8fbb},
{0x204c,0x204c,0xff0c},
{0x204d,0x204d,0xb0},
{0x204e,0x204e,0x3013},
{0x204f,0x204f,0x309c},
{0x2050,0x2050,0x309b},
{0x2051,0x2051,0x2032},
{0x2052,0x2052,0xff0e},
{0x2053,0x2054,0x2018},
{0x2055,0x2056,0x201c},
{0x2057,0x2057,0x201c},
{0x2058,0x2058,0x201e},
{0x2059,0x2059,0x2018},
{0x205a,0x205a,0x201a},
{0x205b,0x205b,0x2033},
{0x205c,0x205c,0x51dc},
{0x205d,0x205d,0x7199},
{0x205e,0x2066,0x2776},
{0x206a,0x206b,0x217a},
{0x206f,0x206f,0x217f},
{0x2070,0x2070,0x210a},
{0x2071,0x2071,0x2109},
{0x2073,0x2073,0x213b},
{0x2074,0x2074,0x3004},
{0x2075,0x2075,0x21c6},
{0x2076,0x2077,0x21c4},
{0x2079,0x207c,0x30f7},
{0x207d,0x207d,0x5927},
{0x207e,0x207e,0x5c0f},
{0x207f,0x207f,0x329d},
{0x2080,0x2080,0x63a7},
{0x2083,0x2083,0x337b},
{0x2084,0x2084,0x337f},
{0x2087,0x2087,0x3333},
{0x2088,0x2088,0x334e},
{0x2089,0x2089,0x3322},
{0x208a,0x208a,0x3316},
{0x208b,0x208b,0x3305},
{0x208c,0x208c,0x3305},
{0x208d,0x208d,0x3305},
{0x208e,0x208e,0x3333},
{0x208f,0x208f,0x334e},
{0x2090,0x2090,0x334e},
{0x2091,0x2091,0x334e},
{0x2092,0x2092,0x3303},
{0x2093,0x2093,0x3318},
{0x2094,0x2094,0x3315},
{0x2095,0x2095,0x3339},
{0x2096,0x2096,0x3339},
{0x2097,0x2097,0x3339},
{0x2098,0x2098,0x3357},
{0x2099,0x2099,0x3342},
{0x209a,0x209a,0x3342},
{0x209b,0x209b,0x3342},
{0x209c,0x209c,0x3323},
{0x209d,0x209d,0x333b},
{0x209e,0x209e,0x3300},
{0x209f,0x209f,0x331e},
{0x20a0,0x20a0,0x331e},
{0x20a1,0x20a1,0x331e},
{0x20a2,0x20a2,0x332a},
{0x20a3,0x20a3,0x332a},
{0x20a4,0x20a4,0x332a},
{0x20a5,0x20a5,0x3347},
{0x20a6,0x20a6,0x3331},
{0x20a7,0x20a7,0x7e8a},
{0x20a8,0x20a8,0x891c},
{0x20a9,0x20a9,0x9348},
{0x20aa,0x20aa,0x9288},
{0x20ab,0x20ab,0x84dc},
{0x20ac,0x20ac,0x4fc9},
{0x20ad,0x20ad,0x70bb},
{0x20ae,0x20ae,0x6631},
{0x20af,0x20af,0x68c8},
{0x20b0,0x20b0,0x92f9},
{0x20b1,0x20b1,0x66fb},
{0x20b2,0x20b2,0x5f45},
{0x20b3,0x20b3,0x4e28},
{0x20b4,0x20b4,0x4ee1},
{0x20b5,0x20b5,0x4efc},
{0x20b6,0x20b6,0x4f00},
{0x20b7,0x20b7,0x4f03},
{0x20b8,0x20b8,0x4f39},
{0x20b9,0x20b9,0x4f56},
{0x20ba,0x20ba,0x4f92},
{0x20bb,0x20bb,0x4f8a},
{0x20bc,0x20bc,0x4f9a},
{0x20bd,0x20bd,0x4f94},
{0x20be,0x20be,0x4fcd},
{0x20bf,0x20bf,0x5040},
{0x20c0,0x20c0,0x5022},
{0x20c1,0x20c1,0x4fff},
{0x20c2,0x20c2,0x501e},
{0x20c3,0x20c3,0x5046},
{0x20c4,0x20c4,0x5070},
{0x20c5,0x20c5,0x5042},
{0x20c6,0x20c6,0x5094},
{0x20c7,0x20c7,0x50f4},
{0x20c8,0x20c8,0x50d8},
{0x20c9,0x20c9,0x514a},
{0x20ca,0x20ca,0x5164},
{0x20cb,0x20cb,0x519d},
{0x20cc,0x20cc,0x51be},
{0x20cd,0x20cd,0x51ec},
{0x20ce,0x20ce,0x5215},
{0x20cf,0x20cf,0x529c},
{0x20d0,0x20d0,0x52a6},
{0x20d1,0x20d1,0x52c0},
{0x20d2,0x20d2,0x52db},
{0x20d3,0x20d3,0x5300},
{0x20d4,0x20d4,0x5307},
{0x20d5,0x20d5,0x5324},
{0x20d6,0x20d6,0x5372},
{0x20d7,0x20d7,0x5393},
{0x20d8,0x20d8,0x53b2},
{0x20d9,0x20d9,0x53dd},
{0x20da,0x20da,0xfa0e},
{0x20db,0x20db,0x549c},
{0x20dc,0x20dc,0x548a},
{0x20dd,0x20dd,0x54a9},
{0x20de,0x20de,0x54ff},
{0x20df,0x20df,0x5586},
{0x20e0,0x20e0,0x5759},
{0x20e1,0x20e1,0x5765},
{0x20e2,0x20e2,0x57ac},
{0x20e3,0x20e3,0x57c8},
{0x20e4,0x20e4,0x57c7},
{0x20e5,0x20e5,0xfa0f},
{0x20e6,0x20e6,0x585a},
{0x20e7,0x20e7,0x589e},
{0x20e8,0x20e8,0x58b2},
{0x20e9,0x20e9,0x590b},
{0x20ea,0x20ea,0x5953},
{0x20eb,0x20eb,0x595b},
{0x20ec,0x20ec,0x595d},
{0x20ed,0x20ed,0x5963},
{0x20ee,0x20ee,0x59a4},
{0x20ef,0x20ef,0x59ba},
{0x20f0,0x20f0,0x5b56},
{0x20f1,0x20f1,0x5bc0},
{0x20f2,0x20f2,0x752f},
{0x20f3,0x20f3,0x5bd8},
{0x20f4,0x20f4,0x5bdb},
{0x20f5,0x20f5,0x5c1e},
{0x20f6,0x20f6,0x5ca6},
{0x20f7,0x20f7,0x5cba},
{0x20f8,0x20f8,0x5cf5},
{0x20f9,0x20f9,0x5d27},
{0x20fa,0x20fa,0x5d53},
{0x20fb,0x20fb,0xfa11},
{0x20fc,0x20fc,0x5d42},
{0x20fd,0x20fd,0x5d6d},
{0x20fe,0x20ff,0x5db8},
{0x2100,0x2100,0x5dd0},
{0x2101,0x2101,0x5f21},
{0x2102,0x2102,0x5f34},
{0x2103,0x2103,0x5f67},
{0x2104,0x2104,0x5fb7},
{0x2105,0x2105,0x5fde},
{0x2106,0x2106,0x605d},
{0x2107,0x2107,0x6085},
{0x2108,0x2108,0x608a},
{0x2109,0x2109,0x60de},
{0x210a,0x210a,0x60d5},
{0x210b,0x210b,0x6120},
{0x210c,0x210c,0x60f2},
{0x210d,0x210d,0x6111},
{0x210e,0x210e,0x6137},
{0x210f,0x210f,0x6130},
{0x2110,0x2110,0x6198},
{0x2111,0x2111,0x6213},
{0x2112,0x2112,0x62a6},
{0x2113,0x2113,0x63f5},
{0x2114,0x2114,0x6460},
{0x2115,0x2115,0x649d},
{0x2116,0x2116,0x64ce},
{0x2117,0x2117,0x654e},
{0x2118,0x2118,0x6600},
{0x2119,0x2119,0x6615},
{0x211a,0x211a,0x6609},
{0x211b,0x211b,0x662e},
{0x211c,0x211c,0x661e},
{0x211d,0x211d,0x6624},
{0x211e,0x211e,0x6665},
{0x211f,0x211f,0x6657},
{0x2120,0x2120,0x6659},
{0x2121,0x2121,0xfa12},
{0x2122,0x2122,0x6673},
{0x2123,0x2123,0x6699},
{0x2124,0x2124,0x66a0},
{0x2125,0x2125,0x66b2},
{0x2126,0x2126,0x66bf},
{0x2127,0x2127,0x66fa},
{0x2128,0x2128,0x670e},
{0x2129,0x2129,0x6717},
{0x212a,0x212a,0x6766},
{0x212b,0x212b,0x67bb},
{0x212c,0x212c,0x6852},
{0x212d,0x212d,0x67c0},
{0x212e,0x212e,0x6801},
{0x212f,0x212f,0x6844},
{0x2130,0x2130,0x68cf},
{0x2131,0x2131,0xfa13},
{0x2132,0x2132,0x6968},
{0x2133,0x2133,0xfa14},
{0x2134,0x2134,0x6998},
{0x2135,0x2135,0x69e2},
{0x2136,0x2136,0x6a30},
{0x2137,0x2137,0x6a6b},
{0x2138,0x2138,0x6a46},
{0x2139,0x2139,0x6a73},
{0x213a,0x213a,0x6a7e},
{0x213b,0x213b,0x6ae2},
{0x213c,0x213c,0x6ae4},
{0x213d,0x213d,0x6bd6},
{0x213e,0x213e,0x6c3f},
{0x213f,0x213f,0x6c5c},
{0x2140,0x2140,0x6c86},
{0x2141,0x2141,0x6c6f},
{0x2142,0x2142,0x6cda},
{0x2143,0x2143,0x6d04},
{0x2144,0x2144,0x6d87},
{0x2145,0x2145,0x6d6f},
{0x2146,0x2146,0x6d96},
{0x2147,0x2147,0x6dac},
{0x2148,0x2148,0x6dcf},
{0x2149,0x2149,0x6df8},
{0x214a,0x214a,0x6df2},
{0x214b,0x214b,0x6dfc},
{0x214c,0x214c,0x6e39},
{0x214d,0x214d,0x6e5c},
{0x214e,0x214e,0x6e27},
{0x214f,0x214f,0x6e3c},
{0x2150,0x2150,0x6ebf},
{0x2151,0x2151,0x6f88},
{0x2152,0x2152,0x6fb5},
{0x2153,0x2153,0x6ff5},
{0x2154,0x2154,0x7005},
{0x2155,0x2155,0x7007},
{0x2156,0x2156,0x7028},
{0x2157,0x2157,0x7085},
{0x2158,0x2158,0x70ab},
{0x2159,0x2159,0x710f},
{0x215a,0x215a,0x7104},
{0x215b,0x215b,0x715c},
{0x215c,0x215d,0x7146},
{0x215f,0x215f,0x71c1},
{0x2160,0x2160,0x71fe},
{0x2161,0x2161,0x72b1},
{0x2162,0x2162,0x72be},
{0x2163,0x2163,0x7324},
{0x2164,0x2164,0xfa16},
{0x2165,0x2165,0x7377},
{0x2166,0x2166,0x73bd},
{0x2167,0x2167,0x73c9},
{0x2168,0x2168,0x73d6},
{0x2169,0x2169,0x73e3},
{0x216a,0x216a,0x73d2},
{0x216b,0x216b,0x7407},
{0x216c,0x216c,0x73f5},
{0x216d,0x216d,0x7426},
{0x216e,0x216e,0x742a},
{0x216f,0x216f,0x7429},
{0x2170,0x2170,0x742e},
{0x2171,0x2171,0x7462},
{0x2172,0x2172,0x7489},
{0x2173,0x2173,0x749f},
{0x2174,0x2174,0x7501},
{0x2175,0x2175,0x756f},
{0x2176,0x2176,0x7682},
{0x2177,0x2177,0x769c},
{0x2178,0x2178,0x769e},
{0x2179,0x2179,0x769b},
{0x217a,0x217a,0x76a6},
{0x217b,0x217b,0xfa17},
{0x217c,0x217c,0x7746},
{0x217d,0x217d,0x52af},
{0x217e,0x217e,0x7821},
{0x217f,0x217f,0x784e},
{0x2180,0x2180,0x7864},
{0x2181,0x2181,0x787a},
{0x2182,0x2182,0x7930},
{0x2183,0x2185,0xfa18},
{0x2186,0x2186,0x7994},
{0x2187,0x2187,0xfa1b},
{0x2188,0x2188,0x799b},
{0x2189,0x2189,0x7ad1},
{0x218a,0x218a,0x7ae7},
{0x218b,0x218b,0xfa1c},
{0x218c,0x218c,0x7aeb},
{0x218d,0x218d,0x7b9e},
{0x218e,0x218e,0xfa1d},
{0x218f,0x218f,0x7d48},
{0x2190,0x2190,0x7d5c},
{0x2191,0x2191,0x7db7},
{0x2192,0x2192,0x7da0},
{0x2193,0x2193,0x7dd6},
{0x2194,0x2194,0x7e52},
{0x2195,0x2195,0x7f47},
{0x2196,0x2196,0x7fa1},
{0x2197,0x2197,0xfa1e},
{0x2198,0x2198,0x8301},
{0x2199,0x2199,0x8362},
{0x219a,0x219a,0x837f},
{0x219b,0x219b,0x83c7},
{0x219c,0x219c,0x83f6},
{0x219d,0x219d,0x8448},
{0x219e,0x219e,0x84b4},
{0x219f,0x219f,0x8553},
{0x21a0,0x21a0,0x8559},
{0x21a1,0x21a1,0x856b},
{0x21a2,0x21a2,0xfa1f},
{0x21a3,0x21a3,0x85b0},
{0x21a4,0x21a5,0xfa20},
{0x21a6,0x21a6,0x8807},
{0x21a7,0x21a7,0x88f5},
{0x21a8,0x21a8,0x8a12},
{0x21a9,0x21a9,0x8a37},
{0x21aa,0x21aa,0x8a79},
{0x21ab,0x21ab,0x8aa7},
{0x21ac,0x21ac,0x8abe},
{0x21ad,0x21ad,0x8adf},
{0x21ae,0x21ae,0xfa22},
{0x21af,0x21af,0x8af6},
{0x21b0,0x21b0,0x8b53},
{0x21b1,0x21b1,0x8b7f},
{0x21b2,0x21b2,0x8cf0},
{0x21b3,0x21b3,0x8cf4},
{0x21b4,0x21b4,0x8d12},
{0x21b5,0x21b5,0x8d76},
{0x21b6,0x21b6,0xfa23},
{0x21b7,0x21b7,0x8ecf},
{0x21b8,0x21b9,0xfa24},
{0x21ba,0x21ba,0x9067},
{0x21bb,0x21bb,0x90de},
{0x21bc,0x21bc,0xfa26},
{0x21bd,0x21bd,0x9115},
{0x21be,0x21be,0x9127},
{0x21bf,0x21bf,0x91da},
{0x21c0,0x21c0,0x91d7},
{0x21c1,0x21c1,0x91de},
{0x21c2,0x21c3,0x91ed},
{0x21c4,0x21c5,0x91e4},
{0x21c6,0x21c6,0x9206},
{0x21c7,0x21c7,0x9210},
{0x21c8,0x21c8,0x920a},
{0x21c9,0x21c9,0x923a},
{0x21ca,0x21ca,0x9240},
{0x21cb,0x21cb,0x923c},
{0x21cc,0x21cc,0x924e},
{0x21cd,0x21cd,0x9259},
{0x21ce,0x21ce,0x9251},
{0x21cf,0x21cf,0x9239},
{0x21d0,0x21d0,0x9267},
{0x21d1,0x21d1,0x92a7},
{0x21d2,0x21d3,0x9277},
{0x21d4,0x21d4,0x92e7},
{0x21d5,0x21d5,0x92d7},
{0x21d6,0x21d6,0x92d9},
{0x21d7,0x21d7,0x92d0},
{0x21d8,0x21d8,0xfa27},
{0x21d9,0x21d9,0x92d5},
{0x21da,0x21da,0x92e0},
{0x21db,0x21db,0x92d3},
{0x21dc,0x21dc,0x9325},
{0x21dd,0x21dd,0x9321},
{0x21de,0x21de,0x92fb},
{0x21df,0x21df,0xfa28},
{0x21e0,0x21e0,0x931e},
{0x21e1,0x21e1,0x92ff},
{0x21e2,0x21e2,0x931d},
{0x21e3,0x21e3,0x9302},
{0x21e4,0x21e4,0x9370},
{0x21e5,0x21e5,0x9357},
{0x21e6,0x21e6,0x93a4},
{0x21e7,0x21e7,0x93c6},
{0x21e8,0x21e8,0x93de},
{0x21e9,0x21e9,0x93f8},
{0x21ea,0x21ea,0x9431},
{0x21eb,0x21eb,0x9445},
{0x21ec,0x21ec,0x9448},
{0x21ed,0x21ed,0x9592},
{0x21ee,0x21ee,0x9686},
{0x21ef,0x21ef,0xfa29},
{0x21f0,0x21f0,0x969d},
{0x21f1,0x21f1,0x96af},
{0x21f2,0x21f2,0x9733},
{0x21f3,0x21f3,0x973b},
{0x21f4,0x21f4,0x9743},
{0x21f5,0x21f5,0x974d},
{0x21f6,0x21f6,0x974f},
{0x21f7,0x21f7,0x9751},
{0x21f8,0x21f8,0x9755},
{0x21f9,0x21f9,0x9857},
{0x21fa,0x21fa,0x9865},
{0x21fb,0x21fc,0xfa2a},
{0x21fd,0x21fd,0x9927},
{0x21fe,0x21fe,0xfa2c},
{0x21ff,0x21ff,0x999e},
{0x2200,0x2200,0x9a4e},
{0x2201,0x2201,0x9ad9},
{0x2202,0x2202,0x9adc},
{0x2203,0x2203,0x9b75},
{0x2204,0x2204,0x9b72},
{0x2205,0x2205,0x9b8f},
{0x2206,0x2206,0x9bb1},
{0x2207,0x2207,0x9bbb},
{0x2208,0x2208,0x9c00},
{0x2209,0x2209,0x9d70},
{0x220a,0x220a,0x9d6b},
{0x220b,0x220b,0xfa2d},
{0x220c,0x220c,0x9e19},
{0x220d,0x220d,0x9ed1},
{0x220e,0x220e,0xac},
{0x220f,0x220f,0x5c},
{0x2210,0x224b,0x20},
{0x224c,0x224c,0xa5},
{0x224d,0x226b,0x5d},
{0x226c,0x226c,0xa6},
{0x226d,0x226d,0x7d},
{0x226e,0x226e,0x303},
{0x226f,0x226f,0x2019},
{0x2270,0x2270,0x5c},
{0x2271,0x2271,0x2018},
{0x2272,0x2272,0x7c},
{0x2273,0x2273,0x7e},
{0x2274,0x2276,0xa1},
{0x2277,0x2277,0x2044},
{0x2278,0x2278,0x192},
{0x2279,0x2279,0xa7},
{0x227a,0x227a,0xa4},
{0x227b,0x227b,0x201c},
{0x227c,0x227c,0xab},
{0x227d,0x227e,0x2039},
{0x227f,0x2280,0xfb01},
{0x2281,0x2281,0x2012},
{0x2282,0x2283,0x2020},
{0x2284,0x2284,0xb7},
{0x2285,0x2285,0xb6},
{0x2286,0x2286,0x2022},
{0x2287,0x2287,0x201a},
{0x2288,0x2288,0x201e},
{0x2289,0x2289,0x201d},
{0x228a,0x228a,0xbb},
{0x228b,0x228b,0x2026},
{0x228c,0x228c,0x2030},
{0x228d,0x228d,0xbf},
{0x228e,0x228f,0x301},
{0x2290,0x2290,0xaf},
{0x2291,0x2293,0x306},
{0x2294,0x2294,0x30a},
{0x2295,0x2295,0xb8},
{0x2296,0x2296,0x30b},
{0x2297,0x2297,0x328},
{0x2298,0x2298,0x30c},
{0x2299,0x2299,0x336},
{0x229a,0x229a,0xc6},
{0x229b,0x229b,0xaa},
{0x229c,0x229c,0x141},
{0x229d,0x229d,0xd8},
{0x229e,0x229e,0x152},
{0x229f,0x229f,0xba},
{0x22a0,0x22a0,0xe6},
{0x22a1,0x22a1,0x131},
{0x22a2,0x22a2,0x142},
{0x22a3,0x22a3,0xf8},
{0x22a4,0x22a4,0x153},
{0x22a5,0x22a5,0xdf},
{0x22a6,0x22a6,0x2d},
{0x22a7,0x22a7,0xa9},
{0x22a8,0x22a8,0xac},
{0x22a9,0x22a9,0xae},
{0x22aa,0x22ad,0xb0},
{0x22ae,0x22ae,0xb5},
{0x22af,0x22af,0xb9},
{0x22b0,0x22b2,0xbc},
{0x22b3,0x22b8,0xc0},
{0x22b9,0x22c9,0xc7},
{0x22ca,0x22cf,0xd9},
{0x22d0,0x22d5,0xe0},
{0x22d6,0x22e6,0xe7},
{0x22e7,0x22ed,0xf9},
{0x22ee,0x22ee,0x160},
{0x22ef,0x22ef,0x178},
{0x22f0,0x22f0,0x17d},
{0x22f1,0x22f1,0x305},
{0x22f2,0x22f2,0x161},
{0x22f3,0x22f3,0x2122},
{0x22f4,0x22f4,0x17e},
{0x22f5,0x22f5,0x30},
{0x22f6,0x22f6,0x2002},
{0x22f7,0x2331,0x21},
{0x2332,0x2332,0xa5},
{0x2333,0x2353,0x5d},
{0x2354,0x2354,0x203e},
{0x2355,0x2355,0xff40},
{0x2356,0x2356,0x2032},
{0x2357,0x2357,0xa8},
{0x2358,0x2358,0x2036},
{0x2359,0x2359,0xc4},
{0x235a,0x235a,0xf9},
{0x235b,0x235b,0xe9},
{0x235c,0x235c,0xed},
{0x235d,0x235d,0xdf},
{0x235e,0x235e,0xe7},
{0x235f,0x235f,0xc7},
{0x2360,0x2360,0xd1},
{0x2361,0x2361,0xf1},
{0x2362,0x2363,0xa2},
{0x2364,0x2364,0xf3},
{0x2365,0x2365,0xfa},
{0x2366,0x2366,0xa1},
{0x2367,0x2367,0xbf},
{0x2368,0x2368,0xbd},
{0x2369,0x2369,0xd6},
{0x236a,0x236a,0xdc},
{0x236b,0x236b,0xe4},
{0x236c,0x236c,0xeb},
{0x236d,0x236d,0xef},
{0x236e,0x236e,0xf6},
{0x236f,0x236f,0xdc},
{0x2370,0x2370,0xe2},
{0x2371,0x2371,0xea},
{0x2372,0x2372,0xee},
{0x2373,0x2373,0xf4},
{0x2374,0x2374,0xfc},
{0x2375,0x2375,0xe0},
{0x2376,0x2376,0xe9},
{0x2377,0x2377,0xe1},
{0x2378,0x2378,0x7e},
{0x2379,0x2379,0x30},
{0x237a,0x237a,0xac},
{0x237b,0x237b,0x5c},
{0x237c,0x23bb,0xff60},
{0x23bc,0x23bd,0x30f0},
{0x23be,0x23be,0x30ee},
{0x23bf,0x23bf,0x30ab},
{0x23c0,0x23c0,0x30b1},
{0x23c1,0x23c1,0x30f4},
{0x23c2,0x23c2,0x30ac},
{0x23c3,0x23c3,0x30ae},
{0x23c4,0x23c4,0x30b0},
{0x23c5,0x23c5,0x30b2},
{0x23c6,0x23c6,0x30b4},
{0x23c7,0x23c7,0x30b6},
{0x23c8,0x23c8,0x30b8},
{0x23c9,0x23c9,0x30ba},
{0x23ca,0x23ca,0x30bc},
{0x23cb,0x23cb,0x30be},
{0x23cc,0x23cc,0x30c0},
{0x23cd,0x23cd,0x30c2},
{0x23ce,0x23ce,0x30c5},
{0x23cf,0x23cf,0x30c7},
{0x23d0,0x23d0,0x30c9},
{0x23d1,0x23d2,0x30d0},
{0x23d3,0x23d4,0x30d3},
{0x23d5,0x23d6,0x30d6},
{0x23d7,0x23d8,0x30d9},
{0x23d9,0x23da,0x30dc},
{0x23db,0x23db,0xff60},
{0x23dc,0x23dc,0x3092},
{0x23dd,0x23dd,0x3041},
{0x23de,0x23de,0x3043},
{0x23df,0x23df,0x3045},
{0x23e0,0x23e0,0x3047},
{0x23e1,0x23e1,0x3049},
{0x23e2,0x23e2,0x3083},
{0x23e3,0x23e3,0x3085},
{0x23e4,0x23e4,0x3087},
{0x23e5,0x23e5,0x3063},
{0x23e6,0x23e6,0x3042},
{0x23e7,0x23e7,0x3044},
{0x23e8,0x23e8,0x3046},
{0x23e9,0x23e9,0x3048},
{0x23ea,0x23eb,0x304a},
{0x23ec,0x23ec,0x304d},
{0x23ed,0x23ed,0x304f},
{0x23ee,0x23ee,0x3051},
{0x23ef,0x23ef,0x3053},
{0x23f0,0x23f0,0x3055},
{0x23f1,0x23f1,0x3057},
{0x23f2,0x23f2,0x3059},
{0x23f3,0x23f3,0x305b},
{0x23f4,0x23f4,0x305d},
{0x23f5,0x23f5,0x305f},
{0x23f6,0x23f6,0x3061},
{0x23f7,0x23f7,0x3064},
{0x23f8,0x23f8,0x3066},
{0x23f9,0x23f9,0x3068},
{0x23fa,0x23ff,0x306a},
{0x2400,0x2400,0x3072},
{0x2401,0x2401,0x3075},
{0x2402,0x2402,0x3078},
{0x2403,0x2403,0x307b},
{0x2404,0x2408,0x307e},
{0x2409,0x2409,0x3084},
{0x240a,0x240a,0x3086},
{0x240b,0x2410,0x3088},
{0x2411,0x2411,0x308f},
{0x2412,0x2412,0x3093},
{0x2413,0x2414,0x3090},
{0x2415,0x2415,0x308e},
{0x2416,0x2416,0x304c},
{0x2417,0x2417,0x304e},
{0x2418,0x2418,0x3050},
{0x2419,0x2419,0x3052},
{0x241a,0x241a,0x3054},
{0x241b,0x241b,0x3056},
{0x241c,0x241c,0x3068},
{0x241d,0x241d,0x305a},
{0x241e,0x241e,0x305c},
{0x241f,0x241f,0x305e},
{0x2420,0x2420,0x3060},
{0x2421,0x2421,0x3062},
{0x2422,0x2422,0x3065},
{0x2423,0x2423,0x3067},
{0x2424,0x2424,0x3069},
{0x2425,0x2426,0x3070},
{0x2427,0x2428,0x3073},
{0x2429,0x242a,0x3076},
{0x242b,0x242c,0x3079},
{0x242d,0x242e,0x307c},
{0x242f,0x2430,0x301d},
{0x2431,0x2431,0x5b},
{0x2432,0x2432,0x5d},
{0x2433,0x2436,0x3008},
{0x2437,0x243a,0x300e},
{0x243b,0x243b,0x2012},
{0x243c,0x243c,0xff60},
{0x243d,0x2488,0x2500},
{0x2489,0x2489,0x25b2},
{0x248a,0x248a,0x20ac},
{0x248b,0x248b,0x2126},
{0x248c,0x248d,0x2032},
{0x248e,0x248e,0xfb00},
{0x248f,0x2490,0xfb03},
{0x2491,0x2491,0x101},
{0x2492,0x2492,0x12b},
{0x2493,0x2493,0x16b},
{0x2494,0x2494,0x113},
{0x2495,0x2495,0x14d},
{0x2496,0x2496,0x100},
{0x2497,0x2497,0x12a},
{0x2498,0x2498,0x16a},
{0x2499,0x2499,0x112},
{0x249a,0x249a,0x14c},
{0x249b,0x249e,0x215b},
{0x249f,0x24a0,0x2153},
{0x24a1,0x24a1,0x2070},
{0x24a2,0x24a7,0x2074},
{0x24a8,0x24b1,0x2080},
{0x24b2,0x24b2,0x1cd},
{0x24b3,0x24b3,0x11a},
{0x24b5,0x24b5,0x1ebc},
{0x24b6,0x24b6,0x1cf},
{0x24b8,0x24b8,0x128},
{0x24b9,0x24b9,0x1d1},
{0x24bb,0x24bb,0x1d3},
{0x24bc,0x24bc,0x16e},
{0x24bd,0x24bd,0x168},
{0x24be,0x24be,0x1ce},
{0x24bf,0x24bf,0x11b},
{0x24c1,0x24c1,0x1ebd},
{0x24c2,0x24c2,0x1d0},
{0x24c4,0x24c4,0x129},
{0x24c5,0x24c5,0x1d2},
{0x24c7,0x24c7,0x1d4},
{0x24c8,0x24c8,0x16f},
{0x24c9,0x24c9,0x169},
{0x24ca,0x24ca,0x251},
{0x24cb,0x24cb,0x1f71},
{0x24cc,0x24cc,0x1f70},
{0x24cd,0x24cd,0x1fd},
{0x24ce,0x24ce,0xe6},
{0x24cf,0x24cf,0x254},
{0x24d0,0x24d0,0x254},
{0x24d1,0x24d1,0x254},
{0x24d2,0x24d2,0x259},
{0x24d3,0x24d3,0x259},
{0x24d4,0x24d5,0x259},
{0x24d6,0x24d6,0x25a},
{0x24d7,0x24d8,0x25a},
{0x24d9,0x24d9,0x1f73},
{0x24da,0x24da,0x1f72},
{0x24db,0x24db,0x237},
{0x24dc,0x24dc,0x14b},
{0x24dd,0x24dd,0x275},
{0x24de,0x24de,0x28c},
{0x24df,0x24df,0x28c},
{0x24e0,0x24e0,0x28c},
{0x24e1,0x24e1,0x292},
{0x24e2,0x24e2,0x283},
{0x24e3,0x24e3,0x2d0},
{0x24e4,0x251f,0x20},
{0x2520,0x2520,0xa5},
{0x2521,0x253f,0x5d},
{0x2540,0x2540,0xa6},
{0x2541,0x2541,0x7d},
{0x2542,0x2542,0x303},
{0x2543,0x2543,0x2019},
{0x2544,0x2544,0x5c},
{0x2545,0x2545,0x2018},
{0x2546,0x2546,0x7c},
{0x2547,0x2547,0x7e},
{0x2548,0x254a,0xa1},
{0x254b,0x254b,0x2044},
{0x254c,0x254c,0x192},
{0x254d,0x254d,0xa7},
{0x254e,0x254e,0xa4},
{0x254f,0x254f,0x201c},
{0x2550,0x2550,0xab},
{0x2551,0x2552,0x2039},
{0x2553,0x2554,0xfb01},
{0x2555,0x2555,0x2012},
{0x2556,0x2557,0x2020},
{0x2558,0x2558,0xb7},
{0x2559,0x2559,0xb6},
{0x255a,0x255a,0x2022},
{0x255b,0x255b,0x201a},
{0x255c,0x255c,0x201e},
{0x255d,0x255d,0x201d},
{0x255e,0x255e,0xbb},
{0x255f,0x255f,0x2026},
{0x2560,0x2560,0x2030},
{0x2561,0x2561,0xbf},
{0x2562,0x2563,0x301},
{0x2564,0x2564,0xaf},
{0x2565,0x2567,0x306},
{0x2568,0x2568,0x30a},
{0x2569,0x2569,0xb8},
{0x256a,0x256a,0x30b},
{0x256b,0x256b,0x328},
{0x256c,0x256c,0x30c},
{0x256d,0x256d,0x336},
{0x256e,0x256e,0xc6},
{0x256f,0x256f,0xaa},
{0x2570,0x2570,0x141},
{0x2571,0x2571,0xd8},
{0x2572,0x2572,0x152},
{0x2573,0x2573,0xba},
{0x2574,0x2574,0xe6},
{0x2575,0x2575,0x131},
{0x2576,0x2576,0x142},
{0x2577,0x2577,0xf8},
{0x2578,0x2578,0x153},
{0x2579,0x2579,0xdf},
{0x257a,0x257a,0x2d},
{0x257b,0x257b,0xa9},
{0x257c,0x257c,0xac},
{0x257d,0x257d,0xae},
{0x257e,0x2581,0xb0},
{0x2582,0x2582,0xb5},
{0x2583,0x2583,0xb9},
{0x2584,0x2586,0xbc},
{0x2587,0x258c,0xc0},
{0x258d,0x259d,0xc7},
{0x259e,0x25a3,0xd9},
{0x25a4,0x25a9,0xe0},
{0x25aa,0x25ba,0xe7},
{0x25bb,0x25c1,0xf9},
{0x25c2,0x25c2,0x160},
{0x25c3,0x25c3,0x178},
{0x25c4,0x25c4,0x17d},
{0x25c5,0x25c5,0x305},
{0x25c6,0x25c6,0x161},
{0x25c7,0x25c7,0x2122},
{0x25c8,0x25c8,0x17e},
{0x25c9,0x25c9,0x30},
{0x25ca,0x25ca,0x20ac},
{0x25cb,0x25cb,0x2126},
{0x25cc,0x25cd,0x2032},
{0x25ce,0x25ce,0xfb00},
{0x25cf,0x25d0,0xfb03},
{0x25d1,0x25d1,0x101},
{0x25d2,0x25d2,0x12b},
{0x25d3,0x25d3,0x16b},
{0x25d4,0x25d4,0x113},
{0x25d5,0x25d5,0x14d},
{0x25d6,0x25d6,0x100},
{0x25d7,0x25d7,0x12a},
{0x25d8,0x25d8,0x16a},
{0x25d9,0x25d9,0x112},
{0x25da,0x25da,0x14c},
{0x25db,0x25de,0x215b},
{0x25df,0x25e0,0x2153},
{0x25e1,0x25e1,0x2070},
{0x25e2,0x25e7,0x2074},
{0x25e8,0x25f1,0x2080},
{0x25f2,0x25f2,0x1cd},
{0x25f3,0x25f3,0x11a},
{0x25f5,0x25f5,0x1ebc},
{0x25f6,0x25f6,0x1cf},
{0x25f8,0x25f8,0x128},
{0x25f9,0x25f9,0x1d1},
{0x25fb,0x25fb,0x1d3},
{0x25fc,0x25fc,0x16e},
{0x25fd,0x25fd,0x168},
{0x25fe,0x25fe,0x1ce},
{0x25ff,0x25ff,0x11b},
{0x2601,0x2601,0x1ebd},
{0x2602,0x2602,0x1d0},
{0x2604,0x2604,0x129},
{0x2605,0x2605,0x1d2},
{0x2607,0x2607,0x1d4},
{0x2608,0x2608,0x16f},
{0x2609,0x2609,0x169},
{0x260a,0x2613,0x30},
{0x2614,0x2614,0x336},
{0x2615,0x2615,0x2d},
{0x2616,0x2616,0x3d},
{0x2617,0x2617,0x2c},
{0x2618,0x2619,0x28},
{0x261a,0x261b,0x2e},
{0x261c,0x261d,0x3a},
{0x261e,0x2627,0x30},
{0x2628,0x2628,0x336},
{0x2629,0x2629,0x2d},
{0x262a,0x262a,0x3d},
{0x262b,0x262b,0x2c},
{0x262c,0x262d,0x28},
{0x262e,0x262f,0x2e},
{0x2630,0x2631,0x3a},
{0x2632,0x2632,0xb7},
{0x2633,0x2633,0x20ac},
{0x2635,0x2636,0x2153},
{0x2637,0x2637,0xbe},
{0x2638,0x263d,0x2155},
{0x2644,0x2647,0x215b},
{0x2690,0x2699,0x30},
{0x269a,0x269a,0x30},
{0x269b,0x269b,0x2163},
{0x26f6,0x2701,0x2170},
{0x2705,0x2710,0x2160},
{0x2714,0x272d,0x41},
{0x272e,0x272e,0x3042},
{0x272f,0x272f,0x3044},
{0x2730,0x2730,0x3046},
{0x2731,0x2731,0x3048},
{0x2732,0x2733,0x304a},
{0x2734,0x2734,0x304d},
{0x2735,0x2735,0x304f},
{0x2736,0x2736,0x3051},
{0x2737,0x2737,0x3053},
{0x2738,0x2738,0x3055},
{0x2739,0x2739,0x3057},
{0x273a,0x273a,0x3059},
{0x273b,0x273b,0x305b},
{0x273c,0x273c,0x305d},
{0x273d,0x273d,0x305f},
{0x273e,0x273e,0x3061},
{0x273f,0x273f,0x3064},
{0x2740,0x2740,0x3066},
{0x2741,0x2741,0x3068},
{0x2742,0x2747,0x306a},
{0x2748,0x2748,0x3072},
{0x2749,0x2749,0x3075},
{0x274a,0x274a,0x3078},
{0x274b,0x274b,0x307b},
{0x274c,0x2750,0x307e},
{0x2751,0x2751,0x3084},
{0x2752,0x2752,0x3086},
{0x2753,0x2758,0x3088},
{0x2759,0x275d,0x308f},
{0x275e,0x275e,0x30a2},
{0x275f,0x275f,0x30a4},
{0x2760,0x2760,0x30a6},
{0x2761,0x2761,0x30a8},
{0x2762,0x2763,0x30aa},
{0x2764,0x2764,0x30ad},
{0x2765,0x2765,0x30af},
{0x2766,0x2766,0x30b1},
{0x2767,0x2767,0x30b3},
{0x2768,0x2768,0x30b5},
{0x2769,0x2769,0x30b7},
{0x276a,0x276a,0x30b9},
{0x276b,0x276b,0x30bb},
{0x276c,0x276c,0x30bd},
{0x276d,0x276d,0x30bf},
{0x276e,0x276e,0x30c1},
{0x276f,0x276f,0x30c4},
{0x2770,0x2770,0x30c6},
{0x2771,0x2771,0x30c8},
{0x2772,0x2777,0x30ca},
{0x2778,0x2778,0x30d2},
{0x2779,0x2779,0x30d5},
{0x277a,0x277a,0x30d8},
{0x277b,0x277b,0x30db},
{0x277c,0x2780,0x30de},
{0x2781,0x2781,0x30e4},
{0x2782,0x2782,0x30e6},
{0x2783,0x2788,0x30e8},
{0x2789,0x278d,0x30ef},
{0x278e,0x2797,0x3220},
{0x27a2,0x27a2,0x55b6},
{0x27a3,0x27a3,0x5408},
{0x27a4,0x27a4,0x6ceb},
{0x27a5,0x27a5,0x554f},
{0x27a6,0x27a6,0x7b54},
{0x27a7,0x27a7,0x4f8b},
{0x27a8,0x27b1,0x30},
{0x27b2,0x27bb,0x30},
{0x27bc,0x27bc,0x3063},
{0x27bd,0x27bd,0x624d},
{0x27be,0x27be,0x3007},
{0x27bf,0x27bf,0x4e00},
{0x27c0,0x27c0,0x4e8c},
{0x27c1,0x27c1,0x4e09},
{0x27c2,0x27c2,0x56db},
{0x27c3,0x27c3,0x4e94},
{0x27c4,0x27c4,0x516d},
{0x27c5,0x27c5,0x4e03},
{0x27c6,0x27c6,0x516b},
{0x27c7,0x27c7,0x4e5d},
{0x27c8,0x27c8,0x5341},
{0x27c9,0x27c9,0x3007},
{0x27ca,0x27ca,0x4e00},
{0x27cb,0x27cb,0x4e8c},
{0x27cc,0x27cc,0x4e09},
{0x27cd,0x27cd,0x56db},
{0x27ce,0x27ce,0x4e94},
{0x27cf,0x27cf,0x516d},
{0x27d0,0x27d0,0x4e03},
{0x27d1,0x27d1,0x516b},
{0x27d2,0x27d2,0x4e5d},
{0x27d3,0x27d3,0x5341},
{0x27d4,0x27d4,0x3007},
{0x27d5,0x27d5,0x4e00},
{0x27d6,0x27d6,0x4e8c},
{0x27d7,0x27d7,0x4e09},
{0x27d8,0x27d8,0x56db},
{0x27d9,0x27d9,0x4e94},
{0x27da,0x27da,0x516d},
{0x27db,0x27db,0x4e03},
{0x27dc,0x27dc,0x516b},
{0x27dd,0x27dd,0x4e5d},
{0x27de,0x27de,0x3064},
{0x27df,0x27df,0x624d},
{0x27e0,0x27e0,0x4e00},
{0x27e1,0x27e1,0x4e8c},
{0x27e2,0x27e2,0x4e09},
{0x27e3,0x27e3,0x56db},
{0x27e4,0x27e4,0x4e94},
{0x27e5,0x27e5,0x516d},
{0x27e6,0x27e6,0x4e03},
{0x27e7,0x27e7,0x516b},
{0x27e8,0x27e8,0x4e5d},
{0x27e9,0x27e9,0x5341},
{0x27ea,0x27ea,0x3007},
{0x27eb,0x27eb,0x4e00},
{0x27ec,0x27ec,0x4e8c},
{0x27ed,0x27ed,0x4e09},
{0x27ee,0x27ee,0x56db},
{0x27ef,0x27ef,0x4e94},
{0x27f0,0x27f0,0x516d},
{0x27f1,0x27f1,0x4e03},
{0x27f2,0x27f2,0x516b},
{0x27f3,0x27f3,0x4e5d},
{0x27f4,0x27f4,0x5341},
{0x27f5,0x27f5,0x30b3},
{0x27f6,0x27f6,0x3063},
{0x27f7,0x27f7,0x30c3},
{0x27f8,0x27f8,0x30a9},
{0x27fb,0x2803,0x2460},
{0x2849,0x2862,0x24d0},
{0x2863,0x287c,0x24b6},
{0x287d,0x287d,0x3042},
{0x287e,0x287e,0x3044},
{0x287f,0x287f,0x3046},
{0x2880,0x2880,0x3048},
{0x2881,0x2882,0x304a},
{0x2883,0x2883,0x304d},
{0x2884,0x2884,0x304f},
{0x2885,0x2885,0x3051},
{0x2886,0x2886,0x3053},
{0x2887,0x2887,0x3055},
{0x2888,0x2888,0x3057},
{0x2889,0x2889,0x3059},
{0x288a,0x288a,0x305b},
{0x288b,0x288b,0x305d},
{0x288c,0x288c,0x305f},
{0x288d,0x288d,0x3061},
{0x288e,0x288e,0x3064},
{0x288f,0x288f,0x3066},
{0x2890,0x2890,0x3068},
{0x2891,0x2896,0x306a},
{0x2897,0x2897,0x3072},
{0x2898,0x2898,0x3075},
{0x2899,0x2899,0x3078},
{0x289a,0x289a,0x307b},
{0x289b,0x289f,0x307e},
{0x28a0,0x28a0,0x3084},
{0x28a1,0x28a1,0x3086},
{0x28a2,0x28a7,0x3088},
{0x28a8,0x28ac,0x308f},
{0x28ad,0x28db,0x32d0},
{0x28dc,0x28dc,0x30f3},
{0x28dd,0x28e6,0x3280},
{0x28e7,0x28e7,0x3290},
{0x28e8,0x28ed,0x328a},
{0x28ee,0x28ee,0x8abf},
{0x28ef,0x28ef,0x329f},
{0x28f0,0x28f0,0x526f},
{0x28f1,0x28f1,0x6e1b},
{0x28f2,0x28f2,0x6a19},
{0x28f3,0x28f3,0x6b20},
{0x28f4,0x28f4,0x57fa},
{0x28f5,0x28f5,0x7981},
{0x28f6,0x28f7,0x32a0},
{0x28f8,0x28f8,0x329b},
{0x28f9,0x28f9,0x329a},
{0x28fa,0x28fa,0x32a3},
{0x28fb,0x28fb,0x32a2},
{0x28fc,0x28fc,0x3297},
{0x28fd,0x28fd,0x51fa},
{0x28fe,0x28fe,0x329c},
{0x28ff,0x28ff,0x3295},
{0x2900,0x2900,0x6e08},
{0x2901,0x2901,0x5897},
{0x2902,0x2902,0x554f},
{0x2903,0x2903,0x7b54},
{0x2904,0x2904,0x4f8b},
{0x2905,0x2905,0x96fb},
{0x2906,0x2906,0x25cc},
{0x2907,0x2907,0x24ff},
{0x296d,0x2986,0x61},
{0x2987,0x29a0,0x41},
{0x29a1,0x29a1,0x3042},
{0x29a2,0x29a2,0x3044},
{0x29a3,0x29a3,0x3046},
{0x29a4,0x29a4,0x3048},
{0x29a5,0x29a6,0x304a},
{0x29a7,0x29a7,0x304d},
{0x29a8,0x29a8,0x304f},
{0x29a9,0x29a9,0x3051},
{0x29aa,0x29aa,0x3053},
{0x29ab,0x29ab,0x3055},
{0x29ac,0x29ac,0x3057},
{0x29ad,0x29ad,0x3059},
{0x29ae,0x29ae,0x305b},
{0x29af,0x29af,0x305d},
{0x29b0,0x29b0,0x305f},
{0x29b1,0x29b1,0x3061},
{0x29b2,0x29b2,0x3064},
{0x29b3,0x29b3,0x3066},
{0x29b4,0x29b4,0x3068},
{0x29b5,0x29ba,0x306a},
{0x29bb,0x29bb,0x3072},
{0x29bc,0x29bc,0x3075},
{0x29bd,0x29bd,0x3078},
{0x29be,0x29be,0x307b},
{0x29bf,0x29c3,0x307e},
{0x29c4,0x29c4,0x3084},
{0x29c5,0x29c5,0x3086},
{0x29c6,0x29cb,0x3088},
{0x29cc,0x29d0,0x308f},
{0x29d1,0x29d1,0x30a2},
{0x29d2,0x29d2,0x30a4},
{0x29d3,0x29d3,0x30a6},
{0x29d4,0x29d4,0x30a8},
{0x29d5,0x29d6,0x30aa},
{0x29d7,0x29d7,0x30ad},
{0x29d8,0x29d8,0x30af},
{0x29d9,0x29d9,0x30b1},
{0x29da,0x29da,0x30b3},
{0x29db,0x29db,0x30b5},
{0x29dc,0x29dc,0x30b7},
{0x29dd,0x29dd,0x30b9},
{0x29de,0x29de,0x30bb},
{0x29df,0x29df,0x30bd},
{0x29e0,0x29e0,0x30bf},
{0x29e1,0x29e1,0x30c1},
{0x29e2,0x29e2,0x30c4},
{0x29e3,0x29e3,0x30c6},
{0x29e4,0x29e4,0x30c8},
{0x29e5,0x29ea,0x30ca},
{0x29eb,0x29eb,0x30d2},
{0x29ec,0x29ec,0x30d5},
{0x29ed,0x29ed,0x30d8},
{0x29ee,0x29ee,0x30db},
{0x29ef,0x29f3,0x30de},
{0x29f4,0x29f4,0x30e4},
{0x29f5,0x29f5,0x30e6},
{0x29f6,0x29fb,0x30e8},
{0x29fc,0x2a00,0x30ef},
{0x2a01,0x2a01,0x65e5},
{0x2a02,0x2a02,0x6708},
{0x2a03,0x2a03,0x706b},
{0x2a04,0x2a04,0x6c34},
{0x2a05,0x2a05,0x6728},
{0x2a06,0x2a06,0x91d1},
{0x2a07,0x2a07,0x571f},
{0x2a08,0x2a08,0x554f},
{0x2a09,0x2a09,0x7b54},
{0x2a0a,0x2a0a,0x4f8b},
{0x2a0b,0x2a0b,0x25cf},
{0x2a0c,0x2a0c,0x30},
{0x2a0e,0x2a0e,0x31},
{0x2a10,0x2a10,0x32},
{0x2a12,0x2a12,0x33},
{0x2a14,0x2a14,0x34},
{0x2a16,0x2a16,0x35},
{0x2a18,0x2a18,0x36},
{0x2a1a,0x2a1a,0x37},
{0x2a1c,0x2a1c,0x38},
{0x2a1e,0x2a1e,0x39},
{0x2a7b,0x2a94,0x61},
{0x2a95,0x2aae,0x41},
{0x2aaf,0x2aaf,0x3042},
{0x2ab0,0x2ab0,0x3044},
{0x2ab1,0x2ab1,0x3046},
{0x2ab2,0x2ab2,0x3048},
{0x2ab3,0x2ab4,0x304a},
{0x2ab5,0x2ab5,0x304d},
{0x2ab6,0x2ab6,0x304f},
{0x2ab7,0x2ab7,0x3051},
{0x2ab8,0x2ab8,0x3053},
{0x2ab9,0x2ab9,0x3055},
{0x2aba,0x2aba,0x3057},
{0x2abb,0x2abb,0x3059},
{0x2abc,0x2abc,0x305b},
{0x2abd,0x2abd,0x305d},
{0x2abe,0x2abe,0x305f},
{0x2abf,0x2abf,0x3061},
{0x2ac0,0x2ac0,0x3064},
{0x2ac1,0x2ac1,0x3066},
{0x2ac2,0x2ac2,0x3068},
{0x2ac3,0x2ac8,0x306a},
{0x2ac9,0x2ac9,0x3072},
{0x2aca,0x2aca,0x3075},
{0x2acb,0x2acb,0x3078},
{0x2acc,0x2acc,0x307b},
{0x2acd,0x2ad1,0x307e},
{0x2ad2,0x2ad2,0x3084},
{0x2ad3,0x2ad3,0x3086},
{0x2ad4,0x2ad9,0x3088},
{0x2ada,0x2ade,0x308f},
{0x2adf,0x2adf,0x30a2},
{0x2ae0,0x2ae0,0x30a4},
{0x2ae1,0x2ae1,0x30a6},
{0x2ae2,0x2ae2,0x30a8},
{0x2ae3,0x2ae4,0x30aa},
{0x2ae5,0x2ae5,0x30ad},
{0x2ae6,0x2ae6,0x30af},
{0x2ae7,0x2ae7,0x30b1},
{0x2ae8,0x2ae8,0x30b3},
{0x2ae9,0x2ae9,0x30b5},
{0x2aea,0x2aea,0x30b7},
{0x2aeb,0x2aeb,0x30b9},
{0x2aec,0x2aec,0x30bb},
{0x2aed,0x2aed,0x30bd},
{0x2aee,0x2aee,0x30bf},
{0x2aef,0x2aef,0x30c1},
{0x2af0,0x2af0,0x30c4},
{0x2af1,0x2af1,0x30c6},
{0x2af2,0x2af2,0x30c8},
{0x2af3,0x2af8,0x30ca},
{0x2af9,0x2af9,0x30d2},
{0x2afa,0x2afa,0x30d5},
{0x2afb,0x2afb,0x30d8},
{0x2afc,0x2afc,0x30db},
{0x2afd,0x2b01,0x30de},
{0x2b02,0x2b02,0x30e4},
{0x2b03,0x2b03,0x30e6},
{0x2b04,0x2b09,0x30e8},
{0x2b0a,0x2b0e,0x30ef},
{0x2b0f,0x2b0f,0x65e5},
{0x2b10,0x2b10,0x6708},
{0x2b11,0x2b11,0x706b},
{0x2b12,0x2b12,0x6c34},
{0x2b13,0x2b13,0x6728},
{0x2b14,0x2b14,0x91d1},
{0x2b15,0x2b15,0x571f},
{0x2b16,0x2b16,0x8ca0},
{0x2b17,0x2b17,0x52dd},
{0x2b18,0x2b18,0x554f},
{0x2b19,0x2b19,0x7b54},
{0x2b1a,0x2b1a,0x4f8b},
{0x2b1b,0x2b1b,0x20de},
{0x2b1c,0x2b1c,0x25a1},
{0x2b1d,0x2b1d,0x30},
{0x2b1f,0x2b1f,0x31},
{0x2b21,0x2b21,0x32},
{0x2b23,0x2b23,0x33},
{0x2b25,0x2b25,0x34},
{0x2b27,0x2b27,0x35},
{0x2b29,0x2b29,0x36},
{0x2b2b,0x2b2b,0x37},
{0x2b2d,0x2b2d,0x38},
{0x2b2f,0x2b2f,0x39},
{0x2b8c,0x2ba5,0x61},
{0x2ba6,0x2bbf,0x41},
{0x2bc0,0x2bc0,0x3042},
{0x2bc1,0x2bc1,0x3044},
{0x2bc2,0x2bc2,0x3046},
{0x2bc3,0x2bc3,0x3048},
{0x2bc4,0x2bc5,0x304a},
{0x2bc6,0x2bc6,0x304d},
{0x2bc7,0x2bc7,0x304f},
{0x2bc8,0x2bc8,0x3051},
{0x2bc9,0x2bc9,0x3053},
{0x2bca,0x2bca,0x3055},
{0x2bcb,0x2bcb,0x3057},
{0x2bcc,0x2bcc,0x3059},
{0x2bcd,0x2bcd,0x305b},
{0x2bce,0x2bce,0x305d},
{0x2bcf,0x2bcf,0x305f},
{0x2bd0,0x2bd0,0x3061},
{0x2bd1,0x2bd1,0x3064},
{0x2bd2,0x2bd2,0x3066},
{0x2bd3,0x2bd3,0x3068},
{0x2bd4,0x2bd9,0x306a},
{0x2bda,0x2bda,0x3072},
{0x2bdb,0x2bdb,0x3075},
{0x2bdc,0x2bdc,0x3078},
{0x2bdd,0x2bdd,0x307b},
{0x2bde,0x2be2,0x307e},
{0x2be3,0x2be3,0x3084},
{0x2be4,0x2be4,0x3086},
{0x2be5,0x2bea,0x3088},
{0x2beb,0x2bef,0x308f},
{0x2bf0,0x2bf0,0x30a2},
{0x2bf1,0x2bf1,0x30a4},
{0x2bf2,0x2bf2,0x30a6},
{0x2bf3,0x2bf3,0x30a8},
{0x2bf4,0x2bf5,0x30aa},
{0x2bf6,0x2bf6,0x30ad},
{0x2bf7,0x2bf7,0x30af},
{0x2bf8,0x2bf8,0x30b1},
{0x2bf9,0x2bf9,0x30b3},
{0x2bfa,0x2bfa,0x30b5},
{0x2bfb,0x2bfb,0x30b7},
{0x2bfc,0x2bfc,0x30b9},
{0x2bfd,0x2bfd,0x30bb},
{0x2bfe,0x2bfe,0x30bd},
{0x2bff,0x2bff,0x30bf},
{0x2c00,0x2c00,0x30c1},
{0x2c01,0x2c01,0x30c4},
{0x2c02,0x2c02,0x30c6},
{0x2c03,0x2c03,0x30c8},
{0x2c04,0x2c09,0x30ca},
{0x2c0a,0x2c0a,0x30d2},
{0x2c0b,0x2c0b,0x30d5},
{0x2c0c,0x2c0c,0x30d8},
{0x2c0d,0x2c0d,0x30db},
{0x2c0e,0x2c12,0x30de},
{0x2c13,0x2c13,0x30e4},
{0x2c14,0x2c14,0x30e6},
{0x2c15,0x2c1a,0x30e8},
{0x2c1b,0x2c1f,0x30ef},
{0x2c20,0x2c20,0x65e5},
{0x2c21,0x2c21,0x6708},
{0x2c22,0x2c22,0x706b},
{0x2c23,0x2c23,0x6c34},
{0x2c24,0x2c24,0x6728},
{0x2c25,0x2c25,0x91d1},
{0x2c26,0x2c26,0x571f},
{0x2c27,0x2c27,0x554f},
{0x2c28,0x2c28,0x7b54},
{0x2c29,0x2c29,0x4f8b},
{0x2c2a,0x2c2a,0x25a0},
{0x2c2b,0x2c2b,0x30},
{0x2c2d,0x2c2d,0x31},
{0x2c2f,0x2c2f,0x32},
{0x2c31,0x2c31,0x33},
{0x2c33,0x2c33,0x34},
{0x2c35,0x2c35,0x35},
{0x2c37,0x2c37,0x36},
{0x2c39,0x2c39,0x37},
{0x2c3b,0x2c3b,0x38},
{0x2c3d,0x2c3d,0x39},
{0x2c9a,0x2cb3,0x61},
{0x2cb4,0x2ccd,0x41},
{0x2cce,0x2cce,0x3042},
{0x2ccf,0x2ccf,0x3044},
{0x2cd0,0x2cd0,0x3046},
{0x2cd1,0x2cd1,0x3048},
{0x2cd2,0x2cd3,0x304a},
{0x2cd4,0x2cd4,0x304d},
{0x2cd5,0x2cd5,0x304f},
{0x2cd6,0x2cd6,0x3051},
{0x2cd7,0x2cd7,0x3053},
{0x2cd8,0x2cd8,0x3055},
{0x2cd9,0x2cd9,0x3057},
{0x2cda,0x2cda,0x3059},
{0x2cdb,0x2cdb,0x305b},
{0x2cdc,0x2cdc,0x305d},
{0x2cdd,0x2cdd,0x305f},
{0x2cde,0x2cde,0x3061},
{0x2cdf,0x2cdf,0x3064},
{0x2ce0,0x2ce0,0x3066},
{0x2ce1,0x2ce1,0x3068},
{0x2ce2,0x2ce7,0x306a},
{0x2ce8,0x2ce8,0x3072},
{0x2ce9,0x2ce9,0x3075},
{0x2cea,0x2cea,0x3078},
{0x2ceb,0x2ceb,0x307b},
{0x2cec,0x2cf0,0x307e},
{0x2cf1,0x2cf1,0x3084},
{0x2cf2,0x2cf2,0x3086},
{0x2cf3,0x2cf8,0x3088},
{0x2cf9,0x2cfd,0x308f},
{0x2cfe,0x2cfe,0x30a2},
{0x2cff,0x2cff,0x30a4},
{0x2d00,0x2d00,0x30a6},
{0x2d01,0x2d01,0x30a8},
{0x2d02,0x2d03,0x30aa},
{0x2d04,0x2d04,0x30ad},
{0x2d05,0x2d05,0x30af},
{0x2d06,0x2d06,0x30b1},
{0x2d07,0x2d07,0x30b3},
{0x2d08,0x2d08,0x30b5},
{0x2d09,0x2d09,0x30b7},
{0x2d0a,0x2d0a,0x30b9},
{0x2d0b,0x2d0b,0x30bb},
{0x2d0c,0x2d0c,0x30bd},
{0x2d0d,0x2d0d,0x30bf},
{0x2d0e,0x2d0e,0x30c1},
{0x2d0f,0x2d0f,0x30c4},
{0x2d10,0x2d10,0x30c6},
{0x2d11,0x2d11,0x30c8},
{0x2d12,0x2d17,0x30ca},
{0x2d18,0x2d18,0x30d2},
{0x2d19,0x2d19,0x30d5},
{0x2d1a,0x2d1a,0x30d8},
{0x2d1b,0x2d1b,0x30db},
{0x2d1c,0x2d20,0x30de},
{0x2d21,0x2d21,0x30e4},
{0x2d22,0x2d22,0x30e6},
{0x2d23,0x2d28,0x30e8},
{0x2d29,0x2d2d,0x30ef},
{0x2d2e,0x2d2e,0x65e5},
{0x2d2f,0x2d2f,0x6708},
{0x2d30,0x2d30,0x706b},
{0x2d31,0x2d31,0x6c34},
{0x2d32,0x2d32,0x6728},
{0x2d33,0x2d33,0x91d1},
{0x2d34,0x2d34,0x571f},
{0x2d35,0x2d35,0x554f},
{0x2d36,0x2d36,0x7b54},
{0x2d37,0x2d37,0x4f8b},
{0x2d38,0x2d38,0x30},
{0x2d3a,0x2d3a,0x31},
{0x2d3c,0x2d3c,0x32},
{0x2d3e,0x2d3e,0x33},
{0x2d40,0x2d40,0x34},
{0x2d42,0x2d42,0x35},
{0x2d44,0x2d44,0x36},
{0x2d46,0x2d46,0x37},
{0x2d48,0x2d48,0x38},
{0x2d4a,0x2d4a,0x39},
{0x2da7,0x2dc0,0x61},
{0x2dc1,0x2dda,0x41},
{0x2ddb,0x2ddb,0x3042},
{0x2ddc,0x2ddc,0x3044},
{0x2ddd,0x2ddd,0x3046},
{0x2dde,0x2dde,0x3048},
{0x2ddf,0x2de0,0x304a},
{0x2de1,0x2de1,0x304d},
{0x2de2,0x2de2,0x304f},
{0x2de3,0x2de3,0x3051},
{0x2de4,0x2de4,0x3053},
{0x2de5,0x2de5,0x3055},
{0x2de6,0x2de6,0x3057},
{0x2de7,0x2de7,0x3059},
{0x2de8,0x2de8,0x305b},
{0x2de9,0x2de9,0x305d},
{0x2dea,0x2dea,0x305f},
{0x2deb,0x2deb,0x3061},
{0x2dec,0x2dec,0x3064},
{0x2ded,0x2ded,0x3066},
{0x2dee,0x2dee,0x3068},
{0x2def,0x2df4,0x306a},
{0x2df5,0x2df5,0x3072},
{0x2df6,0x2df6,0x3075},
{0x2df7,0x2df7,0x3078},
{0x2df8,0x2df8,0x307b},
{0x2df9,0x2dfd,0x307e},
{0x2dfe,0x2dfe,0x3084},
{0x2dff,0x2dff,0x3086},
{0x2e00,0x2e05,0x3088},
{0x2e06,0x2e0a,0x308f},
{0x2e0b,0x2e0b,0x30a2},
{0x2e0c,0x2e0c,0x30a4},
{0x2e0d,0x2e0d,0x30a6},
{0x2e0e,0x2e0e,0x30a8},
{0x2e0f,0x2e10,0x30aa},
{0x2e11,0x2e11,0x30ad},
{0x2e12,0x2e12,0x30af},
{0x2e13,0x2e13,0x30b1},
{0x2e14,0x2e14,0x30b3},
{0x2e15,0x2e15,0x30b5},
{0x2e16,0x2e16,0x30b7},
{0x2e17,0x2e17,0x30b9},
{0x2e18,0x2e18,0x30bb},
{0x2e19,0x2e19,0x30bd},
{0x2e1a,0x2e1a,0x30bf},
{0x2e1b,0x2e1b,0x30c1},
{0x2e1c,0x2e1c,0x30c4},
{0x2e1d,0x2e1d,0x30c6},
{0x2e1e,0x2e1e,0x30c8},
{0x2e1f,0x2e24,0x30ca},
{0x2e25,0x2e25,0x30d2},
{0x2e26,0x2e26,0x30d5},
{0x2e27,0x2e27,0x30d8},
{0x2e28,0x2e28,0x30db},
{0x2e29,0x2e2d,0x30de},
{0x2e2e,0x2e2e,0x30e4},
{0x2e2f,0x2e2f,0x30e6},
{0x2e30,0x2e35,0x30e8},
{0x2e36,0x2e3a,0x30ef},
{0x2e3b,0x2e3b,0x65e5},
{0x2e3c,0x2e3c,0x6708},
{0x2e3d,0x2e3d,0x706b},
{0x2e3e,0x2e3e,0x6c34},
{0x2e3f,0x2e3f,0x6728},
{0x2e40,0x2e40,0x91d1},
{0x2e41,0x2e41,0x571f},
{0x2e42,0x2e42,0x554f},
{0x2e43,0x2e43,0x7b54},
{0x2e44,0x2e44,0x4f8b},
{0x2e45,0x2e45,0x25a0},
{0x2e46,0x2e46,0x33c2},
{0x2e47,0x2e47,0x33cd},
{0x2e48,0x2e48,0x2116},
{0x2e4a,0x2e4a,0x33d8},
{0x2e4b,0x2e4b,0x33da},
{0x2e4c,0x2e4d,0x2121},
{0x2e4f,0x2e4f,0x2100},
{0x2e50,0x2e50,0x33c2},
{0x2e52,0x2e52,0x33c4},
{0x2e53,0x2e53,0x2105},
{0x2e54,0x2e54,0x3397},
{0x2e55,0x2e55,0x3371},
{0x2e56,0x2e56,0x3398},
{0x2e57,0x2e57,0x2113},
{0x2e58,0x2e58,0x338d},
{0x2e59,0x2e59,0x339b},
{0x2e5a,0x2e5a,0x3396},
{0x2e5d,0x2e5e,0x33d7},
{0x2e62,0x2e63,0x3301},
{0x2e64,0x2e64,0x3304},
{0x2e65,0x2e65,0x3306},
{0x2e67,0x2e67,0x3308},
{0x2e69,0x2e69,0x3307},
{0x2e6a,0x2e6a,0x330a},
{0x2e6c,0x2e6c,0x3309},
{0x2e6e,0x2e6e,0x330b},
{0x2e70,0x2e70,0x330c},
{0x2e71,0x2e76,0x330e},
{0x2e78,0x2e78,0x3317},
{0x2e7a,0x2e7a,0x3319},
{0x2e7c,0x2e7f,0x331a},
{0x2e80,0x2e82,0x331f},
{0x2e83,0x2e83,0x3324},
{0x2e85,0x2e85,0x3325},
{0x2e88,0x2e89,0x3328},
{0x2e8b,0x2e8b,0x332d},
{0x2e8e,0x2e90,0x332e},
{0x2e91,0x2e91,0x3332},
{0x2e94,0x2e95,0x3334},
{0x2e96,0x2e96,0x333c},
{0x2e9a,0x2e9a,0x3337},
{0x2e9c,0x2e9c,0x3338},
{0x2e9d,0x2e9d,0x333a},
{0x2e9e,0x2e9e,0x333d},
{0x2e9f,0x2e9f,0x3341},
{0x2ea0,0x2ea2,0x333e},
{0x2ea3,0x2ea6,0x3343},
{0x2ea7,0x2ea7,0x3348},
{0x2ea8,0x2ea9,0x334b},
{0x2eaa,0x2eab,0x334f},
{0x2eae,0x2eae,0x3352},
{0x2eaf,0x2eaf,0x3354},
{0x2eb2,0x2eb2,0x3353},
{0x2eb3,0x2eb4,0x3355},
{0x2eb6,0x2eb7,0x3301},
{0x2eb8,0x2eb8,0x3304},
{0x2eb9,0x2eb9,0x3306},
{0x2ebb,0x2ebb,0x3308},
{0x2ebd,0x2ebd,0x3307},
{0x2ebe,0x2ebe,0x330a},
{0x2ec0,0x2ec0,0x3309},
{0x2ec2,0x2ec2,0x330b},
{0x2ec4,0x2ec4,0x330c},
{0x2ec5,0x2eca,0x330e},
{0x2ecc,0x2ecc,0x3317},
{0x2ece,0x2ece,0x3319},
{0x2ed0,0x2ed3,0x331a},
{0x2ed4,0x2ed6,0x331f},
{0x2ed7,0x2ed7,0x3324},
{0x2ed9,0x2ed9,0x3325},
{0x2edc,0x2edd,0x3328},
{0x2edf,0x2edf,0x332d},
{0x2ee2,0x2ee4,0x332e},
{0x2ee5,0x2ee5,0x3332},
{0x2ee8,0x2ee9,0x3334},
{0x2eea,0x2eea,0x333c},
{0x2eee,0x2eee,0x3337},
{0x2ef0,0x2ef0,0x3338},
{0x2ef1,0x2ef1,0x333a},
{0x2ef2,0x2ef2,0x333d},
{0x2ef3,0x2ef3,0x3341},
{0x2ef4,0x2ef6,0x333e},
{0x2ef7,0x2efa,0x3343},
{0x2efb,0x2efb,0x3348},
{0x2efc,0x2efd,0x334b},
{0x2efe,0x2eff,0x334f},
{0x2f02,0x2f02,0x3352},
{0x2f03,0x2f03,0x3354},
{0x2f06,0x2f06,0x3353},
{0x2f07,0x2f08,0x3355},
{0x2f09,0x2f09,0x337e},
{0x2f0a,0x2f0a,0x337d},
{0x2f0b,0x2f0b,0x337c},
{0x2f0c,0x2f0c,0x337b},
{0x2f1f,0x2f1f,0x2d},
{0x2f20,0x2f20,0x3d},
{0x2f21,0x2f21,0x2103},
{0x2f22,0x2f22,0x2640},
{0x2f23,0x2f23,0x2642},
{0x2f24,0x2f24,0x3013},
{0x2f25,0x2f25,0x3012},
{0x2f26,0x2f29,0x300c},
{0x2f2a,0x2f2b,0x301a},
{0x2f2c,0x2f2d,0x3018},
{0x2f2e,0x2f2f,0xff08},
{0x2f30,0x2f31,0x301d},
{0x2f32,0x2f33,0x2018},
{0x2f34,0x2f35,0x201c},
{0x2f36,0x2f36,0x27},
{0x2f37,0x2f37,0x22},
{0x2f39,0x2f39,0x2135},
{0x2f3a,0x2f3a,0x3d0},
{0x2f3b,0x2f3b,0x220a},
{0x2f3c,0x2f3c,0x210f},
{0x2f3d,0x2f3d,0xb5},
{0x2f3e,0x2f3e,0x3d5},
{0x2f3f,0x2f3f,0x3db},
{0x2f40,0x2f40,0x3d1},
{0x2f42,0x2f43,0x2668},
{0x2f44,0x2f44,0x266c},
{0x2f45,0x2f45,0xff1a},
{0x2f46,0x2f46,0xa9},
{0x2f47,0x2f47,0xa9},
{0x2f48,0x2f48,0xae},
{0x2f49,0x2f49,0xae},
{0x2f4a,0x2f4a,0x303b},
{0x2f4b,0x2f4b,0x303b},
{0x2f4c,0x2f4e,0x3033},
{0x2f4f,0x2f4f,0x203c},
{0x2f50,0x2f50,0x2049},
{0x2f51,0x2f51,0x21},
{0x2f52,0x2f52,0x203c},
{0x2f53,0x2f53,0x2049},
{0x2f54,0x2f54,0x244a},
{0x2f56,0x2f56,0x2213},
{0x2f57,0x2f57,0x2260},
{0x2f58,0x2f58,0x2243},
{0x2f59,0x2f5a,0x2272},
{0x2f5b,0x2f5e,0x300c},
{0x2f5f,0x2f60,0x301a},
{0x2f61,0x2f62,0x3018},
{0x2f63,0x2f64,0xff5f},
{0x2f65,0x2f68,0x300c},
{0x2f69,0x2f6a,0x301a},
{0x2f6b,0x2f6c,0x3018},
{0x2f6d,0x2f6e,0xff5f},
{0x2f6f,0x2f6f,0x239b},
{0x2f70,0x2f71,0x239d},
{0x2f72,0x2f72,0x23a0},
{0x2f73,0x2f73,0x239d},
{0x2f74,0x2f74,0x239b},
{0x2f75,0x2f75,0x23a0},
{0x2f76,0x2f76,0x239e},
{0x2f77,0x2f77,0x23a1},
{0x2f78,0x2f79,0x23a3},
{0x2f7a,0x2f7a,0x23a6},
{0x2f7b,0x2f7b,0x23a3},
{0x2f7c,0x2f7c,0x23a1},
{0x2f7d,0x2f7d,0x23a6},
{0x2f7e,0x2f7e,0x23a4},
{0x2f7f,0x2f7f,0x23a1},
{0x2f80,0x2f81,0x23a3},
{0x2f82,0x2f82,0x23a6},
{0x2f83,0x2f83,0x23a3},
{0x2f84,0x2f84,0x23a1},
{0x2f85,0x2f85,0x23a6},
{0x2f86,0x2f86,0x23a4},
{0x2f87,0x2f87,0xff5c},
{0x2f88,0x2f88,0x2015},
{0x2f89,0x2f8a,0x301d},
{0x2f8b,0x2f8c,0x2018},
{0x2f8d,0x2f8e,0x2018},
{0x2f8f,0x2f8f,0x2702},
{0x2f90,0x2f90,0x2702},
{0x2f91,0x2f91,0x2702},
{0x2f92,0x2f92,0x2702},
{0x2f93,0x2f93,0x303d},
{0x2f94,0x2f94,0x3012},
{0x2f95,0x2f95,0x309f},
{0x2f96,0x2f96,0x534d},
{0x2f97,0x2f97,0x2207},
{0x2f98,0x2f98,0x2205},
{0x2f99,0x2f99,0x22a0},
{0x2f9a,0x2f9a,0x2296},
{0x2f9b,0x2f9b,0x2298},
{0x2f9c,0x2f9c,0x2295},
{0x2f9d,0x2f9d,0x2297},
{0x2f9e,0x2f9e,0x229e},
{0x2f9f,0x2f9f,0x2295},
{0x2fa0,0x2fa0,0x26a0},
{0x2fa1,0x2fa1,0x25b3},
{0x2fa2,0x2fa2,0x25c0},
{0x2fa3,0x2fa3,0x25b6},
{0x2fa4,0x2fa4,0x21e6},
{0x2fa5,0x2fa5,0x21e8},
{0x2fa6,0x2fa6,0x21e7},
{0x2fa7,0x2fa7,0x21e9},
{0x2fa8,0x2fa8,0x21d0},
{0x2fa9,0x2fa9,0x2194},
{0x2faa,0x2fab,0x2198},
{0x2fac,0x2fad,0x2196},
{0x2fae,0x2fae,0x21cc},
{0x2faf,0x2faf,0x21cb},
{0x2fb0,0x2fb1,0x21c4},
{0x2fb2,0x2fb2,0x2190},
{0x2fb3,0x2fb3,0x2192},
{0x2fb4,0x2fb4,0x2191},
{0x2fb5,0x2fb7,0x2193},
{0x2fb8,0x2fb8,0x2504},
{0x2fb9,0x2fb9,0x2506},
{0x2fba,0x2fba,0x3030},
{0x2fbb,0x2fbb,0x2307},
{0x2fbc,0x2fbc,0x3030},
{0x2fbd,0x2fbd,0x2307},
{0x2fbe,0x2fbe,0x3030},
{0x2fbf,0x2fbf,0x2307},
{0x2fc0,0x2fc0,0x3030},
{0x2fc1,0x2fc1,0x2307},
{0x2fc2,0x2fc2,0x3030},
{0x2fc3,0x2fc3,0x2307},
{0x2fc4,0x2fc4,0x2740},
{0x2fc5,0x2fc5,0x273f},
{0x2fc6,0x2fc6,0x25a1},
{0x2fc7,0x2fc8,0x25a0},
{0x2fc9,0x2fc9,0x25a1},
{0x2fca,0x2fca,0x25a1},
{0x2fcb,0x2fcb,0x25a1},
{0x2fcc,0x2fcc,0x25a1},
{0x2fcd,0x2fcd,0x25ab},
{0x2fce,0x2fce,0x25a0},
{0x2fcf,0x2fcf,0x25aa},
{0x2fd0,0x2fd0,0x271a},
{0x2fd1,0x2fd1,0x271a},
{0x2fd2,0x2fd2,0x25c7},
{0x2fd3,0x2fd4,0x25c6},
{0x2fd5,0x2fd5,0x25c7},
{0x2fd6,0x2fd6,0x25c7},
{0x2fd7,0x2fd7,0x25c7},
{0x2fd8,0x2fd8,0x25c7},
{0x2fd9,0x2fd9,0x25c6},
{0x2fda,0x2fda,0x25c6},
{0x2fdb,0x2fdb,0x25ce},
{0x2fdc,0x2fdc,0x25c9},
{0x2fdd,0x2fdd,0x25cb},
{0x2fde,0x2fde,0x25e6},
{0x2fdf,0x2fdf,0x25cf},
{0x2fe0,0x2fe0,0x2022},
{0x2fe1,0x2fe1,0x2756},
{0x2fe2,0x2fe2,0x2756},
{0x2fe3,0x2fe3,0x2756},
{0x2fe4,0x2fe4,0x2756},
{0x2fe5,0x2fe6,0x3008},
{0x2fe7,0x2fea,0x3008},
{0x2feb,0x2fec,0x300a},
{0x2fed,0x2fed,0x3053},
{0x2fee,0x2fee,0x3053},
{0x2fef,0x2fef,0x30b3},
{0x2ff0,0x2ff0,0x30b3},
{0x2ff1,0x2ff2,0x309d},
{0x2ff3,0x2ffc,0x3041},
{0x2ffd,0x2ffd,0x3095},
{0x2ffe,0x3003,0x304b},
{0x3004,0x3004,0x3096},
{0x3005,0x3007,0x3051},
{0x3008,0x3049,0x3053},
{0x304a,0x304b,0x30fd},
{0x304c,0x304c,0x30fc},
{0x304d,0x3056,0x30a1},
{0x3057,0x3057,0x30f5},
{0x3058,0x305d,0x30ab},
{0x305e,0x305e,0x30f6},
{0x305f,0x3061,0x30b1},
{0x3062,0x30a3,0x30b3},
{0x30a4,0x30a7,0x30f7},
{0x30a8,0x30a9,0x309d},
{0x30aa,0x30b3,0x3041},
{0x30b4,0x30b4,0x3095},
{0x30b5,0x30ba,0x304b},
{0x30bb,0x30bb,0x3096},
{0x30bc,0x30be,0x3051},
{0x30bf,0x3100,0x3053},
{0x3101,0x3102,0x30fd},
{0x3103,0x3103,0x30fc},
{0x3104,0x310d,0x30a1},
{0x310e,0x310e,0x30f5},
{0x310f,0x3114,0x30ab},
{0x3115,0x3115,0x30f6},
{0x3116,0x3118,0x30b1},
{0x3119,0x315a,0x30b3},
{0x315b,0x315e,0x30f7},
{0x315f,0x3160,0xfe45},
{0x3161,0x3161,0x30fb},
{0x3162,0x3162,0xff0a},
{0x3163,0x3163,0x203b},
{0x3164,0x3164,0x25cb},
{0x3165,0x3165,0x25ce},
{0x3166,0x3166,0x25c9},
{0x3167,0x3167,0x25b3},
{0x3168,0x3168,0x25b2},
{0x3169,0x316a,0x30fd},
{0x316b,0x316c,0x309d},
{0x316d,0x316e,0xff08},
{0x316f,0x3170,0x3014},
{0x3171,0x3172,0xff08},
{0x3173,0x3174,0x3014},
{0x3175,0x317e,0x30},
{0x317f,0x3189,0x3041},
{0x318a,0x318a,0x3095},
{0x318b,0x3190,0x304c},
{0x3191,0x3191,0x3096},
{0x3192,0x31d4,0x3052},
{0x31d5,0x31d5,0x3041},
{0x31d6,0x31d6,0x3043},
{0x31d7,0x31d7,0x3045},
{0x31d8,0x31d8,0x3047},
{0x31d9,0x31d9,0x3049},
{0x31da,0x31da,0x304b},
{0x31db,0x31db,0x3051},
{0x31dc,0x31dc,0x3063},
{0x31dd,0x31dd,0x3083},
{0x31de,0x31de,0x3085},
{0x31df,0x31df,0x3087},
{0x31e0,0x31e0,0x308e},
{0x31e1,0x31eb,0x30a1},
{0x31ec,0x31ec,0x30f5},
{0x31ed,0x31f2,0x30ac},
{0x31f3,0x31f3,0x30f6},
{0x31f4,0x3236,0x30b2},
{0x3237,0x3237,0x30a1},
{0x3238,0x3238,0x30a3},
{0x3239,0x3239,0x30a5},
{0x323a,0x323a,0x30a7},
{0x323b,0x323b,0x30a9},
{0x323c,0x323d,0x30f5},
{0x323e,0x323e,0x30c3},
{0x323f,0x323f,0x30e3},
{0x3240,0x3240,0x30e5},
{0x3241,0x3241,0x30e7},
{0x3242,0x3242,0x30ee},
{0x3243,0x3243,0x30fc},
{0x3244,0x3244,0x30fc},
{0x3245,0x3245,0x6ce8},
{0x3246,0x3246,0x20ac},
{0x3247,0x3247,0x2126},
{0x3248,0x3249,0x2032},
{0x324a,0x324a,0xfb00},
{0x324b,0x324c,0xfb03},
{0x324d,0x324d,0x101},
{0x324e,0x324e,0x12b},
{0x324f,0x324f,0x16b},
{0x3250,0x3250,0x113},
{0x3251,0x3251,0x14d},
{0x3252,0x3252,0x100},
{0x3253,0x3253,0x12a},
{0x3254,0x3254,0x16a},
{0x3255,0x3255,0x112},
{0x3256,0x3256,0x14c},
{0x3257,0x325a,0x215b},
{0x325b,0x325c,0x2153},
{0x325d,0x325d,0x2070},
{0x325e,0x3263,0x2074},
{0x3264,0x326d,0x2080},
{0x326e,0x326e,0x1cd},
{0x326f,0x326f,0x11a},
{0x3271,0x3271,0x1ebc},
{0x3272,0x3272,0x1cf},
{0x3274,0x3274,0x128},
{0x3275,0x3275,0x1d1},
{0x3277,0x3277,0x1d3},
{0x3278,0x3278,0x16e},
{0x3279,0x3279,0x168},
{0x327a,0x327a,0x1ce},
{0x327b,0x327b,0x11b},
{0x327d,0x327d,0x1ebd},
{0x327e,0x327e,0x1d0},
{0x3280,0x3280,0x129},
{0x3281,0x3281,0x1d2},
{0x3283,0x3283,0x1d4},
{0x3284,0x3284,0x16f},
{0x3285,0x3285,0x169},
{0x3286,0x3286,0x251},
{0x3287,0x3287,0x251},
{0x3288,0x3288,0x251},
{0x3289,0x3289,0x1fd},
{0x328a,0x328a,0xe6},
{0x328b,0x328b,0x254},
{0x328c,0x328c,0x254},
{0x328d,0x328d,0x254},
{0x328e,0x328e,0x259},
{0x328f,0x328f,0x259},
{0x3290,0x3291,0x259},
{0x3292,0x3292,0x25a},
{0x3293,0x3294,0x25a},
{0x3295,0x3295,0x25b},
{0x3296,0x3296,0x25b},
{0x3297,0x3297,0x6a},
{0x3298,0x3298,0x14b},
{0x3299,0x3299,0x275},
{0x329a,0x329a,0x28c},
{0x329b,0x329b,0x28c},
{0x329c,0x329c,0x28c},
{0x329d,0x329d,0x292},
{0x329e,0x329e,0x283},
{0x329f,0x329f,0x2d0},
{0x32a0,0x32db,0x20},
{0x32dc,0x32dc,0xa5},
{0x32dd,0x32fb,0x5d},
{0x32fc,0x32fc,0xa6},
{0x32fd,0x32fd,0x7d},
{0x32fe,0x32fe,0x303},
{0x32ff,0x32ff,0x2019},
{0x3300,0x3300,0x5c},
{0x3301,0x3301,0x2018},
{0x3302,0x3302,0x7c},
{0x3303,0x3303,0x7e},
{0x3304,0x3306,0xa1},
{0x3307,0x3307,0x2044},
{0x3308,0x3308,0x192},
{0x3309,0x3309,0xa7},
{0x330a,0x330a,0xa4},
{0x330b,0x330b,0x201c},
{0x330c,0x330c,0xab},
{0x330d,0x330e,0x2039},
{0x330f,0x3310,0xfb01},
{0x3311,0x3311,0x2012},
{0x3312,0x3313,0x2020},
{0x3314,0x3314,0xb7},
{0x3315,0x3315,0xb6},
{0x3316,0x3316,0x2022},
{0x3317,0x3317,0x201a},
{0x3318,0x3318,0x201e},
{0x3319,0x3319,0x201d},
{0x331a,0x331a,0xbb},
{0x331b,0x331b,0x2026},
{0x331c,0x331c,0x2030},
{0x331d,0x331d,0xbf},
{0x331e,0x331f,0x301},
{0x3320,0x3320,0xaf},
{0x3321,0x3323,0x306},
{0x3324,0x3324,0x30a},
{0x3325,0x3325,0xb8},
{0x3326,0x3326,0x30b},
{0x3327,0x3327,0x328},
{0x3328,0x3328,0x30c},
{0x3329,0x3329,0x336},
{0x332a,0x332a,0xc6},
{0x332b,0x332b,0xaa},
{0x332c,0x332c,0x141},
{0x332d,0x332d,0xd8},
{0x332e,0x332e,0x152},
{0x332f,0x332f,0xba},
{0x3330,0x3330,0xe6},
{0x3331,0x3331,0x131},
{0x3332,0x3332,0x142},
{0x3333,0x3333,0xf8},
{0x3334,0x3334,0x153},
{0x3335,0x3335,0xdf},
{0x3336,0x3336,0x2d},
{0x3337,0x3337,0xa9},
{0x3338,0x3338,0xac},
{0x3339,0x3339,0xae},
{0x333a,0x333d,0xb0},
{0x333e,0x333e,0xb5},
{0x333f,0x333f,0xb9},
{0x3340,0x3342,0xbc},
{0x3343,0x3348,0xc0},
{0x3349,0x3359,0xc7},
{0x335a,0x335f,0xd9},
{0x3360,0x3365,0xe0},
{0x3366,0x3376,0xe7},
{0x3377,0x337d,0xf9},
{0x337e,0x337e,0x160},
{0x337f,0x337f,0x178},
{0x3380,0x3380,0x17d},
{0x3381,0x3381,0x305},
{0x3382,0x3382,0x161},
{0x3383,0x3383,0x2122},
{0x3384,0x3384,0x17e},
{0x3385,0x3385,0x30},
{0x3386,0x3386,0x20ac},
{0x3387,0x3387,0x2126},
{0x3388,0x3389,0x2032},
{0x338a,0x338a,0xfb00},
{0x338b,0x338c,0xfb03},
{0x338d,0x338d,0x101},
{0x338e,0x338e,0x12b},
{0x338f,0x338f,0x16b},
{0x3390,0x3390,0x113},
{0x3391,0x3391,0x14d},
{0x3392,0x3392,0x100},
{0x3393,0x3393,0x12a},
{0x3394,0x3394,0x16a},
{0x3395,0x3395,0x112},
{0x3396,0x3396,0x14c},
{0x3397,0x339a,0x215b},
{0x339b,0x339c,0x2153},
{0x339d,0x339d,0x2070},
{0x339e,0x33a3,0x2074},
{0x33a4,0x33ad,0x2080},
{0x33ae,0x33ae,0x1cd},
{0x33af,0x33af,0x11a},
{0x33b1,0x33b1,0x1ebc},
{0x33b2,0x33b2,0x1cf},
{0x33b4,0x33b4,0x128},
{0x33b5,0x33b5,0x1d1},
{0x33b7,0x33b7,0x1d3},
{0x33b8,0x33b8,0x16e},
{0x33b9,0x33b9,0x168},
{0x33ba,0x33ba,0x1ce},
{0x33bb,0x33bb,0x11b},
{0x33bd,0x33bd,0x1ebd},
{0x33be,0x33be,0x1d0},
{0x33c0,0x33c0,0x129},
{0x33c1,0x33c1,0x1d2},
{0x33c3,0x33c3,0x1d4},
{0x33c4,0x33c4,0x16f},
{0x33c5,0x33c5,0x169},
{0x33c6,0x33cf,0x30},
{0x33d0,0x33d0,0x336},
{0x33d1,0x33d1,0x2d},
{0x33d2,0x33d2,0x3d},
{0x33d3,0x33d3,0x2c},
{0x33d4,0x33d5,0x28},
{0x33d6,0x33d7,0x2e},
{0x33d8,0x33d9,0x3a},
{0x33da,0x33e3,0x30},
{0x33e4,0x33e4,0x336},
{0x33e5,0x33e5,0x2d},
{0x33e6,0x33e6,0x3d},
{0x33e7,0x33e7,0x2c},
{0x33e8,0x33e9,0x28},
{0x33ea,0x33eb,0x2e},
{0x33ec,0x33ed,0x3a},
{0x33ee,0x33ee,0xb7},
{0x33ef,0x33ef,0x2d},
{0x33f0,0x33f0,0x3d},
{0x33f1,0x33f1,0x2103},
{0x33f2,0x33f2,0x2640},
{0x33f3,0x33f3,0x2642},
{0x33f4,0x33f4,0x3013},
{0x33f5,0x33f5,0x3012},
{0x33f6,0x33f9,0x300c},
{0x33fa,0x33fb,0x301a},
{0x33fc,0x33fd,0x3018},
{0x33fe,0x33ff,0xff08},
{0x3400,0x3401,0x301d},
{0x3402,0x3403,0x2018},
{0x3404,0x3405,0x201c},
{0x3406,0x3406,0x27},
{0x3407,0x3407,0x22},
{0x3408,0x3408,0xfa67},
{0x3409,0x3409,0xfa62},
{0x340a,0x340a,0x7de3},
{0x340b,0x340b,0x9ec3},
{0x340c,0x340c,0x6eab},
{0x340d,0x340d,0xfa52},
{0x340e,0x340e,0xfa3d},
{0x340f,0x340f,0xfa45},
{0x3410,0x3410,0xfa3e},
{0x3411,0x3411,0x69ea},
{0x3412,0x3412,0x6e34},
{0x3413,0x3413,0xfa60},
{0x3414,0x3414,0xfa47},
{0x3415,0x3415,0xfa38},
{0x3416,0x3416,0xfa42},
{0x3417,0x3417,0xfa4e},
{0x3418,0x3418,0x865b},
{0x3419,0x3419,0xfa69},
{0x341a,0x341a,0xfa34},
{0x341b,0x341b,0xfa63},
{0x341c,0x341c,0x63ed},
{0x341d,0x341d,0x64ca},
{0x341e,0x341e,0x784f},
{0x341f,0x341f,0xfa54},
{0x3420,0x3420,0xf970},
{0x3421,0x3421,0xfa4d},
{0x3422,0x3422,0xfa61},
{0x3423,0x3423,0xfa48},
{0x3424,0x3424,0xfa4c},
{0x3425,0x3426,0xfa5b},
{0x3427,0x3427,0xfa51},
{0x3428,0x3428,0xfa43},
{0x3429,0x3429,0xfa5a},
{0x342a,0x342a,0x6d89},
{0x342b,0x342b,0x72c0},
{0x342c,0x342c,0x614e},
{0x342e,0x342e,0xfa56},
{0x342f,0x342f,0xfa50},
{0x3430,0x3430,0xfa31},
{0x3431,0x3431,0xfa3b},
{0x3432,0x3432,0x5de2},
{0x3433,0x3433,0xfa3f},
{0x3434,0x3434,0xfa65},
{0x3435,0x3435,0x537d},
{0x3436,0x3436,0xfa37},
{0x3437,0x3437,0xfa5f},
{0x3438,0x3438,0x5fb5},
{0x3439,0x3439,0xfa40},
{0x343a,0x343a,0x93ae},
{0x343b,0x343b,0xfa53},
{0x343c,0x343c,0x9b2d},
{0x343d,0x343d,0xfa55},
{0x343e,0x343e,0xfa68},
{0x343f,0x343f,0xfa44},
{0x3440,0x3440,0xfa59},
{0x3441,0x3441,0x665a},
{0x3442,0x3442,0xfa35},
{0x3443,0x3443,0xfa4b},
{0x3444,0x3444,0xfa64},
{0x3445,0x3445,0xfa41},
{0x3446,0x3446,0xfa30},
{0x3447,0x3447,0x4f75},
{0x3448,0x3448,0xfa39},
{0x3449,0x3449,0xfa33},
{0x344a,0x344a,0x6b65},
{0x344b,0x344b,0xfa3a},
{0x344c,0x344c,0x6bcf},
{0x344d,0x344d,0xfa32},
{0x344e,0x344e,0x623e},
{0x344f,0x344f,0xfa4f},
{0x3450,0x3450,0xf91d},
{0x3451,0x3451,0xf9dc},
{0x3452,0x3452,0xf936},
{0x3453,0x3453,0x6dda},
{0x3454,0x3454,0xf9d0},
{0x3455,0x3455,0x66c6},
{0x3456,0x3456,0x6b77},
{0x3457,0x3457,0xfa57},
{0x3458,0x3458,0x934a},
{0x3459,0x3459,0x5eca},
{0x345a,0x345a,0x9304},
{0x345b,0x345b,0x6982},
{0x345c,0x345c,0x51b4},
{0x345d,0x345d,0x634c},
{0x345e,0x345e,0x86db},
{0x345f,0x345f,0x9089},
{0x3460,0x3460,0x9022},
{0x3461,0x3461,0x5049},
{0x3462,0x3462,0x7def},
{0x3463,0x3463,0x9055},
{0x3464,0x3464,0x53a9},
{0x3465,0x3465,0x990c},
{0x3466,0x3466,0x885b},
{0x3467,0x3467,0x5ef6},
{0x3468,0x3468,0x6cbf},
{0x3469,0x3469,0x925b},
{0x346a,0x346a,0x7fc1},
{0x346b,0x346b,0x82bd},
{0x346c,0x346c,0x96c5},
{0x346d,0x346d,0x5bb3},
{0x346e,0x346e,0x6168},
{0x346f,0x346f,0x6982},
{0x3470,0x3470,0x6bbb},
{0x3471,0x3471,0x6562},
{0x3472,0x3472,0x8cab},
{0x3473,0x3473,0x5dcc},
{0x3474,0x3474,0x9811},
{0x3475,0x3475,0x5e30},
{0x3476,0x3476,0x8ecc},
{0x3477,0x3477,0x7aae},
{0x3478,0x3478,0x5747},
{0x3479,0x3479,0x5091},
{0x347a,0x347a,0x7a74},
{0x347b,0x347b,0x5065},
{0x347c,0x347c,0x5efa},
{0x347d,0x347d,0x9237},
{0x347e,0x347e,0x6a8e},
{0x347f,0x347f,0x4ea4},
{0x3480,0x3480,0x516c},
{0x3481,0x3481,0x66f4},
{0x3482,0x3482,0x6821},
{0x3483,0x3483,0x786c},
{0x3484,0x3484,0x7d5e},
{0x3485,0x3485,0x8003},
{0x3486,0x3486,0x8cfc},
{0x3487,0x3487,0x964d},
{0x3488,0x3488,0x62f7},
{0x3489,0x3489,0x7f6a},
{0x348a,0x348a,0x4f7f},
{0x348b,0x348b,0x53f2},
{0x348c,0x348c,0x59c9},
{0x348d,0x348d,0x8b1d},
{0x348e,0x348e,0x90aa},
{0x348f,0x348f,0x53ce},
{0x3490,0x3490,0x8f2f},
{0x3491,0x3491,0x67d4},
{0x3492,0x3492,0x77ac},
{0x3493,0x3493,0x821c},
{0x3494,0x3494,0x696f},
{0x3495,0x3495,0x677e},
{0x3496,0x3496,0x8a1f},
{0x3497,0x3497,0x4e08},
{0x3498,0x3498,0x57f4},
{0x3499,0x3499,0x690d},
{0x349a,0x349a,0x8077},
{0x349b,0x349b,0x89aa},
{0x349c,0x349c,0x9042},
{0x349d,0x349d,0x636e},
{0x349e,0x349e,0x6442},
{0x349f,0x349f,0x8239},
{0x34a0,0x34a0,0x7dcf},
{0x34a1,0x34a1,0x8061},
{0x34a2,0x34a2,0x50cf},
{0x34a3,0x34a3,0x8a95},
{0x34a4,0x34a4,0x6065},
{0x34a5,0x34a5,0x5146},
{0x34a6,0x34a6,0x773a},
{0x34a7,0x34a7,0x8074},
{0x34a8,0x34a8,0x8df3},
{0x34a9,0x34a9,0x5ead},
{0x34aa,0x34aa,0x5ef7},
{0x34ab,0x34ab,0x8247},
{0x34ac,0x34ac,0x6843},
{0x34ad,0x34ad,0x9003},
{0x34ae,0x34ae,0x6d3e},
{0x34af,0x34af,0x6392},
{0x34b0,0x34b0,0x8f29},
{0x34b1,0x34b1,0x73ed},
{0x34b2,0x34b2,0x9812},
{0x34b3,0x34b3,0x60b2},
{0x34b4,0x34b4,0x6249},
{0x34b5,0x34b5,0x6590},
{0x34b6,0x34b6,0x7dcb},
{0x34b7,0x34b7,0x8ab9},
{0x34b8,0x34b8,0x8ca7},
{0x34b9,0x34b9,0x7236},
{0x34ba,0x34ba,0x847a},
{0x34bb,0x34bb,0x5206},
{0x34bc,0x34bc,0x5674},
{0x34bd,0x34bd,0x61a4},
{0x34be,0x34be,0x7c89},
{0x34bf,0x34bf,0x7d1b},
{0x34c0,0x34c0,0x96f0},
{0x34c1,0x34c1,0x853d},
{0x34c2,0x34c2,0x4fbf},
{0x34c3,0x34c3,0x6367},
{0x34c4,0x34c4,0x76c6},
{0x34c5,0x34c5,0x685d},
{0x34c6,0x34c6,0x8108},
{0x34c7,0x34c7,0x8036},
{0x34c8,0x34c8,0x7ffc},
{0x34c9,0x34c9,0x540f},
{0x34ca,0x34ca,0x96a3},
{0x34cb,0x34cb,0x9e9f},
{0x34cc,0x34cc,0x9e97},
{0x34cd,0x34cd,0x806f},
{0x34ce,0x34ce,0x807e},
{0x34cf,0x34cf,0x6e7e},
{0x34d0,0x34d0,0x5085},
{0x34d1,0x34d1,0x5193},
{0x34d2,0x34d2,0x51db},
{0x34d4,0x34d4,0x5340},
{0x34d5,0x34d5,0x96d9},
{0x34d6,0x34d6,0x55a9},
{0x34d7,0x34d7,0x56c1},
{0x34d8,0x34d8,0x570d},
{0x34d9,0x34d9,0x58ab},
{0x34da,0x34da,0x59da},
{0x34db,0x34db,0x5a36},
{0x34dc,0x34dc,0x5abe},
{0x34dd,0x34dd,0x5d4e},
{0x34de,0x34de,0x5d87},
{0x34df,0x34df,0x5dc9},
{0x34e0,0x34e0,0x5dd3},
{0x34e1,0x34e1,0x5f2d},
{0x34e2,0x34e2,0x5f98},
{0x34e3,0x34e3,0x60d8},
{0x34e4,0x34e4,0x613d},
{0x34e5,0x34e5,0x61fe},
{0x34e6,0x34e6,0x6268},
{0x34e7,0x34e7,0x62cf},
{0x34e8,0x34e8,0x651d},
{0x34e9,0x34e9,0x640f},
{0x34ea,0x34ea,0x64f2},
{0x34eb,0x34eb,0x655d},
{0x34ec,0x34ec,0x665f},
{0x34ed,0x34ed,0x67a9},
{0x34ee,0x34ee,0x67e7},
{0x34ef,0x34ef,0x696b},
{0x34f0,0x34f0,0x6930},
{0x34f1,0x34f1,0x69a7},
{0x34f2,0x34f2,0x6a44},
{0x34f3,0x34f3,0x6a90},
{0x34f4,0x34f4,0x6c08},
{0x34f5,0x34f5,0x6c13},
{0x34f6,0x34f6,0x6e23},
{0x34f7,0x34f7,0x6f11},
{0x34f8,0x34f8,0x6efe},
{0x34f9,0x34f9,0x6f3e},
{0x34fa,0x34fa,0x71ff},
{0x34fb,0x34fb,0x73e5},
{0x34fc,0x34fc,0x7432},
{0x34fd,0x34fd,0x745f},
{0x34fe,0x34fe,0x74e0},
{0x34ff,0x34ff,0x750c},
{0x3500,0x3500,0x7672},
{0x3501,0x3501,0x792a},
{0x3502,0x3502,0x78d4},
{0x3503,0x3503,0x79ba},
{0x3504,0x3504,0x7a19},
{0x3505,0x3505,0x7a95},
{0x3506,0x3506,0x7cf2},
{0x3507,0x3507,0x7d73},
{0x3508,0x3508,0x7ddd},
{0x3509,0x3509,0x7e35},
{0x350a,0x350a,0x7fae},
{0x350b,0x350b,0x7fe1},
{0x350c,0x350c,0x805a},
{0x350d,0x350d,0x805f},
{0x350e,0x350e,0x8073},
{0x350f,0x350f,0x8070},
{0x3510,0x3510,0x8076},
{0x3511,0x3511,0x8153},
{0x3512,0x3512,0x818a},
{0x3513,0x3513,0x81b5},
{0x3514,0x3514,0x81cd},
{0x3515,0x3515,0x83f2},
{0x3516,0x3516,0x8555},
{0x3517,0x3517,0x85d5},
{0x3518,0x3518,0x871a},
{0x3519,0x3519,0x8836},
{0x351a,0x351a,0x889e},
{0x351b,0x351b,0x88d8},
{0x351c,0x351c,0x88f4},
{0x351d,0x351d,0x892b},
{0x351e,0x351e,0x893b},
{0x351f,0x351f,0x896a},
{0x3520,0x3520,0x896f},
{0x3521,0x3521,0x8a1d},
{0x3522,0x3522,0x8d05},
{0x3523,0x3523,0x8d0f},
{0x3524,0x3524,0x9f4e},
{0x3525,0x3525,0x8e91},
{0x3526,0x3526,0x8ea1},
{0x3527,0x3527,0x9052},
{0x3528,0x3528,0x900e},
{0x3529,0x3529,0x9130},
{0x352a,0x352a,0x9156},
{0x352b,0x352b,0x9158},
{0x352c,0x352c,0x9165},
{0x352d,0x352d,0x9173},
{0x352e,0x352e,0x9172},
{0x352f,0x352f,0x91a2},
{0x3530,0x3530,0x91af},
{0x3531,0x3531,0x91aa},
{0x3532,0x3532,0x91b4},
{0x3533,0x3533,0x91ba},
{0x3534,0x3534,0x9477},
{0x3535,0x3535,0x9698},
{0x3536,0x3536,0x973d},
{0x3537,0x3537,0x9760},
{0x3538,0x3538,0x9771},
{0x3539,0x3539,0x980c},
{0x353a,0x353a,0x9873},
{0x353b,0x353b,0x98c3},
{0x353c,0x353c,0x9a45},
{0x353d,0x353d,0x9b4d},
{0x353e,0x353e,0x9b58},
{0x353f,0x353f,0x9bc6},
{0x3540,0x3540,0x9be1},
{0x3541,0x3541,0x9bf1},
{0x3542,0x3542,0x9d48},
{0x3543,0x3543,0x9dcf},
{0x3544,0x3544,0x9f08},
{0x3545,0x3545,0x6271},
{0x3546,0x3546,0x6697},
{0x3547,0x3547,0x610f},
{0x3548,0x3548,0x8863},
{0x3549,0x3549,0x9055},
{0x354a,0x354a,0x907a},
{0x354b,0x354b,0x78ef},
{0x354c,0x354c,0x8c9f},
{0x354d,0x354d,0x2ed7},
{0x354e,0x354e,0x2ebd},
{0x354f,0x354f,0x5ed0},
{0x3550,0x3550,0x74dc},
{0x3551,0x3551,0x904b},
{0x3552,0x3552,0x990c},
{0x3553,0x3553,0x885e},
{0x3554,0x3554,0x92b3},
{0x3555,0x3555,0x95b1},
{0x3556,0x3556,0x5ef6},
{0x3557,0x3557,0x63f4},
{0x3559,0x3559,0x7159},
{0x355a,0x355a,0x9060},
{0x355b,0x355b,0x925b},
{0x355c,0x355c,0x65bc},
{0x355d,0x355d,0x5f80},
{0x355e,0x355e,0x7fc1},
{0x355f,0x355f,0x5378},
{0x3560,0x3560,0x97f3},
{0x3561,0x3561,0x5316},
{0x3562,0x3562,0x82b1},
{0x3563,0x3563,0x83d3},
{0x3564,0x3564,0x8ca8},
{0x3565,0x3565,0x904e},
{0x3567,0x3567,0x96c5},
{0x3568,0x3568,0x9913},
{0x3569,0x3569,0x5efb},
{0x356a,0x356a,0x7070},
{0x356b,0x356b,0x5bb3},
{0x356c,0x356c,0x6168},
{0x356d,0x356d,0x6168},
{0x356e,0x356e,0x6168},
{0x3570,0x3570,0x6982},
{0x3571,0x3571,0x676e},
{0x3572,0x3572,0x2ec6},
{0x3573,0x3573,0x9694},
{0x3575,0x3575,0x8f44},
{0x3576,0x3576,0x938c},
{0x3577,0x3577,0x82c5},
{0x3578,0x3578,0x5bd2},
{0x3579,0x3579,0x74b0},
{0x357a,0x357a,0x7de9},
{0x357b,0x357b,0x7f36},
{0x357c,0x357c,0x9084},
{0x357d,0x357d,0x9592},
{0x357e,0x357e,0x97d3},
{0x357f,0x357f,0x8218},
{0x3580,0x3580,0x5371},
{0x3581,0x3581,0x3402},
{0x3582,0x3582,0x3402},
{0x3583,0x3583,0x3402},
{0x3584,0x3584,0x5e7e},
{0x3585,0x3585,0x65e3},
{0x3586,0x3586,0x671f},
{0x3587,0x3587,0x6a5f},
{0x3588,0x3588,0x8d77},
{0x3589,0x3589,0x98e2},
{0x358b,0x358b,0x55ab},
{0x358c,0x358c,0x8650},
{0x358d,0x358d,0x9006},
{0x358e,0x358e,0x53ca},
{0x358f,0x358f,0x5438},
{0x3590,0x3590,0x6025},
{0x3591,0x3591,0x7d1a},
{0x3592,0x3592,0x5de8},
{0x3593,0x3593,0x62d2},
{0x3594,0x3594,0x8ddd},
{0x3598,0x3598,0x5f3a},
{0x3599,0x3599,0x6050},
{0x359a,0x359a,0x69c1},
{0x359d,0x359d,0x90f7},
{0x359e,0x359e,0x97ff},
{0x359f,0x359f,0x9957},
{0x35a0,0x35a0,0x6681},
{0x35a1,0x35a1,0x2ea9},
{0x35a2,0x35a2,0x8fd1},
{0x35a3,0x35a3,0x4ff1},
{0x35a4,0x35a4,0x77e9},
{0x35a5,0x35a5,0x5177},
{0x35a6,0x35a6,0x865e},
{0x35a7,0x35a7,0x7a7a},
{0x35a8,0x35a8,0x9047},
{0x35a9,0x35a9,0x6adb},
{0x35aa,0x35aa,0x5553},
{0x35ab,0x35ab,0xf909},
{0x35ac,0x35ac,0x6075},
{0x35ad,0x35ad,0x6167},
{0x35ae,0x35ae,0x8fce},
{0x35af,0x35af,0x5091},
{0x35b0,0x35b0,0x6f54},
{0x35b1,0x35b1,0x7a74},
{0x35b2,0x35b2,0x6708},
{0x35b3,0x35b3,0x2ebc},
{0x35b4,0x35b4,0x517c},
{0x35b5,0x35b5,0x5238},
{0x35b7,0x35b7,0x6743},
{0x35b8,0x35b8,0x80a9},
{0x35b9,0x35b9,0x8b19},
{0x35ba,0x35ba,0x9063},
{0x35bc,0x35bc,0x8a01},
{0x35bd,0x35bd,0x6236},
{0x35be,0x35be,0x96c7},
{0x35bf,0x35bf,0x9867},
{0x35c0,0x35c0,0x5433},
{0x35c1,0x35c1,0x5a1b},
{0x35c2,0x35c2,0x8aa4},
{0x35c3,0x35c3,0x5de5},
{0x35c4,0x35c4,0x614c},
{0x35c5,0x35c5,0x6285},
{0x35c6,0x35c6,0x63a7},
{0x35c7,0x35c7,0x69cb},
{0x35c9,0x35c9,0x6e2f},
{0x35ca,0x35ca,0x8015},
{0x35cb,0x35cb,0x8154},
{0x35cc,0x35cc,0x8352},
{0x35cd,0x35cd,0x8b1b},
{0x35ce,0x35ce,0x8cfc},
{0x35cf,0x35cf,0x543f},
{0x35d0,0x35d0,0x9177},
{0x35d1,0x35d1,0x8170},
{0x35d2,0x35d2,0x7511},
{0x35d3,0x35d3,0x8fbc},
{0x35d4,0x35d4,0x4eca},
{0x35d5,0x35d5,0x9396},
{0x35d6,0x35d6,0x5ea7},
{0x35d7,0x35d7,0x5f69},
{0x35d8,0x35d8,0x63a1},
{0x35d9,0x35d9,0x6b72},
{0x35da,0x35da,0x83dc},
{0x35db,0x35db,0x51b4},
{0x35dc,0x35dc,0x54b2},
{0x35dd,0x35dd,0x524a},
{0x35de,0x35de,0x7522},
{0x35df,0x35df,0x4b38},
{0x35e0,0x35e0,0x59ff},
{0x35e1,0x35e1,0x59ff},
{0x35e2,0x35e2,0x5dff},
{0x35e3,0x35e3,0x8aee},
{0x35e4,0x35e4,0x8aee},
{0x35e5,0x35e5,0x8cc7},
{0x35e6,0x35e6,0x8cc7},
{0x35e7,0x35e7,0x6b21},
{0x35e8,0x35e8,0x6b21},
{0x35ea,0x35ea,0x73ba},
{0x35ec,0x35ec,0x6368},
{0x35ed,0x35ed,0x659c},
{0x35ee,0x35ee,0x90aa},
{0x35f0,0x35f0,0x7235},
{0x35f2,0x35f2,0x914c},
{0x35f3,0x35f3,0x5f31},
{0x35f4,0x35f4,0x4e3b},
{0x35f5,0x35f5,0x53d7},
{0x35f6,0x35f6,0x6388},
{0x35f8,0x35f8,0x7d42},
{0x35f9,0x35f9,0x7fd2},
{0x35fa,0x35fa,0x8846},
{0x35fb,0x35fb,0x9031},
{0x35fc,0x35fc,0x4f4f},
{0x35fd,0x35fd,0x8853},
{0x35fe,0x35fe,0x8ff0},
{0x35ff,0x35ff,0x5de1},
{0x3600,0x3600,0x9075},
{0x3601,0x3601,0x9075},
{0x3602,0x3602,0x6240},
{0x3603,0x3603,0x66f8},
{0x3604,0x3604,0x5973},
{0x3605,0x3605,0x52dd},
{0x3606,0x3606,0x5546},
{0x3607,0x3607,0x5bb5},
{0x3609,0x360a,0x2e8c},
{0x360b,0x360b,0x5c19},
{0x360c,0x360c,0x6d88},
{0x360d,0x360d,0x785d},
{0x360e,0x360e,0x8096},
{0x3610,0x3610,0x5b82},
{0x3612,0x3612,0x60c5},
{0x3613,0x3613,0x57f4},
{0x3614,0x3614,0x98fe},
{0x3615,0x3615,0x690d},
{0x3616,0x3616,0x6b96},
{0x3617,0x3617,0x2edd},
{0x3618,0x3618,0x98e0},
{0x3619,0x3619,0x2ede},
{0x361a,0x361a,0x378d},
{0x361b,0x361b,0x4fb5},
{0x361c,0x361c,0x2e97},
{0x361d,0x361d,0x6d78},
{0x361f,0x361f,0x9032},
{0x3620,0x3620,0x4ebb},
{0x3622,0x3622,0x5203},
{0x3623,0x3623,0x5c0b},
{0x3624,0x3624,0x8a0a},
{0x3625,0x3625,0x8a0a},
{0x3626,0x3626,0x8fc5},
{0x3627,0x3627,0x8870},
{0x3628,0x3628,0x9042},
{0x3629,0x3629,0x351f},
{0x362a,0x362a,0x52e2},
{0x362c,0x362c,0x76db},
{0x362d,0x362d,0x8056},
{0x362e,0x362e,0x8980},
{0x362f,0x362f,0x8aa0},
{0x3630,0x3630,0x8acb},
{0x3631,0x3631,0x975c},
{0x3632,0x3632,0x975c},
{0x3633,0x3633,0x7a05},
{0x3634,0x3634,0x8106},
{0x3635,0x3635,0x96bb},
{0x3636,0x3636,0x7c4d},
{0x3637,0x3637,0x7bc0},
{0x3638,0x3638,0x8aaa},
{0x3639,0x3639,0x96ea},
{0x363a,0x363a,0x7d55},
{0x363b,0x363b,0x6247},
{0x363c,0x363c,0x6f98},
{0x363d,0x363d,0x7fa1},
{0x363e,0x363e,0x8239},
{0x363f,0x363f,0x9078},
{0x3640,0x3640,0x9077},
{0x3641,0x3641,0x524d},
{0x3642,0x3642,0x5168},
{0x3643,0x3643,0x6383},
{0x3644,0x3644,0x63f7},
{0x3645,0x3645,0x7626},
{0x3646,0x3646,0x8d70},
{0x3647,0x3647,0x9001},
{0x3648,0x3648,0x906d},
{0x3649,0x3649,0x9020},
{0x364a,0x364a,0x2eca},
{0x364b,0x364b,0x901f},
{0x364c,0x364c,0x8cca},
{0x364d,0x364d,0x5c0a},
{0x364e,0x364e,0x5c0a},
{0x364f,0x364f,0x59a5},
{0x3651,0x3651,0x9000},
{0x3652,0x3652,0x902e},
{0x3653,0x3653,0x968a},
{0x3654,0x3654,0x9bdb},
{0x3655,0x3655,0x5927},
{0x3656,0x3656,0x3427},
{0x3657,0x3657,0x7027},
{0x3658,0x3658,0x9039},
{0x3659,0x3659,0x812b},
{0x365a,0x365a,0x4e39},
{0x365b,0x365b,0x6b4e},
{0x365d,0x365d,0x8a95},
{0x365e,0x365e,0x6696},
{0x365f,0x365f,0x5024},
{0x3660,0x3660,0x7f6e},
{0x3661,0x3661,0x7bc9},
{0x3662,0x3662,0x2eae},
{0x3663,0x3663,0x7b51},
{0x3664,0x3664,0x9010},
{0x3665,0x3665,0x67f1},
{0x3666,0x3666,0x6ce8},
{0x3667,0x3667,0x99d0},
{0x3669,0x3669,0x5fb5},
{0x366a,0x366a,0x61f2},
{0x366b,0x366b,0x671d},
{0x366d,0x366d,0x8abf},
{0x366e,0x366e,0x76f4},
{0x366f,0x366f,0x6715},
{0x3670,0x3670,0x6715},
{0x3671,0x3671,0x589c},
{0x3672,0x3672,0x8ffd},
{0x3673,0x3673,0x901a},
{0x3674,0x3674,0x576a},
{0x3675,0x3675,0x91e3},
{0x3676,0x3676,0x5448},
{0x3677,0x3677,0x5e1d},
{0x3678,0x3678,0x7a0b},
{0x3679,0x3679,0x7684},
{0x367a,0x367a,0x9069},
{0x367b,0x367b,0x8fed},
{0x367c,0x367c,0x6dfb},
{0x367d,0x367d,0x514e},
{0x367e,0x367e,0x9014},
{0x367f,0x367f,0x783a},
{0x3680,0x3680,0x5721},
{0x3683,0x3683,0x5510},
{0x3684,0x3684,0xfa03},
{0x3685,0x3685,0x85e4},
{0x3686,0x3686,0x8b04},
{0x3687,0x3687,0x9003},
{0x3688,0x3688,0x900f},
{0x3689,0x3689,0x9a30},
{0x368a,0x368a,0x5c0e},
{0x368b,0x368b,0x9053},
{0x368c,0x368c,0x541e},
{0x368d,0x368d,0x3b88},
{0x368e,0x368e,0x5167},
{0x368f,0x368f,0x8089},
{0x3690,0x3690,0x4e73},
{0x3691,0x3691,0x5fcd},
{0x3692,0x3692,0x8a8d},
{0x3693,0x3693,0xf95f},
{0x3694,0x3694,0x7d0d},
{0x3695,0x3695,0x8987},
{0x3696,0x3696,0x6d3e},
{0x3697,0x3697,0x80ba},
{0x3698,0x3698,0x535a},
{0x3699,0x3699,0x8584},
{0x369a,0x369a,0x8feb},
{0x369b,0x369b,0x7e1b},
{0x369c,0x369c,0x8087},
{0x369d,0x369d,0x4e37},
{0x369e,0x369e,0x6f51},
{0x369f,0x369f,0x91b1},
{0x36a0,0x36a0,0x4f34},
{0x36a1,0x36a1,0x5224},
{0x36a2,0x36a2,0x534a},
{0x36a3,0x36a3,0x5e06},
{0x36a4,0x36a4,0x7554},
{0x36a5,0x36a5,0x6669},
{0x36a6,0x36a6,0x8543},
{0x36a7,0x36a7,0x907f},
{0x36a8,0x36a8,0x5fae},
{0x36a9,0x36a9,0x9f3b},
{0x36aa,0x36aa,0x5339},
{0x36ab,0x36ab,0x2eaa},
{0x36ac,0x36ac,0x5f65},
{0x36ad,0x36ad,0x59ec},
{0x36af,0x36af,0x8a55},
{0x36b0,0x36b0,0x5e99},
{0x36b1,0x36b1,0x75c5},
{0x36b2,0x36b2,0x5a66},
{0x36b3,0x36b3,0x6577},
{0x36b4,0x36b4,0x6d6e},
{0x36b5,0x36b5,0x8ca0},
{0x36b6,0x36b6,0x8ca0},
{0x36b7,0x36b7,0x670d},
{0x36b8,0x36b8,0x8986},
{0x36b9,0x36b9,0x4e19},
{0x36ba,0x36ba,0x5e63},
{0x36bb,0x36bb,0x5e73},
{0x36bc,0x36bc,0x5f0a},
{0x36bd,0x36bd,0x8511},
{0x36be,0x36be,0x504f},
{0x36bf,0x36bf,0x7de8},
{0x36c0,0x36c0,0x8fd4},
{0x36c1,0x36c1,0x904d},
{0x36c2,0x36c2,0x7c3f},
{0x36c3,0x36c3,0x5305},
{0x36c4,0x36c4,0x5d29},
{0x36c5,0x36c5,0x62b1},
{0x36c6,0x36c6,0x670b},
{0x36c7,0x36c7,0x7832},
{0x36c8,0x36c8,0x7e2b},
{0x36c9,0x36c9,0x80de},
{0x36ca,0x36ca,0x840c},
{0x36cb,0x36cb,0x90a6},
{0x36cc,0x36cc,0x90a6},
{0x36cd,0x36cd,0x98fd},
{0x36ce,0x36ce,0x9d6c},
{0x36cf,0x36cf,0x4ea1},
{0x36d0,0x36d0,0x5e3d},
{0x36d1,0x36d2,0x5fd8},
{0x36d3,0x36d3,0x623f},
{0x36d4,0x36d4,0x671b},
{0x36d5,0x36d5,0x671b},
{0x36d6,0x36d6,0x5192},
{0x36d7,0x36d7,0x6469},
{0x36d8,0x36d8,0x7ffb},
{0x36d9,0x36d9,0x51e1},
{0x36da,0x36da,0x78e8},
{0x36db,0x36db,0x9b54},
{0x36dc,0x36dc,0x9ebb},
{0x36dd,0x36dd,0x69d9},
{0x36de,0x36de,0x685d},
{0x36df,0x36df,0x3468},
{0x36e0,0x36e0,0x9fb4},
{0x36e1,0x36e1,0x7e6d},
{0x36e2,0x36e2,0x9ebf},
{0x36e3,0x36e3,0x8108},
{0x36e4,0x36e4,0x660e},
{0x36e5,0x36e5,0x76df},
{0x36e6,0x36e6,0x8ff7},
{0x36e7,0x36e7,0x5984},
{0x36e8,0x36e8,0x52d0},
{0x36e9,0x36e9,0x76f2},
{0x36ea,0x36ea,0x8017},
{0x36eb,0x36eb,0x623b},
{0x36ec,0x36ec,0x7d0b},
{0x36ed,0x36ed,0x9580},
{0x36ee,0x36ee,0x7d04},
{0x36ef,0x36ef,0x8e8d},
{0x36f0,0x36f0,0x687a},
{0x36f1,0x36f1,0x687a},
{0x36f2,0x36f2,0x6801},
{0x36f3,0x36f3,0x6109},
{0x36f6,0x36f6,0x52c7},
{0x36f7,0x36f7,0x6709},
{0x36f8,0x36f8,0x7336},
{0x36f9,0x36fa,0x7336},
{0x36fc,0x36fc,0x904a},
{0x36fd,0x36fd,0x66dc},
{0x36fe,0x36fe,0x2eb7},
{0x36ff,0x36ff,0x8981},
{0x3700,0x3700,0x990a},
{0x3701,0x3701,0x7fcc},
{0x3702,0x3702,0x7ffc},
{0x3703,0x3703,0x8eb6},
{0x3704,0x3704,0xf91f},
{0x3705,0x3705,0x7387},
{0x3706,0x3706,0x9f8d},
{0x3707,0x3707,0x9f8d},
{0x3708,0x3708,0xf983},
{0x3709,0x3709,0x6881},
{0x370a,0x370a,0x71d0},
{0x370b,0x370b,0x96a3},
{0x370c,0x370c,0x9c57},
{0x370d,0x370d,0x9e9f},
{0x370e,0x370e,0x7c7b},
{0x370f,0x370f,0xf9a2},
{0x3710,0x3710,0x6190},
{0x3711,0x3711,0xf99a},
{0x3712,0x3712,0x6717},
{0x3713,0x3713,0x8002},
{0x3715,0x3715,0x50ca},
{0x3716,0x3716,0x511a},
{0x3717,0x3717,0x5154},
{0x3718,0x3718,0x5195},
{0x3719,0x3719,0x2e87},
{0x371a,0x371a,0x528d},
{0x371b,0x371b,0x52d7},
{0x371e,0x371e,0x353e},
{0x371f,0x371f,0x53df},
{0x3720,0x3720,0x66fc},
{0x3721,0x3721,0x5533},
{0x3722,0x3722,0x55e4},
{0x3723,0x3723,0x5455},
{0x3724,0x3724,0x56c0},
{0x3725,0x3725,0x5939},
{0x3726,0x3726,0x5a1c},
{0x3727,0x3727,0x5ac2},
{0x3728,0x3728,0x5b76},
{0x372a,0x372a,0x5c14},
{0x372b,0x372b,0x37e2},
{0x372c,0x372c,0x5ce6},
{0x372d,0x372d,0x5e54},
{0x372e,0x372e,0x4e48},
{0x372f,0x372f,0x5ee3},
{0x3730,0x3730,0x6097},
{0x3732,0x3732,0x62cc},
{0x3733,0x3733,0x641c},
{0x3734,0x3734,0x63c6},
{0x3735,0x3735,0x6428},
{0x3737,0x3737,0x631b},
{0x3738,0x3738,0x665f},
{0x3739,0x3739,0x665f},
{0x373a,0x373a,0x6663},
{0x373b,0x373b,0x66f5},
{0x373d,0x373d,0x689d},
{0x373e,0x373e,0x688d},
{0x373f,0x373f,0x69f6},
{0x3740,0x3740,0x6986},
{0x3741,0x3741,0x5be8},
{0x3742,0x3742,0x5be8},
{0x3743,0x3743,0x69bb},
{0x3744,0x3744,0x6bcc},
{0x3745,0x3745,0x6f11},
{0x3746,0x3746,0x6e17},
{0x3747,0x3747,0x6f98},
{0x3748,0x3748,0x6caa},
{0x3749,0x3749,0x6caa},
{0x374a,0x374a,0x701b},
{0x374b,0x374b,0x7162},
{0x374c,0x374c,0x723b},
{0x374d,0x374d,0x4e2c},
{0x374e,0x374e,0x731c},
{0x374f,0x374f,0x74ca},
{0x3750,0x3750,0x74ef},
{0x3751,0x3751,0x7575},
{0x3752,0x3752,0x75ec},
{0x3753,0x3753,0x764e},
{0x3754,0x3754,0x3fb1},
{0x3755,0x3755,0x776a},
{0x3756,0x3756,0x77a9},
{0x3757,0x3757,0x7940},
{0x3758,0x3758,0x7953},
{0x3759,0x3759,0x7953},
{0x375a,0x375a,0x9f4b},
{0x375b,0x375b,0x79ae},
{0x375c,0x375c,0x9083},
{0x375d,0x375d,0x7b53},
{0x375e,0x375e,0x7c14},
{0x375f,0x375f,0x7c14},
{0x3760,0x3760,0x4264},
{0x3761,0x3761,0x7c50},
{0x3762,0x3762,0x7c58},
{0x3763,0x3763,0x7d46},
{0x3765,0x3765,0x7e22},
{0x3766,0x3766,0x7e22},
{0x3767,0x3767,0x7e48},
{0x3768,0x3768,0x7e35},
{0x3769,0x3769,0x7e43},
{0x376a,0x376a,0x7e8c},
{0x376b,0x376c,0x7f50},
{0x376d,0x376d,0x7f51},
{0x376f,0x3770,0x7fc5},
{0x3771,0x3771,0x7fe9},
{0x3772,0x3772,0x8141},
{0x3773,0x3773,0x4453},
{0x3774,0x3774,0x8258},
{0x3775,0x3775,0x8279},
{0x3776,0x3777,0x2ebf},
{0x3778,0x3778,0x82e3},
{0x3779,0x3779,0x5179},
{0x377a,0x377a,0x835a},
{0x377b,0x377b,0x8420},
{0x377c,0x377c,0x83bd},
{0x377d,0x377d,0x84f4},
{0x377e,0x377e,0x4525},
{0x377f,0x377f,0x8587},
{0x3780,0x3780,0x85f4},
{0x3782,0x3782,0x8737},
{0x3783,0x3783,0x873b},
{0x3784,0x3784,0x8805},
{0x3785,0x3785,0x87bd},
{0x3786,0x3786,0x867d},
{0x3787,0x3787,0x8836},
{0x3788,0x3788,0x342e},
{0x3789,0x3789,0x88c6},
{0x378a,0x378a,0x89bd},
{0x378b,0x378b,0x8adb},
{0x378c,0x378c,0x8b3e},
{0x378d,0x378d,0x8b5a},
{0x378e,0x378e,0x8d73},
{0x378f,0x378f,0x8d99},
{0x3790,0x3790,0x47e6},
{0x3791,0x3791,0x8e34},
{0x3792,0x3792,0x8e4a},
{0x3793,0x3793,0x8fef},
{0x3794,0x3794,0x9087},
{0x3795,0x3795,0x8ffa},
{0x3796,0x3796,0x901e},
{0x3797,0x3797,0x9035},
{0x3798,0x3798,0x9050},
{0x3799,0x3799,0x8fc8},
{0x379a,0x379a,0x9081},
{0x379b,0x379b,0x908a},
{0x379c,0x379c,0x908a},
{0x379d,0x379d,0x908a},
{0x379e,0x379e,0x908a},
{0x379f,0x379f,0x908a},
{0x37a0,0x37a0,0x908a},
{0x37a1,0x37a1,0x9089},
{0x37a2,0x37a2,0x9089},
{0x37a3,0x37a3,0x9089},
{0x37a4,0x37a4,0x9089},
{0x37a5,0x37a5,0x9089},
{0x37a6,0x37a6,0x9089},
{0x37a7,0x37a7,0x9089},
{0x37a8,0x37a8,0x9089},
{0x37a9,0x37a9,0x9089},
{0x37aa,0x37aa,0x9089},
{0x37ab,0x37ab,0x9089},
{0x37ac,0x37ac,0x9089},
{0x37ae,0x37ae,0x93dd},
{0x37af,0x37af,0x95bc},
{0x37b1,0x37b1,0x96b2},
{0x37b2,0x37b2,0x975c},
{0x37b3,0x37b3,0x9839},
{0x37b4,0x37b4,0x98eb},
{0x37b5,0x37b5,0x9903},
{0x37b6,0x37b6,0x9909},
{0x37b7,0x37b7,0x9945},
{0x37b8,0x37b8,0x9945},
{0x37b9,0x37b9,0x994b},
{0x37ba,0x37ba,0x9a08},
{0x37bb,0x37bb,0x9a5f},
{0x37bc,0x37bc,0x9a65},
{0x37bd,0x37bd,0x9aef},
{0x37be,0x37be,0x9b18},
{0x37bf,0x37bf,0x9bdf},
{0x37c0,0x37c0,0x9d09},
{0x37c1,0x37c1,0x9d08},
{0x37c2,0x37c2,0x9ea5},
{0x37c3,0x37c3,0x9ecc},
{0x37c4,0x37c4,0x9f08},
{0x37c5,0x37c5,0x9f4a},
{0x37c6,0x37c6,0x9f63},
{0x37c7,0x37c7,0x9f67},
{0x37cb,0x37cb,0x891c},
{0x37cc,0x37cc,0x68c8},
{0x37cd,0x37cd,0x66fb},
{0x37ce,0x37ce,0x5f45},
{0x37cf,0x37cf,0x5300},
{0x37d0,0x37d0,0x5389},
{0x37d1,0x37d1,0x5953},
{0x37d2,0x37d2,0xfa11},
{0x37d4,0x37d4,0x6a73},
{0x37d7,0x37d7,0x8a12},
{0x37d8,0x37d9,0x4e04},
{0x37da,0x37da,0x4e1f},
{0x37db,0x37db,0x4e2b},
{0x37dc,0x37dd,0x4e2f},
{0x37de,0x37df,0x4e40},
{0x37e0,0x37e0,0x4e44},
{0x37e1,0x37e1,0x4e5a},
{0x37e2,0x37e2,0x4e7f},
{0x37e3,0x37e3,0x4e8d},
{0x37e4,0x37e4,0x4e96},
{0x37e5,0x37e5,0x4eb9},
{0x37e6,0x37e6,0x4ed0},
{0x37e7,0x37e7,0x4ee0},
{0x37e8,0x37e8,0x4efd},
{0x37e9,0x37e9,0x4eff},
{0x37ea,0x37ea,0x4f0b},
{0x37eb,0x37eb,0x4f15},
{0x37ec,0x37ec,0x4f60},
{0x37ed,0x37ed,0x4f3b},
{0x37ee,0x37ee,0x4f49},
{0x37ef,0x37ef,0x4f54},
{0x37f0,0x37f0,0x4f7a},
{0x37f1,0x37f2,0x4f7d},
{0x37f3,0x37f3,0x4f97},
{0x37f4,0x37f4,0x4fbe},
{0x37f5,0x37f5,0x4fcf},
{0x37f6,0x37f6,0x4ffd},
{0x37f7,0x37f8,0x5000},
{0x37f9,0x37f9,0x5010},
{0x37fa,0x37fa,0x501b},
{0x37fb,0x37fb,0x5027},
{0x37fc,0x37fc,0x502e},
{0x37fd,0x37fd,0x5057},
{0x37fe,0x37fe,0x5066},
{0x37ff,0x37ff,0x506a},
{0x3800,0x3800,0x503b},
{0x3801,0x3801,0x508f},
{0x3802,0x3802,0x5096},
{0x3803,0x3803,0x509c},
{0x3804,0x3804,0x50cc},
{0x3805,0x3805,0x50e6},
{0x3806,0x3806,0x50e9},
{0x3807,0x3807,0x50ef},
{0x3808,0x3808,0x5108},
{0x3809,0x3809,0x510b},
{0x380a,0x380a,0x5110},
{0x380b,0x380b,0x511b},
{0x380c,0x380c,0x511e},
{0x380d,0x380d,0x515f},
{0x380e,0x380e,0x51a1},
{0x380f,0x380f,0x51bc},
{0x3810,0x3810,0x51de},
{0x3811,0x3811,0x51ee},
{0x3812,0x3812,0x51f4},
{0x3813,0x3814,0x5201},
{0x3815,0x3815,0x5213},
{0x3816,0x3816,0x5249},
{0x3817,0x3817,0x5261},
{0x3818,0x3818,0x5266},
{0x3819,0x3819,0x5293},
{0x381a,0x381a,0x52c8},
{0x381b,0x381b,0x52f0},
{0x381c,0x381d,0x530a},
{0x381e,0x381e,0x533e},
{0x381f,0x381f,0x534c},
{0x3820,0x3820,0x534b},
{0x3821,0x3821,0x5361},
{0x3822,0x3822,0x536c},
{0x3823,0x3823,0x53ab},
{0x3824,0x3824,0x53da},
{0x3825,0x3825,0x53e6},
{0x3826,0x3826,0x53f5},
{0x3827,0x3827,0x5427},
{0x3828,0x3828,0x544d},
{0x3829,0x3829,0x5466},
{0x382a,0x382a,0x546b},
{0x382b,0x382b,0x5474},
{0x382c,0x382c,0x548d},
{0x382d,0x382d,0x5496},
{0x382e,0x382e,0x54a1},
{0x382f,0x382f,0x54ad},
{0x3830,0x3830,0x54b9},
{0x3831,0x3831,0x54bf},
{0x3832,0x3832,0x54c6},
{0x3833,0x3833,0x54cd},
{0x3834,0x3834,0x550e},
{0x3835,0x3835,0x552b},
{0x3836,0x3836,0x5535},
{0x3837,0x3837,0x554a},
{0x3838,0x3839,0x5560},
{0x383a,0x383a,0x5588},
{0x383b,0x383b,0x558e},
{0x383c,0x383c,0x5608},
{0x383d,0x383e,0x560e},
{0x383f,0x383f,0x5637},
{0x3840,0x3840,0x563f},
{0x3841,0x3841,0x5649},
{0x3842,0x3842,0x564b},
{0x3843,0x3843,0x564f},
{0x3844,0x3844,0x5666},
{0x3845,0x3845,0x5669},
{0x3846,0x3846,0x566f},
{0x3847,0x3848,0x5671},
{0x3849,0x3849,0x5695},
{0x384a,0x384a,0x569a},
{0x384b,0x384c,0x56ac},
{0x384d,0x384d,0x56b1},
{0x384e,0x384e,0x56c9},
{0x384f,0x384f,0x56dd},
{0x3850,0x3850,0x56e4},
{0x3851,0x3851,0x570a},
{0x3852,0x3852,0x5715},
{0x3853,0x3853,0x5723},
{0x3854,0x3854,0x572f},
{0x3855,0x3856,0x5733},
{0x3857,0x3857,0x574c},
{0x3858,0x3858,0x5770},
{0x3859,0x3859,0x578c},
{0x385a,0x385a,0x579c},
{0x385b,0x385b,0x57b8},
{0x385c,0x385c,0x57e6},
{0x385d,0x385d,0x57ed},
{0x385e,0x385f,0x57f5},
{0x3860,0x3860,0x57ff},
{0x3861,0x3861,0x5809},
{0x3862,0x3862,0x5820},
{0x3863,0x3863,0x5832},
{0x3864,0x3864,0x587c},
{0x3865,0x3865,0x5880},
{0x3866,0x3866,0x58a9},
{0x3867,0x3867,0x58ce},
{0x3868,0x3868,0x58d0},
{0x3869,0x3869,0x58d4},
{0x386a,0x386a,0x58da},
{0x386b,0x386b,0x58e9},
{0x386c,0x386c,0x590c},
{0x386d,0x386d,0x5924},
{0x386e,0x386e,0x592f},
{0x386f,0x386f,0x5961},
{0x3870,0x3870,0x596d},
{0x3871,0x3871,0x59ca},
{0x3872,0x3872,0x59d2},
{0x3873,0x3873,0x59dd},
{0x3874,0x3875,0x59e3},
{0x3876,0x3876,0x5a04},
{0x3877,0x3877,0x5a0c},
{0x3878,0x3878,0x5a23},
{0x3879,0x3879,0x5a47},
{0x387a,0x387a,0x5a55},
{0x387b,0x387b,0x5a63},
{0x387c,0x387c,0x5a6d},
{0x387d,0x387d,0x5a7e},
{0x387e,0x387e,0x5a9e},
{0x387f,0x387f,0x5aa7},
{0x3880,0x3880,0x5aac},
{0x3881,0x3881,0x5ab3},
{0x3882,0x3882,0x5ae0},
{0x3883,0x3883,0x5b00},
{0x3884,0x3884,0x5b19},
{0x3885,0x3885,0x5b25},
{0x3886,0x3886,0x5b2d},
{0x3887,0x3887,0x5b41},
{0x3888,0x3888,0x5b7c},
{0x3889,0x388a,0x5b7e},
{0x388b,0x388b,0x5b8a},
{0x388c,0x388c,0x5c23},
{0x388d,0x388d,0x5c2b},
{0x388e,0x388e,0x5c30},
{0x388f,0x388f,0x5c63},
{0x3890,0x3890,0x5c69},
{0x3891,0x3891,0x5c7c},
{0x3892,0x3892,0x5ccb},
{0x3893,0x3893,0x5cd2},
{0x3894,0x3894,0x5cf4},
{0x3895,0x3895,0x5d24},
{0x3896,0x3896,0x5d26},
{0x3897,0x3897,0x5d43},
{0x3898,0x3898,0x5d46},
{0x3899,0x3899,0x5d4a},
{0x389a,0x389a,0x5d92},
{0x389b,0x389b,0x5d94},
{0x389c,0x389c,0x5d99},
{0x389d,0x389d,0x5da0},
{0x389e,0x389e,0x5dd8},
{0x389f,0x389f,0x5de0},
{0x38a0,0x38a0,0x5df8},
{0x38a1,0x38a1,0x5e00},
{0x38a2,0x38a2,0x5e12},
{0x38a3,0x38a4,0x5e14},
{0x38a5,0x38a5,0x5e18},
{0x38a6,0x38a6,0x5e2e},
{0x38a7,0x38a7,0x5e58},
{0x38a8,0x38a9,0x5e6b},
{0x38aa,0x38aa,0x5ea8},
{0x38ab,0x38ab,0x5eaa},
{0x38ac,0x38ad,0x5ebe},
{0x38ae,0x38ae,0x5ecb},
{0x38af,0x38af,0x5ed2},
{0x38b0,0x38b0,0x5f07},
{0x38b1,0x38b1,0x5f0e},
{0x38b2,0x38b3,0x5f1c},
{0x38b4,0x38b4,0x5f22},
{0x38b5,0x38b5,0x5f28},
{0x38b6,0x38b6,0x5f36},
{0x38b7,0x38b7,0x5f3b},
{0x38b8,0x38b8,0x5f40},
{0x38b9,0x38b9,0x5f50},
{0x38ba,0x38ba,0x5f58},
{0x38bb,0x38bb,0x5f64},
{0x38bc,0x38bc,0x5f89},
{0x38bd,0x38bd,0x5f9c},
{0x38be,0x38be,0x5fa7},
{0x38bf,0x38bf,0x5fa4},
{0x38c0,0x38c0,0x5faf},
{0x38c1,0x38c1,0x5fb8},
{0x38c2,0x38c2,0x5fc4},
{0x38c3,0x38c3,0x5fc9},
{0x38c4,0x38c4,0x5fe1},
{0x38c5,0x38c5,0x5fe9},
{0x38c6,0x38c6,0x5fed},
{0x38c7,0x38c7,0x5ffc},
{0x38c8,0x38c8,0x6017},
{0x38c9,0x38c9,0x601a},
{0x38ca,0x38ca,0x6033},
{0x38cb,0x38cb,0x6061},
{0x38cc,0x38cc,0x607f},
{0x38cd,0x38cd,0x609e},
{0x38ce,0x38ce,0x60a4},
{0x38cf,0x38cf,0x60b0},
{0x38d0,0x38d0,0x60cb},
{0x38d1,0x38d1,0x60db},
{0x38d2,0x38d2,0x60f8},
{0x38d3,0x38d5,0x6112},
{0x38d6,0x38d6,0x611c},
{0x38d7,0x38d7,0x617c},
{0x38d8,0x38d8,0x618d},
{0x38d9,0x38d9,0x619f},
{0x38da,0x38da,0x61a8},
{0x38db,0x38db,0x61c2},
{0x38dc,0x38dc,0x61df},
{0x38dd,0x38dd,0x6215},
{0x38de,0x38de,0x6229},
{0x38df,0x38df,0x6243},
{0x38e0,0x38e0,0x6246},
{0x38e1,0x38e1,0x624c},
{0x38e2,0x38e2,0x6251},
{0x38e3,0x38e3,0x6256},
{0x38e4,0x38e4,0x62c4},
{0x38e5,0x38e5,0x62fc},
{0x38e6,0x38e6,0x630a},
{0x38e7,0x38e7,0x630d},
{0x38e8,0x38e8,0x6318},
{0x38e9,0x38e9,0x6339},
{0x38ea,0x38eb,0x6342},
{0x38ec,0x38ec,0x6365},
{0x38ed,0x38ed,0x6374},
{0x38ee,0x38ee,0x637d},
{0x38ef,0x38ef,0x6384},
{0x38f0,0x38f0,0x6387},
{0x38f1,0x38f1,0x6390},
{0x38f2,0x38f2,0x639e},
{0x38f3,0x38f3,0x63d1},
{0x38f4,0x38f4,0x63dc},
{0x38f5,0x38f5,0x6409},
{0x38f6,0x38f6,0x6410},
{0x38f7,0x38f7,0x6422},
{0x38f8,0x38f8,0x6454},
{0x38f9,0x38f9,0x645b},
{0x38fa,0x38fa,0x646d},
{0x38fb,0x38fb,0x647b},
{0x38fc,0x38fd,0x64be},
{0x38fe,0x38fe,0x64e5},
{0x38ff,0x38ff,0x64f7},
{0x3900,0x3900,0x64fb},
{0x3901,0x3901,0x6504},
{0x3902,0x3902,0x6516},
{0x3903,0x3903,0x6519},
{0x3904,0x3904,0x6547},
{0x3905,0x3905,0x6567},
{0x3906,0x3906,0x6581},
{0x3907,0x3907,0x6585},
{0x3908,0x3908,0x65c2},
{0x3909,0x3909,0x65f0},
{0x390a,0x390a,0x65f2},
{0x390b,0x390b,0x662c},
{0x390c,0x390c,0x664c},
{0x390d,0x390e,0x665b},
{0x390f,0x390f,0x6661},
{0x3910,0x3910,0x666b},
{0x3911,0x3911,0x6677},
{0x3912,0x3912,0x66a4},
{0x3913,0x3913,0x66c8},
{0x3914,0x3914,0x66ec},
{0x3915,0x3915,0x6705},
{0x3916,0x3916,0x6713},
{0x3917,0x3917,0x6733},
{0x3918,0x3918,0x6748},
{0x3919,0x3919,0x674c},
{0x391a,0x391a,0x6776},
{0x391b,0x391b,0x677b},
{0x391c,0x391c,0x67b0},
{0x391d,0x391d,0x67b2},
{0x391e,0x391e,0x67f9},
{0x391f,0x391f,0x67d7},
{0x3920,0x3920,0x67d9},
{0x3921,0x3921,0x67f0},
{0x3922,0x3922,0x682c},
{0x3923,0x3924,0x6830},
{0x3925,0x3925,0x685b},
{0x3926,0x3926,0x6872},
{0x3927,0x3927,0x6875},
{0x3928,0x3928,0x687a},
{0x3929,0x3929,0x6884},
{0x392a,0x392a,0x68a5},
{0x392b,0x392b,0x68b2},
{0x392c,0x392c,0x68d0},
{0x392d,0x392d,0x68d6},
{0x392e,0x392e,0x68e8},
{0x392f,0x392f,0x68ed},
{0x3930,0x3931,0x68f0},
{0x3932,0x3932,0x68fc},
{0x3933,0x3933,0x6911},
{0x3934,0x3934,0x6913},
{0x3935,0x3935,0x6935},
{0x3936,0x3936,0x693b},
{0x3937,0x3937,0x6957},
{0x3938,0x3938,0x6963},
{0x3939,0x3939,0x6972},
{0x393a,0x393b,0x697f},
{0x393c,0x393c,0x69a6},
{0x393d,0x393d,0x69ad},
{0x393e,0x393e,0x69b7},
{0x393f,0x3940,0x69d6},
{0x3941,0x3941,0x6a01},
{0x3942,0x3942,0x6a0f},
{0x3943,0x3943,0x6a15},
{0x3944,0x3944,0x6a28},
{0x3945,0x3945,0x6a34},
{0x3946,0x3946,0x6a3e},
{0x3947,0x3947,0x6a45},
{0x3948,0x3949,0x6a50},
{0x394a,0x394a,0x6a56},
{0x394b,0x394b,0x6a5b},
{0x394c,0x394c,0x6a83},
{0x394d,0x394d,0x6a89},
{0x394e,0x394e,0x6a91},
{0x394f,0x3951,0x6a9d},
{0x3952,0x3952,0x6adc},
{0x3953,0x3953,0x6ae7},
{0x3954,0x3954,0x6aec},
{0x3955,0x3955,0x6b1e},
{0x3956,0x3956,0x6b24},
{0x3957,0x3957,0x6b35},
{0x3958,0x3958,0x6b46},
{0x3959,0x3959,0x6b56},
{0x395a,0x395a,0x6b60},
{0x395b,0x395b,0x6b82},
{0x395c,0x395c,0x6bbe},
{0x395d,0x395d,0x6be1},
{0x395e,0x395e,0x6bf1},
{0x395f,0x395f,0x6c10},
{0x3960,0x3960,0x6c33},
{0x3961,0x3961,0x6c35},
{0x3962,0x3962,0x6c3a},
{0x3963,0x3963,0x6c59},
{0x3964,0x3964,0x6c76},
{0x3965,0x3965,0x6c7b},
{0x3966,0x3966,0x6c85},
{0x3967,0x3967,0x6c95},
{0x3968,0x3968,0x6c9c},
{0x3969,0x3969,0x6cd0},
{0x396a,0x396a,0x6cd4},
{0x396b,0x396b,0x6cd6},
{0x396c,0x396c,0x6ce0},
{0x396d,0x396e,0x6ceb},
{0x396f,0x396f,0x6cee},
{0x3970,0x3970,0x6d0a},
{0x3971,0x3971,0x6d0e},
{0x3972,0x3972,0x6d11},
{0x3973,0x3973,0x6d2e},
{0x3974,0x3974,0x6d57},
{0x3975,0x3975,0x6d5e},
{0x3976,0x3976,0x6d65},
{0x3977,0x3977,0x6d82},
{0x3978,0x3978,0x6dbf},
{0x3979,0x3979,0x6dc4},
{0x397a,0x397a,0x6dca},
{0x397b,0x397b,0x6dd6},
{0x397c,0x397c,0x6de9},
{0x397d,0x397d,0x6e22},
{0x397e,0x397e,0x6e51},
{0x397f,0x397f,0x6ec7},
{0x3980,0x3980,0x6eca},
{0x3981,0x3981,0x6ece},
{0x3982,0x3982,0x6efd},
{0x3983,0x3983,0x6f1a},
{0x3984,0x3984,0x6f2a},
{0x3985,0x3985,0x6f2f},
{0x3986,0x3986,0x6f33},
{0x3987,0x3987,0x6f5a},
{0x3988,0x3988,0x6f5e},
{0x3989,0x3989,0x6f62},
{0x398a,0x398a,0x6f7d},
{0x398b,0x398b,0x6f8b},
{0x398c,0x398c,0x6f8d},
{0x398d,0x398d,0x6f92},
{0x398e,0x398e,0x6f94},
{0x398f,0x398f,0x6f9a},
{0x3990,0x3991,0x6fa7},
{0x3992,0x3992,0x6fb6},
{0x3993,0x3993,0x6fda},
{0x3994,0x3994,0x6fde},
{0x3995,0x3995,0x6ff9},
{0x3996,0x3996,0x7039},
{0x3997,0x3997,0x703c},
{0x3998,0x3998,0x704a},
{0x3999,0x3999,0x7054},
{0x399a,0x399b,0x705d},
{0x399c,0x399c,0x7064},
{0x399d,0x399d,0x706c},
{0x399e,0x399e,0x707e},
{0x399f,0x399f,0x7081},
{0x39a0,0x39a0,0x7095},
{0x39a1,0x39a1,0x70b7},
{0x39a2,0x39a3,0x70d3},
{0x39a4,0x39a4,0x70d8},
{0x39a5,0x39a5,0x70dc},
{0x39a6,0x39a6,0x7107},
{0x39a7,0x39a7,0x7120},
{0x39a8,0x39a8,0x7131},
{0x39a9,0x39a9,0x714a},
{0x39aa,0x39aa,0x7152},
{0x39ab,0x39ab,0x7160},
{0x39ac,0x39ac,0x7179},
{0x39ad,0x39ad,0x7192},
{0x39ae,0x39ae,0x71b3},
{0x39af,0x39af,0x71cb},
{0x39b0,0x39b0,0x71d3},
{0x39b1,0x39b1,0x71d6},
{0x39b2,0x39b2,0x7200},
{0x39b3,0x39b3,0x721d},
{0x39b4,0x39b4,0x722b},
{0x39b5,0x39b5,0x7238},
{0x39b6,0x39b6,0x7241},
{0x39b7,0x39b7,0x7253},
{0x39b8,0x39b9,0x7255},
{0x39ba,0x39ba,0x725c},
{0x39bb,0x39bb,0x728d},
{0x39bc,0x39bc,0x72ad},
{0x39bd,0x39bd,0x72b4},
{0x39be,0x39be,0x72c7},
{0x39bf,0x39bf,0x72fb},
{0x39c0,0x39c1,0x7304},
{0x39c2,0x39c2,0x7328},
{0x39c3,0x39c3,0x7331},
{0x39c4,0x39c4,0x7343},
{0x39c5,0x39c5,0x736c},
{0x39c6,0x39c6,0x737c},
{0x39c7,0x39c7,0x7383},
{0x39c8,0x39c9,0x7385},
{0x39ca,0x39ca,0x7395},
{0x39cb,0x39cd,0x739e},
{0x39ce,0x39ce,0x73a6},
{0x39cf,0x39cf,0x73ab},
{0x39d0,0x39d0,0x73b5},
{0x39d1,0x39d1,0x73b7},
{0x39d2,0x39d2,0x73bc},
{0x39d3,0x39d3,0x73cf},
{0x39d4,0x39d4,0x73d9},
{0x39d5,0x39d5,0x73e9},
{0x39d6,0x39d6,0x73f4},
{0x39d7,0x39d7,0x73fd},
{0x39d8,0x39d8,0x7404},
{0x39d9,0x39d9,0x740a},
{0x39da,0x39db,0x741a},
{0x39dc,0x39dc,0x7424},
{0x39dd,0x39dd,0x7428},
{0x39de,0x39de,0x742c},
{0x39df,0x39e1,0x742f},
{0x39e2,0x39e2,0x7439},
{0x39e3,0x39e3,0x7444},
{0x39e4,0x39e4,0x7447},
{0x39e5,0x39e5,0x744b},
{0x39e6,0x39e6,0x744d},
{0x39e7,0x39e7,0x7451},
{0x39e8,0x39e8,0x7457},
{0x39e9,0x39e9,0x7466},
{0x39ea,0x39ea,0x746b},
{0x39eb,0x39eb,0x7471},
{0x39ec,0x39ec,0x7480},
{0x39ed,0x39ef,0x7485},
{0x39f0,0x39f0,0x7490},
{0x39f1,0x39f1,0x7498},
{0x39f2,0x39f2,0x749c},
{0x39f3,0x39f3,0x74a0},
{0x39f4,0x39f4,0x74a3},
{0x39f5,0x39f5,0x74a8},
{0x39f6,0x39f6,0x74ab},
{0x39f7,0x39f7,0x74b5},
{0x39f8,0x39f8,0x74bf},
{0x39f9,0x39f9,0x74c8},
{0x39fa,0x39fa,0x74da},
{0x39fb,0x39fb,0x74de},
{0x39fc,0x39fc,0x754e},
{0x39fd,0x39fd,0x7579},
{0x39fe,0x39fe,0x7581},
{0x39ff,0x39ff,0x7590},
{0x3a00,0x3a01,0x7592},
{0x3a02,0x3a02,0x75b4},
{0x3a03,0x3a03,0x75e4},
{0x3a04,0x3a04,0x75f9},
{0x3a05,0x3a05,0x7600},
{0x3a06,0x3a06,0x760a},
{0x3a07,0x3a08,0x7615},
{0x3a09,0x3a09,0x7619},
{0x3a0a,0x3a0a,0x761e},
{0x3a0b,0x3a0b,0x762d},
{0x3a0c,0x3a0c,0x7635},
{0x3a0d,0x3a0d,0x7643},
{0x3a0e,0x3a0e,0x764b},
{0x3a0f,0x3a0f,0x7665},
{0x3a10,0x3a10,0x766d},
{0x3a11,0x3a11,0x766f},
{0x3a12,0x3a12,0x7671},
{0x3a13,0x3a13,0x7674},
{0x3a14,0x3a15,0x76a4},
{0x3a16,0x3a16,0x76c5},
{0x3a17,0x3a17,0x76cc},
{0x3a18,0x3a18,0x76ec},
{0x3a19,0x3a19,0x76fc},
{0x3a1a,0x3a1a,0x7734},
{0x3a1b,0x3a1b,0x7736},
{0x3a1c,0x3a1c,0x775c},
{0x3a1d,0x3a1e,0x775f},
{0x3a1f,0x3a1f,0x7772},
{0x3a20,0x3a20,0x777d},
{0x3a21,0x3a21,0x7795},
{0x3a22,0x3a22,0x77aa},
{0x3a23,0x3a23,0x77e6},
{0x3a24,0x3a24,0x77f0},
{0x3a25,0x3a25,0x77f4},
{0x3a26,0x3a26,0x7806},
{0x3a27,0x3a27,0x7822},
{0x3a28,0x3a29,0x782d},
{0x3a2a,0x3a2a,0x7830},
{0x3a2b,0x3a2b,0x7835},
{0x3a2c,0x3a2c,0x7868},
{0x3a2d,0x3a2d,0x789e},
{0x3a2e,0x3a2e,0x78c8},
{0x3a2f,0x3a2f,0x78cc},
{0x3a30,0x3a30,0x78ce},
{0x3a31,0x3a31,0x78e4},
{0x3a32,0x3a33,0x78e0},
{0x3a34,0x3a34,0x78f2},
{0x3a35,0x3a35,0x78f7},
{0x3a36,0x3a36,0x78fb},
{0x3a37,0x3a37,0x7931},
{0x3a38,0x3a38,0x7934},
{0x3a39,0x3a39,0x793b},
{0x3a3a,0x3a3a,0x793d},
{0x3a3b,0x3a3b,0x7945},
{0x3a3c,0x3a3d,0x795b},
{0x3a3e,0x3a3e,0x798b},
{0x3a3f,0x3a3f,0x7996},
{0x3a40,0x3a40,0x7998},
{0x3a41,0x3a41,0x79b8},
{0x3a42,0x3a42,0x79bb},
{0x3a43,0x3a43,0x79ca},
{0x3a44,0x3a44,0x79da},
{0x3a45,0x3a45,0x7a03},
{0x3a46,0x3a46,0x7a09},
{0x3a47,0x3a47,0x7a11},
{0x3a48,0x3a48,0x7a1e},
{0x3a49,0x3a49,0x7a2d},
{0x3a4a,0x3a4a,0x7a39},
{0x3a4b,0x3a4b,0x7a45},
{0x3a4c,0x3a4c,0x7a4c},
{0x3a4d,0x3a4d,0x7a5d},
{0x3a4e,0x3a4e,0x7a60},
{0x3a4f,0x3a4f,0x7a6d},
{0x3a50,0x3a50,0x7a78},
{0x3a51,0x3a51,0x7aa0},
{0x3a52,0x3a52,0x7aa3},
{0x3a53,0x3a53,0x7ab3},
{0x3a54,0x3a55,0x7abb},
{0x3a56,0x3a56,0x7ac6},
{0x3a57,0x3a57,0x7b07},
{0x3a58,0x3a58,0x7b14},
{0x3a59,0x3a59,0x7b27},
{0x3a5a,0x3a5a,0x7b31},
{0x3a5b,0x3a5b,0x7b47},
{0x3a5c,0x3a5c,0x7b4e},
{0x3a5d,0x3a5d,0x7b60},
{0x3a5e,0x3a5e,0x7b69},
{0x3a5f,0x3a5f,0x7b6d},
{0x3a60,0x3a60,0x7b72},
{0x3a61,0x3a61,0x7b91},
{0x3a62,0x3a62,0x7baf},
{0x3a63,0x3a63,0x7bd7},
{0x3a64,0x3a64,0x7bd9},
{0x3a65,0x3a65,0x7c0b},
{0x3a66,0x3a66,0x7c0f},
{0x3a67,0x3a67,0x7c20},
{0x3a68,0x3a68,0x7c26},
{0x3a69,0x3a69,0x7c31},
{0x3a6a,0x3a6a,0x7c36},
{0x3a6b,0x3a6b,0x7c51},
{0x3a6c,0x3a6c,0x7c59},
{0x3a6d,0x3a6d,0x7c67},
{0x3a6e,0x3a6e,0x7c6e},
{0x3a6f,0x3a6f,0x7c70},
{0x3a70,0x3a70,0x7cbc},
{0x3a71,0x3a71,0x7cbf},
{0x3a72,0x3a73,0x7cc8},
{0x3a74,0x3a74,0x7cd7},
{0x3a75,0x3a75,0x7cd9},
{0x3a76,0x3a76,0x7cdd},
{0x3a77,0x3a77,0x7ceb},
{0x3a78,0x3a7a,0x7d07},
{0x3a7b,0x3a7b,0x7d13},
{0x3a7c,0x3a7c,0x7d1d},
{0x3a7d,0x3a7d,0x7d23},
{0x3a7e,0x3a7e,0x7d41},
{0x3a7f,0x3a7f,0x7d53},
{0x3a80,0x3a80,0x7d59},
{0x3a81,0x3a81,0x7d5d},
{0x3a82,0x3a82,0x7d7a},
{0x3a83,0x3a83,0x7d86},
{0x3a84,0x3a85,0x7d8b},
{0x3a86,0x3a86,0x7dcc},
{0x3a87,0x3a87,0x7deb},
{0x3a88,0x3a88,0x7df1},
{0x3a89,0x3a89,0x7df9},
{0x3a8a,0x3a8a,0x7e08},
{0x3a8b,0x3a8b,0x7e11},
{0x3a8c,0x3a8c,0x7e15},
{0x3a8d,0x3a8d,0x7e20},
{0x3a8e,0x3a8e,0x7e47},
{0x3a8f,0x3a8f,0x7e62},
{0x3a90,0x3a90,0x7e6e},
{0x3a91,0x3a91,0x7e73},
{0x3a92,0x3a92,0x7e8d},
{0x3a93,0x3a93,0x7e91},
{0x3a94,0x3a94,0x7e98},
{0x3a95,0x3a95,0x7f44},
{0x3a96,0x3a96,0x7f4f},
{0x3a97,0x3a98,0x7f52},
{0x3a99,0x3a99,0x7f61},
{0x3a9a,0x3a9a,0x7f91},
{0x3a9b,0x3a9b,0x7fbf},
{0x3a9c,0x3a9c,0x7fce},
{0x3a9d,0x3a9d,0x7fdf},
{0x3a9e,0x3a9e,0x7fe5},
{0x3a9f,0x3a9f,0x7fec},
{0x3aa0,0x3aa1,0x7fee},
{0x3aa2,0x3aa2,0x7ffa},
{0x3aa3,0x3aa3,0x800e},
{0x3aa4,0x3aa4,0x8011},
{0x3aa5,0x3aa5,0x8014},
{0x3aa6,0x3aa6,0x8024},
{0x3aa7,0x3aa7,0x8026},
{0x3aa8,0x3aa8,0x803a},
{0x3aa9,0x3aa9,0x803c},
{0x3aaa,0x3aaa,0x8060},
{0x3aab,0x3aab,0x8071},
{0x3aac,0x3aac,0x8075},
{0x3aad,0x3aad,0x809e},
{0x3aae,0x3aae,0x80a6},
{0x3aaf,0x3aaf,0x80ab},
{0x3ab0,0x3ab1,0x80d7},
{0x3ab2,0x3ab2,0x8116},
{0x3ab3,0x3ab3,0x8118},
{0x3ab4,0x3ab4,0x813a},
{0x3ab5,0x3ab5,0x814a},
{0x3ab6,0x3ab6,0x814c},
{0x3ab7,0x3ab7,0x8181},
{0x3ab8,0x3ab8,0x8184},
{0x3ab9,0x3ab9,0x81b4},
{0x3aba,0x3aba,0x81cf},
{0x3abb,0x3abb,0x81f9},
{0x3abc,0x3abc,0x8203},
{0x3abd,0x3abd,0x8221},
{0x3abe,0x3abe,0x8232},
{0x3abf,0x3abf,0x8234},
{0x3ac0,0x3ac0,0x8246},
{0x3ac1,0x3ac1,0x824b},
{0x3ac2,0x3ac2,0x824f},
{0x3ac3,0x3ac3,0x828e},
{0x3ac4,0x3ac4,0x82ae},
{0x3ac5,0x3ac5,0x82b7},
{0x3ac6,0x3ac6,0x82be},
{0x3ac7,0x3ac7,0x82c6},
{0x3ac8,0x3ac8,0x82fe},
{0x3ac9,0x3ac9,0x8343},
{0x3aca,0x3aca,0x8351},
{0x3acb,0x3acb,0x8355},
{0x3acc,0x3acc,0x8386},
{0x3acd,0x3acd,0x838d},
{0x3ace,0x3ace,0x8392},
{0x3acf,0x3acf,0x8398},
{0x3ad0,0x3ad0,0x83a9},
{0x3ad1,0x3ad2,0x83bf},
{0x3ad3,0x3ad3,0x83ea},
{0x3ad4,0x3ad4,0x840f},
{0x3ad5,0x3ad5,0x8411},
{0x3ad6,0x3ad6,0x844a},
{0x3ad7,0x3ad7,0x8476},
{0x3ad8,0x3ad8,0x84a8},
{0x3ad9,0x3ad9,0x84af},
{0x3ada,0x3ada,0x84c0},
{0x3adb,0x3adb,0x84c2},
{0x3adc,0x3adc,0x84f0},
{0x3add,0x3add,0x84fd},
{0x3ade,0x3ade,0x850c},
{0x3adf,0x3adf,0x8534},
{0x3ae0,0x3ae0,0x855e},
{0x3ae1,0x3ae1,0x858f},
{0x3ae2,0x3ae2,0x85b7},
{0x3ae3,0x3ae3,0x85ce},
{0x3ae4,0x3ae4,0x85ad},
{0x3ae5,0x3ae5,0x8612},
{0x3ae6,0x3ae6,0x8629},
{0x3ae7,0x3ae7,0x8652},
{0x3ae8,0x3ae8,0x8663},
{0x3ae9,0x3ae9,0x866c},
{0x3aea,0x3aea,0x866f},
{0x3aeb,0x3aeb,0x867a},
{0x3aec,0x3aec,0x868d},
{0x3aed,0x3aed,0x8691},
{0x3aee,0x3aee,0x8698},
{0x3aef,0x3af0,0x86a7},
{0x3af1,0x3af1,0x86fa},
{0x3af2,0x3af2,0x86fd},
{0x3af3,0x3af3,0x870b},
{0x3af4,0x3af4,0x8713},
{0x3af5,0x3af5,0x8719},
{0x3af6,0x3af6,0x871e},
{0x3af7,0x3af7,0x8728},
{0x3af8,0x3af8,0x873e},
{0x3af9,0x3af9,0x8771},
{0x3afa,0x3afa,0x8788},
{0x3afb,0x3afb,0x8799},
{0x3afc,0x3afd,0x87ac},
{0x3afe,0x3afe,0x87b5},
{0x3aff,0x3aff,0x87d6},
{0x3b00,0x3b00,0x87eb},
{0x3b01,0x3b01,0x87ed},
{0x3b02,0x3b02,0x8801},
{0x3b03,0x3b03,0x8803},
{0x3b04,0x3b04,0x8806},
{0x3b05,0x3b05,0x880b},
{0x3b06,0x3b06,0x8814},
{0x3b07,0x3b07,0x881c},
{0x3b08,0x3b08,0x8856},
{0x3b09,0x3b09,0x885f},
{0x3b0a,0x3b0a,0x8864},
{0x3b0b,0x3b0b,0x8898},
{0x3b0c,0x3b0c,0x88aa},
{0x3b0d,0x3b0e,0x88bd},
{0x3b0f,0x3b0f,0x88ca},
{0x3b10,0x3b10,0x88d2},
{0x3b11,0x3b11,0x88db},
{0x3b12,0x3b13,0x88f0},
{0x3b14,0x3b14,0x8906},
{0x3b15,0x3b17,0x8918},
{0x3b18,0x3b18,0x8927},
{0x3b19,0x3b19,0x8930},
{0x3b1a,0x3b1a,0x893e},
{0x3b1b,0x3b1b,0x897b},
{0x3b1c,0x3b1c,0x89d4},
{0x3b1d,0x3b1d,0x89d6},
{0x3b1e,0x3b1e,0x89e5},
{0x3b1f,0x3b1f,0x89f1},
{0x3b20,0x3b20,0x8a07},
{0x3b21,0x3b21,0x8a0f},
{0x3b22,0x3b22,0x8a15},
{0x3b23,0x3b23,0x8a22},
{0x3b24,0x3b24,0x8a4e},
{0x3b25,0x3b25,0x8a7f},
{0x3b26,0x3b26,0x8af4},
{0x3b27,0x3b27,0x8b1f},
{0x3b28,0x3b28,0x8b37},
{0x3b29,0x3b2a,0x8b43},
{0x3b2b,0x3b2b,0x8b54},
{0x3b2c,0x3b2c,0x8b9c},
{0x3b2d,0x3b2d,0x8b9e},
{0x3b2e,0x3b2e,0x8c47},
{0x3b2f,0x3b2f,0x8c54},
{0x3b30,0x3b30,0x8c73},
{0x3b31,0x3b31,0x8ca4},
{0x3b32,0x3b32,0x8cd9},
{0x3b33,0x3b33,0x8ce1},
{0x3b34,0x3b34,0x8cf8},
{0x3b35,0x3b35,0x8cfe},
{0x3b36,0x3b36,0x8d1b},
{0x3b37,0x3b37,0x8d69},
{0x3b38,0x3b38,0x8d6c},
{0x3b39,0x3b39,0x8d84},
{0x3b3a,0x3b3a,0x8d8d},
{0x3b3b,0x3b3b,0x8d95},
{0x3b3c,0x3b3c,0x8da6},
{0x3b3d,0x3b3d,0x8dc6},
{0x3b3e,0x3b3e,0x8dce},
{0x3b3f,0x3b3f,0x8de4},
{0x3b40,0x3b40,0x8dec},
{0x3b41,0x3b41,0x8e20},
{0x3b42,0x3b42,0x8e4b},
{0x3b43,0x3b43,0x8e6c},
{0x3b44,0x3b44,0x8e70},
{0x3b45,0x3b45,0x8e7a},
{0x3b46,0x3b46,0x8e92},
{0x3b47,0x3b47,0x8eae},
{0x3b48,0x3b48,0x8eb3},
{0x3b49,0x3b49,0x8ed1},
{0x3b4a,0x3b4a,0x8ed4},
{0x3b4b,0x3b4b,0x8ef9},
{0x3b4c,0x3b4c,0x8f17},
{0x3b4d,0x3b4d,0x8f36},
{0x3b4e,0x3b4e,0x8fa6},
{0x3b4f,0x3b50,0x8fb5},
{0x3b51,0x3b51,0x8fc6},
{0x3b52,0x3b52,0x8fe0},
{0x3b53,0x3b53,0x8fe4},
{0x3b54,0x3b54,0x8ff6},
{0x3b55,0x3b55,0x9002},
{0x3b56,0x3b56,0x902c},
{0x3b57,0x3b57,0x9044},
{0x3b58,0x3b58,0x9088},
{0x3b59,0x3b59,0x9095},
{0x3b5a,0x3b5a,0x9099},
{0x3b5b,0x3b5b,0x909b},
{0x3b5c,0x3b5c,0x90a2},
{0x3b5d,0x3b5d,0x90b4},
{0x3b5e,0x3b5e,0x90d7},
{0x3b5f,0x3b5f,0x90dd},
{0x3b60,0x3b60,0x90f4},
{0x3b61,0x3b61,0x9117},
{0x3b62,0x3b62,0x911c},
{0x3b63,0x3b63,0x9131},
{0x3b64,0x3b64,0x913a},
{0x3b65,0x3b65,0x913d},
{0x3b66,0x3b66,0x9148},
{0x3b67,0x3b67,0x915b},
{0x3b68,0x3b68,0x9161},
{0x3b69,0x3b69,0x9164},
{0x3b6a,0x3b6a,0x918e},
{0x3b6b,0x3b6b,0x919e},
{0x3b6c,0x3b6c,0x91a8},
{0x3b6d,0x3b6e,0x91ad},
{0x3b6f,0x3b6f,0x91b2},
{0x3b70,0x3b70,0x91bc},
{0x3b71,0x3b71,0x91f0},
{0x3b72,0x3b72,0x91f7},
{0x3b73,0x3b73,0x91fb},
{0x3b74,0x3b74,0x9207},
{0x3b75,0x3b75,0x9228},
{0x3b76,0x3b76,0x9233},
{0x3b77,0x3b77,0x9238},
{0x3b78,0x3b78,0x9243},
{0x3b79,0x3b79,0x9247},
{0x3b7a,0x3b7a,0x924f},
{0x3b7b,0x3b7b,0x9260},
{0x3b7c,0x3b7c,0x92c2},
{0x3b7d,0x3b7e,0x92cb},
{0x3b7f,0x3b7f,0x92df},
{0x3b80,0x3b80,0x930d},
{0x3b81,0x3b81,0x9315},
{0x3b82,0x3b82,0x931f},
{0x3b83,0x3b83,0x9327},
{0x3b84,0x3b84,0x9347},
{0x3b85,0x3b85,0x9352},
{0x3b86,0x3b86,0x9365},
{0x3b87,0x3b87,0x936a},
{0x3b88,0x3b88,0x936d},
{0x3b89,0x3b89,0x939b},
{0x3b8a,0x3b8a,0x93ba},
{0x3b8b,0x3b8b,0x93a9},
{0x3b8c,0x3b8c,0x93c1},
{0x3b8d,0x3b8d,0x93ca},
{0x3b8e,0x3b8e,0x93e2},
{0x3b8f,0x3b8f,0x93fa},
{0x3b90,0x3b90,0x93fd},
{0x3b91,0x3b91,0x940f},
{0x3b92,0x3b92,0x9434},
{0x3b93,0x3b93,0x943f},
{0x3b94,0x3b94,0x9455},
{0x3b95,0x3b95,0x946b},
{0x3b96,0x3b96,0x9472},
{0x3b97,0x3b97,0x9578},
{0x3b98,0x3b98,0x95a6},
{0x3b99,0x3b99,0x95a9},
{0x3b9a,0x3b9a,0x95ab},
{0x3b9b,0x3b9b,0x95b4},
{0x3b9c,0x3b9c,0x95bd},
{0x3b9d,0x3b9d,0x95da},
{0x3b9e,0x3b9e,0x961d},
{0x3b9f,0x3b9f,0x9641},
{0x3ba0,0x3ba0,0x9658},
{0x3ba1,0x3ba1,0x9684},
{0x3ba2,0x3ba2,0x96a4},
{0x3ba3,0x3ba3,0x96a9},
{0x3ba4,0x3ba4,0x96d2},
{0x3ba6,0x3ba6,0x96de},
{0x3ba7,0x3ba7,0x96e9},
{0x3ba8,0x3ba8,0x96f1},
{0x3ba9,0x3ba9,0x9702},
{0x3baa,0x3baa,0x9709},
{0x3bab,0x3bab,0x975a},
{0x3bac,0x3bac,0x9763},
{0x3bad,0x3bad,0x976e},
{0x3bae,0x3bae,0x9773},
{0x3baf,0x3baf,0x979a},
{0x3bb0,0x3bb0,0x97a2},
{0x3bb1,0x3bb2,0x97b5},
{0x3bb3,0x3bb3,0x97d9},
{0x3bb4,0x3bb4,0x97de},
{0x3bb5,0x3bb5,0x97f4},
{0x3bb6,0x3bb6,0x980a},
{0x3bb7,0x3bb7,0x980e},
{0x3bb8,0x3bb8,0x981e},
{0x3bb9,0x3bb9,0x9823},
{0x3bba,0x3bba,0x982b},
{0x3bbb,0x3bbb,0x983e},
{0x3bbc,0x3bbd,0x9852},
{0x3bbe,0x3bbe,0x9859},
{0x3bbf,0x3bbf,0x986c},
{0x3bc0,0x3bc0,0x98b8},
{0x3bc1,0x3bc1,0x98ba},
{0x3bc2,0x3bc2,0x98bf},
{0x3bc3,0x3bc3,0x98c8},
{0x3bc4,0x3bc4,0x98e5},
{0x3bc5,0x3bc6,0x9932},
{0x3bc7,0x3bc7,0x9940},
{0x3bc8,0x3bc8,0x994d},
{0x3bc9,0x3bc9,0x995c},
{0x3bca,0x3bca,0x995f},
{0x3bcb,0x3bcb,0x99b1},
{0x3bcc,0x3bcd,0x99b9},
{0x3bce,0x3bce,0x99c9},
{0x3bcf,0x3bcf,0x9a02},
{0x3bd0,0x3bd0,0x9a16},
{0x3bd1,0x3bd1,0x9a24},
{0x3bd2,0x3bd2,0x9a27},
{0x3bd3,0x3bd4,0x9a2d},
{0x3bd5,0x3bd5,0x9a36},
{0x3bd6,0x3bd6,0x9a38},
{0x3bd7,0x3bd7,0x9a4a},
{0x3bd8,0x3bd8,0x9a56},
{0x3bd9,0x3bda,0x9ab5},
{0x3bdb,0x3bdb,0x9af9},
{0x3bdc,0x3bdc,0x9b03},
{0x3bdd,0x3bdd,0x9b20},
{0x3bde,0x3bdf,0x9b33},
{0x3be0,0x3be0,0x9b73},
{0x3be1,0x3be1,0x9b79},
{0x3be2,0x3be2,0x9ba7},
{0x3be3,0x3be3,0x9bc1},
{0x3be4,0x3be4,0x9bc7},
{0x3be5,0x3be5,0x9bd7},
{0x3be6,0x3be6,0x9be7},
{0x3be7,0x3be7,0x9beb},
{0x3be8,0x3be8,0x9bf7},
{0x3be9,0x3be9,0x9bfa},
{0x3bea,0x3bea,0x9bfd},
{0x3beb,0x3beb,0x9c0b},
{0x3bec,0x3bec,0x9c27},
{0x3bed,0x3bed,0x9c2a},
{0x3bee,0x3bee,0x9c36},
{0x3bef,0x3bef,0x9c41},
{0x3bf0,0x3bf0,0x9c53},
{0x3bf1,0x3bf1,0x9c63},
{0x3bf2,0x3bf2,0x9c70},
{0x3bf3,0x3bf3,0x9c77},
{0x3bf4,0x3bf4,0x9d02},
{0x3bf5,0x3bf5,0x9d42},
{0x3bf6,0x3bf6,0x9d47},
{0x3bf7,0x3bf7,0x9d63},
{0x3bf8,0x3bf8,0x9d69},
{0x3bf9,0x3bf9,0x9d7c},
{0x3bfa,0x3bfa,0x9d7e},
{0x3bfb,0x3bfb,0x9d8d},
{0x3bfc,0x3bfc,0x9db1},
{0x3bfd,0x3bfd,0x9dc3},
{0x3bfe,0x3bfe,0x9dc7},
{0x3bff,0x3bff,0x9dd6},
{0x3c00,0x3c00,0x9ddf},
{0x3c01,0x3c01,0x9deb},
{0x3c02,0x3c02,0x9df4},
{0x3c03,0x3c03,0x9e15},
{0x3c04,0x3c04,0x9e1d},
{0x3c05,0x3c05,0x9ea4},
{0x3c06,0x3c06,0x9ea8},
{0x3c07,0x3c07,0x9eac},
{0x3c08,0x3c08,0x9ee7},
{0x3c09,0x3c09,0x9eee},
{0x3c0a,0x3c0a,0x9f10},
{0x3c0b,0x3c0b,0x9f12},
{0x3c0c,0x3c0c,0x9f17},
{0x3c0d,0x3c0d,0x9f19},
{0x3c0e,0x3c0e,0x9f2f},
{0x3c0f,0x3c0f,0x9f37},
{0x3c10,0x3c10,0x9f39},
{0x3c11,0x3c11,0x9f41},
{0x3c12,0x3c12,0x9f45},
{0x3c13,0x3c13,0x9f57},
{0x3c14,0x3c14,0x9f68},
{0x3c15,0x3c15,0x9f71},
{0x3c16,0x3c16,0x9f75},
{0x3c17,0x3c17,0x9f90},
{0x3c18,0x3c18,0x9f94},
{0x3c19,0x3c19,0x9fa2},
{0x3c1a,0x3c1a,0x4e30},
{0x3c1b,0x3c1b,0x3405},
{0x3c1d,0x3c1d,0x5620},
{0x3c1e,0x3c1e,0x5ecb},
{0x3c1f,0x3c1f,0x2e95},
{0x3c20,0x3c20,0x60a4},
{0x3c22,0x3c22,0x6b24},
{0x3c23,0x3c23,0x6ff9},
{0x3c24,0x3c24,0x6ee6},
{0x3c25,0x3c25,0x71b3},
{0x3c26,0x3c26,0x2ea4},
{0x3c27,0x3c27,0x7ac6},
{0x3c28,0x3c28,0x7f61},
{0x3c29,0x3c29,0x8071},
{0x3c2a,0x3c2a,0x809e},
{0x3c2b,0x3c2b,0x2ecc},
{0x3c2c,0x3c2c,0x91fc},
{0x3c2d,0x3c2d,0x5db2},
{0x3c2e,0x3c2e,0x97de},
{0x3c2f,0x3c2f,0x4fd3},
{0x3c30,0x3c30,0x50d9},
{0x3c31,0x3c31,0x50f0},
{0x3c32,0x3c32,0x51c3},
{0x3c33,0x3c33,0x5676},
{0x3c34,0x3c34,0x6a54},
{0x3c35,0x3c35,0x6d01},
{0x3c36,0x3c36,0x6dd0},
{0x3c37,0x3c37,0x6e42},
{0x3c38,0x3c38,0x6ed9},
{0x3c39,0x3c39,0x73e4},
{0x3c3a,0x3c3a,0x7421},
{0x3c3b,0x3c3b,0x756c},
{0x3c3c,0x3c3c,0x7851},
{0x3c3d,0x3c3d,0x87f5},
{0x3c3f,0x3c3f,0x73a8},
{0x3c40,0x3c40,0x3af3},
{0x3c41,0x3c41,0x34db},
{0x3c42,0x3c42,0x440c},
{0x3c43,0x3c43,0x3e8a},
{0x3c45,0x3c45,0xfffd},
{0x3c46,0x3c46,0x4be8},
{0x3c47,0x3c47,0xfffd},
{0x3c48,0x3c48,0x3eda},
{0x3c49,0x3c49,0x3b22},
{0x3c4a,0x3c4a,0xfffd},
{0x3c4b,0x3c4b,0x457a},
{0x3c4c,0x3c4c,0x4093},
{0x3c4e,0x3c4e,0x4665},
{0x3c4f,0x3c4f,0x4103},
{0x3c50,0x3c50,0x4293},
{0x3c51,0x3c51,0x46ae},
{0x3c52,0x3c52,0x3488},
{0x3c54,0x3c56,0xf860},
{0x3c57,0x3c57,0xf87a},
{0x3c58,0x3c58,0xf87f},
{0x3c59,0x3c5a,0x30fd},
{0x3c5b,0x3c5c,0x309d},
{0x3c5d,0x3c5d,0x3003},
{0x3c5e,0x3c5e,0x3006},
{0x3c5f,0x3c5f,0x30fc},
{0x3c60,0x3c60,0x2260},
{0x3c61,0x3c62,0x2266},
{0x3c63,0x3c63,0x221e},
{0x3c64,0x3c64,0x2234},
{0x3c65,0x3c65,0x2103},
{0x3c66,0x3c66,0x30ff},
{0x3c67,0x3c67,0x309f},
{0x3c68,0x3c68,0x2208},
{0x3c69,0x3c69,0x220b},
{0x3c6a,0x3c6b,0x2286},
{0x3c6c,0x3c6d,0x2282},
{0x3c6e,0x3c6e,0x222a},
{0x3c6f,0x3c6f,0x2229},
{0x3c70,0x3c71,0x2284},
{0x3c72,0x3c73,0x228a},
{0x3c74,0x3c74,0x2209},
{0x3c75,0x3c75,0x2205},
{0x3c76,0x3c77,0x2305},
{0x3c78,0x3c79,0x2227},
{0x3c7a,0x3c7a,0x21d2},
{0x3c7b,0x3c7b,0x21d4},
{0x3c7c,0x3c7c,0x2200},
{0x3c7d,0x3c7d,0x2203},
{0x3c7e,0x3c80,0x2295},
{0x3c81,0x3c82,0x2225},
{0x3c83,0x3c83,0x2220},
{0x3c84,0x3c84,0x22a5},
{0x3c85,0x3c85,0x2202},
{0x3c86,0x3c86,0x2207},
{0x3c87,0x3c87,0x2261},
{0x3c88,0x3c88,0x2252},
{0x3c89,0x3c8a,0x226a},
{0x3c8b,0x3c8b,0x221a},
{0x3c8c,0x3c8c,0x223d},
{0x3c8d,0x3c8d,0x221d},
{0x3c8e,0x3c8e,0x2235},
{0x3c8f,0x3c90,0x222b},
{0x3c91,0x3c91,0x2262},
{0x3c92,0x3c92,0x2243},
{0x3c93,0x3c93,0x2245},
{0x3c94,0x3c94,0x2248},
{0x3c95,0x3c96,0x2276},
{0x3c97,0x3c97,0x2194},
{0x3c98,0x3c98,0x2213},
{0x3c99,0x3c99,0x2135},
{0x3c9a,0x3c9a,0x210f},
{0x3c9b,0x3c9b,0x2127},
{0x3c9c,0x3c9c,0x30a0},
{0x3c9d,0x3cf2,0x3041},
{0x3cf8,0x3d4d,0x30a1},
{0x3d56,0x3d5f,0x31f0},
{0x3d61,0x3d66,0x31fa},
{0x3d67,0x3d6a,0x30f7},
{0x3d6b,0x3d6b,0x3053},
{0x3d6c,0x3d6c,0x30b3},
{0x3d6d,0x3d6e,0x22da},
{0x3d6f,0x3d6f,0x2155},
{0x3d70,0x3d70,0x2318},
{0x3d71,0x3d72,0x1e3e},
{0x3d73,0x3d74,0x1f8},
{0x3d75,0x3d75,0x1d6},
{0x3d76,0x3d76,0x1d8},
{0x3d77,0x3d77,0x1da},
{0x3d78,0x3d78,0x1dc},
{0x3d79,0x3d79,0x104},
{0x3d7a,0x3d7a,0x2d8},
{0x3d7b,0x3d7b,0x13d},
{0x3d7c,0x3d7c,0x15a},
{0x3d7d,0x3d7d,0x15e},
{0x3d7e,0x3d7e,0x164},
{0x3d7f,0x3d7f,0x179},
{0x3d80,0x3d80,0x17b},
{0x3d81,0x3d81,0x105},
{0x3d82,0x3d82,0x2db},
{0x3d83,0x3d83,0x13e},
{0x3d84,0x3d84,0x15b},
{0x3d85,0x3d85,0x2c7},
{0x3d86,0x3d86,0x15f},
{0x3d87,0x3d87,0x165},
{0x3d88,0x3d88,0x17a},
{0x3d89,0x3d89,0x2dd},
{0x3d8a,0x3d8a,0x17c},
{0x3d8b,0x3d8b,0x154},
{0x3d8c,0x3d8c,0x102},
{0x3d8d,0x3d8d,0x139},
{0x3d8e,0x3d8e,0x106},
{0x3d8f,0x3d8f,0x10c},
{0x3d90,0x3d90,0x118},
{0x3d91,0x3d91,0x10e},
{0x3d92,0x3d92,0x143},
{0x3d93,0x3d93,0x147},
{0x3d94,0x3d94,0x150},
{0x3d95,0x3d95,0x158},
{0x3d96,0x3d96,0x170},
{0x3d97,0x3d97,0x162},
{0x3d98,0x3d98,0x155},
{0x3d99,0x3d99,0x103},
{0x3d9a,0x3d9a,0x13a},
{0x3d9b,0x3d9b,0x107},
{0x3d9c,0x3d9c,0x10d},
{0x3d9d,0x3d9d,0x119},
{0x3d9e,0x3d9e,0x10f},
{0x3d9f,0x3d9f,0x111},
{0x3da0,0x3da0,0x144},
{0x3da1,0x3da1,0x148},
{0x3da2,0x3da2,0x151},
{0x3da3,0x3da3,0x159},
{0x3da4,0x3da4,0x171},
{0x3da5,0x3da5,0x163},
{0x3da6,0x3da6,0x2d9},
{0x3da7,0x3da7,0x108},
{0x3da8,0x3da8,0x11c},
{0x3da9,0x3da9,0x124},
{0x3daa,0x3daa,0x134},
{0x3dab,0x3dab,0x15c},
{0x3dac,0x3dac,0x16c},
{0x3dad,0x3dad,0x109},
{0x3dae,0x3dae,0x11d},
{0x3daf,0x3daf,0x125},
{0x3db0,0x3db0,0x135},
{0x3db1,0x3db1,0x15d},
{0x3db2,0x3db2,0x16d},
{0x3db3,0x3db3,0x271},
{0x3db4,0x3db4,0x28b},
{0x3db5,0x3db5,0x27e},
{0x3db6,0x3db6,0x26c},
{0x3db7,0x3db7,0x26e},
{0x3db8,0x3db8,0x279},
{0x3db9,0x3db9,0x288},
{0x3dba,0x3dba,0x256},
{0x3dbb,0x3dbb,0x273},
{0x3dbc,0x3dbc,0x27d},
{0x3dbd,0x3dbd,0x282},
{0x3dbe,0x3dbe,0x290},
{0x3dbf,0x3dbf,0x27b},
{0x3dc0,0x3dc0,0x26d},
{0x3dc1,0x3dc1,0x25f},
{0x3dc2,0x3dc2,0x272},
{0x3dc3,0x3dc3,0x29d},
{0x3dc4,0x3dc4,0x28e},
{0x3dc5,0x3dc5,0x261},
{0x3dc6,0x3dc6,0x270},
{0x3dc7,0x3dc7,0x281},
{0x3dc8,0x3dc8,0x127},
{0x3dc9,0x3dc9,0x295},
{0x3dca,0x3dca,0x294},
{0x3dcb,0x3dcb,0x266},
{0x3dcc,0x3dcc,0x298},
{0x3dcd,0x3dcd,0x1c2},
{0x3dce,0x3dce,0x253},
{0x3dcf,0x3dcf,0x257},
{0x3dd0,0x3dd0,0x284},
{0x3dd1,0x3dd1,0x260},
{0x3dd2,0x3dd2,0x193},
{0x3dd3,0x3dd3,0x268},
{0x3dd4,0x3dd4,0x289},
{0x3dd5,0x3dd5,0x258},
{0x3dd6,0x3dd6,0x25c},
{0x3dd7,0x3dd7,0x25e},
{0x3dd8,0x3dd8,0x250},
{0x3dd9,0x3dd9,0x26f},
{0x3dda,0x3dda,0x28a},
{0x3ddb,0x3ddb,0x264},
{0x3ddc,0x3ddc,0x252},
{0x3ddd,0x3ddd,0x28d},
{0x3dde,0x3dde,0x265},
{0x3ddf,0x3ddf,0x2a2},
{0x3de0,0x3de0,0x2a1},
{0x3de1,0x3de1,0x255},
{0x3de2,0x3de2,0x291},
{0x3de3,0x3de3,0x27a},
{0x3de4,0x3de4,0x267},
{0x3de5,0x3de5,0x361},
{0x3de6,0x3de6,0x2c8},
{0x3de7,0x3de7,0x2cc},
{0x3de8,0x3de8,0x2d1},
{0x3de9,0x3de9,0x203f},
{0x3dea,0x3dea,0x30f},
{0x3deb,0x3def,0x2e5},
{0x3df2,0x3df2,0x325},
{0x3df3,0x3df3,0x32c},
{0x3df4,0x3df4,0x339},
{0x3df5,0x3df5,0x31c},
{0x3df6,0x3df7,0x31f},
{0x3df8,0x3df8,0x33d},
{0x3df9,0x3df9,0x329},
{0x3dfa,0x3dfa,0x32f},
{0x3dfb,0x3dfb,0x2de},
{0x3dfc,0x3dfc,0x324},
{0x3dfd,0x3dfd,0x330},
{0x3dfe,0x3dfe,0x33c},
{0x3dff,0x3dff,0x334},
{0x3e00,0x3e01,0x31d},
{0x3e02,0x3e03,0x318},
{0x3e04,0x3e04,0x32a},
{0x3e05,0x3e06,0x33a},
{0x3e07,0x3e07,0x31a},
{0x3e08,0x3e08,0x222e},
{0x3e09,0x3e09,0x221f},
{0x3e0a,0x3e0a,0x22bf},
{0x3e0b,0x3e0c,0x262},
{0x3e0d,0x3e0d,0x26a},
{0x3e0e,0x3e0e,0x274},
{0x3e0f,0x3e0f,0x276},
{0x3e10,0x3e10,0x278},
{0x3e11,0x3e11,0x280},
{0x3e12,0x3e12,0x28f},
{0x3e13,0x3e13,0x299},
{0x3e14,0x3e14,0x29c},
{0x3e15,0x3e15,0x29f},
{0x3e16,0x3e16,0x2b0},
{0x3e17,0x3e17,0x2b2},
{0x3e18,0x3e18,0x2b7},
{0x3e19,0x3e19,0x2c1},
{0x3e1a,0x3e1b,0x2e0},
{0x3e1c,0x3e1c,0x220a},
{0x3e1d,0x3e1d,0x2211},
{0x3e1e,0x3e1e,0x222d},
{0x3e1f,0x3e20,0x2272},
{0x3e21,0x3e21,0x2298},
{0x3e22,0x3e22,0x229e},
{0x3e23,0x3e23,0x22a0},
{0x3e24,0x3e24,0x6e},
{0x3e25,0x3e25,0x3b2},
{0x3e26,0x3e26,0x3b8},
{0x3e27,0x3e27,0x3c7},
{0x3e28,0x3e28,0x2127},
{0x3e29,0x3e29,0x30a0},
{0x3e2a,0x3e2a,0x2155},
{0x3e2b,0x3e2c,0x1e3e},
{0x3e2d,0x3e2e,0x1f8},
{0x3e2f,0x3e2f,0x1d6},
{0x3e30,0x3e30,0x1d8},
{0x3e31,0x3e31,0x1da},
{0x3e32,0x3e32,0x1dc},
{0x3e33,0x3e33,0x104},
{0x3e34,0x3e34,0x13d},
{0x3e35,0x3e35,0x15a},
{0x3e36,0x3e36,0x15e},
{0x3e37,0x3e37,0x164},
{0x3e38,0x3e38,0x179},
{0x3e39,0x3e39,0x17b},
{0x3e3a,0x3e3a,0x105},
{0x3e3b,0x3e3b,0x13e},
{0x3e3c,0x3e3c,0x15b},
{0x3e3d,0x3e3d,0x15f},
{0x3e3e,0x3e3e,0x165},
{0x3e3f,0x3e3f,0x17a},
{0x3e40,0x3e40,0x17c},
{0x3e41,0x3e41,0x154},
{0x3e42,0x3e42,0x102},
{0x3e43,0x3e43,0x139},
{0x3e44,0x3e44,0x106},
{0x3e45,0x3e45,0x10c},
{0x3e46,0x3e46,0x118},
{0x3e47,0x3e47,0x10e},
{0x3e48,0x3e48,0x143},
{0x3e49,0x3e49,0x147},
{0x3e4a,0x3e4a,0x150},
{0x3e4b,0x3e4b,0x158},
{0x3e4c,0x3e4c,0x170},
{0x3e4d,0x3e4d,0x162},
{0x3e4e,0x3e4e,0x155},
{0x3e4f,0x3e4f,0x103},
{0x3e50,0x3e50,0x13a},
{0x3e51,0x3e51,0x107},
{0x3e52,0x3e52,0x10d},
{0x3e53,0x3e53,0x119},
{0x3e54,0x3e54,0x10f},
{0x3e55,0x3e55,0x111},
{0x3e56,0x3e56,0x144},
{0x3e57,0x3e57,0x148},
{0x3e58,0x3e58,0x151},
{0x3e59,0x3e59,0x159},
{0x3e5a,0x3e5a,0x171},
{0x3e5b,0x3e5b,0x163},
{0x3e5c,0x3e5c,0x108},
{0x3e5d,0x3e5d,0x11c},
{0x3e5e,0x3e5e,0x124},
{0x3e5f,0x3e5f,0x134},
{0x3e60,0x3e60,0x15c},
{0x3e61,0x3e61,0x16c},
{0x3e62,0x3e62,0x109},
{0x3e63,0x3e63,0x11d},
{0x3e64,0x3e64,0x125},
{0x3e65,0x3e65,0x135},
{0x3e66,0x3e66,0x15d},
{0x3e67,0x3e67,0x16d},
{0x3e68,0x3e69,0x30fd},
{0x3e6a,0x3e6b,0x309d},
{0x3e6c,0x3e6c,0x3003},
{0x3e6d,0x3e6d,0x3006},
{0x3e6e,0x3e6e,0x30fc},
{0x3e6f,0x3e6f,0x30ff},
{0x3e70,0x3e70,0x309f},
{0x3e71,0x3ec6,0x3041},
{0x3ecc,0x3f21,0x30a1},
{0x3f2a,0x3f33,0x31f0},
{0x3f35,0x3f3a,0x31fa},
{0x3f3b,0x3f3e,0x30f7},
{0x3f3f,0x3f3f,0x3053},
{0x3f40,0x3f40,0x30b3},
{0x3f41,0x3f41,0x30fd},
{0x3f42,0x3f42,0x303c},
{0x3f43,0x3f43,0x30ff},
{0x3f44,0x3f44,0x2225},
{0x3f45,0x3f46,0x3016},
{0x3f47,0x3f47,0x266e},
{0x3f48,0x3f48,0x266b},
{0x3f49,0x3f4a,0x2934},
{0x3f4b,0x3f4b,0x29bf},
{0x3f4c,0x3f4c,0x2127},
{0x3f4d,0x3f4d,0x30a0},
{0x3f4e,0x3f4e,0x2013},
{0x3f4f,0x3f50,0x29fa},
{0x3f5e,0x3f5e,0x3c2},
{0x3f5f,0x3f68,0x24f5},
{0x3f69,0x3f6a,0x2616},
{0x3f6b,0x3f6b,0x25b1},
{0x3f6c,0x3f75,0x31f0},
{0x3f77,0x3f7c,0x31fa},
{0x3f7d,0x3f8b,0x23be},
{0x3f8c,0x3f8c,0x203e},
{0x3f8d,0x3f8d,0xff3f},
{0x3f8e,0x3f8e,0x2713},
{0x3f8f,0x3f8f,0x2318},
{0x3f90,0x3f90,0x2423},
{0x3f91,0x3f91,0x23ce},
{0x3f92,0x3f95,0x25d0},
{0x3f96,0x3f97,0x2047},
{0x3f98,0x3f98,0xa4},
{0x3f99,0x3f99,0x2051},
{0x3f9a,0x3f9a,0x2042},
{0x3f9b,0x3faa,0x3190},
{0x3fab,0x3fab,0x2209},
{0x3fac,0x3fac,0x2226},
{0x3fad,0x3fad,0x2245},
{0x3fae,0x3fae,0x2248},
{0x3faf,0x3faf,0x2262},
{0x3fb0,0x3fb1,0x2276},
{0x3fb2,0x3fb3,0x2284},
{0x3fb4,0x3fb5,0x228a},
{0x3fb6,0x3fb7,0x22da},
{0x3fb8,0x3fb9,0x23b0},
{0x3fba,0x3fc5,0x2672},
{0x3fc6,0x3fc7,0x3099},
{0x3fc8,0x3fc8,0x20dd},
{0x3fc9,0x3fca,0x3016},
{0x3fcb,0x3fcb,0x30a0},
{0x3fcc,0x3fcc,0x2013},
{0x3fcd,0x3fd6,0x31f0},
{0x3fd8,0x3fdd,0x31fa},
{0x3fde,0x3fdf,0x23b0},
{0x3fed,0x3ff6,0x31f0},
{0x3ff8,0x3ffd,0x31fa},
{0x400b,0x4014,0x31f0},
{0x4016,0x401b,0x31fa},
{0x401c,0x401c,0x29bf},
{0x401d,0x401d,0x25e6},
{0x402b,0x4034,0x31f0},
{0x4036,0x403b,0x31fa},
{0x403c,0x403f,0x30f7},
{0x4040,0x4040,0x3053},
{0x4041,0x4041,0x30b3},
{0x4042,0x404b,0x31f0},
{0x404d,0x4052,0x31fa},
{0x4053,0x4053,0x3053},
{0x4054,0x4054,0x30b3},
{0x4055,0x4055,0x2260},
{0x4056,0x4057,0x2266},
{0x4058,0x4058,0x221e},
{0x4059,0x4059,0x2234},
{0x405a,0x405a,0x2103},
{0x405b,0x405b,0x2208},
{0x405c,0x405c,0x220b},
{0x405d,0x405e,0x2286},
{0x405f,0x4060,0x2282},
{0x4061,0x4061,0x222a},
{0x4062,0x4062,0x2229},
{0x4063,0x4064,0x2284},
{0x4065,0x4066,0x228a},
{0x4067,0x4067,0x2209},
{0x4068,0x4068,0x2205},
{0x4069,0x406a,0x2305},
{0x406b,0x406c,0x2227},
{0x406d,0x406d,0x21d2},
{0x406e,0x406e,0x21d4},
{0x406f,0x406f,0x2200},
{0x4070,0x4070,0x2203},
{0x4071,0x4073,0x2295},
{0x4074,0x4075,0x2225},
{0x4076,0x4076,0x2220},
{0x4077,0x4077,0x22a5},
{0x4078,0x4078,0x2202},
{0x4079,0x4079,0x2207},
{0x407a,0x407a,0x2261},
{0x407b,0x407b,0x2252},
{0x407c,0x407d,0x226a},
{0x407e,0x407e,0x221a},
{0x407f,0x407f,0x223d},
{0x4080,0x4080,0x221d},
{0x4081,0x4081,0x2235},
{0x4082,0x4083,0x222b},
{0x4084,0x4084,0x2262},
{0x4085,0x4085,0x2243},
{0x4086,0x4086,0x2245},
{0x4087,0x4087,0x2248},
{0x4088,0x4089,0x2276},
{0x408a,0x408a,0x2194},
{0x408b,0x408b,0x2213},
{0x408c,0x408c,0x2135},
{0x408d,0x408d,0x210f},
{0x408e,0x408e,0x2127},
{0x408f,0x408f,0x30a0},
{0x4090,0x4091,0x22da},
{0x4092,0x4092,0x2155},
{0x4093,0x4093,0x2318},
{0x4094,0x4095,0x1e3e},
{0x4096,0x4097,0x1f8},
{0x4098,0x4098,0x1d6},
{0x4099,0x4099,0x1d8},
{0x409a,0x409a,0x1da},
{0x409b,0x409b,0x1dc},
{0x409c,0x409c,0x104},
{0x409d,0x409d,0x2d8},
{0x409e,0x409e,0x13d},
{0x409f,0x409f,0x15a},
{0x40a0,0x40a0,0x15e},
{0x40a1,0x40a1,0x164},
{0x40a2,0x40a2,0x179},
{0x40a3,0x40a3,0x17b},
{0x40a4,0x40a4,0x105},
{0x40a5,0x40a5,0x2db},
{0x40a6,0x40a6,0x13e},
{0x40a7,0x40a7,0x15b},
{0x40a8,0x40a8,0x2c7},
{0x40a9,0x40a9,0x15f},
{0x40aa,0x40aa,0x165},
{0x40ab,0x40ab,0x17a},
{0x40ac,0x40ac,0x2dd},
{0x40ad,0x40ad,0x17c},
{0x40ae,0x40ae,0x154},
{0x40af,0x40af,0x102},
{0x40b0,0x40b0,0x139},
{0x40b1,0x40b1,0x106},
{0x40b2,0x40b2,0x10c},
{0x40b3,0x40b3,0x118},
{0x40b4,0x40b4,0x10e},
{0x40b5,0x40b5,0x143},
{0x40b6,0x40b6,0x147},
{0x40b7,0x40b7,0x150},
{0x40b8,0x40b8,0x158},
{0x40b9,0x40b9,0x170},
{0x40ba,0x40ba,0x162},
{0x40bb,0x40bb,0x155},
{0x40bc,0x40bc,0x103},
{0x40bd,0x40bd,0x13a},
{0x40be,0x40be,0x107},
{0x40bf,0x40bf,0x10d},
{0x40c0,0x40c0,0x119},
{0x40c1,0x40c1,0x10f},
{0x40c2,0x40c2,0x111},
{0x40c3,0x40c3,0x144},
{0x40c4,0x40c4,0x148},
{0x40c5,0x40c5,0x151},
{0x40c6,0x40c6,0x159},
{0x40c7,0x40c7,0x171},
{0x40c8,0x40c8,0x163},
{0x40c9,0x40c9,0x2d9},
{0x40ca,0x40ca,0x108},
{0x40cb,0x40cb,0x11c},
{0x40cc,0x40cc,0x124},
{0x40cd,0x40cd,0x134},
{0x40ce,0x40ce,0x15c},
{0x40cf,0x40cf,0x16c},
{0x40d0,0x40d0,0x109},
{0x40d1,0x40d1,0x11d},
{0x40d2,0x40d2,0x125},
{0x40d3,0x40d3,0x135},
{0x40d4,0x40d4,0x15d},
{0x40d5,0x40d5,0x16d},
{0x40d6,0x40d6,0x271},
{0x40d7,0x40d7,0x28b},
{0x40d8,0x40d8,0x27e},
{0x40d9,0x40d9,0x26c},
{0x40da,0x40da,0x26e},
{0x40db,0x40db,0x279},
{0x40dc,0x40dc,0x288},
{0x40dd,0x40dd,0x256},
{0x40de,0x40de,0x273},
{0x40df,0x40df,0x27d},
{0x40e0,0x40e0,0x282},
{0x40e1,0x40e1,0x290},
{0x40e2,0x40e2,0x27b},
{0x40e3,0x40e3,0x26d},
{0x40e4,0x40e4,0x25f},
{0x40e5,0x40e5,0x272},
{0x40e6,0x40e6,0x29d},
{0x40e7,0x40e7,0x28e},
{0x40e8,0x40e8,0x261},
{0x40e9,0x40e9,0x270},
{0x40ea,0x40ea,0x281},
{0x40eb,0x40eb,0x127},
{0x40ec,0x40ec,0x295},
{0x40ed,0x40ed,0x294},
{0x40ee,0x40ee,0x266},
{0x40ef,0x40ef,0x298},
{0x40f0,0x40f0,0x1c2},
{0x40f1,0x40f1,0x253},
{0x40f2,0x40f2,0x257},
{0x40f3,0x40f3,0x284},
{0x40f4,0x40f4,0x260},
{0x40f5,0x40f5,0x193},
{0x40f6,0x40f6,0x268},
{0x40f7,0x40f7,0x289},
{0x40f8,0x40f8,0x258},
{0x40f9,0x40f9,0x25c},
{0x40fa,0x40fa,0x25e},
{0x40fb,0x40fb,0x250},
{0x40fc,0x40fc,0x26f},
{0x40fd,0x40fd,0x28a},
{0x40fe,0x40fe,0x264},
{0x40ff,0x40ff,0x252},
{0x4100,0x4100,0x28d},
{0x4101,0x4101,0x265},
{0x4102,0x4102,0x2a2},
{0x4103,0x4103,0x2a1},
{0x4104,0x4104,0x255},
{0x4105,0x4105,0x291},
{0x4106,0x4106,0x27a},
{0x4107,0x4107,0x267},
{0x4108,0x4108,0x361},
{0x4109,0x4109,0x2c8},
{0x410a,0x410a,0x2cc},
{0x410b,0x410b,0x2d1},
{0x410c,0x410c,0x203f},
{0x410d,0x410d,0x30f},
{0x410e,0x4112,0x2e5},
{0x4115,0x4115,0x325},
{0x4116,0x4116,0x32c},
{0x4117,0x4117,0x339},
{0x4118,0x4118,0x31c},
{0x4119,0x411a,0x31f},
{0x411b,0x411b,0x33d},
{0x411c,0x411c,0x329},
{0x411d,0x411d,0x32f},
{0x411e,0x411e,0x2de},
{0x411f,0x411f,0x324},
{0x4120,0x4120,0x330},
{0x4121,0x4121,0x33c},
{0x4122,0x4122,0x334},
{0x4123,0x4124,0x31d},
{0x4125,0x4126,0x318},
{0x4127,0x4127,0x32a},
{0x4128,0x4129,0x33a},
{0x412a,0x412a,0x31a},
{0x412b,0x412b,0x222e},
{0x412c,0x412c,0x221f},
{0x412d,0x412d,0x22bf},
{0x412e,0x412f,0x262},
{0x4130,0x4130,0x26a},
{0x4131,0x4131,0x274},
{0x4132,0x4132,0x276},
{0x4133,0x4133,0x278},
{0x4134,0x4134,0x280},
{0x4135,0x4135,0x28f},
{0x4136,0x4136,0x299},
{0x4137,0x4137,0x29c},
{0x4138,0x4138,0x29f},
{0x4139,0x4139,0x2b0},
{0x413a,0x413a,0x2b2},
{0x413b,0x413b,0x2b7},
{0x413c,0x413c,0x2c0},
{0x413d,0x413e,0x2e0},
{0x413f,0x413f,0x220a},
{0x4140,0x4140,0x2211},
{0x4141,0x4141,0x222d},
{0x4142,0x4143,0x2272},
{0x4144,0x4144,0x2298},
{0x4145,0x4145,0x229e},
{0x4146,0x4146,0x22a0},
{0x4147,0x4147,0x6e},
{0x4148,0x4148,0x3b2},
{0x4149,0x4149,0x3b8},
{0x414a,0x414a,0x3c7},
{0x414b,0x414b,0x2127},
{0x414c,0x414c,0x30a0},
{0x414d,0x414d,0x2155},
{0x414e,0x414f,0x1e3e},
{0x4150,0x4151,0x1f8},
{0x4152,0x4152,0x1d6},
{0x4153,0x4153,0x1d8},
{0x4154,0x4154,0x1da},
{0x4155,0x4155,0x1dc},
{0x4156,0x4156,0x104},
{0x4157,0x4157,0x13d},
{0x4158,0x4158,0x15a},
{0x4159,0x4159,0x15e},
{0x415a,0x415a,0x164},
{0x415b,0x415b,0x179},
{0x415c,0x415c,0x17b},
{0x415d,0x415d,0x105},
{0x415e,0x415e,0x13e},
{0x415f,0x415f,0x15b},
{0x4160,0x4160,0x15f},
{0x4161,0x4161,0x165},
{0x4162,0x4162,0x17a},
{0x4163,0x4163,0x17c},
{0x4164,0x4164,0x154},
{0x4165,0x4165,0x102},
{0x4166,0x4166,0x139},
{0x4167,0x4167,0x106},
{0x4168,0x4168,0x10c},
{0x4169,0x4169,0x118},
{0x416a,0x416a,0x10e},
{0x416b,0x416b,0x143},
{0x416c,0x416c,0x147},
{0x416d,0x416d,0x150},
{0x416e,0x416e,0x158},
{0x416f,0x416f,0x170},
{0x4170,0x4170,0x162},
{0x4171,0x4171,0x155},
{0x4172,0x4172,0x103},
{0x4173,0x4173,0x13a},
{0x4174,0x4174,0x107},
{0x4175,0x4175,0x10d},
{0x4176,0x4176,0x119},
{0x4177,0x4177,0x10f},
{0x4178,0x4178,0x111},
{0x4179,0x4179,0x144},
{0x417a,0x417a,0x148},
{0x417b,0x417b,0x151},
{0x417c,0x417c,0x159},
{0x417d,0x417d,0x171},
{0x417e,0x417e,0x163},
{0x417f,0x417f,0x108},
{0x4180,0x4180,0x11c},
{0x4181,0x4181,0x124},
{0x4182,0x4182,0x134},
{0x4183,0x4183,0x15c},
{0x4184,0x4184,0x16c},
{0x4185,0x4185,0x109},
{0x4186,0x4186,0x11d},
{0x4187,0x4187,0x125},
{0x4188,0x4188,0x135},
{0x4189,0x4189,0x15d},
{0x418a,0x418a,0x16d},
{0x418b,0x418b,0x4f48},
{0x418c,0x418c,0x4f5f},
{0x418d,0x418d,0x4f6a},
{0x418e,0x418e,0x4f6c},
{0x418f,0x418f,0x500e},
{0x4190,0x4190,0x5018},
{0x4191,0x4191,0x5041},
{0x4192,0x4192,0x50f2},
{0x4193,0x4193,0x50d0},
{0x4194,0x4194,0x5106},
{0x4195,0x4195,0x5103},
{0x4196,0x4196,0x5135},
{0x4197,0x4197,0x5155},
{0x4198,0x4198,0x5157},
{0x4199,0x4199,0x34b5},
{0x419a,0x419a,0x51ca},
{0x419b,0x419b,0x51e2},
{0x419c,0x419c,0x5257},
{0x419d,0x419d,0x52cc},
{0x419e,0x419e,0x52d6},
{0x419f,0x419f,0x52fb},
{0x41a0,0x41a0,0x531c},
{0x41a1,0x41a1,0x5363},
{0x41a2,0x41a2,0x539d},
{0x41a3,0x41a3,0x5412},
{0x41a4,0x41a4,0x547f},
{0x41a5,0x41a5,0x5488},
{0x41a6,0x41a6,0x5550},
{0x41a7,0x41a7,0x5581},
{0x41a8,0x41a8,0x55ad},
{0x41a9,0x41a9,0x55ce},
{0x41aa,0x41aa,0x563b},
{0x41ab,0x41ab,0x569e},
{0x41ac,0x41ac,0x56a9},
{0x41ad,0x41ad,0x56b3},
{0x41ae,0x41ae,0x5777},
{0x41af,0x41af,0x577c},
{0x41b1,0x41b1,0x57cf},
{0x41b2,0x41b2,0x57e4},
{0x41b3,0x41b3,0x5864},
{0x41b4,0x41b4,0x5889},
{0x41b6,0x41b6,0x58d2},
{0x41b7,0x41b7,0x58e0},
{0x41b8,0x41b8,0x8641},
{0x41b9,0x41b9,0x598b},
{0x41ba,0x41ba,0x5992},
{0x41bb,0x41bb,0x59c3},
{0x41bc,0x41bc,0x5a13},
{0x41bd,0x41bd,0x5a67},
{0x41be,0x41be,0x5a77},
{0x41bf,0x41bf,0x5a84},
{0x41c0,0x41c0,0x5ac4},
{0x41c2,0x41c2,0x5b7d},
{0x41c3,0x41c3,0x5b93},
{0x41c4,0x41c4,0x5c12},
{0x41c5,0x41c5,0xfa3c},
{0x41c7,0x41c7,0x5c7a},
{0x41c8,0x41c8,0x5c8f},
{0x41c9,0x41c9,0x5c9f},
{0x41ca,0x41ca,0x5ca3},
{0x41cb,0x41cb,0x5caa},
{0x41cc,0x41cc,0x5cd0},
{0x41ce,0x41ce,0x5d0d},
{0x41cf,0x41cf,0x5d47},
{0x41d0,0x41d0,0x5d81},
{0x41d1,0x41d1,0x5da4},
{0x41d2,0x41d2,0x5da7},
{0x41d3,0x41d3,0x5dcb},
{0x41d4,0x41d4,0x5e5e},
{0x41d5,0x41d5,0x5ef9},
{0x41d6,0x41d6,0x5f00},
{0x41d7,0x41d7,0x5f02},
{0x41d8,0x41d8,0x5f23},
{0x41d9,0x41d9,0x5f3d},
{0x41da,0x41da,0x5f54},
{0x41db,0x41db,0x5f7d},
{0x41dc,0x41dc,0x600d},
{0x41dd,0x41dd,0x6014},
{0x41de,0x41de,0x6018},
{0x41df,0x41df,0x6035},
{0x41e0,0x41e0,0x6047},
{0x41e1,0x41e1,0x609d},
{0x41e2,0x41e2,0x60d4},
{0x41e3,0x41e3,0x60dd},
{0x41e4,0x41e4,0x612b},
{0x41e5,0x41e5,0x61bc},
{0x41e6,0x41e6,0x61b9},
{0x41e7,0x41e7,0x6222},
{0x41e8,0x41e8,0x625a},
{0x41e9,0x41e9,0x626f},
{0x41ea,0x41ea,0x62d6},
{0x41eb,0x41eb,0x637c},
{0x41ec,0x41ec,0x63e5},
{0x41ed,0x41ed,0x6479},
{0x41ee,0x41ee,0x64c4},
{0x41ef,0x41ef,0x64d0},
{0x41f0,0x41f0,0x6529},
{0x41f1,0x41f1,0x659d},
{0x41f2,0x41f2,0x663a},
{0x41f3,0x41f3,0x6622},
{0x41f4,0x41f4,0x662b},
{0x41f5,0x41f5,0x6630},
{0x41f6,0x41f6,0x6633},
{0x41f7,0x41f7,0x6648},
{0x41f9,0x41fa,0x6677},
{0x41fb,0x41fb,0x668d},
{0x41fc,0x41fc,0x66bb},
{0x41fd,0x41fd,0x66db},
{0x41fe,0x41fe,0x66e8},
{0x41ff,0x41ff,0x6747},
{0x4200,0x4200,0x6781},
{0x4201,0x4201,0x6793},
{0x4202,0x4202,0x6798},
{0x4203,0x4203,0x679b},
{0x4204,0x4204,0x67fc},
{0x4205,0x4205,0x681d},
{0x4206,0x4206,0x68a3},
{0x4207,0x4207,0x690a},
{0x4208,0x4208,0x6949},
{0x420a,0x420a,0x6942},
{0x420b,0x420b,0x6964},
{0x420c,0x420c,0x69a5},
{0x420d,0x420d,0x69cf},
{0x420e,0x420e,0x3bb6},
{0x420f,0x420f,0x3bc3},
{0x4210,0x4210,0x69e9},
{0x4211,0x4211,0x69f5},
{0x4213,0x4213,0x6a3b},
{0x4215,0x4215,0x6a94},
{0x4216,0x4216,0x6aa5},
{0x4217,0x4217,0x3c0f},
{0x4218,0x4218,0x6b1b},
{0x4219,0x4219,0x6b2c},
{0x421a,0x421a,0x6b67},
{0x421b,0x421b,0x6ba9},
{0x421c,0x421c,0x6bad},
{0x421d,0x421d,0x6bd7},
{0x421e,0x421e,0x6bff},
{0x421f,0x421f,0x6c05},
{0x4220,0x4220,0x6c74},
{0x4221,0x4221,0x6c98},
{0x4222,0x4222,0x6cfb},
{0x4223,0x4223,0x6cc6},
{0x4224,0x4224,0x6d31},
{0x4225,0x4225,0x6d39},
{0x4226,0x4226,0x6d3f},
{0x4227,0x4227,0x6d58},
{0x4228,0x4228,0x6d94},
{0x4229,0x4229,0x6daa},
{0x422a,0x422a,0x6ddb},
{0x422b,0x422b,0x6ddd},
{0x422c,0x422c,0x6e44},
{0x422d,0x422d,0x6e5e},
{0x422e,0x422e,0x6eb1},
{0x422f,0x422f,0x6ec1},
{0x4230,0x4230,0x6f10},
{0x4231,0x4231,0x6f59},
{0x4232,0x4232,0x6f61},
{0x4233,0x4233,0x6f7e},
{0x4234,0x4234,0x6f8c},
{0x4235,0x4235,0x6fa0},
{0x4236,0x4236,0x6fbc},
{0x4237,0x4237,0x6fc7},
{0x4238,0x4238,0x6fca},
{0x4239,0x4239,0x6ff0},
{0x423a,0x423a,0x704e},
{0x423b,0x423b,0x7075},
{0x423c,0x423c,0x70a4},
{0x423d,0x423d,0x70e4},
{0x423e,0x423e,0x712b},
{0x423f,0x423f,0x711e},
{0x4240,0x4240,0x712e},
{0x4241,0x4241,0x7151},
{0x4242,0x4242,0x7168},
{0x4243,0x4243,0x7185},
{0x4244,0x4244,0x7187},
{0x4245,0x4245,0x71ba},
{0x4246,0x4246,0x71c4},
{0x4247,0x4247,0x7215},
{0x4248,0x4248,0x3e3f},
{0x4249,0x4249,0x729b},
{0x424b,0x424b,0x7327},
{0x424c,0x424c,0x7350},
{0x424d,0x424d,0x7366},
{0x424e,0x424e,0x73a2},
{0x424f,0x424f,0x742b},
{0x4250,0x4250,0x7446},
{0x4251,0x4251,0x7462},
{0x4252,0x4252,0x746d},
{0x4253,0x4253,0x74a6},
{0x4254,0x4254,0x74a9},
{0x4255,0x4255,0x74c9},
{0x4256,0x4256,0x74ff},
{0x4257,0x4257,0x7517},
{0x4258,0x4258,0x3f72},
{0x4259,0x4259,0x75ce},
{0x425a,0x425a,0x7602},
{0x425b,0x425b,0x7608},
{0x425c,0x425c,0x7664},
{0x425d,0x425d,0x7681},
{0x425e,0x425e,0x769d},
{0x425f,0x425f,0x76aa},
{0x4260,0x4260,0x76b6},
{0x4261,0x4261,0x76ce},
{0x4262,0x4262,0x76d4},
{0x4263,0x4263,0x76e6},
{0x4264,0x4264,0x76f1},
{0x4265,0x4265,0x770a},
{0x4266,0x4266,0x7719},
{0x4267,0x4268,0x774d},
{0x4269,0x4269,0x777a},
{0x426a,0x426a,0x7780},
{0x426b,0x426b,0x7794},
{0x426c,0x426c,0x77e0},
{0x426e,0x426e,0x7843},
{0x426f,0x426f,0x786e},
{0x4270,0x4270,0x78b0},
{0x4272,0x4272,0x78ad},
{0x4273,0x4273,0x7900},
{0x4274,0x4274,0x791c},
{0x4275,0x4275,0x792e},
{0x4276,0x4276,0x7934},
{0x4277,0x4277,0x7946},
{0x4278,0x4278,0x7979},
{0x4279,0x4279,0x79c8},
{0x427b,0x427b,0x79d4},
{0x427c,0x427c,0x79de},
{0x427d,0x427d,0x79eb},
{0x427e,0x427e,0x79ed},
{0x427f,0x427f,0x7a85},
{0x4281,0x4281,0x7ace},
{0x4282,0x4282,0x7afd},
{0x4283,0x4283,0x7b12},
{0x4284,0x4284,0x7b2d},
{0x4285,0x4285,0x7b3b},
{0x4286,0x4286,0x7b6f},
{0x4287,0x4287,0x7c01},
{0x4288,0x4288,0x7c33},
{0x428a,0x428a,0x7c6d},
{0x428b,0x428b,0x7c79},
{0x428c,0x428c,0x7c8f},
{0x428d,0x428d,0x7c94},
{0x428e,0x428e,0x7ca0},
{0x428f,0x428f,0x7cd5},
{0x4290,0x4290,0x7d31},
{0x4291,0x4291,0x7d5c},
{0x4292,0x4292,0x7d83},
{0x4293,0x4293,0x7da6},
{0x4294,0x4294,0x7dc2},
{0x4295,0x4295,0x7e28},
{0x4296,0x4296,0x7f97},
{0x4297,0x4297,0x7fdb},
{0x4298,0x4298,0x8035},
{0x4299,0x4299,0x8037},
{0x429a,0x429a,0x80ca},
{0x429b,0x429b,0x80e0},
{0x429c,0x429c,0x80f3},
{0x429d,0x429d,0x8160},
{0x429e,0x429f,0x8167},
{0x42a0,0x42a0,0x816d},
{0x42a1,0x42a1,0x81bb},
{0x42a2,0x42a2,0x81ca},
{0x42a3,0x42a3,0x81d7},
{0x42a4,0x42a4,0x445b},
{0x42a5,0x42a5,0x8260},
{0x42a6,0x42a6,0x8274},
{0x42a8,0x42a8,0x82a1},
{0x42a9,0x42aa,0x82a3},
{0x42ab,0x42ab,0x82a9},
{0x42ac,0x42ac,0x82bf},
{0x42ad,0x42ad,0x82d5},
{0x42ae,0x42ae,0x82fd},
{0x42af,0x42af,0x8300},
{0x42b0,0x42b0,0x8322},
{0x42b1,0x42b1,0x832d},
{0x42b2,0x42b2,0x833a},
{0x42b3,0x42b3,0x8347},
{0x42b4,0x42b4,0x837d},
{0x42b5,0x42b5,0x83a7},
{0x42b6,0x42b6,0x83cf},
{0x42b7,0x42b7,0x83d1},
{0x42b8,0x42b8,0x83e1},
{0x42b9,0x42b9,0x8401},
{0x42ba,0x42ba,0x8406},
{0x42bb,0x42bb,0x845f},
{0x42bc,0x42bc,0x8470},
{0x42bd,0x42bd,0x8473},
{0x42be,0x42be,0x8485},
{0x42bf,0x42bf,0x849e},
{0x42c0,0x42c0,0x84ba},
{0x42c2,0x42c2,0x8532},
{0x42c3,0x42c3,0x851e},
{0x42c4,0x42c4,0x852f},
{0x42c5,0x42c5,0x8564},
{0x42c6,0x42c6,0x857a},
{0x42c7,0x42c7,0x858c},
{0x42c8,0x42c8,0x85a2},
{0x42c9,0x42c9,0x85cb},
{0x42ca,0x42ca,0x85ed},
{0x42cb,0x42cb,0x85ff},
{0x42cc,0x42cd,0x8604},
{0x42ce,0x42ce,0x8610},
{0x42d0,0x42d0,0x8618},
{0x42d1,0x42d1,0x8638},
{0x42d2,0x42d2,0x8657},
{0x42d3,0x42d3,0x8662},
{0x42d4,0x42d4,0x459d},
{0x42d5,0x42d5,0x8675},
{0x42d6,0x42d6,0x86b8},
{0x42d7,0x42d7,0x86fc},
{0x42d8,0x42d8,0x8787},
{0x42d9,0x42d9,0x45ea},
{0x42da,0x42da,0x880a},
{0x42db,0x42db,0x8810},
{0x42dc,0x42dc,0x88ce},
{0x42de,0x42de,0x8932},
{0x42df,0x42df,0x8939},
{0x42e0,0x42e0,0x8940},
{0x42e1,0x42e1,0x8994},
{0x42e2,0x42e2,0x89f6},
{0x42e3,0x42e3,0x8a47},
{0x42e4,0x42e4,0x8a5d},
{0x42e5,0x42e5,0x8a61},
{0x42e6,0x42e6,0x8a75},
{0x42e7,0x42e7,0x8ad0},
{0x42e8,0x42e8,0x8b46},
{0x42e9,0x42e9,0x8b59},
{0x42ea,0x42ea,0x8b69},
{0x42eb,0x42eb,0x8b9d},
{0x42ec,0x42ec,0x8c49},
{0x42ed,0x42ed,0x8c68},
{0x42ee,0x42ee,0x8daf},
{0x42ef,0x42ef,0x8dd1},
{0x42f0,0x42f0,0x8dd7},
{0x42f1,0x42f1,0x8e23},
{0x42f2,0x42f2,0x8e3d},
{0x42f3,0x42f3,0x8e7b},
{0x42f5,0x42f5,0x4844},
{0x42f6,0x42f6,0x8efa},
{0x42f7,0x42f7,0x8f1e},
{0x42f8,0x42f8,0x8f2d},
{0x42f9,0x42f9,0x8f54},
{0x42fb,0x42fb,0x8fe8},
{0x42fc,0x42fc,0x8fee},
{0x42fd,0x42fd,0x9008},
{0x42fe,0x42fe,0x902d},
{0x42ff,0x42ff,0x9097},
{0x4300,0x4300,0x90b3},
{0x4301,0x4301,0x90be},
{0x4302,0x4303,0x90c4},
{0x4304,0x4304,0x90c7},
{0x4305,0x4305,0x90ef},
{0x4306,0x4306,0x9114},
{0x4307,0x4307,0x9116},
{0x4308,0x4309,0x9122},
{0x430a,0x430a,0x912f},
{0x430b,0x430b,0x9134},
{0x430c,0x430c,0x9183},
{0x430d,0x430d,0x91f1},
{0x430e,0x430e,0x9349},
{0x430f,0x4310,0x9364},
{0x4311,0x4311,0x93a3},
{0x4312,0x4312,0x93df},
{0x4313,0x4313,0x9404},
{0x4314,0x4314,0x9433},
{0x4315,0x4315,0x944a},
{0x4316,0x4316,0x9463},
{0x4317,0x4317,0x9471},
{0x4318,0x4318,0x958e},
{0x4319,0x4319,0x959f},
{0x431a,0x431a,0x95ac},
{0x431b,0x431b,0x95b6},
{0x431c,0x431c,0x95cb},
{0x431d,0x431d,0x95d0},
{0x431e,0x431e,0x95d3},
{0x431f,0x431f,0x49b0},
{0x4320,0x4320,0x95de},
{0x4321,0x4321,0x96a5},
{0x4322,0x4322,0x96ef},
{0x4323,0x4323,0x974e},
{0x4324,0x4324,0x9795},
{0x4325,0x4325,0x97ae},
{0x4326,0x4326,0x97ba},
{0x4327,0x4327,0x97c1},
{0x4328,0x4328,0x97c9},
{0x4329,0x4329,0x97db},
{0x432a,0x432a,0x9856},
{0x432b,0x432b,0x98e7},
{0x432c,0x432c,0x9958},
{0x432d,0x432d,0x9a03},
{0x432e,0x432e,0x9ac1},
{0x432f,0x432f,0x9ac3},
{0x4330,0x4330,0x9ace},
{0x4331,0x4331,0x9ad6},
{0x4332,0x4332,0x9b02},
{0x4333,0x4333,0x9b08},
{0x4334,0x4334,0x4c17},
{0x4335,0x4335,0x9b2d},
{0x4336,0x4336,0x9b5e},
{0x4337,0x4337,0x9b66},
{0x4338,0x4338,0x9b84},
{0x4339,0x4339,0x9b8a},
{0x433a,0x433a,0x9b9e},
{0x433b,0x433b,0x9bce},
{0x433c,0x433c,0x9be5},
{0x433d,0x433d,0x9bf8},
{0x433e,0x433e,0x9c23},
{0x433f,0x4340,0x9c4f},
{0x4341,0x4341,0x9c65},
{0x4342,0x4343,0x9d1d},
{0x4344,0x4344,0x9d43},
{0x4345,0x4345,0x9d52},
{0x4346,0x4346,0x9d8a},
{0x4347,0x4347,0x9d96},
{0x4348,0x4348,0x9dc0},
{0x4349,0x4349,0x9dac},
{0x434a,0x434a,0x9dbc},
{0x434c,0x434c,0x9de7},
{0x434d,0x434d,0x9e07},
{0x434e,0x434e,0x9e9e},
{0x434f,0x434f,0x9eaf},
{0x4350,0x4350,0x9f97},
{0x4352,0x4352,0x4e02},
{0x4353,0x4353,0x4e0f},
{0x4354,0x4354,0x4e12},
{0x4355,0x4355,0x4e29},
{0x4356,0x4356,0x4e2e},
{0x4357,0x4357,0x4e47},
{0x4359,0x4359,0x4e51},
{0x435a,0x435a,0x3406},
{0x435c,0x435c,0x4e69},
{0x435d,0x435d,0x4e9d},
{0x435e,0x435e,0x342c},
{0x435f,0x435f,0x4ebc},
{0x4360,0x4360,0x4ec3},
{0x4361,0x4361,0x4ec8},
{0x4362,0x4362,0x4eeb},
{0x4363,0x4363,0x4eda},
{0x4364,0x4364,0x4ef1},
{0x4365,0x4365,0x4ef5},
{0x4366,0x4366,0x4f16},
{0x4367,0x4367,0x4f64},
{0x4368,0x4368,0x4f37},
{0x4369,0x4369,0x4f3e},
{0x436a,0x436a,0x4f58},
{0x436c,0x436d,0x4f77},
{0x436e,0x436e,0x4f82},
{0x436f,0x436f,0x4f85},
{0x4370,0x4370,0x4fe6},
{0x4371,0x4371,0x4fb2},
{0x4372,0x4372,0x4fc5},
{0x4373,0x4373,0x4fcb},
{0x4374,0x4374,0x4fd2},
{0x4375,0x4375,0x346a},
{0x4376,0x4376,0x4ff2},
{0x4377,0x4377,0x5013},
{0x4378,0x4378,0x501c},
{0x4379,0x4379,0x504e},
{0x437a,0x437a,0x5053},
{0x437b,0x437b,0x5063},
{0x437c,0x437c,0x50a3},
{0x437d,0x437d,0x5088},
{0x437e,0x437f,0x5092},
{0x4380,0x4380,0x5095},
{0x4381,0x4381,0x50aa},
{0x4383,0x4383,0x50b1},
{0x4384,0x4385,0x50ba},
{0x4386,0x4386,0x50c4},
{0x4387,0x4387,0x50c7},
{0x4388,0x4388,0x50f3},
{0x438a,0x438a,0x50ce},
{0x438c,0x438c,0x50d4},
{0x438d,0x438d,0x50e1},
{0x438e,0x438e,0x3492},
{0x4390,0x4390,0x5117},
{0x4392,0x4392,0x5160},
{0x4394,0x4394,0x5173},
{0x4395,0x4395,0x5183},
{0x4396,0x4396,0x518b},
{0x4397,0x4397,0x34bc},
{0x4398,0x4398,0x5198},
{0x4399,0x4399,0x51a3},
{0x439a,0x439a,0x51ad},
{0x439b,0x439b,0x34c7},
{0x439d,0x439d,0x51f3},
{0x439e,0x439e,0x5212},
{0x439f,0x439f,0x5216},
{0x43a1,0x43a1,0x5255},
{0x43a2,0x43a2,0x525c},
{0x43a3,0x43a3,0x526c},
{0x43a4,0x43a4,0x5277},
{0x43a5,0x43a5,0x5284},
{0x43a6,0x43a6,0x5282},
{0x43a8,0x43a8,0x5298},
{0x43aa,0x43aa,0x52a4},
{0x43ab,0x43ac,0x52ba},
{0x43ad,0x43ad,0x52ca},
{0x43ae,0x43ae,0x52d1},
{0x43b0,0x43b0,0x52f7},
{0x43b1,0x43b1,0x5335},
{0x43b2,0x43b2,0x5342},
{0x43b5,0x43b5,0x5367},
{0x43b6,0x43b6,0x537a},
{0x43b7,0x43b7,0x53a4},
{0x43b8,0x43b8,0x53b4},
{0x43ba,0x43ba,0x53b7},
{0x43bb,0x43bb,0x53c0},
{0x43bd,0x43be,0x355d},
{0x43bf,0x43bf,0x53d5},
{0x43c0,0x43c0,0x3563},
{0x43c1,0x43c1,0x53f4},
{0x43c2,0x43c2,0x5424},
{0x43c3,0x43c3,0x5428},
{0x43c4,0x43c4,0x356e},
{0x43c5,0x43c5,0x5443},
{0x43c6,0x43c6,0x5462},
{0x43c7,0x43c7,0x546c},
{0x43c8,0x43c8,0x5495},
{0x43c9,0x43c9,0x54a0},
{0x43ca,0x43ca,0x54a6},
{0x43cb,0x43cb,0x54ae},
{0x43cc,0x43cc,0x54b7},
{0x43cd,0x43cd,0x54ba},
{0x43ce,0x43ce,0x54c3},
{0x43d0,0x43d0,0x54ec},
{0x43d1,0x43d1,0x54ef},
{0x43d2,0x43d2,0x54f1},
{0x43d3,0x43d3,0x54f3},
{0x43d4,0x43d5,0x5500},
{0x43d6,0x43d6,0x5509},
{0x43d7,0x43d7,0x553c},
{0x43d8,0x43d8,0x5541},
{0x43d9,0x43d9,0x35a6},
{0x43da,0x43da,0x5547},
{0x43db,0x43db,0x35a8},
{0x43dc,0x43dc,0x5564},
{0x43de,0x43de,0x557d},
{0x43df,0x43df,0x5582},
{0x43e0,0x43e0,0x5591},
{0x43e1,0x43e1,0x35c5},
{0x43e2,0x43e2,0x55d2},
{0x43e5,0x43e5,0x55bf},
{0x43e6,0x43e6,0x55c9},
{0x43e7,0x43e7,0x55cc},
{0x43e8,0x43e8,0x55d1},
{0x43e9,0x43e9,0x55dd},
{0x43ea,0x43ea,0x35da},
{0x43eb,0x43eb,0x55e2},
{0x43ed,0x43ed,0x55e9},
{0x43ee,0x43ee,0x5628},
{0x43f0,0x43f0,0x5607},
{0x43f1,0x43f1,0x5610},
{0x43f2,0x43f2,0x5630},
{0x43f3,0x43f3,0x35f4},
{0x43f4,0x43f4,0x563d},
{0x43f5,0x43f5,0x5640},
{0x43f6,0x43f6,0x5647},
{0x43f7,0x43f7,0x565e},
{0x43f8,0x43f8,0x5660},
{0x43f9,0x43f9,0x566d},
{0x43fa,0x43fa,0x3605},
{0x43fb,0x43fb,0x5688},
{0x43fc,0x43fc,0x568c},
{0x43fd,0x43fd,0x569d},
{0x43fe,0x43fe,0x56a8},
{0x43ff,0x43ff,0x56b2},
{0x4400,0x4400,0x56c5},
{0x4401,0x4401,0x56cd},
{0x4402,0x4402,0x56df},
{0x4403,0x4403,0x56e8},
{0x4404,0x4405,0x56f6},
{0x4408,0x4408,0x5729},
{0x440a,0x440b,0x5745},
{0x440c,0x440c,0x574d},
{0x440e,0x440e,0x5768},
{0x440f,0x440f,0x576f},
{0x4410,0x4412,0x5773},
{0x4413,0x4413,0x577b},
{0x4416,0x4416,0x579a},
{0x4417,0x4418,0x579d},
{0x4419,0x4419,0x57a8},
{0x441a,0x441a,0x57d7},
{0x441c,0x441c,0x57cc},
{0x441f,0x441f,0x57de},
{0x4420,0x4420,0x57f0},
{0x4421,0x4421,0x364a},
{0x4422,0x4422,0x57f8},
{0x4423,0x4423,0x57fb},
{0x4424,0x4424,0x57fd},
{0x4425,0x4425,0x5804},
{0x4426,0x4426,0x581e},
{0x4427,0x4427,0x5827},
{0x4428,0x4428,0x5839},
{0x442a,0x442a,0x5849},
{0x442b,0x442b,0x584c},
{0x442c,0x442c,0x5867},
{0x442d,0x442e,0x588a},
{0x442f,0x442f,0x588d},
{0x4430,0x4431,0x588f},
{0x4432,0x4432,0x5894},
{0x4433,0x4433,0x589d},
{0x4434,0x4434,0x58aa},
{0x4435,0x4435,0x58b1},
{0x4437,0x4437,0x58c3},
{0x4438,0x4438,0x58cd},
{0x4439,0x4439,0x58e2},
{0x443a,0x443b,0x58f3},
{0x443c,0x443d,0x5905},
{0x443e,0x443e,0x590d},
{0x443f,0x443f,0x5914},
{0x4441,0x4441,0x3691},
{0x4442,0x4442,0x593d},
{0x4443,0x4443,0x3699},
{0x4444,0x4444,0x5946},
{0x4445,0x4445,0x3696},
{0x4447,0x4447,0x595f},
{0x4449,0x444a,0x5975},
{0x444b,0x444b,0x597c},
{0x444c,0x444c,0x599f},
{0x444d,0x444d,0x59ae},
{0x444e,0x444e,0x59bc},
{0x444f,0x444f,0x59c8},
{0x4450,0x4450,0x59cd},
{0x4451,0x4451,0x59de},
{0x4452,0x4452,0x59e7},
{0x4453,0x4453,0x59ee},
{0x4456,0x4456,0x36cf},
{0x4457,0x4457,0x5a0d},
{0x4458,0x4458,0x5a17},
{0x4459,0x4459,0x5a27},
{0x445a,0x445a,0x5a2d},
{0x445b,0x445b,0x5a65},
{0x445c,0x445c,0x5a7a},
{0x445d,0x445d,0x5a8b},
{0x445e,0x445e,0x5a9c},
{0x445f,0x4460,0x5a9f},
{0x4461,0x4461,0x5aa2},
{0x4462,0x4462,0x5ab1},
{0x4463,0x4463,0x5ab5},
{0x4464,0x4464,0x5aba},
{0x4465,0x4465,0x5abf},
{0x4466,0x4466,0x5ada},
{0x4467,0x4467,0x5adc},
{0x4468,0x4468,0x5ae5},
{0x4469,0x4469,0x5af0},
{0x446a,0x446a,0x5aee},
{0x446b,0x446b,0x5af5},
{0x446c,0x446c,0x5b08},
{0x446d,0x446d,0x5b17},
{0x446e,0x446e,0x5b34},
{0x446f,0x446f,0x5b4c},
{0x4470,0x4470,0x5b52},
{0x4471,0x4471,0x5b68},
{0x4472,0x4472,0x5b6f},
{0x4473,0x4473,0x5b81},
{0x4474,0x4474,0x5b84},
{0x4476,0x4476,0x5b96},
{0x4477,0x4477,0x5bac},
{0x4478,0x4479,0x3761},
{0x447a,0x447a,0x5bce},
{0x447b,0x447b,0x5bd6},
{0x447c,0x447c,0x376c},
{0x447d,0x447d,0x376b},
{0x447e,0x447e,0x5bf1},
{0x447f,0x447f,0x5bfd},
{0x4480,0x4480,0x3775},
{0x4481,0x4481,0x5c03},
{0x4482,0x4482,0x5c29},
{0x4484,0x4484,0x5c5f},
{0x4485,0x4486,0x5c67},
{0x4487,0x4487,0x5c70},
{0x448c,0x448c,0x5c88},
{0x448d,0x448d,0x5c8a},
{0x448e,0x448e,0x37c1},
{0x4491,0x4491,0x5ca0},
{0x4492,0x4492,0x5ca2},
{0x4493,0x4493,0x5ca7},
{0x4495,0x4495,0x5cad},
{0x4496,0x4496,0x5cb5},
{0x4498,0x4498,0x5cc9},
{0x449b,0x449b,0x5d06},
{0x449c,0x449c,0x5d10},
{0x449d,0x449d,0x5d2b},
{0x449e,0x449e,0x5d1d},
{0x449f,0x449f,0x5d20},
{0x44a0,0x44a0,0x5d31},
{0x44a1,0x44a1,0x5d39},
{0x44a2,0x44a2,0x37e8},
{0x44a3,0x44a3,0x5d61},
{0x44a4,0x44a4,0x5d6a},
{0x44a5,0x44a5,0x37f4},
{0x44a6,0x44a6,0x5d70},
{0x44a8,0x44a8,0x37fd},
{0x44a9,0x44a9,0x5d88},
{0x44aa,0x44aa,0x3800},
{0x44ab,0x44ab,0x5d97},
{0x44ac,0x44ac,0x5db0},
{0x44ad,0x44ad,0x5db4},
{0x44af,0x44af,0x5dd1},
{0x44b0,0x44b0,0x5dd7},
{0x44b2,0x44b2,0x5de4},
{0x44b3,0x44b3,0x5de9},
{0x44b4,0x44b4,0x382f},
{0x44b5,0x44b5,0x3836},
{0x44b6,0x44b6,0x3840},
{0x44b7,0x44b7,0x5e1f},
{0x44b8,0x44b8,0x5e3e},
{0x44b9,0x44b9,0x5e49},
{0x44ba,0x44ba,0x385c},
{0x44bb,0x44bb,0x5e56},
{0x44bc,0x44bc,0x3861},
{0x44bd,0x44be,0x5e6d},
{0x44c0,0x44c0,0x5ea5},
{0x44c1,0x44c1,0x5eac},
{0x44c2,0x44c2,0x5eb9},
{0x44c3,0x44c3,0x5ec6},
{0x44c4,0x44c4,0x5ed9},
{0x44c6,0x44c6,0x5efd},
{0x44c7,0x44c7,0x5f08},
{0x44c9,0x44c9,0x5f1e},
{0x44ca,0x44ca,0x5f47},
{0x44cb,0x44cb,0x5f63},
{0x44cc,0x44cc,0x5f72},
{0x44cd,0x44cd,0x5f7e},
{0x44ce,0x44ce,0x5f8f},
{0x44cf,0x44cf,0x5fa2},
{0x44d0,0x44d0,0x5fc7},
{0x44d1,0x44d1,0x5fcb},
{0x44d2,0x44d4,0x5fd2},
{0x44d5,0x44d5,0x5fe2},
{0x44d6,0x44d7,0x5fee},
{0x44d8,0x44d8,0x5ff3},
{0x44d9,0x44d9,0x3917},
{0x44da,0x44da,0x6022},
{0x44db,0x44db,0x6024},
{0x44dc,0x44dc,0x391a},
{0x44dd,0x44dd,0x604c},
{0x44de,0x44de,0x6095},
{0x44df,0x44df,0x60a8},
{0x44e1,0x44e1,0x60b1},
{0x44e2,0x44e2,0x60be},
{0x44e3,0x44e3,0x60c8},
{0x44e4,0x44e4,0x60d9},
{0x44e5,0x44e5,0x60ee},
{0x44e6,0x44e6,0x60f5},
{0x44e7,0x44e7,0x6110},
{0x44e8,0x44e8,0x6119},
{0x44e9,0x44e9,0x611e},
{0x44ea,0x44ea,0x613a},
{0x44eb,0x44eb,0x396f},
{0x44ec,0x44ec,0x6141},
{0x44ed,0x44ed,0x6146},
{0x44ee,0x44ee,0x6160},
{0x44f0,0x44f1,0x6192},
{0x44f2,0x44f2,0x6197},
{0x44f3,0x44f3,0x61a5},
{0x44f4,0x44f4,0x61ad},
{0x44f6,0x44f6,0x61d5},
{0x44f7,0x44f7,0x61dd},
{0x44f8,0x44f8,0x61f5},
{0x44fa,0x44fa,0x6223},
{0x44fb,0x44fb,0x6252},
{0x44fc,0x44fc,0x6261},
{0x44fd,0x44fd,0x6264},
{0x44fe,0x44fe,0x627b},
{0x44ff,0x44ff,0x626d},
{0x4500,0x4500,0x6273},
{0x4501,0x4501,0x6299},
{0x4502,0x4502,0x62d5},
{0x4504,0x4504,0x62fd},
{0x4505,0x4505,0x6303},
{0x4506,0x4506,0x6310},
{0x4509,0x4509,0x6332},
{0x450a,0x450a,0x6335},
{0x450b,0x450c,0x633b},
{0x450d,0x450d,0x6341},
{0x450e,0x450e,0x6344},
{0x450f,0x450f,0x634e},
{0x4511,0x4511,0x6359},
{0x4514,0x4514,0x636c},
{0x4515,0x4515,0x6399},
{0x4517,0x4517,0x6394},
{0x4518,0x4518,0x63bd},
{0x4519,0x451a,0x63d4},
{0x451b,0x451b,0x63e0},
{0x451c,0x451d,0x63eb},
{0x451e,0x451e,0x63f2},
{0x451f,0x451f,0x641e},
{0x4520,0x4520,0x6425},
{0x4521,0x4521,0x6429},
{0x4522,0x4522,0x642f},
{0x4523,0x4523,0x645a},
{0x4524,0x4524,0x645d},
{0x4525,0x4525,0x6473},
{0x4526,0x4526,0x647d},
{0x4527,0x4527,0x6487},
{0x4528,0x4528,0x6491},
{0x4529,0x4529,0x649f},
{0x452a,0x452b,0x64cb},
{0x452c,0x452c,0x64d5},
{0x452d,0x452d,0x64d7},
{0x452f,0x452f,0x64e4},
{0x4530,0x4530,0x64ff},
{0x4531,0x4531,0x3a6e},
{0x4532,0x4532,0x650f},
{0x4533,0x4533,0x6514},
{0x4534,0x4534,0x3a73},
{0x4535,0x4535,0x651e},
{0x4536,0x4536,0x6532},
{0x4537,0x4537,0x6544},
{0x4538,0x4538,0x6554},
{0x4539,0x4539,0x656b},
{0x453a,0x453a,0x657a},
{0x453b,0x453b,0x6584},
{0x453c,0x453c,0x658a},
{0x453d,0x453d,0x65b2},
{0x453e,0x453e,0x65b5},
{0x453f,0x453f,0x65b8},
{0x4540,0x4540,0x65bf},
{0x4541,0x4541,0x65c9},
{0x4542,0x4542,0x65d4},
{0x4543,0x4543,0x3ad6},
{0x4544,0x4544,0x65f9},
{0x4545,0x4545,0x65fc},
{0x4546,0x4546,0x6604},
{0x4547,0x4547,0x6608},
{0x4548,0x4548,0x6621},
{0x4549,0x4549,0x662a},
{0x454a,0x454a,0x6645},
{0x454b,0x454b,0x6651},
{0x454c,0x454c,0x664e},
{0x454d,0x454d,0x3aea},
{0x4551,0x4551,0x666a},
{0x4552,0x4553,0x666c},
{0x4554,0x4554,0x667b},
{0x4555,0x4555,0x6680},
{0x4556,0x4556,0x6690},
{0x4557,0x4557,0x6692},
{0x4558,0x4558,0x3b0e},
{0x4559,0x4559,0x66ad},
{0x455a,0x455a,0x66b1},
{0x455b,0x455b,0x66b5},
{0x455c,0x455c,0x3b1a},
{0x455d,0x455d,0x3b1c},
{0x455e,0x455e,0x3ad7},
{0x455f,0x455f,0x6701},
{0x4560,0x4560,0x6712},
{0x4562,0x4562,0x6719},
{0x4565,0x4565,0x674d},
{0x4566,0x4566,0x6754},
{0x4567,0x4567,0x675d},
{0x456b,0x456b,0x6774},
{0x456d,0x456d,0x6792},
{0x456f,0x456f,0x8363},
{0x4570,0x4570,0x6810},
{0x4571,0x4571,0x67c3},
{0x4572,0x4572,0x67c8},
{0x4573,0x4573,0x67d2},
{0x4574,0x4574,0x67db},
{0x4575,0x4575,0x67f7},
{0x4579,0x4579,0x6818},
{0x457a,0x457a,0x681f},
{0x457b,0x457b,0x682d},
{0x457d,0x457d,0x6833},
{0x457e,0x457e,0x683b},
{0x457f,0x457f,0x683e},
{0x4580,0x4580,0x6845},
{0x4581,0x4581,0x6849},
{0x4582,0x4582,0x684c},
{0x4583,0x4583,0x6855},
{0x4584,0x4584,0x6857},
{0x4585,0x4585,0x3b77},
{0x4586,0x4586,0x686b},
{0x4587,0x4587,0x686e},
{0x4588,0x4588,0x687c},
{0x4589,0x4589,0x6882},
{0x458a,0x458a,0x6890},
{0x458b,0x458b,0x6896},
{0x458c,0x458c,0x3b6d},
{0x458d,0x458f,0x6898},
{0x4590,0x4590,0x689c},
{0x4591,0x4592,0x68aa},
{0x4593,0x4593,0x68b4},
{0x4594,0x4594,0x68bb},
{0x4595,0x4595,0x68fb},
{0x4598,0x4598,0x68c3},
{0x4599,0x4599,0x68c5},
{0x459a,0x459a,0x68cc},
{0x459b,0x459b,0x68d9},
{0x459c,0x459d,0x68e4},
{0x459e,0x459e,0x68ec},
{0x459f,0x459f,0x68f7},
{0x45a0,0x45a0,0x6903},
{0x45a1,0x45a1,0x6907},
{0x45a2,0x45a2,0x3b87},
{0x45a4,0x45a4,0x3b8d},
{0x45a5,0x45a5,0x6946},
{0x45a6,0x45a6,0x6969},
{0x45a7,0x45a7,0x696c},
{0x45a8,0x45a8,0x697a},
{0x45a9,0x45a9,0x6992},
{0x45aa,0x45aa,0x3ba4},
{0x45ab,0x45ab,0x6996},
{0x45ac,0x45ac,0x69b0},
{0x45ad,0x45ad,0x69ba},
{0x45ae,0x45ae,0x69bc},
{0x45af,0x45af,0x69c0},
{0x45b0,0x45b0,0x69d1},
{0x45b4,0x45b4,0x69e3},
{0x45b5,0x45b6,0x69ee},
{0x45b7,0x45b7,0x69f3},
{0x45b8,0x45b8,0x3bcd},
{0x45b9,0x45b9,0x69f4},
{0x45ba,0x45ba,0x69fe},
{0x45bb,0x45bb,0x6a11},
{0x45bc,0x45bc,0x6a1a},
{0x45bd,0x45bd,0x6a1d},
{0x45bf,0x45c0,0x6a32},
{0x45c1,0x45c1,0x6a3f},
{0x45c2,0x45c2,0x6a49},
{0x45c3,0x45c3,0x6a7a},
{0x45c4,0x45c4,0x6a4e},
{0x45c5,0x45c5,0x6a52},
{0x45c6,0x45c6,0x6a64},
{0x45c8,0x45c8,0x6a8b},
{0x45c9,0x45c9,0x3bf0},
{0x45ca,0x45ca,0x6aa1},
{0x45cc,0x45cc,0x6aab},
{0x45cd,0x45cd,0x6abd},
{0x45ce,0x45ce,0x6ac6},
{0x45cf,0x45cf,0x6ad4},
{0x45d0,0x45d0,0x6ad0},
{0x45d1,0x45d1,0x6add},
{0x45d4,0x45d6,0x6af1},
{0x45d7,0x45d7,0x6afd},
{0x45d9,0x45d9,0x6b0b},
{0x45da,0x45dc,0x6b0f},
{0x45de,0x45de,0x6b17},
{0x45df,0x45df,0x3c26},
{0x45e0,0x45e0,0x6b2f},
{0x45e1,0x45e1,0x6b4a},
{0x45e2,0x45e2,0x6b58},
{0x45e3,0x45e3,0x6b6c},
{0x45e4,0x45e4,0x6b75},
{0x45e5,0x45e5,0x6b7a},
{0x45e6,0x45e6,0x6b81},
{0x45e7,0x45e7,0x6b9b},
{0x45e8,0x45e8,0x6bae},
{0x45ea,0x45ea,0x6bbd},
{0x45eb,0x45ed,0x6bc7},
{0x45ee,0x45ee,0x6bda},
{0x45ef,0x45f0,0x6be6},
{0x45f1,0x45f1,0x6bee},
{0x45f2,0x45f2,0x6c02},
{0x45f3,0x45f3,0x6c0a},
{0x45f4,0x45f4,0x6c0e},
{0x45f5,0x45f5,0x6c36},
{0x45f7,0x45f7,0x6c4d},
{0x45f8,0x45f8,0x6c5b},
{0x45f9,0x45f9,0x6c6d},
{0x45fa,0x45fa,0x6c84},
{0x45fb,0x45fb,0x6c89},
{0x45fc,0x45fc,0x3cc3},
{0x45fd,0x45fd,0x6c94},
{0x45fe,0x45fe,0x6c97},
{0x45ff,0x45ff,0x6cad},
{0x4600,0x4600,0x6cc2},
{0x4601,0x4601,0x3cd2},
{0x4602,0x4602,0x6cdc},
{0x4603,0x4603,0x6ce9},
{0x4604,0x4604,0x6ced},
{0x4606,0x4606,0x6d00},
{0x4607,0x4607,0x6d24},
{0x4608,0x4609,0x6d26},
{0x460a,0x460a,0x6c67},
{0x460b,0x460b,0x6d2f},
{0x460c,0x460c,0x6d3c},
{0x460d,0x460d,0x6d5b},
{0x460e,0x460e,0x6d60},
{0x460f,0x460f,0x6d70},
{0x4610,0x4611,0x6d80},
{0x4612,0x4612,0x6d8a},
{0x4613,0x4613,0x6d8d},
{0x4614,0x4614,0x6d91},
{0x4615,0x4615,0x6d98},
{0x461a,0x461a,0x6dab},
{0x461b,0x461b,0x6dae},
{0x461c,0x461c,0x6db4},
{0x461d,0x461d,0x6dc2},
{0x461e,0x461e,0x6d34},
{0x461f,0x461f,0x6dc8},
{0x4620,0x4620,0x6dce},
{0x4621,0x4621,0x6ddf},
{0x4622,0x4622,0x6df6},
{0x4623,0x4623,0x6e36},
{0x4624,0x4624,0x6e1e},
{0x4625,0x4625,0x3d11},
{0x4626,0x4626,0x6e32},
{0x4627,0x4628,0x6e48},
{0x4629,0x462a,0x6e4b},
{0x462b,0x462b,0x6e4f},
{0x462c,0x462d,0x6e53},
{0x462e,0x462e,0x6e57},
{0x462f,0x462f,0x6e63},
{0x4630,0x4630,0x3d1e},
{0x4631,0x4631,0x6e93},
{0x4632,0x4632,0x6ea7},
{0x4633,0x4633,0x6eb4},
{0x4634,0x4634,0x6ec3},
{0x4635,0x4635,0x6f35},
{0x4636,0x4636,0x6eeb},
{0x4637,0x4637,0x6ef9},
{0x4638,0x4638,0x6efb},
{0x4639,0x4639,0x6f0a},
{0x463a,0x463a,0x6f0c},
{0x463b,0x463b,0x6f18},
{0x463c,0x463c,0x6f25},
{0x463d,0x463d,0x6f36},
{0x463e,0x463e,0x6f3c},
{0x4640,0x4640,0x6f52},
{0x4641,0x4641,0x6f57},
{0x4642,0x4642,0x6f60},
{0x4643,0x4643,0x6f68},
{0x4644,0x4644,0x6f90},
{0x4645,0x4645,0x6f96},
{0x4646,0x4646,0x6fbe},
{0x4647,0x4647,0x6f9f},
{0x4648,0x4648,0x6fa5},
{0x4649,0x4649,0x6faf},
{0x464a,0x464a,0x3d64},
{0x464b,0x464c,0x6fc8},
{0x464d,0x464d,0x6fe9},
{0x464f,0x464f,0x6ffc},
{0x4650,0x4650,0x7000},
{0x4651,0x4651,0x700a},
{0x4652,0x4652,0x7023},
{0x4654,0x4654,0x703a},
{0x4655,0x4655,0x7043},
{0x4656,0x4656,0x7047},
{0x4657,0x4657,0x704b},
{0x4658,0x4658,0x3d9a},
{0x4659,0x4659,0x7065},
{0x465a,0x465a,0x7069},
{0x465b,0x465b,0x706e},
{0x465c,0x465c,0x7076},
{0x465d,0x465d,0x7086},
{0x465e,0x465e,0x7097},
{0x4660,0x4660,0x709f},
{0x4661,0x4661,0x70b1},
{0x4663,0x4663,0x70ec},
{0x4664,0x4664,0x70ca},
{0x4665,0x4665,0x70d1},
{0x4666,0x4666,0x7103},
{0x4667,0x4667,0x7106},
{0x4668,0x4668,0x7108},
{0x4669,0x4669,0x710c},
{0x466a,0x466a,0x3dc0},
{0x466b,0x466b,0x712f},
{0x466c,0x466c,0x7150},
{0x466d,0x466d,0x7153},
{0x466e,0x466e,0x715e},
{0x466f,0x466f,0x3dd4},
{0x4670,0x4670,0x7196},
{0x4671,0x4671,0x7180},
{0x4672,0x4672,0x719b},
{0x4673,0x4673,0x71a0},
{0x4674,0x4674,0x71a2},
{0x4675,0x4676,0x71ae},
{0x4678,0x4678,0x71d9},
{0x4679,0x4679,0x71dc},
{0x467a,0x467a,0x7207},
{0x467b,0x467b,0x3e05},
{0x467c,0x467c,0x7234},
{0x467d,0x467d,0x7239},
{0x467e,0x467e,0x7242},
{0x467f,0x467f,0x7257},
{0x4680,0x4680,0x7263},
{0x4682,0x4683,0x726e},
{0x4684,0x4684,0x7278},
{0x4685,0x4685,0x727f},
{0x4686,0x4686,0x728e},
{0x4688,0x4688,0x72ae},
{0x4689,0x4689,0x72b0},
{0x468a,0x468a,0x72c1},
{0x468b,0x468b,0x3e60},
{0x468c,0x468c,0x72cc},
{0x468d,0x468d,0x3e66},
{0x468e,0x468e,0x3e68},
{0x468f,0x468f,0x72f3},
{0x4690,0x4690,0x72fa},
{0x4691,0x4691,0x7307},
{0x4692,0x4692,0x7312},
{0x4693,0x4694,0x7318},
{0x4695,0x4695,0x3e83},
{0x4696,0x4696,0x7339},
{0x4697,0x4697,0x732c},
{0x4698,0x4698,0x7333},
{0x4699,0x4699,0x733d},
{0x469a,0x469a,0x7352},
{0x469b,0x469b,0x3e94},
{0x469c,0x469c,0x736b},
{0x469e,0x469f,0x736e},
{0x46a0,0x46a0,0x7371},
{0x46a1,0x46a1,0x7381},
{0x46a2,0x46a2,0x738a},
{0x46a3,0x46a3,0x7394},
{0x46a4,0x46a4,0x7398},
{0x46a5,0x46a5,0x739c},
{0x46a6,0x46a6,0x73a5},
{0x46a7,0x46a7,0x73b9},
{0x46a8,0x46a8,0x73bf},
{0x46a9,0x46a9,0x73c5},
{0x46aa,0x46aa,0x73cb},
{0x46ab,0x46ab,0x73e1},
{0x46ac,0x46ac,0x73e7},
{0x46ad,0x46ad,0x73f9},
{0x46ae,0x46ae,0x7413},
{0x46af,0x46af,0x73fa},
{0x46b0,0x46b0,0x7401},
{0x46b1,0x46b1,0x7453},
{0x46b2,0x46b2,0x7440},
{0x46b3,0x46b3,0x7443},
{0x46b4,0x46b4,0x7452},
{0x46b5,0x46b5,0x745d},
{0x46b6,0x46b6,0x7481},
{0x46b7,0x46b7,0x7488},
{0x46b9,0x46b9,0x7492},
{0x46ba,0x46ba,0x7497},
{0x46bb,0x46bb,0x7499},
{0x46bc,0x46bc,0x74a1},
{0x46bd,0x46bd,0x74a5},
{0x46be,0x46be,0x74aa},
{0x46bf,0x46bf,0x74b9},
{0x46c0,0x46c0,0x74bb},
{0x46c1,0x46c1,0x74ba},
{0x46c2,0x46c2,0x74d6},
{0x46c3,0x46c3,0x74d8},
{0x46c4,0x46c4,0x74eb},
{0x46c6,0x46c6,0x74fa},
{0x46c8,0x46c8,0x7520},
{0x46c9,0x46c9,0x7524},
{0x46ca,0x46ca,0x752a},
{0x46cb,0x46cb,0x3f57},
{0x46cd,0x46ce,0x753d},
{0x46cf,0x46cf,0x7540},
{0x46d0,0x46d0,0x7548},
{0x46d1,0x46d1,0x7550},
{0x46d2,0x46d2,0x7552},
{0x46d3,0x46d3,0x7572},
{0x46d4,0x46d4,0x7571},
{0x46d5,0x46d5,0x757a},
{0x46d6,0x46d7,0x757d},
{0x46d8,0x46d8,0x758c},
{0x46d9,0x46d9,0x3f75},
{0x46da,0x46da,0x75a2},
{0x46db,0x46db,0x3f77},
{0x46dc,0x46dc,0x75b0},
{0x46dd,0x46dd,0x75b7},
{0x46de,0x46df,0x75bf},
{0x46e0,0x46e0,0x75c6},
{0x46e1,0x46e1,0x75cf},
{0x46e2,0x46e2,0x75d3},
{0x46e3,0x46e3,0x75dd},
{0x46e4,0x46e5,0x75df},
{0x46e6,0x46e6,0x75e7},
{0x46e7,0x46e7,0x75ee},
{0x46e8,0x46e8,0x75f1},
{0x46e9,0x46e9,0x7603},
{0x46ea,0x46ea,0x7618},
{0x46eb,0x46eb,0x7607},
{0x46ec,0x46ec,0x760f},
{0x46ed,0x46ed,0x3fae},
{0x46ef,0x46ef,0x7613},
{0x46f0,0x46f1,0x761b},
{0x46f3,0x46f3,0x7625},
{0x46f4,0x46f4,0x7628},
{0x46f5,0x46f5,0x763c},
{0x46f6,0x46f6,0x7633},
{0x46f8,0x46f8,0x3fc9},
{0x46f9,0x46f9,0x7641},
{0x46fb,0x46fb,0x7649},
{0x46fc,0x46fc,0x7655},
{0x46fd,0x46fd,0x3fd7},
{0x46fe,0x46fe,0x766e},
{0x46ff,0x46ff,0x7695},
{0x4700,0x4700,0x76a1},
{0x4701,0x4701,0x76a0},
{0x4702,0x4703,0x76a7},
{0x4704,0x4704,0x76af},
{0x4706,0x4706,0x76c9},
{0x4708,0x4708,0x76e8},
{0x470a,0x470a,0x7717},
{0x470b,0x470b,0x771a},
{0x470c,0x470c,0x772d},
{0x470d,0x470d,0x7735},
{0x470f,0x470f,0x4039},
{0x4712,0x4712,0x7758},
{0x4714,0x4714,0x777c},
{0x4716,0x4716,0x4058},
{0x4717,0x4717,0x779a},
{0x4718,0x4718,0x779f},
{0x4719,0x4719,0x77a2},
{0x471a,0x471a,0x77a4},
{0x471b,0x471c,0x77de},
{0x471d,0x471d,0x77e4},
{0x471e,0x471e,0x77ea},
{0x471f,0x471f,0x77ec},
{0x4720,0x4720,0x77fb},
{0x4722,0x4722,0x7805},
{0x4723,0x4723,0x7809},
{0x4724,0x4724,0x780d},
{0x4725,0x4725,0x7819},
{0x4726,0x4726,0x782c},
{0x4727,0x4727,0x7847},
{0x4728,0x4728,0x786a},
{0x472a,0x472a,0x788a},
{0x472b,0x472b,0x7894},
{0x472c,0x472c,0x78a4},
{0x472d,0x472d,0x789d},
{0x472e,0x472e,0x789f},
{0x472f,0x472f,0x78bb},
{0x4730,0x4730,0x78d5},
{0x4731,0x4731,0x78e6},
{0x4732,0x4733,0x78f9},
{0x4734,0x4734,0x78fe},
{0x4736,0x4736,0x7910},
{0x4737,0x4737,0x791b},
{0x4738,0x4738,0x7925},
{0x4739,0x4739,0x794a},
{0x473a,0x473a,0x7958},
{0x473b,0x473b,0x4105},
{0x473c,0x473c,0x7967},
{0x473d,0x473d,0x7972},
{0x473e,0x473e,0x7995},
{0x473f,0x473f,0x79a1},
{0x4740,0x4740,0x79a9},
{0x4741,0x4741,0x79b4},
{0x4742,0x4742,0x79c2},
{0x4743,0x4743,0x79c7},
{0x4744,0x4745,0x79cc},
{0x4746,0x4746,0x79d6},
{0x4747,0x4747,0x4148},
{0x474a,0x474a,0x414f},
{0x474b,0x474b,0x7a0a},
{0x474c,0x474c,0x7a15},
{0x474d,0x474d,0x7a1b},
{0x474e,0x474e,0x4163},
{0x474f,0x474f,0x7a38},
{0x4750,0x4750,0x7a47},
{0x4751,0x4751,0x7a56},
{0x4752,0x4752,0x7a59},
{0x4753,0x4753,0x7a5c},
{0x4754,0x4754,0x7a5f},
{0x4755,0x4755,0x7a67},
{0x4756,0x4756,0x7a6a},
{0x4757,0x4757,0x7a75},
{0x4758,0x4758,0x7a82},
{0x4759,0x4759,0x7a8a},
{0x475a,0x475a,0x7a90},
{0x475b,0x475b,0x7aac},
{0x475d,0x475d,0x41b4},
{0x475e,0x475e,0x7ab9},
{0x475f,0x475f,0x7abe},
{0x4760,0x4760,0x41bf},
{0x4761,0x4761,0x7acc},
{0x4762,0x4762,0x7ae8},
{0x4763,0x4763,0x7af4},
{0x4767,0x4767,0x7b3d},
{0x4768,0x4768,0x7b2a},
{0x4769,0x476a,0x7b2e},
{0x476b,0x476b,0x41e6},
{0x476c,0x476c,0x41f3},
{0x476d,0x476d,0x7b7f},
{0x476e,0x476e,0x7b41},
{0x476f,0x476f,0x41ee},
{0x4770,0x4770,0x7b55},
{0x4771,0x4771,0x7b79},
{0x4772,0x4772,0x7b64},
{0x4773,0x4773,0x7b66},
{0x4774,0x4774,0x7b73},
{0x4776,0x4776,0x4207},
{0x4777,0x4777,0x7b90},
{0x4778,0x4778,0x7b9b},
{0x4779,0x4779,0x420e},
{0x477a,0x477a,0x7bb5},
{0x477b,0x477b,0x7bbc},
{0x477c,0x477c,0x7bc5},
{0x477d,0x477d,0x7bca},
{0x4780,0x4780,0x7bd4},
{0x4781,0x4781,0x7bd6},
{0x4782,0x4782,0x7bda},
{0x4783,0x4783,0x7bea},
{0x4784,0x4784,0x7bf0},
{0x4785,0x4785,0x7c03},
{0x4786,0x4786,0x7c0e},
{0x4787,0x4787,0x7c45},
{0x4788,0x4788,0x7c4a},
{0x4789,0x4789,0x7c57},
{0x478a,0x478a,0x7c5e},
{0x478b,0x478b,0x7c61},
{0x478c,0x478c,0x7c69},
{0x478d,0x478d,0x7c6f},
{0x4791,0x4791,0x7ca6},
{0x4793,0x4794,0x7cb6},
{0x4796,0x4796,0x7cc4},
{0x4798,0x4798,0x7ccd},
{0x479b,0x479b,0x7ce6},
{0x479d,0x479d,0x7cf5},
{0x479e,0x479e,0x7d03},
{0x479f,0x479f,0x42c6},
{0x47a0,0x47a0,0x7d12},
{0x47a1,0x47a1,0x7d1e},
{0x47a4,0x47a5,0x7d3d},
{0x47a6,0x47a6,0x7d40},
{0x47a7,0x47a7,0x7d47},
{0x47aa,0x47aa,0x42d6},
{0x47ab,0x47ab,0x7d5a},
{0x47ac,0x47ac,0x7d6a},
{0x47ad,0x47ad,0x7d70},
{0x47ae,0x47ae,0x42dd},
{0x47af,0x47af,0x7d7f},
{0x47b1,0x47b1,0x7d88},
{0x47b2,0x47b2,0x7d97},
{0x47b4,0x47b4,0x7d9d},
{0x47b5,0x47b5,0x7da7},
{0x47b6,0x47b6,0x7daa},
{0x47b7,0x47b7,0x7db6},
{0x47b8,0x47b8,0x7dc0},
{0x47b9,0x47b9,0x7dd7},
{0x47ba,0x47ba,0x7dd9},
{0x47bb,0x47bb,0x7de6},
{0x47bc,0x47bc,0x4302},
{0x47be,0x47be,0xfa58},
{0x47bf,0x47bf,0x7e10},
{0x47c0,0x47c0,0x7e17},
{0x47c1,0x47c1,0x7e1d},
{0x47c2,0x47c2,0x7e27},
{0x47c3,0x47c3,0x7e2c},
{0x47c4,0x47c4,0x7e45},
{0x47c5,0x47c5,0x7e75},
{0x47c6,0x47c6,0x7e7e},
{0x47c7,0x47c8,0x7e86},
{0x47c9,0x47c9,0x432b},
{0x47ca,0x47ca,0x7e9a},
{0x47cb,0x47cb,0x4343},
{0x47cc,0x47cc,0x7f3c},
{0x47cd,0x47cd,0x7f3b},
{0x47ce,0x47ce,0x7f3e},
{0x47cf,0x47cf,0x7f43},
{0x47d0,0x47d0,0x34c1},
{0x47d3,0x47d4,0x7f63},
{0x47d5,0x47d5,0x7f6d},
{0x47d6,0x47d7,0x7f7d},
{0x47d8,0x47d8,0x7f90},
{0x47d9,0x47d9,0x517b},
{0x47db,0x47db,0x7f96},
{0x47dc,0x47dc,0x7f9c},
{0x47dd,0x47dd,0x7fad},
{0x47df,0x47df,0x7fc3},
{0x47e0,0x47e0,0x7fcf},
{0x47e1,0x47e1,0x7fe3},
{0x47e2,0x47e2,0x7ff2},
{0x47e3,0x47e3,0x800a},
{0x47e4,0x47e4,0x8008},
{0x47e5,0x47e5,0x8016},
{0x47e6,0x47e6,0x802c},
{0x47e7,0x47e7,0x8030},
{0x47e8,0x47e8,0x8043},
{0x47e9,0x47e9,0x8066},
{0x47ea,0x47ea,0x807b},
{0x47eb,0x47eb,0x8099},
{0x47ec,0x47ec,0x809c},
{0x47ed,0x47ed,0x80a4},
{0x47ee,0x47ee,0x80a7},
{0x47ef,0x47ef,0x80b8},
{0x47f1,0x47f1,0x80c5},
{0x47f2,0x47f2,0x80d5},
{0x47f3,0x47f3,0x80e6},
{0x47f4,0x47f4,0x810d},
{0x47f5,0x47f5,0x80f5},
{0x47f6,0x47f6,0x80fb},
{0x47f7,0x47f7,0x43ee},
{0x47f8,0x47f8,0x8135},
{0x47f9,0x47f9,0x811e},
{0x47fa,0x47fa,0x43f0},
{0x47fb,0x47fb,0x8124},
{0x47fc,0x47fc,0x8127},
{0x47fd,0x47fd,0x812c},
{0x47ff,0x47ff,0x813d},
{0x4800,0x4800,0x4408},
{0x4801,0x4801,0x8169},
{0x4802,0x4802,0x4417},
{0x4803,0x4803,0x441c},
{0x4804,0x4804,0x8185},
{0x4805,0x4805,0x4422},
{0x4806,0x4806,0x8198},
{0x4807,0x4807,0x81b2},
{0x4808,0x4808,0x81c1},
{0x4809,0x4809,0x81c3},
{0x480a,0x480a,0x81d6},
{0x480b,0x480b,0x81db},
{0x480d,0x480d,0x81e4},
{0x480f,0x480f,0x81ec},
{0x4810,0x4810,0x81fd},
{0x4811,0x4811,0x81ff},
{0x4813,0x4813,0x8204},
{0x4815,0x4815,0x8219},
{0x4816,0x4816,0x8222},
{0x4818,0x4818,0x823c},
{0x4819,0x4819,0x8249},
{0x481a,0x481a,0x8245},
{0x481c,0x481c,0x4476},
{0x481d,0x481d,0x447a},
{0x481e,0x481e,0x8257},
{0x4820,0x4820,0x825c},
{0x4821,0x4821,0x8263},
{0x4823,0x4823,0x4491},
{0x4824,0x4824,0x827d},
{0x4825,0x4825,0x827f},
{0x4826,0x4826,0x8283},
{0x4827,0x4827,0x828a},
{0x4828,0x4828,0x8293},
{0x4829,0x482a,0x82a7},
{0x482b,0x482b,0x82b2},
{0x482c,0x482c,0x82b4},
{0x482d,0x482d,0x82ba},
{0x482e,0x482e,0x82bc},
{0x482f,0x482f,0x82e2},
{0x4830,0x4830,0x82e8},
{0x4831,0x4831,0x82f7},
{0x4832,0x4833,0x8307},
{0x4834,0x4834,0x830c},
{0x4835,0x4835,0x8354},
{0x4836,0x4836,0x831b},
{0x4837,0x4837,0x831d},
{0x4838,0x4838,0x8330},
{0x4839,0x4839,0x833c},
{0x483a,0x483a,0x8344},
{0x483b,0x483b,0x8357},
{0x483c,0x483c,0x44be},
{0x483d,0x483d,0x44d4},
{0x483e,0x483e,0x44b3},
{0x483f,0x4840,0x8394},
{0x4841,0x4841,0x839b},
{0x4842,0x4842,0x839d},
{0x4843,0x4843,0x83c9},
{0x4844,0x4844,0x83d0},
{0x4845,0x4845,0x83d4},
{0x4846,0x4846,0x83dd},
{0x4847,0x4847,0x83e5},
{0x4848,0x4848,0x83f9},
{0x4849,0x4849,0x8415},
{0x484b,0x484b,0x8417},
{0x484c,0x484c,0x8439},
{0x484d,0x484d,0x844f},
{0x484e,0x484f,0x8451},
{0x4850,0x4851,0x8459},
{0x4852,0x4852,0x845c},
{0x4854,0x4854,0x8465},
{0x4855,0x4855,0x8478},
{0x4856,0x4856,0x847c},
{0x4857,0x4857,0x8481},
{0x4858,0x4858,0x450d},
{0x4859,0x4859,0x8497},
{0x485a,0x485a,0x84a6},
{0x485b,0x485b,0x84be},
{0x485c,0x485c,0x4508},
{0x485d,0x485e,0x84ce},
{0x485f,0x485f,0x84d3},
{0x4861,0x4861,0x84e7},
{0x4862,0x4862,0x84ea},
{0x4863,0x4863,0x84ef},
{0x4864,0x4864,0x84f1},
{0x4865,0x4865,0x84fa},
{0x4866,0x4866,0x851b},
{0x4867,0x4868,0x8524},
{0x4869,0x4869,0x852b},
{0x486a,0x486a,0x854f},
{0x486b,0x486b,0x856f},
{0x486c,0x486c,0x4543},
{0x486d,0x486d,0x8551},
{0x486e,0x486f,0x8561},
{0x4871,0x4871,0x857b},
{0x4872,0x4872,0x857d},
{0x4873,0x4873,0x857f},
{0x4874,0x4874,0x8581},
{0x4875,0x4875,0x8586},
{0x4876,0x4876,0x8593},
{0x4877,0x4877,0x859d},
{0x4878,0x4878,0x859f},
{0x487c,0x487c,0x85bc},
{0x487d,0x487d,0x85c7},
{0x487e,0x487e,0x85ca},
{0x487f,0x4880,0x85d8},
{0x4881,0x4881,0x85df},
{0x4882,0x4882,0x85e1},
{0x4883,0x4883,0x85e6},
{0x4884,0x4884,0x85f6},
{0x4885,0x4885,0x8600},
{0x4886,0x4886,0x8611},
{0x4887,0x4887,0x861e},
{0x4888,0x4888,0x8621},
{0x4889,0x4889,0x8624},
{0x488a,0x488a,0x8627},
{0x488c,0x488c,0x8639},
{0x488d,0x488d,0x863c},
{0x488f,0x488f,0x8640},
{0x4890,0x4890,0x8653},
{0x4891,0x4891,0x8656},
{0x4892,0x4892,0x8677},
{0x4893,0x4893,0x8687},
{0x4894,0x4894,0x8689},
{0x4895,0x4896,0x869c},
{0x4897,0x4897,0x86b1},
{0x4898,0x4898,0x86b3},
{0x4899,0x4899,0x86c1},
{0x489a,0x489a,0x86c3},
{0x489b,0x489b,0x86d1},
{0x489c,0x489c,0x86d5},
{0x489d,0x489d,0x86d7},
{0x489e,0x489e,0x86e3},
{0x489f,0x489f,0x86e6},
{0x48a0,0x48a0,0x45b8},
{0x48a1,0x48a1,0x8705},
{0x48a2,0x48a2,0x8707},
{0x48a3,0x48a3,0x870e},
{0x48a4,0x48a4,0x8710},
{0x48a5,0x48a5,0x871f},
{0x48a6,0x48a6,0x8721},
{0x48a7,0x48a7,0x8723},
{0x48a8,0x48a8,0x8731},
{0x48a9,0x48a9,0x873a},
{0x48aa,0x48aa,0x8740},
{0x48ab,0x48ab,0x8743},
{0x48ac,0x48ac,0x8751},
{0x48ad,0x48ad,0x8758},
{0x48ae,0x48af,0x8764},
{0x48b0,0x48b0,0x8772},
{0x48b1,0x48b1,0x877c},
{0x48b4,0x48b4,0x87a7},
{0x48b5,0x48b5,0x8789},
{0x48b6,0x48b6,0x878b},
{0x48b7,0x48b7,0x8793},
{0x48b8,0x48b8,0x87a0},
{0x48ba,0x48ba,0x45e5},
{0x48bb,0x48bb,0x87be},
{0x48bd,0x48bd,0x87c1},
{0x48be,0x48be,0x87ce},
{0x48bf,0x48bf,0x87df},
{0x48c1,0x48c1,0x87e3},
{0x48c2,0x48c3,0x87e5},
{0x48c4,0x48c4,0x87ea},
{0x48c5,0x48c5,0x8813},
{0x48c6,0x48c6,0x8828},
{0x48c7,0x48c7,0x882e},
{0x48c8,0x48c8,0x8832},
{0x48c9,0x48c9,0x883c},
{0x48ca,0x48ca,0x460f},
{0x48cb,0x48cb,0x884a},
{0x48cc,0x48cc,0x8858},
{0x48cf,0x48cf,0x8869},
{0x48d1,0x48d1,0x886f},
{0x48d2,0x48d2,0x88a0},
{0x48d3,0x48d3,0x88bc},
{0x48d4,0x48d4,0x88c0},
{0x48d6,0x48d6,0x88d1},
{0x48d7,0x48d7,0x88d3},
{0x48d8,0x48d8,0x4641},
{0x48d9,0x48d9,0x8901},
{0x48db,0x48db,0x8937},
{0x48dd,0x48dd,0x8942},
{0x48de,0x48de,0x8945},
{0x48df,0x48df,0x8949},
{0x48e1,0x48e1,0x8962},
{0x48e2,0x48e2,0x8989},
{0x48e3,0x48e3,0x8990},
{0x48e4,0x48e4,0x899f},
{0x48e5,0x48e5,0x89b0},
{0x48e6,0x48e6,0x89b7},
{0x48e7,0x48e7,0x89d8},
{0x48e8,0x48e8,0x89eb},
{0x48e9,0x48e9,0x46a1},
{0x48ea,0x48ea,0x89f3},
{0x48eb,0x48eb,0x89fd},
{0x48ec,0x48ec,0x89ff},
{0x48ed,0x48ed,0x46af},
{0x48ee,0x48ee,0x8a11},
{0x48ef,0x48ef,0x8a14},
{0x48f1,0x48f1,0x8a21},
{0x48f2,0x48f2,0x8a35},
{0x48f3,0x48f3,0x8a3e},
{0x48f4,0x48f4,0x8a45},
{0x48f5,0x48f5,0x8a4d},
{0x48f6,0x48f6,0x8a58},
{0x48f7,0x48f7,0x8aae},
{0x48f8,0x48f8,0x8a90},
{0x48f9,0x48f9,0x8ab7},
{0x48fa,0x48fa,0x8ad7},
{0x48fb,0x48fb,0x8afc},
{0x48fd,0x48fd,0x8b0a},
{0x48fe,0x48fe,0x8b05},
{0x48ff,0x48ff,0x8b0d},
{0x4900,0x4900,0x8b1c},
{0x4901,0x4901,0x8b2d},
{0x4902,0x4902,0x470c},
{0x4903,0x4903,0x8b51},
{0x4904,0x4904,0x8b5e},
{0x4905,0x4905,0x8b76},
{0x4906,0x4906,0x8b81},
{0x4907,0x4907,0x8b8b},
{0x4908,0x4909,0x8b94},
{0x490a,0x490a,0x8c39},
{0x490c,0x490c,0x8c3d},
{0x490f,0x490f,0x8c45},
{0x4910,0x4910,0x8c4f},
{0x4911,0x4911,0x8c57},
{0x4912,0x4912,0x8c69},
{0x4913,0x4913,0x8c6d},
{0x4915,0x4915,0x8c93},
{0x4916,0x4916,0x8c92},
{0x4917,0x4917,0x8c99},
{0x4918,0x4918,0x4764},
{0x4919,0x4919,0x8c9b},
{0x491a,0x491a,0x8cd6},
{0x491b,0x491b,0x8cd5},
{0x491d,0x491d,0x8cf1},
{0x491f,0x491f,0x8d09},
{0x4920,0x4920,0x8d0e},
{0x4921,0x4921,0x8dc8},
{0x4922,0x4922,0x8dd9},
{0x4923,0x4923,0x8e0c},
{0x4924,0x4924,0x47fd},
{0x4925,0x4925,0x8dfd},
{0x4926,0x4926,0x8e06},
{0x4928,0x4928,0x8e14},
{0x4929,0x4929,0x8e16},
{0x492a,0x492b,0x8e21},
{0x492c,0x492c,0x8e27},
{0x492e,0x492e,0x4816},
{0x492f,0x492f,0x8e36},
{0x4930,0x4930,0x8e39},
{0x4931,0x4931,0x8e54},
{0x4932,0x4932,0x8e62},
{0x4933,0x4933,0x8e6d},
{0x4934,0x4934,0x8e6f},
{0x4935,0x4935,0x8e98},
{0x4936,0x4936,0x8e9e},
{0x4937,0x4937,0x8eb5},
{0x4938,0x4938,0x8ebb},
{0x493a,0x493a,0x484e},
{0x493c,0x493c,0x8f00},
{0x493d,0x493d,0x8f08},
{0x493e,0x493e,0x8f2b},
{0x493f,0x493f,0x8f40},
{0x4940,0x4940,0x8f4a},
{0x4941,0x4941,0x8f58},
{0x4943,0x4943,0x8fa4},
{0x4944,0x4944,0x8fb4},
{0x4946,0x4946,0x8fc1},
{0x4947,0x4947,0x8fc6},
{0x4948,0x4948,0xfa24},
{0x4949,0x4949,0x8fca},
{0x494a,0x494a,0x8fcd},
{0x494b,0x494b,0x8fd3},
{0x494c,0x494c,0x8fd5},
{0x494d,0x494d,0x8ff1},
{0x494e,0x494e,0x8ff5},
{0x494f,0x494f,0x8ffb},
{0x4950,0x4950,0x900c},
{0x4951,0x4951,0x9037},
{0x4953,0x4953,0x9043},
{0x4954,0x4954,0x905d},
{0x4957,0x4957,0x9085},
{0x4958,0x4958,0x908c},
{0x4959,0x4959,0x9090},
{0x495a,0x495a,0x90a1},
{0x495b,0x495b,0x48b5},
{0x495c,0x495c,0x90b0},
{0x495d,0x495d,0x90b6},
{0x495e,0x495e,0x90c3},
{0x495f,0x495f,0x90c8},
{0x4961,0x4961,0x90dc},
{0x4962,0x4962,0x90df},
{0x4964,0x4964,0x90f6},
{0x4965,0x4965,0x90f2},
{0x4966,0x4966,0x9100},
{0x4967,0x4967,0x90eb},
{0x4968,0x4969,0x90fe},
{0x496a,0x496a,0x9104},
{0x496b,0x496b,0x9106},
{0x496c,0x496c,0x9118},
{0x496d,0x496d,0x911e},
{0x496e,0x496e,0x9137},
{0x496f,0x496f,0x9139},
{0x4970,0x4971,0x9146},
{0x4972,0x4972,0x9157},
{0x4973,0x4973,0x9159},
{0x4974,0x4974,0x9174},
{0x4975,0x4975,0x9179},
{0x4976,0x4976,0x9185},
{0x4977,0x4977,0x91b3},
{0x4978,0x4978,0x91b6},
{0x4979,0x497a,0x91c3},
{0x497d,0x497d,0x91ec},
{0x497e,0x497e,0x9201},
{0x497f,0x4980,0x9216},
{0x4982,0x4982,0x9242},
{0x4983,0x4983,0x924a},
{0x4984,0x4984,0x9256},
{0x4985,0x4985,0x9261},
{0x4986,0x4986,0x9265},
{0x4987,0x4987,0x9268},
{0x4989,0x498a,0x927c},
{0x498b,0x498b,0x927f},
{0x498c,0x498c,0x9289},
{0x498d,0x498d,0x928d},
{0x498e,0x498e,0x9297},
{0x498f,0x498f,0x9299},
{0x4990,0x4990,0x929f},
{0x4991,0x4991,0x92ab},
{0x4994,0x4994,0x92b2},
{0x4995,0x4996,0x92bf},
{0x4997,0x4997,0x92c6},
{0x4998,0x4998,0x92ce},
{0x4999,0x4999,0x92e5},
{0x499a,0x499a,0x9311},
{0x499d,0x499d,0x92f7},
{0x499e,0x499e,0x9329},
{0x49a1,0x49a1,0x9351},
{0x49a2,0x49a2,0x935a},
{0x49a3,0x49a3,0x936b},
{0x49a4,0x49a4,0x9371},
{0x49a5,0x49a5,0x9373},
{0x49a6,0x49a6,0x93a1},
{0x49a9,0x49a9,0x9388},
{0x49aa,0x49aa,0x938b},
{0x49ab,0x49ab,0x938f},
{0x49ac,0x49ac,0x939e},
{0x49ad,0x49ad,0x93f5},
{0x49b0,0x49b0,0x93f1},
{0x49b1,0x49b1,0x93c7},
{0x49b2,0x49b2,0x93dc},
{0x49b3,0x49b3,0x93e7},
{0x49b4,0x49b4,0x9409},
{0x49b5,0x49b6,0x9416},
{0x49b7,0x49b7,0x93fb},
{0x49b8,0x49b8,0x9432},
{0x49b9,0x49b9,0x943b},
{0x49bc,0x49bc,0x946d},
{0x49bd,0x49bd,0x946f},
{0x49be,0x49be,0x9579},
{0x49bf,0x49bf,0x9586},
{0x49c0,0x49c1,0x958c},
{0x49c4,0x49c4,0x95c8},
{0x49c7,0x49c7,0x962c},
{0x49c8,0x49c9,0x9633},
{0x49cb,0x49cb,0x963c},
{0x49cc,0x49cc,0x9661},
{0x49ce,0x49ce,0x9682},
{0x49d0,0x49d0,0x969a},
{0x49d2,0x49d2,0x49e7},
{0x49d3,0x49d3,0x96b3},
{0x49d4,0x49d4,0x96ba},
{0x49d5,0x49d5,0x96bd},
{0x49d6,0x49d6,0x49fa},
{0x49d8,0x49d8,0x96d8},
{0x49d9,0x49d9,0x96da},
{0x49da,0x49da,0x96dd},
{0x49db,0x49db,0x4a04},
{0x49dc,0x49dc,0x9714},
{0x49dd,0x49dd,0x9723},
{0x49de,0x49de,0x4a29},
{0x49df,0x49df,0x9736},
{0x49e0,0x49e0,0x9741},
{0x49e1,0x49e1,0x9747},
{0x49e2,0x49e2,0x9757},
{0x49e3,0x49e3,0x975b},
{0x49e4,0x49e4,0x976a},
{0x49e7,0x49e7,0x9796},
{0x49e8,0x49e8,0x979e},
{0x49e9,0x49ea,0x97b1},
{0x49eb,0x49eb,0x97be},
{0x49ec,0x49ec,0x97cc},
{0x49ed,0x49ed,0x97d1},
{0x49ee,0x49ee,0x97d4},
{0x49ef,0x49ef,0x97d8},
{0x49f0,0x49f0,0x97e1},
{0x49f1,0x49f1,0x97f1},
{0x49f2,0x49f2,0x9804},
{0x49f3,0x49f3,0x980d},
{0x49f4,0x49f4,0x9814},
{0x49f5,0x49f5,0x9816},
{0x49f6,0x49f6,0x4abc},
{0x49f8,0x49f9,0x9832},
{0x49fa,0x49fa,0x9825},
{0x49fb,0x49fb,0x9847},
{0x49fc,0x49fc,0x9866},
{0x49fd,0x49fd,0x98ab},
{0x49fe,0x49fe,0x98ad},
{0x49ff,0x49ff,0x98b0},
{0x4a01,0x4a01,0x98b7},
{0x4a02,0x4a03,0x98bb},
{0x4a04,0x4a04,0x98c2},
{0x4a05,0x4a05,0x98c7},
{0x4a06,0x4a06,0x98cb},
{0x4a07,0x4a07,0x98e1},
{0x4a08,0x4a08,0x98e3},
{0x4a09,0x4a09,0x98ea},
{0x4a0a,0x4a0b,0x98f0},
{0x4a0c,0x4a0c,0x98f3},
{0x4a0d,0x4a0d,0x9908},
{0x4a0e,0x4a0e,0x4b3b},
{0x4a10,0x4a11,0x9916},
{0x4a13,0x4a15,0x991a},
{0x4a17,0x4a17,0x9931},
{0x4a18,0x4a1a,0x993a},
{0x4a1b,0x4a1b,0x9941},
{0x4a1c,0x4a1c,0x9946},
{0x4a1d,0x4a1d,0x994e},
{0x4a1e,0x4a1e,0x9960},
{0x4a1f,0x4a1f,0x99a3},
{0x4a20,0x4a20,0x99a6},
{0x4a21,0x4a21,0x99bd},
{0x4a22,0x4a22,0x99bf},
{0x4a23,0x4a23,0x99c3},
{0x4a24,0x4a24,0x99d4},
{0x4a25,0x4a25,0x99d9},
{0x4a26,0x4a26,0x99de},
{0x4a28,0x4a28,0x99f0},
{0x4a29,0x4a29,0x99f9},
{0x4a2a,0x4a2a,0x99fc},
{0x4a2b,0x4a2b,0x9a0a},
{0x4a2c,0x4a2c,0x9a11},
{0x4a2d,0x4a2d,0x9a1a},
{0x4a2e,0x4a2e,0x9a20},
{0x4a2f,0x4a2f,0x9a31},
{0x4a30,0x4a30,0x9a44},
{0x4a31,0x4a31,0x9a4c},
{0x4a32,0x4a32,0x9a58},
{0x4a33,0x4a33,0x4bc2},
{0x4a34,0x4a34,0x9aaf},
{0x4a35,0x4a35,0x4bca},
{0x4a36,0x4a36,0x9ab7},
{0x4a37,0x4a37,0x4bd2},
{0x4a38,0x4a38,0x9ab9},
{0x4a3a,0x4a3a,0x9ac6},
{0x4a3b,0x4a3b,0x9ad0},
{0x4a3c,0x4a3c,0x9ad2},
{0x4a3d,0x4a3d,0x9ad5},
{0x4a3e,0x4a3e,0x9ae0},
{0x4a3f,0x4a3f,0x9ae5},
{0x4a40,0x4a40,0x9ae9},
{0x4a41,0x4a41,0x9b0c},
{0x4a42,0x4a42,0x9b10},
{0x4a43,0x4a43,0x9b12},
{0x4a44,0x4a44,0x9b16},
{0x4a45,0x4a45,0x9b1c},
{0x4a46,0x4a46,0x9b2b},
{0x4a47,0x4a47,0x9b3d},
{0x4a48,0x4a48,0x4c20},
{0x4a49,0x4a49,0x9b4b},
{0x4a4a,0x4a4a,0x9b63},
{0x4a4b,0x4a4b,0x9b65},
{0x4a4c,0x4a4d,0x9b6b},
{0x4a4e,0x4a4f,0x9b76},
{0x4a50,0x4a50,0x9ba6},
{0x4a51,0x4a51,0x9bac},
{0x4a53,0x4a53,0x9bb2},
{0x4a54,0x4a54,0x9bb8},
{0x4a55,0x4a55,0x9bbe},
{0x4a56,0x4a56,0x9bf3},
{0x4a57,0x4a57,0x9bd8},
{0x4a58,0x4a58,0x9bdd},
{0x4a59,0x4a59,0x9bea},
{0x4a5a,0x4a5a,0x9bef},
{0x4a5b,0x4a5b,0x9bee},
{0x4a5f,0x4a5f,0x9c16},
{0x4a60,0x4a62,0x9c18},
{0x4a63,0x4a63,0x9c1d},
{0x4a64,0x4a64,0x9c22},
{0x4a65,0x4a65,0x9c29},
{0x4a67,0x4a67,0x9c31},
{0x4a68,0x4a68,0x9c37},
{0x4a69,0x4a69,0x9c45},
{0x4a6a,0x4a6a,0x9c5c},
{0x4a6c,0x4a6d,0x9c49},
{0x4a6f,0x4a6f,0x9c54},
{0x4a70,0x4a70,0x9c58},
{0x4a71,0x4a71,0x9c5b},
{0x4a72,0x4a72,0x9c5d},
{0x4a73,0x4a73,0x9c5f},
{0x4a74,0x4a76,0x9c69},
{0x4a77,0x4a78,0x9c6d},
{0x4a79,0x4a79,0x9c72},
{0x4a7a,0x4a7a,0x9c75},
{0x4a7b,0x4a7b,0x9c7a},
{0x4a7c,0x4a7c,0x9ce6},
{0x4a7d,0x4a7d,0x9cf2},
{0x4a7e,0x4a7e,0x9d0b},
{0x4a80,0x4a80,0x9d11},
{0x4a81,0x4a82,0x9d17},
{0x4a84,0x4a84,0x4cc4},
{0x4a86,0x4a86,0x9d32},
{0x4a87,0x4a87,0x4cd1},
{0x4a88,0x4a88,0x9d4a},
{0x4a89,0x4a89,0x9d5f},
{0x4a8a,0x4a8a,0x9d62},
{0x4a8d,0x4a8d,0x9d73},
{0x4a8e,0x4a8f,0x9d76},
{0x4a90,0x4a90,0x9d84},
{0x4a91,0x4a91,0x9d99},
{0x4a92,0x4a92,0x9da1},
{0x4a93,0x4a93,0x9dbf},
{0x4a94,0x4a94,0x9db5},
{0x4a95,0x4a95,0x9db9},
{0x4a96,0x4a96,0x9dbd},
{0x4a97,0x4a97,0x9dc9},
{0x4a98,0x4a98,0x9dda},
{0x4a99,0x4a99,0x9de0},
{0x4a9a,0x4a9a,0x9de3},
{0x4a9b,0x4a9b,0x4d07},
{0x4a9c,0x4a9c,0x9e0a},
{0x4a9d,0x4a9d,0x9e02},
{0x4a9e,0x4a9e,0x9e0d},
{0x4a9f,0x4a9f,0x9e1c},
{0x4aa0,0x4aa0,0x9e7b},
{0x4aa2,0x4aa2,0x9e80},
{0x4aa3,0x4aa3,0x9e85},
{0x4aa4,0x4aa4,0x9e9b},
{0x4aa6,0x4aa6,0x9ebd},
{0x4aa8,0x4aa8,0x9edf},
{0x4aa9,0x4aa9,0x9eff},
{0x4aaa,0x4aaa,0x9f02},
{0x4aab,0x4aab,0x4d77},
{0x4aac,0x4aac,0x9f03},
{0x4aad,0x4aad,0x9f3a},
{0x4aae,0x4aae,0x9f3d},
{0x4aaf,0x4aaf,0x9f46},
{0x4ab0,0x4ab0,0x9f53},
{0x4ab1,0x4ab1,0x9f55},
{0x4ab2,0x4ab2,0x9f58},
{0x4ab4,0x4ab4,0x9f5d},
{0x4ab6,0x4ab6,0x9f69},
{0x4ab7,0x4ab7,0x9f6d},
{0x4ab8,0x4ab8,0x9f70},
{0x4aba,0x4aba,0x2eac},
{0x4abb,0x4abb,0x3614},
{0x4abc,0x4abc,0x38ad},
{0x4abd,0x4abd,0x3dcc},
{0x4abe,0x4abe,0x3fdc},
{0x4abf,0x4abf,0x45be},
{0x4ac0,0x4ac0,0x4610},
{0x4ac1,0x4ac1,0x4b7e},
{0x4ac2,0x4ac2,0x4c38},
{0x4ac3,0x4ac3,0x4ce1},
{0x4ac4,0x4ac4,0x4e0c},
{0x4ac5,0x4ac6,0x4e23},
{0x4ac7,0x4ac7,0x4e79},
{0x4ac8,0x4ac8,0x4ef3},
{0x4ac9,0x4ac9,0x4f0c},
{0x4aca,0x4aca,0x4f19},
{0x4acb,0x4acb,0x4f2b},
{0x4acc,0x4acc,0x4f2e},
{0x4acd,0x4acd,0x4f31},
{0x4ace,0x4ace,0x4f84},
{0x4acf,0x4acf,0x4f9e},
{0x4ad0,0x4ad0,0x4fb7},
{0x4ad1,0x4ad1,0x5004},
{0x4ad2,0x4ad2,0x500c},
{0x4ad3,0x4ad3,0x504c},
{0x4ad4,0x4ad4,0x505f},
{0x4ad5,0x4ad5,0x5062},
{0x4ad6,0x4ad6,0x5077},
{0x4ad7,0x4ad7,0x508e},
{0x4ad8,0x4ad8,0x509e},
{0x4ad9,0x4ad9,0x50a2},
{0x4ada,0x4ada,0x50c3},
{0x4adb,0x4adb,0x50e8},
{0x4adc,0x4adc,0x50f1},
{0x4add,0x4add,0x50fe},
{0x4ade,0x4ade,0x5107},
{0x4adf,0x4ae1,0x510c},
{0x4ae2,0x4ae2,0x5133},
{0x4ae3,0x4ae3,0x5138},
{0x4ae4,0x4ae4,0x5174},
{0x4ae5,0x4ae5,0x5184},
{0x4ae6,0x4ae6,0x51b8},
{0x4ae7,0x4ae7,0x51ba},
{0x4ae8,0x4ae8,0x51c8},
{0x4ae9,0x4ae9,0x51cf},
{0x4aea,0x4aea,0x51d1},
{0x4aeb,0x4aec,0x51d3},
{0x4aed,0x4aed,0x51d8},
{0x4aee,0x4aee,0x51df},
{0x4aef,0x4aef,0x5205},
{0x4af0,0x4af0,0x5226},
{0x4af1,0x4af1,0x5228},
{0x4af2,0x4af2,0x522b},
{0x4af3,0x4af4,0x5231},
{0x4af5,0x4af5,0x5235},
{0x4af6,0x4af6,0x523c},
{0x4af7,0x4af7,0x525a},
{0x4af8,0x4af8,0x5260},
{0x4af9,0x4af9,0x526e},
{0x4afa,0x4afb,0x5278},
{0x4afc,0x4afc,0x528a},
{0x4afd,0x4afd,0x528c},
{0x4afe,0x4afe,0x52e1},
{0x4aff,0x4aff,0x52e9},
{0x4b00,0x4b00,0x52f1},
{0x4b01,0x4b01,0x5303},
{0x4b02,0x4b02,0x5311},
{0x4b03,0x4b03,0x531f},
{0x4b04,0x4b04,0x532d},
{0x4b05,0x4b05,0x5332},
{0x4b06,0x4b06,0x533d},
{0x4b07,0x4b07,0x5365},
{0x4b08,0x4b08,0x536d},
{0x4b09,0x4b09,0x5379},
{0x4b0a,0x4b0a,0x537e},
{0x4b0b,0x4b0b,0x5394},
{0x4b0c,0x4b0c,0x5399},
{0x4b0d,0x4b0d,0x53aa},
{0x4b0e,0x4b0e,0x53af},
{0x4b0f,0x4b0f,0x53ba},
{0x4b10,0x4b10,0x53c1},
{0x4b11,0x4b12,0x53c4},
{0x4b13,0x4b13,0x53e0},
{0x4b14,0x4b14,0x5413},
{0x4b15,0x4b15,0x542a},
{0x4b16,0x4b16,0x5431},
{0x4b17,0x4b18,0x5434},
{0x4b19,0x4b19,0x544c},
{0x4b1a,0x4b1a,0x54a7},
{0x4b1b,0x4b1b,0x54aa},
{0x4b1c,0x4b1c,0x54b1},
{0x4b1d,0x4b1d,0x54bb},
{0x4b1e,0x4b1e,0x54ce},
{0x4b1f,0x4b1f,0x54ea},
{0x4b20,0x4b20,0x54fc},
{0x4b21,0x4b21,0x5505},
{0x4b22,0x4b22,0x5508},
{0x4b23,0x4b23,0x5515},
{0x4b24,0x4b24,0x5527},
{0x4b25,0x4b25,0x552a},
{0x4b26,0x4b26,0x5536},
{0x4b27,0x4b27,0x5551},
{0x4b28,0x4b28,0x5566},
{0x4b29,0x4b29,0x558f},
{0x4b2a,0x4b2a,0x5592},
{0x4b2b,0x4b2b,0x5594},
{0x4b2c,0x4b2c,0x55a4},
{0x4b2d,0x4b2d,0x55b2},
{0x4b2e,0x4b2e,0x55c3},
{0x4b2f,0x4b2f,0x55c6},
{0x4b30,0x4b30,0x55d3},
{0x4b31,0x4b31,0x55db},
{0x4b32,0x4b32,0x55ec},
{0x4b33,0x4b33,0x55ee},
{0x4b34,0x4b34,0x55f1},
{0x4b35,0x4b35,0x55f6},
{0x4b36,0x4b36,0x55f8},
{0x4b37,0x4b37,0x5605},
{0x4b38,0x4b38,0x560d},
{0x4b39,0x4b3a,0x5611},
{0x4b3b,0x4b3b,0x562c},
{0x4b3c,0x4b3c,0x5635},
{0x4b3d,0x4b3d,0x5639},
{0x4b3e,0x4b3e,0x564d},
{0x4b3f,0x4b3f,0x5654},
{0x4b40,0x4b40,0x5685},
{0x4b41,0x4b41,0x569f},
{0x4b42,0x4b42,0x56a6},
{0x4b43,0x4b43,0x56b7},
{0x4b44,0x4b44,0x56cc},
{0x4b45,0x4b45,0x56cf},
{0x4b46,0x4b46,0x56d9},
{0x4b47,0x4b47,0x56e1},
{0x4b48,0x4b48,0x56eb},
{0x4b49,0x4b49,0x56ed},
{0x4b4a,0x4b4a,0x56f1},
{0x4b4b,0x4b4b,0x5707},
{0x4b4c,0x4b4c,0x570c},
{0x4b4d,0x4b4e,0x571a},
{0x4b4f,0x4b4f,0x571d},
{0x4b50,0x4b50,0x572c},
{0x4b51,0x4b51,0x572e},
{0x4b52,0x4b53,0x573d},
{0x4b54,0x4b54,0x575f},
{0x4b55,0x4b55,0x576b},
{0x4b56,0x4b56,0x576d},
{0x4b57,0x4b57,0x577a},
{0x4b58,0x4b58,0x5783},
{0x4b59,0x4b59,0x5797},
{0x4b5a,0x4b5a,0x57ae},
{0x4b5b,0x4b5b,0x57d5},
{0x4b5c,0x4b5c,0x57e7},
{0x4b5d,0x4b5d,0x580d},
{0x4b5e,0x4b5e,0x5826},
{0x4b5f,0x4b5f,0x584d},
{0x4b60,0x4b60,0x584f},
{0x4b61,0x4b61,0x585f},
{0x4b62,0x4b62,0x586d},
{0x4b63,0x4b63,0x587f},
{0x4b64,0x4b64,0x5881},
{0x4b65,0x4b65,0x5898},
{0x4b66,0x4b66,0x58bc},
{0x4b67,0x4b67,0x591f},
{0x4b68,0x4b68,0x5923},
{0x4b69,0x4b69,0x5959},
{0x4b6a,0x4b6a,0x5979},
{0x4b6b,0x4b6b,0x5997},
{0x4b6c,0x4b6c,0x59af},
{0x4b6d,0x4b6d,0x59b3},
{0x4b6e,0x4b6e,0x59df},
{0x4b6f,0x4b6f,0x59f1},
{0x4b70,0x4b70,0x59f8},
{0x4b71,0x4b71,0x5ab2},
{0x4b72,0x4b72,0x5ab8},
{0x4b73,0x4b73,0x5aea},
{0x4b74,0x4b74,0x5af6},
{0x4b75,0x4b75,0x5b1b},
{0x4b76,0x4b76,0x5b1d},
{0x4b77,0x4b77,0x5b21},
{0x4b78,0x4b78,0x5b38},
{0x4b79,0x4b79,0x5bb7},
{0x4b7a,0x4b7a,0x5bd7},
{0x4b7b,0x4b7b,0x5be0},
{0x4b7c,0x4b7c,0x5c1f},
{0x4b7d,0x4b7d,0x5c2a},
{0x4b7e,0x4b7e,0x5c2c},
{0x4b7f,0x4b7f,0x5c36},
{0x4b80,0x4b80,0x5c59},
{0x4b81,0x4b81,0x5c5c},
{0x4b82,0x4b82,0x5c6d},
{0x4b83,0x4b83,0x5cdd},
{0x4b84,0x4b84,0x5d01},
{0x4b85,0x4b85,0x5d34},
{0x4b86,0x4b86,0x5d3d},
{0x4b87,0x4b87,0x5d59},
{0x4b88,0x4b88,0x5d7e},
{0x4b89,0x4b89,0x5d83},
{0x4b8a,0x4b8a,0x5dc7},
{0x4b8b,0x4b8b,0x5df9},
{0x4b8c,0x4b8c,0x5e28},
{0x4b8d,0x4b8d,0x5e32},
{0x4b8e,0x4b8e,0x5e35},
{0x4b8f,0x4b8f,0x5e5b},
{0x4b90,0x4b90,0x5e68},
{0x4b91,0x4b91,0x5e6a},
{0x4b92,0x4b92,0x5e77},
{0x4b93,0x4b93,0x5e80},
{0x4b94,0x4b94,0x5e8b},
{0x4b95,0x4b95,0x5eb3},
{0x4b96,0x4b96,0x5ebd},
{0x4b97,0x4b97,0x5ed1},
{0x4b98,0x4b99,0x5ed4},
{0x4b9a,0x4b9a,0x5f4d},
{0x4b9b,0x4b9b,0x5fac},
{0x4b9c,0x4b9c,0x5fea},
{0x4b9d,0x4b9d,0x6007},
{0x4b9e,0x4b9e,0x6049},
{0x4b9f,0x4b9f,0x6054},
{0x4ba0,0x4ba0,0x6067},
{0x4ba1,0x4ba1,0x60bb},
{0x4ba2,0x4ba2,0x60c4},
{0x4ba3,0x4ba3,0x60fd},
{0x4ba4,0x4ba4,0x610a},
{0x4ba5,0x4ba5,0x6116},
{0x4ba6,0x4ba6,0x612a},
{0x4ba7,0x4ba7,0x6136},
{0x4ba8,0x4ba8,0x615e},
{0x4ba9,0x4ba9,0x6164},
{0x4baa,0x4baa,0x617b},
{0x4bab,0x4bab,0x617d},
{0x4bac,0x4bac,0x617f},
{0x4bad,0x4bad,0x619d},
{0x4bae,0x4bae,0x61b8},
{0x4baf,0x4baf,0x61dc},
{0x4bb0,0x4bb0,0x61e2},
{0x4bb1,0x4bb1,0x61e5},
{0x4bb2,0x4bb2,0x61e8},
{0x4bb3,0x4bb3,0x6204},
{0x4bb4,0x4bb4,0x6207},
{0x4bb5,0x4bb5,0x6231},
{0x4bb6,0x4bb6,0x6239},
{0x4bb7,0x4bb7,0x623d},
{0x4bb8,0x4bb8,0x627a},
{0x4bb9,0x4bb9,0x6290},
{0x4bba,0x4bba,0x62a8},
{0x4bbb,0x4bbb,0x62da},
{0x4bbc,0x4bbc,0x62f4},
{0x4bbd,0x4bbd,0x6316},
{0x4bbe,0x4bbe,0x632a},
{0x4bbf,0x4bbf,0x6336},
{0x4bc0,0x4bc0,0x6346},
{0x4bc1,0x4bc1,0x634b},
{0x4bc2,0x4bc2,0x6353},
{0x4bc3,0x4bc3,0x6371},
{0x4bc4,0x4bc4,0x6375},
{0x4bc5,0x4bc5,0x637f},
{0x4bc6,0x4bc6,0x6382},
{0x4bc7,0x4bc7,0x638a},
{0x4bc8,0x4bc9,0x63ae},
{0x4bca,0x4bca,0x63ea},
{0x4bcb,0x4bcc,0x63f8},
{0x4bcd,0x4bcd,0x6412},
{0x4bce,0x4bce,0x6418},
{0x4bcf,0x4bcf,0x6420},
{0x4bd0,0x4bd0,0x6424},
{0x4bd1,0x4bd1,0x642a},
{0x4bd2,0x4bd2,0x6435},
{0x4bd3,0x4bd3,0x643d},
{0x4bd4,0x4bd4,0x643f},
{0x4bd5,0x4bd5,0x6452},
{0x4bd6,0x4bd6,0x645f},
{0x4bd7,0x4bd7,0x6474},
{0x4bd8,0x4bd8,0x6490},
{0x4bd9,0x4bda,0x6498},
{0x4bdb,0x4bdb,0x64ac},
{0x4bdc,0x4bdc,0x64b3},
{0x4bdd,0x4bdd,0x64ed},
{0x4bde,0x4bde,0x64f0},
{0x4bdf,0x4bdf,0x651b},
{0x4be0,0x4be0,0x651f},
{0x4be1,0x4be1,0x652e},
{0x4be2,0x4be2,0x6549},
{0x4be3,0x4be3,0x6560},
{0x4be4,0x4be4,0x6592},
{0x4be5,0x4be5,0x6595},
{0x4be6,0x4be6,0x65b4},
{0x4be7,0x4be7,0x65be},
{0x4be8,0x4be8,0x65c8},
{0x4be9,0x4be9,0x65ce},
{0x4bea,0x4bea,0x65d0},
{0x4beb,0x4beb,0x65df},
{0x4bec,0x4bec,0x667e},
{0x4bed,0x4bee,0x668b},
{0x4bef,0x4bef,0x66b3},
{0x4bf0,0x4bf0,0x66c0},
{0x4bf1,0x4bf1,0x66cf},
{0x4bf2,0x4bf2,0x6725},
{0x4bf3,0x4bf3,0x6735},
{0x4bf4,0x4bf4,0x6755},
{0x4bf5,0x4bf5,0x6780},
{0x4bf6,0x4bf6,0x678f},
{0x4bf7,0x4bf7,0x6791},
{0x4bf8,0x4bf8,0x67a4},
{0x4bf9,0x4bf9,0x67b1},
{0x4bfa,0x4bfa,0x67b5},
{0x4bfb,0x4bfb,0x67be},
{0x4bfc,0x4bfc,0x6828},
{0x4bfd,0x4bfd,0x6886},
{0x4bfe,0x4bfe,0x68eb},
{0x4bff,0x4bff,0x68f5},
{0x4c00,0x4c00,0x6917},
{0x4c01,0x4c01,0x6933},
{0x4c02,0x4c02,0x6938},
{0x4c03,0x4c03,0x695b},
{0x4c04,0x4c04,0x6965},
{0x4c05,0x4c05,0x69a8},
{0x4c06,0x4c06,0x69ab},
{0x4c07,0x4c07,0x69af},
{0x4c08,0x4c08,0x69e5},
{0x4c09,0x4c09,0x69f1},
{0x4c0a,0x4c0a,0x6a4a},
{0x4c0b,0x4c0b,0x6a55},
{0x4c0c,0x4c0c,0x6a67},
{0x4c0d,0x4c0d,0x6a71},
{0x4c0e,0x4c0e,0x6aaf},
{0x4c0f,0x4c10,0x6ac8},
{0x4c11,0x4c11,0x6b03},
{0x4c12,0x4c12,0x6b3b},
{0x4c13,0x4c13,0x6b3f},
{0x4c14,0x4c15,0x6b7d},
{0x4c16,0x4c16,0x6bb0},
{0x4c17,0x4c17,0x6bf7},
{0x4c18,0x4c18,0x6bf9},
{0x4c19,0x4c19,0x6c04},
{0x4c1a,0x4c1a,0x6c09},
{0x4c1b,0x4c1b,0x6c0d},
{0x4c1c,0x4c1c,0x6c2c},
{0x4c1d,0x4c1d,0x6c4a},
{0x4c1e,0x4c1e,0x6c52},
{0x4c1f,0x4c1f,0x6c54},
{0x4c20,0x4c20,0x6c79},
{0x4c21,0x4c21,0x6cac},
{0x4c22,0x4c22,0x6cb4},
{0x4c23,0x4c23,0x6cd2},
{0x4c24,0x4c24,0x6d61},
{0x4c25,0x4c25,0x6d7c},
{0x4c26,0x4c26,0x6db9},
{0x4c27,0x4c27,0x6df0},
{0x4c28,0x4c28,0x6e45},
{0x4c29,0x4c29,0x6e73},
{0x4c2a,0x4c2a,0x6e7b},
{0x4c2b,0x4c2b,0x6e7d},
{0x4c2c,0x4c2c,0x6e89},
{0x4c2d,0x4c2d,0x6ebc},
{0x4c2e,0x4c2f,0x6eda},
{0x4c30,0x4c30,0x6f26},
{0x4c31,0x4c31,0x6f29},
{0x4c32,0x4c32,0x6f30},
{0x4c33,0x4c33,0x6f87},
{0x4c34,0x4c34,0x6f9d},
{0x4c35,0x4c35,0x6fae},
{0x4c36,0x4c36,0x6fb7},
{0x4c37,0x4c37,0x700d},
{0x4c38,0x4c38,0x7020},
{0x4c39,0x4c39,0x7049},
{0x4c3a,0x4c3a,0x7098},
{0x4c3b,0x4c3b,0x70b0},
{0x4c3c,0x4c3d,0x70d5},
{0x4c3e,0x4c3e,0x7145},
{0x4c3f,0x4c3f,0x71b2},
{0x4c40,0x4c40,0x71f4},
{0x4c41,0x4c41,0x7217},
{0x4c42,0x4c42,0x721f},
{0x4c43,0x4c43,0x7243},
{0x4c44,0x4c45,0x724f},
{0x4c46,0x4c46,0x725a},
{0x4c47,0x4c47,0x7260},
{0x4c48,0x4c48,0x7268},
{0x4c49,0x4c49,0x7277},
{0x4c4a,0x4c4a,0x7284},
{0x4c4b,0x4c4b,0x72c9},
{0x4c4c,0x4c4c,0x72e5},
{0x4c4d,0x4c4d,0x72f4},
{0x4c4e,0x4c4e,0x7302},
{0x4c4f,0x4c4f,0x730b},
{0x4c50,0x4c50,0x731e},
{0x4c51,0x4c51,0x7322},
{0x4c52,0x4c53,0x733a},
{0x4c54,0x4c54,0x734d},
{0x4c55,0x4c55,0x7358},
{0x4c56,0x4c56,0x7367},
{0x4c57,0x4c57,0x7472},
{0x4c58,0x4c58,0x74af},
{0x4c59,0x4c59,0x74df},
{0x4c5a,0x4c5a,0x74e4},
{0x4c5b,0x4c5b,0x74f4},
{0x4c5c,0x4c5c,0x74fb},
{0x4c5d,0x4c5d,0x7516},
{0x4c5e,0x4c5e,0x7521},
{0x4c5f,0x4c5f,0x753f},
{0x4c60,0x4c60,0x755e},
{0x4c61,0x4c61,0x7599},
{0x4c62,0x4c62,0x75a4},
{0x4c63,0x4c63,0x75c1},
{0x4c64,0x4c64,0x75c4},
{0x4c65,0x4c65,0x75cc},
{0x4c66,0x4c66,0x75d7},
{0x4c67,0x4c67,0x75dc},
{0x4c68,0x4c68,0x75e1},
{0x4c69,0x4c69,0x75ef},
{0x4c6a,0x4c6a,0x7604},
{0x4c6b,0x4c6b,0x760c},
{0x4c6c,0x4c6c,0x761d},
{0x4c6d,0x4c6d,0x7632},
{0x4c6e,0x4c6e,0x7638},
{0x4c6f,0x4c6f,0x7645},
{0x4c70,0x4c70,0x764a},
{0x4c71,0x4c71,0x765f},
{0x4c72,0x4c72,0x76ad},
{0x4c73,0x4c73,0x76bd},
{0x4c74,0x4c74,0x76d9},
{0x4c75,0x4c75,0x76eb},
{0x4c76,0x4c76,0x76f0},
{0x4c77,0x4c77,0x76f9},
{0x4c78,0x4c78,0x7700},
{0x4c79,0x4c79,0x770e},
{0x4c7a,0x4c7a,0x7722},
{0x4c7b,0x4c7b,0x7728},
{0x4c7c,0x4c7c,0x772f},
{0x4c7d,0x4c7d,0x7739},
{0x4c7e,0x4c7e,0x773e},
{0x4c7f,0x4c7f,0x7745},
{0x4c80,0x4c80,0x774a},
{0x4c81,0x4c81,0x774f},
{0x4c82,0x4c82,0x775e},
{0x4c83,0x4c83,0x7764},
{0x4c84,0x4c84,0x7767},
{0x4c85,0x4c85,0x776c},
{0x4c86,0x4c86,0x7784},
{0x4c87,0x4c88,0x778c},
{0x4c89,0x4c89,0x7796},
{0x4c8a,0x4c8a,0x77a7},
{0x4c8b,0x4c8b,0x77af},
{0x4c8c,0x4c8c,0x77b7},
{0x4c8d,0x4c8d,0x77be},
{0x4c8e,0x4c8e,0x77c9},
{0x4c8f,0x4c8f,0x77d1},
{0x4c90,0x4c90,0x77d9},
{0x4c91,0x4c91,0x77f1},
{0x4c92,0x4c92,0x7837},
{0x4c93,0x4c93,0x785c},
{0x4c94,0x4c94,0x787e},
{0x4c95,0x4c95,0x7898},
{0x4c96,0x4c96,0x78a1},
{0x4c97,0x4c97,0x78b1},
{0x4c98,0x4c98,0x78b3},
{0x4c99,0x4c99,0x78c9},
{0x4c9a,0x4c9a,0x78d3},
{0x4c9b,0x4c9b,0x790c},
{0x4c9c,0x4c9c,0x791f},
{0x4c9d,0x4c9e,0x7927},
{0x4c9f,0x4c9f,0x793f},
{0x4ca0,0x4ca0,0x7942},
{0x4ca1,0x4ca1,0x7954},
{0x4ca2,0x4ca2,0x796b},
{0x4ca3,0x4ca3,0x797c},
{0x4ca4,0x4ca4,0x79ab},
{0x4ca5,0x4ca5,0x79c4},
{0x4ca6,0x4ca6,0x79ea},
{0x4ca7,0x4ca7,0x7a02},
{0x4ca8,0x4ca8,0x7a0c},
{0x4ca9,0x4ca9,0x7a30},
{0x4caa,0x4caa,0x7a3a},
{0x4cab,0x4cab,0x7a44},
{0x4cac,0x4cac,0x7a80},
{0x4cad,0x4cad,0x7a86},
{0x4cae,0x4cae,0x7a94},
{0x4caf,0x4caf,0x7ab5},
{0x4cb0,0x4cb0,0x7abd},
{0x4cb1,0x4cb1,0x7afe},
{0x4cb2,0x4cb2,0x7b2b},
{0x4cb3,0x4cb3,0x7b77},
{0x4cb4,0x4cb4,0x7ba0},
{0x4cb5,0x4cb5,0x7bac},
{0x4cb6,0x4cb6,0x7bb0},
{0x4cb7,0x4cb7,0x7be8},
{0x4cb8,0x4cb8,0x7bf2},
{0x4cb9,0x4cb9,0x7bf8},
{0x4cba,0x4cba,0x7bfc},
{0x4cbb,0x4cbb,0x7bfe},
{0x4cbc,0x4cbc,0x7c09},
{0x4cbd,0x4cbd,0x7c28},
{0x4cbe,0x4cbe,0x7c2f},
{0x4cbf,0x4cbf,0x7c42},
{0x4cc0,0x4cc1,0x7c52},
{0x4cc2,0x4cc4,0x7c5b},
{0x4cc5,0x4cc5,0x7c72},
{0x4cc6,0x4cc6,0x7c7d},
{0x4cc7,0x4cc7,0x7c87},
{0x4cc8,0x4cc8,0x7c9e},
{0x4cc9,0x4cc9,0x7cba},
{0x4cca,0x4cca,0x7cc7},
{0x4ccb,0x4ccb,0x7cd3},
{0x4ccc,0x4ccc,0x7cda},
{0x4ccd,0x4ccd,0x7d3c},
{0x4cce,0x4cce,0x7d4d},
{0x4ccf,0x4ccf,0x7d82},
{0x4cd0,0x4cd0,0x7d85},
{0x4cd1,0x4cd1,0x7d8d},
{0x4cd2,0x4cd2,0x7d91},
{0x4cd3,0x4cd3,0x7d9e},
{0x4cd4,0x4cd4,0x7db3},
{0x4cd5,0x4cd5,0x7db9},
{0x4cd6,0x4cd6,0x7dd0},
{0x4cd7,0x4cd7,0x7de5},
{0x4cd8,0x4cd9,0x7df5},
{0x4cda,0x4cda,0x7e2f},
{0x4cdb,0x4cdb,0x7e36},
{0x4cdc,0x4cdc,0x7e44},
{0x4cdd,0x4cdd,0x7e6f},
{0x4cde,0x4cde,0x7e78},
{0x4cdf,0x4cdf,0x7e81},
{0x4ce0,0x4ce0,0x7f3d},
{0x4ce1,0x4ce1,0x7f5b},
{0x4ce2,0x4ce2,0x7f5d},
{0x4ce3,0x4ce3,0x7f65},
{0x4ce4,0x4ce4,0x7f71},
{0x4ce5,0x4ce6,0x7f7f},
{0x4ce7,0x4ce7,0x7f8b},
{0x4ce8,0x4ce8,0x7fa2},
{0x4ce9,0x4ceb,0x7ffd},
{0x4cec,0x4cec,0x8007},
{0x4ced,0x4ced,0x800d},
{0x4cee,0x4cee,0x801e},
{0x4cef,0x4cef,0x8039},
{0x4cf0,0x4cf0,0x8088},
{0x4cf1,0x4cf1,0x808e},
{0x4cf2,0x4cf2,0x80cf},
{0x4cf3,0x4cf3,0x80d4},
{0x4cf4,0x4cf4,0x80ed},
{0x4cf5,0x4cf5,0x80f0},
{0x4cf6,0x4cf6,0x80f7},
{0x4cf7,0x4cf7,0x80fa},
{0x4cf8,0x4cf8,0x80fe},
{0x4cf9,0x4cf9,0x8103},
{0x4cfa,0x4cfa,0x8117},
{0x4cfb,0x4cfb,0x8130},
{0x4cfc,0x4cfc,0x8157},
{0x4cfd,0x4cfd,0x816f},
{0x4cfe,0x4cfe,0x8173},
{0x4cff,0x4cff,0x818b},
{0x4d00,0x4d00,0x8190},
{0x4d01,0x4d01,0x819b},
{0x4d02,0x4d02,0x819e},
{0x4d03,0x4d03,0x81cb},
{0x4d04,0x4d04,0x81d5},
{0x4d05,0x4d06,0x81dd},
{0x4d07,0x4d07,0x81e1},
{0x4d08,0x4d08,0x81ef},
{0x4d09,0x4d09,0x81f6},
{0x4d0a,0x4d0a,0x8200},
{0x4d0b,0x4d0b,0x820b},
{0x4d0c,0x4d0d,0x8213},
{0x4d0e,0x4d0e,0x821a},
{0x4d0f,0x4d0f,0x823a},
{0x4d10,0x4d10,0x8244},
{0x4d11,0x4d11,0x826d},
{0x4d12,0x4d12,0x8284},
{0x4d13,0x4d13,0x8289},
{0x4d14,0x4d14,0x8291},
{0x4d15,0x4d15,0x82aa},
{0x4d16,0x4d16,0x82b0},
{0x4d17,0x4d17,0x82d0},
{0x4d18,0x4d18,0x82ea},
{0x4d19,0x4d19,0x82ef},
{0x4d1a,0x4d1a,0x82f6},
{0x4d1b,0x4d1b,0x8356},
{0x4d1c,0x4d1c,0x8378},
{0x4d1d,0x4d1d,0x83f8},
{0x4d1e,0x4d1e,0x83fc},
{0x4d1f,0x4d1f,0x8458},
{0x4d20,0x4d20,0x8493},
{0x4d21,0x4d21,0x84b1},
{0x4d22,0x4d22,0x84bd},
{0x4d23,0x4d23,0x84fb},
{0x4d24,0x4d24,0x8546},
{0x4d25,0x4d25,0x8556},
{0x4d26,0x4d26,0x855d},
{0x4d27,0x4d27,0x8585},
{0x4d28,0x4d28,0x8598},
{0x4d29,0x4d29,0x8642},
{0x4d2a,0x4d2a,0x8646},
{0x4d2b,0x4d2b,0x86c0},
{0x4d2c,0x4d2c,0x8714},
{0x4d2d,0x4d2d,0x8722},
{0x4d2e,0x4d2e,0x872e},
{0x4d2f,0x4d2f,0x8739},
{0x4d30,0x4d30,0x875d},
{0x4d31,0x4d31,0x877b},
{0x4d32,0x4d32,0x878c},
{0x4d33,0x4d33,0x878e},
{0x4d34,0x4d35,0x8797},
{0x4d36,0x4d36,0x879e},
{0x4d37,0x4d37,0x87a3},
{0x4d38,0x4d38,0x87ae},
{0x4d39,0x4d39,0x87bf},
{0x4d3a,0x4d3a,0x87c9},
{0x4d3b,0x4d3b,0x87da},
{0x4d3c,0x4d3c,0x8818},
{0x4d3d,0x4d3d,0x881b},
{0x4d3e,0x4d3e,0x882d},
{0x4d3f,0x4d3f,0x883a},
{0x4d40,0x4d40,0x8845},
{0x4d41,0x4d41,0x884b},
{0x4d42,0x4d42,0x884e},
{0x4d43,0x4d43,0x8855},
{0x4d44,0x4d44,0x885a},
{0x4d45,0x4d45,0x886e},
{0x4d46,0x4d48,0x889a},
{0x4d49,0x4d49,0x88cd},
{0x4d4a,0x4d4a,0x88e0},
{0x4d4b,0x4d4b,0x88ef},
{0x4d4c,0x4d4d,0x890e},
{0x4d4e,0x4d4e,0x8926},
{0x4d4f,0x4d4f,0x8935},
{0x4d50,0x4d50,0x895a},
{0x4d51,0x4d51,0x895c},
{0x4d52,0x4d52,0x896b},
{0x4d53,0x4d53,0x8970},
{0x4d54,0x4d54,0x897c},
{0x4d55,0x4d55,0x89a5},
{0x4d56,0x4d56,0x89b5},
{0x4d57,0x4d57,0x89bc},
{0x4d58,0x4d58,0x89d5},
{0x4d59,0x4d59,0x8a49},
{0x4d5a,0x4d5a,0x8a57},
{0x4d5b,0x4d5b,0x8a67},
{0x4d5c,0x4d5c,0x8a7e},
{0x4d5d,0x4d5d,0x8a86},
{0x4d5e,0x4d5e,0x8a96},
{0x4d5f,0x4d5f,0x8ab6},
{0x4d60,0x4d60,0x8ac9},
{0x4d61,0x4d61,0x8ad1},
{0x4d62,0x4d62,0x8add},
{0x4d63,0x4d63,0x8aec},
{0x4d64,0x4d64,0x8af5},
{0x4d65,0x4d65,0x8b06},
{0x4d66,0x4d66,0x8b0f},
{0x4d67,0x4d67,0x8b11},
{0x4d68,0x4d68,0x8b45},
{0x4d69,0x4d69,0x8b52},
{0x4d6a,0x4d6a,0x8b6d},
{0x4d6b,0x4d6b,0x8b78},
{0x4d6c,0x4d6c,0x8b7c},
{0x4d6d,0x4d6d,0x8b7e},
{0x4d6e,0x4d6e,0x8b85},
{0x4d6f,0x4d6f,0x8b9f},
{0x4d70,0x4d70,0x8c4b},
{0x4d71,0x4d71,0x8c53},
{0x4d72,0x4d72,0x8c7b},
{0x4d73,0x4d73,0x8cba},
{0x4d74,0x4d74,0x8cc5},
{0x4d75,0x4d75,0x8cc9},
{0x4d76,0x4d76,0x8cd2},
{0x4d77,0x4d77,0x8cec},
{0x4d78,0x4d78,0x8cf5},
{0x4d79,0x4d79,0x8cf7},
{0x4d7a,0x4d7a,0x8d01},
{0x4d7b,0x4d7b,0x8d03},
{0x4d7c,0x4d7c,0x8d17},
{0x4d7d,0x4d7d,0x8d1c},
{0x4d7e,0x4d7e,0x8d6e},
{0x4d7f,0x4d7f,0x8d91},
{0x4d80,0x4d80,0x8d9f},
{0x4d81,0x4d81,0x8dab},
{0x4d82,0x4d82,0x8db2},
{0x4d83,0x4d83,0x8dd5},
{0x4d84,0x4d84,0x8de7},
{0x4d85,0x4d86,0x8df1},
{0x4d87,0x4d87,0x8df4},
{0x4d88,0x4d88,0x8e01},
{0x4d89,0x4d89,0x8e0b},
{0x4d8a,0x4d8a,0x8e26},
{0x4d8b,0x4d8b,0x8e31},
{0x4d8c,0x4d8d,0x8e40},
{0x4d8e,0x4d8e,0x8e4d},
{0x4d8f,0x4d8f,0x8e4f},
{0x4d90,0x4d90,0x8e5c},
{0x4d91,0x4d91,0x8e61},
{0x4d92,0x4d92,0x8e69},
{0x4d93,0x4d93,0x8e71},
{0x4d94,0x4d94,0x8e75},
{0x4d95,0x4d95,0x8e77},
{0x4d96,0x4d96,0x8e89},
{0x4d97,0x4d97,0x8e90},
{0x4d98,0x4d98,0x8e95},
{0x4d99,0x4d99,0x8e9a},
{0x4d9a,0x4d9a,0x8ea7},
{0x4d9b,0x4d9b,0x8ea9},
{0x4d9c,0x4d9c,0x8ead},
{0x4d9d,0x4d9d,0x8ee8},
{0x4d9e,0x4d9e,0x8ef0},
{0x4d9f,0x4d9f,0x8f07},
{0x4da0,0x4da0,0x8f18},
{0x4da1,0x4da1,0x8f25},
{0x4da2,0x4da2,0x8f27},
{0x4da3,0x4da3,0x8f2c},
{0x4da4,0x4da4,0x8f35},
{0x4da5,0x4da5,0x8f3a},
{0x4da6,0x4da6,0x8f43},
{0x4da7,0x4da7,0x8f47},
{0x4da8,0x4da8,0x8f51},
{0x4da9,0x4da9,0x8f55},
{0x4daa,0x4dac,0x8fa0},
{0x4dad,0x4dad,0x8fa5},
{0x4dae,0x4dae,0x9004},
{0x4daf,0x4daf,0x901b},
{0x4db0,0x4db0,0x902f},
{0x4db1,0x4db1,0x904c},
{0x4db2,0x4db2,0x905b},
{0x4db3,0x4db3,0x9070},
{0x4db4,0x4db4,0x9074},
{0x4db5,0x4db5,0x9079},
{0x4db6,0x4db6,0x908b},
{0x4db7,0x4db7,0x9098},
{0x4db8,0x4db8,0x90a0},
{0x4db9,0x4db9,0x90b2},
{0x4dba,0x4dba,0x90bd},
{0x4dbb,0x4dbb,0x90c9},
{0x4dbc,0x4dbc,0x90f0},
{0x4dbd,0x4dbd,0x9105},
{0x4dbe,0x4dbe,0x9125},
{0x4dbf,0x4dbf,0x915a},
{0x4dc0,0x4dc0,0x9167},
{0x4dc1,0x4dc1,0x917a},
{0x4dc2,0x4dc2,0x918a},
{0x4dc3,0x4dc3,0x9191},
{0x4dc4,0x4dc4,0x9195},
{0x4dc5,0x4dc5,0x91b0},
{0x4dc6,0x4dc6,0x91bb},
{0x4dc7,0x4dc7,0x91bd},
{0x4dc8,0x4dc8,0x91c2},
{0x4dc9,0x4dc9,0x91c5},
{0x4dca,0x4dca,0x9200},
{0x4dcb,0x4dcb,0x9209},
{0x4dcc,0x4dcc,0x9223},
{0x4dcd,0x4dcd,0x9276},
{0x4dce,0x4dce,0x928e},
{0x4dcf,0x4dcf,0x92af},
{0x4dd0,0x4dd1,0x92bb},
{0x4dd2,0x4dd2,0x92c1},
{0x4dd3,0x4dd3,0x92c3},
{0x4dd4,0x4dd4,0x92c5},
{0x4dd5,0x4dd5,0x92c8},
{0x4dd6,0x4dd6,0x9314},
{0x4dd7,0x4dd7,0x9333},
{0x4dd8,0x4dd8,0x9336},
{0x4dd9,0x4dd9,0x9358},
{0x4dda,0x4dda,0x937f},
{0x4ddb,0x4ddb,0x9382},
{0x4ddc,0x4ddc,0x938a},
{0x4ddd,0x4ddd,0x93bb},
{0x4dde,0x4dde,0x93cc},
{0x4ddf,0x4ddf,0x93e6},
{0x4de0,0x4de0,0x93f9},
{0x4de1,0x4de1,0x9402},
{0x4de2,0x4de3,0x940d},
{0x4de4,0x4de4,0x942e},
{0x4de5,0x4de5,0x944c},
{0x4de6,0x4de6,0x9588},
{0x4de7,0x4de7,0x95a1},
{0x4de8,0x4de8,0x95bf},
{0x4de9,0x4de9,0x95c6},
{0x4dea,0x4dea,0x95c9},
{0x4deb,0x4dec,0x95d1},
{0x4ded,0x4ded,0x95e0},
{0x4dee,0x4dee,0x95e4},
{0x4def,0x4def,0x95e6},
{0x4df0,0x4df0,0x9624},
{0x4df1,0x4df1,0x9631},
{0x4df2,0x4df2,0x9638},
{0x4df3,0x4df3,0x963d},
{0x4df4,0x4df4,0x9654},
{0x4df5,0x4df5,0x9674},
{0x4df6,0x4df6,0x967b},
{0x4df7,0x4df7,0x967f},
{0x4df8,0x4df8,0x9681},
{0x4df9,0x4df9,0x9683},
{0x4dfa,0x4dfa,0x9689},
{0x4dfb,0x4dfb,0x9696},
{0x4dfc,0x4dfc,0x96ae},
{0x4dfd,0x4dfd,0x9703},
{0x4dfe,0x4dfe,0x971b},
{0x4dff,0x4e00,0x9721},
{0x4e01,0x4e01,0x9728},
{0x4e02,0x4e02,0x9731},
{0x4e03,0x4e03,0x9767},
{0x4e04,0x4e04,0x9776},
{0x4e05,0x4e05,0x977d},
{0x4e06,0x4e06,0x977f},
{0x4e07,0x4e07,0x9799},
{0x4e08,0x4e08,0x979f},
{0x4e09,0x4e09,0x97ac},
{0x4e0a,0x4e0a,0x97b9},
{0x4e0b,0x4e0b,0x97cd},
{0x4e0c,0x4e0c,0x97e0},
{0x4e0d,0x4e0d,0x97ef},
{0x4e0e,0x4e0e,0x9807},
{0x4e0f,0x4e0f,0x9826},
{0x4e10,0x4e10,0x982e},
{0x4e11,0x4e12,0x9862},
{0x4e13,0x4e13,0x98b4},
{0x4e14,0x4e14,0x98c5},
{0x4e15,0x4e15,0x9902},
{0x4e16,0x4e16,0x9911},
{0x4e17,0x4e17,0x9915},
{0x4e18,0x4e18,0x9935},
{0x4e19,0x4e19,0x9948},
{0x4e1a,0x4e1a,0x9954},
{0x4e1b,0x4e1b,0x995e},
{0x4e1c,0x4e1c,0x99e1},
{0x4e1d,0x4e1d,0x9a0c},
{0x4e1e,0x4e1e,0x9a10},
{0x4e1f,0x4e1f,0x9a23},
{0x4e20,0x4e20,0x9a41},
{0x4e21,0x4e21,0x9a51},
{0x4e22,0x4e23,0x9abd},
{0x4e24,0x4e24,0x9b01},
{0x4e25,0x4e25,0x9b09},
{0x4e26,0x4e26,0x9b0b},
{0x4e27,0x4e28,0x9b0d},
{0x4e29,0x4e29,0x9b19},
{0x4e2a,0x4e2a,0x9b35},
{0x4e2b,0x4e2b,0x9b48},
{0x4e2c,0x4e2c,0x9b55},
{0x4e2d,0x4e2d,0x9b68},
{0x4e2e,0x4e2e,0x9b80},
{0x4e2f,0x4e2f,0x9b86},
{0x4e30,0x4e30,0x9b90},
{0x4e31,0x4e31,0x9b9d},
{0x4e32,0x4e32,0x9bb0},
{0x4e33,0x4e33,0x9bbf},
{0x4e34,0x4e34,0x9bc8},
{0x4e35,0x4e35,0x9bff},
{0x4e36,0x4e36,0x9c02},
{0x4e37,0x4e37,0x9c1c},
{0x4e38,0x4e38,0x9c35},
{0x4e39,0x4e39,0x9c44},
{0x4e3a,0x4e3a,0x9c56},
{0x4e3b,0x4e3b,0x9c61},
{0x4e3c,0x4e3c,0x9c68},
{0x4e3d,0x4e3d,0x9d30},
{0x4e3e,0x4e3e,0x9d3d},
{0x4e3f,0x4e3f,0x9d6a},
{0x4e40,0x4e40,0x9d7b},
{0x4e41,0x4e41,0x9de5},
{0x4e42,0x4e42,0x9de9},
{0x4e43,0x4e43,0x9df3},
{0x4e44,0x4e44,0x9e7a},
{0x4e45,0x4e47,0x9e82},
{0x4e48,0x4e48,0x9eb0},
{0x4e49,0x4e49,0x9ee4},
{0x4e4a,0x4e4a,0x9ef0},
{0x4e4b,0x4e4b,0x9ef2},
{0x4e4c,0x4e4c,0x9f09},
{0x4e4d,0x4e4d,0x9f0f},
{0x4e4e,0x4e4e,0x9f14},
{0x4e4f,0x4e4f,0x9f1b},
{0x4e50,0x4e50,0x9f22},
{0x4e51,0x4e51,0x9f26},
{0x4e52,0x4e53,0x9f2a},
{0x4e54,0x4e54,0x9f34},
{0x4e55,0x4e55,0x9f5a},
{0x4e56,0x4e56,0x9f6f},
{0x4e57,0x4e57,0x9f9e},
{0x4e58,0x4e58,0x9fa5},
{0x4e63,0x4e63,0x35de},
{0x4e64,0x4e64,0xfffd},
{0x4e65,0x4e65,0xfffd},
{0x4e66,0x4e66,0xfffd},
{0x4e67,0x4e67,0xfffd},
{0x4e69,0x4e69,0x4e0e},
{0x4e6a,0x4e6a,0x4e26},
{0x4e6c,0x4e6c,0x4f73},
{0x4e6d,0x4e6d,0x5056},
{0x4e6e,0x4e6e,0x50ed},
{0x4e6f,0x4e6f,0x516b},
{0x4e71,0x4e71,0x51de},
{0x4e72,0x4e72,0x51fd},
{0x4e74,0x4e74,0x5264},
{0x4e75,0x4e76,0x5271},
{0x4e77,0x4e77,0x533f},
{0x4e78,0x4e78,0x5354},
{0x4e79,0x4e79,0x5440},
{0x4e7b,0x4e7b,0x559c},
{0x4e7c,0x4e7c,0x55ab},
{0x4e7d,0x4e7d,0x5609},
{0x4e7e,0x4e7e,0x56ae},
{0x4e7f,0x4e7f,0x56ae},
{0x4e80,0x4e80,0x56c0},
{0x4e81,0x4e81,0x56c3},
{0x4e82,0x4e82,0x56ce},
{0x4e83,0x4e83,0x56ee},
{0x4e84,0x4e84,0x57d6},
{0x4e85,0x4e85,0x583d},
{0x4e86,0x4e86,0x5859},
{0x4e87,0x4e87,0x594f},
{0x4e88,0x4e88,0x5951},
{0x4e89,0x4e89,0x5960},
{0x4e8a,0x4e8a,0x5962},
{0x4e8b,0x4e8b,0x5ada},
{0x4e8c,0x4e8c,0x5b5a},
{0x4e8d,0x4e8d,0x5b73},
{0x4e8e,0x4e8e,0x5b7c},
{0x4e8f,0x4e8f,0x5bb3},
{0x4e91,0x4e91,0x5e43},
{0x4e92,0x4e92,0x5ea7},
{0x4e93,0x4e93,0x5ee3},
{0x4e94,0x4e94,0x5f38},
{0x4e95,0x4e95,0x5fa1},
{0x4e96,0x4e96,0x5fdd},
{0x4e97,0x4e97,0x6162},
{0x4e98,0x4e98,0x61b2},
{0x4e99,0x4e99,0x61f8},
{0x4e9a,0x4e9a,0x39a4},
{0x4e9b,0x4e9b,0x39b8},
{0x4e9d,0x4e9d,0x64a5},
{0x4e9e,0x4e9e,0x64f6},
{0x4e9f,0x4e9f,0x3a5c},
{0x4ea0,0x4ea1,0x655d},
{0x4ea3,0x4ea3,0x6587},
{0x4ea4,0x4ea4,0x6589},
{0x4ea6,0x4ea6,0x658e},
{0x4ea7,0x4ea7,0x3ac4},
{0x4ea8,0x4ea8,0x3acb},
{0x4ea9,0x4ea9,0x65e1},
{0x4eaa,0x4eaa,0x6667},
{0x4eab,0x4eab,0x666e},
{0x4eac,0x4eac,0x66c1},
{0x4ead,0x4ead,0x66dc},
{0x4eae,0x4eae,0x3a85},
{0x4eaf,0x4eaf,0x6700},
{0x4eb0,0x4eb0,0x6761},
{0x4eb1,0x4eb1,0x67c4},
{0x4eb2,0x4eb2,0x6802},
{0x4eb3,0x4eb3,0x693d},
{0x4eb4,0x4eb4,0x695e},
{0x4eb5,0x4eb5,0x6a05},
{0x4eb6,0x4eb6,0x6a9c},
{0x4eb7,0x4eb7,0x3bf3},
{0x4eb8,0x4eb8,0x6af8},
{0x4eb9,0x4eb9,0x6bb1},
{0x4eba,0x4eba,0x6c38},
{0x4ebb,0x4ebb,0x6caa},
{0x4ebc,0x4ebc,0x6dbc},
{0x4ebd,0x4ebd,0x6de4},
{0x4ebe,0x4ebe,0x3d31},
{0x4ebf,0x4ebf,0x6e08},
{0x4ec0,0x4ec0,0x6e72},
{0x4ec1,0x4ec1,0x6ecb},
{0x4ec2,0x4ec2,0x6ed5},
{0x4ec3,0x4ec3,0x6edb},
{0x4ec4,0x4ec4,0x6f5b},
{0x4ec5,0x4ec5,0x6f5b},
{0x4ec6,0x4ec6,0x6f64},
{0x4ec7,0x4ec7,0x6f74},
{0x4ec9,0x4ec9,0x723e},
{0x4eca,0x4eca,0x3e40},
{0x4ecb,0x4ecb,0x38a1},
{0x4ecc,0x4ecc,0x73ca},
{0x4ecd,0x4ecd,0x74bd},
{0x4ece,0x4ece,0x755d},
{0x4ecf,0x4ecf,0x75bc},
{0x4ed0,0x4ed0,0x75d9},
{0x4ed1,0x4ed1,0x7608},
{0x4ed2,0x4ed2,0x7664},
{0x4ed3,0x4ed3,0x7669},
{0x4ed4,0x4ed4,0x76c8},
{0x4ed5,0x4ed5,0x7737},
{0x4ed6,0x4ed6,0x777e},
{0x4ed7,0x4ed7,0x79a7},
{0x4ed8,0x4ed8,0x79b1},
{0x4ed9,0x4ed9,0x7abe},
{0x4eda,0x4eda,0x7be0},
{0x4edb,0x4edb,0x7c50},
{0x4edc,0x4edc,0x7d09},
{0x4edd,0x4edd,0x7db2},
{0x4ede,0x4ede,0x7db2},
{0x4edf,0x4edf,0x7f3e},
{0x4ee0,0x4ee0,0x7fe1},
{0x4ee1,0x4ee1,0x8012},
{0x4ee2,0x4ee2,0x8077},
{0x4ee3,0x4ee3,0x80d6},
{0x4ee4,0x4ee4,0x8109},
{0x4ee5,0x4ee5,0x8129},
{0x4ee6,0x4ee6,0x820c},
{0x4ee7,0x4ee7,0x821b},
{0x4ee8,0x4ee8,0x8240},
{0x4ee9,0x4ee9,0x83df},
{0x4eea,0x4eea,0x83d4},
{0x4eeb,0x4eeb,0x840f},
{0x4eec,0x4eec,0x8449},
{0x4eed,0x4eed,0x84ea},
{0x4eef,0x4eef,0x8551},
{0x4ef0,0x4ef0,0x8563},
{0x4ef1,0x4ef1,0x85cf},
{0x4ef2,0x4ef2,0x864e},
{0x4ef3,0x4ef3,0x8662},
{0x4ef4,0x4ef4,0x868a},
{0x4ef5,0x4ef5,0x8842},
{0x4ef6,0x4ef6,0x8877},
{0x4ef7,0x4ef7,0x8941},
{0x4ef8,0x4ef8,0x8b56},
{0x4ef9,0x4ef9,0x8b56},
{0x4efa,0x4efa,0x8b5c},
{0x4efb,0x4efb,0x471f},
{0x4efd,0x4efd,0x8c6a},
{0x4efe,0x4efe,0x8c79},
{0x4eff,0x4eff,0x8cca},
{0x4f00,0x4f00,0x8d67},
{0x4f01,0x4f01,0x481e},
{0x4f02,0x4f02,0x8f03},
{0x4f03,0x4f03,0x8f44},
{0x4f04,0x4f04,0x8fc5},
{0x4f05,0x4f05,0x8fd4},
{0x4f06,0x4f06,0x8ff6},
{0x4f07,0x4f07,0x9077},
{0x4f08,0x4f08,0x9077},
{0x4f09,0x4f0a,0x9089},
{0x4f0b,0x4f0b,0x90a8},
{0x4f0c,0x4f0c,0x914d},
{0x4f0d,0x4f0d,0x92e9},
{0x4f0e,0x4f0e,0x9335},
{0x4f0f,0x4f0f,0x938b},
{0x4f10,0x4f10,0x943a},
{0x4f11,0x4f11,0x95cd},
{0x4f12,0x4f12,0x962a},
{0x4f13,0x4f13,0x9698},
{0x4f14,0x4f14,0x96a7},
{0x4f15,0x4f15,0x97ff},
{0x4f16,0x4f16,0x97ff},
{0x4f17,0x4f17,0x990a},
{0x4f18,0x4f18,0x9921},
{0x4f19,0x4f19,0x9957},
{0x4f1a,0x4f1a,0x9ba8},
{0x4f1b,0x4f1b,0x9bae},
{0x4f1c,0x4f1c,0x9bb9},
{0x4f1d,0x4f1d,0x9c08},
{0x4f1e,0x4f1e,0x9c24},
{0x4f1f,0x4f1f,0x9c3b},
{0x4f20,0x4f20,0x9c75},
{0x4f21,0x4f21,0x9ce6},
{0x4f22,0x4f22,0x9dc0},
{0x4f23,0x4f23,0x9dc2},
{0x4f24,0x4f24,0x9e78},
{0x4f25,0x4f25,0x98ef},
{0x4f27,0x4f27,0x7b08},
{0x4f28,0x4f28,0x646f},
{0x4f29,0x4f29,0x7c3e},
{0x4f2a,0x4f2a,0x7b75},
{0x4f2b,0x4f2b,0x7515},
{0x4f2c,0x4f2c,0x8a1d},
{0x4f2d,0x4f2d,0x6062},
{0x4f2e,0x4f2e,0x7078},
{0x4f2f,0x4f2f,0x5ed0},
{0x4f30,0x4f30,0x7c82},
{0x4f31,0x4f31,0x9699},
{0x4f32,0x4f32,0x8654},
{0x4f33,0x4f33,0x8171},
{0x4f34,0x4f34,0x9375},
{0x4f35,0x4f35,0x54ac},
{0x4f36,0x4f36,0x72e1},
{0x4f37,0x4f37,0x6897},
{0x4f38,0x4f38,0x9bab},
{0x4f39,0x4f39,0x53c9},
{0x4f3a,0x4f3a,0x6756},
{0x4f3b,0x4f3b,0x7526},
{0x4f3c,0x4f3c,0x7511},
{0x4f3d,0x4f3d,0x7ac8},
{0x4f3e,0x4f3e,0x633a},
{0x4f3f,0x4f3f,0x9041},
{0x4f40,0x4f40,0x99c1},
{0x4f41,0x4f41,0x65a7},
{0x4f42,0x4f42,0x91e1},
{0x4f43,0x4f43,0x97ad},
{0x4f44,0x4f44,0x8292},
{0x4f45,0x4f45,0x7c7e},
{0x4f46,0x4f46,0x723a},
{0x4f47,0x4f47,0x8805},
{0x4f48,0x4f48,0x8703},
{0x4f49,0x4f49,0x9b1d},
{0x4f4a,0x4f4a,0x9b2c},
{0x4f4b,0x4f4b,0x5002},
{0x4f4c,0x4f4c,0x51de},
{0x4f4d,0x4f4d,0x5307},
{0x4f4e,0x4f4e,0x5bec},
{0x4f4f,0x4f4f,0xf928},
{0x4f50,0x4f50,0x661e},
{0x4f51,0x4f51,0xf929},
{0x4f52,0x4f52,0x704a},
{0x4f53,0x4f53,0xfa15},
{0x4f54,0x4f54,0x5561},
{0x4f55,0x4f55,0x6c3a},
{0x4f56,0x4f56,0x7a60},
{0x4f58,0x4f58,0x8803},
{0x4f59,0x4f59,0x95ab},
{0x4f5a,0x4f5a,0x9755},
{0x4f5d,0x4f5e,0x384},
{0x4f5f,0x4f5f,0xba},
{0x4f60,0x4f60,0xaa},
{0x4f61,0x4f61,0x2116},
{0x4f62,0x4f62,0x110},
{0x4f63,0x4f63,0x126},
{0x4f64,0x4f64,0x132},
{0x4f65,0x4f65,0x13f},
{0x4f66,0x4f66,0x14a},
{0x4f67,0x4f67,0x166},
{0x4f68,0x4f68,0x133},
{0x4f69,0x4f69,0x138},
{0x4f6a,0x4f6a,0x140},
{0x4f6b,0x4f6b,0x149},
{0x4f6c,0x4f6c,0x167},
{0x4f6d,0x4f6d,0x10a},
{0x4f6e,0x4f6e,0x116},
{0x4f6f,0x4f6f,0x11e},
{0x4f70,0x4f70,0x122},
{0x4f71,0x4f71,0x120},
{0x4f72,0x4f72,0x130},
{0x4f73,0x4f73,0x12e},
{0x4f74,0x4f74,0x136},
{0x4f75,0x4f75,0x13d},
{0x4f76,0x4f76,0x13b},
{0x4f77,0x4f77,0x145},
{0x4f78,0x4f78,0x156},
{0x4f79,0x4f79,0x172},
{0x4f7a,0x4f7a,0x1d7},
{0x4f7b,0x4f7b,0x1db},
{0x4f7c,0x4f7c,0x1d9},
{0x4f7d,0x4f7d,0x1d5},
{0x4f7e,0x4f7e,0x174},
{0x4f7f,0x4f7f,0x176},
{0x4f80,0x4f80,0x10b},
{0x4f81,0x4f81,0x117},
{0x4f82,0x4f82,0x1f5},
{0x4f83,0x4f83,0x11f},
{0x4f84,0x4f84,0x121},
{0x4f85,0x4f85,0x12f},
{0x4f86,0x4f86,0x137},
{0x4f87,0x4f87,0x13e},
{0x4f88,0x4f88,0x13c},
{0x4f89,0x4f89,0x146},
{0x4f8a,0x4f8a,0x157},
{0x4f8b,0x4f8b,0x173},
{0x4f8c,0x4f8c,0x175},
{0x4f8d,0x4f8d,0x177},
{0x4f8e,0x4f8e,0x212e},
{0x4f8f,0x4f8f,0x2206},
{0x4f90,0x4f90,0x220f},
{0x4f91,0x4f92,0x2264},
{0x4f93,0x4f93,0x25ca},
{0x4f94,0x4f95,0x384},
{0x4f96,0x4f96,0xba},
{0x4f97,0x4f97,0xaa},
{0x4f98,0x4f98,0x2116},
{0x4f99,0x4f99,0x110},
{0x4f9a,0x4f9a,0x126},
{0x4f9b,0x4f9b,0x132},
{0x4f9c,0x4f9c,0x13f},
{0x4f9d,0x4f9d,0x14a},
{0x4f9e,0x4f9e,0x166},
{0x4f9f,0x4f9f,0x133},
{0x4fa0,0x4fa0,0x138},
{0x4fa1,0x4fa1,0x140},
{0x4fa2,0x4fa2,0x149},
{0x4fa3,0x4fa3,0x167},
{0x4fa4,0x4fa4,0x10a},
{0x4fa5,0x4fa5,0x116},
{0x4fa6,0x4fa6,0x11e},
{0x4fa7,0x4fa7,0x122},
{0x4fa8,0x4fa8,0x120},
{0x4fa9,0x4fa9,0x130},
{0x4faa,0x4faa,0x12e},
{0x4fab,0x4fab,0x136},
{0x4fac,0x4fac,0x13d},
{0x4fad,0x4fad,0x13b},
{0x4fae,0x4fae,0x145},
{0x4faf,0x4faf,0x156},
{0x4fb0,0x4fb0,0x172},
{0x4fb1,0x4fb1,0x1d7},
{0x4fb2,0x4fb2,0x1db},
{0x4fb3,0x4fb3,0x1d9},
{0x4fb4,0x4fb4,0x1d5},
{0x4fb5,0x4fb5,0x174},
{0x4fb6,0x4fb6,0x176},
{0x4fb7,0x4fb7,0x10b},
{0x4fb8,0x4fb8,0x117},
{0x4fb9,0x4fb9,0x1f5},
{0x4fba,0x4fba,0x11f},
{0x4fbb,0x4fbb,0x121},
{0x4fbc,0x4fbc,0x12f},
{0x4fbd,0x4fbd,0x137},
{0x4fbe,0x4fbe,0x13e},
{0x4fbf,0x4fbf,0x13c},
{0x4fc0,0x4fc0,0x146},
{0x4fc1,0x4fc1,0x157},
{0x4fc2,0x4fc2,0x173},
{0x4fc3,0x4fc3,0x175},
{0x4fc4,0x4fc4,0x177},
{0x4fc5,0x4fc5,0x212e},
{0x4fc6,0x4fc6,0x2206},
{0x4fc7,0x4fc7,0x220f},
{0x4fc8,0x4fc9,0x2264},
{0x4fca,0x4fca,0x25ca},
{0x4fcb,0x4fcb,0x386},
{0x4fcc,0x4fce,0x388},
{0x4fcf,0x4fcf,0x3aa},
{0x4fd0,0x4fd0,0x38c},
{0x4fd1,0x4fd1,0x38e},
{0x4fd2,0x4fd2,0x3ab},
{0x4fd3,0x4fd3,0x38f},
{0x4fd4,0x4fd7,0x3ac},
{0x4fd8,0x4fd8,0x3ca},
{0x4fd9,0x4fd9,0x390},
{0x4fda,0x4fdb,0x3cc},
{0x4fdc,0x4fdc,0x3cb},
{0x4fdd,0x4fdd,0x3b0},
{0x4fde,0x4fde,0x3ce},
{0x4fdf,0x4fe9,0x402},
{0x4fea,0x4feb,0x40e},
{0x4fec,0x4ff6,0x452},
{0x4ff7,0x4ff8,0x45e},
{0x4ff9,0x4ffa,0x30ce},
{0x4ffb,0x4ffb,0x30d2},
{0x4ffc,0x4ffc,0x30d5},
{0x4ffd,0x4ffd,0x30d8},
{0x4ffe,0x4ffe,0x30db},
{0x4fff,0x5003,0x30de},
{0x5004,0x5004,0x30e4},
{0x5005,0x5005,0x30e6},
{0x5006,0x500b,0x30e8},
{0x500c,0x5010,0x30ef},
{0x5011,0x5016,0x41},
{0x5017,0x5017,0x7d42},
{0x5018,0x5018,0x48},
{0x501e,0x501e,0x6bb5},
{0x501f,0x501f,0x30b4},
{0x5020,0x5020,0x30df},
{0x5021,0x5029,0x31},
{0x502a,0x502a,0x30},
{0x5035,0x503e,0x30},
{0x503f,0x5048,0x30},
{0x5049,0x5049,0x5e74},
{0x504a,0x504a,0x4e},
{0x504b,0x504b,0x5929},
{0x504c,0x504c,0x518d},
{0x504d,0x504d,0x65b0},
{0x504e,0x504e,0x6620},
{0x504f,0x504f,0x58f0},
{0x5050,0x5050,0x524d},
{0x5051,0x5051,0x5f8c},
{0x5052,0x5052,0x7d42},
{0x5053,0x5053,0x7acb},
{0x5054,0x5054,0x4ea4},
{0x5056,0x5056,0x5287},
{0x5057,0x5057,0x53f8},
{0x5058,0x5058,0x89e3},
{0x5059,0x5059,0x682a},
{0x505a,0x505a,0x6c17},
{0x505b,0x505b,0x4e8c},
{0x505c,0x505c,0x591a},
{0x505d,0x505d,0x6587},
{0x505e,0x505e,0x624b},
{0x5061,0x5061,0x53cc},
{0x5066,0x5066,0x30c7},
{0x5068,0x5068,0x2014},
{0x5069,0x5069,0x2014},
{0x506a,0x506a,0x2014},
{0x506b,0x506c,0x2491},
{0x51dd,0x51dd,0xfffd},
{0x51de,0x51de,0xfffd},
{0x51e1,0x51e2,0x384},
{0x51e3,0x51e3,0xba},
{0x51e4,0x51e4,0xaa},
{0x51e5,0x51e5,0x2116},
{0x51e6,0x51e6,0x110},
{0x51e7,0x51e7,0x126},
{0x51e8,0x51e8,0x132},
{0x51e9,0x51e9,0x13f},
{0x51ea,0x51ea,0x14a},
{0x51eb,0x51eb,0x166},
{0x51ec,0x51ec,0x133},
{0x51ed,0x51ed,0x138},
{0x51ee,0x51ee,0x140},
{0x51ef,0x51ef,0x149},
{0x51f0,0x51f0,0x167},
{0x51f1,0x51f1,0x10a},
{0x51f2,0x51f2,0x116},
{0x51f3,0x51f3,0x11e},
{0x51f4,0x51f4,0x122},
{0x51f5,0x51f5,0x120},
{0x51f6,0x51f6,0x130},
{0x51f7,0x51f7,0x12e},
{0x51f8,0x51f8,0x136},
{0x51f9,0x51f9,0x13d},
{0x51fa,0x51fa,0x13b},
{0x51fb,0x51fb,0x145},
{0x51fc,0x51fc,0x156},
{0x51fd,0x51fd,0x172},
{0x51fe,0x51fe,0x1d7},
{0x51ff,0x51ff,0x1db},
{0x5200,0x5200,0x1d9},
{0x5201,0x5201,0x1d5},
{0x5202,0x5202,0x174},
{0x5203,0x5203,0x176},
{0x5204,0x5204,0x10b},
{0x5205,0x5205,0x117},
{0x5206,0x5206,0x1f5},
{0x5207,0x5207,0x11f},
{0x5208,0x5208,0x121},
{0x5209,0x5209,0x12f},
{0x520a,0x520a,0x137},
{0x520b,0x520b,0x13e},
{0x520c,0x520c,0x13c},
{0x520d,0x520d,0x146},
{0x520e,0x520e,0x157},
{0x520f,0x520f,0x173},
{0x5210,0x5210,0x175},
{0x5211,0x5211,0x177},
{0x5212,0x5212,0x212e},
{0x5213,0x5213,0x2206},
{0x5214,0x5214,0x220f},
{0x5215,0x5216,0x2264},
{0x5217,0x5217,0x25ca},
{0x5218,0x5219,0x384},
{0x521a,0x521a,0xba},
{0x521b,0x521b,0xaa},
{0x521c,0x521c,0x2116},
{0x521d,0x521d,0x110},
{0x521e,0x521e,0x126},
{0x521f,0x521f,0x132},
{0x5220,0x5220,0x13f},
{0x5221,0x5221,0x14a},
{0x5222,0x5222,0x166},
{0x5223,0x5223,0x133},
{0x5224,0x5224,0x138},
{0x5225,0x5225,0x140},
{0x5226,0x5226,0x149},
{0x5227,0x5227,0x167},
{0x5228,0x5228,0x10a},
{0x5229,0x5229,0x116},
{0x522a,0x522a,0x11e},
{0x522b,0x522b,0x122},
{0x522c,0x522c,0x120},
{0x522d,0x522d,0x130},
{0x522e,0x522e,0x12e},
{0x522f,0x522f,0x136},
{0x5230,0x5230,0x13d},
{0x5231,0x5231,0x13b},
{0x5232,0x5232,0x145},
{0x5233,0x5233,0x156},
{0x5234,0x5234,0x172},
{0x5235,0x5235,0x1d7},
{0x5236,0x5236,0x1db},
{0x5237,0x5237,0x1d9},
{0x5238,0x5238,0x1d5},
{0x5239,0x5239,0x174},
{0x523a,0x523a,0x176},
{0x523b,0x523b,0x10b},
{0x523c,0x523c,0x117},
{0x523d,0x523d,0x1f5},
{0x523e,0x523e,0x11f},
{0x523f,0x523f,0x121},
{0x5240,0x5240,0x12f},
{0x5241,0x5241,0x137},
{0x5242,0x5242,0x13e},
{0x5243,0x5243,0x13c},
{0x5244,0x5244,0x146},
{0x5245,0x5245,0x157},
{0x5246,0x5246,0x173},
{0x5247,0x5247,0x175},
{0x5248,0x5248,0x177},
{0x5249,0x5249,0x212e},
{0x524a,0x524a,0x2206},
{0x524b,0x524b,0x220f},
{0x524c,0x524d,0x2264},
{0x524e,0x524e,0x25ca},
{0x524f,0x524f,0x6ea2},
{0x5250,0x5250,0xfa40},
{0x5251,0x5251,0xfa20},
{0x5252,0x5252,0x8b7f},
{0x5253,0x5253,0x4e35},
{0x5254,0x5254,0x4e5c},
{0x5255,0x5255,0x4e63},
{0x5256,0x5256,0x4e68},
{0x5257,0x5258,0x4e74},
{0x5259,0x5259,0x4e97},
{0x525a,0x525a,0x4eaf},
{0x525b,0x525b,0x4edb},
{0x525c,0x525c,0x4ee2},
{0x525d,0x525d,0x4ee8},
{0x525e,0x525e,0x4eef},
{0x525f,0x525f,0x4efe},
{0x5260,0x5260,0x4f02},
{0x5261,0x5261,0x4f08},
{0x5262,0x5262,0x4f12},
{0x5263,0x5263,0x4f17},
{0x5264,0x5264,0x4f33},
{0x5265,0x5265,0x4f35},
{0x5266,0x5266,0x4f40},
{0x5267,0x5267,0x4f42},
{0x5268,0x5269,0x4f4b},
{0x526a,0x526a,0x4f52},
{0x526b,0x526b,0x4f63},
{0x526c,0x526c,0x4f6e},
{0x526d,0x526d,0x4f71},
{0x526e,0x526e,0x4f79},
{0x526f,0x526f,0x4f81},
{0x5270,0x5270,0x4f89},
{0x5271,0x5271,0x4f8c},
{0x5272,0x5272,0x4f8e},
{0x5273,0x5273,0x4f90},
{0x5274,0x5274,0x4f93},
{0x5275,0x5275,0x4f99},
{0x5276,0x5276,0x4f9f},
{0x5277,0x5277,0x4fb9},
{0x5278,0x527a,0x4fbb},
{0x527b,0x527c,0x4fc0},
{0x527d,0x527d,0x4fc6},
{0x527e,0x527e,0x4fc8},
{0x527f,0x527f,0x4fcc},
{0x5280,0x5280,0x4fdc},
{0x5281,0x5281,0x4fe2},
{0x5282,0x5282,0x4ff0},
{0x5283,0x5283,0x4ffc},
{0x5284,0x5284,0x5007},
{0x5285,0x5285,0x500a},
{0x5286,0x5286,0x5017},
{0x5287,0x5287,0x501d},
{0x5288,0x5288,0x5030},
{0x5289,0x528a,0x5032},
{0x528b,0x528b,0x5035},
{0x528c,0x528c,0x5045},
{0x528d,0x528d,0x504a},
{0x528e,0x528f,0x5051},
{0x5290,0x5290,0x5059},
{0x5291,0x5291,0x5060},
{0x5292,0x5292,0x5067},
{0x5293,0x5293,0x506d},
{0x5294,0x5294,0x5071},
{0x5295,0x5295,0x5081},
{0x5296,0x5297,0x5083},
{0x5298,0x5298,0x5086},
{0x5299,0x5299,0x508a},
{0x529a,0x529a,0x5090},
{0x529b,0x529b,0x509b},
{0x529c,0x529e,0x509f},
{0x529f,0x52a0,0x50af},
{0x52a1,0x52a1,0x50b9},
{0x52a2,0x52a2,0x50bd},
{0x52a3,0x52a3,0x50c0},
{0x52a4,0x52a4,0x50d3},
{0x52a5,0x52a6,0x50dc},
{0x52a7,0x52a7,0x50df},
{0x52a8,0x52a8,0x50e2},
{0x52a9,0x52a9,0x50e4},
{0x52aa,0x52aa,0x50f6},
{0x52ab,0x52ab,0x50fa},
{0x52ac,0x52ac,0x50f2},
{0x52ad,0x52ad,0x5119},
{0x52ae,0x52af,0x511c},
{0x52b0,0x52b0,0x5123},
{0x52b1,0x52b2,0x5127},
{0x52b3,0x52b4,0x512c},
{0x52b5,0x52b5,0x512f},
{0x52b6,0x52b6,0x5131},
{0x52b7,0x52b7,0x5134},
{0x52b8,0x52b8,0x5139},
{0x52b9,0x52b9,0x5142},
{0x52ba,0x52ba,0x514f},
{0x52bb,0x52bb,0x5153},
{0x52bc,0x52bc,0x5158},
{0x52bd,0x52bd,0x5166},
{0x52be,0x52be,0x517e},
{0x52bf,0x52bf,0x518e},
{0x52c0,0x52c0,0x51bf},
{0x52c1,0x52c1,0x51c2},
{0x52c2,0x52c2,0x51d2},
{0x52c3,0x52c3,0x51d5},
{0x52c4,0x52c4,0x51e5},
{0x52c5,0x52c5,0x51f2},
{0x52c6,0x52c6,0x51f7},
{0x52c7,0x52c7,0x5218},
{0x52c8,0x52c8,0x5222},
{0x52c9,0x52c9,0x5245},
{0x52ca,0x52ca,0x5258},
{0x52cb,0x52cb,0x525f},
{0x52cc,0x52cc,0x5280},
{0x52cd,0x52cd,0x5285},
{0x52ce,0x52d0,0x5295},
{0x52d1,0x52d1,0x529a},
{0x52d2,0x52d2,0x52a5},
{0x52d3,0x52d3,0x52a7},
{0x52d4,0x52d4,0x52b0},
{0x52d5,0x52d7,0x52b6},
{0x52d8,0x52d8,0x52bd},
{0x52d9,0x52d9,0x52c4},
{0x52da,0x52da,0x52c6},
{0x52db,0x52db,0x52cf},
{0x52dc,0x52dc,0x52d4},
{0x52dd,0x52dd,0x52dc},
{0x52de,0x52de,0x52e5},
{0x52df,0x52df,0x52e8},
{0x52e0,0x52e0,0x52ea},
{0x52e1,0x52e1,0x52ec},
{0x52e2,0x52e2,0x52f4},
{0x52e3,0x52e3,0x52f6},
{0x52e4,0x52e4,0x530c},
{0x52e5,0x52e5,0x5313},
{0x52e6,0x52e6,0x5318},
{0x52e7,0x52e7,0x531b},
{0x52e8,0x52e8,0x531e},
{0x52e9,0x52e9,0x5325},
{0x52ea,0x52ec,0x5327},
{0x52ed,0x52ee,0x532b},
{0x52ef,0x52ef,0x5330},
{0x52f0,0x52f0,0x533c},
{0x52f1,0x52f1,0x5359},
{0x52f2,0x52f2,0x535b},
{0x52f3,0x52f3,0x5365},
{0x52f4,0x52f4,0x5383},
{0x52f5,0x52f6,0x5387},
{0x52f7,0x52f7,0x538e},
{0x52f8,0x52f8,0x53a1},
{0x52f9,0x52f9,0x53b5},
{0x52fa,0x52fa,0x53b8},
{0x52fb,0x52fb,0x53bd},
{0x52fc,0x52fc,0x53cf},
{0x52fd,0x52fe,0x53d2},
{0x52ff,0x52ff,0x53de},
{0x5300,0x5300,0x53e0},
{0x5301,0x5301,0x53e7},
{0x5302,0x5302,0x5402},
{0x5303,0x5303,0x541a},
{0x5304,0x5304,0x5421},
{0x5305,0x5305,0x542f},
{0x5306,0x5306,0x5444},
{0x5307,0x5307,0x5447},
{0x5308,0x5308,0x544f},
{0x5309,0x5309,0x545e},
{0x530a,0x530a,0x5464},
{0x530b,0x530b,0x5467},
{0x530c,0x530c,0x5469},
{0x530d,0x530e,0x546d},
{0x530f,0x530f,0x5481},
{0x5310,0x5310,0x5483},
{0x5311,0x5311,0x5485},
{0x5312,0x5312,0x5489},
{0x5313,0x5313,0x5491},
{0x5314,0x5314,0x549f},
{0x5315,0x5315,0x54ca},
{0x5316,0x5316,0x54e0},
{0x5317,0x5317,0x54f6},
{0x5318,0x5318,0x54fe},
{0x5319,0x531a,0x550c},
{0x531b,0x531b,0x5532},
{0x531c,0x531c,0x553b},
{0x531d,0x531d,0x553d},
{0x531e,0x531e,0x5549},
{0x531f,0x531f,0x554d},
{0x5320,0x5320,0x5558},
{0x5321,0x5322,0x555a},
{0x5323,0x5323,0x557f},
{0x5324,0x5324,0x5593},
{0x5325,0x5325,0x5597},
{0x5326,0x5326,0x55a3},
{0x5327,0x5327,0x55c1},
{0x5328,0x5328,0x55cb},
{0x5329,0x532a,0x55d7},
{0x532b,0x532b,0x55de},
{0x532c,0x532c,0x55ff},
{0x532d,0x532d,0x5605},
{0x532e,0x532e,0x560a},
{0x532f,0x532f,0x5619},
{0x5330,0x5330,0x5633},
{0x5331,0x5331,0x563c},
{0x5332,0x5332,0x5641},
{0x5333,0x5334,0x5643},
{0x5335,0x5335,0x5646},
{0x5336,0x5338,0x5661},
{0x5339,0x5339,0x5675},
{0x533a,0x533a,0x5684},
{0x533b,0x533b,0x568b},
{0x533c,0x533c,0x56a7},
{0x533d,0x533d,0x56ab},
{0x533e,0x533e,0x56be},
{0x533f,0x533f,0x56cb},
{0x5340,0x5340,0x56d0},
{0x5341,0x5341,0x56dc},
{0x5342,0x5344,0x56e5},
{0x5345,0x5346,0x5701},
{0x5347,0x5347,0x5711},
{0x5348,0x5348,0x5720},
{0x5349,0x5349,0x5722},
{0x534a,0x534b,0x5724},
{0x534c,0x534c,0x572a},
{0x534d,0x534d,0x573f},
{0x534e,0x534e,0x5752},
{0x534f,0x534f,0x5762},
{0x5350,0x5350,0x5767},
{0x5351,0x5351,0x576e},
{0x5352,0x5352,0x5771},
{0x5353,0x5353,0x5779},
{0x5354,0x5354,0x577e},
{0x5355,0x5355,0x5781},
{0x5356,0x5356,0x5794},
{0x5357,0x5357,0x5799},
{0x5358,0x5358,0x579f},
{0x5359,0x5359,0x57a1},
{0x535a,0x535a,0x5795},
{0x535b,0x535b,0x57a7},
{0x535c,0x535c,0x57a9},
{0x535d,0x535d,0x57bd},
{0x535e,0x535e,0x57dd},
{0x535f,0x535f,0x57e9},
{0x5360,0x5360,0x57fe},
{0x5361,0x5361,0x5803},
{0x5362,0x5362,0x5808},
{0x5363,0x5363,0x57e1},
{0x5364,0x5364,0x580c},
{0x5365,0x5365,0x581b},
{0x5366,0x5366,0x581f},
{0x5367,0x5367,0x582d},
{0x5368,0x5368,0x583f},
{0x5369,0x5369,0x5850},
{0x536a,0x536a,0x5855},
{0x536b,0x536b,0x5868},
{0x536c,0x536c,0x5878},
{0x536d,0x536e,0x5887},
{0x536f,0x536f,0x588c},
{0x5370,0x5370,0x5896},
{0x5371,0x5373,0x58a0},
{0x5374,0x5374,0x58a6},
{0x5375,0x5375,0x58c4},
{0x5376,0x5376,0x58c2},
{0x5377,0x5377,0x58c8},
{0x5378,0x5378,0x58d6},
{0x5379,0x5379,0x58dd},
{0x537a,0x537a,0x58e1},
{0x537b,0x537b,0x5906},
{0x537c,0x537d,0x5912},
{0x537e,0x537e,0x591d},
{0x537f,0x537f,0x5921},
{0x5380,0x5380,0x5928},
{0x5381,0x5381,0x5930},
{0x5382,0x5382,0x5933},
{0x5383,0x5384,0x5935},
{0x5385,0x5385,0x593f},
{0x5386,0x5386,0x5943},
{0x5387,0x5387,0x5952},
{0x5388,0x5388,0x595e},
{0x5389,0x5389,0x596b},
{0x538a,0x538a,0x596f},
{0x538b,0x538b,0x5972},
{0x538c,0x538c,0x597b},
{0x538d,0x538d,0x598c},
{0x538e,0x538e,0x598e},
{0x538f,0x538f,0x5995},
{0x5390,0x5390,0x59a7},
{0x5391,0x5391,0x59ad},
{0x5392,0x5392,0x59b0},
{0x5393,0x5393,0x59b7},
{0x5394,0x5394,0x59c1},
{0x5395,0x5395,0x59c4},
{0x5396,0x5396,0x59ef},
{0x5397,0x5397,0x59f2},
{0x5398,0x5398,0x59f4},
{0x5399,0x5399,0x59f7},
{0x539a,0x539a,0x5a00},
{0x539b,0x539b,0x5a0e},
{0x539c,0x539c,0x5a12},
{0x539d,0x539d,0x5a1e},
{0x539e,0x539e,0x5a24},
{0x539f,0x539f,0x5a28},
{0x53a0,0x53a0,0x5a2a},
{0x53a1,0x53a1,0x5a30},
{0x53a2,0x53a3,0x5a44},
{0x53a4,0x53a4,0x5a48},
{0x53a5,0x53a5,0x5a4c},
{0x53a6,0x53a6,0x5a50},
{0x53a7,0x53a7,0x5a5e},
{0x53a8,0x53a8,0x5a7b},
{0x53a9,0x53a9,0x5a90},
{0x53aa,0x53aa,0x5a93},
{0x53ab,0x53ab,0x5a96},
{0x53ac,0x53ac,0x5a99},
{0x53ad,0x53ad,0x5abb},
{0x53ae,0x53ae,0x5ac6},
{0x53af,0x53af,0x5ac8},
{0x53b0,0x53b0,0x5acf},
{0x53b1,0x53b1,0x5afd},
{0x53b2,0x53b2,0x5b01},
{0x53b3,0x53b3,0x5b4b},
{0x53b4,0x53b4,0x5b5e},
{0x53b5,0x53b5,0x5b6e},
{0x53b6,0x53b6,0x5b86},
{0x53b7,0x53b7,0x5b8e},
{0x53b8,0x53b9,0x5b90},
{0x53ba,0x53ba,0x5b94},
{0x53bb,0x53bc,0x5ba8},
{0x53bd,0x53bd,0x5bad},
{0x53be,0x53be,0x5baf},
{0x53bf,0x53c0,0x5bb1},
{0x53c1,0x53c1,0x5bba},
{0x53c2,0x53c2,0x5bbc},
{0x53c3,0x53c3,0x5bc1},
{0x53c4,0x53c4,0x5bcd},
{0x53c5,0x53c5,0x5bcf},
{0x53c6,0x53c7,0x5bd9},
{0x53c8,0x53c8,0x5bef},
{0x53c9,0x53c9,0x5bf4},
{0x53ca,0x53ca,0x5c0c},
{0x53cb,0x53cb,0x5c17},
{0x53cc,0x53cc,0x5c26},
{0x53cd,0x53cd,0x5c2e},
{0x53ce,0x53ce,0x5c32},
{0x53cf,0x53cf,0x5c35},
{0x53d0,0x53d0,0x5c5a},
{0x53d1,0x53d2,0x5c74},
{0x53d3,0x53d3,0x5c7b},
{0x53d4,0x53d4,0x5c7d},
{0x53d5,0x53d5,0x5c87},
{0x53d6,0x53d6,0x5c92},
{0x53d7,0x53d7,0x5c9d},
{0x53d8,0x53d8,0x5cb2},
{0x53d9,0x53d9,0x5cb4},
{0x53da,0x53da,0x5cd7},
{0x53db,0x53db,0x5cee},
{0x53dc,0x53dd,0x5cf1},
{0x53de,0x53de,0x5d12},
{0x53df,0x53df,0x5d23},
{0x53e0,0x53e0,0x5d3f},
{0x53e1,0x53e1,0x5d48},
{0x53e2,0x53e2,0x5d55},
{0x53e3,0x53e3,0x5d51},
{0x53e4,0x53e5,0x5d5f},
{0x53e6,0x53e6,0x5d62},
{0x53e7,0x53e7,0x5d64},
{0x53e8,0x53e9,0x5d79},
{0x53ea,0x53ea,0x5d7f},
{0x53eb,0x53eb,0x5d8a},
{0x53ec,0x53ec,0x5d93},
{0x53ed,0x53ed,0x5d95},
{0x53ee,0x53ee,0x5d9b},
{0x53ef,0x53ef,0x5d9f},
{0x53f0,0x53f0,0x5dab},
{0x53f1,0x53f1,0x5dc3},
{0x53f2,0x53f2,0x5dce},
{0x53f3,0x53f3,0x5dd9},
{0x53f4,0x53f4,0x5e07},
{0x53f5,0x53f5,0x5e0d},
{0x53f6,0x53f6,0x5e20},
{0x53f7,0x53f7,0x5e4b},
{0x53f8,0x53f9,0x5e50},
{0x53fa,0x53fa,0x5e5c},
{0x53fb,0x53fb,0x5e70},
{0x53fc,0x53fc,0x5e8e},
{0x53fd,0x53fd,0x5ea2},
{0x53fe,0x53fe,0x5ea4},
{0x53ff,0x53ff,0x5eb1},
{0x5400,0x5400,0x5ecc},
{0x5401,0x5401,0x5ece},
{0x5402,0x5402,0x5edc},
{0x5403,0x5403,0x5ede},
{0x5404,0x5404,0x5ee5},
{0x5405,0x5405,0x5eeb},
{0x5406,0x5406,0x5f06},
{0x5407,0x5407,0x5f19},
{0x5408,0x5408,0x5f24},
{0x5409,0x540a,0x5f2b},
{0x540b,0x540b,0x5f2e},
{0x540c,0x540c,0x5f30},
{0x540d,0x540d,0x5f3f},
{0x540e,0x540e,0x5f44},
{0x540f,0x540f,0x5f5b},
{0x5410,0x5410,0x5f60},
{0x5411,0x5411,0x5f6f},
{0x5412,0x5413,0x5f74},
{0x5414,0x5414,0x5f78},
{0x5415,0x5415,0x5f7a},
{0x5416,0x5416,0x5f8d},
{0x5417,0x5417,0x5f96},
{0x5418,0x5418,0x5f9d},
{0x5419,0x5419,0x5fab},
{0x541a,0x541b,0x5fb0},
{0x541c,0x541c,0x5fc8},
{0x541d,0x541e,0x5fd0},
{0x541f,0x541f,0x5fe8},
{0x5420,0x5420,0x5fec},
{0x5421,0x5421,0x5ff2},
{0x5422,0x5422,0x5ff6},
{0x5423,0x5423,0x5ffa},
{0x5424,0x5424,0x600a},
{0x5425,0x5425,0x6013},
{0x5426,0x5426,0x601f},
{0x5427,0x5427,0x602d},
{0x5428,0x5428,0x6040},
{0x5429,0x5429,0x6048},
{0x542a,0x542a,0x6051},
{0x542b,0x542c,0x6056},
{0x542d,0x542d,0x6071},
{0x542e,0x542e,0x607e},
{0x542f,0x542f,0x6082},
{0x5430,0x5430,0x6086},
{0x5431,0x5431,0x6088},
{0x5432,0x5432,0x608e},
{0x5433,0x5433,0x6091},
{0x5434,0x5434,0x6093},
{0x5435,0x5435,0x6098},
{0x5436,0x5436,0x65df},
{0x5437,0x5437,0x60a2},
{0x5438,0x5438,0x60a5},
{0x5439,0x5439,0x60b7},
{0x543a,0x543a,0x60c2},
{0x543b,0x543c,0x60c9},
{0x543d,0x543e,0x60ce},
{0x543f,0x543f,0x60e2},
{0x5440,0x5440,0x60e5},
{0x5441,0x5441,0x60fc},
{0x5442,0x5442,0x6102},
{0x5443,0x5443,0x6107},
{0x5444,0x5444,0x610c},
{0x5445,0x5445,0x6117},
{0x5446,0x5446,0x6122},
{0x5447,0x5447,0x6131},
{0x5448,0x5448,0x6135},
{0x5449,0x5449,0x6139},
{0x544a,0x544a,0x6145},
{0x544b,0x544b,0x6149},
{0x544c,0x544c,0x616c},
{0x544d,0x544d,0x6172},
{0x544e,0x544e,0x6178},
{0x544f,0x5450,0x6180},
{0x5451,0x5452,0x6183},
{0x5453,0x5453,0x618b},
{0x5454,0x5454,0x619c},
{0x5455,0x5455,0x61a0},
{0x5456,0x5456,0x61aa},
{0x5457,0x5458,0x61c0},
{0x5459,0x545a,0x61ce},
{0x545b,0x545b,0x61de},
{0x545c,0x545c,0x61e1},
{0x545d,0x545d,0x61e7},
{0x545e,0x545e,0x61e9},
{0x545f,0x5460,0x61ec},
{0x5461,0x5461,0x61ef},
{0x5462,0x5462,0x6201},
{0x5463,0x5463,0x6203},
{0x5464,0x5464,0x621c},
{0x5465,0x5465,0x6220},
{0x5466,0x5466,0x6227},
{0x5467,0x5467,0x622b},
{0x5468,0x5468,0x6242},
{0x5469,0x5469,0x6244},
{0x546a,0x546a,0x6250},
{0x546b,0x546b,0x6254},
{0x546c,0x546c,0x625c},
{0x546d,0x546d,0x627d},
{0x546e,0x5470,0x628d},
{0x5471,0x5471,0x62b3},
{0x5472,0x5473,0x62b6},
{0x5474,0x5474,0x62ba},
{0x5475,0x5476,0x62be},
{0x5477,0x5477,0x62ce},
{0x5478,0x5478,0x62ea},
{0x5479,0x5479,0x62f2},
{0x547a,0x547a,0x6304},
{0x547b,0x547b,0x630b},
{0x547c,0x547c,0x6313},
{0x547d,0x547d,0x6329},
{0x547e,0x547e,0x632d},
{0x547f,0x547f,0x634a},
{0x5480,0x5480,0x6352},
{0x5481,0x5481,0x6354},
{0x5482,0x5482,0x6358},
{0x5483,0x5483,0x635b},
{0x5484,0x5484,0x6366},
{0x5485,0x5485,0x636d},
{0x5486,0x5486,0x6378},
{0x5487,0x5487,0x6395},
{0x5488,0x5488,0x639a},
{0x5489,0x5489,0x63a4},
{0x548a,0x548a,0x63a6},
{0x548b,0x548b,0x63ad},
{0x548c,0x548c,0x63c1},
{0x548d,0x548d,0x63c5},
{0x548e,0x548e,0x63c8},
{0x548f,0x548f,0x63ce},
{0x5490,0x5490,0x63d3},
{0x5491,0x5491,0x63f3},
{0x5492,0x5492,0x640a},
{0x5493,0x5493,0x6430},
{0x5494,0x5494,0x644b},
{0x5495,0x5495,0x644f},
{0x5496,0x5496,0x6453},
{0x5497,0x5497,0x645c},
{0x5498,0x5498,0x6461},
{0x5499,0x5499,0x6463},
{0x549a,0x549a,0x6485},
{0x549b,0x549b,0x648f},
{0x549c,0x549c,0x649b},
{0x549d,0x549d,0x64a1},
{0x549e,0x549e,0x64a3},
{0x549f,0x549f,0x64a6},
{0x54a0,0x54a0,0x64a8},
{0x54a1,0x54a1,0x64bd},
{0x54a2,0x54a2,0x64c9},
{0x54a3,0x54a3,0x64d1},
{0x54a4,0x54a5,0x64e9},
{0x54a6,0x54a6,0x64f5},
{0x54a7,0x54a7,0x6501},
{0x54a8,0x54aa,0x6508},
{0x54ab,0x54ab,0x6513},
{0x54ac,0x54ac,0x6526},
{0x54ad,0x54ad,0x6531},
{0x54ae,0x54ae,0x653a},
{0x54af,0x54b0,0x653c},
{0x54b1,0x54b1,0x6543},
{0x54b2,0x54b2,0x6550},
{0x54b3,0x54b3,0x6552},
{0x54b4,0x54b4,0x655f},
{0x54b5,0x54b5,0x657d},
{0x54b6,0x54b6,0x6598},
{0x54b7,0x54b7,0x65a0},
{0x54b8,0x54b8,0x65a3},
{0x54b9,0x54b9,0x65a6},
{0x54ba,0x54ba,0x65ae},
{0x54bb,0x54bb,0x65b3},
{0x54bc,0x54bc,0x65d6},
{0x54bd,0x54bd,0x65d8},
{0x54be,0x54be,0x65df},
{0x54bf,0x54c0,0x65f4},
{0x54c1,0x54c2,0x65fe},
{0x54c3,0x54c3,0x660d},
{0x54c4,0x54c5,0x6611},
{0x54c6,0x54c6,0x6616},
{0x54c7,0x54c7,0x661d},
{0x54c8,0x54c8,0x6623},
{0x54c9,0x54c9,0x6626},
{0x54ca,0x54ca,0x6629},
{0x54cb,0x54cb,0x6639},
{0x54cc,0x54cc,0x6637},
{0x54cd,0x54cd,0x6640},
{0x54ce,0x54ce,0x6646},
{0x54cf,0x54cf,0x664a},
{0x54d0,0x54d0,0x6658},
{0x54d1,0x54d1,0x6660},
{0x54d2,0x54d2,0x6675},
{0x54d3,0x54d3,0x667f},
{0x54d4,0x54d4,0x6679},
{0x54d5,0x54d5,0x667c},
{0x54d6,0x54d8,0x669a},
{0x54d9,0x54d9,0x669f},
{0x54da,0x54da,0x69fe},
{0x54db,0x54dc,0x66c2},
{0x54dd,0x54dd,0x66cc},
{0x54de,0x54de,0x66ce},
{0x54df,0x54df,0x66d4},
{0x54e0,0x54e0,0x66df},
{0x54e1,0x54e1,0x66eb},
{0x54e2,0x54e2,0x66ee},
{0x54e3,0x54e3,0x6707},
{0x54e4,0x54e4,0x671c},
{0x54e5,0x54e5,0x6720},
{0x54e6,0x54e6,0x6722},
{0x54e7,0x54e7,0x673e},
{0x54e8,0x54e8,0x6745},
{0x54e9,0x54e9,0x676c},
{0x54ea,0x54ea,0x6784},
{0x54eb,0x54eb,0x678e},
{0x54ec,0x54ec,0x6796},
{0x54ed,0x54ed,0x6799},
{0x54ee,0x54ef,0x67bc},
{0x54f0,0x54f0,0x67c2},
{0x54f1,0x54f1,0x67c5},
{0x54f2,0x54f2,0x67c9},
{0x54f3,0x54f3,0x67dc},
{0x54f4,0x54f4,0x67e1},
{0x54f5,0x54f5,0x67e6},
{0x54f6,0x54f6,0x67f2},
{0x54f7,0x54f7,0x67f6},
{0x54f8,0x54f8,0x6814},
{0x54f9,0x54f9,0x6819},
{0x54fa,0x54fa,0x6827},
{0x54fb,0x54fb,0x682f},
{0x54fc,0x54fc,0x683f},
{0x54fd,0x54fd,0x684a},
{0x54fe,0x54fe,0x6858},
{0x54ff,0x5501,0x686f},
{0x5502,0x5502,0x6879},
{0x5503,0x5503,0x687b},
{0x5504,0x5504,0x6888},
{0x5505,0x5505,0x68a1},
{0x5506,0x5506,0x68a9},
{0x5507,0x5507,0x68ae},
{0x5508,0x5508,0x68d1},
{0x5509,0x5509,0x68d3},
{0x550a,0x550b,0x68dc},
{0x550c,0x550c,0x68ea},
{0x550d,0x550d,0x68f6},
{0x550e,0x550e,0x68fd},
{0x550f,0x550f,0x6906},
{0x5510,0x5510,0x6909},
{0x5511,0x5511,0x6910},
{0x5512,0x5512,0x6916},
{0x5513,0x5513,0x6931},
{0x5514,0x5514,0x6945},
{0x5515,0x5515,0x694e},
{0x5516,0x5516,0x6966},
{0x5517,0x5518,0x6970},
{0x5519,0x5519,0x697b},
{0x551a,0x551a,0x698d},
{0x551b,0x551b,0x69a1},
{0x551c,0x551c,0x69b8},
{0x551d,0x551d,0x69c5},
{0x551e,0x551e,0x69c8},
{0x551f,0x551f,0x69fe},
{0x5520,0x5520,0x6a00},
{0x5521,0x5521,0x6a03},
{0x5522,0x5522,0x6a20},
{0x5523,0x5523,0x6a24},
{0x5524,0x5524,0x6a37},
{0x5525,0x5525,0x6a55},
{0x5526,0x5526,0x6a6a},
{0x5527,0x5527,0x6a81},
{0x5528,0x5529,0x6a86},
{0x552a,0x552a,0x6a9b},
{0x552b,0x552c,0x6ab0},
{0x552d,0x552d,0x6ab4},
{0x552e,0x552f,0x6abe},
{0x5530,0x5530,0x6acc},
{0x5531,0x5532,0x6ad5},
{0x5533,0x5533,0x6af0},
{0x5534,0x5534,0x6afc},
{0x5535,0x5535,0x6b02},
{0x5536,0x5537,0x6b06},
{0x5538,0x5538,0x6b09},
{0x5539,0x5539,0x6b28},
{0x553a,0x553a,0x6b2b},
{0x553b,0x553b,0x6b36},
{0x553c,0x553c,0x6b4d},
{0x553d,0x553d,0x6b52},
{0x553e,0x553e,0x6b5d},
{0x553f,0x553f,0x6b6b},
{0x5540,0x5540,0x6b6e},
{0x5541,0x5541,0x6b70},
{0x5542,0x5542,0x6b85},
{0x5543,0x5543,0x6b97},
{0x5544,0x5545,0x6b9f},
{0x5546,0x5547,0x6ba2},
{0x5548,0x5548,0x6ba8},
{0x5549,0x5549,0x6bac},
{0x554a,0x554b,0x6bb8},
{0x554c,0x554d,0x6bc3},
{0x554e,0x554e,0x6be3},
{0x554f,0x554f,0x6c12},
{0x5550,0x5550,0x6c19},
{0x5551,0x5551,0x6c1f},
{0x5552,0x5554,0x6c26},
{0x5555,0x5555,0x6c2e},
{0x5556,0x5556,0x6c3b},
{0x5557,0x5557,0x6c4b},
{0x5558,0x5558,0x6c4f},
{0x5559,0x5559,0x6c6b},
{0x555a,0x555a,0x6c78},
{0x555b,0x555b,0x6c87},
{0x555c,0x555c,0x6c9f},
{0x555d,0x555d,0x6cb0},
{0x555e,0x555e,0x6cb2},
{0x555f,0x555f,0x6ccd},
{0x5560,0x5560,0x6ccf},
{0x5561,0x5561,0x6cd1},
{0x5562,0x5562,0x6ce7},
{0x5563,0x5563,0x6cf2},
{0x5564,0x5564,0x6cf4},
{0x5565,0x5565,0x6d07},
{0x5566,0x5566,0x6d0f},
{0x5567,0x5567,0x6d13},
{0x5568,0x5568,0x6d1a},
{0x5569,0x5569,0x6d28},
{0x556a,0x556a,0x6d5f},
{0x556b,0x556b,0x6d67},
{0x556c,0x556c,0x6d92},
{0x556d,0x556d,0x6d97},
{0x556e,0x556e,0x6db7},
{0x556f,0x556f,0x6dbd},
{0x5570,0x5570,0x6de0},
{0x5571,0x5571,0x6de2},
{0x5572,0x5572,0x6de5},
{0x5573,0x5573,0x6def},
{0x5574,0x5574,0x6df4},
{0x5575,0x5575,0x6e00},
{0x5576,0x5576,0x6e04},
{0x5577,0x5577,0x6e3b},
{0x5578,0x5578,0x6e52},
{0x5579,0x5579,0x6e5d},
{0x557a,0x557a,0x6e62},
{0x557b,0x557b,0x6e68},
{0x557c,0x557c,0x6e8d},
{0x557d,0x557d,0x6e99},
{0x557e,0x557e,0x6ea0},
{0x557f,0x5580,0x6ead},
{0x5581,0x5581,0x6eb3},
{0x5582,0x5582,0x6ebb},
{0x5583,0x5583,0x6ec0},
{0x5584,0x5584,0x6ec8},
{0x5585,0x5585,0x6ecd},
{0x5586,0x5586,0x6ecf},
{0x5587,0x5588,0x6eed},
{0x5589,0x5589,0x6f04},
{0x558a,0x558a,0x6f08},
{0x558b,0x558b,0x6f0d},
{0x558c,0x558c,0x6f16},
{0x558d,0x558d,0x6f1b},
{0x558e,0x558e,0x6f3b},
{0x558f,0x558f,0x6f2d},
{0x5590,0x5590,0x6f4f},
{0x5591,0x5591,0x6f53},
{0x5592,0x5592,0x6f5d},
{0x5593,0x5593,0x6f6c},
{0x5594,0x5594,0x6f83},
{0x5595,0x5595,0x6f93},
{0x5596,0x5596,0x6fa6},
{0x5597,0x5597,0x6fb0},
{0x5598,0x5598,0x6fc5},
{0x5599,0x5599,0x6fe8},
{0x559a,0x559a,0x6ffd},
{0x559b,0x559b,0x7017},
{0x559c,0x559c,0x702f},
{0x559d,0x559d,0x7034},
{0x559e,0x559e,0x7037},
{0x559f,0x559f,0x7044},
{0x55a0,0x55a0,0x7048},
{0x55a1,0x55a1,0x7055},
{0x55a2,0x55a2,0x7094},
{0x55a3,0x55a3,0x7096},
{0x55a4,0x55a4,0x709b},
{0x55a5,0x55a5,0x70b4},
{0x55a6,0x55a6,0x70fa},
{0x55a7,0x55a7,0x7105},
{0x55a8,0x55a8,0x710b},
{0x55a9,0x55a9,0x712d},
{0x55aa,0x55aa,0x7138},
{0x55ab,0x55ab,0x7141},
{0x55ac,0x55ac,0x714b},
{0x55ad,0x55ad,0x74d8},
{0x55ae,0x55ae,0x7157},
{0x55af,0x55af,0x715a},
{0x55b0,0x55b0,0x718c},
{0x55b1,0x55b1,0x719a},
{0x55b2,0x55b2,0x71b0},
{0x55b3,0x55b4,0x71bf},
{0x55b5,0x55b5,0x71cc},
{0x55b6,0x55b6,0x71da},
{0x55b7,0x55b7,0x71f8},
{0x55b8,0x55b9,0x7208},
{0x55ba,0x55ba,0x7213},
{0x55bb,0x55bb,0x721a},
{0x55bc,0x55bc,0x7224},
{0x55bd,0x55bd,0x722f},
{0x55be,0x55be,0x7245},
{0x55bf,0x55bf,0x724e},
{0x55c0,0x55c0,0x725e},
{0x55c1,0x55c1,0x726b},
{0x55c2,0x55c2,0x7271},
{0x55c3,0x55c4,0x727b},
{0x55c5,0x55c5,0x7289},
{0x55c6,0x55c6,0x7293},
{0x55c7,0x55c7,0x72a8},
{0x55c8,0x55c9,0x72d5},
{0x55ca,0x55ca,0x72d8},
{0x55cb,0x55cb,0x72df},
{0x55cc,0x55cc,0x72fe},
{0x55cd,0x55cd,0x730d},
{0x55ce,0x55ce,0x7313},
{0x55cf,0x55cf,0x7332},
{0x55d0,0x55d0,0x7335},
{0x55d1,0x55d1,0x7356},
{0x55d2,0x55d5,0x735d},
{0x55d6,0x55d6,0x7369},
{0x55d7,0x55d7,0x7379},
{0x55d8,0x55d8,0x7380},
{0x55d9,0x55d9,0x738e},
{0x55da,0x55da,0x7390},
{0x55db,0x55db,0x7393},
{0x55dc,0x55dc,0x7397},
{0x55dd,0x55dd,0x73aa},
{0x55de,0x55de,0x73ad},
{0x55df,0x55df,0x73c6},
{0x55e0,0x55e0,0x73cc},
{0x55e1,0x55e1,0x73d3},
{0x55e2,0x55e2,0x73dd},
{0x55e3,0x55e3,0x73e6},
{0x55e4,0x55e4,0x73f7},
{0x55e5,0x55e5,0x73fb},
{0x55e6,0x55e7,0x73ff},
{0x55e8,0x55e8,0x7411},
{0x55e9,0x55e9,0x742d},
{0x55ea,0x55eb,0x7467},
{0x55ec,0x55ec,0x746e},
{0x55ed,0x55ed,0x748f},
{0x55ee,0x55ee,0x7491},
{0x55ef,0x55ef,0x749a},
{0x55f0,0x55f0,0x74ae},
{0x55f1,0x55f2,0x74b1},
{0x55f3,0x55f3,0x74cc},
{0x55f4,0x55f4,0x74d0},
{0x55f5,0x55f5,0x74d3},
{0x55f6,0x55f6,0x74d8},
{0x55f7,0x55f7,0x74db},
{0x55f8,0x55f8,0x74e8},
{0x55f9,0x55f9,0x74ea},
{0x55fa,0x55fa,0x74ef},
{0x55fb,0x55fb,0x74fc},
{0x55fc,0x55fc,0x7506},
{0x55fd,0x55fd,0x7512},
{0x55fe,0x55fe,0x7527},
{0x55ff,0x55ff,0x7529},
{0x5600,0x5600,0x7536},
{0x5601,0x5601,0x7539},
{0x5602,0x5602,0x7543},
{0x5603,0x5603,0x7547},
{0x5604,0x5604,0x7557},
{0x5605,0x5605,0x755f},
{0x5606,0x5606,0x7561},
{0x5607,0x5608,0x757b},
{0x5609,0x5609,0x7585},
{0x560a,0x560a,0x7595},
{0x560b,0x560b,0x759c},
{0x560c,0x560c,0x75ba},
{0x560d,0x560d,0x7612},
{0x560e,0x560e,0x7623},
{0x560f,0x560f,0x7629},
{0x5610,0x5611,0x7639},
{0x5612,0x5612,0x7640},
{0x5613,0x5613,0x7644},
{0x5614,0x5614,0x7659},
{0x5615,0x5615,0x7685},
{0x5616,0x5617,0x768c},
{0x5618,0x5618,0x769f},
{0x5619,0x561a,0x76a2},
{0x561b,0x561b,0x76c1},
{0x561c,0x561c,0x76cb},
{0x561d,0x561d,0x76d4},
{0x561e,0x561e,0x76e0},
{0x561f,0x561f,0x76f6},
{0x5620,0x5620,0x7706},
{0x5621,0x5621,0x7712},
{0x5622,0x5623,0x7714},
{0x5624,0x5624,0x771c},
{0x5625,0x5625,0x772e},
{0x5626,0x5626,0x773d},
{0x5627,0x5627,0x7742},
{0x5628,0x5628,0x7752},
{0x5629,0x562a,0x7756},
{0x562b,0x562b,0x7770},
{0x562c,0x562d,0x7773},
{0x562e,0x562e,0x778d},
{0x562f,0x562f,0x77a2},
{0x5630,0x5630,0x77ae},
{0x5631,0x5631,0x77b1},
{0x5632,0x5632,0x77b5},
{0x5633,0x5633,0x77c3},
{0x5634,0x5634,0x77d2},
{0x5635,0x5635,0x77d5},
{0x5636,0x5636,0x77f8},
{0x5637,0x5637,0x780e},
{0x5638,0x5638,0x7811},
{0x5639,0x5639,0x781d},
{0x563a,0x563a,0x7823},
{0x563b,0x563b,0x7844},
{0x563c,0x563c,0x7848},
{0x563d,0x563d,0x784c},
{0x563e,0x563e,0x7852},
{0x563f,0x563f,0x785e},
{0x5640,0x5641,0x7860},
{0x5642,0x5642,0x7863},
{0x5643,0x5643,0x788f},
{0x5644,0x5644,0x78a8},
{0x5645,0x5645,0x78ac},
{0x5646,0x5646,0x78b2},
{0x5647,0x5647,0x78bd},
{0x5648,0x5648,0x78bf},
{0x5649,0x5649,0x78c7},
{0x564a,0x564a,0x78d2},
{0x564b,0x564b,0x78d6},
{0x564c,0x564c,0x78db},
{0x564d,0x564d,0x78df},
{0x564e,0x564e,0x78ea},
{0x564f,0x564f,0x78f3},
{0x5650,0x5650,0x78f6},
{0x5651,0x5651,0x78ff},
{0x5652,0x5652,0x7906},
{0x5653,0x5653,0x791a},
{0x5654,0x5654,0x791e},
{0x5655,0x5655,0x7920},
{0x5656,0x5656,0x7929},
{0x5657,0x5657,0x792d},
{0x5658,0x5658,0x7935},
{0x5659,0x5659,0x7944},
{0x565a,0x565a,0x794b},
{0x565b,0x565b,0x794f},
{0x565c,0x565c,0x7951},
{0x565d,0x565d,0x7969},
{0x565e,0x565e,0x797b},
{0x565f,0x565f,0x797e},
{0x5660,0x5660,0x798c},
{0x5661,0x5661,0x7991},
{0x5662,0x5662,0x7993},
{0x5663,0x5663,0x799c},
{0x5664,0x5664,0x79a8},
{0x5665,0x5665,0x79af},
{0x5666,0x5666,0x79cf},
{0x5667,0x5667,0x79dd},
{0x5668,0x5668,0x79e0},
{0x5669,0x5669,0x79e2},
{0x566a,0x566a,0x79e5},
{0x566b,0x566b,0x79f1},
{0x566c,0x566c,0x79f8},
{0x566d,0x566d,0x79fc},
{0x566e,0x566e,0x7a07},
{0x566f,0x566f,0x7a21},
{0x5670,0x5670,0x7a27},
{0x5671,0x5671,0x7a2b},
{0x5672,0x5672,0x7a2f},
{0x5673,0x5674,0x7a34},
{0x5675,0x5675,0x7a48},
{0x5676,0x5676,0x7a55},
{0x5677,0x5677,0x7a65},
{0x5678,0x5678,0x7a7e},
{0x5679,0x5679,0x7a8b},
{0x567a,0x567a,0x7a91},
{0x567b,0x567b,0x7a9e},
{0x567c,0x567c,0x7ac9},
{0x567d,0x567d,0x7adb},
{0x567e,0x567e,0x7ae9},
{0x567f,0x567f,0x7aec},
{0x5680,0x5680,0x7af1},
{0x5681,0x5681,0x7afb},
{0x5682,0x5682,0x7b1f},
{0x5683,0x5683,0x7b23},
{0x5684,0x5684,0x7b29},
{0x5685,0x5685,0x7b30},
{0x5686,0x5686,0x7b34},
{0x5687,0x5688,0x7b3f},
{0x5689,0x5689,0x7b6a},
{0x568a,0x568a,0x7b84},
{0x568b,0x568b,0x7b89},
{0x568c,0x568c,0x7b8e},
{0x568d,0x568d,0x7b96},
{0x568e,0x568e,0x7ba5},
{0x568f,0x568f,0x7bb2},
{0x5690,0x5690,0x7bb6},
{0x5691,0x5692,0x7bba},
{0x5693,0x5693,0x7bbd},
{0x5694,0x5694,0x7bc2},
{0x5695,0x5695,0x7bc8},
{0x5696,0x5696,0x7bdb},
{0x5697,0x5698,0x7bf4},
{0x5699,0x569a,0x7bf9},
{0x569b,0x569b,0x7c02},
{0x569c,0x569c,0x7c04},
{0x569d,0x569d,0x7c06},
{0x569e,0x569e,0x7c0c},
{0x569f,0x569f,0x7c19},
{0x56a0,0x56a0,0x7c1b},
{0x56a1,0x56a1,0x7c25},
{0x56a2,0x56a2,0x7c2c},
{0x56a3,0x56a3,0x7c34},
{0x56a4,0x56a5,0x7c39},
{0x56a6,0x56a6,0x7c46},
{0x56a7,0x56a7,0x7c55},
{0x56a8,0x56a8,0x7c5a},
{0x56a9,0x56a9,0x7c63},
{0x56aa,0x56aa,0x7c69},
{0x56ab,0x56ab,0x7c7c},
{0x56ac,0x56ac,0x7c86},
{0x56ad,0x56ad,0x7cb0},
{0x56ae,0x56ae,0x7cbb},
{0x56af,0x56af,0x7ccf},
{0x56b0,0x56b0,0x7cd4},
{0x56b1,0x56b1,0x7ce9},
{0x56b2,0x56b2,0x7d0f},
{0x56b3,0x56b3,0x7d11},
{0x56b4,0x56b4,0x7d16},
{0x56b5,0x56b5,0x7d26},
{0x56b6,0x56b6,0x7d2a},
{0x56b7,0x56b7,0x7d2d},
{0x56b8,0x56b8,0x7d51},
{0x56b9,0x56b9,0x7d57},
{0x56ba,0x56ba,0x7d65},
{0x56bb,0x56bb,0x7d67},
{0x56bc,0x56bc,0x7d78},
{0x56bd,0x56bd,0x7d7b},
{0x56be,0x56be,0x7d81},
{0x56bf,0x56bf,0x7d96},
{0x56c0,0x56c3,0x7dc3},
{0x56c4,0x56c5,0x7dcd},
{0x56c6,0x56c6,0x7e00},
{0x56c7,0x56c7,0x7de2},
{0x56c8,0x56c8,0x7dea},
{0x56c9,0x56c9,0x7ded},
{0x56ca,0x56ca,0x7dfa},
{0x56cb,0x56cb,0x7e1c},
{0x56cc,0x56cc,0x7e2d},
{0x56cd,0x56cd,0x7e33},
{0x56ce,0x56ce,0x7e3f},
{0x56cf,0x56cf,0x7e4e},
{0x56d0,0x56d0,0x7e50},
{0x56d1,0x56d1,0x7e58},
{0x56d2,0x56d2,0x7e5f},
{0x56d3,0x56d3,0x7e65},
{0x56d4,0x56d4,0x7e95},
{0x56d5,0x56d6,0x7e9d},
{0x56d7,0x56d7,0x7f3f},
{0x56d8,0x56d8,0x7f5c},
{0x56d9,0x56d9,0x7f66},
{0x56da,0x56da,0x7f80},
{0x56db,0x56db,0x7f8d},
{0x56dc,0x56dc,0x7f8f},
{0x56dd,0x56dd,0x7fa6},
{0x56de,0x56de,0x7faa},
{0x56df,0x56df,0x7fb4},
{0x56e0,0x56e0,0x7fbc},
{0x56e1,0x56e1,0x7fc0},
{0x56e2,0x56e2,0x7fc8},
{0x56e3,0x56e3,0x7fe8},
{0x56e4,0x56e4,0x800f},
{0x56e5,0x56e5,0x8013},
{0x56e6,0x56e6,0x801d},
{0x56e7,0x56e8,0x801f},
{0x56e9,0x56e9,0x802e},
{0x56ea,0x56ea,0x8034},
{0x56eb,0x56eb,0x803e},
{0x56ec,0x56ec,0x8040},
{0x56ed,0x56ed,0x8044},
{0x56ee,0x56ee,0x8064},
{0x56ef,0x56ef,0x806d},
{0x56f0,0x56f0,0x8081},
{0x56f1,0x56f1,0x80b9},
{0x56f2,0x56f2,0x80c8},
{0x56f3,0x56f3,0x80cd},
{0x56f4,0x56f4,0x80d2},
{0x56f5,0x56f5,0x80ee},
{0x56f6,0x56f6,0x80f2},
{0x56f7,0x56f7,0x80f6},
{0x56f8,0x56f8,0x80f9},
{0x56f9,0x56f9,0x810b},
{0x56fa,0x56fa,0x811c},
{0x56fb,0x56fb,0x8120},
{0x56fc,0x56fc,0x813c},
{0x56fd,0x56fd,0x8145},
{0x56fe,0x56fe,0x8147},
{0x56ff,0x56ff,0x8152},
{0x5700,0x5700,0x8161},
{0x5701,0x5701,0x8177},
{0x5702,0x5702,0x8186},
{0x5703,0x5703,0x818e},
{0x5704,0x5704,0x8196},
{0x5705,0x5705,0x81a2},
{0x5706,0x5706,0x81ae},
{0x5707,0x5707,0x81c5},
{0x5708,0x5708,0x81ce},
{0x5709,0x5709,0x81eb},
{0x570a,0x570c,0x81f0},
{0x570d,0x570d,0x81f5},
{0x570e,0x570e,0x81f8},
{0x570f,0x570f,0x8200},
{0x5710,0x5710,0x820f},
{0x5711,0x5711,0x821d},
{0x5712,0x5712,0x8228},
{0x5713,0x5713,0x8243},
{0x5714,0x5714,0x824e},
{0x5715,0x5715,0x8251},
{0x5716,0x5716,0x8256},
{0x5717,0x5717,0x8267},
{0x5718,0x5718,0x827b},
{0x5719,0x571a,0x8280},
{0x571b,0x571b,0x8287},
{0x571c,0x571c,0x8294},
{0x571d,0x571d,0x8296},
{0x571e,0x571e,0x8298},
{0x571f,0x5720,0x829a},
{0x5721,0x5721,0x82a0},
{0x5722,0x5722,0x82da},
{0x5723,0x5723,0x82e0},
{0x5724,0x5724,0x82e4},
{0x5725,0x5725,0x82ed},
{0x5726,0x5727,0x830a},
{0x5728,0x5729,0x831e},
{0x572a,0x572a,0x8321},
{0x572b,0x572b,0x832c},
{0x572c,0x572c,0x832e},
{0x572d,0x572d,0x8333},
{0x572e,0x572e,0x8337},
{0x572f,0x572f,0x833d},
{0x5730,0x5730,0x8342},
{0x5731,0x5732,0x834d},
{0x5733,0x5733,0x8370},
{0x5734,0x5734,0x8380},
{0x5735,0x5735,0x8382},
{0x5736,0x5736,0x8384},
{0x5737,0x5737,0x8399},
{0x5738,0x5738,0x839c},
{0x5739,0x5739,0x83a6},
{0x573a,0x573a,0x83ac},
{0x573b,0x573b,0x83be},
{0x573c,0x573c,0x8353},
{0x573d,0x573d,0x83e8},
{0x573e,0x573e,0x8419},
{0x573f,0x573f,0x83ad},
{0x5740,0x5740,0x842f},
{0x5741,0x5741,0x8445},
{0x5742,0x5742,0x8447},
{0x5743,0x5743,0x844d},
{0x5744,0x5744,0x8456},
{0x5745,0x5745,0x845c},
{0x5746,0x5746,0x8460},
{0x5747,0x5747,0x8464},
{0x5748,0x5748,0x8467},
{0x5749,0x5749,0x846a},
{0x574a,0x574a,0x8474},
{0x574b,0x574b,0x847d},
{0x574c,0x574c,0x8492},
{0x574d,0x574d,0x8495},
{0x574e,0x574f,0x84a9},
{0x5750,0x5751,0x84c7},
{0x5752,0x5752,0x84cc},
{0x5753,0x5753,0x84f2},
{0x5754,0x5754,0x84f7},
{0x5755,0x5756,0x8502},
{0x5757,0x5757,0x8507},
{0x5758,0x5758,0x850e},
{0x5759,0x5759,0x8510},
{0x575a,0x575a,0x851c},
{0x575b,0x575b,0x8522},
{0x575c,0x575c,0x8527},
{0x575d,0x575d,0x852a},
{0x575e,0x575e,0x8533},
{0x575f,0x575f,0x8536},
{0x5760,0x5760,0x853f},
{0x5761,0x5761,0x8550},
{0x5762,0x5762,0x8552},
{0x5763,0x5764,0x855c},
{0x5765,0x5766,0x855f},
{0x5767,0x5767,0x8579},
{0x5768,0x5768,0x8589},
{0x5769,0x5769,0x858b},
{0x576a,0x576a,0x85a0},
{0x576b,0x576b,0x85a5},
{0x576c,0x576c,0x85a7},
{0x576d,0x576d,0x85b4},
{0x576e,0x576e,0x85b6},
{0x576f,0x576f,0x85b8},
{0x5770,0x5772,0x85bd},
{0x5773,0x5773,0x85c2},
{0x5774,0x5774,0x85da},
{0x5775,0x5775,0x85e0},
{0x5776,0x5776,0x85e8},
{0x5777,0x5777,0x85f3},
{0x5778,0x5778,0x85fc},
{0x5779,0x577a,0x860d},
{0x577b,0x577b,0x8619},
{0x577c,0x577c,0x861b},
{0x577d,0x577d,0x8636},
{0x577e,0x577e,0x863a},
{0x577f,0x577f,0x863d},
{0x5780,0x5781,0x8658},
{0x5782,0x5782,0x865d},
{0x5783,0x5784,0x8660},
{0x5785,0x5785,0x8664},
{0x5786,0x5786,0x8669},
{0x5787,0x5787,0x8676},
{0x5788,0x5788,0x8696},
{0x5789,0x5789,0x869a},
{0x578a,0x578a,0x86a1},
{0x578b,0x578b,0x86a6},
{0x578c,0x578c,0x86ad},
{0x578d,0x578e,0x86b4},
{0x578f,0x578f,0x86b7},
{0x5790,0x5790,0x86b9},
{0x5791,0x5791,0x86bf},
{0x5792,0x5792,0x86c5},
{0x5793,0x5793,0x86d2},
{0x5794,0x5794,0x86da},
{0x5795,0x5795,0x86dc},
{0x5796,0x5796,0x86e0},
{0x5797,0x5797,0x86e5},
{0x5798,0x5798,0x86e7},
{0x5799,0x5799,0x8688},
{0x579a,0x579a,0x8704},
{0x579b,0x579b,0x870f},
{0x579c,0x579c,0x872f},
{0x579d,0x579d,0x8732},
{0x579e,0x579f,0x873c},
{0x57a0,0x57a0,0x8745},
{0x57a1,0x57a1,0x874d},
{0x57a2,0x57a2,0x8761},
{0x57a3,0x57a3,0x876f},
{0x57a4,0x57a7,0x8783},
{0x57a8,0x57a8,0x8790},
{0x57a9,0x57a9,0x8795},
{0x57aa,0x57aa,0x87a3},
{0x57ab,0x57ab,0x87b1},
{0x57ac,0x57ac,0x87c8},
{0x57ad,0x57ad,0x87ca},
{0x57ae,0x57ae,0x87d5},
{0x57af,0x57af,0x87d9},
{0x57b0,0x57b0,0x87dc},
{0x57b1,0x57b1,0x87e2},
{0x57b2,0x57b2,0x87e4},
{0x57b3,0x57b3,0x87f1},
{0x57b4,0x57b4,0x87f3},
{0x57b5,0x57b5,0x87f8},
{0x57b6,0x57b6,0x87fa},
{0x57b7,0x57b7,0x87ff},
{0x57b8,0x57b8,0x8809},
{0x57b9,0x57b9,0x8819},
{0x57ba,0x57ba,0x8812},
{0x57bb,0x57bb,0x881a},
{0x57bc,0x57bc,0x881e},
{0x57bd,0x57bd,0x8830},
{0x57be,0x57be,0x8835},
{0x57bf,0x57bf,0x8841},
{0x57c0,0x57c0,0x8843},
{0x57c1,0x57c2,0x8848},
{0x57c3,0x57c3,0x884b},
{0x57c4,0x57c4,0x8851},
{0x57c5,0x57c5,0x885c},
{0x57c6,0x57c6,0x8860},
{0x57c7,0x57c7,0x8871},
{0x57c8,0x57c8,0x8879},
{0x57c9,0x57c9,0x887b},
{0x57ca,0x57ca,0x8880},
{0x57cb,0x57cb,0x889f},
{0x57cc,0x57cc,0x88a8},
{0x57cd,0x57cd,0x88ba},
{0x57ce,0x57cf,0x88cb},
{0x57d0,0x57d0,0x88de},
{0x57d1,0x57d1,0x88e7},
{0x57d2,0x57d2,0x88f7},
{0x57d3,0x57d3,0x890d},
{0x57d4,0x57d5,0x8915},
{0x57d6,0x57d6,0x8920},
{0x57d7,0x57d7,0x8928},
{0x57d8,0x57d8,0x8931},
{0x57d9,0x57d9,0x893a},
{0x57da,0x57da,0x8946},
{0x57db,0x57db,0x894f},
{0x57dc,0x57dc,0x8952},
{0x57dd,0x57dd,0x8957},
{0x57de,0x57de,0x895b},
{0x57df,0x57df,0x8961},
{0x57e0,0x57e0,0x8963},
{0x57e1,0x57e1,0x896e},
{0x57e2,0x57e2,0x8973},
{0x57e3,0x57e3,0x8975},
{0x57e4,0x57e4,0x897a},
{0x57e5,0x57e5,0x897d},
{0x57e6,0x57e6,0x898d},
{0x57e7,0x57e7,0x8995},
{0x57e8,0x57e9,0x899b},
{0x57ea,0x57ea,0x89a0},
{0x57eb,0x57eb,0x89b4},
{0x57ec,0x57ec,0x89b6},
{0x57ed,0x57ed,0x89d7},
{0x57ee,0x57ee,0x89e9},
{0x57ef,0x57ef,0x89ed},
{0x57f0,0x57f0,0x89f9},
{0x57f1,0x57f2,0x8a04},
{0x57f3,0x57f3,0x8a1e},
{0x57f4,0x57f4,0x8a20},
{0x57f5,0x57f5,0x8a24},
{0x57f6,0x57f6,0x8a26},
{0x57f7,0x57f8,0x8a2b},
{0x57f9,0x57f9,0x8a2f},
{0x57fa,0x57fa,0x8a3d},
{0x57fb,0x57fb,0x8a40},
{0x57fc,0x57fc,0x8a43},
{0x57fd,0x57fd,0x8a53},
{0x57fe,0x57fe,0x8a56},
{0x57ff,0x57ff,0x8a5c},
{0x5800,0x5800,0x8a65},
{0x5801,0x5802,0x8a76},
{0x5803,0x5804,0x8a7a},
{0x5805,0x5805,0x8a80},
{0x5806,0x5806,0x8a83},
{0x5807,0x5807,0x8a8b},
{0x5808,0x5808,0x8a8f},
{0x5809,0x5809,0x8a92},
{0x580a,0x580a,0x8a97},
{0x580b,0x580b,0x8a99},
{0x580c,0x580c,0x8a9f},
{0x580d,0x580d,0x8aa9},
{0x580e,0x580e,0x8aaf},
{0x580f,0x580f,0x8ab3},
{0x5810,0x5810,0x8abb},
{0x5811,0x5811,0x8ac3},
{0x5812,0x5812,0x8ac6},
{0x5813,0x5813,0x8ac8},
{0x5814,0x5814,0x8aca},
{0x5815,0x5817,0x8ad3},
{0x5818,0x5818,0x8af0},
{0x5819,0x5819,0x8aff},
{0x581a,0x581a,0x8b0b},
{0x581b,0x581b,0x8b1e},
{0x581c,0x581c,0x8b30},
{0x581d,0x581d,0x8b3c},
{0x581e,0x581e,0x8b42},
{0x581f,0x581f,0x8b48},
{0x5820,0x5820,0x8b4d},
{0x5821,0x5821,0x8b63},
{0x5822,0x5822,0x8b79},
{0x5823,0x5823,0x8b84},
{0x5824,0x5824,0x8b8d},
{0x5825,0x5825,0x8b8f},
{0x5826,0x5826,0x8c38},
{0x5827,0x5827,0x8c3e},
{0x5828,0x5828,0x8c51},
{0x5829,0x5829,0x8c58},
{0x582a,0x582a,0x8c5b},
{0x582b,0x582b,0x8c5d},
{0x582c,0x582c,0x8c59},
{0x582d,0x582e,0x8c63},
{0x582f,0x582f,0x8c66},
{0x5830,0x5831,0x8c75},
{0x5832,0x5832,0x8c7e},
{0x5833,0x5834,0x8c86},
{0x5835,0x5835,0x8c8b},
{0x5836,0x5836,0x8c90},
{0x5837,0x5838,0x8c9b},
{0x5839,0x5839,0x8cb9},
{0x583a,0x583a,0x8cc6},
{0x583b,0x583b,0x8ccb},
{0x583c,0x583c,0x8ccf},
{0x583d,0x583d,0x8cdd},
{0x583e,0x583e,0x8ce8},
{0x583f,0x583f,0x8cef},
{0x5840,0x5840,0x8cf2},
{0x5841,0x5841,0x8cff},
{0x5842,0x5842,0x8d65},
{0x5843,0x5843,0x8d7f},
{0x5844,0x5844,0x8d82},
{0x5845,0x5845,0x8d88},
{0x5846,0x5846,0x8d90},
{0x5847,0x5847,0x8d9e},
{0x5848,0x5848,0x8da0},
{0x5849,0x5849,0x8dac},
{0x584a,0x584a,0x8db5},
{0x584b,0x584b,0x8db7},
{0x584c,0x584c,0x8db9},
{0x584d,0x584d,0x8dbb},
{0x584e,0x584e,0x8dc0},
{0x584f,0x584f,0x8dc5},
{0x5850,0x5850,0x8dc7},
{0x5851,0x5851,0x8dca},
{0x5852,0x5852,0x8dd4},
{0x5853,0x5853,0x8de5},
{0x5854,0x5854,0x8df0},
{0x5855,0x5855,0x8dbc},
{0x5856,0x5857,0x8e04},
{0x5858,0x5858,0x8e11},
{0x5859,0x5859,0x8e33},
{0x585a,0x585b,0x8e37},
{0x585c,0x585c,0x8e4e},
{0x585d,0x585d,0x8e5b},
{0x585e,0x585f,0x8e5d},
{0x5860,0x5860,0x8e79},
{0x5861,0x5862,0x8e82},
{0x5863,0x5863,0x8e9b},
{0x5864,0x5864,0x8e9d},
{0x5865,0x5865,0x8ea2},
{0x5866,0x5866,0x8eba},
{0x5867,0x5867,0x8ec1},
{0x5868,0x5869,0x8ec3},
{0x586a,0x586a,0x8ec7},
{0x586b,0x586b,0x8edc},
{0x586c,0x586c,0x8eee},
{0x586d,0x586d,0x8ef1},
{0x586e,0x586e,0x8ef7},
{0x586f,0x586f,0x8eed},
{0x5870,0x5870,0x8f02},
{0x5871,0x5872,0x8f0f},
{0x5873,0x5873,0x8f16},
{0x5874,0x5875,0x8f20},
{0x5876,0x5876,0x8f23},
{0x5877,0x5877,0x8f28},
{0x5878,0x5878,0x8f2e},
{0x5879,0x5879,0x8f34},
{0x587a,0x587a,0x8f37},
{0x587b,0x587b,0x8f41},
{0x587c,0x587c,0x8f4f},
{0x587d,0x587e,0x8f52},
{0x587f,0x5880,0x8f5d},
{0x5881,0x5881,0x8f65},
{0x5882,0x5882,0x8f9d},
{0x5883,0x5883,0x8fb8},
{0x5884,0x5884,0x8fbe},
{0x5885,0x5885,0x8fc0},
{0x5886,0x5886,0x8fcb},
{0x5887,0x5887,0x8fd0},
{0x5888,0x5888,0x8fd2},
{0x5889,0x5889,0x8fe3},
{0x588a,0x588a,0x8ffe},
{0x588b,0x588b,0x9018},
{0x588c,0x588e,0x9028},
{0x588f,0x5890,0x9033},
{0x5891,0x5891,0x903f},
{0x5892,0x5892,0x9062},
{0x5893,0x5893,0x9066},
{0x5894,0x5894,0x906c},
{0x5895,0x5895,0x908e},
{0x5896,0x5896,0x90a5},
{0x5897,0x5897,0x90cc},
{0x5898,0x5898,0x90d5},
{0x5899,0x589a,0x90d8},
{0x589b,0x589b,0x90e5},
{0x589c,0x589c,0x90d2},
{0x589d,0x589d,0x9108},
{0x589e,0x589e,0x910d},
{0x589f,0x589f,0x9110},
{0x58a0,0x58a0,0x911a},
{0x58a1,0x58a1,0x9120},
{0x58a2,0x58a2,0x9129},
{0x58a3,0x58a3,0x912e},
{0x58a4,0x58a4,0x9136},
{0x58a5,0x58a5,0x913c},
{0x58a6,0x58a6,0x9143},
{0x58a7,0x58a7,0x914f},
{0x58a8,0x58a8,0x9153},
{0x58a9,0x58a9,0x916d},
{0x58aa,0x58aa,0x917b},
{0x58ab,0x58ab,0x9181},
{0x58ac,0x58ac,0x9186},
{0x58ad,0x58ae,0x9193},
{0x58af,0x58af,0x9198},
{0x58b0,0x58b0,0x91a1},
{0x58b1,0x58b1,0x91a6},
{0x58b2,0x58b2,0x91bf},
{0x58b3,0x58b4,0x91d3},
{0x58b5,0x58b5,0x91d9},
{0x58b6,0x58b7,0x91e9},
{0x58b8,0x58b8,0x91ef},
{0x58b9,0x58b9,0x91f9},
{0x58ba,0x58ba,0x91fd},
{0x58bb,0x58bc,0x9204},
{0x58bd,0x58bd,0x920c},
{0x58be,0x58bf,0x9212},
{0x58c0,0x58c0,0x9218},
{0x58c1,0x58c2,0x921c},
{0x58c3,0x58c5,0x9224},
{0x58c6,0x58c8,0x922e},
{0x58c9,0x58ca,0x9235},
{0x58cb,0x58cb,0x923e},
{0x58cc,0x58cc,0x9246},
{0x58cd,0x58cd,0x924d},
{0x58ce,0x58ce,0x9258},
{0x58cf,0x58d0,0x925c},
{0x58d1,0x58d1,0x9269},
{0x58d2,0x58d4,0x926e},
{0x58d5,0x58d5,0x9275},
{0x58d6,0x58d6,0x9279},
{0x58d7,0x58d7,0x927b},
{0x58d8,0x58d8,0x928a},
{0x58d9,0x58d9,0x9292},
{0x58da,0x58da,0x92a0},
{0x58db,0x58dc,0x92a4},
{0x58dd,0x58dd,0x92a8},
{0x58de,0x58de,0x92b6},
{0x58df,0x58df,0x92b8},
{0x58e0,0x58e0,0x92ba},
{0x58e1,0x58e1,0x92bd},
{0x58e2,0x58e2,0x92c7},
{0x58e3,0x58e3,0x92cd},
{0x58e4,0x58e4,0x92d8},
{0x58e5,0x58e6,0x92dc},
{0x58e7,0x58e7,0x92e1},
{0x58e8,0x58e8,0x92e3},
{0x58e9,0x58e9,0x92e8},
{0x58ea,0x58ea,0x92ec},
{0x58eb,0x58eb,0x92ee},
{0x58ec,0x58ec,0x92f0},
{0x58ed,0x58ed,0x9300},
{0x58ee,0x58ee,0x9308},
{0x58ef,0x58ef,0x931c},
{0x58f0,0x58f0,0x9324},
{0x58f1,0x58f1,0x932a},
{0x58f2,0x58f2,0x9334},
{0x58f3,0x58f3,0x9337},
{0x58f4,0x58f4,0x9350},
{0x58f5,0x58f5,0x9355},
{0x58f6,0x58f6,0x935e},
{0x58f7,0x58f7,0x9367},
{0x58f8,0x58f8,0x9369},
{0x58f9,0x58f9,0x936f},
{0x58fa,0x58fa,0x9374},
{0x58fb,0x58fb,0x9376},
{0x58fc,0x58fc,0x937a},
{0x58fd,0x58fd,0x937d},
{0x58fe,0x58ff,0x9380},
{0x5900,0x5900,0x938d},
{0x5901,0x5901,0x9392},
{0x5902,0x5902,0x9395},
{0x5903,0x5903,0x9398},
{0x5904,0x5904,0x93a1},
{0x5905,0x5905,0x93a6},
{0x5906,0x5906,0x93a8},
{0x5907,0x5907,0x93ab},
{0x5908,0x590a,0x93b4},
{0x590b,0x590c,0x93c4},
{0x590d,0x590d,0x93c9},
{0x590e,0x590e,0x93cb},
{0x590f,0x590f,0x93cd},
{0x5910,0x5910,0x93d3},
{0x5911,0x5911,0x93d9},
{0x5912,0x5912,0x93f7},
{0x5913,0x5913,0x9401},
{0x5914,0x5914,0x9408},
{0x5915,0x5915,0x9415},
{0x5916,0x5916,0x941f},
{0x5917,0x5917,0x942f},
{0x5918,0x5918,0x943d},
{0x5919,0x5919,0x9443},
{0x591a,0x591a,0x9459},
{0x591b,0x591b,0x945c},
{0x591c,0x591c,0x945f},
{0x591d,0x591d,0x9461},
{0x591e,0x591e,0x9468},
{0x591f,0x591f,0x946e},
{0x5920,0x5920,0x9484},
{0x5921,0x5921,0x9483},
{0x5922,0x5922,0x957e},
{0x5923,0x5923,0x9584},
{0x5924,0x5925,0x959d},
{0x5926,0x5926,0x95ba},
{0x5927,0x5927,0x95d9},
{0x5928,0x5928,0x95dd},
{0x5929,0x5929,0x95df},
{0x592a,0x592a,0x961e},
{0x592b,0x592b,0x9622},
{0x592c,0x592d,0x9625},
{0x592e,0x592e,0x9637},
{0x592f,0x5930,0x9639},
{0x5931,0x5931,0x9652},
{0x5932,0x5933,0x9656},
{0x5934,0x5934,0x966e},
{0x5935,0x5935,0x967c},
{0x5936,0x5936,0x967e},
{0x5937,0x5937,0x9691},
{0x5938,0x5938,0x969f},
{0x5939,0x5939,0x96a6},
{0x593a,0x593a,0x96ca},
{0x593b,0x593b,0x96da},
{0x593c,0x593c,0x96df},
{0x593d,0x593d,0x96fa},
{0x593e,0x593e,0x9705},
{0x593f,0x593f,0x971a},
{0x5940,0x5940,0x971d},
{0x5941,0x5941,0x9721},
{0x5942,0x5942,0x974a},
{0x5943,0x5943,0x9758},
{0x5944,0x5945,0x9777},
{0x5946,0x5946,0x977b},
{0x5947,0x5947,0x9780},
{0x5948,0x5948,0x9789},
{0x5949,0x5949,0x9797},
{0x594a,0x594a,0x97b8},
{0x594b,0x594b,0x97bc},
{0x594c,0x594c,0x97bf},
{0x594d,0x594e,0x97c4},
{0x594f,0x594f,0x97c7},
{0x5950,0x5950,0x97ca},
{0x5951,0x5951,0x97ce},
{0x5952,0x5952,0x97d0},
{0x5953,0x5953,0x97d7},
{0x5954,0x5954,0x97dd},
{0x5955,0x5955,0x97e4},
{0x5956,0x5957,0x97f7},
{0x5958,0x5958,0x97fa},
{0x5959,0x5959,0x9819},
{0x595a,0x595a,0x981c},
{0x595b,0x595b,0x9820},
{0x595c,0x595c,0x982f},
{0x595d,0x595d,0x9835},
{0x595e,0x595e,0x9844},
{0x595f,0x595f,0x984a},
{0x5960,0x5960,0x9851},
{0x5961,0x5961,0x986a},
{0x5962,0x5962,0x98ae},
{0x5963,0x5963,0x98cc},
{0x5964,0x5964,0x98e6},
{0x5965,0x5965,0x98f6},
{0x5966,0x5966,0x9907},
{0x5967,0x5967,0x991f},
{0x5968,0x5968,0x9922},
{0x5969,0x5969,0x9926},
{0x596a,0x596a,0x992b},
{0x596b,0x596b,0x9934},
{0x596c,0x596c,0x9939},
{0x596d,0x596d,0x9947},
{0x596e,0x596e,0x9959},
{0x596f,0x596f,0x995b},
{0x5970,0x5970,0x999b},
{0x5971,0x5971,0x999d},
{0x5972,0x5972,0x999f},
{0x5973,0x5973,0x99b0},
{0x5974,0x5974,0x99b2},
{0x5975,0x5975,0x99b5},
{0x5976,0x5976,0x99d3},
{0x5977,0x5977,0x99da},
{0x5978,0x5978,0x99dc},
{0x5979,0x5979,0x99e7},
{0x597a,0x597c,0x99ea},
{0x597d,0x597e,0x99f4},
{0x597f,0x5980,0x99fd},
{0x5981,0x5981,0x9a04},
{0x5982,0x5982,0x9a0b},
{0x5983,0x5983,0x9a1e},
{0x5984,0x5984,0x9a22},
{0x5985,0x5985,0x9a33},
{0x5986,0x5986,0x9a35},
{0x5987,0x5987,0x9a47},
{0x5988,0x5989,0x9a4a},
{0x598a,0x598a,0x9a54},
{0x598b,0x598b,0x9a5d},
{0x598c,0x598c,0x9aaa},
{0x598d,0x598d,0x9aac},
{0x598e,0x598e,0x9aae},
{0x598f,0x598f,0x9ab2},
{0x5990,0x5990,0x9ab4},
{0x5991,0x5991,0x9abb},
{0x5992,0x5992,0x9abf},
{0x5993,0x5993,0x9ac8},
{0x5994,0x5994,0x9ad7},
{0x5995,0x5995,0x9adb},
{0x5996,0x5996,0x9ae4},
{0x5997,0x5997,0x9ae7},
{0x5998,0x5998,0x9aec},
{0x5999,0x599a,0x9af2},
{0x599b,0x599b,0x9af5},
{0x599c,0x599c,0x9afa},
{0x599d,0x599d,0x9afd},
{0x599e,0x599f,0x9aff},
{0x59a0,0x59a1,0x9b04},
{0x59a2,0x59a2,0x9b1b},
{0x59a3,0x59a3,0x9b26},
{0x59a4,0x59a4,0x9b35},
{0x59a5,0x59a5,0x9b37},
{0x59a6,0x59a7,0x9b39},
{0x59a8,0x59a8,0x9b4c},
{0x59a9,0x59aa,0x9b56},
{0x59ab,0x59ab,0x9b5b},
{0x59ac,0x59ac,0x9b61},
{0x59ad,0x59ad,0x9b6a},
{0x59ae,0x59af,0x9b6d},
{0x59b0,0x59b0,0x9b78},
{0x59b1,0x59b1,0x9b7f},
{0x59b2,0x59b2,0x9b85},
{0x59b3,0x59b3,0x9b87},
{0x59b4,0x59b4,0x9b89},
{0x59b5,0x59b5,0x9b8b},
{0x59b6,0x59b6,0x9b8d},
{0x59b7,0x59b7,0x9b94},
{0x59b8,0x59b8,0x9b9a},
{0x59b9,0x59b9,0x9ba9},
{0x59ba,0x59ba,0x9bb7},
{0x59bb,0x59bb,0x9bbc},
{0x59bc,0x59bc,0x9bd0},
{0x59bd,0x59bd,0x9bf9},
{0x59be,0x59be,0x9c0f},
{0x59bf,0x59bf,0x9c11},
{0x59c0,0x59c0,0x9c1e},
{0x59c1,0x59c1,0x9c26},
{0x59c2,0x59c2,0x9c28},
{0x59c3,0x59c3,0x9c3d},
{0x59c4,0x59c4,0x9c43},
{0x59c5,0x59c5,0x9c4e},
{0x59c6,0x59c6,0x9c5e},
{0x59c7,0x59c7,0x9c7b},
{0x59c8,0x59c8,0x9cf7},
{0x59c9,0x59c9,0x9cf9},
{0x59ca,0x59ca,0x9d1c},
{0x59cb,0x59cb,0x9d2f},
{0x59cc,0x59cd,0x9d33},
{0x59ce,0x59ce,0x9d3a},
{0x59cf,0x59cf,0x9d3c},
{0x59d0,0x59d0,0x9d45},
{0x59d1,0x59d2,0x9d53},
{0x59d3,0x59d3,0x9d65},
{0x59d4,0x59d4,0x9d83},
{0x59d5,0x59d5,0x9d86},
{0x59d6,0x59d6,0x9d8e},
{0x59d7,0x59d8,0x9d92},
{0x59d9,0x59d9,0x9d95},
{0x59da,0x59db,0x9d97},
{0x59dc,0x59dc,0x9daa},
{0x59dd,0x59dd,0x9dae},
{0x59de,0x59de,0x9dbf},
{0x59df,0x59df,0x9dca},
{0x59e0,0x59e1,0x9dd4},
{0x59e2,0x59e2,0x9dde},
{0x59e3,0x59e3,0x9dee},
{0x59e4,0x59e4,0x9df0},
{0x59e5,0x59e5,0x9dfe},
{0x59e6,0x59e6,0x9e0e},
{0x59e7,0x59e9,0x9e10},
{0x59ea,0x59ea,0x9e16},
{0x59eb,0x59eb,0x9e87},
{0x59ec,0x59ed,0x9e8e},
{0x59ee,0x59ee,0x9e96},
{0x59ef,0x59ef,0x9e98},
{0x59f0,0x59f0,0x9eae},
{0x59f1,0x59f1,0x9eb3},
{0x59f2,0x59f2,0x9ec6},
{0x59f3,0x59f3,0x9ec8},
{0x59f4,0x59f4,0x9ecb},
{0x59f5,0x59f5,0x9ed5},
{0x59f6,0x59f7,0x9eec},
{0x59f8,0x59f8,0x9ef1},
{0x59f9,0x59f9,0x9ef5},
{0x59fa,0x59fa,0x9ef8},
{0x59fb,0x59fb,0x9f11},
{0x59fc,0x59fc,0x9f16},
{0x59fd,0x59fd,0x9f1a},
{0x59fe,0x59fe,0x9f1f},
{0x59ff,0x5a00,0x9f31},
{0x5a01,0x5a01,0x9f3c},
{0x5a02,0x5a02,0x9f3f},
{0x5a03,0x5a04,0x9f43},
{0x5a05,0x5a05,0x9f47},
{0x5a06,0x5a06,0x9f56},
{0x5a07,0x5a07,0x9f5e},
{0x5a08,0x5a08,0x9f6e},
{0x5a09,0x5a09,0x9f73},
{0x5a0a,0x5a0a,0x9f7a},
{0x5a0b,0x5a0b,0x9f7d},
{0x5a0c,0x5a0c,0x9f8f},
{0x5a0d,0x5a0e,0x9f91},
{0x5a0f,0x5a0f,0x9f96},
{0x5a10,0x5a10,0x9fa1},
{0x5a11,0x5a11,0x9fa3},
};

static const pdf_xrange cmap_Adobe_Japan1_UCS2_xranges[] = {
{0x1dd9,0x1dd9,0x28cdd},
{0x1df1,0x1df1,0x2f8ed},
{0x1df6,0x1df6,0x25874},
{0x1df9,0x1df9,0x28ef6},
{0x1e0f,0x1e0f,0x2f8dc},
{0x1e36,0x1e36,0x2f884},
{0x1e4a,0x1e4a,0x2f877},
{0x1e86,0x1e86,0x2f80f},
{0x1e88,0x1e88,0x2f8d3},
{0x1e89,0x1e89,0x2f818},
{0x1e91,0x1e91,0x21a1a},
{0x1e9e,0x1e9e,0x243d0},
{0x1e9f,0x1e9f,0x2f920},
{0x215e,0x215e,0x20611},
{0x342d,0x342d,0x2f945},
{0x34d3,0x34d3,0x2090e},
{0x3558,0x3558,0x2f8fc},
{0x3566,0x3566,0x2f995},
{0x356f,0x356f,0x2f8ea},
{0x3574,0x3574,0x2f822},
{0x358a,0x358a,0x20bb7},
{0x3595,0x3595,0x29d4b},
{0x3596,0x3596,0x29d4b},
{0x3597,0x3597,0x2f833},
{0x359b,0x359b,0x2363a},
{0x359c,0x359c,0x2363a},
{0x35b6,0x35b6,0x2f8ac},
{0x35bb,0x35bb,0x20a64},
{0x35c8,0x35c8,0x2f903},
{0x35e9,0x35e9,0x2f90b},
{0x35eb,0x35eb,0x20b9f},
{0x35ef,0x35ef,0x2f828},
{0x35f1,0x35f1,0x2f921},
{0x35f7,0x35f7,0x2f83f},
{0x3608,0x3608,0x2f873},
{0x360f,0x360f,0x2000b},
{0x3611,0x3611,0x2f852},
{0x361e,0x361e,0x2f947},
{0x3621,0x3621,0x201a2},
{0x362b,0x362b,0x2f8b2},
{0x3650,0x3650,0x23cfe},
{0x365c,0x365c,0x2f91a},
{0x3668,0x3668,0x2f89a},
{0x366c,0x366c,0x2f90f},
{0x3681,0x3681,0x2123d},
{0x3682,0x3682,0x2f81a},
{0x36ae,0x36ae,0x2f862},
{0x36f4,0x36f4,0x2f9d0},
{0x36f5,0x36f5,0x2f9df},
{0x36fb,0x36fb,0x2567f},
{0x3714,0x3714,0x266b0},
{0x371c,0x371c,0x2008a},
{0x371d,0x371d,0x2f82c},
{0x3729,0x3729,0x2f86d},
{0x3731,0x3731,0x2f8b6},
{0x3736,0x3736,0x26999},
{0x373c,0x373c,0x2f8db},
{0x3764,0x3764,0x2f96c},
{0x376e,0x376e,0x26270},
{0x3781,0x3781,0x200b0},
{0x37ad,0x37ad,0x28987},
{0x37b0,0x37b0,0x28e17},
{0x37c8,0x37c8,0x2a61a},
{0x37c9,0x37c9,0x242ee},
{0x37ca,0x37ca,0x242ee},
{0x37d3,0x37d3,0x2f8e1},
{0x37d5,0x37d5,0x23cbe},
{0x37d6,0x37d6,0x20611},
{0x3ba5,0x3ba5,0x2f9f4},
{0x3c1c,0x3c1c,0x2f804},
{0x3c21,0x3c21,0x2363a},
{0x3c3e,0x3c3e,0x233fe},
{0x3c44,0x3c44,0x235c4},
{0x3c4d,0x3c4d,0x29e3d},
{0x3c53,0x3c53,0x22609},
{0x41b0,0x41b0,0x2131b},
{0x41b5,0x41b5,0x2146e},
{0x41c1,0x41c1,0x218bd},
{0x41c6,0x41c6,0x216b4},
{0x41cd,0x41cd,0x21e34},
{0x41f8,0x41f8,0x231c4},
{0x4209,0x4209,0x235c4},
{0x4212,0x4212,0x2373f},
{0x4214,0x4214,0x23763},
{0x424a,0x424a,0x247f1},
{0x426d,0x426d,0x2548e},
{0x4271,0x4271,0x2550e},
{0x427a,0x427a,0x25771},
{0x4280,0x4280,0x259c4},
{0x4289,0x4289,0x25da1},
{0x42a7,0x42a7,0x26aff},
{0x42c1,0x42c1,0x26e40},
{0x42cf,0x42cf,0x270f4},
{0x42dd,0x42dd,0x27684},
{0x42f4,0x42f4,0x28277},
{0x42fa,0x42fa,0x283cd},
{0x434b,0x434b,0x2a190},
{0x4351,0x4351,0x20089},
{0x4358,0x4358,0x200a2},
{0x435b,0x435b,0x200a4},
{0x436b,0x436b,0x20213},
{0x4382,0x4382,0x2032b},
{0x4389,0x4389,0x20381},
{0x438b,0x438b,0x20371},
{0x438f,0x438f,0x203f9},
{0x4391,0x4391,0x2044a},
{0x4393,0x4393,0x20509},
{0x439c,0x439c,0x205d6},
{0x43a0,0x43a0,0x2074f},
{0x43a7,0x43a7,0x20807},
{0x43a9,0x43a9,0x2083a},
{0x43af,0x43af,0x208b9},
{0x43b3,0x43b3,0x2097c},
{0x43b4,0x43b4,0x2099d},
{0x43b9,0x43b9,0x20ad3},
{0x43bc,0x43bc,0x20b1d},
{0x43cf,0x43cf,0x20d45},
{0x43dd,0x43dd,0x20de1},
{0x43e3,0x43e3,0x20e95},
{0x43e4,0x43e4,0x20e6d},
{0x43ec,0x43ec,0x20e64},
{0x43ef,0x43ef,0x20f5f},
{0x4406,0x4406,0x21201},
{0x4407,0x4407,0x21255},
{0x4409,0x4409,0x2127b},
{0x440d,0x440d,0x21274},
{0x4414,0x4414,0x212e4},
{0x4415,0x4415,0x212d7},
{0x441b,0x441b,0x212fd},
{0x441d,0x441d,0x21336},
{0x441e,0x441e,0x21344},
{0x4429,0x4429,0x213c4},
{0x4436,0x4436,0x2146d},
{0x4440,0x4440,0x215d7},
{0x4446,0x4446,0x26c29},
{0x4448,0x4448,0x21647},
{0x4454,0x4454,0x21706},
{0x4455,0x4455,0x21742},
{0x4475,0x4475,0x219c3},
{0x4483,0x4483,0x21c56},
{0x4488,0x4488,0x21d2d},
{0x4489,0x4489,0x21d45},
{0x448a,0x448a,0x21d78},
{0x448b,0x448b,0x21d62},
{0x448f,0x448f,0x21da1},
{0x4490,0x4490,0x21d9c},
{0x4494,0x4494,0x21d92},
{0x4497,0x4497,0x21db7},
{0x4499,0x4499,0x21de0},
{0x449a,0x449a,0x21e33},
{0x44a7,0x44a7,0x21f1e},
{0x44ae,0x44ae,0x21f76},
{0x44b1,0x44b1,0x21ffa},
{0x44bf,0x44bf,0x2217b},
{0x44c5,0x44c5,0x2231e},
{0x44c8,0x44c8,0x223ad},
{0x44e0,0x44e0,0x226f3},
{0x44ef,0x44ef,0x2285b},
{0x44f5,0x44f5,0x228ab},
{0x44f9,0x44f9,0x2298f},
{0x4503,0x4503,0x22ab8},
{0x4507,0x4508,0x22b4f},
{0x4510,0x4510,0x22b46},
{0x4512,0x4512,0x22c1d},
{0x4513,0x4513,0x22ba6},
{0x4516,0x4516,0x22c24},
{0x452e,0x452e,0x22de1},
{0x454e,0x454e,0x231c3},
{0x454f,0x454f,0x231f5},
{0x4550,0x4550,0x231b6},
{0x4561,0x4561,0x23372},
{0x4563,0x4563,0x233d3},
{0x4564,0x4564,0x233d2},
{0x4568,0x4568,0x233d0},
{0x4569,0x4569,0x233e4},
{0x456a,0x456a,0x233d5},
{0x456c,0x456c,0x233da},
{0x456e,0x456e,0x233df},
{0x4576,0x4576,0x2344a},
{0x4577,0x4577,0x23451},
{0x4578,0x4578,0x2344b},
{0x457c,0x457c,0x23465},
{0x4596,0x4596,0x234e4},
{0x4597,0x4597,0x2355a},
{0x45a3,0x45a3,0x23594},
{0x45b1,0x45b1,0x23639},
{0x45b2,0x45b2,0x23647},
{0x45b3,0x45b3,0x23638},
{0x45be,0x45be,0x2371c},
{0x45c7,0x45c7,0x2370c},
{0x45cb,0x45cb,0x23764},
{0x45d2,0x45d2,0x237ff},
{0x45d3,0x45d3,0x237e7},
{0x45d8,0x45d8,0x23824},
{0x45dd,0x45dd,0x2383d},
{0x45e9,0x45e9,0x23a98},
{0x45f6,0x45f6,0x23c7f},
{0x4605,0x4605,0x23d00},
{0x4616,0x4616,0x23d40},
{0x4617,0x4617,0x23dfa},
{0x4618,0x4618,0x23df9},
{0x4619,0x4619,0x23dd3},
{0x463f,0x463f,0x23f7e},
{0x464e,0x464e,0x24096},
{0x4653,0x4653,0x24103},
{0x465f,0x465f,0x241c6},
{0x4662,0x4662,0x241fe},
{0x4677,0x4677,0x243bc},
{0x4681,0x4681,0x24629},
{0x4687,0x4687,0x246a5},
{0x469d,0x469d,0x24896},
{0x46b8,0x46b8,0x24a4d},
{0x46c5,0x46c5,0x24b56},
{0x46c7,0x46c7,0x24b6f},
{0x46cc,0x46cc,0x24c16},
{0x46ee,0x46ee,0x24e0e},
{0x46f2,0x46f2,0x24e37},
{0x46f7,0x46f7,0x24e6a},
{0x46fa,0x46fa,0x24e8b},
{0x4705,0x4705,0x2504a},
{0x4707,0x4707,0x25055},
{0x4709,0x4709,0x25122},
{0x470e,0x470e,0x251a9},
{0x4710,0x4710,0x251e5},
{0x4711,0x4711,0x251cd},
{0x4713,0x4713,0x2521e},
{0x4715,0x4715,0x2524c},
{0x4721,0x4721,0x2542e},
{0x4729,0x4729,0x254d9},
{0x4735,0x4735,0x255a7},
{0x4748,0x4748,0x257a9},
{0x4749,0x4749,0x257b4},
{0x475c,0x475c,0x259d4},
{0x4764,0x4764,0x25ae4},
{0x4765,0x4765,0x25ae3},
{0x4766,0x4766,0x25af1},
{0x4775,0x4775,0x25bb2},
{0x477e,0x477e,0x25c4b},
{0x477f,0x477f,0x25c64},
{0x478e,0x478e,0x25e2e},
{0x478f,0x478f,0x25e56},
{0x4790,0x4790,0x25e65},
{0x4792,0x4792,0x25e62},
{0x4795,0x4795,0x25ed8},
{0x4797,0x4797,0x25ec2},
{0x4799,0x4799,0x25ee8},
{0x479a,0x479a,0x25f23},
{0x479c,0x479c,0x25f5c},
{0x47a2,0x47a2,0x25fe0},
{0x47a3,0x47a3,0x25fd4},
{0x47a8,0x47a8,0x2600c},
{0x47a9,0x47a9,0x25ffb},
{0x47b0,0x47b0,0x26017},
{0x47b3,0x47b3,0x26060},
{0x47bd,0x47bd,0x260ed},
{0x47d1,0x47d1,0x26270},
{0x47d2,0x47d2,0x26286},
{0x47da,0x47da,0x23d0e},
{0x47de,0x47de,0x26402},
{0x47f0,0x47f0,0x2667e},
{0x47fe,0x47fe,0x2671d},
{0x480c,0x480c,0x268dd},
{0x480e,0x480e,0x268ea},
{0x4812,0x4812,0x2696f},
{0x4814,0x4814,0x269dd},
{0x4817,0x4817,0x26a1e},
{0x481b,0x481b,0x26a58},
{0x481f,0x481f,0x26a8c},
{0x4822,0x4822,0x26ab7},
{0x484a,0x484a,0x26c73},
{0x4853,0x4853,0x26cdd},
{0x4860,0x4860,0x26e65},
{0x4870,0x4870,0x26f94},
{0x4879,0x4879,0x26ff8},
{0x487a,0x487b,0x26ff6},
{0x488b,0x488b,0x2710d},
{0x488e,0x488e,0x27139},
{0x48b2,0x48b2,0x273db},
{0x48b3,0x48b3,0x273da},
{0x48b9,0x48b9,0x273fe},
{0x48bc,0x48bc,0x27410},
{0x48c0,0x48c0,0x27449},
{0x48cd,0x48cd,0x27615},
{0x48ce,0x48ce,0x27614},
{0x48d0,0x48d0,0x27631},
{0x48d5,0x48d5,0x27693},
{0x48da,0x48da,0x2770e},
{0x48dc,0x48dc,0x27723},
{0x48e0,0x48e0,0x27752},
{0x48f0,0x48f0,0x27985},
{0x48fc,0x48fc,0x27a84},
{0x490b,0x490b,0x27bb3},
{0x490d,0x490d,0x27bbe},
{0x490e,0x490e,0x27bc7},
{0x4914,0x4914,0x27cb8},
{0x491c,0x491c,0x27da0},
{0x491e,0x491e,0x27e10},
{0x4927,0x4927,0x2808a},
{0x492d,0x492d,0x280bb},
{0x4939,0x4939,0x28282},
{0x493b,0x493b,0x282f3},
{0x4942,0x4942,0x2840c},
{0x4945,0x4945,0x28455},
{0x4952,0x4952,0x2856b},
{0x4955,0x4956,0x285c8},
{0x4960,0x4960,0x286d7},
{0x4963,0x4963,0x286fa},
{0x497b,0x497b,0x28949},
{0x497c,0x497c,0x28946},
{0x4981,0x4981,0x2896b},
{0x4988,0x4988,0x28988},
{0x4992,0x4993,0x289ba},
{0x499b,0x499b,0x28a1e},
{0x499c,0x499c,0x28a29},
{0x499f,0x499f,0x28a71},
{0x49a0,0x49a0,0x28a43},
{0x49a7,0x49a7,0x28a99},
{0x49a8,0x49a8,0x28acd},
{0x49ae,0x49ae,0x28ae4},
{0x49af,0x49af,0x28add},
{0x49ba,0x49ba,0x28bc1},
{0x49bb,0x49bb,0x28bef},
{0x49c2,0x49c2,0x28d10},
{0x49c3,0x49c3,0x28d71},
{0x49c5,0x49c5,0x28dfb},
{0x49c6,0x49c6,0x28e1f},
{0x49ca,0x49ca,0x28e36},
{0x49cd,0x49cd,0x28e89},
{0x49cf,0x49cf,0x28eeb},
{0x49d1,0x49d1,0x28f32},
{0x49d7,0x49d7,0x28ff8},
{0x49e5,0x49e5,0x292a0},
{0x49e6,0x49e6,0x292b1},
{0x49f7,0x49f7,0x29490},
{0x4a00,0x4a00,0x295cf},
{0x4a0f,0x4a0f,0x296f0},
{0x4a12,0x4a12,0x29719},
{0x4a16,0x4a16,0x29750},
{0x4a27,0x4a27,0x298c6},
{0x4a39,0x4a39,0x29a72},
{0x4a52,0x4a52,0x29ddb},
{0x4a5c,0x4a5c,0x29e15},
{0x4a5d,0x4a5d,0x29e8a},
{0x4a5e,0x4a5e,0x29e49},
{0x4a66,0x4a66,0x29ec4},
{0x4a6b,0x4a6b,0x29ee9},
{0x4a6e,0x4a6e,0x29edb},
{0x4a7f,0x4a7f,0x29fd7},
{0x4a83,0x4a83,0x2a02f},
{0x4a85,0x4a85,0x2a01a},
{0x4a8b,0x4a8b,0x2a0f9},
{0x4a8c,0x4a8c,0x2a082},
{0x4aa1,0x4aa1,0x22218},
{0x4aa5,0x4aa5,0x2a38c},
{0x4aa7,0x4aa7,0x2a437},
{0x4ab3,0x4ab3,0x2a5f1},
{0x4ab5,0x4ab5,0x2a602},
{0x4ab9,0x4ab9,0x2a6b2},
{0x4e59,0x4e59,0x200f5},
{0x4e5a,0x4e5a,0x24e04},
{0x4e5b,0x4e5b,0x24ff2},
{0x4e5c,0x4e5c,0x27d73},
{0x4e5d,0x4e5d,0x2f815},
{0x4e5e,0x4e5e,0x2f846},
{0x4e5f,0x4e5f,0x2f899},
{0x4e60,0x4e60,0x2f8a6},
{0x4e61,0x4e61,0x2f8e5},
{0x4e62,0x4e62,0x2f9de},
{0x4e68,0x4e68,0x2a2b2},
{0x4e6b,0x4e6b,0x20158},
{0x4e70,0x4e70,0x205b1},
{0x4e73,0x4e73,0x206ec},
{0x4e7a,0x4e7a,0x20d58},
{0x4e90,0x4e90,0x259cc},
{0x4e9c,0x4e9c,0x22e42},
{0x4ea2,0x4ea2,0x22feb},
{0x4ea5,0x4ea5,0x279b4},
{0x4ec8,0x4ec8,0x2404b},
{0x4eee,0x4eee,0x26c9e},
{0x4efc,0x4efc,0x27c3c},
{0x4f26,0x4f26,0x2383d},
{0x4f57,0x4f57,0x2634c},
{0x4f5b,0x4f5b,0x29e3d},
{0x4f5c,0x4f5c,0x2a61a},
};

static const pdf_mrange cmap_Adobe_Japan1_UCS2_mranges[] = {
{0x1f7d,0x0},
{0x1ffd,0x3},
{0x1ffe,0x7},
{0x2067,0xb},
{0x2068,0x10},
{0x2069,0x14},
{0x206c,0x17},
{0x206d,0x1c},
{0x206e,0x20},
{0x2072,0x23},
{0x2078,0x26},
{0x2081,0x29},
{0x2082,0x2e},
{0x2085,0x33},
{0x2086,0x38},
{0x24b4,0x3d},
{0x24b7,0x40},
{0x24ba,0x43},
{0x24c0,0x46},
{0x24c3,0x49},
{0x24c6,0x4c},
{0x25f4,0x4f},
{0x25f7,0x52},
{0x25fa,0x55},
{0x2600,0x58},
{0x2603,0x5b},
{0x2606,0x5e},
{0x2634,0x61},
{0x263e,0x65},
{0x263f,0x69},
{0x2640,0x6d},
{0x2641,0x71},
{0x2642,0x75},
{0x2643,0x79},
{0x2648,0x7d},
{0x2649,0x81},
{0x264a,0x85},
{0x264b,0x89},
{0x264c,0x8d},
{0x264d,0x91},
{0x264e,0x95},
{0x264f,0x9a},
{0x2650,0x9f},
{0x2651,0xa4},
{0x2652,0xa9},
{0x2653,0xae},
{0x2654,0xb3},
{0x2655,0xb8},
{0x2656,0xbd},
{0x2657,0xc2},
{0x2658,0xc7},
{0x2659,0xcc},
{0x265a,0xd1},
{0x265b,0xd6},
{0x265c,0xdc},
{0x265d,0xe1},
{0x265e,0xe6},
{0x265f,0xeb},
{0x2660,0xf1},
{0x2661,0xf7},
{0x2662,0xfb},
{0x2663,0xff},
{0x2664,0x103},
{0x2665,0x107},
{0x2666,0x10b},
{0x2667,0x10f},
{0x2668,0x113},
{0x2669,0x117},
{0x266a,0x11b},
{0x266b,0x11f},
{0x266c,0x123},
{0x266d,0x127},
{0x266e,0x12b},
{0x266f,0x12f},
{0x2670,0x133},
{0x2671,0x137},
{0x2672,0x13b},
{0x2673,0x13f},
{0x2674,0x143},
{0x2675,0x147},
{0x2676,0x14b},
{0x2677,0x14f},
{0x2678,0x153},
{0x2679,0x157},
{0x267a,0x15b},
{0x267b,0x15f},
{0x267c,0x163},
{0x267d,0x167},
{0x267e,0x16c},
{0x267f,0x171},
{0x2680,0x176},
{0x2681,0x17b},
{0x2682,0x180},
{0x2683,0x185},
{0x2684,0x18a},
{0x2685,0x18f},
{0x2686,0x194},
{0x2687,0x199},
{0x2688,0x19e},
{0x2689,0x1a3},
{0x268a,0x1a8},
{0x268b,0x1ae},
{0x268c,0x1b3},
{0x268d,0x1b8},
{0x268e,0x1bd},
{0x268f,0x1c3},
{0x269c,0x1c9},
{0x269d,0x1cc},
{0x269e,0x1cf},
{0x269f,0x1d2},
{0x26a0,0x1d5},
{0x26a1,0x1d8},
{0x26a2,0x1db},
{0x26a3,0x1de},
{0x26a4,0x1e1},
{0x26a5,0x1e4},
{0x26a6,0x1e7},
{0x26a7,0x1ea},
{0x26a8,0x1ed},
{0x26a9,0x1f0},
{0x26aa,0x1f3},
{0x26ab,0x1f6},
{0x26ac,0x1f9},
{0x26ad,0x1fc},
{0x26ae,0x1ff},
{0x26af,0x202},
{0x26b0,0x205},
{0x26b1,0x208},
{0x26b2,0x20b},
{0x26b3,0x20e},
{0x26b4,0x211},
{0x26b5,0x214},
{0x26b6,0x217},
{0x26b7,0x21a},
{0x26b8,0x21d},
{0x26b9,0x220},
{0x26ba,0x223},
{0x26bb,0x226},
{0x26bc,0x229},
{0x26bd,0x22c},
{0x26be,0x22f},
{0x26bf,0x232},
{0x26c0,0x235},
{0x26c1,0x238},
{0x26c2,0x23b},
{0x26c3,0x23e},
{0x26c4,0x241},
{0x26c5,0x244},
{0x26c6,0x247},
{0x26c7,0x24a},
{0x26c8,0x24d},
{0x26c9,0x250},
{0x26ca,0x253},
{0x26cb,0x256},
{0x26cc,0x259},
{0x26cd,0x25c},
{0x26ce,0x25f},
{0x26cf,0x262},
{0x26d0,0x265},
{0x26d1,0x268},
{0x26d2,0x26b},
{0x26d3,0x26e},
{0x26d4,0x271},
{0x26d5,0x274},
{0x26d6,0x277},
{0x26d7,0x27a},
{0x26d8,0x27d},
{0x26d9,0x280},
{0x26da,0x283},
{0x26db,0x286},
{0x26dc,0x289},
{0x26dd,0x28c},
{0x26de,0x28f},
{0x26df,0x292},
{0x26e0,0x295},
{0x26e1,0x298},
{0x26e2,0x29b},
{0x26e3,0x29e},
{0x26e4,0x2a1},
{0x26e5,0x2a4},
{0x26e6,0x2a7},
{0x26e7,0x2aa},
{0x26e8,0x2ad},
{0x26e9,0x2b0},
{0x26ea,0x2b3},
{0x26eb,0x2b6},
{0x26ec,0x2b9},
{0x26ed,0x2bc},
{0x26ee,0x2bf},
{0x26ef,0x2c2},
{0x26f0,0x2c5},
{0x26f1,0x2c8},
{0x26f2,0x2cb},
{0x26f3,0x2ce},
{0x26f4,0x2d1},
{0x26f5,0x2d4},
{0x2702,0x2d8},
{0x2703,0x2dd},
{0x2704,0x2e1},
{0x2711,0x2e4},
{0x2712,0x2e9},
{0x2713,0x2ed},
{0x2798,0x2f0},
{0x2799,0x2f3},
{0x279a,0x2f6},
{0x279b,0x2f9},
{0x279c,0x2fc},
{0x279d,0x2ff},
{0x279e,0x302},
{0x279f,0x305},
{0x27a0,0x308},
{0x27a1,0x30b},
{0x27f9,0x30e},
{0x27fa,0x311},
{0x2804,0x314},
{0x2805,0x317},
{0x2806,0x31a},
{0x2807,0x31d},
{0x2808,0x320},
{0x2809,0x323},
{0x280a,0x326},
{0x280b,0x329},
{0x280c,0x32c},
{0x280d,0x32f},
{0x280e,0x332},
{0x280f,0x335},
{0x2810,0x338},
{0x2811,0x33b},
{0x2812,0x33e},
{0x2813,0x341},
{0x2814,0x344},
{0x2815,0x347},
{0x2816,0x34a},
{0x2817,0x34d},
{0x2818,0x350},
{0x2819,0x353},
{0x281a,0x356},
{0x281b,0x359},
{0x281c,0x35c},
{0x281d,0x35f},
{0x281e,0x362},
{0x281f,0x365},
{0x2820,0x368},
{0x2821,0x36b},
{0x2822,0x36e},
{0x2823,0x371},
{0x2824,0x374},
{0x2825,0x377},
{0x2826,0x37a},
{0x2827,0x37d},
{0x2828,0x380},
{0x2829,0x383},
{0x282a,0x386},
{0x282b,0x389},
{0x282c,0x38c},
{0x282d,0x38f},
{0x282e,0x392},
{0x282f,0x395},
{0x2830,0x398},
{0x2831,0x39b},
{0x2832,0x39e},
{0x2833,0x3a1},
{0x2834,0x3a4},
{0x2835,0x3a7},
{0x2836,0x3aa},
{0x2837,0x3ad},
{0x2838,0x3b0},
{0x2839,0x3b3},
{0x283a,0x3b6},
{0x283b,0x3b9},
{0x283c,0x3bc},
{0x283d,0x3bf},
{0x283e,0x3c2},
{0x283f,0x3c5},
{0x2840,0x3c8},
{0x2841,0x3cb},
{0x2842,0x3ce},
{0x2843,0x3d1},
{0x2844,0x3d4},
{0x2845,0x3d7},
{0x2846,0x3da},
{0x2847,0x3dd},
{0x2848,0x3e0},
{0x2908,0x3e4},
{0x2909,0x3e7},
{0x290a,0x3ea},
{0x290b,0x3ed},
{0x290c,0x3f0},
{0x290d,0x3f3},
{0x290e,0x3f6},
{0x290f,0x3f9},
{0x2910,0x3fc},
{0x2911,0x3ff},
{0x2912,0x402},
{0x2913,0x405},
{0x2914,0x408},
{0x2915,0x40b},
{0x2916,0x40e},
{0x2917,0x411},
{0x2918,0x414},
{0x2919,0x417},
{0x291a,0x41a},
{0x291b,0x41d},
{0x291c,0x420},
{0x291d,0x423},
{0x291e,0x426},
{0x291f,0x429},
{0x2920,0x42c},
{0x2921,0x42f},
{0x2922,0x432},
{0x2923,0x435},
{0x2924,0x438},
{0x2925,0x43b},
{0x2926,0x43e},
{0x2927,0x441},
{0x2928,0x444},
{0x2929,0x447},
{0x292a,0x44a},
{0x292b,0x44d},
{0x292c,0x450},
{0x292d,0x453},
{0x292e,0x456},
{0x292f,0x459},
{0x2930,0x45c},
{0x2931,0x45f},
{0x2932,0x462},
{0x2933,0x465},
{0x2934,0x468},
{0x2935,0x46b},
{0x2936,0x46e},
{0x2937,0x471},
{0x2938,0x474},
{0x2939,0x477},
{0x293a,0x47a},
{0x293b,0x47d},
{0x293c,0x480},
{0x293d,0x483},
{0x293e,0x486},
{0x293f,0x489},
{0x2940,0x48c},
{0x2941,0x48f},
{0x2942,0x492},
{0x2943,0x495},
{0x2944,0x498},
{0x2945,0x49b},
{0x2946,0x49e},
{0x2947,0x4a1},
{0x2948,0x4a4},
{0x2949,0x4a7},
{0x294a,0x4aa},
{0x294b,0x4ad},
{0x294c,0x4b0},
{0x294d,0x4b3},
{0x294e,0x4b6},
{0x294f,0x4b9},
{0x2950,0x4bc},
{0x2951,0x4bf},
{0x2952,0x4c2},
{0x2953,0x4c5},
{0x2954,0x4c8},
{0x2955,0x4cb},
{0x2956,0x4ce},
{0x2957,0x4d1},
{0x2958,0x4d4},
{0x2959,0x4d7},
{0x295a,0x4da},
{0x295b,0x4dd},
{0x295c,0x4e0},
{0x295d,0x4e3},
{0x295e,0x4e6},
{0x295f,0x4e9},
{0x2960,0x4ec},
{0x2961,0x4ef},
{0x2962,0x4f2},
{0x2963,0x4f5},
{0x2964,0x4f8},
{0x2965,0x4fb},
{0x2966,0x4fe},
{0x2967,0x501},
{0x2968,0x504},
{0x2969,0x507},
{0x296a,0x50a},
{0x296b,0x50d},
{0x296c,0x510},
{0x2a0d,0x514},
{0x2a0f,0x517},
{0x2a11,0x51a},
{0x2a13,0x51d},
{0x2a15,0x520},
{0x2a17,0x523},
{0x2a19,0x526},
{0x2a1b,0x529},
{0x2a1d,0x52c},
{0x2a1f,0x52f},
{0x2a20,0x532},
{0x2a21,0x535},
{0x2a22,0x538},
{0x2a23,0x53b},
{0x2a24,0x53e},
{0x2a25,0x541},
{0x2a26,0x544},
{0x2a27,0x547},
{0x2a28,0x54a},
{0x2a29,0x54d},
{0x2a2a,0x550},
{0x2a2b,0x553},
{0x2a2c,0x556},
{0x2a2d,0x559},
{0x2a2e,0x55c},
{0x2a2f,0x55f},
{0x2a30,0x562},
{0x2a31,0x565},
{0x2a32,0x568},
{0x2a33,0x56b},
{0x2a34,0x56e},
{0x2a35,0x571},
{0x2a36,0x574},
{0x2a37,0x577},
{0x2a38,0x57a},
{0x2a39,0x57d},
{0x2a3a,0x580},
{0x2a3b,0x583},
{0x2a3c,0x586},
{0x2a3d,0x589},
{0x2a3e,0x58c},
{0x2a3f,0x58f},
{0x2a40,0x592},
{0x2a41,0x595},
{0x2a42,0x598},
{0x2a43,0x59b},
{0x2a44,0x59e},
{0x2a45,0x5a1},
{0x2a46,0x5a4},
{0x2a47,0x5a7},
{0x2a48,0x5aa},
{0x2a49,0x5ad},
{0x2a4a,0x5b0},
{0x2a4b,0x5b3},
{0x2a4c,0x5b6},
{0x2a4d,0x5b9},
{0x2a4e,0x5bc},
{0x2a4f,0x5bf},
{0x2a50,0x5c2},
{0x2a51,0x5c5},
{0x2a52,0x5c8},
{0x2a53,0x5cb},
{0x2a54,0x5ce},
{0x2a55,0x5d1},
{0x2a56,0x5d4},
{0x2a57,0x5d7},
{0x2a58,0x5da},
{0x2a59,0x5dd},
{0x2a5a,0x5e0},
{0x2a5b,0x5e3},
{0x2a5c,0x5e6},
{0x2a5d,0x5e9},
{0x2a5e,0x5ec},
{0x2a5f,0x5ef},
{0x2a60,0x5f2},
{0x2a61,0x5f5},
{0x2a62,0x5f8},
{0x2a63,0x5fb},
{0x2a64,0x5fe},
{0x2a65,0x601},
{0x2a66,0x604},
{0x2a67,0x607},
{0x2a68,0x60a},
{0x2a69,0x60d},
{0x2a6a,0x610},
{0x2a6b,0x613},
{0x2a6c,0x616},
{0x2a6d,0x619},
{0x2a6e,0x61c},
{0x2a6f,0x61f},
{0x2a70,0x622},
{0x2a71,0x625},
{0x2a72,0x628},
{0x2a73,0x62b},
{0x2a74,0x62e},
{0x2a75,0x631},
{0x2a76,0x634},
{0x2a77,0x637},
{0x2a78,0x63a},
{0x2a79,0x63d},
{0x2a7a,0x640},
{0x2b1e,0x644},
{0x2b20,0x647},
{0x2b22,0x64a},
{0x2b24,0x64d},
{0x2b26,0x650},
{0x2b28,0x653},
{0x2b2a,0x656},
{0x2b2c,0x659},
{0x2b2e,0x65c},
{0x2b30,0x65f},
{0x2b31,0x662},
{0x2b32,0x665},
{0x2b33,0x668},
{0x2b34,0x66b},
{0x2b35,0x66e},
{0x2b36,0x671},
{0x2b37,0x674},
{0x2b38,0x677},
{0x2b39,0x67a},
{0x2b3a,0x67d},
{0x2b3b,0x680},
{0x2b3c,0x683},
{0x2b3d,0x686},
{0x2b3e,0x689},
{0x2b3f,0x68c},
{0x2b40,0x68f},
{0x2b41,0x692},
{0x2b42,0x695},
{0x2b43,0x698},
{0x2b44,0x69b},
{0x2b45,0x69e},
{0x2b46,0x6a1},
{0x2b47,0x6a4},
{0x2b48,0x6a7},
{0x2b49,0x6aa},
{0x2b4a,0x6ad},
{0x2b4b,0x6b0},
{0x2b4c,0x6b3},
{0x2b4d,0x6b6},
{0x2b4e,0x6b9},
{0x2b4f,0x6bc},
{0x2b50,0x6bf},
{0x2b51,0x6c2},
{0x2b52,0x6c5},
{0x2b53,0x6c8},
{0x2b54,0x6cb},
{0x2b55,0x6ce},
{0x2b56,0x6d1},
{0x2b57,0x6d4},
{0x2b58,0x6d7},
{0x2b59,0x6da},
{0x2b5a,0x6dd},
{0x2b5b,0x6e0},
{0x2b5c,0x6e3},
{0x2b5d,0x6e6},
{0x2b5e,0x6e9},
{0x2b5f,0x6ec},
{0x2b60,0x6ef},
{0x2b61,0x6f2},
{0x2b62,0x6f5},
{0x2b63,0x6f8},
{0x2b64,0x6fb},
{0x2b65,0x6fe},
{0x2b66,0x701},
{0x2b67,0x704},
{0x2b68,0x707},
{0x2b69,0x70a},
{0x2b6a,0x70d},
{0x2b6b,0x710},
{0x2b6c,0x713},
{0x2b6d,0x716},
{0x2b6e,0x719},
{0x2b6f,0x71c},
{0x2b70,0x71f},
{0x2b71,0x722},
{0x2b72,0x725},
{0x2b73,0x728},
{0x2b74,0x72b},
{0x2b75,0x72e},
{0x2b76,0x731},
{0x2b77,0x734},
{0x2b78,0x737},
{0x2b79,0x73a},
{0x2b7a,0x73d},
{0x2b7b,0x740},
{0x2b7c,0x743},
{0x2b7d,0x746},
{0x2b7e,0x749},
{0x2b7f,0x74c},
{0x2b80,0x74f},
{0x2b81,0x752},
{0x2b82,0x755},
{0x2b83,0x758},
{0x2b84,0x75b},
{0x2b85,0x75e},
{0x2b86,0x761},
{0x2b87,0x764},
{0x2b88,0x767},
{0x2b89,0x76a},
{0x2b8a,0x76d},
{0x2b8b,0x770},
{0x2c2c,0x774},
{0x2c2e,0x777},
{0x2c30,0x77a},
{0x2c32,0x77d},
{0x2c34,0x780},
{0x2c36,0x783},
{0x2c38,0x786},
{0x2c3a,0x789},
{0x2c3c,0x78c},
{0x2c3e,0x78f},
{0x2c3f,0x792},
{0x2c40,0x795},
{0x2c41,0x798},
{0x2c42,0x79b},
{0x2c43,0x79e},
{0x2c44,0x7a1},
{0x2c45,0x7a4},
{0x2c46,0x7a7},
{0x2c47,0x7aa},
{0x2c48,0x7ad},
{0x2c49,0x7b0},
{0x2c4a,0x7b3},
{0x2c4b,0x7b6},
{0x2c4c,0x7b9},
{0x2c4d,0x7bc},
{0x2c4e,0x7bf},
{0x2c4f,0x7c2},
{0x2c50,0x7c5},
{0x2c51,0x7c8},
{0x2c52,0x7cb},
{0x2c53,0x7ce},
{0x2c54,0x7d1},
{0x2c55,0x7d4},
{0x2c56,0x7d7},
{0x2c57,0x7da},
{0x2c58,0x7dd},
{0x2c59,0x7e0},
{0x2c5a,0x7e3},
{0x2c5b,0x7e6},
{0x2c5c,0x7e9},
{0x2c5d,0x7ec},
{0x2c5e,0x7ef},
{0x2c5f,0x7f2},
{0x2c60,0x7f5},
{0x2c61,0x7f8},
{0x2c62,0x7fb},
{0x2c63,0x7fe},
{0x2c64,0x801},
{0x2c65,0x804},
{0x2c66,0x807},
{0x2c67,0x80a},
{0x2c68,0x80d},
{0x2c69,0x810},
{0x2c6a,0x813},
{0x2c6b,0x816},
{0x2c6c,0x819},
{0x2c6d,0x81c},
{0x2c6e,0x81f},
{0x2c6f,0x822},
{0x2c70,0x825},
{0x2c71,0x828},
{0x2c72,0x82b},
{0x2c73,0x82e},
{0x2c74,0x831},
{0x2c75,0x834},
{0x2c76,0x837},
{0x2c77,0x83a},
{0x2c78,0x83d},
{0x2c79,0x840},
{0x2c7a,0x843},
{0x2c7b,0x846},
{0x2c7c,0x849},
{0x2c7d,0x84c},
{0x2c7e,0x84f},
{0x2c7f,0x852},
{0x2c80,0x855},
{0x2c81,0x858},
{0x2c82,0x85b},
{0x2c83,0x85e},
{0x2c84,0x861},
{0x2c85,0x864},
{0x2c86,0x867},
{0x2c87,0x86a},
{0x2c88,0x86d},
{0x2c89,0x870},
{0x2c8a,0x873},
{0x2c8b,0x876},
{0x2c8c,0x879},
{0x2c8d,0x87c},
{0x2c8e,0x87f},
{0x2c8f,0x882},
{0x2c90,0x885},
{0x2c91,0x888},
{0x2c92,0x88b},
{0x2c93,0x88e},
{0x2c94,0x891},
{0x2c95,0x894},
{0x2c96,0x897},
{0x2c97,0x89a},
{0x2c98,0x89d},
{0x2c99,0x8a0},
{0x2d39,0x8a4},
{0x2d3b,0x8a7},
{0x2d3d,0x8aa},
{0x2d3f,0x8ad},
{0x2d41,0x8b0},
{0x2d43,0x8b3},
{0x2d45,0x8b6},
{0x2d47,0x8b9},
{0x2d49,0x8bc},
{0x2d4b,0x8bf},
{0x2d4c,0x8c2},
{0x2d4d,0x8c5},
{0x2d4e,0x8c8},
{0x2d4f,0x8cb},
{0x2d50,0x8ce},
{0x2d51,0x8d1},
{0x2d52,0x8d4},
{0x2d53,0x8d7},
{0x2d54,0x8da},
{0x2d55,0x8dd},
{0x2d56,0x8e0},
{0x2d57,0x8e3},
{0x2d58,0x8e6},
{0x2d59,0x8e9},
{0x2d5a,0x8ec},
{0x2d5b,0x8ef},
{0x2d5c,0x8f2},
{0x2d5d,0x8f5},
{0x2d5e,0x8f8},
{0x2d5f,0x8fb},
{0x2d60,0x8fe},
{0x2d61,0x901},
{0x2d62,0x904},
{0x2d63,0x907},
{0x2d64,0x90a},
{0x2d65,0x90d},
{0x2d66,0x910},
{0x2d67,0x913},
{0x2d68,0x916},
{0x2d69,0x919},
{0x2d6a,0x91c},
{0x2d6b,0x91f},
{0x2d6c,0x922},
{0x2d6d,0x925},
{0x2d6e,0x928},
{0x2d6f,0x92b},
{0x2d70,0x92e},
{0x2d71,0x931},
{0x2d72,0x934},
{0x2d73,0x937},
{0x2d74,0x93a},
{0x2d75,0x93d},
{0x2d76,0x940},
{0x2d77,0x943},
{0x2d78,0x946},
{0x2d79,0x949},
{0x2d7a,0x94c},
{0x2d7b,0x94f},
{0x2d7c,0x952},
{0x2d7d,0x955},
{0x2d7e,0x958},
{0x2d7f,0x95b},
{0x2d80,0x95e},
{0x2d81,0x961},
{0x2d82,0x964},
{0x2d83,0x967},
{0x2d84,0x96a},
{0x2d85,0x96d},
{0x2d86,0x970},
{0x2d87,0x973},
{0x2d88,0x976},
{0x2d89,0x979},
{0x2d8a,0x97c},
{0x2d8b,0x97f},
{0x2d8c,0x982},
{0x2d8d,0x985},
{0x2d8e,0x988},
{0x2d8f,0x98b},
{0x2d90,0x98e},
{0x2d91,0x991},
{0x2d92,0x994},
{0x2d93,0x997},
{0x2d94,0x99a},
{0x2d95,0x99d},
{0x2d96,0x9a0},
{0x2d97,0x9a3},
{0x2d98,0x9a6},
{0x2d99,0x9a9},
{0x2d9a,0x9ac},
{0x2d9b,0x9af},
{0x2d9c,0x9b2},
{0x2d9d,0x9b5},
{0x2d9e,0x9b8},
{0x2d9f,0x9bb},
{0x2da0,0x9be},
{0x2da1,0x9c1},
{0x2da2,0x9c4},
{0x2da3,0x9c7},
{0x2da4,0x9ca},
{0x2da5,0x9cd},
{0x2da6,0x9d0},
{0x2e49,0x9d4},
{0x2e4e,0x9d7},
{0x2e51,0x9da},
{0x2e5b,0x9de},
{0x2e5c,0x9e2},
{0x2e5f,0x9e6},
{0x2e60,0x9e9},
{0x2e61,0x9ec},
{0x2e66,0x9ef},
{0x2e68,0x9f3},
{0x2e6b,0x9f7},
{0x2e6d,0xa00},
{0x2e6f,0xa05},
{0x2e77,0xa09},
{0x2e79,0xa10},
{0x2e7b,0xa15},
{0x2e84,0xa1b},
{0x2e86,0xa1e},
{0x2e87,0xa21},
{0x2e8a,0xa26},
{0x2e8c,0xa2a},
{0x2e8d,0xa2f},
{0x2e92,0xa33},
{0x2e93,0xa38},
{0x2e97,0xa3d},
{0x2e98,0xa41},
{0x2e99,0xa49},
{0x2e9b,0xa4d},
{0x2eac,0xa50},
{0x2ead,0xa54},
{0x2eb0,0xa57},
{0x2eb1,0xa5b},
{0x2eb5,0xa5f},
{0x2eba,0xa62},
{0x2ebc,0xa66},
{0x2ebf,0xa6a},
{0x2ec1,0xa73},
{0x2ec3,0xa78},
{0x2ecb,0xa7c},
{0x2ecd,0xa83},
{0x2ecf,0xa88},
{0x2ed8,0xa8e},
{0x2eda,0xa91},
{0x2edb,0xa94},
{0x2ede,0xa99},
{0x2ee0,0xa9d},
{0x2ee1,0xaa2},
{0x2ee6,0xaa6},
{0x2ee7,0xaab},
{0x2eeb,0xab0},
{0x2eec,0xab4},
{0x2eed,0xabc},
{0x2eef,0xac0},
{0x2f00,0xac3},
{0x2f01,0xac7},
{0x2f04,0xaca},
{0x2f05,0xace},
{0x2f0d,0xad2},
{0x2f0e,0xad7},
{0x2f0f,0xadc},
{0x2f10,0xae1},
{0x2f11,0xae6},
{0x2f12,0xaeb},
{0x2f13,0xaf0},
{0x2f14,0xaf5},
{0x2f15,0xafa},
{0x2f16,0xaff},
{0x2f17,0xb04},
{0x2f18,0xb09},
{0x2f19,0xb0e},
{0x2f1a,0xb13},
{0x2f1b,0xb18},
{0x2f1c,0xb1d},
{0x2f1d,0xb22},
{0x2f1e,0xb27},
{0x2f38,0xb2c},
{0x2f41,0xb30},
{0x2f55,0xb33},
{0x3270,0xb36},
{0x3273,0xb39},
{0x3276,0xb3c},
{0x327c,0xb3f},
{0x327f,0xb42},
{0x3282,0xb45},
{0x33b0,0xb48},
{0x33b3,0xb4b},
{0x33b6,0xb4e},
{0x33bc,0xb51},
{0x33bf,0xb54},
{0x33c2,0xb57},
{0x3cf3,0xb5a},
{0x3cf4,0xb5d},
{0x3cf5,0xb60},
{0x3cf6,0xb63},
{0x3cf7,0xb66},
{0x3d4e,0xb69},
{0x3d4f,0xb6c},
{0x3d50,0xb6f},
{0x3d51,0xb72},
{0x3d52,0xb75},
{0x3d53,0xb78},
{0x3d54,0xb7b},
{0x3d55,0xb7e},
{0x3d60,0xb81},
{0x3df0,0xb84},
{0x3df1,0xb87},
{0x3ec7,0xb8a},
{0x3ec8,0xb8d},
{0x3ec9,0xb90},
{0x3eca,0xb93},
{0x3ecb,0xb96},
{0x3f22,0xb99},
{0x3f23,0xb9c},
{0x3f24,0xb9f},
{0x3f25,0xba2},
{0x3f26,0xba5},
{0x3f27,0xba8},
{0x3f28,0xbab},
{0x3f29,0xbae},
{0x3f34,0xbb1},
{0x3f51,0xbb4},
{0x3f52,0xbb7},
{0x3f53,0xbba},
{0x3f54,0xbbd},
{0x3f55,0xbc0},
{0x3f56,0xbc3},
{0x3f57,0xbc6},
{0x3f58,0xbc9},
{0x3f59,0xbcc},
{0x3f5a,0xbcf},
{0x3f5b,0xbd2},
{0x3f5c,0xbd5},
{0x3f5d,0xbd8},
{0x3f76,0xbdb},
{0x3fd7,0xbde},
{0x3fe0,0xbe1},
{0x3fe1,0xbe4},
{0x3fe2,0xbe7},
{0x3fe3,0xbea},
{0x3fe4,0xbed},
{0x3fe5,0xbf0},
{0x3fe6,0xbf3},
{0x3fe7,0xbf6},
{0x3fe8,0xbf9},
{0x3fe9,0xbfc},
{0x3fea,0xbff},
{0x3feb,0xc02},
{0x3fec,0xc05},
{0x3ff7,0xc08},
{0x3ffe,0xc0b},
{0x3fff,0xc0e},
{0x4000,0xc11},
{0x4001,0xc14},
{0x4002,0xc17},
{0x4003,0xc1a},
{0x4004,0xc1d},
{0x4005,0xc20},
{0x4006,0xc23},
{0x4007,0xc26},
{0x4008,0xc29},
{0x4009,0xc2c},
{0x400a,0xc2f},
{0x4015,0xc32},
{0x401e,0xc35},
{0x401f,0xc38},
{0x4020,0xc3b},
{0x4021,0xc3e},
{0x4022,0xc41},
{0x4023,0xc44},
{0x4024,0xc47},
{0x4025,0xc4a},
{0x4026,0xc4d},
{0x4027,0xc50},
{0x4028,0xc53},
{0x4029,0xc56},
{0x402a,0xc59},
{0x4035,0xc5c},
{0x404c,0xc5f},
{0x4113,0xc62},
{0x4114,0xc65},
{0x5019,0xc68},
{0x501a,0xc6b},
{0x501b,0xc6f},
{0x501c,0xc73},
{0x501d,0xc76},
{0x502b,0xc79},
{0x502c,0xc7c},
{0x502d,0xc7f},
{0x502e,0xc82},
{0x502f,0xc85},
{0x5030,0xc88},
{0x5031,0xc8b},
{0x5032,0xc8e},
{0x5033,0xc91},
{0x5034,0xc94},
{0x5055,0xc97},
{0x505f,0xc9a},
{0x5060,0xc9d},
{0x5062,0xca0},
{0x5063,0xca3},
{0x5064,0xca6},
{0x5065,0xca9},
{0x5067,0xcac},
{0x506d,0xcaf},
{0x506e,0xcb2},
{0x506f,0xcb5},
{0x5070,0xcb8},
{0x5071,0xcbb},
{0x5072,0xcbe},
{0x5073,0xcc1},
{0x5074,0xcc4},
{0x5075,0xcc7},
{0x5076,0xcca},
{0x5077,0xccd},
{0x5078,0xcd0},
{0x5079,0xcd3},
{0x507a,0xcd6},
{0x507b,0xcd9},
{0x507c,0xcdc},
{0x507d,0xcdf},
{0x507e,0xce2},
{0x507f,0xce5},
{0x5080,0xce8},
{0x5081,0xceb},
{0x5082,0xcee},
{0x5083,0xcf1},
{0x5084,0xcf4},
{0x5085,0xcf7},
{0x5086,0xcfa},
{0x5087,0xcfd},
{0x5088,0xd00},
{0x5089,0xd03},
{0x508a,0xd06},
{0x508b,0xd09},
{0x508c,0xd0c},
{0x508d,0xd0f},
{0x508e,0xd12},
{0x508f,0xd15},
{0x5090,0xd18},
{0x5091,0xd1b},
{0x5092,0xd1e},
{0x5093,0xd21},
{0x5094,0xd24},
{0x5095,0xd27},
{0x5096,0xd2a},
{0x5097,0xd2d},
{0x5098,0xd30},
{0x5099,0xd33},
{0x509a,0xd36},
{0x509b,0xd39},
{0x509c,0xd3c},
{0x509d,0xd3f},
{0x509e,0xd42},
{0x509f,0xd45},
{0x50a0,0xd48},
{0x50a1,0xd4b},
{0x50a2,0xd4e},
{0x50a3,0xd51},
{0x50a4,0xd54},
{0x50a5,0xd57},
{0x50a6,0xd5a},
{0x50a7,0xd5d},
{0x50a8,0xd60},
{0x50a9,0xd63},
{0x50aa,0xd67},
{0x50ab,0xd6b},
{0x50ac,0xd6f},
{0x50ad,0xd73},
{0x50ae,0xd77},
{0x50af,0xd7b},
{0x50b0,0xd7f},
{0x50b1,0xd83},
{0x50b2,0xd87},
{0x50b3,0xd8b},
{0x50b4,0xd8f},
{0x50b5,0xd93},
{0x50b6,0xd97},
{0x50b7,0xd9b},
{0x50b8,0xd9f},
{0x50b9,0xda3},
{0x50ba,0xda7},
{0x50bb,0xdab},
{0x50bc,0xdaf},
{0x50bd,0xdb3},
{0x50be,0xdb7},
{0x50bf,0xdbb},
{0x50c0,0xdbf},
{0x50c1,0xdc3},
{0x50c2,0xdc7},
{0x50c3,0xdcb},
{0x50c4,0xdcf},
{0x50c5,0xdd3},
{0x50c6,0xdd7},
{0x50c7,0xddb},
{0x50c8,0xddf},
{0x50c9,0xde3},
{0x50ca,0xde7},
{0x50cb,0xdeb},
{0x50cc,0xdef},
{0x50cd,0xdf3},
{0x50ce,0xdf7},
{0x50cf,0xdfb},
{0x50d0,0xdff},
{0x50d1,0xe03},
{0x50d2,0xe07},
{0x50d3,0xe0b},
{0x50d4,0xe0f},
{0x50d5,0xe13},
{0x50d6,0xe17},
{0x50d7,0xe1b},
{0x50d8,0xe1f},
{0x50d9,0xe23},
{0x50da,0xe27},
{0x50db,0xe2b},
{0x50dc,0xe2f},
{0x50dd,0xe33},
{0x50de,0xe37},
{0x50df,0xe3b},
{0x50e0,0xe3f},
{0x50e1,0xe43},
{0x50e2,0xe47},
{0x50e3,0xe4b},
{0x50e4,0xe4f},
{0x50e5,0xe53},
{0x50e6,0xe57},
{0x50e7,0xe5b},
{0x50e8,0xe5f},
{0x50e9,0xe63},
{0x50ea,0xe67},
{0x50eb,0xe6b},
{0x50ec,0xe6f},
{0x50ed,0xe73},
{0x50ee,0xe77},
{0x50ef,0xe7b},
{0x50f0,0xe7f},
{0x50f1,0xe83},
{0x50f2,0xe87},
{0x50f3,0xe8b},
{0x50f4,0xe8f},
{0x50f5,0xe93},
{0x50f6,0xe97},
{0x50f7,0xe9b},
{0x50f8,0xe9f},
{0x50f9,0xea3},
{0x50fa,0xea7},
{0x50fb,0xeab},
{0x50fc,0xeaf},
{0x50fd,0xeb3},
{0x50fe,0xeb7},
{0x50ff,0xebb},
{0x5100,0xebf},
{0x5101,0xec3},
{0x5102,0xec7},
{0x5103,0xecb},
{0x5104,0xecf},
{0x5105,0xed3},
{0x5106,0xed7},
{0x5107,0xedb},
{0x5108,0xedf},
{0x5109,0xee3},
{0x510a,0xee7},
{0x510b,0xeeb},
{0x510c,0xeef},
{0x510d,0xef3},
{0x510e,0xef6},
{0x510f,0xef9},
{0x5110,0xefc},
{0x5111,0xeff},
{0x5112,0xf02},
{0x5113,0xf05},
{0x5114,0xf08},
{0x5115,0xf0b},
{0x5116,0xf0e},
{0x5117,0xf11},
{0x5118,0xf14},
{0x5119,0xf17},
{0x511a,0xf1a},
{0x511b,0xf1d},
{0x511c,0xf20},
{0x511d,0xf23},
{0x511e,0xf26},
{0x511f,0xf29},
{0x5120,0xf2c},
{0x5121,0xf2f},
{0x5122,0xf32},
{0x5123,0xf35},
{0x5124,0xf38},
{0x5125,0xf3b},
{0x5126,0xf3e},
{0x5127,0xf41},
{0x5128,0xf44},
{0x5129,0xf47},
{0x512a,0xf4a},
{0x512b,0xf4d},
{0x512c,0xf50},
{0x512d,0xf53},
{0x512e,0xf56},
{0x512f,0xf59},
{0x5130,0xf5c},
{0x5131,0xf5f},
{0x5132,0xf62},
{0x5133,0xf65},
{0x5134,0xf68},
{0x5135,0xf6b},
{0x5136,0xf6e},
{0x5137,0xf71},
{0x5138,0xf74},
{0x5139,0xf77},
{0x513a,0xf7a},
{0x513b,0xf7d},
{0x513c,0xf80},
{0x513d,0xf83},
{0x513e,0xf86},
{0x513f,0xf89},
{0x5140,0xf8c},
{0x5141,0xf8f},
{0x5142,0xf92},
{0x5143,0xf95},
{0x5144,0xf98},
{0x5145,0xf9b},
{0x5146,0xf9e},
{0x5147,0xfa1},
{0x5148,0xfa4},
{0x5149,0xfa7},
{0x514a,0xfaa},
{0x514b,0xfad},
{0x514c,0xfb0},
{0x514d,0xfb3},
{0x514e,0xfb6},
{0x514f,0xfb9},
{0x5150,0xfbc},
{0x5151,0xfbf},
{0x5152,0xfc2},
{0x5153,0xfc5},
{0x5154,0xfc8},
{0x5155,0xfcb},
{0x5156,0xfce},
{0x5157,0xfd1},
{0x5158,0xfd4},
{0x5159,0xfd7},
{0x515a,0xfda},
{0x515b,0xfdd},
{0x515c,0xfe0},
{0x515d,0xfe3},
{0x515e,0xfe6},
{0x515f,0xfe9},
{0x5160,0xfec},
{0x5161,0xfef},
{0x5162,0xff2},
{0x5163,0xff5},
{0x5164,0xff8},
{0x5165,0xffb},
{0x5166,0xffe},
{0x5167,0x1001},
{0x5168,0x1004},
{0x5169,0x1007},
{0x516a,0x100a},
{0x516b,0x100d},
{0x516c,0x1010},
{0x516d,0x1013},
{0x516e,0x1016},
{0x516f,0x1019},
{0x5170,0x101c},
{0x5171,0x101f},
{0x5172,0x1023},
{0x5173,0x1027},
{0x5174,0x102b},
{0x5175,0x102f},
{0x5176,0x1033},
{0x5177,0x1037},
{0x5178,0x103b},
{0x5179,0x103f},
{0x517a,0x1043},
{0x517b,0x1047},
{0x517c,0x104b},
{0x517d,0x104f},
{0x517e,0x1053},
{0x517f,0x1057},
{0x5180,0x105b},
{0x5181,0x105f},
{0x5182,0x1063},
{0x5183,0x1067},
{0x5184,0x106b},
{0x5185,0x106f},
{0x5186,0x1073},
{0x5187,0x1077},
{0x5188,0x107b},
{0x5189,0x107f},
{0x518a,0x1083},
{0x518b,0x1087},
{0x518c,0x108b},
{0x518d,0x108f},
{0x518e,0x1093},
{0x518f,0x1097},
{0x5190,0x109b},
{0x5191,0x109f},
{0x5192,0x10a3},
{0x5193,0x10a7},
{0x5194,0x10ab},
{0x5195,0x10af},
{0x5196,0x10b3},
{0x5197,0x10b7},
{0x5198,0x10bb},
{0x5199,0x10bf},
{0x519a,0x10c3},
{0x519b,0x10c7},
{0x519c,0x10cb},
{0x519d,0x10cf},
{0x519e,0x10d3},
{0x519f,0x10d7},
{0x51a0,0x10db},
{0x51a1,0x10df},
{0x51a2,0x10e3},
{0x51a3,0x10e7},
{0x51a4,0x10ea},
{0x51a5,0x10ed},
{0x51a6,0x10f0},
{0x51a7,0x10f3},
{0x51a8,0x10f6},
{0x51a9,0x10f9},
{0x51aa,0x10fc},
{0x51ab,0x10ff},
{0x51ac,0x1102},
{0x51ad,0x1105},
{0x51ae,0x1108},
{0x51af,0x110b},
{0x51b0,0x110e},
{0x51b1,0x1111},
{0x51b2,0x1114},
{0x51b3,0x1117},
{0x51b4,0x111a},
{0x51b5,0x111d},
{0x51b6,0x1120},
{0x51b7,0x1123},
{0x51b8,0x1127},
{0x51b9,0x112b},
{0x51ba,0x112e},
{0x51bb,0x1131},
{0x51bc,0x1134},
{0x51bd,0x1137},
{0x51be,0x113a},
{0x51bf,0x113d},
{0x51c0,0x1141},
{0x51c1,0x1145},
{0x51c2,0x1148},
{0x51c3,0x114c},
{0x51c4,0x1151},
{0x51c5,0x1157},
{0x51c6,0x115c},
{0x51c7,0x1161},
{0x51c8,0x1166},
{0x51c9,0x116a},
{0x51ca,0x116e},
{0x51cb,0x1172},
{0x51cc,0x1177},
{0x51cd,0x117a},
{0x51ce,0x117e},
{0x51cf,0x1184},
{0x51d0,0x1187},
{0x51d1,0x118b},
{0x51d2,0x1190},
{0x51d3,0x1196},
{0x51d4,0x119b},
{0x51d5,0x11a0},
{0x51d6,0x11a5},
{0x51d7,0x11a9},
{0x51d8,0x11ad},
{0x51d9,0x11b1},
{0x51da,0x11b6},
{0x51db,0x11b9},
{0x51dc,0x11bd},
{0x51df,0x11c3},
{0x51e0,0x11c6},
};

static const int cmap_Adobe_Japan1_UCS2_table[] = {
0x2,0x30,0x2e,
0x3,0x73,0x65,0x63,
0x3,0x6d,0x69,0x6e,
0x4,0x58,0x49,0x49,0x49,
0x3,0x58,0x49,0x56,
0x2,0x58,0x56,
0x4,0x78,0x69,0x69,0x69,
0x3,0x78,0x69,0x76,
0x2,0x78,0x76,
0x2,0x54,0x42,
0x2,0x2193,0x2191,
0x4,0x6709,0x9650,0x4f1a,0x793e,
0x4,0x8ca1,0x56e3,0x6cd5,0x4eba,
0x4,0x6709,0x9650,0x4f1a,0x793e,
0x4,0x8ca1,0x56e3,0x6cd5,0x4eba,
0x2,0x45,0x30a,
0x2,0x49,0x30a,
0x2,0x4f,0x30a,
0x2,0x65,0x30a,
0x2,0x131,0x30a,
0x2,0x6f,0x30a,
0x2,0x45,0x30a,
0x2,0x49,0x30a,
0x2,0x4f,0x30a,
0x2,0x65,0x30a,
0x2,0x131,0x30a,
0x2,0x6f,0x30a,
0x3,0x30,0x2f,0x33,
0x3,0x31,0x2f,0x37,
0x3,0x32,0x2f,0x37,
0x3,0x33,0x2f,0x37,
0x3,0x34,0x2f,0x37,
0x3,0x35,0x2f,0x37,
0x3,0x36,0x2f,0x37,
0x3,0x31,0x2f,0x39,
0x3,0x32,0x2f,0x39,
0x3,0x34,0x2f,0x39,
0x3,0x35,0x2f,0x39,
0x3,0x37,0x2f,0x39,
0x3,0x38,0x2f,0x39,
0x4,0x31,0x2f,0x31,0x30,
0x4,0x33,0x2f,0x31,0x30,
0x4,0x37,0x2f,0x31,0x30,
0x4,0x39,0x2f,0x31,0x30,
0x4,0x31,0x2f,0x31,0x31,
0x4,0x32,0x2f,0x31,0x31,
0x4,0x33,0x2f,0x31,0x31,
0x4,0x34,0x2f,0x31,0x31,
0x4,0x35,0x2f,0x31,0x31,
0x4,0x36,0x2f,0x31,0x31,
0x4,0x37,0x2f,0x31,0x31,
0x4,0x38,0x2f,0x31,0x31,
0x4,0x39,0x2f,0x31,0x31,
0x5,0x31,0x30,0x2f,0x31,0x31,
0x4,0x31,0x2f,0x31,0x32,
0x4,0x35,0x2f,0x31,0x32,
0x4,0x37,0x2f,0x31,0x32,
0x5,0x31,0x31,0x2f,0x31,0x32,
0x5,0x31,0x2f,0x31,0x30,0x30,
0x3,0x30,0x2f,0x33,
0x3,0x31,0x2f,0x32,
0x3,0x31,0x2f,0x33,
0x3,0x32,0x2f,0x33,
0x3,0x31,0x2f,0x34,
0x3,0x33,0x2f,0x34,
0x3,0x31,0x2f,0x35,
0x3,0x32,0x2f,0x35,
0x3,0x33,0x2f,0x35,
0x3,0x34,0x2f,0x35,
0x3,0x31,0x2f,0x36,
0x3,0x35,0x2f,0x36,
0x3,0x31,0x2f,0x37,
0x3,0x32,0x2f,0x37,
0x3,0x33,0x2f,0x37,
0x3,0x34,0x2f,0x37,
0x3,0x35,0x2f,0x37,
0x3,0x36,0x2f,0x37,
0x3,0x31,0x2f,0x38,
0x3,0x33,0x2f,0x38,
0x3,0x35,0x2f,0x38,
0x3,0x37,0x2f,0x38,
0x3,0x31,0x2f,0x39,
0x3,0x32,0x2f,0x39,
0x3,0x34,0x2f,0x39,
0x3,0x35,0x2f,0x39,
0x3,0x37,0x2f,0x39,
0x3,0x38,0x2f,0x39,
0x4,0x31,0x2f,0x31,0x30,
0x4,0x33,0x2f,0x31,0x30,
0x4,0x37,0x2f,0x31,0x30,
0x4,0x39,0x2f,0x31,0x30,
0x4,0x31,0x2f,0x31,0x31,
0x4,0x32,0x2f,0x31,0x31,
0x4,0x33,0x2f,0x31,0x31,
0x4,0x34,0x2f,0x31,0x31,
0x4,0x35,0x2f,0x31,0x31,
0x4,0x36,0x2f,0x31,0x31,
0x4,0x37,0x2f,0x31,0x31,
0x4,0x38,0x2f,0x31,0x31,
0x4,0x39,0x2f,0x31,0x31,
0x5,0x31,0x30,0x2f,0x31,0x31,
0x4,0x31,0x2f,0x31,0x32,
0x4,0x35,0x2f,0x31,0x32,
0x4,0x37,0x2f,0x31,0x32,
0x5,0x31,0x31,0x2f,0x31,0x32,
0x5,0x31,0x2f,0x31,0x30,0x30,
0x2,0x30,0x30,
0x2,0x30,0x31,
0x2,0x30,0x32,
0x2,0x30,0x33,
0x2,0x30,0x34,
0x2,0x30,0x35,
0x2,0x30,0x36,
0x2,0x30,0x37,
0x2,0x30,0x38,
0x2,0x30,0x39,
0x2,0x32,0x31,
0x2,0x32,0x32,
0x2,0x32,0x33,
0x2,0x32,0x34,
0x2,0x32,0x35,
0x2,0x32,0x36,
0x2,0x32,0x37,
0x2,0x32,0x38,
0x2,0x32,0x39,
0x2,0x33,0x30,
0x2,0x33,0x31,
0x2,0x33,0x32,
0x2,0x33,0x33,
0x2,0x33,0x34,
0x2,0x33,0x35,
0x2,0x33,0x36,
0x2,0x33,0x37,
0x2,0x33,0x38,
0x2,0x33,0x39,
0x2,0x34,0x30,
0x2,0x34,0x31,
0x2,0x34,0x32,
0x2,0x34,0x33,
0x2,0x34,0x34,
0x2,0x34,0x35,
0x2,0x34,0x36,
0x2,0x34,0x37,
0x2,0x34,0x38,
0x2,0x34,0x39,
0x2,0x35,0x30,
0x2,0x35,0x31,
0x2,0x35,0x32,
0x2,0x35,0x33,
0x2,0x35,0x34,
0x2,0x35,0x35,
0x2,0x35,0x36,
0x2,0x35,0x37,
0x2,0x35,0x38,
0x2,0x35,0x39,
0x2,0x36,0x30,
0x2,0x36,0x31,
0x2,0x36,0x32,
0x2,0x36,0x33,
0x2,0x36,0x34,
0x2,0x36,0x35,
0x2,0x36,0x36,
0x2,0x36,0x37,
0x2,0x36,0x38,
0x2,0x36,0x39,
0x2,0x37,0x30,
0x2,0x37,0x31,
0x2,0x37,0x32,
0x2,0x37,0x33,
0x2,0x37,0x34,
0x2,0x37,0x35,
0x2,0x37,0x36,
0x2,0x37,0x37,
0x2,0x37,0x38,
0x2,0x37,0x39,
0x2,0x38,0x30,
0x2,0x38,0x31,
0x2,0x38,0x32,
0x2,0x38,0x33,
0x2,0x38,0x34,
0x2,0x38,0x35,
0x2,0x38,0x36,
0x2,0x38,0x37,
0x2,0x38,0x38,
0x2,0x38,0x39,
0x2,0x39,0x30,
0x2,0x39,0x31,
0x2,0x39,0x32,
0x2,0x39,0x33,
0x2,0x39,0x34,
0x2,0x39,0x35,
0x2,0x39,0x36,
0x2,0x39,0x37,
0x2,0x39,0x38,
0x2,0x39,0x39,
0x3,0x31,0x30,0x30,
0x4,0x78,0x69,0x69,0x69,
0x3,0x78,0x69,0x76,
0x2,0x78,0x76,
0x4,0x58,0x49,0x49,0x49,
0x3,0x58,0x49,0x56,
0x2,0x58,0x56,
0x2,0x5341,0x4e00,
0x2,0x5341,0x4e8c,
0x2,0x5341,0x4e09,
0x2,0x5341,0x56db,
0x2,0x5341,0x4e94,
0x2,0x5341,0x516d,
0x2,0x5341,0x4e03,
0x2,0x5341,0x516b,
0x2,0x5341,0x4e5d,
0x2,0x4e8c,0x5341,
0x2,0x28,0x29,
0x2,0x30,0x30,
0x2,0x33,0x32,
0x2,0x33,0x33,
0x2,0x33,0x34,
0x2,0x33,0x35,
0x2,0x33,0x36,
0x2,0x33,0x37,
0x2,0x33,0x38,
0x2,0x33,0x39,
0x2,0x34,0x30,
0x2,0x34,0x31,
0x2,0x34,0x32,
0x2,0x34,0x33,
0x2,0x34,0x34,
0x2,0x34,0x35,
0x2,0x34,0x36,
0x2,0x34,0x37,
0x2,0x34,0x38,
0x2,0x34,0x39,
0x2,0x35,0x30,
0x2,0x35,0x31,
0x2,0x35,0x32,
0x2,0x35,0x33,
0x2,0x35,0x34,
0x2,0x35,0x35,
0x2,0x35,0x36,
0x2,0x35,0x37,
0x2,0x35,0x38,
0x2,0x35,0x39,
0x2,0x36,0x30,
0x2,0x36,0x31,
0x2,0x36,0x32,
0x2,0x36,0x33,
0x2,0x36,0x34,
0x2,0x36,0x35,
0x2,0x36,0x36,
0x2,0x36,0x37,
0x2,0x36,0x38,
0x2,0x36,0x39,
0x2,0x37,0x30,
0x2,0x37,0x31,
0x2,0x37,0x32,
0x2,0x37,0x33,
0x2,0x37,0x34,
0x2,0x37,0x35,
0x2,0x37,0x36,
0x2,0x37,0x37,
0x2,0x37,0x38,
0x2,0x37,0x39,
0x2,0x38,0x30,
0x2,0x38,0x31,
0x2,0x38,0x32,
0x2,0x38,0x33,
0x2,0x38,0x34,
0x2,0x38,0x35,
0x2,0x38,0x36,
0x2,0x38,0x37,
0x2,0x38,0x38,
0x2,0x38,0x39,
0x2,0x39,0x30,
0x2,0x39,0x31,
0x2,0x39,0x32,
0x2,0x39,0x33,
0x2,0x39,0x34,
0x2,0x39,0x35,
0x2,0x39,0x36,
0x2,0x39,0x37,
0x2,0x39,0x38,
0x2,0x39,0x39,
0x3,0x31,0x30,0x30,
0x2,0x30,0x30,
0x2,0x30,0x31,
0x2,0x30,0x32,
0x2,0x30,0x33,
0x2,0x30,0x34,
0x2,0x30,0x35,
0x2,0x30,0x36,
0x2,0x30,0x37,
0x2,0x30,0x38,
0x2,0x30,0x39,
0x2,0x31,0x30,
0x2,0x31,0x31,
0x2,0x31,0x32,
0x2,0x31,0x33,
0x2,0x31,0x34,
0x2,0x31,0x35,
0x2,0x31,0x36,
0x2,0x31,0x37,
0x2,0x31,0x38,
0x2,0x31,0x39,
0x2,0x32,0x30,
0x2,0x32,0x31,
0x2,0x32,0x32,
0x2,0x32,0x33,
0x2,0x32,0x34,
0x2,0x32,0x35,
0x2,0x32,0x36,
0x2,0x32,0x37,
0x2,0x32,0x38,
0x2,0x32,0x39,
0x2,0x33,0x30,
0x2,0x33,0x31,
0x2,0x33,0x32,
0x2,0x33,0x33,
0x2,0x33,0x34,
0x2,0x33,0x35,
0x2,0x33,0x36,
0x2,0x33,0x37,
0x2,0x33,0x38,
0x2,0x33,0x39,
0x2,0x34,0x30,
0x2,0x34,0x31,
0x2,0x34,0x32,
0x2,0x34,0x33,
0x2,0x34,0x34,
0x2,0x34,0x35,
0x2,0x34,0x36,
0x2,0x34,0x37,
0x2,0x34,0x38,
0x2,0x34,0x39,
0x2,0x35,0x30,
0x2,0x35,0x31,
0x2,0x35,0x32,
0x2,0x35,0x33,
0x2,0x35,0x34,
0x2,0x35,0x35,
0x2,0x35,0x36,
0x2,0x35,0x37,
0x2,0x35,0x38,
0x2,0x35,0x39,
0x2,0x36,0x30,
0x2,0x36,0x31,
0x2,0x36,0x32,
0x2,0x36,0x33,
0x2,0x36,0x34,
0x2,0x36,0x35,
0x2,0x36,0x36,
0x2,0x36,0x37,
0x2,0x36,0x38,
0x2,0x36,0x39,
0x2,0x37,0x30,
0x2,0x37,0x31,
0x2,0x37,0x32,
0x2,0x37,0x33,
0x2,0x37,0x34,
0x2,0x37,0x35,
0x2,0x37,0x36,
0x2,0x37,0x37,
0x2,0x37,0x38,
0x2,0x37,0x39,
0x2,0x38,0x30,
0x2,0x38,0x31,
0x2,0x38,0x32,
0x2,0x38,0x33,
0x2,0x38,0x34,
0x2,0x38,0x35,
0x2,0x38,0x36,
0x2,0x38,0x37,
0x2,0x38,0x38,
0x2,0x38,0x39,
0x2,0x39,0x30,
0x2,0x39,0x31,
0x2,0x39,0x32,
0x2,0x39,0x33,
0x2,0x39,0x34,
0x2,0x39,0x35,
0x2,0x39,0x36,
0x2,0x39,0x37,
0x2,0x39,0x38,
0x2,0x39,0x39,
0x3,0x31,0x30,0x30,
0x2,0x30,0x30,
0x2,0x30,0x31,
0x2,0x30,0x32,
0x2,0x30,0x33,
0x2,0x30,0x34,
0x2,0x30,0x35,
0x2,0x30,0x36,
0x2,0x30,0x37,
0x2,0x30,0x38,
0x2,0x30,0x39,
0x2,0x31,0x30,
0x2,0x31,0x31,
0x2,0x31,0x32,
0x2,0x31,0x33,
0x2,0x31,0x34,
0x2,0x31,0x35,
0x2,0x31,0x36,
0x2,0x31,0x37,
0x2,0x31,0x38,
0x2,0x31,0x39,
0x2,0x32,0x30,
0x2,0x32,0x31,
0x2,0x32,0x32,
0x2,0x32,0x33,
0x2,0x32,0x34,
0x2,0x32,0x35,
0x2,0x32,0x36,
0x2,0x32,0x37,
0x2,0x32,0x38,
0x2,0x32,0x39,
0x2,0x33,0x30,
0x2,0x33,0x31,
0x2,0x33,0x32,
0x2,0x33,0x33,
0x2,0x33,0x34,
0x2,0x33,0x35,
0x2,0x33,0x36,
0x2,0x33,0x37,
0x2,0x33,0x38,
0x2,0x33,0x39,
0x2,0x34,0x30,
0x2,0x34,0x31,
0x2,0x34,0x32,
0x2,0x34,0x33,
0x2,0x34,0x34,
0x2,0x34,0x35,
0x2,0x34,0x36,
0x2,0x34,0x37,
0x2,0x34,0x38,
0x2,0x34,0x39,
0x2,0x35,0x30,
0x2,0x35,0x31,
0x2,0x35,0x32,
0x2,0x35,0x33,
0x2,0x35,0x34,
0x2,0x35,0x35,
0x2,0x35,0x36,
0x2,0x35,0x37,
0x2,0x35,0x38,
0x2,0x35,0x39,
0x2,0x36,0x30,
0x2,0x36,0x31,
0x2,0x36,0x32,
0x2,0x36,0x33,
0x2,0x36,0x34,
0x2,0x36,0x35,
0x2,0x36,0x36,
0x2,0x36,0x37,
0x2,0x36,0x38,
0x2,0x36,0x39,
0x2,0x37,0x30,
0x2,0x37,0x31,
0x2,0x37,0x32,
0x2,0x37,0x33,
0x2,0x37,0x34,
0x2,0x37,0x35,
0x2,0x37,0x36,
0x2,0x37,0x37,
0x2,0x37,0x38,
0x2,0x37,0x39,
0x2,0x38,0x30,
0x2,0x38,0x31,
0x2,0x38,0x32,
0x2,0x38,0x33,
0x2,0x38,0x34,
0x2,0x38,0x35,
0x2,0x38,0x36,
0x2,0x38,0x37,
0x2,0x38,0x38,
0x2,0x38,0x39,
0x2,0x39,0x30,
0x2,0x39,0x31,
0x2,0x39,0x32,
0x2,0x39,0x33,
0x2,0x39,0x34,
0x2,0x39,0x35,
0x2,0x39,0x36,
0x2,0x39,0x37,
0x2,0x39,0x38,
0x2,0x39,0x39,
0x3,0x31,0x30,0x30,
0x2,0x30,0x30,
0x2,0x30,0x31,
0x2,0x30,0x32,
0x2,0x30,0x33,
0x2,0x30,0x34,
0x2,0x30,0x35,
0x2,0x30,0x36,
0x2,0x30,0x37,
0x2,0x30,0x38,
0x2,0x30,0x39,
0x2,0x31,0x30,
0x2,0x31,0x31,
0x2,0x31,0x32,
0x2,0x31,0x33,
0x2,0x31,0x34,
0x2,0x31,0x35,
0x2,0x31,0x36,
0x2,0x31,0x37,
0x2,0x31,0x38,
0x2,0x31,0x39,
0x2,0x32,0x30,
0x2,0x32,0x31,
0x2,0x32,0x32,
0x2,0x32,0x33,
0x2,0x32,0x34,
0x2,0x32,0x35,
0x2,0x32,0x36,
0x2,0x32,0x37,
0x2,0x32,0x38,
0x2,0x32,0x39,
0x2,0x33,0x30,
0x2,0x33,0x31,
0x2,0x33,0x32,
0x2,0x33,0x33,
0x2,0x33,0x34,
0x2,0x33,0x35,
0x2,0x33,0x36,
0x2,0x33,0x37,
0x2,0x33,0x38,
0x2,0x33,0x39,
0x2,0x34,0x30,
0x2,0x34,0x31,
0x2,0x34,0x32,
0x2,0x34,0x33,
0x2,0x34,0x34,
0x2,0x34,0x35,
0x2,0x34,0x36,
0x2,0x34,0x37,
0x2,0x34,0x38,
0x2,0x34,0x39,
0x2,0x35,0x30,
0x2,0x35,0x31,
0x2,0x35,0x32,
0x2,0x35,0x33,
0x2,0x35,0x34,
0x2,0x35,0x35,
0x2,0x35,0x36,
0x2,0x35,0x37,
0x2,0x35,0x38,
0x2,0x35,0x39,
0x2,0x36,0x30,
0x2,0x36,0x31,
0x2,0x36,0x32,
0x2,0x36,0x33,
0x2,0x36,0x34,
0x2,0x36,0x35,
0x2,0x36,0x36,
0x2,0x36,0x37,
0x2,0x36,0x38,
0x2,0x36,0x39,
0x2,0x37,0x30,
0x2,0x37,0x31,
0x2,0x37,0x32,
0x2,0x37,0x33,
0x2,0x37,0x34,
0x2,0x37,0x35,
0x2,0x37,0x36,
0x2,0x37,0x37,
0x2,0x37,0x38,
0x2,0x37,0x39,
0x2,0x38,0x30,
0x2,0x38,0x31,
0x2,0x38,0x32,
0x2,0x38,0x33,
0x2,0x38,0x34,
0x2,0x38,0x35,
0x2,0x38,0x36,
0x2,0x38,0x37,
0x2,0x38,0x38,
0x2,0x38,0x39,
0x2,0x39,0x30,
0x2,0x39,0x31,
0x2,0x39,0x32,
0x2,0x39,0x33,
0x2,0x39,0x34,
0x2,0x39,0x35,
0x2,0x39,0x36,
0x2,0x39,0x37,
0x2,0x39,0x38,
0x2,0x39,0x39,
0x3,0x31,0x30,0x30,
0x2,0x30,0x30,
0x2,0x30,0x31,
0x2,0x30,0x32,
0x2,0x30,0x33,
0x2,0x30,0x34,
0x2,0x30,0x35,
0x2,0x30,0x36,
0x2,0x30,0x37,
0x2,0x30,0x38,
0x2,0x30,0x39,
0x2,0x31,0x30,
0x2,0x31,0x31,
0x2,0x31,0x32,
0x2,0x31,0x33,
0x2,0x31,0x34,
0x2,0x31,0x35,
0x2,0x31,0x36,
0x2,0x31,0x37,
0x2,0x31,0x38,
0x2,0x31,0x39,
0x2,0x32,0x30,
0x2,0x32,0x31,
0x2,0x32,0x32,
0x2,0x32,0x33,
0x2,0x32,0x34,
0x2,0x32,0x35,
0x2,0x32,0x36,
0x2,0x32,0x37,
0x2,0x32,0x38,
0x2,0x32,0x39,
0x2,0x33,0x30,
0x2,0x33,0x31,
0x2,0x33,0x32,
0x2,0x33,0x33,
0x2,0x33,0x34,
0x2,0x33,0x35,
0x2,0x33,0x36,
0x2,0x33,0x37,
0x2,0x33,0x38,
0x2,0x33,0x39,
0x2,0x34,0x30,
0x2,0x34,0x31,
0x2,0x34,0x32,
0x2,0x34,0x33,
0x2,0x34,0x34,
0x2,0x34,0x35,
0x2,0x34,0x36,
0x2,0x34,0x37,
0x2,0x34,0x38,
0x2,0x34,0x39,
0x2,0x35,0x30,
0x2,0x35,0x31,
0x2,0x35,0x32,
0x2,0x35,0x33,
0x2,0x35,0x34,
0x2,0x35,0x35,
0x2,0x35,0x36,
0x2,0x35,0x37,
0x2,0x35,0x38,
0x2,0x35,0x39,
0x2,0x36,0x30,
0x2,0x36,0x31,
0x2,0x36,0x32,
0x2,0x36,0x33,
0x2,0x36,0x34,
0x2,0x36,0x35,
0x2,0x36,0x36,
0x2,0x36,0x37,
0x2,0x36,0x38,
0x2,0x36,0x39,
0x2,0x37,0x30,
0x2,0x37,0x31,
0x2,0x37,0x32,
0x2,0x37,0x33,
0x2,0x37,0x34,
0x2,0x37,0x35,
0x2,0x37,0x36,
0x2,0x37,0x37,
0x2,0x37,0x38,
0x2,0x37,0x39,
0x2,0x38,0x30,
0x2,0x38,0x31,
0x2,0x38,0x32,
0x2,0x38,0x33,
0x2,0x38,0x34,
0x2,0x38,0x35,
0x2,0x38,0x36,
0x2,0x38,0x37,
0x2,0x38,0x38,
0x2,0x38,0x39,
0x2,0x39,0x30,
0x2,0x39,0x31,
0x2,0x39,0x32,
0x2,0x39,0x33,
0x2,0x39,0x34,
0x2,0x39,0x35,
0x2,0x39,0x36,
0x2,0x39,0x37,
0x2,0x39,0x38,
0x2,0x39,0x39,
0x3,0x31,0x30,0x30,
0x2,0x30,0x30,
0x2,0x30,0x31,
0x2,0x30,0x32,
0x2,0x30,0x33,
0x2,0x30,0x34,
0x2,0x30,0x35,
0x2,0x30,0x36,
0x2,0x30,0x37,
0x2,0x30,0x38,
0x2,0x30,0x39,
0x2,0x31,0x30,
0x2,0x31,0x31,
0x2,0x31,0x32,
0x2,0x31,0x33,
0x2,0x31,0x34,
0x2,0x31,0x35,
0x2,0x31,0x36,
0x2,0x31,0x37,
0x2,0x31,0x38,
0x2,0x31,0x39,
0x2,0x32,0x30,
0x2,0x32,0x31,
0x2,0x32,0x32,
0x2,0x32,0x33,
0x2,0x32,0x34,
0x2,0x32,0x35,
0x2,0x32,0x36,
0x2,0x32,0x37,
0x2,0x32,0x38,
0x2,0x32,0x39,
0x2,0x33,0x30,
0x2,0x33,0x31,
0x2,0x33,0x32,
0x2,0x33,0x33,
0x2,0x33,0x34,
0x2,0x33,0x35,
0x2,0x33,0x36,
0x2,0x33,0x37,
0x2,0x33,0x38,
0x2,0x33,0x39,
0x2,0x34,0x30,
0x2,0x34,0x31,
0x2,0x34,0x32,
0x2,0x34,0x33,
0x2,0x34,0x34,
0x2,0x34,0x35,
0x2,0x34,0x36,
0x2,0x34,0x37,
0x2,0x34,0x38,
0x2,0x34,0x39,
0x2,0x35,0x30,
0x2,0x35,0x31,
0x2,0x35,0x32,
0x2,0x35,0x33,
0x2,0x35,0x34,
0x2,0x35,0x35,
0x2,0x35,0x36,
0x2,0x35,0x37,
0x2,0x35,0x38,
0x2,0x35,0x39,
0x2,0x36,0x30,
0x2,0x36,0x31,
0x2,0x36,0x32,
0x2,0x36,0x33,
0x2,0x36,0x34,
0x2,0x36,0x35,
0x2,0x36,0x36,
0x2,0x36,0x37,
0x2,0x36,0x38,
0x2,0x36,0x39,
0x2,0x37,0x30,
0x2,0x37,0x31,
0x2,0x37,0x32,
0x2,0x37,0x33,
0x2,0x37,0x34,
0x2,0x37,0x35,
0x2,0x37,0x36,
0x2,0x37,0x37,
0x2,0x37,0x38,
0x2,0x37,0x39,
0x2,0x38,0x30,
0x2,0x38,0x31,
0x2,0x38,0x32,
0x2,0x38,0x33,
0x2,0x38,0x34,
0x2,0x38,0x35,
0x2,0x38,0x36,
0x2,0x38,0x37,
0x2,0x38,0x38,
0x2,0x38,0x39,
0x2,0x39,0x30,
0x2,0x39,0x31,
0x2,0x39,0x32,
0x2,0x39,0x33,
0x2,0x39,0x34,
0x2,0x39,0x35,
0x2,0x39,0x36,
0x2,0x39,0x37,
0x2,0x39,0x38,
0x2,0x39,0x39,
0x3,0x31,0x30,0x30,
0x2,0x50,0x48,
0x2,0x56,0x53,
0x3,0x63,0x2f,0x63,
0x3,0x6d,0x2f,0x6d,
0x3,0x6e,0x2f,0x6d,
0x2,0x3055,0x3058,
0x2,0x3055,0x3058,
0x2,0x30a2,0x30c8,
0x3,0x30a6,0x30eb,0x30b7,
0x3,0x30a8,0x30af,0x30b5,
0x8,0x30aa,0x30f3,0x30b0,0x30b9,0x30c8,0x30ed,0x30fc,0x30e0,
0x4,0x30aa,0x30f3,0x30c8,0x30ed,
0x3,0x30ab,0x30c3,0x30d7,
0x6,0x30ad,0x30ed,0x30ea,0x30c3,0x30c8,0x30eb,
0x4,0x30b0,0x30b9,0x30fc,0x30e0,
0x5,0x30af,0x30eb,0x30b6,0x30fc,0x30c9,
0x2,0x30c7,0x30ab,
0x2,0x30c6,0x30e9,
0x4,0x30c9,0x30e9,0x30af,0x30de,
0x3,0x30d0,0x30fc,0x30c4,
0x4,0x30d1,0x30b9,0x30ab,0x30eb,
0x3,0x30d0,0x30ec,0x30eb,
0x4,0x30d5,0x30a1,0x30e9,0x30c9,
0x4,0x30d5,0x30a7,0x30e0,0x30c8,
0x3,0x30d8,0x30af,0x30c8,
0x7,0x30d8,0x30af,0x30c8,0x30d1,0x30b9,0x30ab,0x30eb,
0x3,0x30da,0x30bb,0x30bf,
0x2,0x30da,0x30bf,
0x3,0x30e6,0x30fc,0x30ed,
0x2,0x30e9,0x30c9,
0x3,0x30eb,0x30af,0x30b9,
0x3,0x30eb,0x30d4,0x30a2,
0x2,0x30a2,0x30c8,
0x3,0x30a6,0x30eb,0x30b7,
0x3,0x30a8,0x30af,0x30b5,
0x8,0x30aa,0x30f3,0x30b0,0x30b9,0x30c8,0x30ed,0x30fc,0x30e0,
0x4,0x30aa,0x30f3,0x30c8,0x30ed,
0x3,0x30ab,0x30c3,0x30d7,
0x6,0x30ad,0x30ed,0x30ea,0x30c4,0x30c8,0x30eb,
0x4,0x30b0,0x30b9,0x30fc,0x30e0,
0x5,0x30af,0x30eb,0x30b6,0x30cf,0x30c9,
0x2,0x30c7,0x30ab,
0x2,0x30c6,0x30e9,
0x4,0x30c9,0x30e9,0x30af,0x30de,
0x3,0x30d0,0x30fc,0x30c4,
0x4,0x30d1,0x30b9,0x30ab,0x30eb,
0x3,0x30d0,0x30ec,0x30eb,
0x4,0x30d5,0x30a1,0x30e9,0x30c9,
0x4,0x30d5,0x30a7,0x30e0,0x30c8,
0x3,0x30d8,0x30af,0x30c8,
0x7,0x30d8,0x30af,0x30c8,0x30d1,0x30b9,0x30ab,0x30eb,
0x3,0x30da,0x30bb,0x30bf,
0x2,0x30da,0x30bf,
0x3,0x30e6,0x30fc,0x30ed,
0x2,0x30e9,0x30c9,
0x3,0x30eb,0x30af,0x30b9,
0x3,0x30eb,0x30d4,0x30a2,
0x4,0x533b,0x7642,0x6cd5,0x4eba,
0x4,0x5b66,0x6821,0x6cd5,0x4eba,
0x4,0x5171,0x540c,0x7d44,0x5408,
0x4,0x5354,0x540c,0x7d44,0x5408,
0x4,0x5408,0x8cc7,0x4f1a,0x793e,
0x4,0x5408,0x540d,0x4f1a,0x793e,
0x4,0x793e,0x56e3,0x6cd5,0x4eba,
0x4,0x5b97,0x6559,0x6cd5,0x4eba,
0x4,0x90f5,0x4fbf,0x756a,0x53f7,
0x4,0x533b,0x7642,0x6cd5,0x4eba,
0x4,0x5b66,0x6821,0x6cd5,0x4eba,
0x4,0x5171,0x540c,0x7d44,0x5408,
0x4,0x5354,0x540c,0x7d44,0x5408,
0x4,0x5408,0x8cc7,0x4f1a,0x793e,
0x4,0x5408,0x540d,0x4f1a,0x793e,
0x4,0x793e,0x56e3,0x6cd5,0x4eba,
0x4,0x5b97,0x6559,0x6cd5,0x4eba,
0x4,0x90f5,0x4fbf,0x756a,0x53f7,
0x3,0x4a,0x41,0x53,
0x2,0x91ce,0x7403,
0x2,0x5c,0x5c,
0x2,0x45,0x30a,
0x2,0x49,0x30a,
0x2,0x4f,0x30a,
0x2,0x65,0x30a,
0x2,0x131,0x30a,
0x2,0x6f,0x30a,
0x2,0x45,0x30a,
0x2,0x49,0x30a,
0x2,0x4f,0x30a,
0x2,0x65,0x30a,
0x2,0x131,0x30a,
0x2,0x6f,0x30a,
0x2,0x304b,0x309a,
0x2,0x304d,0x309a,
0x2,0x304f,0x309a,
0x2,0x3051,0x309a,
0x2,0x3053,0x309a,
0x2,0x30ab,0x309a,
0x2,0x30ad,0x309a,
0x2,0x30af,0x309a,
0x2,0x30b1,0x309a,
0x2,0x30b3,0x309a,
0x2,0x30bb,0x309a,
0x2,0x30c4,0x309a,
0x2,0x30c8,0x309a,
0x2,0x31f7,0x309a,
0x2,0x2e9,0x2e5,
0x2,0x2e5,0x2e9,
0x2,0x304b,0x309a,
0x2,0x304d,0x309a,
0x2,0x304f,0x309a,
0x2,0x3051,0x309a,
0x2,0x3053,0x309a,
0x2,0x30ab,0x309a,
0x2,0x30ad,0x309a,
0x2,0x30af,0x309a,
0x2,0x30b1,0x309a,
0x2,0x30b3,0x309a,
0x2,0x30bb,0x309a,
0x2,0x30c4,0x309a,
0x2,0x30c8,0x309a,
0x2,0x31f7,0x309a,
0x2,0x304b,0x309a,
0x2,0x304d,0x309a,
0x2,0x304f,0x309a,
0x2,0x3051,0x309a,
0x2,0x3053,0x309a,
0x2,0x30ab,0x309a,
0x2,0x30ad,0x309a,
0x2,0x30af,0x309a,
0x2,0x30b1,0x309a,
0x2,0x30b3,0x309a,
0x2,0x30bb,0x309a,
0x2,0x30c4,0x309a,
0x2,0x30c8,0x309a,
0x2,0x31f7,0x309a,
0x2,0x31f7,0x309a,
0x2,0x304b,0x309a,
0x2,0x304d,0x309a,
0x2,0x304f,0x309a,
0x2,0x3051,0x309a,
0x2,0x3053,0x309a,
0x2,0x30ab,0x309a,
0x2,0x30ad,0x309a,
0x2,0x30af,0x309a,
0x2,0x30b1,0x309a,
0x2,0x30b3,0x309a,
0x2,0x30bb,0x309a,
0x2,0x30c4,0x309a,
0x2,0x30c8,0x309a,
0x2,0x31f7,0x309a,
0x2,0x304b,0x309a,
0x2,0x304d,0x309a,
0x2,0x304f,0x309a,
0x2,0x3051,0x309a,
0x2,0x3053,0x309a,
0x2,0x30ab,0x309a,
0x2,0x30ad,0x309a,
0x2,0x30af,0x309a,
0x2,0x30b1,0x309a,
0x2,0x30b3,0x309a,
0x2,0x30bb,0x309a,
0x2,0x30c4,0x309a,
0x2,0x30c8,0x309a,
0x2,0x31f7,0x309a,
0x2,0x304b,0x309a,
0x2,0x304d,0x309a,
0x2,0x304f,0x309a,
0x2,0x3051,0x309a,
0x2,0x3053,0x309a,
0x2,0x30ab,0x309a,
0x2,0x30ad,0x309a,
0x2,0x30af,0x309a,
0x2,0x30b1,0x309a,
0x2,0x30b3,0x309a,
0x2,0x30bb,0x309a,
0x2,0x30c4,0x309a,
0x2,0x30c8,0x309a,
0x2,0x31f7,0x309a,
0x2,0x31f7,0x309a,
0x2,0x2e9,0x2e5,
0x2,0x2e5,0x2e9,
0x2,0x43,0x4c,
0x3,0x4b,0x43,0x4c,
0x3,0x42,0x45,0x4c,
0x2,0x41,0x53,
0x2,0x41,0x4d,
0x2,0x30,0xd7,
0x2,0x31,0xd7,
0x2,0x32,0xd7,
0x2,0x33,0xd7,
0x2,0x34,0xd7,
0x2,0x35,0xd7,
0x2,0x36,0xd7,
0x2,0x37,0xd7,
0x2,0x38,0xd7,
0x2,0x39,0xd7,
0x2,0x307b,0x304b,
0x2,0x50,0x56,
0x2,0x4d,0x56,
0x2,0x53,0x53,
0x2,0x53,0x31,
0x2,0x53,0x32,
0x2,0x53,0x33,
0x2,0x48,0x56,
0x2,0x30,0x30,
0x2,0x30,0x31,
0x2,0x30,0x32,
0x2,0x30,0x33,
0x2,0x30,0x34,
0x2,0x30,0x35,
0x2,0x30,0x36,
0x2,0x30,0x37,
0x2,0x30,0x38,
0x2,0x30,0x39,
0x2,0x31,0x30,
0x2,0x31,0x31,
0x2,0x31,0x32,
0x2,0x31,0x33,
0x2,0x31,0x34,
0x2,0x31,0x35,
0x2,0x31,0x36,
0x2,0x31,0x37,
0x2,0x31,0x38,
0x2,0x31,0x39,
0x2,0x32,0x30,
0x2,0x32,0x31,
0x2,0x32,0x32,
0x2,0x32,0x33,
0x2,0x32,0x34,
0x2,0x32,0x35,
0x2,0x32,0x36,
0x2,0x32,0x37,
0x2,0x32,0x38,
0x2,0x32,0x39,
0x2,0x33,0x30,
0x2,0x33,0x31,
0x2,0x33,0x32,
0x2,0x33,0x33,
0x2,0x33,0x34,
0x2,0x33,0x35,
0x2,0x33,0x36,
0x2,0x33,0x37,
0x2,0x33,0x38,
0x2,0x33,0x39,
0x2,0x34,0x30,
0x2,0x34,0x31,
0x2,0x34,0x32,
0x2,0x34,0x33,
0x2,0x34,0x34,
0x2,0x34,0x35,
0x2,0x34,0x36,
0x2,0x34,0x37,
0x2,0x34,0x38,
0x2,0x34,0x39,
0x2,0x35,0x30,
0x2,0x35,0x31,
0x2,0x35,0x32,
0x2,0x35,0x33,
0x2,0x35,0x34,
0x2,0x35,0x35,
0x2,0x35,0x36,
0x2,0x35,0x37,
0x2,0x35,0x38,
0x2,0x35,0x39,
0x3,0x27,0x30,0x30,
0x3,0x27,0x30,0x31,
0x3,0x27,0x30,0x32,
0x3,0x27,0x30,0x33,
0x3,0x27,0x30,0x34,
0x3,0x27,0x30,0x35,
0x3,0x27,0x30,0x36,
0x3,0x27,0x30,0x37,
0x3,0x27,0x30,0x38,
0x3,0x27,0x30,0x39,
0x3,0x27,0x31,0x30,
0x3,0x27,0x31,0x31,
0x3,0x27,0x31,0x32,
0x3,0x27,0x31,0x33,
0x3,0x27,0x31,0x34,
0x3,0x27,0x31,0x35,
0x3,0x27,0x31,0x36,
0x3,0x27,0x31,0x37,
0x3,0x27,0x31,0x38,
0x3,0x27,0x31,0x39,
0x3,0x27,0x32,0x30,
0x3,0x27,0x32,0x31,
0x3,0x27,0x32,0x32,
0x3,0x27,0x32,0x33,
0x3,0x27,0x32,0x34,
0x3,0x27,0x32,0x35,
0x3,0x27,0x32,0x36,
0x3,0x27,0x32,0x37,
0x3,0x27,0x32,0x38,
0x3,0x27,0x32,0x39,
0x3,0x27,0x33,0x30,
0x3,0x27,0x33,0x31,
0x3,0x27,0x33,0x32,
0x3,0x27,0x33,0x33,
0x3,0x27,0x33,0x34,
0x3,0x27,0x33,0x35,
0x3,0x27,0x33,0x36,
0x3,0x27,0x33,0x37,
0x3,0x27,0x33,0x38,
0x3,0x27,0x33,0x39,
0x3,0x27,0x34,0x30,
0x3,0x27,0x34,0x31,
0x3,0x27,0x34,0x32,
0x3,0x27,0x34,0x33,
0x3,0x27,0x34,0x34,
0x3,0x27,0x34,0x35,
0x3,0x27,0x34,0x36,
0x3,0x27,0x34,0x37,
0x3,0x27,0x34,0x38,
0x3,0x27,0x34,0x39,
0x3,0x27,0x35,0x30,
0x3,0x27,0x35,0x31,
0x3,0x27,0x35,0x32,
0x3,0x27,0x35,0x33,
0x3,0x27,0x35,0x34,
0x3,0x27,0x35,0x35,
0x3,0x27,0x35,0x36,
0x3,0x27,0x35,0x37,
0x3,0x27,0x35,0x38,
0x3,0x27,0x35,0x39,
0x3,0x27,0x36,0x30,
0x3,0x27,0x36,0x31,
0x3,0x27,0x36,0x32,
0x3,0x27,0x36,0x33,
0x3,0x27,0x36,0x34,
0x3,0x27,0x36,0x35,
0x3,0x27,0x36,0x36,
0x3,0x27,0x36,0x37,
0x3,0x27,0x36,0x38,
0x3,0x27,0x36,0x39,
0x3,0x27,0x37,0x30,
0x3,0x27,0x37,0x31,
0x3,0x27,0x37,0x32,
0x3,0x27,0x37,0x33,
0x3,0x27,0x37,0x34,
0x3,0x27,0x37,0x35,
0x3,0x27,0x37,0x36,
0x3,0x27,0x37,0x37,
0x3,0x27,0x37,0x38,
0x3,0x27,0x37,0x39,
0x3,0x27,0x38,0x30,
0x3,0x27,0x38,0x31,
0x3,0x27,0x38,0x32,
0x3,0x27,0x38,0x33,
0x3,0x27,0x38,0x34,
0x3,0x27,0x38,0x35,
0x3,0x27,0x38,0x36,
0x3,0x27,0x38,0x37,
0x3,0x27,0x38,0x38,
0x3,0x27,0x38,0x39,
0x3,0x27,0x39,0x30,
0x3,0x27,0x39,0x31,
0x3,0x27,0x39,0x32,
0x3,0x27,0x39,0x33,
0x3,0x27,0x39,0x34,
0x3,0x27,0x39,0x35,
0x3,0x27,0x39,0x36,
0x3,0x27,0x39,0x37,
0x3,0x27,0x39,0x38,
0x3,0x27,0x39,0x39,
0x2,0x30,0x30,
0x2,0x30,0x31,
0x2,0x30,0x32,
0x2,0x30,0x33,
0x2,0x30,0x34,
0x2,0x30,0x35,
0x2,0x30,0x36,
0x2,0x30,0x37,
0x2,0x30,0x38,
0x2,0x30,0x39,
0x2,0x31,0x30,
0x2,0x31,0x31,
0x2,0x31,0x32,
0x2,0x31,0x33,
0x2,0x31,0x34,
0x2,0x31,0x35,
0x2,0x31,0x36,
0x2,0x31,0x37,
0x2,0x31,0x38,
0x2,0x31,0x39,
0x2,0x32,0x30,
0x2,0x32,0x31,
0x2,0x32,0x32,
0x2,0x32,0x33,
0x2,0x32,0x34,
0x2,0x32,0x35,
0x2,0x32,0x36,
0x2,0x32,0x37,
0x2,0x32,0x38,
0x2,0x32,0x39,
0x2,0x33,0x30,
0x2,0x33,0x31,
0x2,0x33,0x32,
0x2,0x33,0x33,
0x2,0x33,0x34,
0x2,0x33,0x35,
0x2,0x33,0x36,
0x2,0x33,0x37,
0x2,0x33,0x38,
0x2,0x33,0x39,
0x2,0x34,0x30,
0x2,0x34,0x31,
0x2,0x34,0x32,
0x2,0x34,0x33,
0x2,0x34,0x34,
0x2,0x34,0x35,
0x2,0x34,0x36,
0x2,0x34,0x37,
0x2,0x34,0x38,
0x2,0x34,0x39,
0x2,0x35,0x30,
0x2,0x35,0x31,
0x2,0x35,0x32,
0x2,0x35,0x33,
0x2,0x35,0x34,
0x2,0x35,0x35,
0x2,0x35,0x36,
0x2,0x35,0x37,
0x2,0x35,0x38,
0x2,0x35,0x39,
0x2,0x36,0x30,
0x2,0x36,0x31,
0x2,0x36,0x32,
0x2,0x36,0x33,
0x2,0x36,0x34,
0x2,0x36,0x35,
0x2,0x36,0x36,
0x2,0x36,0x37,
0x2,0x36,0x38,
0x2,0x36,0x39,
0x2,0x37,0x30,
0x2,0x37,0x31,
0x2,0x37,0x32,
0x2,0x37,0x33,
0x2,0x37,0x34,
0x2,0x37,0x35,
0x2,0x37,0x36,
0x2,0x37,0x37,
0x2,0x37,0x38,
0x2,0x37,0x39,
0x2,0x38,0x30,
0x2,0x38,0x31,
0x2,0x38,0x32,
0x2,0x38,0x33,
0x2,0x38,0x34,
0x2,0x38,0x35,
0x2,0x38,0x36,
0x2,0x38,0x37,
0x2,0x38,0x38,
0x2,0x38,0x39,
0x2,0x39,0x30,
0x2,0x39,0x31,
0x2,0x39,0x32,
0x2,0x39,0x33,
0x2,0x39,0x34,
0x2,0x39,0x35,
0x2,0x39,0x36,
0x2,0x39,0x37,
0x2,0x39,0x38,
0x2,0x39,0x39,
0x3,0x31,0x30,0x30,
0x3,0x31,0x30,0x31,
0x3,0x31,0x30,0x32,
0x3,0x31,0x30,0x33,
0x3,0x31,0x30,0x34,
0x3,0x31,0x30,0x35,
0x3,0x31,0x30,0x36,
0x3,0x31,0x30,0x37,
0x3,0x31,0x30,0x38,
0x3,0x31,0x30,0x39,
0x3,0x31,0x31,0x30,
0x3,0x31,0x31,0x31,
0x3,0x31,0x31,0x32,
0x3,0x31,0x31,0x33,
0x3,0x31,0x31,0x34,
0x3,0x31,0x31,0x35,
0x3,0x31,0x31,0x36,
0x3,0x31,0x31,0x37,
0x3,0x31,0x31,0x38,
0x3,0x31,0x31,0x39,
0x3,0x31,0x32,0x30,
0x3,0x31,0x32,0x31,
0x3,0x31,0x32,0x32,
0x3,0x31,0x32,0x33,
0x3,0x31,0x32,0x34,
0x3,0x31,0x32,0x35,
0x3,0x31,0x32,0x36,
0x3,0x31,0x32,0x37,
0x3,0x31,0x32,0x38,
0x3,0x31,0x32,0x39,
0x3,0x31,0x33,0x30,
0x3,0x31,0x33,0x31,
0x3,0x31,0x33,0x32,
0x3,0x31,0x33,0x33,
0x3,0x31,0x33,0x34,
0x3,0x31,0x33,0x35,
0x3,0x31,0x33,0x36,
0x3,0x31,0x33,0x37,
0x3,0x31,0x33,0x38,
0x3,0x31,0x33,0x39,
0x3,0x31,0x34,0x30,
0x3,0x31,0x34,0x31,
0x3,0x31,0x34,0x32,
0x3,0x31,0x34,0x33,
0x3,0x31,0x34,0x34,
0x3,0x31,0x34,0x35,
0x3,0x31,0x34,0x36,
0x3,0x31,0x34,0x37,
0x3,0x31,0x34,0x38,
0x3,0x31,0x34,0x39,
0x2,0x2e,0x30,
0x2,0x2e,0x31,
0x2,0x2e,0x32,
0x2,0x2e,0x33,
0x2,0x2e,0x34,
0x2,0x2e,0x35,
0x2,0x2e,0x36,
0x2,0x2e,0x37,
0x2,0x2e,0x38,
0x2,0x2e,0x39,
0x2,0x2c,0x30,
0x2,0x2c,0x31,
0x2,0x2c,0x32,
0x2,0x2c,0x33,
0x2,0x2c,0x34,
0x2,0x2c,0x35,
0x2,0x2c,0x36,
0x2,0x2c,0x37,
0x2,0x2c,0x38,
0x2,0x2c,0x39,
0x3,0x70,0x70,0x62,
0x3,0x70,0x70,0x6d,
0x2,0x27,0x53,
0x2,0x48,0x32,
0x2,0x4f,0x32,
0x2,0x4f,0x78,
0x2,0x4e,0x78,
0x2,0x51,0x32,
0x3,0x4a,0x72,0x2e,
0x3,0x44,0x72,0x2e,
0x2,0x30ac,0x30eb,
0x3,0x30b0,0x30ec,0x30a4,
0x4,0x30af,0x30ed,0x30fc,0x30ca,
0x5,0x30b7,0x30fc,0x30d9,0x30eb,0x30c8,
0x4,0x30b7,0x30a7,0x30b1,0x30eb,
0x4,0x30b8,0x30e5,0x30fc,0x30eb,
0x4,0x30c7,0x30b7,0x30d9,0x30eb,
0x3,0x30c9,0x30c3,0x30c8,
0x3,0x30d0,0x30a4,0x30c8,
0x3,0x30d3,0x30c3,0x30c8,
0x4,0x30d9,0x30af,0x30ec,0x30eb,
0x2,0x30dc,0x30fc,
0x3,0x30e9,0x30f3,0x30c9,
0x5,0x30ea,0x30f3,0x30ae,0x30c3,0x30c8,
0x2,0x30ac,0x30eb,
0x3,0x30b0,0x30ec,0x30a4,
0x4,0x30af,0x30ed,0x30fc,0x30ca,
0x5,0x30b7,0x30fc,0x30d9,0x30eb,0x30c8,
0x4,0x30b7,0x30a7,0x30b1,0x30eb,
0x4,0x30b8,0x30e5,0x30fc,0x30eb,
0x4,0x30c7,0x30b7,0x30d9,0x30eb,
0x3,0x30c9,0x30c3,0x30c8,
0x3,0x30d0,0x30a4,0x30c8,
0x3,0x30d3,0x30c3,0x30c8,
0x4,0x30d9,0x30af,0x30ec,0x30eb,
0x2,0x30dc,0x30fc,
0x3,0x30e9,0x30f3,0x30c9,
0x5,0x30ea,0x30f3,0x30ae,0x30c3,0x30c8,
0x2,0x3002,0x300d,
0x2,0x3002,0x300f,
};

static pdf_cmap cmap_Adobe_Japan1_UCS2 = {
	{ -1, pdf_drop_cmap_imp },
	/* cmapname */ "Adobe-Japan1-UCS2",
	/* usecmap */ "", NULL,
	/* wmode */ 0,
	/* codespaces */ 1, {
		{ 2, 0x0000, 0xffff },
	},
	16762, 16762, (pdf_range*)cmap_Adobe_Japan1_UCS2_ranges,
	384, 384, (pdf_xrange*)cmap_Adobe_Japan1_UCS2_xranges,
	1361, 1361, (pdf_mrange*)cmap_Adobe_Japan1_UCS2_mranges,
	4553, 4553, (int*)cmap_Adobe_Japan1_UCS2_table,
	0, 0, 0, NULL /* splay tree */
};
