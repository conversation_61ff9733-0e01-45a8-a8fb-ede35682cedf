struct css_property_info { const char *name; int key; };
%define hash-function-name css_property_hash
%define lookup-function-name css_property_lookup
%define word-array-name css_property_list
%global-table
%struct-type
%%
-mupdf-leading,PRO_LEADING
-webkit-text-fill-color,PRO_TEXT_FILL_COLOR
-webkit-text-stroke-color,PRO_TEXT_STROKE_COLOR
-webkit-text-stroke-width,PRO_TEXT_STROKE_WIDTH
background-color,PRO_BACKGROUND_COLOR
border,PRO_BORDER
border-bottom,PRO_BORDER_BOTTOM
border-bottom-color,PRO_BORDER_BOTTOM_COLOR
border-bottom-style,PRO_BORDER_BOTTOM_STYLE
border-bottom-width,PRO_BORDER_BOTTOM_WIDTH
border-color,PRO_BORDER_COLOR
border-left,PRO_BORDER_LEFT
border-left-color,PRO_BORDER_LEFT_COLOR
border-left-style,PRO_BORDER_LEFT_STYLE
border-left-width,PRO_BORDER_LEFT_WIDTH
border-right,PRO_BORDER_RIGHT
border-right-color,PRO_BORDER_RIGHT_COLOR
border-right-style,PRO_BORDER_RIGHT_STYLE
border-right-width,PRO_BORDER_RIGHT_WIDTH
border-spacing,PRO_BORDER_SPACING
border-style,PRO_BORDER_STYLE
border-top,PRO_BORDER_TOP
border-top-color,PRO_BORDER_TOP_COLOR
border-top-style,PRO_BORDER_TOP_STYLE
border-top-width,PRO_BORDER_TOP_WIDTH
border-width,PRO_BORDER_WIDTH
color,PRO_COLOR
direction,PRO_DIRECTION
display,PRO_DISPLAY
font,PRO_FONT
font-family,PRO_FONT_FAMILY
font-size,PRO_FONT_SIZE
font-style,PRO_FONT_STYLE
font-variant,PRO_FONT_VARIANT
font-weight,PRO_FONT_WEIGHT
height,PRO_HEIGHT
letter-spacing,PRO_LETTER_SPACING
line-height,PRO_LINE_HEIGHT
list-style,PRO_LIST_STYLE
list-style-image,PRO_LIST_STYLE_IMAGE
list-style-position,PRO_LIST_STYLE_POSITION
list-style-type,PRO_LIST_STYLE_TYPE
margin,PRO_MARGIN
margin-bottom,PRO_MARGIN_BOTTOM
margin-left,PRO_MARGIN_LEFT
margin-right,PRO_MARGIN_RIGHT
margin-top,PRO_MARGIN_TOP
orphans,PRO_ORPHANS
overflow-wrap,PRO_OVERFLOW_WRAP
padding,PRO_PADDING
padding-bottom,PRO_PADDING_BOTTOM
padding-left,PRO_PADDING_LEFT
padding-right,PRO_PADDING_RIGHT
padding-top,PRO_PADDING_TOP
page-break-after,PRO_PAGE_BREAK_AFTER
page-break-before,PRO_PAGE_BREAK_BEFORE
quotes,PRO_QUOTES
src,PRO_SRC
text-align,PRO_TEXT_ALIGN
text-decoration,PRO_TEXT_DECORATION
text-indent,PRO_TEXT_INDENT
text-transform,PRO_TEXT_TRANSFORM
vertical-align,PRO_VERTICAL_ALIGN
visibility,PRO_VISIBILITY
white-space,PRO_WHITE_SPACE
widows,PRO_WIDOWS
width,PRO_WIDTH
word-spacing,PRO_WORD_SPACING
