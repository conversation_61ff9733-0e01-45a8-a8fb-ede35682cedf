/* This file was automatically created from UnicodeData.txt */

static const int ucd_tolower2[] = {
0x41,0x5a,32,
0xc0,0xd6,32,
0xd8,0xde,32,
0x189,0x18a,205,
0x1b1,0x1b2,217,
0x388,0x38a,37,
0x38e,0x38f,63,
0x391,0x3a1,32,
0x3a3,0x3ab,32,
0x3fd,0x3ff,-130,
0x400,0x40f,80,
0x410,0x42f,32,
0x531,0x556,48,
0x10a0,0x10c5,7264,
0x13a0,0x13ef,38864,
0x13f0,0x13f5,8,
0x1c90,0x1cba,-3008,
0x1cbd,0x1cbf,-3008,
0x1f08,0x1f0f,-8,
0x1f18,0x1f1d,-8,
0x1f28,0x1f2f,-8,
0x1f38,0x1f3f,-8,
0x1f48,0x1f4d,-8,
0x1f68,0x1f6f,-8,
0x1f88,0x1f8f,-8,
0x1f98,0x1f9f,-8,
0x1fa8,0x1faf,-8,
0x1fb8,0x1fb9,-8,
0x1fba,0x1fbb,-74,
0x1fc8,0x1fcb,-86,
0x1fd8,0x1fd9,-8,
0x1fda,0x1fdb,-100,
0x1fe8,0x1fe9,-8,
0x1fea,0x1feb,-112,
0x1ff8,0x1ff9,-128,
0x1ffa,0x1ffb,-126,
0x2160,0x216f,16,
0x24b6,0x24cf,26,
0x2c00,0x2c2f,48,
0x2c7e,0x2c7f,-10815,
0xff21,0xff3a,32,
0x10400,0x10427,40,
0x104b0,0x104d3,40,
0x10570,0x1057a,39,
0x1057c,0x1058a,39,
0x1058c,0x10592,39,
0x10594,0x10595,39,
0x10c80,0x10cb2,64,
0x10d50,0x10d65,32,
0x118a0,0x118bf,32,
0x16e40,0x16e5f,32,
0x1e900,0x1e921,34,
};

static const int ucd_tolower1[] = {
0x100,1,
0x102,1,
0x104,1,
0x106,1,
0x108,1,
0x10a,1,
0x10c,1,
0x10e,1,
0x110,1,
0x112,1,
0x114,1,
0x116,1,
0x118,1,
0x11a,1,
0x11c,1,
0x11e,1,
0x120,1,
0x122,1,
0x124,1,
0x126,1,
0x128,1,
0x12a,1,
0x12c,1,
0x12e,1,
0x130,-199,
0x132,1,
0x134,1,
0x136,1,
0x139,1,
0x13b,1,
0x13d,1,
0x13f,1,
0x141,1,
0x143,1,
0x145,1,
0x147,1,
0x14a,1,
0x14c,1,
0x14e,1,
0x150,1,
0x152,1,
0x154,1,
0x156,1,
0x158,1,
0x15a,1,
0x15c,1,
0x15e,1,
0x160,1,
0x162,1,
0x164,1,
0x166,1,
0x168,1,
0x16a,1,
0x16c,1,
0x16e,1,
0x170,1,
0x172,1,
0x174,1,
0x176,1,
0x178,-121,
0x179,1,
0x17b,1,
0x17d,1,
0x181,210,
0x182,1,
0x184,1,
0x186,206,
0x187,1,
0x18b,1,
0x18e,79,
0x18f,202,
0x190,203,
0x191,1,
0x193,205,
0x194,207,
0x196,211,
0x197,209,
0x198,1,
0x19c,211,
0x19d,213,
0x19f,214,
0x1a0,1,
0x1a2,1,
0x1a4,1,
0x1a6,218,
0x1a7,1,
0x1a9,218,
0x1ac,1,
0x1ae,218,
0x1af,1,
0x1b3,1,
0x1b5,1,
0x1b7,219,
0x1b8,1,
0x1bc,1,
0x1c4,2,
0x1c5,1,
0x1c7,2,
0x1c8,1,
0x1ca,2,
0x1cb,1,
0x1cd,1,
0x1cf,1,
0x1d1,1,
0x1d3,1,
0x1d5,1,
0x1d7,1,
0x1d9,1,
0x1db,1,
0x1de,1,
0x1e0,1,
0x1e2,1,
0x1e4,1,
0x1e6,1,
0x1e8,1,
0x1ea,1,
0x1ec,1,
0x1ee,1,
0x1f1,2,
0x1f2,1,
0x1f4,1,
0x1f6,-97,
0x1f7,-56,
0x1f8,1,
0x1fa,1,
0x1fc,1,
0x1fe,1,
0x200,1,
0x202,1,
0x204,1,
0x206,1,
0x208,1,
0x20a,1,
0x20c,1,
0x20e,1,
0x210,1,
0x212,1,
0x214,1,
0x216,1,
0x218,1,
0x21a,1,
0x21c,1,
0x21e,1,
0x220,-130,
0x222,1,
0x224,1,
0x226,1,
0x228,1,
0x22a,1,
0x22c,1,
0x22e,1,
0x230,1,
0x232,1,
0x23a,10795,
0x23b,1,
0x23d,-163,
0x23e,10792,
0x241,1,
0x243,-195,
0x244,69,
0x245,71,
0x246,1,
0x248,1,
0x24a,1,
0x24c,1,
0x24e,1,
0x370,1,
0x372,1,
0x376,1,
0x37f,116,
0x386,38,
0x38c,64,
0x3cf,8,
0x3d8,1,
0x3da,1,
0x3dc,1,
0x3de,1,
0x3e0,1,
0x3e2,1,
0x3e4,1,
0x3e6,1,
0x3e8,1,
0x3ea,1,
0x3ec,1,
0x3ee,1,
0x3f4,-60,
0x3f7,1,
0x3f9,-7,
0x3fa,1,
0x460,1,
0x462,1,
0x464,1,
0x466,1,
0x468,1,
0x46a,1,
0x46c,1,
0x46e,1,
0x470,1,
0x472,1,
0x474,1,
0x476,1,
0x478,1,
0x47a,1,
0x47c,1,
0x47e,1,
0x480,1,
0x48a,1,
0x48c,1,
0x48e,1,
0x490,1,
0x492,1,
0x494,1,
0x496,1,
0x498,1,
0x49a,1,
0x49c,1,
0x49e,1,
0x4a0,1,
0x4a2,1,
0x4a4,1,
0x4a6,1,
0x4a8,1,
0x4aa,1,
0x4ac,1,
0x4ae,1,
0x4b0,1,
0x4b2,1,
0x4b4,1,
0x4b6,1,
0x4b8,1,
0x4ba,1,
0x4bc,1,
0x4be,1,
0x4c0,15,
0x4c1,1,
0x4c3,1,
0x4c5,1,
0x4c7,1,
0x4c9,1,
0x4cb,1,
0x4cd,1,
0x4d0,1,
0x4d2,1,
0x4d4,1,
0x4d6,1,
0x4d8,1,
0x4da,1,
0x4dc,1,
0x4de,1,
0x4e0,1,
0x4e2,1,
0x4e4,1,
0x4e6,1,
0x4e8,1,
0x4ea,1,
0x4ec,1,
0x4ee,1,
0x4f0,1,
0x4f2,1,
0x4f4,1,
0x4f6,1,
0x4f8,1,
0x4fa,1,
0x4fc,1,
0x4fe,1,
0x500,1,
0x502,1,
0x504,1,
0x506,1,
0x508,1,
0x50a,1,
0x50c,1,
0x50e,1,
0x510,1,
0x512,1,
0x514,1,
0x516,1,
0x518,1,
0x51a,1,
0x51c,1,
0x51e,1,
0x520,1,
0x522,1,
0x524,1,
0x526,1,
0x528,1,
0x52a,1,
0x52c,1,
0x52e,1,
0x10c7,7264,
0x10cd,7264,
0x1c89,1,
0x1e00,1,
0x1e02,1,
0x1e04,1,
0x1e06,1,
0x1e08,1,
0x1e0a,1,
0x1e0c,1,
0x1e0e,1,
0x1e10,1,
0x1e12,1,
0x1e14,1,
0x1e16,1,
0x1e18,1,
0x1e1a,1,
0x1e1c,1,
0x1e1e,1,
0x1e20,1,
0x1e22,1,
0x1e24,1,
0x1e26,1,
0x1e28,1,
0x1e2a,1,
0x1e2c,1,
0x1e2e,1,
0x1e30,1,
0x1e32,1,
0x1e34,1,
0x1e36,1,
0x1e38,1,
0x1e3a,1,
0x1e3c,1,
0x1e3e,1,
0x1e40,1,
0x1e42,1,
0x1e44,1,
0x1e46,1,
0x1e48,1,
0x1e4a,1,
0x1e4c,1,
0x1e4e,1,
0x1e50,1,
0x1e52,1,
0x1e54,1,
0x1e56,1,
0x1e58,1,
0x1e5a,1,
0x1e5c,1,
0x1e5e,1,
0x1e60,1,
0x1e62,1,
0x1e64,1,
0x1e66,1,
0x1e68,1,
0x1e6a,1,
0x1e6c,1,
0x1e6e,1,
0x1e70,1,
0x1e72,1,
0x1e74,1,
0x1e76,1,
0x1e78,1,
0x1e7a,1,
0x1e7c,1,
0x1e7e,1,
0x1e80,1,
0x1e82,1,
0x1e84,1,
0x1e86,1,
0x1e88,1,
0x1e8a,1,
0x1e8c,1,
0x1e8e,1,
0x1e90,1,
0x1e92,1,
0x1e94,1,
0x1e9e,-7615,
0x1ea0,1,
0x1ea2,1,
0x1ea4,1,
0x1ea6,1,
0x1ea8,1,
0x1eaa,1,
0x1eac,1,
0x1eae,1,
0x1eb0,1,
0x1eb2,1,
0x1eb4,1,
0x1eb6,1,
0x1eb8,1,
0x1eba,1,
0x1ebc,1,
0x1ebe,1,
0x1ec0,1,
0x1ec2,1,
0x1ec4,1,
0x1ec6,1,
0x1ec8,1,
0x1eca,1,
0x1ecc,1,
0x1ece,1,
0x1ed0,1,
0x1ed2,1,
0x1ed4,1,
0x1ed6,1,
0x1ed8,1,
0x1eda,1,
0x1edc,1,
0x1ede,1,
0x1ee0,1,
0x1ee2,1,
0x1ee4,1,
0x1ee6,1,
0x1ee8,1,
0x1eea,1,
0x1eec,1,
0x1eee,1,
0x1ef0,1,
0x1ef2,1,
0x1ef4,1,
0x1ef6,1,
0x1ef8,1,
0x1efa,1,
0x1efc,1,
0x1efe,1,
0x1f59,-8,
0x1f5b,-8,
0x1f5d,-8,
0x1f5f,-8,
0x1fbc,-9,
0x1fcc,-9,
0x1fec,-7,
0x1ffc,-9,
0x2126,-7517,
0x212a,-8383,
0x212b,-8262,
0x2132,28,
0x2183,1,
0x2c60,1,
0x2c62,-10743,
0x2c63,-3814,
0x2c64,-10727,
0x2c67,1,
0x2c69,1,
0x2c6b,1,
0x2c6d,-10780,
0x2c6e,-10749,
0x2c6f,-10783,
0x2c70,-10782,
0x2c72,1,
0x2c75,1,
0x2c80,1,
0x2c82,1,
0x2c84,1,
0x2c86,1,
0x2c88,1,
0x2c8a,1,
0x2c8c,1,
0x2c8e,1,
0x2c90,1,
0x2c92,1,
0x2c94,1,
0x2c96,1,
0x2c98,1,
0x2c9a,1,
0x2c9c,1,
0x2c9e,1,
0x2ca0,1,
0x2ca2,1,
0x2ca4,1,
0x2ca6,1,
0x2ca8,1,
0x2caa,1,
0x2cac,1,
0x2cae,1,
0x2cb0,1,
0x2cb2,1,
0x2cb4,1,
0x2cb6,1,
0x2cb8,1,
0x2cba,1,
0x2cbc,1,
0x2cbe,1,
0x2cc0,1,
0x2cc2,1,
0x2cc4,1,
0x2cc6,1,
0x2cc8,1,
0x2cca,1,
0x2ccc,1,
0x2cce,1,
0x2cd0,1,
0x2cd2,1,
0x2cd4,1,
0x2cd6,1,
0x2cd8,1,
0x2cda,1,
0x2cdc,1,
0x2cde,1,
0x2ce0,1,
0x2ce2,1,
0x2ceb,1,
0x2ced,1,
0x2cf2,1,
0xa640,1,
0xa642,1,
0xa644,1,
0xa646,1,
0xa648,1,
0xa64a,1,
0xa64c,1,
0xa64e,1,
0xa650,1,
0xa652,1,
0xa654,1,
0xa656,1,
0xa658,1,
0xa65a,1,
0xa65c,1,
0xa65e,1,
0xa660,1,
0xa662,1,
0xa664,1,
0xa666,1,
0xa668,1,
0xa66a,1,
0xa66c,1,
0xa680,1,
0xa682,1,
0xa684,1,
0xa686,1,
0xa688,1,
0xa68a,1,
0xa68c,1,
0xa68e,1,
0xa690,1,
0xa692,1,
0xa694,1,
0xa696,1,
0xa698,1,
0xa69a,1,
0xa722,1,
0xa724,1,
0xa726,1,
0xa728,1,
0xa72a,1,
0xa72c,1,
0xa72e,1,
0xa732,1,
0xa734,1,
0xa736,1,
0xa738,1,
0xa73a,1,
0xa73c,1,
0xa73e,1,
0xa740,1,
0xa742,1,
0xa744,1,
0xa746,1,
0xa748,1,
0xa74a,1,
0xa74c,1,
0xa74e,1,
0xa750,1,
0xa752,1,
0xa754,1,
0xa756,1,
0xa758,1,
0xa75a,1,
0xa75c,1,
0xa75e,1,
0xa760,1,
0xa762,1,
0xa764,1,
0xa766,1,
0xa768,1,
0xa76a,1,
0xa76c,1,
0xa76e,1,
0xa779,1,
0xa77b,1,
0xa77d,-35332,
0xa77e,1,
0xa780,1,
0xa782,1,
0xa784,1,
0xa786,1,
0xa78b,1,
0xa78d,-42280,
0xa790,1,
0xa792,1,
0xa796,1,
0xa798,1,
0xa79a,1,
0xa79c,1,
0xa79e,1,
0xa7a0,1,
0xa7a2,1,
0xa7a4,1,
0xa7a6,1,
0xa7a8,1,
0xa7aa,-42308,
0xa7ab,-42319,
0xa7ac,-42315,
0xa7ad,-42305,
0xa7ae,-42308,
0xa7b0,-42258,
0xa7b1,-42282,
0xa7b2,-42261,
0xa7b3,928,
0xa7b4,1,
0xa7b6,1,
0xa7b8,1,
0xa7ba,1,
0xa7bc,1,
0xa7be,1,
0xa7c0,1,
0xa7c2,1,
0xa7c4,-48,
0xa7c5,-42307,
0xa7c6,-35384,
0xa7c7,1,
0xa7c9,1,
0xa7cb,-42343,
0xa7cc,1,
0xa7d0,1,
0xa7d6,1,
0xa7d8,1,
0xa7da,1,
0xa7dc,-42561,
0xa7f5,1,
};

static const int ucd_toupper2[] = {
0x61,0x7a,-32,
0xe0,0xf6,-32,
0xf8,0xfe,-32,
0x23f,0x240,10815,
0x256,0x257,-205,
0x28a,0x28b,-217,
0x37b,0x37d,130,
0x3ad,0x3af,-37,
0x3b1,0x3c1,-32,
0x3c3,0x3cb,-32,
0x3cd,0x3ce,-63,
0x430,0x44f,-32,
0x450,0x45f,-80,
0x561,0x586,-48,
0x10d0,0x10fa,3008,
0x10fd,0x10ff,3008,
0x13f8,0x13fd,-8,
0x1c83,0x1c84,-6242,
0x1f00,0x1f07,8,
0x1f10,0x1f15,8,
0x1f20,0x1f27,8,
0x1f30,0x1f37,8,
0x1f40,0x1f45,8,
0x1f60,0x1f67,8,
0x1f70,0x1f71,74,
0x1f72,0x1f75,86,
0x1f76,0x1f77,100,
0x1f78,0x1f79,128,
0x1f7a,0x1f7b,112,
0x1f7c,0x1f7d,126,
0x1f80,0x1f87,8,
0x1f90,0x1f97,8,
0x1fa0,0x1fa7,8,
0x1fb0,0x1fb1,8,
0x1fd0,0x1fd1,8,
0x1fe0,0x1fe1,8,
0x2170,0x217f,-16,
0x24d0,0x24e9,-26,
0x2c30,0x2c5f,-48,
0x2d00,0x2d25,-7264,
0xab70,0xabbf,-38864,
0xff41,0xff5a,-32,
0x10428,0x1044f,-40,
0x104d8,0x104fb,-40,
0x10597,0x105a1,-39,
0x105a3,0x105b1,-39,
0x105b3,0x105b9,-39,
0x105bb,0x105bc,-39,
0x10cc0,0x10cf2,-64,
0x10d70,0x10d85,-32,
0x118c0,0x118df,-32,
0x16e60,0x16e7f,-32,
0x1e922,0x1e943,-34,
};

static const int ucd_toupper1[] = {
0xb5,743,
0xff,121,
0x101,-1,
0x103,-1,
0x105,-1,
0x107,-1,
0x109,-1,
0x10b,-1,
0x10d,-1,
0x10f,-1,
0x111,-1,
0x113,-1,
0x115,-1,
0x117,-1,
0x119,-1,
0x11b,-1,
0x11d,-1,
0x11f,-1,
0x121,-1,
0x123,-1,
0x125,-1,
0x127,-1,
0x129,-1,
0x12b,-1,
0x12d,-1,
0x12f,-1,
0x131,-232,
0x133,-1,
0x135,-1,
0x137,-1,
0x13a,-1,
0x13c,-1,
0x13e,-1,
0x140,-1,
0x142,-1,
0x144,-1,
0x146,-1,
0x148,-1,
0x14b,-1,
0x14d,-1,
0x14f,-1,
0x151,-1,
0x153,-1,
0x155,-1,
0x157,-1,
0x159,-1,
0x15b,-1,
0x15d,-1,
0x15f,-1,
0x161,-1,
0x163,-1,
0x165,-1,
0x167,-1,
0x169,-1,
0x16b,-1,
0x16d,-1,
0x16f,-1,
0x171,-1,
0x173,-1,
0x175,-1,
0x177,-1,
0x17a,-1,
0x17c,-1,
0x17e,-1,
0x17f,-300,
0x180,195,
0x183,-1,
0x185,-1,
0x188,-1,
0x18c,-1,
0x192,-1,
0x195,97,
0x199,-1,
0x19a,163,
0x19b,42561,
0x19e,130,
0x1a1,-1,
0x1a3,-1,
0x1a5,-1,
0x1a8,-1,
0x1ad,-1,
0x1b0,-1,
0x1b4,-1,
0x1b6,-1,
0x1b9,-1,
0x1bd,-1,
0x1bf,56,
0x1c5,-1,
0x1c6,-2,
0x1c8,-1,
0x1c9,-2,
0x1cb,-1,
0x1cc,-2,
0x1ce,-1,
0x1d0,-1,
0x1d2,-1,
0x1d4,-1,
0x1d6,-1,
0x1d8,-1,
0x1da,-1,
0x1dc,-1,
0x1dd,-79,
0x1df,-1,
0x1e1,-1,
0x1e3,-1,
0x1e5,-1,
0x1e7,-1,
0x1e9,-1,
0x1eb,-1,
0x1ed,-1,
0x1ef,-1,
0x1f2,-1,
0x1f3,-2,
0x1f5,-1,
0x1f9,-1,
0x1fb,-1,
0x1fd,-1,
0x1ff,-1,
0x201,-1,
0x203,-1,
0x205,-1,
0x207,-1,
0x209,-1,
0x20b,-1,
0x20d,-1,
0x20f,-1,
0x211,-1,
0x213,-1,
0x215,-1,
0x217,-1,
0x219,-1,
0x21b,-1,
0x21d,-1,
0x21f,-1,
0x223,-1,
0x225,-1,
0x227,-1,
0x229,-1,
0x22b,-1,
0x22d,-1,
0x22f,-1,
0x231,-1,
0x233,-1,
0x23c,-1,
0x242,-1,
0x247,-1,
0x249,-1,
0x24b,-1,
0x24d,-1,
0x24f,-1,
0x250,10783,
0x251,10780,
0x252,10782,
0x253,-210,
0x254,-206,
0x259,-202,
0x25b,-203,
0x25c,42319,
0x260,-205,
0x261,42315,
0x263,-207,
0x264,42343,
0x265,42280,
0x266,42308,
0x268,-209,
0x269,-211,
0x26a,42308,
0x26b,10743,
0x26c,42305,
0x26f,-211,
0x271,10749,
0x272,-213,
0x275,-214,
0x27d,10727,
0x280,-218,
0x282,42307,
0x283,-218,
0x287,42282,
0x288,-218,
0x289,-69,
0x28c,-71,
0x292,-219,
0x29d,42261,
0x29e,42258,
0x345,84,
0x371,-1,
0x373,-1,
0x377,-1,
0x3ac,-38,
0x3c2,-31,
0x3cc,-64,
0x3d0,-62,
0x3d1,-57,
0x3d5,-47,
0x3d6,-54,
0x3d7,-8,
0x3d9,-1,
0x3db,-1,
0x3dd,-1,
0x3df,-1,
0x3e1,-1,
0x3e3,-1,
0x3e5,-1,
0x3e7,-1,
0x3e9,-1,
0x3eb,-1,
0x3ed,-1,
0x3ef,-1,
0x3f0,-86,
0x3f1,-80,
0x3f2,7,
0x3f3,-116,
0x3f5,-96,
0x3f8,-1,
0x3fb,-1,
0x461,-1,
0x463,-1,
0x465,-1,
0x467,-1,
0x469,-1,
0x46b,-1,
0x46d,-1,
0x46f,-1,
0x471,-1,
0x473,-1,
0x475,-1,
0x477,-1,
0x479,-1,
0x47b,-1,
0x47d,-1,
0x47f,-1,
0x481,-1,
0x48b,-1,
0x48d,-1,
0x48f,-1,
0x491,-1,
0x493,-1,
0x495,-1,
0x497,-1,
0x499,-1,
0x49b,-1,
0x49d,-1,
0x49f,-1,
0x4a1,-1,
0x4a3,-1,
0x4a5,-1,
0x4a7,-1,
0x4a9,-1,
0x4ab,-1,
0x4ad,-1,
0x4af,-1,
0x4b1,-1,
0x4b3,-1,
0x4b5,-1,
0x4b7,-1,
0x4b9,-1,
0x4bb,-1,
0x4bd,-1,
0x4bf,-1,
0x4c2,-1,
0x4c4,-1,
0x4c6,-1,
0x4c8,-1,
0x4ca,-1,
0x4cc,-1,
0x4ce,-1,
0x4cf,-15,
0x4d1,-1,
0x4d3,-1,
0x4d5,-1,
0x4d7,-1,
0x4d9,-1,
0x4db,-1,
0x4dd,-1,
0x4df,-1,
0x4e1,-1,
0x4e3,-1,
0x4e5,-1,
0x4e7,-1,
0x4e9,-1,
0x4eb,-1,
0x4ed,-1,
0x4ef,-1,
0x4f1,-1,
0x4f3,-1,
0x4f5,-1,
0x4f7,-1,
0x4f9,-1,
0x4fb,-1,
0x4fd,-1,
0x4ff,-1,
0x501,-1,
0x503,-1,
0x505,-1,
0x507,-1,
0x509,-1,
0x50b,-1,
0x50d,-1,
0x50f,-1,
0x511,-1,
0x513,-1,
0x515,-1,
0x517,-1,
0x519,-1,
0x51b,-1,
0x51d,-1,
0x51f,-1,
0x521,-1,
0x523,-1,
0x525,-1,
0x527,-1,
0x529,-1,
0x52b,-1,
0x52d,-1,
0x52f,-1,
0x1c80,-6254,
0x1c81,-6253,
0x1c82,-6244,
0x1c85,-6243,
0x1c86,-6236,
0x1c87,-6181,
0x1c88,35266,
0x1c8a,-1,
0x1d79,35332,
0x1d7d,3814,
0x1d8e,35384,
0x1e01,-1,
0x1e03,-1,
0x1e05,-1,
0x1e07,-1,
0x1e09,-1,
0x1e0b,-1,
0x1e0d,-1,
0x1e0f,-1,
0x1e11,-1,
0x1e13,-1,
0x1e15,-1,
0x1e17,-1,
0x1e19,-1,
0x1e1b,-1,
0x1e1d,-1,
0x1e1f,-1,
0x1e21,-1,
0x1e23,-1,
0x1e25,-1,
0x1e27,-1,
0x1e29,-1,
0x1e2b,-1,
0x1e2d,-1,
0x1e2f,-1,
0x1e31,-1,
0x1e33,-1,
0x1e35,-1,
0x1e37,-1,
0x1e39,-1,
0x1e3b,-1,
0x1e3d,-1,
0x1e3f,-1,
0x1e41,-1,
0x1e43,-1,
0x1e45,-1,
0x1e47,-1,
0x1e49,-1,
0x1e4b,-1,
0x1e4d,-1,
0x1e4f,-1,
0x1e51,-1,
0x1e53,-1,
0x1e55,-1,
0x1e57,-1,
0x1e59,-1,
0x1e5b,-1,
0x1e5d,-1,
0x1e5f,-1,
0x1e61,-1,
0x1e63,-1,
0x1e65,-1,
0x1e67,-1,
0x1e69,-1,
0x1e6b,-1,
0x1e6d,-1,
0x1e6f,-1,
0x1e71,-1,
0x1e73,-1,
0x1e75,-1,
0x1e77,-1,
0x1e79,-1,
0x1e7b,-1,
0x1e7d,-1,
0x1e7f,-1,
0x1e81,-1,
0x1e83,-1,
0x1e85,-1,
0x1e87,-1,
0x1e89,-1,
0x1e8b,-1,
0x1e8d,-1,
0x1e8f,-1,
0x1e91,-1,
0x1e93,-1,
0x1e95,-1,
0x1e9b,-59,
0x1ea1,-1,
0x1ea3,-1,
0x1ea5,-1,
0x1ea7,-1,
0x1ea9,-1,
0x1eab,-1,
0x1ead,-1,
0x1eaf,-1,
0x1eb1,-1,
0x1eb3,-1,
0x1eb5,-1,
0x1eb7,-1,
0x1eb9,-1,
0x1ebb,-1,
0x1ebd,-1,
0x1ebf,-1,
0x1ec1,-1,
0x1ec3,-1,
0x1ec5,-1,
0x1ec7,-1,
0x1ec9,-1,
0x1ecb,-1,
0x1ecd,-1,
0x1ecf,-1,
0x1ed1,-1,
0x1ed3,-1,
0x1ed5,-1,
0x1ed7,-1,
0x1ed9,-1,
0x1edb,-1,
0x1edd,-1,
0x1edf,-1,
0x1ee1,-1,
0x1ee3,-1,
0x1ee5,-1,
0x1ee7,-1,
0x1ee9,-1,
0x1eeb,-1,
0x1eed,-1,
0x1eef,-1,
0x1ef1,-1,
0x1ef3,-1,
0x1ef5,-1,
0x1ef7,-1,
0x1ef9,-1,
0x1efb,-1,
0x1efd,-1,
0x1eff,-1,
0x1f51,8,
0x1f53,8,
0x1f55,8,
0x1f57,8,
0x1fb3,9,
0x1fbe,-7205,
0x1fc3,9,
0x1fe5,7,
0x1ff3,9,
0x214e,-28,
0x2184,-1,
0x2c61,-1,
0x2c65,-10795,
0x2c66,-10792,
0x2c68,-1,
0x2c6a,-1,
0x2c6c,-1,
0x2c73,-1,
0x2c76,-1,
0x2c81,-1,
0x2c83,-1,
0x2c85,-1,
0x2c87,-1,
0x2c89,-1,
0x2c8b,-1,
0x2c8d,-1,
0x2c8f,-1,
0x2c91,-1,
0x2c93,-1,
0x2c95,-1,
0x2c97,-1,
0x2c99,-1,
0x2c9b,-1,
0x2c9d,-1,
0x2c9f,-1,
0x2ca1,-1,
0x2ca3,-1,
0x2ca5,-1,
0x2ca7,-1,
0x2ca9,-1,
0x2cab,-1,
0x2cad,-1,
0x2caf,-1,
0x2cb1,-1,
0x2cb3,-1,
0x2cb5,-1,
0x2cb7,-1,
0x2cb9,-1,
0x2cbb,-1,
0x2cbd,-1,
0x2cbf,-1,
0x2cc1,-1,
0x2cc3,-1,
0x2cc5,-1,
0x2cc7,-1,
0x2cc9,-1,
0x2ccb,-1,
0x2ccd,-1,
0x2ccf,-1,
0x2cd1,-1,
0x2cd3,-1,
0x2cd5,-1,
0x2cd7,-1,
0x2cd9,-1,
0x2cdb,-1,
0x2cdd,-1,
0x2cdf,-1,
0x2ce1,-1,
0x2ce3,-1,
0x2cec,-1,
0x2cee,-1,
0x2cf3,-1,
0x2d27,-7264,
0x2d2d,-7264,
0xa641,-1,
0xa643,-1,
0xa645,-1,
0xa647,-1,
0xa649,-1,
0xa64b,-1,
0xa64d,-1,
0xa64f,-1,
0xa651,-1,
0xa653,-1,
0xa655,-1,
0xa657,-1,
0xa659,-1,
0xa65b,-1,
0xa65d,-1,
0xa65f,-1,
0xa661,-1,
0xa663,-1,
0xa665,-1,
0xa667,-1,
0xa669,-1,
0xa66b,-1,
0xa66d,-1,
0xa681,-1,
0xa683,-1,
0xa685,-1,
0xa687,-1,
0xa689,-1,
0xa68b,-1,
0xa68d,-1,
0xa68f,-1,
0xa691,-1,
0xa693,-1,
0xa695,-1,
0xa697,-1,
0xa699,-1,
0xa69b,-1,
0xa723,-1,
0xa725,-1,
0xa727,-1,
0xa729,-1,
0xa72b,-1,
0xa72d,-1,
0xa72f,-1,
0xa733,-1,
0xa735,-1,
0xa737,-1,
0xa739,-1,
0xa73b,-1,
0xa73d,-1,
0xa73f,-1,
0xa741,-1,
0xa743,-1,
0xa745,-1,
0xa747,-1,
0xa749,-1,
0xa74b,-1,
0xa74d,-1,
0xa74f,-1,
0xa751,-1,
0xa753,-1,
0xa755,-1,
0xa757,-1,
0xa759,-1,
0xa75b,-1,
0xa75d,-1,
0xa75f,-1,
0xa761,-1,
0xa763,-1,
0xa765,-1,
0xa767,-1,
0xa769,-1,
0xa76b,-1,
0xa76d,-1,
0xa76f,-1,
0xa77a,-1,
0xa77c,-1,
0xa77f,-1,
0xa781,-1,
0xa783,-1,
0xa785,-1,
0xa787,-1,
0xa78c,-1,
0xa791,-1,
0xa793,-1,
0xa794,48,
0xa797,-1,
0xa799,-1,
0xa79b,-1,
0xa79d,-1,
0xa79f,-1,
0xa7a1,-1,
0xa7a3,-1,
0xa7a5,-1,
0xa7a7,-1,
0xa7a9,-1,
0xa7b5,-1,
0xa7b7,-1,
0xa7b9,-1,
0xa7bb,-1,
0xa7bd,-1,
0xa7bf,-1,
0xa7c1,-1,
0xa7c3,-1,
0xa7c8,-1,
0xa7ca,-1,
0xa7cd,-1,
0xa7d1,-1,
0xa7d7,-1,
0xa7d9,-1,
0xa7db,-1,
0xa7f6,-1,
0xab53,-928,
};
