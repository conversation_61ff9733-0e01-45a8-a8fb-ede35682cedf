/* This file was generated by scripts/glyphdump.py. Do not edit. */

static const char *single_name_list[] = {
"A",
"AE",
"AEacute",
"AEmacron",
"AEsmall",
"Aacute",
"Aacutesmall",
"Abreve",
"Abreveacute",
"Abrevecyrillic",
"Abrevedotbelow",
"Abrevegrave",
"Abrevehookabove",
"Abrevetilde",
"Acaron",
"Acircle",
"Acircumflex",
"Acircumflexacute",
"Acircumflexdotbelow",
"Acircumflexgrave",
"Acircumflexhookabove",
"Acircumflexsmall",
"Acircumflextilde",
"Acute",
"Acutesmall",
"Acyrillic",
"Adblgrave",
"Adieresis",
"Adieresiscyrillic",
"Adieresismacron",
"Adieresissmall",
"Adotbelow",
"Adotmacron",
"Agrave",
"Agravesmall",
"Ahookabove",
"Aiecyrillic",
"Ainvertedbreve",
"Alpha",
"Alphatonos",
"Amacron",
"Amonospace",
"Aogonek",
"Aring",
"Aringacute",
"Aringbelow",
"Aringsmall",
"Asmall",
"Atilde",
"Atildesmall",
"Aybarmenian",
"B",
"Bcircle",
"Bdotaccent",
"Bdotbelow",
"Becyrillic",
"Benarmenian",
"Beta",
"Bhook",
"Blinebelow",
"Bmonospace",
"Brevesmall",
"Bsmall",
"Btopbar",
"C",
"Caarmenian",
"Cacute",
"Caron",
"Caronsmall",
"Ccaron",
"Ccedilla",
"Ccedillaacute",
"Ccedillasmall",
"Ccircle",
"Ccircumflex",
"Cdot",
"Cdotaccent",
"Cedillasmall",
"Chaarmenian",
"Cheabkhasiancyrillic",
"Checyrillic",
"Chedescenderabkhasiancyrillic",
"Chedescendercyrillic",
"Chedieresiscyrillic",
"Cheharmenian",
"Chekhakassiancyrillic",
"Cheverticalstrokecyrillic",
"Chi",
"Chook",
"Circumflexsmall",
"Cmonospace",
"Coarmenian",
"Csmall",
"D",
"DZ",
"DZcaron",
"Daarmenian",
"Dafrican",
"Dbar",
"Dcaron",
"Dcedilla",
"Dcircle",
"Dcircumflexbelow",
"Dcroat",
"Ddotaccent",
"Ddotbelow",
"Decyrillic",
"Deicoptic",
"Delta",
"Deltagreek",
"Dhook",
"Dieresis",
"DieresisAcute",
"DieresisGrave",
"Dieresissmall",
"Digamma",
"Digammagreek",
"Djecyrillic",
"Dlinebelow",
"Dmonospace",
"Dotaccentsmall",
"Dslash",
"Dsmall",
"Dtopbar",
"Dz",
"Dzcaron",
"Dzeabkhasiancyrillic",
"Dzecyrillic",
"Dzhecyrillic",
"E",
"Eacute",
"Eacutesmall",
"Ebreve",
"Ecaron",
"Ecedillabreve",
"Echarmenian",
"Ecircle",
"Ecircumflex",
"Ecircumflexacute",
"Ecircumflexbelow",
"Ecircumflexdotbelow",
"Ecircumflexgrave",
"Ecircumflexhookabove",
"Ecircumflexsmall",
"Ecircumflextilde",
"Ecyrillic",
"Edblgrave",
"Edieresis",
"Edieresissmall",
"Edot",
"Edotaccent",
"Edotbelow",
"Efcyrillic",
"Egrave",
"Egravesmall",
"Eharmenian",
"Ehookabove",
"Eightroman",
"Einvertedbreve",
"Eiotifiedcyrillic",
"Elcyrillic",
"Elevenroman",
"Emacron",
"Emacronacute",
"Emacrongrave",
"Emcyrillic",
"Emonospace",
"Encyrillic",
"Endescendercyrillic",
"Eng",
"Enghecyrillic",
"Enhookcyrillic",
"Eogonek",
"Eopen",
"Epsilon",
"Epsilontonos",
"Ercyrillic",
"Ereversed",
"Ereversedcyrillic",
"Escyrillic",
"Esdescendercyrillic",
"Esh",
"Esmall",
"Eta",
"Etarmenian",
"Etatonos",
"Eth",
"Ethsmall",
"Etilde",
"Etildebelow",
"Euro",
"Ezh",
"Ezhcaron",
"Ezhreversed",
"F",
"Fcircle",
"Fdotaccent",
"Feharmenian",
"Feicoptic",
"Fhook",
"Finv",
"Fitacyrillic",
"Fiveroman",
"Fmonospace",
"Fourroman",
"Fsmall",
"G",
"GBsquare",
"Gacute",
"Gamma",
"Gammaafrican",
"Gangiacoptic",
"Gbreve",
"Gcaron",
"Gcedilla",
"Gcircle",
"Gcircumflex",
"Gcommaaccent",
"Gdot",
"Gdotaccent",
"Gecyrillic",
"Ghadarmenian",
"Ghemiddlehookcyrillic",
"Ghestrokecyrillic",
"Gheupturncyrillic",
"Ghook",
"Gimarmenian",
"Gjecyrillic",
"Gmacron",
"Gmir",
"Gmonospace",
"Grave",
"Gravesmall",
"Gsmall",
"Gsmallhook",
"Gstroke",
"H",
"H18533",
"H18543",
"H18551",
"H22073",
"HPsquare",
"Haabkhasiancyrillic",
"Hadescendercyrillic",
"Hardsigncyrillic",
"Hbar",
"Hbrevebelow",
"Hcedilla",
"Hcircle",
"Hcircumflex",
"Hdieresis",
"Hdotaccent",
"Hdotbelow",
"Hmonospace",
"Hoarmenian",
"Horicoptic",
"Hsmall",
"Hungarumlaut",
"Hungarumlautsmall",
"Hzsquare",
"I",
"IAcyrillic",
"IJ",
"IUcyrillic",
"Iacute",
"Iacutesmall",
"Ibreve",
"Icaron",
"Icircle",
"Icircumflex",
"Icircumflexsmall",
"Icyrillic",
"Idblgrave",
"Idieresis",
"Idieresisacute",
"Idieresiscyrillic",
"Idieresissmall",
"Idot",
"Idotaccent",
"Idotbelow",
"Iebrevecyrillic",
"Iecyrillic",
"Ifractur",
"Ifraktur",
"Igrave",
"Igravesmall",
"Ihookabove",
"Iicyrillic",
"Iinvertedbreve",
"Iishortcyrillic",
"Imacron",
"Imacroncyrillic",
"Imonospace",
"Iniarmenian",
"Iocyrillic",
"Iogonek",
"Iota",
"Iotaafrican",
"Iotadieresis",
"Iotatonos",
"Ismall",
"Istroke",
"Itilde",
"Itildebelow",
"Izhitsacyrillic",
"Izhitsadblgravecyrillic",
"J",
"Jaarmenian",
"Jcircle",
"Jcircumflex",
"Jecyrillic",
"Jheharmenian",
"Jmonospace",
"Jsmall",
"K",
"KBsquare",
"KKsquare",
"Kabashkircyrillic",
"Kacute",
"Kacyrillic",
"Kadescendercyrillic",
"Kahookcyrillic",
"Kappa",
"Kastrokecyrillic",
"Kaverticalstrokecyrillic",
"Kcaron",
"Kcedilla",
"Kcircle",
"Kcommaaccent",
"Kdotbelow",
"Keharmenian",
"Kenarmenian",
"Khacyrillic",
"Kheicoptic",
"Khook",
"Kjecyrillic",
"Klinebelow",
"Kmonospace",
"Koppacyrillic",
"Koppagreek",
"Ksicyrillic",
"Ksmall",
"L",
"LJ",
"LL",
"Lacute",
"Lambda",
"Lcaron",
"Lcedilla",
"Lcircle",
"Lcircumflexbelow",
"Lcommaaccent",
"Ldot",
"Ldotaccent",
"Ldotbelow",
"Ldotbelowmacron",
"Liwnarmenian",
"Lj",
"Ljecyrillic",
"Llinebelow",
"Lmonospace",
"Lslash",
"Lslashsmall",
"Lsmall",
"M",
"MBsquare",
"Macron",
"Macronsmall",
"Macute",
"Mcircle",
"Mdotaccent",
"Mdotbelow",
"Menarmenian",
"Mmonospace",
"Msmall",
"Mturned",
"Mu",
"N",
"NJ",
"Nacute",
"Ncaron",
"Ncedilla",
"Ncircle",
"Ncircumflexbelow",
"Ncommaaccent",
"Ndotaccent",
"Ndotbelow",
"Ng",
"Nhookleft",
"Nineroman",
"Nj",
"Njecyrillic",
"Nlinebelow",
"Nmonospace",
"Nowarmenian",
"Nsmall",
"Ntilde",
"Ntildesmall",
"Nu",
"O",
"OE",
"OEsmall",
"Oacute",
"Oacutesmall",
"Obarredcyrillic",
"Obarreddieresiscyrillic",
"Obreve",
"Ocaron",
"Ocenteredtilde",
"Ocircle",
"Ocircumflex",
"Ocircumflexacute",
"Ocircumflexdotbelow",
"Ocircumflexgrave",
"Ocircumflexhookabove",
"Ocircumflexsmall",
"Ocircumflextilde",
"Ocyrillic",
"Odblacute",
"Odblgrave",
"Odieresis",
"Odieresiscyrillic",
"Odieresissmall",
"Odotbelow",
"Ogoneksmall",
"Ograve",
"Ogravesmall",
"Oharmenian",
"Ohm",
"Ohookabove",
"Ohorn",
"Ohornacute",
"Ohorndotbelow",
"Ohorngrave",
"Ohornhookabove",
"Ohorntilde",
"Ohungarumlaut",
"Oi",
"Oinvertedbreve",
"Omacron",
"Omacronacute",
"Omacrongrave",
"Omega",
"Omegacyrillic",
"Omegagreek",
"Omegainv",
"Omegaroundcyrillic",
"Omegatitlocyrillic",
"Omegatonos",
"Omicron",
"Omicrontonos",
"Omonospace",
"Oneroman",
"Oogonek",
"Oogonekmacron",
"Oopen",
"Oslash",
"Oslashacute",
"Oslashsmall",
"Osmall",
"Ostrokeacute",
"Otcyrillic",
"Otilde",
"Otildeacute",
"Otildedieresis",
"Otildesmall",
"P",
"Pacute",
"Pcircle",
"Pdotaccent",
"Pecyrillic",
"Peharmenian",
"Pemiddlehookcyrillic",
"Phi",
"Phook",
"Pi",
"Piwrarmenian",
"Pmonospace",
"Psi",
"Psicyrillic",
"Psmall",
"Q",
"Qcircle",
"Qmonospace",
"Qsmall",
"R",
"Raarmenian",
"Racute",
"Rcaron",
"Rcedilla",
"Rcircle",
"Rcommaaccent",
"Rdblgrave",
"Rdotaccent",
"Rdotbelow",
"Rdotbelowmacron",
"Reharmenian",
"Rfractur",
"Rfraktur",
"Rho",
"Ringsmall",
"Rinvertedbreve",
"Rlinebelow",
"Rmonospace",
"Rsmall",
"Rsmallinverted",
"Rsmallinvertedsuperior",
"S",
"SF010000",
"SF020000",
"SF030000",
"SF040000",
"SF050000",
"SF060000",
"SF070000",
"SF080000",
"SF090000",
"SF100000",
"SF110000",
"SF190000",
"SF200000",
"SF210000",
"SF220000",
"SF230000",
"SF240000",
"SF250000",
"SF260000",
"SF270000",
"SF280000",
"SF360000",
"SF370000",
"SF380000",
"SF390000",
"SF400000",
"SF410000",
"SF420000",
"SF430000",
"SF440000",
"SF450000",
"SF460000",
"SF470000",
"SF480000",
"SF490000",
"SF500000",
"SF510000",
"SF520000",
"SF530000",
"SF540000",
"Sacute",
"Sacutedotaccent",
"Sampigreek",
"Scaron",
"Scarondotaccent",
"Scaronsmall",
"Scedilla",
"Schwa",
"Schwacyrillic",
"Schwadieresiscyrillic",
"Scircle",
"Scircumflex",
"Scommaaccent",
"Sdotaccent",
"Sdotbelow",
"Sdotbelowdotaccent",
"Seharmenian",
"Sevenroman",
"Shaarmenian",
"Shacyrillic",
"Shchacyrillic",
"Sheicoptic",
"Shhacyrillic",
"Shimacoptic",
"Sigma",
"Sixroman",
"Smonospace",
"Softsigncyrillic",
"Ssmall",
"Stigmagreek",
"T",
"Tau",
"Tbar",
"Tcaron",
"Tcedilla",
"Tcircle",
"Tcircumflexbelow",
"Tcommaaccent",
"Tdotaccent",
"Tdotbelow",
"Tecyrillic",
"Tedescendercyrillic",
"Tenroman",
"Tetsecyrillic",
"Theta",
"Thook",
"Thorn",
"Thornsmall",
"Threeroman",
"Tildesmall",
"Tiwnarmenian",
"Tlinebelow",
"Tmonospace",
"Toarmenian",
"Tonefive",
"Tonesix",
"Tonetwo",
"Tretroflexhook",
"Tsecyrillic",
"Tshecyrillic",
"Tsmall",
"Twelveroman",
"Tworoman",
"U",
"Uacute",
"Uacutesmall",
"Ubreve",
"Ucaron",
"Ucircle",
"Ucircumflex",
"Ucircumflexbelow",
"Ucircumflexsmall",
"Ucyrillic",
"Udblacute",
"Udblgrave",
"Udieresis",
"Udieresisacute",
"Udieresisbelow",
"Udieresiscaron",
"Udieresiscyrillic",
"Udieresisgrave",
"Udieresismacron",
"Udieresissmall",
"Udotbelow",
"Ugrave",
"Ugravesmall",
"Uhookabove",
"Uhorn",
"Uhornacute",
"Uhorndotbelow",
"Uhorngrave",
"Uhornhookabove",
"Uhorntilde",
"Uhungarumlaut",
"Uhungarumlautcyrillic",
"Uinvertedbreve",
"Ukcyrillic",
"Umacron",
"Umacroncyrillic",
"Umacrondieresis",
"Umonospace",
"Uogonek",
"Upsilon",
"Upsilon1",
"Upsilonacutehooksymbolgreek",
"Upsilonafrican",
"Upsilondieresis",
"Upsilondieresishooksymbolgreek",
"Upsilonhooksymbol",
"Upsilontonos",
"Uring",
"Ushortcyrillic",
"Usmall",
"Ustraightcyrillic",
"Ustraightstrokecyrillic",
"Utilde",
"Utildeacute",
"Utildebelow",
"V",
"Vcircle",
"Vdotbelow",
"Vecyrillic",
"Vewarmenian",
"Vhook",
"Vmonospace",
"Voarmenian",
"Vsmall",
"Vtilde",
"W",
"Wacute",
"Wcircle",
"Wcircumflex",
"Wdieresis",
"Wdotaccent",
"Wdotbelow",
"Wgrave",
"Wmonospace",
"Wsmall",
"X",
"Xcircle",
"Xdieresis",
"Xdotaccent",
"Xeharmenian",
"Xi",
"Xmonospace",
"Xsmall",
"Y",
"Yacute",
"Yacutesmall",
"Yatcyrillic",
"Ycircle",
"Ycircumflex",
"Ydieresis",
"Ydieresissmall",
"Ydotaccent",
"Ydotbelow",
"Yen",
"Yericyrillic",
"Yerudieresiscyrillic",
"Ygrave",
"Yhook",
"Yhookabove",
"Yiarmenian",
"Yicyrillic",
"Yiwnarmenian",
"Ymonospace",
"Ysmall",
"Ytilde",
"Yusbigcyrillic",
"Yusbigiotifiedcyrillic",
"Yuslittlecyrillic",
"Yuslittleiotifiedcyrillic",
"Z",
"Zaarmenian",
"Zacute",
"Zcaron",
"Zcaronsmall",
"Zcircle",
"Zcircumflex",
"Zdot",
"Zdotaccent",
"Zdotbelow",
"Zecyrillic",
"Zedescendercyrillic",
"Zedieresiscyrillic",
"Zeta",
"Zhearmenian",
"Zhebrevecyrillic",
"Zhecyrillic",
"Zhedescendercyrillic",
"Zhedieresiscyrillic",
"Zlinebelow",
"Zmonospace",
"Zsmall",
"Zstroke",
"a",
"aabengali",
"aacute",
"aadeva",
"aagujarati",
"aagurmukhi",
"aamatragurmukhi",
"aarusquare",
"aavowelsignbengali",
"aavowelsigndeva",
"aavowelsigngujarati",
"abbreviationmarkarmenian",
"abbreviationsigndeva",
"abengali",
"abopomofo",
"abreve",
"abreveacute",
"abrevecyrillic",
"abrevedotbelow",
"abrevegrave",
"abrevehookabove",
"abrevetilde",
"acaron",
"acircle",
"acircumflex",
"acircumflexacute",
"acircumflexdotbelow",
"acircumflexgrave",
"acircumflexhookabove",
"acircumflextilde",
"acute",
"acutebelowcmb",
"acutecmb",
"acutecomb",
"acutedeva",
"acutelowmod",
"acutetonecmb",
"acyrillic",
"adblgrave",
"addakgurmukhi",
"adeva",
"adieresis",
"adieresiscyrillic",
"adieresismacron",
"adotbelow",
"adotmacron",
"ae",
"aeacute",
"aekorean",
"aemacron",
"afii00208",
"afii08941",
"afii10017",
"afii10018",
"afii10019",
"afii10020",
"afii10021",
"afii10022",
"afii10023",
"afii10024",
"afii10025",
"afii10026",
"afii10027",
"afii10028",
"afii10029",
"afii10030",
"afii10031",
"afii10032",
"afii10033",
"afii10034",
"afii10035",
"afii10036",
"afii10037",
"afii10038",
"afii10039",
"afii10040",
"afii10041",
"afii10042",
"afii10043",
"afii10044",
"afii10045",
"afii10046",
"afii10047",
"afii10048",
"afii10049",
"afii10050",
"afii10051",
"afii10052",
"afii10053",
"afii10054",
"afii10055",
"afii10056",
"afii10057",
"afii10058",
"afii10059",
"afii10060",
"afii10061",
"afii10062",
"afii10063",
"afii10064",
"afii10065",
"afii10066",
"afii10067",
"afii10068",
"afii10069",
"afii10070",
"afii10071",
"afii10072",
"afii10073",
"afii10074",
"afii10075",
"afii10076",
"afii10077",
"afii10078",
"afii10079",
"afii10080",
"afii10081",
"afii10082",
"afii10083",
"afii10084",
"afii10085",
"afii10086",
"afii10087",
"afii10088",
"afii10089",
"afii10090",
"afii10091",
"afii10092",
"afii10093",
"afii10094",
"afii10095",
"afii10096",
"afii10097",
"afii10098",
"afii10099",
"afii10100",
"afii10101",
"afii10102",
"afii10103",
"afii10104",
"afii10105",
"afii10106",
"afii10107",
"afii10108",
"afii10109",
"afii10110",
"afii10145",
"afii10146",
"afii10147",
"afii10148",
"afii10192",
"afii10193",
"afii10194",
"afii10195",
"afii10196",
"afii10831",
"afii10832",
"afii10846",
"afii299",
"afii300",
"afii301",
"afii57381",
"afii57388",
"afii57392",
"afii57393",
"afii57394",
"afii57395",
"afii57396",
"afii57397",
"afii57398",
"afii57399",
"afii57400",
"afii57401",
"afii57403",
"afii57407",
"afii57409",
"afii57410",
"afii57411",
"afii57412",
"afii57413",
"afii57414",
"afii57415",
"afii57416",
"afii57417",
"afii57418",
"afii57419",
"afii57420",
"afii57421",
"afii57422",
"afii57423",
"afii57424",
"afii57425",
"afii57426",
"afii57427",
"afii57428",
"afii57429",
"afii57430",
"afii57431",
"afii57432",
"afii57433",
"afii57434",
"afii57440",
"afii57441",
"afii57442",
"afii57443",
"afii57444",
"afii57445",
"afii57446",
"afii57448",
"afii57449",
"afii57450",
"afii57451",
"afii57452",
"afii57453",
"afii57454",
"afii57455",
"afii57456",
"afii57457",
"afii57458",
"afii57470",
"afii57505",
"afii57506",
"afii57507",
"afii57508",
"afii57509",
"afii57511",
"afii57512",
"afii57513",
"afii57514",
"afii57519",
"afii57534",
"afii57636",
"afii57645",
"afii57658",
"afii57664",
"afii57665",
"afii57666",
"afii57667",
"afii57668",
"afii57669",
"afii57670",
"afii57671",
"afii57672",
"afii57673",
"afii57674",
"afii57675",
"afii57676",
"afii57677",
"afii57678",
"afii57679",
"afii57680",
"afii57681",
"afii57682",
"afii57683",
"afii57684",
"afii57685",
"afii57686",
"afii57687",
"afii57688",
"afii57689",
"afii57690",
"afii57694",
"afii57695",
"afii57700",
"afii57705",
"afii57716",
"afii57717",
"afii57718",
"afii57723",
"afii57793",
"afii57794",
"afii57795",
"afii57796",
"afii57797",
"afii57798",
"afii57799",
"afii57800",
"afii57801",
"afii57802",
"afii57803",
"afii57804",
"afii57806",
"afii57807",
"afii57839",
"afii57841",
"afii57842",
"afii57929",
"afii61248",
"afii61289",
"afii61352",
"afii61573",
"afii61574",
"afii61575",
"afii61664",
"afii63167",
"afii64937",
"agrave",
"agujarati",
"agurmukhi",
"ahiragana",
"ahookabove",
"aibengali",
"aibopomofo",
"aideva",
"aiecyrillic",
"aigujarati",
"aigurmukhi",
"aimatragurmukhi",
"ainarabic",
"ainfinalarabic",
"aininitialarabic",
"ainmedialarabic",
"ainvertedbreve",
"aivowelsignbengali",
"aivowelsigndeva",
"aivowelsigngujarati",
"akatakana",
"akatakanahalfwidth",
"akorean",
"alef",
"alefarabic",
"alefdageshhebrew",
"aleffinalarabic",
"alefhamzaabovearabic",
"alefhamzaabovefinalarabic",
"alefhamzabelowarabic",
"alefhamzabelowfinalarabic",
"alefhebrew",
"aleflamedhebrew",
"alefmaddaabovearabic",
"alefmaddaabovefinalarabic",
"alefmaksuraarabic",
"alefmaksurafinalarabic",
"alefmaksurainitialarabic",
"alefmaksuramedialarabic",
"alefpatahhebrew",
"alefqamatshebrew",
"aleph",
"allequal",
"alpha",
"alphatonos",
"altselector",
"amacron",
"amonospace",
"ampersand",
"ampersandmonospace",
"ampersandsmall",
"amsquare",
"anbopomofo",
"angbopomofo",
"angbracketleft",
"angbracketright",
"angkhankhuthai",
"angle",
"anglebracketleft",
"anglebracketleftvertical",
"anglebracketright",
"anglebracketrightvertical",
"angleleft",
"angleright",
"angstrom",
"anoteleia",
"anticlockwise",
"anudattadeva",
"anusvarabengali",
"anusvaradeva",
"anusvaragujarati",
"aogonek",
"apaatosquare",
"aparen",
"apostrophearmenian",
"apostrophemod",
"apple",
"approaches",
"approxequal",
"approxequalorimage",
"approximatelyequal",
"approxorequal",
"araeaekorean",
"araeakorean",
"arc",
"archleftdown",
"archrightdown",
"arighthalfring",
"aring",
"aringacute",
"aringbelow",
"arrowboth",
"arrowbothv",
"arrowdashdown",
"arrowdashleft",
"arrowdashright",
"arrowdashup",
"arrowdblboth",
"arrowdblbothv",
"arrowdbldown",
"arrowdblleft",
"arrowdblright",
"arrowdblup",
"arrowdown",
"arrowdownleft",
"arrowdownright",
"arrowdownwhite",
"arrowheaddownmod",
"arrowheadleftmod",
"arrowheadrightmod",
"arrowheadupmod",
"arrowhorizex",
"arrowleft",
"arrowleftbothalf",
"arrowleftdbl",
"arrowleftdblstroke",
"arrowleftoverright",
"arrowlefttophalf",
"arrowleftwhite",
"arrownortheast",
"arrownorthwest",
"arrowparrleftright",
"arrowparrrightleft",
"arrowright",
"arrowrightbothalf",
"arrowrightdblstroke",
"arrowrightheavy",
"arrowrightoverleft",
"arrowrighttophalf",
"arrowrightwhite",
"arrowsoutheast",
"arrowsouthwest",
"arrowtableft",
"arrowtabright",
"arrowtailleft",
"arrowtailright",
"arrowtripleleft",
"arrowtripleright",
"arrowup",
"arrowupdn",
"arrowupdnbse",
"arrowupdownbase",
"arrowupleft",
"arrowupleftofdown",
"arrowupright",
"arrowupwhite",
"arrowvertex",
"ascendercompwordmark",
"asciicircum",
"asciicircummonospace",
"asciitilde",
"asciitildemonospace",
"ascript",
"ascriptturned",
"asmallhiragana",
"asmallkatakana",
"asmallkatakanahalfwidth",
"asterisk",
"asteriskaltonearabic",
"asteriskarabic",
"asteriskcentered",
"asteriskmath",
"asteriskmonospace",
"asterisksmall",
"asterism",
"asuperior",
"asymptoticallyequal",
"at",
"atilde",
"atmonospace",
"atsmall",
"aturned",
"aubengali",
"aubopomofo",
"audeva",
"augujarati",
"augurmukhi",
"aulengthmarkbengali",
"aumatragurmukhi",
"auvowelsignbengali",
"auvowelsigndeva",
"auvowelsigngujarati",
"avagrahadeva",
"aybarmenian",
"ayin",
"ayinaltonehebrew",
"ayinhebrew",
"b",
"babengali",
"backslash",
"backslashmonospace",
"badeva",
"bagujarati",
"bagurmukhi",
"bahiragana",
"bahtthai",
"bakatakana",
"bar",
"bardbl",
"barmonospace",
"bbopomofo",
"bcircle",
"bdotaccent",
"bdotbelow",
"beamedsixteenthnotes",
"because",
"becyrillic",
"beharabic",
"behfinalarabic",
"behinitialarabic",
"behiragana",
"behmedialarabic",
"behmeeminitialarabic",
"behmeemisolatedarabic",
"behnoonfinalarabic",
"bekatakana",
"benarmenian",
"bet",
"beta",
"betasymbolgreek",
"betdagesh",
"betdageshhebrew",
"beth",
"bethebrew",
"betrafehebrew",
"between",
"bhabengali",
"bhadeva",
"bhagujarati",
"bhagurmukhi",
"bhook",
"bihiragana",
"bikatakana",
"bilabialclick",
"bindigurmukhi",
"birusquare",
"blackcircle",
"blackdiamond",
"blackdownpointingtriangle",
"blackleftpointingpointer",
"blackleftpointingtriangle",
"blacklenticularbracketleft",
"blacklenticularbracketleftvertical",
"blacklenticularbracketright",
"blacklenticularbracketrightvertical",
"blacklowerlefttriangle",
"blacklowerrighttriangle",
"blackrectangle",
"blackrightpointingpointer",
"blackrightpointingtriangle",
"blacksmallsquare",
"blacksmilingface",
"blacksquare",
"blackstar",
"blackupperlefttriangle",
"blackupperrighttriangle",
"blackuppointingsmalltriangle",
"blackuppointingtriangle",
"blank",
"blinebelow",
"block",
"bmonospace",
"bobaimaithai",
"bohiragana",
"bokatakana",
"bparen",
"bqsquare",
"braceex",
"braceleft",
"braceleftbt",
"braceleftmid",
"braceleftmonospace",
"braceleftsmall",
"bracelefttp",
"braceleftvertical",
"braceright",
"bracerightbt",
"bracerightmid",
"bracerightmonospace",
"bracerightsmall",
"bracerighttp",
"bracerightvertical",
"bracketleft",
"bracketleftbt",
"bracketleftex",
"bracketleftmonospace",
"bracketlefttp",
"bracketright",
"bracketrightbt",
"bracketrightex",
"bracketrightmonospace",
"bracketrighttp",
"breve",
"brevebelowcmb",
"brevecmb",
"breveinvertedbelowcmb",
"breveinvertedcmb",
"breveinverteddoublecmb",
"bridgebelowcmb",
"bridgeinvertedbelowcmb",
"brokenbar",
"bstroke",
"bsuperior",
"btopbar",
"buhiragana",
"bukatakana",
"bullet",
"bulletinverse",
"bulletoperator",
"bullseye",
"c",
"caarmenian",
"cabengali",
"cacute",
"cadeva",
"cagujarati",
"cagurmukhi",
"calsquare",
"candrabindubengali",
"candrabinducmb",
"candrabindudeva",
"candrabindugujarati",
"capitalcompwordmark",
"capslock",
"careof",
"caron",
"caronbelowcmb",
"caroncmb",
"carriagereturn",
"cbopomofo",
"ccaron",
"ccedilla",
"ccedillaacute",
"ccircle",
"ccircumflex",
"ccurl",
"cdot",
"cdotaccent",
"cdsquare",
"cedilla",
"cedillacmb",
"ceilingleft",
"ceilingright",
"cent",
"centigrade",
"centinferior",
"centmonospace",
"centoldstyle",
"centsuperior",
"chaarmenian",
"chabengali",
"chadeva",
"chagujarati",
"chagurmukhi",
"chbopomofo",
"cheabkhasiancyrillic",
"check",
"checkmark",
"checyrillic",
"chedescenderabkhasiancyrillic",
"chedescendercyrillic",
"chedieresiscyrillic",
"cheharmenian",
"chekhakassiancyrillic",
"cheverticalstrokecyrillic",
"chi",
"chieuchacirclekorean",
"chieuchaparenkorean",
"chieuchcirclekorean",
"chieuchkorean",
"chieuchparenkorean",
"chochangthai",
"chochanthai",
"chochingthai",
"chochoethai",
"chook",
"cieucacirclekorean",
"cieucaparenkorean",
"cieuccirclekorean",
"cieuckorean",
"cieucparenkorean",
"cieucuparenkorean",
"circle",
"circleR",
"circleS",
"circleasterisk",
"circlecopyrt",
"circledivide",
"circledot",
"circleequal",
"circleminus",
"circlemultiply",
"circleot",
"circleplus",
"circlepostalmark",
"circlering",
"circlewithlefthalfblack",
"circlewithrighthalfblack",
"circumflex",
"circumflexbelowcmb",
"circumflexcmb",
"clear",
"clickalveolar",
"clickdental",
"clicklateral",
"clickretroflex",
"clockwise",
"club",
"clubsuitblack",
"clubsuitwhite",
"cmcubedsquare",
"cmonospace",
"cmsquaredsquare",
"coarmenian",
"colon",
"colonmonetary",
"colonmonospace",
"colonsign",
"colonsmall",
"colontriangularhalfmod",
"colontriangularmod",
"comma",
"commaabovecmb",
"commaaboverightcmb",
"commaaccent",
"commaarabic",
"commaarmenian",
"commainferior",
"commamonospace",
"commareversedabovecmb",
"commareversedmod",
"commasmall",
"commasuperior",
"commaturnedabovecmb",
"commaturnedmod",
"compass",
"complement",
"compwordmark",
"congruent",
"contourintegral",
"control",
"controlACK",
"controlBEL",
"controlBS",
"controlCAN",
"controlCR",
"controlDC1",
"controlDC2",
"controlDC3",
"controlDC4",
"controlDEL",
"controlDLE",
"controlEM",
"controlENQ",
"controlEOT",
"controlESC",
"controlETB",
"controlETX",
"controlFF",
"controlFS",
"controlGS",
"controlHT",
"controlLF",
"controlNAK",
"controlRS",
"controlSI",
"controlSO",
"controlSOT",
"controlSTX",
"controlSUB",
"controlSYN",
"controlUS",
"controlVT",
"coproduct",
"copyright",
"copyrightsans",
"copyrightserif",
"cornerbracketleft",
"cornerbracketlefthalfwidth",
"cornerbracketleftvertical",
"cornerbracketright",
"cornerbracketrighthalfwidth",
"cornerbracketrightvertical",
"corporationsquare",
"cosquare",
"coverkgsquare",
"cparen",
"cruzeiro",
"cstretched",
"curlyand",
"curlyleft",
"curlyor",
"curlyright",
"currency",
"cwm",
"cyrBreve",
"cyrFlex",
"cyrbreve",
"cyrflex",
"d",
"daarmenian",
"dabengali",
"dadarabic",
"dadeva",
"dadfinalarabic",
"dadinitialarabic",
"dadmedialarabic",
"dagesh",
"dageshhebrew",
"dagger",
"daggerdbl",
"dagujarati",
"dagurmukhi",
"dahiragana",
"dakatakana",
"dalarabic",
"dalet",
"daletdagesh",
"daletdageshhebrew",
"daleth",
"dalethebrew",
"dalfinalarabic",
"dammaarabic",
"dammalowarabic",
"dammatanaltonearabic",
"dammatanarabic",
"danda",
"dargahebrew",
"dargalefthebrew",
"dasiapneumatacyrilliccmb",
"dbar",
"dblGrave",
"dblanglebracketleft",
"dblanglebracketleftvertical",
"dblanglebracketright",
"dblanglebracketrightvertical",
"dblarchinvertedbelowcmb",
"dblarrowdwn",
"dblarrowheadleft",
"dblarrowheadright",
"dblarrowleft",
"dblarrowright",
"dblarrowup",
"dblbracketleft",
"dblbracketright",
"dbldanda",
"dblgrave",
"dblgravecmb",
"dblintegral",
"dbllowline",
"dbllowlinecmb",
"dbloverlinecmb",
"dblprimemod",
"dblverticalbar",
"dblverticallineabovecmb",
"dbopomofo",
"dbsquare",
"dcaron",
"dcedilla",
"dcircle",
"dcircumflexbelow",
"dcroat",
"ddabengali",
"ddadeva",
"ddagujarati",
"ddagurmukhi",
"ddalarabic",
"ddalfinalarabic",
"dddhadeva",
"ddhabengali",
"ddhadeva",
"ddhagujarati",
"ddhagurmukhi",
"ddotaccent",
"ddotbelow",
"decimalseparatorarabic",
"decimalseparatorpersian",
"decyrillic",
"defines",
"degree",
"dehihebrew",
"dehiragana",
"deicoptic",
"dekatakana",
"deleteleft",
"deleteright",
"delta",
"deltaturned",
"denominatorminusonenumeratorbengali",
"dezh",
"dhabengali",
"dhadeva",
"dhagujarati",
"dhagurmukhi",
"dhook",
"dialytikatonos",
"dialytikatonoscmb",
"diamond",
"diamondmath",
"diamondsolid",
"diamondsuitwhite",
"dieresis",
"dieresisacute",
"dieresisbelowcmb",
"dieresiscmb",
"dieresisgrave",
"dieresistonos",
"difference",
"dihiragana",
"dikatakana",
"dittomark",
"divide",
"dividemultiply",
"divides",
"divisionslash",
"djecyrillic",
"dkshade",
"dlinebelow",
"dlsquare",
"dmacron",
"dmonospace",
"dnblock",
"dochadathai",
"dodekthai",
"dohiragana",
"dokatakana",
"dollar",
"dollarinferior",
"dollarmonospace",
"dollaroldstyle",
"dollarsmall",
"dollarsuperior",
"dong",
"dorusquare",
"dotaccent",
"dotaccentcmb",
"dotbelowcmb",
"dotbelowcomb",
"dotkatakana",
"dotlessi",
"dotlessj",
"dotlessjstrokehook",
"dotmath",
"dotplus",
"dottedcircle",
"doubleyodpatah",
"doubleyodpatahhebrew",
"downfall",
"downslope",
"downtackbelowcmb",
"downtackmod",
"dparen",
"dsuperior",
"dtail",
"dtopbar",
"duhiragana",
"dukatakana",
"dz",
"dzaltone",
"dzcaron",
"dzcurl",
"dzeabkhasiancyrillic",
"dzecyrillic",
"dzhecyrillic",
"e",
"eacute",
"earth",
"ebengali",
"ebopomofo",
"ebreve",
"ecandradeva",
"ecandragujarati",
"ecandravowelsigndeva",
"ecandravowelsigngujarati",
"ecaron",
"ecedillabreve",
"echarmenian",
"echyiwnarmenian",
"ecircle",
"ecircumflex",
"ecircumflexacute",
"ecircumflexbelow",
"ecircumflexdotbelow",
"ecircumflexgrave",
"ecircumflexhookabove",
"ecircumflextilde",
"ecyrillic",
"edblgrave",
"edeva",
"edieresis",
"edot",
"edotaccent",
"edotbelow",
"eegurmukhi",
"eematragurmukhi",
"efcyrillic",
"egrave",
"egujarati",
"eharmenian",
"ehbopomofo",
"ehiragana",
"ehookabove",
"eibopomofo",
"eight",
"eightarabic",
"eightbengali",
"eightcircle",
"eightcircleinversesansserif",
"eightdeva",
"eighteencircle",
"eighteenparen",
"eighteenperiod",
"eightgujarati",
"eightgurmukhi",
"eighthackarabic",
"eighthangzhou",
"eighthnotebeamed",
"eightideographicparen",
"eightinferior",
"eightmonospace",
"eightoldstyle",
"eightparen",
"eightperiod",
"eightpersian",
"eightroman",
"eightsuperior",
"eightthai",
"einvertedbreve",
"eiotifiedcyrillic",
"ekatakana",
"ekatakanahalfwidth",
"ekonkargurmukhi",
"ekorean",
"elcyrillic",
"element",
"elevencircle",
"elevenparen",
"elevenperiod",
"elevenroman",
"ellipsis",
"ellipsisvertical",
"emacron",
"emacronacute",
"emacrongrave",
"emcyrillic",
"emdash",
"emdashvertical",
"emonospace",
"emphasismarkarmenian",
"emptyset",
"emptyslot",
"enbopomofo",
"encyrillic",
"endash",
"endashvertical",
"endescendercyrillic",
"eng",
"engbopomofo",
"enghecyrillic",
"enhookcyrillic",
"enspace",
"eogonek",
"eokorean",
"eopen",
"eopenclosed",
"eopenreversed",
"eopenreversedclosed",
"eopenreversedhook",
"eparen",
"epsilon",
"epsilon1",
"epsiloninv",
"epsilontonos",
"equal",
"equaldotleftright",
"equaldotrightleft",
"equalmonospace",
"equalorfollows",
"equalorgreater",
"equalorless",
"equalorprecedes",
"equalorsimilar",
"equalsdots",
"equalsmall",
"equalsuperior",
"equivalence",
"equivasymptotic",
"erbopomofo",
"ercyrillic",
"ereversed",
"ereversedcyrillic",
"escyrillic",
"esdescendercyrillic",
"esh",
"eshcurl",
"eshortdeva",
"eshortvowelsigndeva",
"eshreversedloop",
"eshsquatreversed",
"esmallhiragana",
"esmallkatakana",
"esmallkatakanahalfwidth",
"estimated",
"esuperior",
"eta",
"etarmenian",
"etatonos",
"eth",
"etilde",
"etildebelow",
"etnahtafoukhhebrew",
"etnahtafoukhlefthebrew",
"etnahtahebrew",
"etnahtalefthebrew",
"eturned",
"eukorean",
"euro",
"evowelsignbengali",
"evowelsigndeva",
"evowelsigngujarati",
"exclam",
"exclamarmenian",
"exclamdbl",
"exclamdown",
"exclamdownsmall",
"exclammonospace",
"exclamsmall",
"existential",
"ezh",
"ezhcaron",
"ezhcurl",
"ezhreversed",
"ezhtail",
"f",
"fadeva",
"fagurmukhi",
"fahrenheit",
"fathaarabic",
"fathalowarabic",
"fathatanarabic",
"fbopomofo",
"fcircle",
"fdotaccent",
"feharabic",
"feharmenian",
"fehfinalarabic",
"fehinitialarabic",
"fehmedialarabic",
"feicoptic",
"female",
"ff",
"ffi",
"ffl",
"fi",
"fifteencircle",
"fifteenparen",
"fifteenperiod",
"figuredash",
"filledbox",
"filledrect",
"finalkaf",
"finalkafdagesh",
"finalkafdageshhebrew",
"finalkafhebrew",
"finalmem",
"finalmemhebrew",
"finalnun",
"finalnunhebrew",
"finalpe",
"finalpehebrew",
"finaltsadi",
"finaltsadihebrew",
"firsttonechinese",
"fisheye",
"fitacyrillic",
"five",
"fivearabic",
"fivebengali",
"fivecircle",
"fivecircleinversesansserif",
"fivedeva",
"fiveeighths",
"fivegujarati",
"fivegurmukhi",
"fivehackarabic",
"fivehangzhou",
"fiveideographicparen",
"fiveinferior",
"fivemonospace",
"fiveoldstyle",
"fiveparen",
"fiveperiod",
"fivepersian",
"fiveroman",
"fivesuperior",
"fivethai",
"fl",
"flat",
"floorleft",
"floorright",
"florin",
"fmonospace",
"fmsquare",
"fofanthai",
"fofathai",
"follownotdbleqv",
"follownotslnteql",
"followornoteqvlnt",
"follows",
"followsequal",
"followsorcurly",
"followsorequal",
"fongmanthai",
"forall",
"forces",
"forcesbar",
"fork",
"four",
"fourarabic",
"fourbengali",
"fourcircle",
"fourcircleinversesansserif",
"fourdeva",
"fourgujarati",
"fourgurmukhi",
"fourhackarabic",
"fourhangzhou",
"fourideographicparen",
"fourinferior",
"fourmonospace",
"fournumeratorbengali",
"fouroldstyle",
"fourparen",
"fourperiod",
"fourpersian",
"fourroman",
"foursuperior",
"fourteencircle",
"fourteenparen",
"fourteenperiod",
"fourthai",
"fourthtonechinese",
"fparen",
"fraction",
"franc",
"frown",
"g",
"gabengali",
"gacute",
"gadeva",
"gafarabic",
"gaffinalarabic",
"gafinitialarabic",
"gafmedialarabic",
"gagujarati",
"gagurmukhi",
"gahiragana",
"gakatakana",
"gamma",
"gammalatinsmall",
"gammasuperior",
"gangiacoptic",
"gbopomofo",
"gbreve",
"gcaron",
"gcedilla",
"gcircle",
"gcircumflex",
"gcommaaccent",
"gdot",
"gdotaccent",
"gecyrillic",
"gehiragana",
"gekatakana",
"geomequivalent",
"geometricallyequal",
"gereshaccenthebrew",
"gereshhebrew",
"gereshmuqdamhebrew",
"germandbls",
"gershayimaccenthebrew",
"gershayimhebrew",
"getamark",
"ghabengali",
"ghadarmenian",
"ghadeva",
"ghagujarati",
"ghagurmukhi",
"ghainarabic",
"ghainfinalarabic",
"ghaininitialarabic",
"ghainmedialarabic",
"ghemiddlehookcyrillic",
"ghestrokecyrillic",
"gheupturncyrillic",
"ghhadeva",
"ghhagurmukhi",
"ghook",
"ghzsquare",
"gihiragana",
"gikatakana",
"gimarmenian",
"gimel",
"gimeldagesh",
"gimeldageshhebrew",
"gimelhebrew",
"gjecyrillic",
"glottalinvertedstroke",
"glottalstop",
"glottalstopinverted",
"glottalstopmod",
"glottalstopreversed",
"glottalstopreversedmod",
"glottalstopreversedsuperior",
"glottalstopstroke",
"glottalstopstrokereversed",
"gmacron",
"gmonospace",
"gohiragana",
"gokatakana",
"gparen",
"gpasquare",
"gradient",
"grave",
"gravebelowcmb",
"gravecmb",
"gravecomb",
"gravedeva",
"gravelowmod",
"gravemonospace",
"gravetonecmb",
"greater",
"greaterdbleqlless",
"greaterdblequal",
"greaterdot",
"greaterequal",
"greaterequalorless",
"greaterlessequal",
"greatermonospace",
"greatermuch",
"greaternotdblequal",
"greaternotequal",
"greaterorapproxeql",
"greaterorequalslant",
"greaterorequivalent",
"greaterorless",
"greaterornotdbleql",
"greaterornotequal",
"greaterorsimilar",
"greateroverequal",
"greatersmall",
"gscript",
"gstroke",
"guhiragana",
"guillemotleft",
"guillemotright",
"guilsinglleft",
"guilsinglright",
"gukatakana",
"guramusquare",
"gysquare",
"h",
"haabkhasiancyrillic",
"haaltonearabic",
"habengali",
"hadescendercyrillic",
"hadeva",
"hagujarati",
"hagurmukhi",
"haharabic",
"hahfinalarabic",
"hahinitialarabic",
"hahiragana",
"hahmedialarabic",
"haitusquare",
"hakatakana",
"hakatakanahalfwidth",
"halantgurmukhi",
"hamzaarabic",
"hamzalowarabic",
"hangulfiller",
"hardsigncyrillic",
"harpoondownleft",
"harpoondownright",
"harpoonleftbarbup",
"harpoonleftright",
"harpoonrightbarbup",
"harpoonrightleft",
"harpoonupleft",
"harpoonupright",
"hasquare",
"hatafpatah",
"hatafpatah16",
"hatafpatah23",
"hatafpatah2f",
"hatafpatahhebrew",
"hatafpatahnarrowhebrew",
"hatafpatahquarterhebrew",
"hatafpatahwidehebrew",
"hatafqamats",
"hatafqamats1b",
"hatafqamats28",
"hatafqamats34",
"hatafqamatshebrew",
"hatafqamatsnarrowhebrew",
"hatafqamatsquarterhebrew",
"hatafqamatswidehebrew",
"hatafsegol",
"hatafsegol17",
"hatafsegol24",
"hatafsegol30",
"hatafsegolhebrew",
"hatafsegolnarrowhebrew",
"hatafsegolquarterhebrew",
"hatafsegolwidehebrew",
"hbar",
"hbopomofo",
"hbrevebelow",
"hcedilla",
"hcircle",
"hcircumflex",
"hdieresis",
"hdotaccent",
"hdotbelow",
"he",
"heart",
"heartsuitblack",
"heartsuitwhite",
"hedagesh",
"hedageshhebrew",
"hehaltonearabic",
"heharabic",
"hehebrew",
"hehfinalaltonearabic",
"hehfinalalttwoarabic",
"hehfinalarabic",
"hehhamzaabovefinalarabic",
"hehhamzaaboveisolatedarabic",
"hehinitialaltonearabic",
"hehinitialarabic",
"hehiragana",
"hehmedialaltonearabic",
"hehmedialarabic",
"heiseierasquare",
"hekatakana",
"hekatakanahalfwidth",
"hekutaarusquare",
"henghook",
"herutusquare",
"het",
"hethebrew",
"hhook",
"hhooksuperior",
"hieuhacirclekorean",
"hieuhaparenkorean",
"hieuhcirclekorean",
"hieuhkorean",
"hieuhparenkorean",
"hihiragana",
"hikatakana",
"hikatakanahalfwidth",
"hiriq",
"hiriq14",
"hiriq21",
"hiriq2d",
"hiriqhebrew",
"hiriqnarrowhebrew",
"hiriqquarterhebrew",
"hiriqwidehebrew",
"hlinebelow",
"hmonospace",
"hoarmenian",
"hohipthai",
"hohiragana",
"hokatakana",
"hokatakanahalfwidth",
"holam",
"holam19",
"holam26",
"holam32",
"holamhebrew",
"holamnarrowhebrew",
"holamquarterhebrew",
"holamwidehebrew",
"honokhukthai",
"hookabovecomb",
"hookcmb",
"hookpalatalizedbelowcmb",
"hookretroflexbelowcmb",
"hoonsquare",
"horicoptic",
"horizontalbar",
"horncmb",
"hotsprings",
"house",
"hparen",
"hsuperior",
"hturned",
"huhiragana",
"huiitosquare",
"hukatakana",
"hukatakanahalfwidth",
"hungarumlaut",
"hungarumlautcmb",
"hv",
"hyphen",
"hyphenchar",
"hypheninferior",
"hyphenmonospace",
"hyphensmall",
"hyphensuperior",
"hyphentwo",
"i",
"iacute",
"iacyrillic",
"ibengali",
"ibopomofo",
"ibreve",
"icaron",
"icircle",
"icircumflex",
"icyrillic",
"idblgrave",
"ideographearthcircle",
"ideographfirecircle",
"ideographicallianceparen",
"ideographiccallparen",
"ideographiccentrecircle",
"ideographicclose",
"ideographiccomma",
"ideographiccommaleft",
"ideographiccongratulationparen",
"ideographiccorrectcircle",
"ideographicearthparen",
"ideographicenterpriseparen",
"ideographicexcellentcircle",
"ideographicfestivalparen",
"ideographicfinancialcircle",
"ideographicfinancialparen",
"ideographicfireparen",
"ideographichaveparen",
"ideographichighcircle",
"ideographiciterationmark",
"ideographiclaborcircle",
"ideographiclaborparen",
"ideographicleftcircle",
"ideographiclowcircle",
"ideographicmedicinecircle",
"ideographicmetalparen",
"ideographicmoonparen",
"ideographicnameparen",
"ideographicperiod",
"ideographicprintcircle",
"ideographicreachparen",
"ideographicrepresentparen",
"ideographicresourceparen",
"ideographicrightcircle",
"ideographicsecretcircle",
"ideographicselfparen",
"ideographicsocietyparen",
"ideographicspace",
"ideographicspecialparen",
"ideographicstockparen",
"ideographicstudyparen",
"ideographicsunparen",
"ideographicsuperviseparen",
"ideographicwaterparen",
"ideographicwoodparen",
"ideographiczero",
"ideographmetalcircle",
"ideographmooncircle",
"ideographnamecircle",
"ideographsuncircle",
"ideographwatercircle",
"ideographwoodcircle",
"ideva",
"idieresis",
"idieresisacute",
"idieresiscyrillic",
"idotbelow",
"iebrevecyrillic",
"iecyrillic",
"ieungacirclekorean",
"ieungaparenkorean",
"ieungcirclekorean",
"ieungkorean",
"ieungparenkorean",
"igrave",
"igujarati",
"igurmukhi",
"ihiragana",
"ihookabove",
"iibengali",
"iicyrillic",
"iideva",
"iigujarati",
"iigurmukhi",
"iimatragurmukhi",
"iinvertedbreve",
"iishortcyrillic",
"iivowelsignbengali",
"iivowelsigndeva",
"iivowelsigngujarati",
"ij",
"ikatakana",
"ikatakanahalfwidth",
"ikorean",
"ilde",
"iluyhebrew",
"imacron",
"imacroncyrillic",
"imageorapproximatelyequal",
"imatragurmukhi",
"imonospace",
"increment",
"infinity",
"iniarmenian",
"integerdivide",
"integral",
"integralbottom",
"integralbt",
"integralex",
"integraltop",
"integraltp",
"intercal",
"interrobang",
"interrobangdown",
"intersection",
"intersectiondbl",
"intersectionsq",
"intisquare",
"invbullet",
"invcircle",
"invsmileface",
"iocyrillic",
"iogonek",
"iota",
"iotadieresis",
"iotadieresistonos",
"iotalatin",
"iotatonos",
"iparen",
"irigurmukhi",
"ismallhiragana",
"ismallkatakana",
"ismallkatakanahalfwidth",
"issharbengali",
"istroke",
"isuperior",
"iterationhiragana",
"iterationkatakana",
"itilde",
"itildebelow",
"iubopomofo",
"iucyrillic",
"ivowelsignbengali",
"ivowelsigndeva",
"ivowelsigngujarati",
"izhitsacyrillic",
"izhitsadblgravecyrillic",
"j",
"jaarmenian",
"jabengali",
"jadeva",
"jagujarati",
"jagurmukhi",
"jbopomofo",
"jcaron",
"jcircle",
"jcircumflex",
"jcrossedtail",
"jdotlessstroke",
"jecyrillic",
"jeemarabic",
"jeemfinalarabic",
"jeeminitialarabic",
"jeemmedialarabic",
"jeharabic",
"jehfinalarabic",
"jhabengali",
"jhadeva",
"jhagujarati",
"jhagurmukhi",
"jheharmenian",
"jis",
"jmonospace",
"jparen",
"jsuperior",
"k",
"kabashkircyrillic",
"kabengali",
"kacute",
"kacyrillic",
"kadescendercyrillic",
"kadeva",
"kaf",
"kafarabic",
"kafdagesh",
"kafdageshhebrew",
"kaffinalarabic",
"kafhebrew",
"kafinitialarabic",
"kafmedialarabic",
"kafrafehebrew",
"kagujarati",
"kagurmukhi",
"kahiragana",
"kahookcyrillic",
"kakatakana",
"kakatakanahalfwidth",
"kappa",
"kappasymbolgreek",
"kapyeounmieumkorean",
"kapyeounphieuphkorean",
"kapyeounpieupkorean",
"kapyeounssangpieupkorean",
"karoriisquare",
"kashidaautoarabic",
"kashidaautonosidebearingarabic",
"kasmallkatakana",
"kasquare",
"kasraarabic",
"kasratanarabic",
"kastrokecyrillic",
"katahiraprolongmarkhalfwidth",
"kaverticalstrokecyrillic",
"kbopomofo",
"kcalsquare",
"kcaron",
"kcedilla",
"kcircle",
"kcommaaccent",
"kdotbelow",
"keharmenian",
"kehiragana",
"kekatakana",
"kekatakanahalfwidth",
"kenarmenian",
"kesmallkatakana",
"kgreenlandic",
"khabengali",
"khacyrillic",
"khadeva",
"khagujarati",
"khagurmukhi",
"khaharabic",
"khahfinalarabic",
"khahinitialarabic",
"khahmedialarabic",
"kheicoptic",
"khhadeva",
"khhagurmukhi",
"khieukhacirclekorean",
"khieukhaparenkorean",
"khieukhcirclekorean",
"khieukhkorean",
"khieukhparenkorean",
"khokhaithai",
"khokhonthai",
"khokhuatthai",
"khokhwaithai",
"khomutthai",
"khook",
"khorakhangthai",
"khzsquare",
"kihiragana",
"kikatakana",
"kikatakanahalfwidth",
"kiroguramusquare",
"kiromeetorusquare",
"kirosquare",
"kiyeokacirclekorean",
"kiyeokaparenkorean",
"kiyeokcirclekorean",
"kiyeokkorean",
"kiyeokparenkorean",
"kiyeoksioskorean",
"kjecyrillic",
"klinebelow",
"klsquare",
"kmcubedsquare",
"kmonospace",
"kmsquaredsquare",
"kohiragana",
"kohmsquare",
"kokaithai",
"kokatakana",
"kokatakanahalfwidth",
"kooposquare",
"koppacyrillic",
"koreanstandardsymbol",
"koroniscmb",
"kparen",
"kpasquare",
"ksicyrillic",
"ktsquare",
"kturned",
"kuhiragana",
"kukatakana",
"kukatakanahalfwidth",
"kvsquare",
"kwsquare",
"l",
"labengali",
"lacute",
"ladeva",
"lagujarati",
"lagurmukhi",
"lakkhangyaothai",
"lamaleffinalarabic",
"lamalefhamzaabovefinalarabic",
"lamalefhamzaaboveisolatedarabic",
"lamalefhamzabelowfinalarabic",
"lamalefhamzabelowisolatedarabic",
"lamalefisolatedarabic",
"lamalefmaddaabovefinalarabic",
"lamalefmaddaaboveisolatedarabic",
"lamarabic",
"lambda",
"lambdastroke",
"lamed",
"lameddagesh",
"lameddageshhebrew",
"lamedhebrew",
"lamfinalarabic",
"lamhahinitialarabic",
"laminitialarabic",
"lamjeeminitialarabic",
"lamkhahinitialarabic",
"lamlamhehisolatedarabic",
"lammedialarabic",
"lammeemhahinitialarabic",
"lammeeminitialarabic",
"largecircle",
"latticetop",
"lbar",
"lbelt",
"lbopomofo",
"lcaron",
"lcedilla",
"lcircle",
"lcircumflexbelow",
"lcommaaccent",
"ldot",
"ldotaccent",
"ldotbelow",
"ldotbelowmacron",
"leftangleabovecmb",
"lefttackbelowcmb",
"less",
"lessdbleqlgreater",
"lessdblequal",
"lessdot",
"lessequal",
"lessequalgreater",
"lessequalorgreater",
"lessmonospace",
"lessmuch",
"lessnotdblequal",
"lessnotequal",
"lessorapproxeql",
"lessorequalslant",
"lessorequivalent",
"lessorgreater",
"lessornotdbleql",
"lessornotequal",
"lessorsimilar",
"lessoverequal",
"lesssmall",
"lezh",
"lfblock",
"lhookretroflex",
"lira",
"liwnarmenian",
"lj",
"ljecyrillic",
"ll",
"lladeva",
"llagujarati",
"llinebelow",
"llladeva",
"llvocalicbengali",
"llvocalicdeva",
"llvocalicvowelsignbengali",
"llvocalicvowelsigndeva",
"lmiddletilde",
"lmonospace",
"lmsquare",
"lochulathai",
"logicaland",
"logicalnot",
"logicalnotreversed",
"logicalor",
"lolingthai",
"longs",
"longst",
"lowlinecenterline",
"lowlinecmb",
"lowlinedashed",
"lozenge",
"lparen",
"lscript",
"lslash",
"lsquare",
"lsuperior",
"ltshade",
"luthai",
"lvocalicbengali",
"lvocalicdeva",
"lvocalicvowelsignbengali",
"lvocalicvowelsigndeva",
"lxsquare",
"m",
"mabengali",
"macron",
"macronbelowcmb",
"macroncmb",
"macronlowmod",
"macronmonospace",
"macute",
"madeva",
"magujarati",
"magurmukhi",
"mahapakhhebrew",
"mahapakhlefthebrew",
"mahiragana",
"maichattawalowleftthai",
"maichattawalowrightthai",
"maichattawathai",
"maichattawaupperleftthai",
"maieklowleftthai",
"maieklowrightthai",
"maiekthai",
"maiekupperleftthai",
"maihanakatleftthai",
"maihanakatthai",
"maitaikhuleftthai",
"maitaikhuthai",
"maitholowleftthai",
"maitholowrightthai",
"maithothai",
"maithoupperleftthai",
"maitrilowleftthai",
"maitrilowrightthai",
"maitrithai",
"maitriupperleftthai",
"maiyamokthai",
"makatakana",
"makatakanahalfwidth",
"male",
"maltesecross",
"mansyonsquare",
"maqafhebrew",
"mars",
"masoracirclehebrew",
"masquare",
"mbopomofo",
"mbsquare",
"mcircle",
"mcubedsquare",
"mdotaccent",
"mdotbelow",
"measuredangle",
"meemarabic",
"meemfinalarabic",
"meeminitialarabic",
"meemmedialarabic",
"meemmeeminitialarabic",
"meemmeemisolatedarabic",
"meetorusquare",
"mehiragana",
"meizierasquare",
"mekatakana",
"mekatakanahalfwidth",
"mem",
"memdagesh",
"memdageshhebrew",
"memhebrew",
"menarmenian",
"merkhahebrew",
"merkhakefulahebrew",
"merkhakefulalefthebrew",
"merkhalefthebrew",
"mhook",
"mhzsquare",
"middledotkatakanahalfwidth",
"middot",
"mieumacirclekorean",
"mieumaparenkorean",
"mieumcirclekorean",
"mieumkorean",
"mieumpansioskorean",
"mieumparenkorean",
"mieumpieupkorean",
"mieumsioskorean",
"mihiragana",
"mikatakana",
"mikatakanahalfwidth",
"minus",
"minusbelowcmb",
"minuscircle",
"minusmod",
"minusplus",
"minute",
"miribaarusquare",
"mirisquare",
"mlonglegturned",
"mlsquare",
"mmcubedsquare",
"mmonospace",
"mmsquaredsquare",
"mohiragana",
"mohmsquare",
"mokatakana",
"mokatakanahalfwidth",
"molsquare",
"momathai",
"moverssquare",
"moverssquaredsquare",
"mparen",
"mpasquare",
"mssquare",
"msuperior",
"mturned",
"mu",
"mu1",
"muasquare",
"muchgreater",
"muchless",
"mufsquare",
"mugreek",
"mugsquare",
"muhiragana",
"mukatakana",
"mukatakanahalfwidth",
"mulsquare",
"multicloseleft",
"multicloseright",
"multimap",
"multiopenleft",
"multiopenright",
"multiply",
"mumsquare",
"munahhebrew",
"munahlefthebrew",
"musicalnote",
"musicalnotedbl",
"musicflatsign",
"musicsharpsign",
"mussquare",
"muvsquare",
"muwsquare",
"mvmegasquare",
"mvsquare",
"mwmegasquare",
"mwsquare",
"n",
"nabengali",
"nabla",
"nacute",
"nadeva",
"nagujarati",
"nagurmukhi",
"nahiragana",
"nakatakana",
"nakatakanahalfwidth",
"nand",
"napostrophe",
"nasquare",
"natural",
"nbopomofo",
"nbspace",
"ncaron",
"ncedilla",
"ncircle",
"ncircumflexbelow",
"ncommaaccent",
"ndotaccent",
"ndotbelow",
"negationslash",
"nehiragana",
"nekatakana",
"nekatakanahalfwidth",
"newsheqelsign",
"nfsquare",
"ng",
"ngabengali",
"ngadeva",
"ngagujarati",
"ngagurmukhi",
"ngonguthai",
"nhiragana",
"nhookleft",
"nhookretroflex",
"nieunacirclekorean",
"nieunaparenkorean",
"nieuncieuckorean",
"nieuncirclekorean",
"nieunhieuhkorean",
"nieunkorean",
"nieunpansioskorean",
"nieunparenkorean",
"nieunsioskorean",
"nieuntikeutkorean",
"nihiragana",
"nikatakana",
"nikatakanahalfwidth",
"nikhahitleftthai",
"nikhahitthai",
"nine",
"ninearabic",
"ninebengali",
"ninecircle",
"ninecircleinversesansserif",
"ninedeva",
"ninegujarati",
"ninegurmukhi",
"ninehackarabic",
"ninehangzhou",
"nineideographicparen",
"nineinferior",
"ninemonospace",
"nineoldstyle",
"nineparen",
"nineperiod",
"ninepersian",
"nineroman",
"ninesuperior",
"nineteencircle",
"nineteenparen",
"nineteenperiod",
"ninethai",
"nj",
"njecyrillic",
"nkatakana",
"nkatakanahalfwidth",
"nlegrightlong",
"nlinebelow",
"nmonospace",
"nmsquare",
"nnabengali",
"nnadeva",
"nnagujarati",
"nnagurmukhi",
"nnnadeva",
"nohiragana",
"nokatakana",
"nokatakanahalfwidth",
"nonbreakingspace",
"nonenthai",
"nonuthai",
"noonarabic",
"noonfinalarabic",
"noonghunnaarabic",
"noonghunnafinalarabic",
"nooninitialarabic",
"noonjeeminitialarabic",
"noonjeemisolatedarabic",
"noonmedialarabic",
"noonmeeminitialarabic",
"noonmeemisolatedarabic",
"noonnoonfinalarabic",
"notapproxequal",
"notarrowboth",
"notarrowleft",
"notarrowright",
"notbar",
"notcontains",
"notdblarrowboth",
"notdblarrowleft",
"notdblarrowright",
"notelement",
"notelementof",
"notequal",
"notexistential",
"notfollows",
"notforces",
"notforcesextra",
"notgreater",
"notgreaterequal",
"notgreaternorequal",
"notgreaternorless",
"notidentical",
"notless",
"notlessequal",
"notlessnorequal",
"notparallel",
"notprecedes",
"notsatisfies",
"notsimilar",
"notsubset",
"notsubseteql",
"notsubsetoreql",
"notsucceeds",
"notsuperset",
"notsuperseteql",
"notsupersetoreql",
"nottriangeqlleft",
"nottriangeqlright",
"nottriangleleft",
"nottriangleright",
"notturnstile",
"nowarmenian",
"nparen",
"nssquare",
"nsuperior",
"ntilde",
"nu",
"nuhiragana",
"nukatakana",
"nukatakanahalfwidth",
"nuktabengali",
"nuktadeva",
"nuktagujarati",
"nuktagurmukhi",
"numbersign",
"numbersignmonospace",
"numbersignsmall",
"numeralsigngreek",
"numeralsignlowergreek",
"numero",
"nun",
"nundagesh",
"nundageshhebrew",
"nunhebrew",
"nvsquare",
"nwsquare",
"nyabengali",
"nyadeva",
"nyagujarati",
"nyagurmukhi",
"o",
"oacute",
"oangthai",
"obarred",
"obarredcyrillic",
"obarreddieresiscyrillic",
"obengali",
"obopomofo",
"obreve",
"ocandradeva",
"ocandragujarati",
"ocandravowelsigndeva",
"ocandravowelsigngujarati",
"ocaron",
"ocircle",
"ocircumflex",
"ocircumflexacute",
"ocircumflexdotbelow",
"ocircumflexgrave",
"ocircumflexhookabove",
"ocircumflextilde",
"ocyrillic",
"odblacute",
"odblgrave",
"odeva",
"odieresis",
"odieresiscyrillic",
"odotbelow",
"oe",
"oekorean",
"ogonek",
"ogonekcmb",
"ograve",
"ogujarati",
"oharmenian",
"ohiragana",
"ohookabove",
"ohorn",
"ohornacute",
"ohorndotbelow",
"ohorngrave",
"ohornhookabove",
"ohorntilde",
"ohungarumlaut",
"oi",
"oinvertedbreve",
"okatakana",
"okatakanahalfwidth",
"okorean",
"olehebrew",
"omacron",
"omacronacute",
"omacrongrave",
"omdeva",
"omega",
"omega1",
"omegacyrillic",
"omegalatinclosed",
"omegaroundcyrillic",
"omegatitlocyrillic",
"omegatonos",
"omgujarati",
"omicron",
"omicrontonos",
"omonospace",
"one",
"onearabic",
"onebengali",
"onecircle",
"onecircleinversesansserif",
"onedeva",
"onedotenleader",
"oneeighth",
"onefitted",
"onegujarati",
"onegurmukhi",
"onehackarabic",
"onehalf",
"onehangzhou",
"oneideographicparen",
"oneinferior",
"onemonospace",
"onenumeratorbengali",
"oneoldstyle",
"oneparen",
"oneperiod",
"onepersian",
"onequarter",
"oneroman",
"onesuperior",
"onethai",
"onethird",
"oogonek",
"oogonekmacron",
"oogurmukhi",
"oomatragurmukhi",
"oopen",
"oparen",
"openbullet",
"option",
"ordfeminine",
"ordmasculine",
"orthogonal",
"orunderscore",
"oshortdeva",
"oshortvowelsigndeva",
"oslash",
"oslashacute",
"osmallhiragana",
"osmallkatakana",
"osmallkatakanahalfwidth",
"ostrokeacute",
"osuperior",
"otcyrillic",
"otilde",
"otildeacute",
"otildedieresis",
"oubopomofo",
"overline",
"overlinecenterline",
"overlinecmb",
"overlinedashed",
"overlinedblwavy",
"overlinewavy",
"overscore",
"ovowelsignbengali",
"ovowelsigndeva",
"ovowelsigngujarati",
"owner",
"p",
"paampssquare",
"paasentosquare",
"pabengali",
"pacute",
"padeva",
"pagedown",
"pageup",
"pagujarati",
"pagurmukhi",
"pahiragana",
"paiyannoithai",
"pakatakana",
"palatalizationcyrilliccmb",
"palochkacyrillic",
"pansioskorean",
"paragraph",
"parallel",
"parenleft",
"parenleftaltonearabic",
"parenleftbt",
"parenleftex",
"parenleftinferior",
"parenleftmonospace",
"parenleftsmall",
"parenleftsuperior",
"parenlefttp",
"parenleftvertical",
"parenright",
"parenrightaltonearabic",
"parenrightbt",
"parenrightex",
"parenrightinferior",
"parenrightmonospace",
"parenrightsmall",
"parenrightsuperior",
"parenrighttp",
"parenrightvertical",
"partialdiff",
"paseqhebrew",
"pashtahebrew",
"pasquare",
"patah",
"patah11",
"patah1d",
"patah2a",
"patahhebrew",
"patahnarrowhebrew",
"patahquarterhebrew",
"patahwidehebrew",
"pazerhebrew",
"pbopomofo",
"pcircle",
"pdotaccent",
"pe",
"pecyrillic",
"pedagesh",
"pedageshhebrew",
"peezisquare",
"pefinaldageshhebrew",
"peharabic",
"peharmenian",
"pehebrew",
"pehfinalarabic",
"pehinitialarabic",
"pehiragana",
"pehmedialarabic",
"pekatakana",
"pemiddlehookcyrillic",
"perafehebrew",
"percent",
"percentarabic",
"percentmonospace",
"percentsmall",
"period",
"periodarmenian",
"periodcentered",
"periodhalfwidth",
"periodinferior",
"periodmonospace",
"periodsmall",
"periodsuperior",
"perispomenigreekcmb",
"perpcorrespond",
"perpendicular",
"pertenthousand",
"perthousand",
"peseta",
"pfsquare",
"phabengali",
"phadeva",
"phagujarati",
"phagurmukhi",
"phi",
"phi1",
"phieuphacirclekorean",
"phieuphaparenkorean",
"phieuphcirclekorean",
"phieuphkorean",
"phieuphparenkorean",
"philatin",
"phinthuthai",
"phisymbolgreek",
"phook",
"phophanthai",
"phophungthai",
"phosamphaothai",
"pi",
"pi1",
"pieupacirclekorean",
"pieupaparenkorean",
"pieupcieuckorean",
"pieupcirclekorean",
"pieupkiyeokkorean",
"pieupkorean",
"pieupparenkorean",
"pieupsioskiyeokkorean",
"pieupsioskorean",
"pieupsiostikeutkorean",
"pieupthieuthkorean",
"pieuptikeutkorean",
"pihiragana",
"pikatakana",
"pisymbolgreek",
"piwrarmenian",
"planckover2pi",
"planckover2pi1",
"plus",
"plusbelowcmb",
"pluscircle",
"plusminus",
"plusmod",
"plusmonospace",
"plussmall",
"plussuperior",
"pmonospace",
"pmsquare",
"pohiragana",
"pointingindexdownwhite",
"pointingindexleftwhite",
"pointingindexrightwhite",
"pointingindexupwhite",
"pokatakana",
"poplathai",
"postalmark",
"postalmarkface",
"pparen",
"precedenotdbleqv",
"precedenotslnteql",
"precedeornoteqvlnt",
"precedes",
"precedesequal",
"precedesorcurly",
"precedesorequal",
"prescription",
"prime",
"primemod",
"primereverse",
"primereversed",
"product",
"projective",
"prolongedkana",
"propellor",
"propersubset",
"propersuperset",
"proportion",
"proportional",
"psi",
"psicyrillic",
"psilipneumatacyrilliccmb",
"pssquare",
"puhiragana",
"pukatakana",
"punctdash",
"pvsquare",
"pwsquare",
"q",
"qadeva",
"qadmahebrew",
"qafarabic",
"qaffinalarabic",
"qafinitialarabic",
"qafmedialarabic",
"qamats",
"qamats10",
"qamats1a",
"qamats1c",
"qamats27",
"qamats29",
"qamats33",
"qamatsde",
"qamatshebrew",
"qamatsnarrowhebrew",
"qamatsqatanhebrew",
"qamatsqatannarrowhebrew",
"qamatsqatanquarterhebrew",
"qamatsqatanwidehebrew",
"qamatsquarterhebrew",
"qamatswidehebrew",
"qarneyparahebrew",
"qbopomofo",
"qcircle",
"qhook",
"qmonospace",
"qof",
"qofdagesh",
"qofdageshhebrew",
"qofhebrew",
"qparen",
"quarternote",
"qubuts",
"qubuts18",
"qubuts25",
"qubuts31",
"qubutshebrew",
"qubutsnarrowhebrew",
"qubutsquarterhebrew",
"qubutswidehebrew",
"question",
"questionarabic",
"questionarmenian",
"questiondown",
"questiondownsmall",
"questiongreek",
"questionmonospace",
"questionsmall",
"quotedbl",
"quotedblbase",
"quotedblleft",
"quotedblmonospace",
"quotedblprime",
"quotedblprimereversed",
"quotedblright",
"quoteleft",
"quoteleftreversed",
"quotereversed",
"quoteright",
"quoterightn",
"quotesinglbase",
"quotesingle",
"quotesinglemonospace",
"r",
"raarmenian",
"rabengali",
"racute",
"radeva",
"radical",
"radicalex",
"radoverssquare",
"radoverssquaredsquare",
"radsquare",
"rafe",
"rafehebrew",
"ragujarati",
"ragurmukhi",
"rahiragana",
"rakatakana",
"rakatakanahalfwidth",
"ralowerdiagonalbengali",
"ramiddlediagonalbengali",
"ramshorn",
"rangedash",
"ratio",
"rbopomofo",
"rcaron",
"rcedilla",
"rcircle",
"rcommaaccent",
"rdblgrave",
"rdotaccent",
"rdotbelow",
"rdotbelowmacron",
"referencemark",
"reflexsubset",
"reflexsuperset",
"registered",
"registersans",
"registerserif",
"reharabic",
"reharmenian",
"rehfinalarabic",
"rehiragana",
"rekatakana",
"rekatakanahalfwidth",
"resh",
"reshdageshhebrew",
"reshhebrew",
"revasymptequal",
"reversedtilde",
"reviahebrew",
"reviamugrashhebrew",
"revlogicalnot",
"revsimilar",
"rfishhook",
"rfishhookreversed",
"rhabengali",
"rhadeva",
"rho",
"rho1",
"rhook",
"rhookturned",
"rhookturnedsuperior",
"rhosymbolgreek",
"rhotichookmod",
"rieulacirclekorean",
"rieulaparenkorean",
"rieulcirclekorean",
"rieulhieuhkorean",
"rieulkiyeokkorean",
"rieulkiyeoksioskorean",
"rieulkorean",
"rieulmieumkorean",
"rieulpansioskorean",
"rieulparenkorean",
"rieulphieuphkorean",
"rieulpieupkorean",
"rieulpieupsioskorean",
"rieulsioskorean",
"rieulthieuthkorean",
"rieultikeutkorean",
"rieulyeorinhieuhkorean",
"rightangle",
"rightanglene",
"rightanglenw",
"rightanglese",
"rightanglesw",
"righttackbelowcmb",
"righttriangle",
"rihiragana",
"rikatakana",
"rikatakanahalfwidth",
"ring",
"ringbelowcmb",
"ringcmb",
"ringfitted",
"ringhalfleft",
"ringhalfleftarmenian",
"ringhalfleftbelowcmb",
"ringhalfleftcentered",
"ringhalfright",
"ringhalfrightbelowcmb",
"ringhalfrightcentered",
"ringinequal",
"rinvertedbreve",
"rittorusquare",
"rlinebelow",
"rlongleg",
"rlonglegturned",
"rmonospace",
"rohiragana",
"rokatakana",
"rokatakanahalfwidth",
"roruathai",
"rparen",
"rrabengali",
"rradeva",
"rragurmukhi",
"rreharabic",
"rrehfinalarabic",
"rrvocalicbengali",
"rrvocalicdeva",
"rrvocalicgujarati",
"rrvocalicvowelsignbengali",
"rrvocalicvowelsigndeva",
"rrvocalicvowelsigngujarati",
"rsuperior",
"rtblock",
"rturned",
"rturnedsuperior",
"ruhiragana",
"rukatakana",
"rukatakanahalfwidth",
"rupeemarkbengali",
"rupeesignbengali",
"rupiah",
"ruthai",
"rvocalicbengali",
"rvocalicdeva",
"rvocalicgujarati",
"rvocalicvowelsignbengali",
"rvocalicvowelsigndeva",
"rvocalicvowelsigngujarati",
"s",
"sabengali",
"sacute",
"sacutedotaccent",
"sadarabic",
"sadeva",
"sadfinalarabic",
"sadinitialarabic",
"sadmedialarabic",
"sagujarati",
"sagurmukhi",
"sahiragana",
"sakatakana",
"sakatakanahalfwidth",
"sallallahoualayhewasallamarabic",
"samekh",
"samekhdagesh",
"samekhdageshhebrew",
"samekhhebrew",
"saraaathai",
"saraaethai",
"saraaimaimalaithai",
"saraaimaimuanthai",
"saraamthai",
"saraathai",
"saraethai",
"saraiileftthai",
"saraiithai",
"saraileftthai",
"saraithai",
"saraothai",
"saraueeleftthai",
"saraueethai",
"saraueleftthai",
"sarauethai",
"sarauthai",
"sarauuthai",
"satisfies",
"sbopomofo",
"scaron",
"scarondotaccent",
"scedilla",
"schwa",
"schwacyrillic",
"schwadieresiscyrillic",
"schwahook",
"scircle",
"scircumflex",
"scommaaccent",
"sdotaccent",
"sdotbelow",
"sdotbelowdotaccent",
"seagullbelowcmb",
"second",
"secondtonechinese",
"section",
"seenarabic",
"seenfinalarabic",
"seeninitialarabic",
"seenmedialarabic",
"segol",
"segol13",
"segol1f",
"segol2c",
"segolhebrew",
"segolnarrowhebrew",
"segolquarterhebrew",
"segoltahebrew",
"segolwidehebrew",
"seharmenian",
"sehiragana",
"sekatakana",
"sekatakanahalfwidth",
"semicolon",
"semicolonarabic",
"semicolonmonospace",
"semicolonsmall",
"semivoicedmarkkana",
"semivoicedmarkkanahalfwidth",
"sentisquare",
"sentosquare",
"seven",
"sevenarabic",
"sevenbengali",
"sevencircle",
"sevencircleinversesansserif",
"sevendeva",
"seveneighths",
"sevengujarati",
"sevengurmukhi",
"sevenhackarabic",
"sevenhangzhou",
"sevenideographicparen",
"seveninferior",
"sevenmonospace",
"sevenoldstyle",
"sevenparen",
"sevenperiod",
"sevenpersian",
"sevenroman",
"sevensuperior",
"seventeencircle",
"seventeenparen",
"seventeenperiod",
"seventhai",
"sfthyphen",
"shaarmenian",
"shabengali",
"shacyrillic",
"shaddaarabic",
"shaddadammaarabic",
"shaddadammatanarabic",
"shaddafathaarabic",
"shaddakasraarabic",
"shaddakasratanarabic",
"shade",
"shadedark",
"shadelight",
"shademedium",
"shadeva",
"shagujarati",
"shagurmukhi",
"shalshelethebrew",
"sharp",
"shbopomofo",
"shchacyrillic",
"sheenarabic",
"sheenfinalarabic",
"sheeninitialarabic",
"sheenmedialarabic",
"sheicoptic",
"sheqel",
"sheqelhebrew",
"sheva",
"sheva115",
"sheva15",
"sheva22",
"sheva2e",
"shevahebrew",
"shevanarrowhebrew",
"shevaquarterhebrew",
"shevawidehebrew",
"shhacyrillic",
"shiftleft",
"shiftright",
"shimacoptic",
"shin",
"shindagesh",
"shindageshhebrew",
"shindageshshindot",
"shindageshshindothebrew",
"shindageshsindot",
"shindageshsindothebrew",
"shindothebrew",
"shinhebrew",
"shinshindot",
"shinshindothebrew",
"shinsindot",
"shinsindothebrew",
"shook",
"sigma",
"sigma1",
"sigmafinal",
"sigmalunatesymbolgreek",
"sihiragana",
"sikatakana",
"sikatakanahalfwidth",
"siluqhebrew",
"siluqlefthebrew",
"similar",
"similarequal",
"sindothebrew",
"siosacirclekorean",
"siosaparenkorean",
"sioscieuckorean",
"sioscirclekorean",
"sioskiyeokkorean",
"sioskorean",
"siosnieunkorean",
"siosparenkorean",
"siospieupkorean",
"siostikeutkorean",
"six",
"sixarabic",
"sixbengali",
"sixcircle",
"sixcircleinversesansserif",
"sixdeva",
"sixgujarati",
"sixgurmukhi",
"sixhackarabic",
"sixhangzhou",
"sixideographicparen",
"sixinferior",
"sixmonospace",
"sixoldstyle",
"sixparen",
"sixperiod",
"sixpersian",
"sixroman",
"sixsuperior",
"sixteencircle",
"sixteencurrencydenominatorbengali",
"sixteenparen",
"sixteenperiod",
"sixthai",
"slash",
"slashmonospace",
"slong",
"slongdotaccent",
"slurabove",
"slurbelow",
"smile",
"smileface",
"smonospace",
"sofpasuqhebrew",
"softhyphen",
"softsigncyrillic",
"sohiragana",
"sokatakana",
"sokatakanahalfwidth",
"soliduslongoverlaycmb",
"solidusshortoverlaycmb",
"sorusithai",
"sosalathai",
"sosothai",
"sosuathai",
"space",
"spacehackarabic",
"spade",
"spadesuitblack",
"spadesuitwhite",
"sparen",
"sphericalangle",
"square",
"squarebelowcmb",
"squarecc",
"squarecm",
"squarediagonalcrosshatchfill",
"squaredot",
"squarehorizontalfill",
"squareimage",
"squarekg",
"squarekm",
"squarekmcapital",
"squareln",
"squarelog",
"squaremg",
"squaremil",
"squareminus",
"squaremm",
"squaremsquared",
"squaremultiply",
"squareoriginal",
"squareorthogonalcrosshatchfill",
"squareplus",
"squaresolid",
"squareupperlefttolowerrightfill",
"squareupperrighttolowerleftfill",
"squareverticalfill",
"squarewhitewithsmallblack",
"squiggleleftright",
"squiggleright",
"srsquare",
"ssabengali",
"ssadeva",
"ssagujarati",
"ssangcieuckorean",
"ssanghieuhkorean",
"ssangieungkorean",
"ssangkiyeokkorean",
"ssangnieunkorean",
"ssangpieupkorean",
"ssangsioskorean",
"ssangtikeutkorean",
"ssuperior",
"st",
"star",
"sterling",
"sterlingmonospace",
"strokelongoverlaycmb",
"strokeshortoverlaycmb",
"subset",
"subsetdbl",
"subsetdblequal",
"subsetnoteql",
"subsetnotequal",
"subsetorequal",
"subsetornotdbleql",
"subsetsqequal",
"succeeds",
"suchthat",
"suhiragana",
"sukatakana",
"sukatakanahalfwidth",
"sukunarabic",
"summation",
"sun",
"superset",
"supersetdbl",
"supersetdblequal",
"supersetnoteql",
"supersetnotequal",
"supersetorequal",
"supersetornotdbleql",
"supersetsqequal",
"svsquare",
"syouwaerasquare",
"t",
"tabengali",
"tackdown",
"tackleft",
"tadeva",
"tagujarati",
"tagurmukhi",
"taharabic",
"tahfinalarabic",
"tahinitialarabic",
"tahiragana",
"tahmedialarabic",
"taisyouerasquare",
"takatakana",
"takatakanahalfwidth",
"tatweelarabic",
"tau",
"tav",
"tavdages",
"tavdagesh",
"tavdageshhebrew",
"tavhebrew",
"tbar",
"tbopomofo",
"tcaron",
"tccurl",
"tcedilla",
"tcheharabic",
"tchehfinalarabic",
"tchehinitialarabic",
"tchehmedialarabic",
"tcircle",
"tcircumflexbelow",
"tcommaaccent",
"tdieresis",
"tdotaccent",
"tdotbelow",
"tecyrillic",
"tedescendercyrillic",
"teharabic",
"tehfinalarabic",
"tehhahinitialarabic",
"tehhahisolatedarabic",
"tehinitialarabic",
"tehiragana",
"tehjeeminitialarabic",
"tehjeemisolatedarabic",
"tehmarbutaarabic",
"tehmarbutafinalarabic",
"tehmedialarabic",
"tehmeeminitialarabic",
"tehmeemisolatedarabic",
"tehnoonfinalarabic",
"tekatakana",
"tekatakanahalfwidth",
"telephone",
"telephoneblack",
"telishagedolahebrew",
"telishaqetanahebrew",
"tencircle",
"tenideographicparen",
"tenparen",
"tenperiod",
"tenroman",
"tesh",
"tet",
"tetdagesh",
"tetdageshhebrew",
"tethebrew",
"tetsecyrillic",
"tevirhebrew",
"tevirlefthebrew",
"thabengali",
"thadeva",
"thagujarati",
"thagurmukhi",
"thalarabic",
"thalfinalarabic",
"thanthakhatlowleftthai",
"thanthakhatlowrightthai",
"thanthakhatthai",
"thanthakhatupperleftthai",
"theharabic",
"thehfinalarabic",
"thehinitialarabic",
"thehmedialarabic",
"thereexists",
"therefore",
"theta",
"theta1",
"thetasymbolgreek",
"thieuthacirclekorean",
"thieuthaparenkorean",
"thieuthcirclekorean",
"thieuthkorean",
"thieuthparenkorean",
"thirteencircle",
"thirteenparen",
"thirteenperiod",
"thonangmonthothai",
"thook",
"thophuthaothai",
"thorn",
"thothahanthai",
"thothanthai",
"thothongthai",
"thothungthai",
"thousandcyrillic",
"thousandsseparatorarabic",
"thousandsseparatorpersian",
"three",
"threearabic",
"threebengali",
"threecircle",
"threecircleinversesansserif",
"threedeva",
"threeeighths",
"threegujarati",
"threegurmukhi",
"threehackarabic",
"threehangzhou",
"threeideographicparen",
"threeinferior",
"threemonospace",
"threenumeratorbengali",
"threeoldstyle",
"threeparen",
"threeperiod",
"threepersian",
"threequarters",
"threequartersemdash",
"threeroman",
"threesuperior",
"threethai",
"thzsquare",
"tihiragana",
"tikatakana",
"tikatakanahalfwidth",
"tikeutacirclekorean",
"tikeutaparenkorean",
"tikeutcirclekorean",
"tikeutkorean",
"tikeutparenkorean",
"tilde",
"tildebelowcmb",
"tildecmb",
"tildecomb",
"tildedoublecmb",
"tildeoperator",
"tildeoverlaycmb",
"tildeverticalcmb",
"timescircle",
"tipehahebrew",
"tipehalefthebrew",
"tippigurmukhi",
"titlocyrilliccmb",
"tiwnarmenian",
"tlinebelow",
"tmonospace",
"toarmenian",
"tohiragana",
"tokatakana",
"tokatakanahalfwidth",
"tonebarextrahighmod",
"tonebarextralowmod",
"tonebarhighmod",
"tonebarlowmod",
"tonebarmidmod",
"tonefive",
"tonesix",
"tonetwo",
"tonos",
"tonsquare",
"topatakthai",
"tortoiseshellbracketleft",
"tortoiseshellbracketleftsmall",
"tortoiseshellbracketleftvertical",
"tortoiseshellbracketright",
"tortoiseshellbracketrightsmall",
"tortoiseshellbracketrightvertical",
"totaothai",
"tpalatalhook",
"tparen",
"trademark",
"trademarksans",
"trademarkserif",
"tretroflexhook",
"triagdn",
"triaglf",
"triagrt",
"triagup",
"triangle",
"triangledownsld",
"triangleinv",
"triangleleft",
"triangleleftequal",
"triangleleftsld",
"triangleright",
"trianglerightequal",
"trianglerightsld",
"trianglesolid",
"ts",
"tsadi",
"tsadidagesh",
"tsadidageshhebrew",
"tsadihebrew",
"tsecyrillic",
"tsere",
"tsere12",
"tsere1e",
"tsere2b",
"tserehebrew",
"tserenarrowhebrew",
"tserequarterhebrew",
"tserewidehebrew",
"tshecyrillic",
"tsuperior",
"ttabengali",
"ttadeva",
"ttagujarati",
"ttagurmukhi",
"tteharabic",
"ttehfinalarabic",
"ttehinitialarabic",
"ttehmedialarabic",
"tthabengali",
"tthadeva",
"tthagujarati",
"tthagurmukhi",
"tturned",
"tuhiragana",
"tukatakana",
"tukatakanahalfwidth",
"turnstileleft",
"turnstileright",
"tusmallhiragana",
"tusmallkatakana",
"tusmallkatakanahalfwidth",
"twelvecircle",
"twelveparen",
"twelveperiod",
"twelveroman",
"twelveudash",
"twentycircle",
"twentyhangzhou",
"twentyparen",
"twentyperiod",
"two",
"twoarabic",
"twobengali",
"twocircle",
"twocircleinversesansserif",
"twodeva",
"twodotenleader",
"twodotleader",
"twodotleadervertical",
"twogujarati",
"twogurmukhi",
"twohackarabic",
"twohangzhou",
"twoideographicparen",
"twoinferior",
"twomonospace",
"twonumeratorbengali",
"twooldstyle",
"twoparen",
"twoperiod",
"twopersian",
"tworoman",
"twostroke",
"twosuperior",
"twothai",
"twothirds",
"u",
"uacute",
"ubar",
"ubengali",
"ubopomofo",
"ubreve",
"ucaron",
"ucircle",
"ucircumflex",
"ucircumflexbelow",
"ucyrillic",
"udattadeva",
"udblacute",
"udblgrave",
"udeva",
"udieresis",
"udieresisacute",
"udieresisbelow",
"udieresiscaron",
"udieresiscyrillic",
"udieresisgrave",
"udieresismacron",
"udotbelow",
"ugrave",
"ugujarati",
"ugurmukhi",
"uhiragana",
"uhookabove",
"uhorn",
"uhornacute",
"uhorndotbelow",
"uhorngrave",
"uhornhookabove",
"uhorntilde",
"uhungarumlaut",
"uhungarumlautcyrillic",
"uinvertedbreve",
"ukatakana",
"ukatakanahalfwidth",
"ukcyrillic",
"ukorean",
"umacron",
"umacroncyrillic",
"umacrondieresis",
"umatragurmukhi",
"umonospace",
"underscore",
"underscoredbl",
"underscoremonospace",
"underscorevertical",
"underscorewavy",
"union",
"uniondbl",
"unionmulti",
"unionsq",
"universal",
"uogonek",
"uparen",
"upblock",
"upperdothebrew",
"uprise",
"upsilon",
"upsilondieresis",
"upsilondieresistonos",
"upsilonlatin",
"upsilontonos",
"upslope",
"uptackbelowcmb",
"uptackmod",
"uragurmukhi",
"uring",
"ushortcyrillic",
"usmallhiragana",
"usmallkatakana",
"usmallkatakanahalfwidth",
"ustraightcyrillic",
"ustraightstrokecyrillic",
"utilde",
"utildeacute",
"utildebelow",
"uubengali",
"uudeva",
"uugujarati",
"uugurmukhi",
"uumatragurmukhi",
"uuvowelsignbengali",
"uuvowelsigndeva",
"uuvowelsigngujarati",
"uvowelsignbengali",
"uvowelsigndeva",
"uvowelsigngujarati",
"v",
"vadeva",
"vagujarati",
"vagurmukhi",
"vakatakana",
"vav",
"vavdagesh",
"vavdagesh65",
"vavdageshhebrew",
"vavhebrew",
"vavholam",
"vavholamhebrew",
"vavvavhebrew",
"vavyodhebrew",
"vcircle",
"vdotbelow",
"vector",
"vecyrillic",
"veharabic",
"vehfinalarabic",
"vehinitialarabic",
"vehmedialarabic",
"vekatakana",
"venus",
"verticalbar",
"verticallineabovecmb",
"verticallinebelowcmb",
"verticallinelowmod",
"verticallinemod",
"vewarmenian",
"vhook",
"vikatakana",
"viramabengali",
"viramadeva",
"viramagujarati",
"visargabengali",
"visargadeva",
"visargagujarati",
"visiblespace",
"visualspace",
"vmonospace",
"voarmenian",
"voicediterationhiragana",
"voicediterationkatakana",
"voicedmarkkana",
"voicedmarkkanahalfwidth",
"vokatakana",
"vparen",
"vtilde",
"vturned",
"vuhiragana",
"vukatakana",
"w",
"wacute",
"waekorean",
"wahiragana",
"wakatakana",
"wakatakanahalfwidth",
"wakorean",
"wasmallhiragana",
"wasmallkatakana",
"wattosquare",
"wavedash",
"wavyunderscorevertical",
"wawarabic",
"wawfinalarabic",
"wawhamzaabovearabic",
"wawhamzaabovefinalarabic",
"wbsquare",
"wcircle",
"wcircumflex",
"wdieresis",
"wdotaccent",
"wdotbelow",
"wehiragana",
"weierstrass",
"wekatakana",
"wekorean",
"weokorean",
"wgrave",
"whitebullet",
"whitecircle",
"whitecircleinverse",
"whitecornerbracketleft",
"whitecornerbracketleftvertical",
"whitecornerbracketright",
"whitecornerbracketrightvertical",
"whitediamond",
"whitediamondcontainingblacksmalldiamond",
"whitedownpointingsmalltriangle",
"whitedownpointingtriangle",
"whiteleftpointingsmalltriangle",
"whiteleftpointingtriangle",
"whitelenticularbracketleft",
"whitelenticularbracketright",
"whiterightpointingsmalltriangle",
"whiterightpointingtriangle",
"whitesmallsquare",
"whitesmilingface",
"whitesquare",
"whitestar",
"whitetelephone",
"whitetortoiseshellbracketleft",
"whitetortoiseshellbracketright",
"whiteuppointingsmalltriangle",
"whiteuppointingtriangle",
"wihiragana",
"wikatakana",
"wikorean",
"wmonospace",
"wohiragana",
"wokatakana",
"wokatakanahalfwidth",
"won",
"wonmonospace",
"wowaenthai",
"wparen",
"wreathproduct",
"wring",
"wsuperior",
"wturned",
"wynn",
"x",
"xabovecmb",
"xbopomofo",
"xcircle",
"xdieresis",
"xdotaccent",
"xeharmenian",
"xi",
"xmonospace",
"xparen",
"xsuperior",
"y",
"yaadosquare",
"yabengali",
"yacute",
"yadeva",
"yaekorean",
"yagujarati",
"yagurmukhi",
"yahiragana",
"yakatakana",
"yakatakanahalfwidth",
"yakorean",
"yamakkanthai",
"yasmallhiragana",
"yasmallkatakana",
"yasmallkatakanahalfwidth",
"yatcyrillic",
"ycircle",
"ycircumflex",
"ydieresis",
"ydotaccent",
"ydotbelow",
"yeharabic",
"yehbarreearabic",
"yehbarreefinalarabic",
"yehfinalarabic",
"yehhamzaabovearabic",
"yehhamzaabovefinalarabic",
"yehhamzaaboveinitialarabic",
"yehhamzaabovemedialarabic",
"yehinitialarabic",
"yehmedialarabic",
"yehmeeminitialarabic",
"yehmeemisolatedarabic",
"yehnoonfinalarabic",
"yehthreedotsbelowarabic",
"yekorean",
"yen",
"yenmonospace",
"yeokorean",
"yeorinhieuhkorean",
"yerahbenyomohebrew",
"yerahbenyomolefthebrew",
"yericyrillic",
"yerudieresiscyrillic",
"yesieungkorean",
"yesieungpansioskorean",
"yesieungsioskorean",
"yetivhebrew",
"ygrave",
"yhook",
"yhookabove",
"yiarmenian",
"yicyrillic",
"yikorean",
"yinyang",
"yiwnarmenian",
"ymonospace",
"yod",
"yoddagesh",
"yoddageshhebrew",
"yodhebrew",
"yodyodhebrew",
"yodyodpatahhebrew",
"yohiragana",
"yoikorean",
"yokatakana",
"yokatakanahalfwidth",
"yokorean",
"yosmallhiragana",
"yosmallkatakana",
"yosmallkatakanahalfwidth",
"yotgreek",
"yoyaekorean",
"yoyakorean",
"yoyakthai",
"yoyingthai",
"yparen",
"ypogegrammeni",
"ypogegrammenigreekcmb",
"yr",
"yring",
"ysuperior",
"ytilde",
"yturned",
"yuhiragana",
"yuikorean",
"yukatakana",
"yukatakanahalfwidth",
"yukorean",
"yusbigcyrillic",
"yusbigiotifiedcyrillic",
"yuslittlecyrillic",
"yuslittleiotifiedcyrillic",
"yusmallhiragana",
"yusmallkatakana",
"yusmallkatakanahalfwidth",
"yuyekorean",
"yuyeokorean",
"yyabengali",
"yyadeva",
"z",
"zaarmenian",
"zacute",
"zadeva",
"zagurmukhi",
"zaharabic",
"zahfinalarabic",
"zahinitialarabic",
"zahiragana",
"zahmedialarabic",
"zainarabic",
"zainfinalarabic",
"zakatakana",
"zaqefgadolhebrew",
"zaqefqatanhebrew",
"zarqahebrew",
"zayin",
"zayindagesh",
"zayindageshhebrew",
"zayinhebrew",
"zbopomofo",
"zcaron",
"zcircle",
"zcircumflex",
"zcurl",
"zdot",
"zdotaccent",
"zdotbelow",
"zecyrillic",
"zedescendercyrillic",
"zedieresiscyrillic",
"zehiragana",
"zekatakana",
"zero",
"zeroarabic",
"zerobengali",
"zerodeva",
"zerogujarati",
"zerogurmukhi",
"zerohackarabic",
"zeroinferior",
"zeromonospace",
"zerooldstyle",
"zeropersian",
"zerosuperior",
"zerothai",
"zerowidthjoiner",
"zerowidthnonjoiner",
"zerowidthspace",
"zeta",
"zhbopomofo",
"zhearmenian",
"zhebrevecyrillic",
"zhecyrillic",
"zhedescendercyrillic",
"zhedieresiscyrillic",
"zihiragana",
"zikatakana",
"zinorhebrew",
"zlinebelow",
"zmonospace",
"zohiragana",
"zokatakana",
"zparen",
"zretroflexhook",
"zstroke",
"zuhiragana",
"zukatakana",
};

static const unsigned short single_code_list[] = {
0x0041,
0x00c6,
0x01fc,
0x01e2,
0xf7e6,
0x00c1,
0xf7e1,
0x0102,
0x1eae,
0x04d0,
0x1eb6,
0x1eb0,
0x1eb2,
0x1eb4,
0x01cd,
0x24b6,
0x00c2,
0x1ea4,
0x1eac,
0x1ea6,
0x1ea8,
0xf7e2,
0x1eaa,
0xf6c9,
0xf7b4,
0x0410,
0x0200,
0x00c4,
0x04d2,
0x01de,
0xf7e4,
0x1ea0,
0x01e0,
0x00c0,
0xf7e0,
0x1ea2,
0x04d4,
0x0202,
0x0391,
0x0386,
0x0100,
0xff21,
0x0104,
0x00c5,
0x01fa,
0x1e00,
0xf7e5,
0xf761,
0x00c3,
0xf7e3,
0x0531,
0x0042,
0x24b7,
0x1e02,
0x1e04,
0x0411,
0x0532,
0x0392,
0x0181,
0x1e06,
0xff22,
0xf6f4,
0xf762,
0x0182,
0x0043,
0x053e,
0x0106,
0xf6ca,
0xf6f5,
0x010c,
0x00c7,
0x1e08,
0xf7e7,
0x24b8,
0x0108,
0x010a,
0x010a,
0xf7b8,
0x0549,
0x04bc,
0x0427,
0x04be,
0x04b6,
0x04f4,
0x0543,
0x04cb,
0x04b8,
0x03a7,
0x0187,
0xf6f6,
0xff23,
0x0551,
0xf763,
0x0044,
0x01f1,
0x01c4,
0x0534,
0x0189,
0x0110,
0x010e,
0x1e10,
0x24b9,
0x1e12,
0x0110,
0x1e0a,
0x1e0c,
0x0414,
0x03ee,
0x2206,
0x0394,
0x018a,
0xf6cb,
0xf6cc,
0xf6cd,
0xf7a8,
0x03dd,
0x03dc,
0x0402,
0x1e0e,
0xff24,
0xf6f7,
0x0110,
0xf764,
0x018b,
0x01f2,
0x01c5,
0x04e0,
0x0405,
0x040f,
0x0045,
0x00c9,
0xf7e9,
0x0114,
0x011a,
0x1e1c,
0x0535,
0x24ba,
0x00ca,
0x1ebe,
0x1e18,
0x1ec6,
0x1ec0,
0x1ec2,
0xf7ea,
0x1ec4,
0x0404,
0x0204,
0x00cb,
0xf7eb,
0x0116,
0x0116,
0x1eb8,
0x0424,
0x00c8,
0xf7e8,
0x0537,
0x1eba,
0x2167,
0x0206,
0x0464,
0x041b,
0x216a,
0x0112,
0x1e16,
0x1e14,
0x041c,
0xff25,
0x041d,
0x04a2,
0x014a,
0x04a4,
0x04c7,
0x0118,
0x0190,
0x0395,
0x0388,
0x0420,
0x018e,
0x042d,
0x0421,
0x04aa,
0x01a9,
0xf765,
0x0397,
0x0538,
0x0389,
0x00d0,
0xf7f0,
0x1ebc,
0x1e1a,
0x20ac,
0x01b7,
0x01ee,
0x01b8,
0x0046,
0x24bb,
0x1e1e,
0x0556,
0x03e4,
0x0191,
0x2132,
0x0472,
0x2164,
0xff26,
0x2163,
0xf766,
0x0047,
0x3387,
0x01f4,
0x0393,
0x0194,
0x03ea,
0x011e,
0x01e6,
0x0122,
0x24bc,
0x011c,
0x0122,
0x0120,
0x0120,
0x0413,
0x0542,
0x0494,
0x0492,
0x0490,
0x0193,
0x0533,
0x0403,
0x1e20,
0x2141,
0xff27,
0xf6ce,
0xf760,
0xf767,
0x029b,
0x01e4,
0x0048,
0x25cf,
0x25aa,
0x25ab,
0x25a1,
0x33cb,
0x04a8,
0x04b2,
0x042a,
0x0126,
0x1e2a,
0x1e28,
0x24bd,
0x0124,
0x1e26,
0x1e22,
0x1e24,
0xff28,
0x0540,
0x03e8,
0xf768,
0xf6cf,
0xf6f8,
0x3390,
0x0049,
0x042f,
0x0132,
0x042e,
0x00cd,
0xf7ed,
0x012c,
0x01cf,
0x24be,
0x00ce,
0xf7ee,
0x0406,
0x0208,
0x00cf,
0x1e2e,
0x04e4,
0xf7ef,
0x0130,
0x0130,
0x1eca,
0x04d6,
0x0415,
0x2111,
0x2111,
0x00cc,
0xf7ec,
0x1ec8,
0x0418,
0x020a,
0x0419,
0x012a,
0x04e2,
0xff29,
0x053b,
0x0401,
0x012e,
0x0399,
0x0196,
0x03aa,
0x038a,
0xf769,
0x0197,
0x0128,
0x1e2c,
0x0474,
0x0476,
0x004a,
0x0541,
0x24bf,
0x0134,
0x0408,
0x054b,
0xff2a,
0xf76a,
0x004b,
0x3385,
0x33cd,
0x04a0,
0x1e30,
0x041a,
0x049a,
0x04c3,
0x039a,
0x049e,
0x049c,
0x01e8,
0x0136,
0x24c0,
0x0136,
0x1e32,
0x0554,
0x053f,
0x0425,
0x03e6,
0x0198,
0x040c,
0x1e34,
0xff2b,
0x0480,
0x03de,
0x046e,
0xf76b,
0x004c,
0x01c7,
0xf6bf,
0x0139,
0x039b,
0x013d,
0x013b,
0x24c1,
0x1e3c,
0x013b,
0x013f,
0x013f,
0x1e36,
0x1e38,
0x053c,
0x01c8,
0x0409,
0x1e3a,
0xff2c,
0x0141,
0xf6f9,
0xf76c,
0x004d,
0x3386,
0xf6d0,
0xf7af,
0x1e3e,
0x24c2,
0x1e40,
0x1e42,
0x0544,
0xff2d,
0xf76d,
0x019c,
0x039c,
0x004e,
0x01ca,
0x0143,
0x0147,
0x0145,
0x24c3,
0x1e4a,
0x0145,
0x1e44,
0x1e46,
0x014a,
0x019d,
0x2168,
0x01cb,
0x040a,
0x1e48,
0xff2e,
0x0546,
0xf76e,
0x00d1,
0xf7f1,
0x039d,
0x004f,
0x0152,
0xf6fa,
0x00d3,
0xf7f3,
0x04e8,
0x04ea,
0x014e,
0x01d1,
0x019f,
0x24c4,
0x00d4,
0x1ed0,
0x1ed8,
0x1ed2,
0x1ed4,
0xf7f4,
0x1ed6,
0x041e,
0x0150,
0x020c,
0x00d6,
0x04e6,
0xf7f6,
0x1ecc,
0xf6fb,
0x00d2,
0xf7f2,
0x0555,
0x2126,
0x1ece,
0x01a0,
0x1eda,
0x1ee2,
0x1edc,
0x1ede,
0x1ee0,
0x0150,
0x01a2,
0x020e,
0x014c,
0x1e52,
0x1e50,
0x2126,
0x0460,
0x03a9,
0x2127,
0x047a,
0x047c,
0x038f,
0x039f,
0x038c,
0xff2f,
0x2160,
0x01ea,
0x01ec,
0x0186,
0x00d8,
0x01fe,
0xf7f8,
0xf76f,
0x01fe,
0x047e,
0x00d5,
0x1e4c,
0x1e4e,
0xf7f5,
0x0050,
0x1e54,
0x24c5,
0x1e56,
0x041f,
0x054a,
0x04a6,
0x03a6,
0x01a4,
0x03a0,
0x0553,
0xff30,
0x03a8,
0x0470,
0xf770,
0x0051,
0x24c6,
0xff31,
0xf771,
0x0052,
0x054c,
0x0154,
0x0158,
0x0156,
0x24c7,
0x0156,
0x0210,
0x1e58,
0x1e5a,
0x1e5c,
0x0550,
0x211c,
0x211c,
0x03a1,
0xf6fc,
0x0212,
0x1e5e,
0xff32,
0xf772,
0x0281,
0x02b6,
0x0053,
0x250c,
0x2514,
0x2510,
0x2518,
0x253c,
0x252c,
0x2534,
0x251c,
0x2524,
0x2500,
0x2502,
0x2561,
0x2562,
0x2556,
0x2555,
0x2563,
0x2551,
0x2557,
0x255d,
0x255c,
0x255b,
0x255e,
0x255f,
0x255a,
0x2554,
0x2569,
0x2566,
0x2560,
0x2550,
0x256c,
0x2567,
0x2568,
0x2564,
0x2565,
0x2559,
0x2558,
0x2552,
0x2553,
0x256b,
0x256a,
0x015a,
0x1e64,
0x03e0,
0x0160,
0x1e66,
0xf6fd,
0x015e,
0x018f,
0x04d8,
0x04da,
0x24c8,
0x015c,
0x0218,
0x1e60,
0x1e62,
0x1e68,
0x054d,
0x2166,
0x0547,
0x0428,
0x0429,
0x03e2,
0x04ba,
0x03ec,
0x03a3,
0x2165,
0xff33,
0x042c,
0xf773,
0x03da,
0x0054,
0x03a4,
0x0166,
0x0164,
0x0162,
0x24c9,
0x1e70,
0x0162,
0x1e6a,
0x1e6c,
0x0422,
0x04ac,
0x2169,
0x04b4,
0x0398,
0x01ac,
0x00de,
0xf7fe,
0x2162,
0xf6fe,
0x054f,
0x1e6e,
0xff34,
0x0539,
0x01bc,
0x0184,
0x01a7,
0x01ae,
0x0426,
0x040b,
0xf774,
0x216b,
0x2161,
0x0055,
0x00da,
0xf7fa,
0x016c,
0x01d3,
0x24ca,
0x00db,
0x1e76,
0xf7fb,
0x0423,
0x0170,
0x0214,
0x00dc,
0x01d7,
0x1e72,
0x01d9,
0x04f0,
0x01db,
0x01d5,
0xf7fc,
0x1ee4,
0x00d9,
0xf7f9,
0x1ee6,
0x01af,
0x1ee8,
0x1ef0,
0x1eea,
0x1eec,
0x1eee,
0x0170,
0x04f2,
0x0216,
0x0478,
0x016a,
0x04ee,
0x1e7a,
0xff35,
0x0172,
0x03a5,
0x03d2,
0x03d3,
0x01b1,
0x03ab,
0x03d4,
0x03d2,
0x038e,
0x016e,
0x040e,
0xf775,
0x04ae,
0x04b0,
0x0168,
0x1e78,
0x1e74,
0x0056,
0x24cb,
0x1e7e,
0x0412,
0x054e,
0x01b2,
0xff36,
0x0548,
0xf776,
0x1e7c,
0x0057,
0x1e82,
0x24cc,
0x0174,
0x1e84,
0x1e86,
0x1e88,
0x1e80,
0xff37,
0xf777,
0x0058,
0x24cd,
0x1e8c,
0x1e8a,
0x053d,
0x039e,
0xff38,
0xf778,
0x0059,
0x00dd,
0xf7fd,
0x0462,
0x24ce,
0x0176,
0x0178,
0xf7ff,
0x1e8e,
0x1ef4,
0x00a5,
0x042b,
0x04f8,
0x1ef2,
0x01b3,
0x1ef6,
0x0545,
0x0407,
0x0552,
0xff39,
0xf779,
0x1ef8,
0x046a,
0x046c,
0x0466,
0x0468,
0x005a,
0x0536,
0x0179,
0x017d,
0xf6ff,
0x24cf,
0x1e90,
0x017b,
0x017b,
0x1e92,
0x0417,
0x0498,
0x04de,
0x0396,
0x053a,
0x04c1,
0x0416,
0x0496,
0x04dc,
0x1e94,
0xff3a,
0xf77a,
0x01b5,
0x0061,
0x0986,
0x00e1,
0x0906,
0x0a86,
0x0a06,
0x0a3e,
0x3303,
0x09be,
0x093e,
0x0abe,
0x055f,
0x0970,
0x0985,
0x311a,
0x0103,
0x1eaf,
0x04d1,
0x1eb7,
0x1eb1,
0x1eb3,
0x1eb5,
0x01ce,
0x24d0,
0x00e2,
0x1ea5,
0x1ead,
0x1ea7,
0x1ea9,
0x1eab,
0x00b4,
0x0317,
0x0301,
0x0301,
0x0954,
0x02cf,
0x0341,
0x0430,
0x0201,
0x0a71,
0x0905,
0x00e4,
0x04d3,
0x01df,
0x1ea1,
0x01e1,
0x00e6,
0x01fd,
0x3150,
0x01e3,
0x2015,
0x20a4,
0x0410,
0x0411,
0x0412,
0x0413,
0x0414,
0x0415,
0x0401,
0x0416,
0x0417,
0x0418,
0x0419,
0x041a,
0x041b,
0x041c,
0x041d,
0x041e,
0x041f,
0x0420,
0x0421,
0x0422,
0x0423,
0x0424,
0x0425,
0x0426,
0x0427,
0x0428,
0x0429,
0x042a,
0x042b,
0x042c,
0x042d,
0x042e,
0x042f,
0x0490,
0x0402,
0x0403,
0x0404,
0x0405,
0x0406,
0x0407,
0x0408,
0x0409,
0x040a,
0x040b,
0x040c,
0x040e,
0xf6c4,
0xf6c5,
0x0430,
0x0431,
0x0432,
0x0433,
0x0434,
0x0435,
0x0451,
0x0436,
0x0437,
0x0438,
0x0439,
0x043a,
0x043b,
0x043c,
0x043d,
0x043e,
0x043f,
0x0440,
0x0441,
0x0442,
0x0443,
0x0444,
0x0445,
0x0446,
0x0447,
0x0448,
0x0449,
0x044a,
0x044b,
0x044c,
0x044d,
0x044e,
0x044f,
0x0491,
0x0452,
0x0453,
0x0454,
0x0455,
0x0456,
0x0457,
0x0458,
0x0459,
0x045a,
0x045b,
0x045c,
0x045e,
0x040f,
0x0462,
0x0472,
0x0474,
0xf6c6,
0x045f,
0x0463,
0x0473,
0x0475,
0xf6c7,
0xf6c8,
0x04d9,
0x200e,
0x200f,
0x200d,
0x066a,
0x060c,
0x0660,
0x0661,
0x0662,
0x0663,
0x0664,
0x0665,
0x0666,
0x0667,
0x0668,
0x0669,
0x061b,
0x061f,
0x0621,
0x0622,
0x0623,
0x0624,
0x0625,
0x0626,
0x0627,
0x0628,
0x0629,
0x062a,
0x062b,
0x062c,
0x062d,
0x062e,
0x062f,
0x0630,
0x0631,
0x0632,
0x0633,
0x0634,
0x0635,
0x0636,
0x0637,
0x0638,
0x0639,
0x063a,
0x0640,
0x0641,
0x0642,
0x0643,
0x0644,
0x0645,
0x0646,
0x0648,
0x0649,
0x064a,
0x064b,
0x064c,
0x064d,
0x064e,
0x064f,
0x0650,
0x0651,
0x0652,
0x0647,
0x06a4,
0x067e,
0x0686,
0x0698,
0x06af,
0x0679,
0x0688,
0x0691,
0x06ba,
0x06d2,
0x06d5,
0x20aa,
0x05be,
0x05c3,
0x05d0,
0x05d1,
0x05d2,
0x05d3,
0x05d4,
0x05d5,
0x05d6,
0x05d7,
0x05d8,
0x05d9,
0x05da,
0x05db,
0x05dc,
0x05dd,
0x05de,
0x05df,
0x05e0,
0x05e1,
0x05e2,
0x05e3,
0x05e4,
0x05e5,
0x05e6,
0x05e7,
0x05e8,
0x05e9,
0x05ea,
0xfb2a,
0xfb2b,
0xfb4b,
0xfb1f,
0x05f0,
0x05f1,
0x05f2,
0xfb35,
0x05b4,
0x05b5,
0x05b6,
0x05bb,
0x05b8,
0x05b7,
0x05b0,
0x05b2,
0x05b1,
0x05b3,
0x05c2,
0x05c1,
0x05b9,
0x05bc,
0x05bd,
0x05bf,
0x05c0,
0x02bc,
0x2105,
0x2113,
0x2116,
0x202c,
0x202d,
0x202e,
0x200c,
0x066d,
0x02bd,
0x00e0,
0x0a85,
0x0a05,
0x3042,
0x1ea3,
0x0990,
0x311e,
0x0910,
0x04d5,
0x0a90,
0x0a10,
0x0a48,
0x0639,
0xfeca,
0xfecb,
0xfecc,
0x0203,
0x09c8,
0x0948,
0x0ac8,
0x30a2,
0xff71,
0x314f,
0x05d0,
0x0627,
0xfb30,
0xfe8e,
0x0623,
0xfe84,
0x0625,
0xfe88,
0x05d0,
0xfb4f,
0x0622,
0xfe82,
0x0649,
0xfef0,
0xfef3,
0xfef4,
0xfb2e,
0xfb2f,
0x2135,
0x224c,
0x03b1,
0x03ac,
0xd802,
0x0101,
0xff41,
0x0026,
0xff06,
0xf726,
0x33c2,
0x3122,
0x3124,
0x27e8,
0x27e9,
0x0e5a,
0x2220,
0x3008,
0xfe3f,
0x3009,
0xfe40,
0x2329,
0x232a,
0x212b,
0x0387,
0x27f2,
0x0952,
0x0982,
0x0902,
0x0a82,
0x0105,
0x3300,
0x249c,
0x055a,
0x02bc,
0xf8ff,
0x2250,
0x2248,
0x2252,
0x2245,
0x224a,
0x318e,
0x318d,
0x2312,
0x21b6,
0x21b7,
0x1e9a,
0x00e5,
0x01fb,
0x1e01,
0x2194,
0x2195,
0x21e3,
0x21e0,
0x21e2,
0x21e1,
0x21d4,
0x21d5,
0x21d3,
0x21d0,
0x21d2,
0x21d1,
0x2193,
0x2199,
0x2198,
0x21e9,
0x02c5,
0x02c2,
0x02c3,
0x02c4,
0xf8e7,
0x2190,
0x21bd,
0x21d0,
0x21cd,
0x21c6,
0x21bc,
0x21e6,
0x2197,
0x2196,
0x21c6,
0x21c4,
0x2192,
0x21c1,
0x21cf,
0x279e,
0x21c4,
0x21c0,
0x21e8,
0x2198,
0x2199,
0x21e4,
0x21e5,
0x21a2,
0x21a3,
0x21da,
0x21db,
0x2191,
0x2195,
0x21a8,
0x21a8,
0x2196,
0x21c5,
0x2197,
0x21e7,
0xf8e6,
0xd80a,
0x005e,
0xff3e,
0x007e,
0xff5e,
0x0251,
0x0252,
0x3041,
0x30a1,
0xff67,
0x002a,
0x066d,
0x066d,
0x2217,
0x2217,
0xff0a,
0xfe61,
0x2042,
0xf6e9,
0x2243,
0x0040,
0x00e3,
0xff20,
0xfe6b,
0x0250,
0x0994,
0x3120,
0x0914,
0x0a94,
0x0a14,
0x09d7,
0x0a4c,
0x09cc,
0x094c,
0x0acc,
0x093d,
0x0561,
0x05e2,
0xfb20,
0x05e2,
0x0062,
0x09ac,
0x005c,
0xff3c,
0x092c,
0x0aac,
0x0a2c,
0x3070,
0x0e3f,
0x30d0,
0x007c,
0x2225,
0xff5c,
0x3105,
0x24d1,
0x1e03,
0x1e05,
0x266c,
0x2235,
0x0431,
0x0628,
0xfe90,
0xfe91,
0x3079,
0xfe92,
0xfc9f,
0xfc08,
0xfc6d,
0x30d9,
0x0562,
0x05d1,
0x03b2,
0x03d0,
0xfb31,
0xfb31,
0x2136,
0x05d1,
0xfb4c,
0x226c,
0x09ad,
0x092d,
0x0aad,
0x0a2d,
0x0253,
0x3073,
0x30d3,
0x0298,
0x0a02,
0x3331,
0x25cf,
0x25c6,
0x25bc,
0x25c4,
0x25c0,
0x3010,
0xfe3b,
0x3011,
0xfe3c,
0x25e3,
0x25e2,
0x25ac,
0x25ba,
0x25b6,
0x25aa,
0x263b,
0x25a0,
0x2605,
0x25e4,
0x25e5,
0x25b4,
0x25b2,
0x2423,
0x1e07,
0x2588,
0xff42,
0x0e1a,
0x307c,
0x30dc,
0x249d,
0x33c3,
0xf8f4,
0x007b,
0xf8f3,
0xf8f2,
0xff5b,
0xfe5b,
0xf8f1,
0xfe37,
0x007d,
0xf8fe,
0xf8fd,
0xff5d,
0xfe5c,
0xf8fc,
0xfe38,
0x005b,
0xf8f0,
0xf8ef,
0xff3b,
0xf8ee,
0x005d,
0xf8fb,
0xf8fa,
0xff3d,
0xf8f9,
0x02d8,
0x032e,
0x0306,
0x032f,
0x0311,
0x0361,
0x032a,
0x033a,
0x00a6,
0x0180,
0xf6ea,
0x0183,
0x3076,
0x30d6,
0x2022,
0x25d8,
0x2219,
0x25ce,
0x0063,
0x056e,
0x099a,
0x0107,
0x091a,
0x0a9a,
0x0a1a,
0x3388,
0x0981,
0x0310,
0x0901,
0x0a81,
0xd809,
0x21ea,
0x2105,
0x02c7,
0x032c,
0x030c,
0x21b5,
0x3118,
0x010d,
0x00e7,
0x1e09,
0x24d2,
0x0109,
0x0255,
0x010b,
0x010b,
0x33c5,
0x00b8,
0x0327,
0x2308,
0x2309,
0x00a2,
0x2103,
0xf6df,
0xffe0,
0xf7a2,
0xf6e0,
0x0579,
0x099b,
0x091b,
0x0a9b,
0x0a1b,
0x3114,
0x04bd,
0x2713,
0x2713,
0x0447,
0x04bf,
0x04b7,
0x04f5,
0x0573,
0x04cc,
0x04b9,
0x03c7,
0x3277,
0x3217,
0x3269,
0x314a,
0x3209,
0x0e0a,
0x0e08,
0x0e09,
0x0e0c,
0x0188,
0x3276,
0x3216,
0x3268,
0x3148,
0x3208,
0x321c,
0x25cb,
0x00ae,
0x24c8,
0x229b,
0x20dd,
0x2298,
0x2299,
0x229c,
0x2296,
0x2297,
0x2299,
0x2295,
0x3036,
0x229a,
0x25d0,
0x25d1,
0x02c6,
0x032d,
0x0302,
0x2327,
0x01c2,
0x01c0,
0x01c1,
0x01c3,
0x27f3,
0x2663,
0x2663,
0x2667,
0x33a4,
0xff43,
0x33a0,
0x0581,
0x003a,
0x20a1,
0xff1a,
0x20a1,
0xfe55,
0x02d1,
0x02d0,
0x002c,
0x0313,
0x0315,
0xf6c3,
0x060c,
0x055d,
0xf6e1,
0xff0c,
0x0314,
0x02bd,
0xfe50,
0xf6e2,
0x0312,
0x02bb,
0x263c,
0x2201,
0x200c,
0x2245,
0x222e,
0x2303,
0x0006,
0x0007,
0x0008,
0x0018,
0x000d,
0x0011,
0x0012,
0x0013,
0x0014,
0x007f,
0x0010,
0x0019,
0x0005,
0x0004,
0x001b,
0x0017,
0x0003,
0x000c,
0x001c,
0x001d,
0x0009,
0x000a,
0x0015,
0x001e,
0x000f,
0x000e,
0x0002,
0x0001,
0x001a,
0x0016,
0x001f,
0x000b,
0x2a3f,
0x00a9,
0xf8e9,
0xf6d9,
0x300c,
0xff62,
0xfe41,
0x300d,
0xff63,
0xfe42,
0x337f,
0x33c7,
0x33c6,
0x249e,
0x20a2,
0x0297,
0x22cf,
0x21ab,
0x22ce,
0x21ac,
0x00a4,
0x200c,
0xf6d1,
0xf6d2,
0xf6d4,
0xf6d5,
0x0064,
0x0564,
0x09a6,
0x0636,
0x0926,
0xfebe,
0xfebf,
0xfec0,
0x05bc,
0x05bc,
0x2020,
0x2021,
0x0aa6,
0x0a26,
0x3060,
0x30c0,
0x062f,
0x05d3,
0xfb33,
0xfb33,
0x2138,
0x05d3,
0xfeaa,
0x064f,
0x064f,
0x064c,
0x064c,
0x0964,
0x05a7,
0x05a7,
0x0485,
0x0111,
0xf6d3,
0x300a,
0xfe3d,
0x300b,
0xfe3e,
0x032b,
0x21ca,
0x219e,
0x21a0,
0x21d4,
0x21d2,
0x21c8,
0x27e6,
0x27e7,
0x0965,
0xf6d6,
0x030f,
0x222c,
0x2017,
0x0333,
0x033f,
0x02ba,
0x2016,
0x030e,
0x3109,
0x33c8,
0x010f,
0x1e11,
0x24d3,
0x1e13,
0x0111,
0x09a1,
0x0921,
0x0aa1,
0x0a21,
0x0688,
0xfb89,
0x095c,
0x09a2,
0x0922,
0x0aa2,
0x0a22,
0x1e0b,
0x1e0d,
0x066b,
0x066b,
0x0434,
0x225c,
0x00b0,
0x05ad,
0x3067,
0x03ef,
0x30c7,
0x232b,
0x2326,
0x03b4,
0x018d,
0x09f8,
0x02a4,
0x09a7,
0x0927,
0x0aa7,
0x0a27,
0x0257,
0x0385,
0x0344,
0x2666,
0x22c4,
0x2666,
0x2662,
0x00a8,
0xf6d7,
0x0324,
0x0308,
0xf6d8,
0x0385,
0x224f,
0x3062,
0x30c2,
0x3003,
0x00f7,
0x22c7,
0x2223,
0x2215,
0x0452,
0x2593,
0x1e0f,
0x3397,
0x0111,
0xff44,
0x2584,
0x0e0e,
0x0e14,
0x3069,
0x30c9,
0x0024,
0xf6e3,
0xff04,
0xf724,
0xfe69,
0xf6e4,
0x20ab,
0x3326,
0x02d9,
0x0307,
0x0323,
0x0323,
0x30fb,
0x0131,
0xf6be,
0x0284,
0x22c5,
0x2214,
0x25cc,
0xfb1f,
0xfb1f,
0x22ce,
0x29f9,
0x031e,
0x02d5,
0x249f,
0xf6eb,
0x0256,
0x018c,
0x3065,
0x30c5,
0x01f3,
0x02a3,
0x01c6,
0x02a5,
0x04e1,
0x0455,
0x045f,
0x0065,
0x00e9,
0x2641,
0x098f,
0x311c,
0x0115,
0x090d,
0x0a8d,
0x0945,
0x0ac5,
0x011b,
0x1e1d,
0x0565,
0x0587,
0x24d4,
0x00ea,
0x1ebf,
0x1e19,
0x1ec7,
0x1ec1,
0x1ec3,
0x1ec5,
0x0454,
0x0205,
0x090f,
0x00eb,
0x0117,
0x0117,
0x1eb9,
0x0a0f,
0x0a47,
0x0444,
0x00e8,
0x0a8f,
0x0567,
0x311d,
0x3048,
0x1ebb,
0x311f,
0x0038,
0x0668,
0x09ee,
0x2467,
0x2791,
0x096e,
0x2471,
0x2485,
0x2499,
0x0aee,
0x0a6e,
0x0668,
0x3028,
0x266b,
0x3227,
0x2088,
0xff18,
0xf738,
0x247b,
0x248f,
0x06f8,
0x2177,
0x2078,
0x0e58,
0x0207,
0x0465,
0x30a8,
0xff74,
0x0a74,
0x3154,
0x043b,
0x2208,
0x246a,
0x247e,
0x2492,
0x217a,
0x2026,
0x22ee,
0x0113,
0x1e17,
0x1e15,
0x043c,
0x2014,
0xfe31,
0xff45,
0x055b,
0x2205,
0xd801,
0x3123,
0x043d,
0x2013,
0xfe32,
0x04a3,
0x014b,
0x3125,
0x04a5,
0x04c8,
0x2002,
0x0119,
0x3153,
0x025b,
0x029a,
0x025c,
0x025e,
0x025d,
0x24a0,
0x03b5,
0x03f5,
0x03f6,
0x03ad,
0x003d,
0x2252,
0x2253,
0xff1d,
0x22df,
0x2a96,
0x2a95,
0x22de,
0x2242,
0x2251,
0xfe66,
0x207c,
0x2261,
0x224d,
0x3126,
0x0440,
0x0258,
0x044d,
0x0441,
0x04ab,
0x0283,
0x0286,
0x090e,
0x0946,
0x01aa,
0x0285,
0x3047,
0x30a7,
0xff6a,
0x212e,
0xf6ec,
0x03b7,
0x0568,
0x03ae,
0x00f0,
0x1ebd,
0x1e1b,
0x0591,
0x0591,
0x0591,
0x0591,
0x01dd,
0x3161,
0x20ac,
0x09c7,
0x0947,
0x0ac7,
0x0021,
0x055c,
0x203c,
0x00a1,
0xf7a1,
0xff01,
0xf721,
0x2203,
0x0292,
0x01ef,
0x0293,
0x01b9,
0x01ba,
0x0066,
0x095e,
0x0a5e,
0x2109,
0x064e,
0x064e,
0x064b,
0x3108,
0x24d5,
0x1e1f,
0x0641,
0x0586,
0xfed2,
0xfed3,
0xfed4,
0x03e5,
0x2640,
0xfb00,
0xfb03,
0xfb04,
0xfb01,
0x246e,
0x2482,
0x2496,
0x2012,
0x25a0,
0x25ac,
0x05da,
0xfb3a,
0xfb3a,
0x05da,
0x05dd,
0x05dd,
0x05df,
0x05df,
0x05e3,
0x05e3,
0x05e5,
0x05e5,
0x02c9,
0x25c9,
0x0473,
0x0035,
0x0665,
0x09eb,
0x2464,
0x278e,
0x096b,
0x215d,
0x0aeb,
0x0a6b,
0x0665,
0x3025,
0x3224,
0x2085,
0xff15,
0xf735,
0x2478,
0x248c,
0x06f5,
0x2174,
0x2075,
0x0e55,
0xfb02,
0x266d,
0x230a,
0x230b,
0x0192,
0xff46,
0x3399,
0x0e1f,
0x0e1d,
0x2aba,
0x2ab6,
0x22e9,
0x227b,
0x2ab0,
0x227d,
0x227f,
0x0e4f,
0x2200,
0x22a9,
0x22aa,
0x22d4,
0x0034,
0x0664,
0x09ea,
0x2463,
0x278d,
0x096a,
0x0aea,
0x0a6a,
0x0664,
0x3024,
0x3223,
0x2084,
0xff14,
0x09f7,
0xf734,
0x2477,
0x248b,
0x06f4,
0x2173,
0x2074,
0x246d,
0x2481,
0x2495,
0x0e54,
0x02cb,
0x24a1,
0x2044,
0x20a3,
0x2322,
0x0067,
0x0997,
0x01f5,
0x0917,
0x06af,
0xfb93,
0xfb94,
0xfb95,
0x0a97,
0x0a17,
0x304c,
0x30ac,
0x03b3,
0x0263,
0x02e0,
0x03eb,
0x310d,
0x011f,
0x01e7,
0x0123,
0x24d6,
0x011d,
0x0123,
0x0121,
0x0121,
0x0433,
0x3052,
0x30b2,
0x224e,
0x2251,
0x059c,
0x05f3,
0x059d,
0x00df,
0x059e,
0x05f4,
0x3013,
0x0998,
0x0572,
0x0918,
0x0a98,
0x0a18,
0x063a,
0xfece,
0xfecf,
0xfed0,
0x0495,
0x0493,
0x0491,
0x095a,
0x0a5a,
0x0260,
0x3393,
0x304e,
0x30ae,
0x0563,
0x05d2,
0xfb32,
0xfb32,
0x05d2,
0x0453,
0x01be,
0x0294,
0x0296,
0x02c0,
0x0295,
0x02c1,
0x02e4,
0x02a1,
0x02a2,
0x1e21,
0xff47,
0x3054,
0x30b4,
0x24a2,
0x33ac,
0x2207,
0x0060,
0x0316,
0x0300,
0x0300,
0x0953,
0x02ce,
0xff40,
0x0340,
0x003e,
0x2a8c,
0x2267,
0x22d7,
0x2265,
0x22db,
0x22db,
0xff1e,
0x226b,
0x2a8a,
0x2a88,
0x2a86,
0x2a7e,
0x2273,
0x2277,
0x2269,
0x2269,
0x2273,
0x2267,
0xfe65,
0x0261,
0x01e5,
0x3050,
0x00ab,
0x00bb,
0x2039,
0x203a,
0x30b0,
0x3318,
0x33c9,
0x0068,
0x04a9,
0x06c1,
0x09b9,
0x04b3,
0x0939,
0x0ab9,
0x0a39,
0x062d,
0xfea2,
0xfea3,
0x306f,
0xfea4,
0x332a,
0x30cf,
0xff8a,
0x0a4d,
0x0621,
0x0621,
0x3164,
0x044a,
0x21c3,
0x21c2,
0x21bc,
0x21cc,
0x21c0,
0x21cb,
0x21bf,
0x21be,
0x33ca,
0x05b2,
0x05b2,
0x05b2,
0x05b2,
0x05b2,
0x05b2,
0x05b2,
0x05b2,
0x05b3,
0x05b3,
0x05b3,
0x05b3,
0x05b3,
0x05b3,
0x05b3,
0x05b3,
0x05b1,
0x05b1,
0x05b1,
0x05b1,
0x05b1,
0x05b1,
0x05b1,
0x05b1,
0x0127,
0x310f,
0x1e2b,
0x1e29,
0x24d7,
0x0125,
0x1e27,
0x1e23,
0x1e25,
0x05d4,
0x2665,
0x2665,
0x2661,
0xfb34,
0xfb34,
0x06c1,
0x0647,
0x05d4,
0xfba7,
0xfeea,
0xfeea,
0xfba5,
0xfba4,
0xfba8,
0xfeeb,
0x3078,
0xfba9,
0xfeec,
0x337b,
0x30d8,
0xff8d,
0x3336,
0x0267,
0x3339,
0x05d7,
0x05d7,
0x0266,
0x02b1,
0x327b,
0x321b,
0x326d,
0x314e,
0x320d,
0x3072,
0x30d2,
0xff8b,
0x05b4,
0x05b4,
0x05b4,
0x05b4,
0x05b4,
0x05b4,
0x05b4,
0x05b4,
0x1e96,
0xff48,
0x0570,
0x0e2b,
0x307b,
0x30db,
0xff8e,
0x05b9,
0x05b9,
0x05b9,
0x05b9,
0x05b9,
0x05b9,
0x05b9,
0x05b9,
0x0e2e,
0x0309,
0x0309,
0x0321,
0x0322,
0x3342,
0x03e9,
0x2015,
0x031b,
0x2668,
0x2302,
0x24a3,
0x02b0,
0x0265,
0x3075,
0x3333,
0x30d5,
0xff8c,
0x02dd,
0x030b,
0x0195,
0x002d,
0x002d,
0xf6e5,
0xff0d,
0xfe63,
0xf6e6,
0x2010,
0x0069,
0x00ed,
0x044f,
0x0987,
0x3127,
0x012d,
0x01d0,
0x24d8,
0x00ee,
0x0456,
0x0209,
0x328f,
0x328b,
0x323f,
0x323a,
0x32a5,
0x3006,
0x3001,
0xff64,
0x3237,
0x32a3,
0x322f,
0x323d,
0x329d,
0x3240,
0x3296,
0x3236,
0x322b,
0x3232,
0x32a4,
0x3005,
0x3298,
0x3238,
0x32a7,
0x32a6,
0x32a9,
0x322e,
0x322a,
0x3234,
0x3002,
0x329e,
0x3243,
0x3239,
0x323e,
0x32a8,
0x3299,
0x3242,
0x3233,
0x3000,
0x3235,
0x3231,
0x323b,
0x3230,
0x323c,
0x322c,
0x322d,
0x3007,
0x328e,
0x328a,
0x3294,
0x3290,
0x328c,
0x328d,
0x0907,
0x00ef,
0x1e2f,
0x04e5,
0x1ecb,
0x04d7,
0x0435,
0x3275,
0x3215,
0x3267,
0x3147,
0x3207,
0x00ec,
0x0a87,
0x0a07,
0x3044,
0x1ec9,
0x0988,
0x0438,
0x0908,
0x0a88,
0x0a08,
0x0a40,
0x020b,
0x0439,
0x09c0,
0x0940,
0x0ac0,
0x0133,
0x30a4,
0xff72,
0x3163,
0x02dc,
0x05ac,
0x012b,
0x04e3,
0x2253,
0x0a3f,
0xff49,
0x2206,
0x221e,
0x056b,
0x2216,
0x222b,
0x2321,
0x2321,
0xf8f5,
0x2320,
0x2320,
0x22ba,
0x203d,
0x2e18,
0x2229,
0x22d2,
0x2293,
0x3305,
0x25d8,
0x25d9,
0x263b,
0x0451,
0x012f,
0x03b9,
0x03ca,
0x0390,
0x0269,
0x03af,
0x24a4,
0x0a72,
0x3043,
0x30a3,
0xff68,
0x09fa,
0x0268,
0xf6ed,
0x309d,
0x30fd,
0x0129,
0x1e2d,
0x3129,
0x044e,
0x09bf,
0x093f,
0x0abf,
0x0475,
0x0477,
0x006a,
0x0571,
0x099c,
0x091c,
0x0a9c,
0x0a1c,
0x3110,
0x01f0,
0x24d9,
0x0135,
0x029d,
0x025f,
0x0458,
0x062c,
0xfe9e,
0xfe9f,
0xfea0,
0x0698,
0xfb8b,
0x099d,
0x091d,
0x0a9d,
0x0a1d,
0x057b,
0x3004,
0xff4a,
0x24a5,
0x02b2,
0x006b,
0x04a1,
0x0995,
0x1e31,
0x043a,
0x049b,
0x0915,
0x05db,
0x0643,
0xfb3b,
0xfb3b,
0xfeda,
0x05db,
0xfedb,
0xfedc,
0xfb4d,
0x0a95,
0x0a15,
0x304b,
0x04c4,
0x30ab,
0xff76,
0x03ba,
0x03f0,
0x3171,
0x3184,
0x3178,
0x3179,
0x330d,
0x0640,
0x0640,
0x30f5,
0x3384,
0x0650,
0x064d,
0x049f,
0xff70,
0x049d,
0x310e,
0x3389,
0x01e9,
0x0137,
0x24da,
0x0137,
0x1e33,
0x0584,
0x3051,
0x30b1,
0xff79,
0x056f,
0x30f6,
0x0138,
0x0996,
0x0445,
0x0916,
0x0a96,
0x0a16,
0x062e,
0xfea6,
0xfea7,
0xfea8,
0x03e7,
0x0959,
0x0a59,
0x3278,
0x3218,
0x326a,
0x314b,
0x320a,
0x0e02,
0x0e05,
0x0e03,
0x0e04,
0x0e5b,
0x0199,
0x0e06,
0x3391,
0x304d,
0x30ad,
0xff77,
0x3315,
0x3316,
0x3314,
0x326e,
0x320e,
0x3260,
0x3131,
0x3200,
0x3133,
0x045c,
0x1e35,
0x3398,
0x33a6,
0xff4b,
0x33a2,
0x3053,
0x33c0,
0x0e01,
0x30b3,
0xff7a,
0x331e,
0x0481,
0x327f,
0x0343,
0x24a6,
0x33aa,
0x046f,
0x33cf,
0x029e,
0x304f,
0x30af,
0xff78,
0x33b8,
0x33be,
0x006c,
0x09b2,
0x013a,
0x0932,
0x0ab2,
0x0a32,
0x0e45,
0xfefc,
0xfef8,
0xfef7,
0xfefa,
0xfef9,
0xfefb,
0xfef6,
0xfef5,
0x0644,
0x03bb,
0x019b,
0x05dc,
0xfb3c,
0xfb3c,
0x05dc,
0xfede,
0xfcca,
0xfedf,
0xfcc9,
0xfccb,
0xfdf2,
0xfee0,
0xfd88,
0xfccc,
0x25ef,
0x22a4,
0x019a,
0x026c,
0x310c,
0x013e,
0x013c,
0x24db,
0x1e3d,
0x013c,
0x0140,
0x0140,
0x1e37,
0x1e39,
0x031a,
0x0318,
0x003c,
0x2a8b,
0x2266,
0x22d6,
0x2264,
0x22da,
0x22da,
0xff1c,
0x226a,
0x2a89,
0x2a87,
0x2a85,
0x2a7d,
0x2272,
0x2276,
0x2268,
0x2268,
0x2272,
0x2266,
0xfe64,
0x026e,
0x258c,
0x026d,
0x20a4,
0x056c,
0x01c9,
0x0459,
0xf6c0,
0x0933,
0x0ab3,
0x1e3b,
0x0934,
0x09e1,
0x0961,
0x09e3,
0x0963,
0x026b,
0xff4c,
0x33d0,
0x0e2c,
0x2227,
0x00ac,
0x2310,
0x2228,
0x0e25,
0x017f,
0xfb05,
0xfe4e,
0x0332,
0xfe4d,
0x25ca,
0x24a7,
0x2113,
0x0142,
0x2113,
0xf6ee,
0x2591,
0x0e26,
0x098c,
0x090c,
0x09e2,
0x0962,
0x33d3,
0x006d,
0x09ae,
0x00af,
0x0331,
0x0304,
0x02cd,
0xffe3,
0x1e3f,
0x092e,
0x0aae,
0x0a2e,
0x05a4,
0x05a4,
0x307e,
0xf895,
0xf894,
0x0e4b,
0xf893,
0xf88c,
0xf88b,
0x0e48,
0xf88a,
0xf884,
0x0e31,
0xf889,
0x0e47,
0xf88f,
0xf88e,
0x0e49,
0xf88d,
0xf892,
0xf891,
0x0e4a,
0xf890,
0x0e46,
0x30de,
0xff8f,
0x2642,
0x2720,
0x3347,
0x05be,
0x2642,
0x05af,
0x3383,
0x3107,
0x33d4,
0x24dc,
0x33a5,
0x1e41,
0x1e43,
0x2221,
0x0645,
0xfee2,
0xfee3,
0xfee4,
0xfcd1,
0xfc48,
0x334d,
0x3081,
0x337e,
0x30e1,
0xff92,
0x05de,
0xfb3e,
0xfb3e,
0x05de,
0x0574,
0x05a5,
0x05a6,
0x05a6,
0x05a5,
0x0271,
0x3392,
0xff65,
0x00b7,
0x3272,
0x3212,
0x3264,
0x3141,
0x3170,
0x3204,
0x316e,
0x316f,
0x307f,
0x30df,
0xff90,
0x2212,
0x0320,
0x2296,
0x02d7,
0x2213,
0x2032,
0x334a,
0x3349,
0x0270,
0x3396,
0x33a3,
0xff4d,
0x339f,
0x3082,
0x33c1,
0x30e2,
0xff93,
0x33d6,
0x0e21,
0x33a7,
0x33a8,
0x24a8,
0x33ab,
0x33b3,
0xf6ef,
0x026f,
0x00b5,
0x00b5,
0x3382,
0x226b,
0x226a,
0x338c,
0x03bc,
0x338d,
0x3080,
0x30e0,
0xff91,
0x3395,
0x22c9,
0x22ca,
0x22b8,
0x22cb,
0x22cc,
0x00d7,
0x339b,
0x05a3,
0x05a3,
0x266a,
0x266b,
0x266d,
0x266f,
0x33b2,
0x33b6,
0x33bc,
0x33b9,
0x33b7,
0x33bf,
0x33bd,
0x006e,
0x09a8,
0x2207,
0x0144,
0x0928,
0x0aa8,
0x0a28,
0x306a,
0x30ca,
0xff85,
0x22bc,
0x0149,
0x3381,
0x266e,
0x310b,
0x00a0,
0x0148,
0x0146,
0x24dd,
0x1e4b,
0x0146,
0x1e45,
0x1e47,
0x0338,
0x306d,
0x30cd,
0xff88,
0x20aa,
0x338b,
0x014b,
0x0999,
0x0919,
0x0a99,
0x0a19,
0x0e07,
0x3093,
0x0272,
0x0273,
0x326f,
0x320f,
0x3135,
0x3261,
0x3136,
0x3134,
0x3168,
0x3201,
0x3167,
0x3166,
0x306b,
0x30cb,
0xff86,
0xf899,
0x0e4d,
0x0039,
0x0669,
0x09ef,
0x2468,
0x2792,
0x096f,
0x0aef,
0x0a6f,
0x0669,
0x3029,
0x3228,
0x2089,
0xff19,
0xf739,
0x247c,
0x2490,
0x06f9,
0x2178,
0x2079,
0x2472,
0x2486,
0x249a,
0x0e59,
0x01cc,
0x045a,
0x30f3,
0xff9d,
0x019e,
0x1e49,
0xff4e,
0x339a,
0x09a3,
0x0923,
0x0aa3,
0x0a23,
0x0929,
0x306e,
0x30ce,
0xff89,
0x00a0,
0x0e13,
0x0e19,
0x0646,
0xfee6,
0x06ba,
0xfb9f,
0xfee7,
0xfcd2,
0xfc4b,
0xfee8,
0xfcd5,
0xfc4e,
0xfc8d,
0x2247,
0x21ae,
0x219a,
0x219b,
0x2224,
0x220c,
0x21ce,
0x21cd,
0x21cf,
0x2209,
0x2209,
0x2260,
0x2204,
0x2281,
0x22ae,
0x22af,
0x226f,
0x2271,
0x2271,
0x2279,
0x2262,
0x226e,
0x2270,
0x2270,
0x2226,
0x2280,
0x22ad,
0x2241,
0x2284,
0x2288,
0x228a,
0x2281,
0x2285,
0x2289,
0x228b,
0x22ec,
0x22ed,
0x22ea,
0x22eb,
0x22ac,
0x0576,
0x24a9,
0x33b1,
0x207f,
0x00f1,
0x03bd,
0x306c,
0x30cc,
0xff87,
0x09bc,
0x093c,
0x0abc,
0x0a3c,
0x0023,
0xff03,
0xfe5f,
0x0374,
0x0375,
0x2116,
0x05e0,
0xfb40,
0xfb40,
0x05e0,
0x33b5,
0x33bb,
0x099e,
0x091e,
0x0a9e,
0x0a1e,
0x006f,
0x00f3,
0x0e2d,
0x0275,
0x04e9,
0x04eb,
0x0993,
0x311b,
0x014f,
0x0911,
0x0a91,
0x0949,
0x0ac9,
0x01d2,
0x24de,
0x00f4,
0x1ed1,
0x1ed9,
0x1ed3,
0x1ed5,
0x1ed7,
0x043e,
0x0151,
0x020d,
0x0913,
0x00f6,
0x04e7,
0x1ecd,
0x0153,
0x315a,
0x02db,
0x0328,
0x00f2,
0x0a93,
0x0585,
0x304a,
0x1ecf,
0x01a1,
0x1edb,
0x1ee3,
0x1edd,
0x1edf,
0x1ee1,
0x0151,
0x01a3,
0x020f,
0x30aa,
0xff75,
0x3157,
0x05ab,
0x014d,
0x1e53,
0x1e51,
0x0950,
0x03c9,
0x03d6,
0x0461,
0x0277,
0x047b,
0x047d,
0x03ce,
0x0ad0,
0x03bf,
0x03cc,
0xff4f,
0x0031,
0x0661,
0x09e7,
0x2460,
0x278a,
0x0967,
0x2024,
0x215b,
0xf6dc,
0x0ae7,
0x0a67,
0x0661,
0x00bd,
0x3021,
0x3220,
0x2081,
0xff11,
0x09f4,
0xf731,
0x2474,
0x2488,
0x06f1,
0x00bc,
0x2170,
0x00b9,
0x0e51,
0x2153,
0x01eb,
0x01ed,
0x0a13,
0x0a4b,
0x0254,
0x24aa,
0x25e6,
0x2325,
0x00aa,
0x00ba,
0x221f,
0x22bb,
0x0912,
0x094a,
0x00f8,
0x01ff,
0x3049,
0x30a9,
0xff6b,
0x01ff,
0xf6f0,
0x047f,
0x00f5,
0x1e4d,
0x1e4f,
0x3121,
0x203e,
0xfe4a,
0x0305,
0xfe49,
0xfe4c,
0xfe4b,
0x00af,
0x09cb,
0x094b,
0x0acb,
0x220b,
0x0070,
0x3380,
0x332b,
0x09aa,
0x1e55,
0x092a,
0x21df,
0x21de,
0x0aaa,
0x0a2a,
0x3071,
0x0e2f,
0x30d1,
0x0484,
0x04c0,
0x317f,
0x00b6,
0x2225,
0x0028,
0xfd3e,
0xf8ed,
0xf8ec,
0x208d,
0xff08,
0xfe59,
0x207d,
0xf8eb,
0xfe35,
0x0029,
0xfd3f,
0xf8f8,
0xf8f7,
0x208e,
0xff09,
0xfe5a,
0x207e,
0xf8f6,
0xfe36,
0x2202,
0x05c0,
0x0599,
0x33a9,
0x05b7,
0x05b7,
0x05b7,
0x05b7,
0x05b7,
0x05b7,
0x05b7,
0x05b7,
0x05a1,
0x3106,
0x24df,
0x1e57,
0x05e4,
0x043f,
0xfb44,
0xfb44,
0x333b,
0xfb43,
0x067e,
0x057a,
0x05e4,
0xfb57,
0xfb58,
0x307a,
0xfb59,
0x30da,
0x04a7,
0xfb4e,
0x0025,
0x066a,
0xff05,
0xfe6a,
0x002e,
0x0589,
0x00b7,
0xff61,
0xf6e7,
0xff0e,
0xfe52,
0xf6e8,
0x0342,
0x2a5e,
0x22a5,
0x2031,
0x2030,
0x20a7,
0x338a,
0x09ab,
0x092b,
0x0aab,
0x0a2b,
0x03c6,
0x03d5,
0x327a,
0x321a,
0x326c,
0x314d,
0x320c,
0x0278,
0x0e3a,
0x03d5,
0x01a5,
0x0e1e,
0x0e1c,
0x0e20,
0x03c0,
0x03d6,
0x3273,
0x3213,
0x3176,
0x3265,
0x3172,
0x3142,
0x3205,
0x3174,
0x3144,
0x3175,
0x3177,
0x3173,
0x3074,
0x30d4,
0x03d6,
0x0583,
0x210f,
0x210f,
0x002b,
0x031f,
0x2295,
0x00b1,
0x02d6,
0xff0b,
0xfe62,
0x207a,
0xff50,
0x33d8,
0x307d,
0x261f,
0x261c,
0x261e,
0x261d,
0x30dd,
0x0e1b,
0x3012,
0x3020,
0x24ab,
0x2ab9,
0x2ab5,
0x22e8,
0x227a,
0x2aaf,
0x227c,
0x227e,
0x211e,
0x2032,
0x02b9,
0x2035,
0x2035,
0x220f,
0x2305,
0x30fc,
0x2318,
0x2282,
0x2283,
0x2237,
0x221d,
0x03c8,
0x0471,
0x0486,
0x33b0,
0x3077,
0x30d7,
0x2014,
0x33b4,
0x33ba,
0x0071,
0x0958,
0x05a8,
0x0642,
0xfed6,
0xfed7,
0xfed8,
0x05b8,
0x05b8,
0x05b8,
0x05b8,
0x05b8,
0x05b8,
0x05b8,
0x05b8,
0x05b8,
0x05b8,
0x05b8,
0x05b8,
0x05b8,
0x05b8,
0x05b8,
0x05b8,
0x059f,
0x3111,
0x24e0,
0x02a0,
0xff51,
0x05e7,
0xfb47,
0xfb47,
0x05e7,
0x24ac,
0x2669,
0x05bb,
0x05bb,
0x05bb,
0x05bb,
0x05bb,
0x05bb,
0x05bb,
0x05bb,
0x003f,
0x061f,
0x055e,
0x00bf,
0xf7bf,
0x037e,
0xff1f,
0xf73f,
0x0022,
0x201e,
0x201c,
0xff02,
0x301e,
0x301d,
0x201d,
0x2018,
0x201b,
0x201b,
0x2019,
0x0149,
0x201a,
0x0027,
0xff07,
0x0072,
0x057c,
0x09b0,
0x0155,
0x0930,
0x221a,
0xf8e5,
0x33ae,
0x33af,
0x33ad,
0x05bf,
0x05bf,
0x0ab0,
0x0a30,
0x3089,
0x30e9,
0xff97,
0x09f1,
0x09f0,
0x0264,
0x2013,
0x2236,
0x3116,
0x0159,
0x0157,
0x24e1,
0x0157,
0x0211,
0x1e59,
0x1e5b,
0x1e5d,
0x203b,
0x2286,
0x2287,
0x00ae,
0xf8e8,
0xf6da,
0x0631,
0x0580,
0xfeae,
0x308c,
0x30ec,
0xff9a,
0x05e8,
0xfb48,
0x05e8,
0x22cd,
0x223d,
0x0597,
0x0597,
0x2310,
0x223d,
0x027e,
0x027f,
0x09dd,
0x095d,
0x03c1,
0x03f1,
0x027d,
0x027b,
0x02b5,
0x03f1,
0x02de,
0x3271,
0x3211,
0x3263,
0x3140,
0x313a,
0x3169,
0x3139,
0x313b,
0x316c,
0x3203,
0x313f,
0x313c,
0x316b,
0x313d,
0x313e,
0x316a,
0x316d,
0x221f,
0x231d,
0x231c,
0x231f,
0x231e,
0x0319,
0x22bf,
0x308a,
0x30ea,
0xff98,
0x02da,
0x0325,
0x030a,
0xd80d,
0x02bf,
0x0559,
0x031c,
0x02d3,
0x02be,
0x0339,
0x02d2,
0x2256,
0x0213,
0x3351,
0x1e5f,
0x027c,
0x027a,
0xff52,
0x308d,
0x30ed,
0xff9b,
0x0e23,
0x24ad,
0x09dc,
0x0931,
0x0a5c,
0x0691,
0xfb8d,
0x09e0,
0x0960,
0x0ae0,
0x09c4,
0x0944,
0x0ac4,
0xf6f1,
0x2590,
0x0279,
0x02b4,
0x308b,
0x30eb,
0xff99,
0x09f2,
0x09f3,
0xf6dd,
0x0e24,
0x098b,
0x090b,
0x0a8b,
0x09c3,
0x0943,
0x0ac3,
0x0073,
0x09b8,
0x015b,
0x1e65,
0x0635,
0x0938,
0xfeba,
0xfebb,
0xfebc,
0x0ab8,
0x0a38,
0x3055,
0x30b5,
0xff7b,
0xfdfa,
0x05e1,
0xfb41,
0xfb41,
0x05e1,
0x0e32,
0x0e41,
0x0e44,
0x0e43,
0x0e33,
0x0e30,
0x0e40,
0xf886,
0x0e35,
0xf885,
0x0e34,
0x0e42,
0xf888,
0x0e37,
0xf887,
0x0e36,
0x0e38,
0x0e39,
0x22a8,
0x3119,
0x0161,
0x1e67,
0x015f,
0x0259,
0x04d9,
0x04db,
0x025a,
0x24e2,
0x015d,
0x0219,
0x1e61,
0x1e63,
0x1e69,
0x033c,
0x2033,
0x02ca,
0x00a7,
0x0633,
0xfeb2,
0xfeb3,
0xfeb4,
0x05b6,
0x05b6,
0x05b6,
0x05b6,
0x05b6,
0x05b6,
0x05b6,
0x0592,
0x05b6,
0x057d,
0x305b,
0x30bb,
0xff7e,
0x003b,
0x061b,
0xff1b,
0xfe54,
0x309c,
0xff9f,
0x3322,
0x3323,
0x0037,
0x0667,
0x09ed,
0x2466,
0x2790,
0x096d,
0x215e,
0x0aed,
0x0a6d,
0x0667,
0x3027,
0x3226,
0x2087,
0xff17,
0xf737,
0x247a,
0x248e,
0x06f7,
0x2176,
0x2077,
0x2470,
0x2484,
0x2498,
0x0e57,
0x00ad,
0x0577,
0x09b6,
0x0448,
0x0651,
0xfc61,
0xfc5e,
0xfc60,
0xfc62,
0xfc5f,
0x2592,
0x2593,
0x2591,
0x2592,
0x0936,
0x0ab6,
0x0a36,
0x0593,
0x266f,
0x3115,
0x0449,
0x0634,
0xfeb6,
0xfeb7,
0xfeb8,
0x03e3,
0x20aa,
0x20aa,
0x05b0,
0x05b0,
0x05b0,
0x05b0,
0x05b0,
0x05b0,
0x05b0,
0x05b0,
0x05b0,
0x04bb,
0x21b0,
0x21b1,
0x03ed,
0x05e9,
0xfb49,
0xfb49,
0xfb2c,
0xfb2c,
0xfb2d,
0xfb2d,
0x05c1,
0x05e9,
0xfb2a,
0xfb2a,
0xfb2b,
0xfb2b,
0x0282,
0x03c3,
0x03c2,
0x03c2,
0x03f2,
0x3057,
0x30b7,
0xff7c,
0x05bd,
0x05bd,
0x223c,
0x2243,
0x05c2,
0x3274,
0x3214,
0x317e,
0x3266,
0x317a,
0x3145,
0x317b,
0x3206,
0x317d,
0x317c,
0x0036,
0x0666,
0x09ec,
0x2465,
0x278f,
0x096c,
0x0aec,
0x0a6c,
0x0666,
0x3026,
0x3225,
0x2086,
0xff16,
0xf736,
0x2479,
0x248d,
0x06f6,
0x2175,
0x2076,
0x246f,
0x09f9,
0x2483,
0x2497,
0x0e56,
0x002f,
0xff0f,
0x017f,
0x1e9b,
0x2322,
0x2323,
0x2323,
0x263a,
0xff53,
0x05c3,
0x00ad,
0x044c,
0x305d,
0x30bd,
0xff7f,
0x0338,
0x0337,
0x0e29,
0x0e28,
0x0e0b,
0x0e2a,
0x0020,
0x0020,
0x2660,
0x2660,
0x2664,
0x24ae,
0x2222,
0x25a1,
0x033b,
0x33c4,
0x339d,
0x25a9,
0x22a1,
0x25a4,
0x228f,
0x338f,
0x339e,
0x33ce,
0x33d1,
0x33d2,
0x338e,
0x33d5,
0x229f,
0x339c,
0x33a1,
0x22a0,
0x2290,
0x25a6,
0x229e,
0x25a0,
0x25a7,
0x25a8,
0x25a5,
0x25a3,
0x21ad,
0x21dd,
0x33db,
0x09b7,
0x0937,
0x0ab7,
0x3149,
0x3185,
0x3180,
0x3132,
0x3165,
0x3143,
0x3146,
0x3138,
0xf6f2,
0xfb06,
0x22c6,
0x00a3,
0xffe1,
0x0336,
0x0335,
0x2282,
0x22d0,
0x2ac5,
0x228a,
0x228a,
0x2286,
0x2acb,
0x2291,
0x227b,
0x220b,
0x3059,
0x30b9,
0xff7d,
0x0652,
0x2211,
0x263c,
0x2283,
0x22d1,
0x2ac6,
0x228b,
0x228b,
0x2287,
0x2acc,
0x2292,
0x33dc,
0x337c,
0x0074,
0x09a4,
0x22a4,
0x22a3,
0x0924,
0x0aa4,
0x0a24,
0x0637,
0xfec2,
0xfec3,
0x305f,
0xfec4,
0x337d,
0x30bf,
0xff80,
0x0640,
0x03c4,
0x05ea,
0xfb4a,
0xfb4a,
0xfb4a,
0x05ea,
0x0167,
0x310a,
0x0165,
0x02a8,
0x0163,
0x0686,
0xfb7b,
0xfb7c,
0xfb7d,
0x24e3,
0x1e71,
0x0163,
0x1e97,
0x1e6b,
0x1e6d,
0x0442,
0x04ad,
0x062a,
0xfe96,
0xfca2,
0xfc0c,
0xfe97,
0x3066,
0xfca1,
0xfc0b,
0x0629,
0xfe94,
0xfe98,
0xfca4,
0xfc0e,
0xfc73,
0x30c6,
0xff83,
0x2121,
0x260e,
0x05a0,
0x05a9,
0x2469,
0x3229,
0x247d,
0x2491,
0x2179,
0x02a7,
0x05d8,
0xfb38,
0xfb38,
0x05d8,
0x04b5,
0x059b,
0x059b,
0x09a5,
0x0925,
0x0aa5,
0x0a25,
0x0630,
0xfeac,
0xf898,
0xf897,
0x0e4c,
0xf896,
0x062b,
0xfe9a,
0xfe9b,
0xfe9c,
0x2203,
0x2234,
0x03b8,
0x03d1,
0x03d1,
0x3279,
0x3219,
0x326b,
0x314c,
0x320b,
0x246c,
0x2480,
0x2494,
0x0e11,
0x01ad,
0x0e12,
0x00fe,
0x0e17,
0x0e10,
0x0e18,
0x0e16,
0x0482,
0x066c,
0x066c,
0x0033,
0x0663,
0x09e9,
0x2462,
0x278c,
0x0969,
0x215c,
0x0ae9,
0x0a69,
0x0663,
0x3023,
0x3222,
0x2083,
0xff13,
0x09f6,
0xf733,
0x2476,
0x248a,
0x06f3,
0x00be,
0xf6de,
0x2172,
0x00b3,
0x0e53,
0x3394,
0x3061,
0x30c1,
0xff81,
0x3270,
0x3210,
0x3262,
0x3137,
0x3202,
0x02dc,
0x0330,
0x0303,
0x0303,
0x0360,
0x223c,
0x0334,
0x033e,
0x2297,
0x0596,
0x0596,
0x0a70,
0x0483,
0x057f,
0x1e6f,
0xff54,
0x0569,
0x3068,
0x30c8,
0xff84,
0x02e5,
0x02e9,
0x02e6,
0x02e8,
0x02e7,
0x01bd,
0x0185,
0x01a8,
0x0384,
0x3327,
0x0e0f,
0x3014,
0xfe5d,
0xfe39,
0x3015,
0xfe5e,
0xfe3a,
0x0e15,
0x01ab,
0x24af,
0x2122,
0xf8ea,
0xf6db,
0x0288,
0x25bc,
0x25c4,
0x25ba,
0x25b2,
0x25b3,
0x25bc,
0x25bd,
0x25c1,
0x22b4,
0x25c0,
0x25b7,
0x22b5,
0x25b6,
0x25b2,
0x02a6,
0x05e6,
0xfb46,
0xfb46,
0x05e6,
0x0446,
0x05b5,
0x05b5,
0x05b5,
0x05b5,
0x05b5,
0x05b5,
0x05b5,
0x05b5,
0x045b,
0xf6f3,
0x099f,
0x091f,
0x0a9f,
0x0a1f,
0x0679,
0xfb67,
0xfb68,
0xfb69,
0x09a0,
0x0920,
0x0aa0,
0x0a20,
0x0287,
0x3064,
0x30c4,
0xff82,
0x22a2,
0x22a3,
0x3063,
0x30c3,
0xff6f,
0x246b,
0x247f,
0x2493,
0x217b,
0xd80c,
0x2473,
0x5344,
0x2487,
0x249b,
0x0032,
0x0662,
0x09e8,
0x2461,
0x278b,
0x0968,
0x2025,
0x2025,
0xfe30,
0x0ae8,
0x0a68,
0x0662,
0x3022,
0x3221,
0x2082,
0xff12,
0x09f5,
0xf732,
0x2475,
0x2489,
0x06f2,
0x2171,
0x01bb,
0x00b2,
0x0e52,
0x2154,
0x0075,
0x00fa,
0x0289,
0x0989,
0x3128,
0x016d,
0x01d4,
0x24e4,
0x00fb,
0x1e77,
0x0443,
0x0951,
0x0171,
0x0215,
0x0909,
0x00fc,
0x01d8,
0x1e73,
0x01da,
0x04f1,
0x01dc,
0x01d6,
0x1ee5,
0x00f9,
0x0a89,
0x0a09,
0x3046,
0x1ee7,
0x01b0,
0x1ee9,
0x1ef1,
0x1eeb,
0x1eed,
0x1eef,
0x0171,
0x04f3,
0x0217,
0x30a6,
0xff73,
0x0479,
0x315c,
0x016b,
0x04ef,
0x1e7b,
0x0a41,
0xff55,
0x005f,
0x2017,
0xff3f,
0xfe33,
0xfe4f,
0x222a,
0x22d3,
0x228e,
0x2294,
0x2200,
0x0173,
0x24b0,
0x2580,
0x05c4,
0x22cf,
0x03c5,
0x03cb,
0x03b0,
0x028a,
0x03cd,
0x29f8,
0x031d,
0x02d4,
0x0a73,
0x016f,
0x045e,
0x3045,
0x30a5,
0xff69,
0x04af,
0x04b1,
0x0169,
0x1e79,
0x1e75,
0x098a,
0x090a,
0x0a8a,
0x0a0a,
0x0a42,
0x09c2,
0x0942,
0x0ac2,
0x09c1,
0x0941,
0x0ac1,
0x0076,
0x0935,
0x0ab5,
0x0a35,
0x30f7,
0x05d5,
0xfb35,
0xfb35,
0xfb35,
0x05d5,
0xfb4b,
0xfb4b,
0x05f0,
0x05f1,
0x24e5,
0x1e7f,
0x20d7,
0x0432,
0x06a4,
0xfb6b,
0xfb6c,
0xfb6d,
0x30f9,
0x2640,
0x007c,
0x030d,
0x0329,
0x02cc,
0x02c8,
0x057e,
0x028b,
0x30f8,
0x09cd,
0x094d,
0x0acd,
0x0983,
0x0903,
0x0a83,
0x2423,
0x2423,
0xff56,
0x0578,
0x309e,
0x30fe,
0x309b,
0xff9e,
0x30fa,
0x24b1,
0x1e7d,
0x028c,
0x3094,
0x30f4,
0x0077,
0x1e83,
0x3159,
0x308f,
0x30ef,
0xff9c,
0x3158,
0x308e,
0x30ee,
0x3357,
0x301c,
0xfe34,
0x0648,
0xfeee,
0x0624,
0xfe86,
0x33dd,
0x24e6,
0x0175,
0x1e85,
0x1e87,
0x1e89,
0x3091,
0x2118,
0x30f1,
0x315e,
0x315d,
0x1e81,
0x25e6,
0x25cb,
0x25d9,
0x300e,
0xfe43,
0x300f,
0xfe44,
0x25c7,
0x25c8,
0x25bf,
0x25bd,
0x25c3,
0x25c1,
0x3016,
0x3017,
0x25b9,
0x25b7,
0x25ab,
0x263a,
0x25a1,
0x2606,
0x260f,
0x3018,
0x3019,
0x25b5,
0x25b3,
0x3090,
0x30f0,
0x315f,
0xff57,
0x3092,
0x30f2,
0xff66,
0x20a9,
0xffe6,
0x0e27,
0x24b2,
0x2240,
0x1e98,
0x02b7,
0x028d,
0x01bf,
0x0078,
0x033d,
0x3112,
0x24e7,
0x1e8d,
0x1e8b,
0x056d,
0x03be,
0xff58,
0x24b3,
0x02e3,
0x0079,
0x334e,
0x09af,
0x00fd,
0x092f,
0x3152,
0x0aaf,
0x0a2f,
0x3084,
0x30e4,
0xff94,
0x3151,
0x0e4e,
0x3083,
0x30e3,
0xff6c,
0x0463,
0x24e8,
0x0177,
0x00ff,
0x1e8f,
0x1ef5,
0x064a,
0x06d2,
0xfbaf,
0xfef2,
0x0626,
0xfe8a,
0xfe8b,
0xfe8c,
0xfef3,
0xfef4,
0xfcdd,
0xfc58,
0xfc94,
0x06d1,
0x3156,
0x00a5,
0xffe5,
0x3155,
0x3186,
0x05aa,
0x05aa,
0x044b,
0x04f9,
0x3181,
0x3183,
0x3182,
0x059a,
0x1ef3,
0x01b4,
0x1ef7,
0x0575,
0x0457,
0x3162,
0x262f,
0x0582,
0xff59,
0x05d9,
0xfb39,
0xfb39,
0x05d9,
0x05f2,
0xfb1f,
0x3088,
0x3189,
0x30e8,
0xff96,
0x315b,
0x3087,
0x30e7,
0xff6e,
0x03f3,
0x3188,
0x3187,
0x0e22,
0x0e0d,
0x24b4,
0x037a,
0x0345,
0x01a6,
0x1e99,
0x02b8,
0x1ef9,
0x028e,
0x3086,
0x318c,
0x30e6,
0xff95,
0x3160,
0x046b,
0x046d,
0x0467,
0x0469,
0x3085,
0x30e5,
0xff6d,
0x318b,
0x318a,
0x09df,
0x095f,
0x007a,
0x0566,
0x017a,
0x095b,
0x0a5b,
0x0638,
0xfec6,
0xfec7,
0x3056,
0xfec8,
0x0632,
0xfeb0,
0x30b6,
0x0595,
0x0594,
0x0598,
0x05d6,
0xfb36,
0xfb36,
0x05d6,
0x3117,
0x017e,
0x24e9,
0x1e91,
0x0291,
0x017c,
0x017c,
0x1e93,
0x0437,
0x0499,
0x04df,
0x305c,
0x30bc,
0x0030,
0x0660,
0x09e6,
0x0966,
0x0ae6,
0x0a66,
0x0660,
0x2080,
0xff10,
0xf730,
0x06f0,
0x2070,
0x0e50,
0xfeff,
0x200c,
0x200b,
0x03b6,
0x3113,
0x056a,
0x04c2,
0x0436,
0x0497,
0x04dd,
0x3058,
0x30b8,
0x05ae,
0x1e95,
0xff5a,
0x305e,
0x30be,
0x24b5,
0x0290,
0x01b6,
0x305a,
0x30ba,
};

static const unsigned short agl_dup_offsets[] = {
0x0020, 0,
0x002d, 3,
0x007c, 6,
0x00a0, 9,
0x00a5, 12,
0x00ad, 15,
0x00ae, 18,
0x00af, 21,
0x00b5, 24,
0x00b7, 27,
0x010a, 30,
0x010b, 33,
0x0110, 36,
0x0111, 40,
0x0116, 44,
0x0117, 47,
0x0120, 50,
0x0121, 53,
0x0122, 56,
0x0123, 59,
0x0130, 62,
0x0136, 65,
0x0137, 68,
0x013b, 71,
0x013c, 74,
0x013f, 77,
0x0140, 80,
0x0145, 83,
0x0146, 86,
0x0149, 89,
0x014a, 92,
0x014b, 95,
0x0150, 98,
0x0151, 101,
0x0156, 104,
0x0157, 107,
0x0162, 110,
0x0163, 113,
0x0170, 116,
0x0171, 119,
0x017b, 122,
0x017c, 125,
0x017f, 128,
0x01fe, 131,
0x01ff, 134,
0x02bc, 137,
0x02bd, 140,
0x02dc, 143,
0x0300, 146,
0x0301, 149,
0x0303, 152,
0x0309, 155,
0x0323, 158,
0x0338, 161,
0x0385, 164,
0x03c2, 167,
0x03d1, 170,
0x03d2, 173,
0x03d5, 176,
0x03d6, 179,
0x03f1, 183,
0x0401, 186,
0x0402, 189,
0x0403, 192,
0x0404, 195,
0x0405, 198,
0x0406, 201,
0x0407, 204,
0x0408, 207,
0x0409, 210,
0x040a, 213,
0x040b, 216,
0x040c, 219,
0x040e, 222,
0x040f, 225,
0x0410, 228,
0x0411, 231,
0x0412, 234,
0x0413, 237,
0x0414, 240,
0x0415, 243,
0x0416, 246,
0x0417, 249,
0x0418, 252,
0x0419, 255,
0x041a, 258,
0x041b, 261,
0x041c, 264,
0x041d, 267,
0x041e, 270,
0x041f, 273,
0x0420, 276,
0x0421, 279,
0x0422, 282,
0x0423, 285,
0x0424, 288,
0x0425, 291,
0x0426, 294,
0x0427, 297,
0x0428, 300,
0x0429, 303,
0x042a, 306,
0x042b, 309,
0x042c, 312,
0x042d, 315,
0x042e, 318,
0x042f, 321,
0x0430, 324,
0x0431, 327,
0x0432, 330,
0x0433, 333,
0x0434, 336,
0x0435, 339,
0x0436, 342,
0x0437, 345,
0x0438, 348,
0x0439, 351,
0x043a, 354,
0x043b, 357,
0x043c, 360,
0x043d, 363,
0x043e, 366,
0x043f, 369,
0x0440, 372,
0x0441, 375,
0x0442, 378,
0x0443, 381,
0x0444, 384,
0x0445, 387,
0x0446, 390,
0x0447, 393,
0x0448, 396,
0x0449, 399,
0x044a, 402,
0x044b, 405,
0x044c, 408,
0x044d, 411,
0x044e, 414,
0x044f, 417,
0x0451, 420,
0x0452, 423,
0x0453, 426,
0x0454, 429,
0x0455, 432,
0x0456, 435,
0x0457, 438,
0x0458, 441,
0x0459, 444,
0x045a, 447,
0x045b, 450,
0x045c, 453,
0x045e, 456,
0x045f, 459,
0x0462, 462,
0x0463, 465,
0x0472, 468,
0x0473, 471,
0x0474, 474,
0x0475, 477,
0x0490, 480,
0x0491, 483,
0x04d9, 486,
0x0591, 489,
0x0596, 494,
0x0597, 497,
0x059b, 500,
0x05a3, 503,
0x05a4, 506,
0x05a5, 509,
0x05a6, 512,
0x05a7, 515,
0x05aa, 518,
0x05b0, 521,
0x05b1, 532,
0x05b2, 542,
0x05b3, 552,
0x05b4, 562,
0x05b5, 572,
0x05b6, 582,
0x05b7, 592,
0x05b8, 602,
0x05b9, 620,
0x05bb, 630,
0x05bc, 640,
0x05bd, 644,
0x05be, 648,
0x05bf, 651,
0x05c0, 655,
0x05c1, 658,
0x05c2, 661,
0x05c3, 664,
0x05d0, 667,
0x05d1, 671,
0x05d2, 675,
0x05d3, 679,
0x05d4, 683,
0x05d5, 687,
0x05d6, 691,
0x05d7, 695,
0x05d8, 699,
0x05d9, 703,
0x05da, 707,
0x05db, 711,
0x05dc, 715,
0x05dd, 719,
0x05de, 723,
0x05df, 727,
0x05e0, 731,
0x05e1, 735,
0x05e2, 739,
0x05e3, 743,
0x05e4, 747,
0x05e5, 751,
0x05e6, 755,
0x05e7, 759,
0x05e8, 763,
0x05e9, 767,
0x05ea, 771,
0x05f0, 775,
0x05f1, 778,
0x05f2, 781,
0x060c, 784,
0x061b, 787,
0x061f, 790,
0x0621, 793,
0x0622, 797,
0x0623, 800,
0x0624, 803,
0x0625, 806,
0x0626, 809,
0x0627, 812,
0x0628, 815,
0x0629, 818,
0x062a, 821,
0x062b, 824,
0x062c, 827,
0x062d, 830,
0x062e, 833,
0x062f, 836,
0x0630, 839,
0x0631, 842,
0x0632, 845,
0x0633, 848,
0x0634, 851,
0x0635, 854,
0x0636, 857,
0x0637, 860,
0x0638, 863,
0x0639, 866,
0x063a, 869,
0x0640, 872,
0x0641, 877,
0x0642, 880,
0x0643, 883,
0x0644, 886,
0x0645, 889,
0x0646, 892,
0x0647, 895,
0x0648, 898,
0x0649, 901,
0x064a, 904,
0x064b, 907,
0x064c, 910,
0x064d, 914,
0x064e, 917,
0x064f, 921,
0x0650, 925,
0x0651, 928,
0x0652, 931,
0x0660, 934,
0x0661, 938,
0x0662, 942,
0x0663, 946,
0x0664, 950,
0x0665, 954,
0x0666, 958,
0x0667, 962,
0x0668, 966,
0x0669, 970,
0x066a, 974,
0x066b, 977,
0x066c, 980,
0x066d, 983,
0x0679, 987,
0x067e, 990,
0x0686, 993,
0x0688, 996,
0x0691, 999,
0x0698, 1002,
0x06a4, 1005,
0x06af, 1008,
0x06ba, 1011,
0x06c1, 1014,
0x06d2, 1017,
0x200c, 1020,
0x2013, 1025,
0x2014, 1028,
0x2015, 1031,
0x2017, 1034,
0x201b, 1037,
0x2025, 1040,
0x2032, 1043,
0x2035, 1046,
0x20a1, 1049,
0x20a4, 1052,
0x20aa, 1055,
0x20ac, 1060,
0x2105, 1063,
0x210f, 1066,
0x2111, 1069,
0x2113, 1072,
0x2116, 1076,
0x211c, 1079,
0x2126, 1082,
0x2195, 1085,
0x2196, 1088,
0x2197, 1091,
0x2198, 1094,
0x2199, 1097,
0x21a8, 1100,
0x21bc, 1103,
0x21c0, 1106,
0x21c4, 1109,
0x21c6, 1112,
0x21cd, 1115,
0x21cf, 1118,
0x21d0, 1121,
0x21d2, 1124,
0x21d4, 1127,
0x2200, 1130,
0x2203, 1133,
0x2206, 1136,
0x2207, 1139,
0x2209, 1142,
0x220b, 1145,
0x2217, 1148,
0x221f, 1151,
0x2225, 1154,
0x223c, 1157,
0x223d, 1160,
0x2243, 1163,
0x2245, 1166,
0x2251, 1169,
0x2252, 1172,
0x2253, 1175,
0x2266, 1178,
0x2267, 1181,
0x2268, 1184,
0x2269, 1187,
0x226a, 1190,
0x226b, 1193,
0x2270, 1196,
0x2271, 1199,
0x2272, 1202,
0x2273, 1205,
0x227b, 1208,
0x2281, 1211,
0x2282, 1214,
0x2283, 1217,
0x2286, 1220,
0x2287, 1223,
0x228a, 1226,
0x228b, 1230,
0x2295, 1234,
0x2296, 1237,
0x2297, 1240,
0x2299, 1243,
0x22a3, 1246,
0x22a4, 1249,
0x22ce, 1252,
0x22cf, 1255,
0x22da, 1258,
0x22db, 1261,
0x2310, 1264,
0x2320, 1267,
0x2321, 1270,
0x2322, 1273,
0x2323, 1276,
0x2423, 1279,
0x24c8, 1283,
0x2591, 1286,
0x2592, 1289,
0x2593, 1292,
0x25a0, 1295,
0x25a1, 1299,
0x25aa, 1303,
0x25ab, 1306,
0x25ac, 1309,
0x25b2, 1312,
0x25b3, 1316,
0x25b6, 1319,
0x25b7, 1322,
0x25ba, 1325,
0x25bc, 1328,
0x25bd, 1332,
0x25c0, 1335,
0x25c1, 1338,
0x25c4, 1341,
0x25cb, 1344,
0x25cf, 1347,
0x25d8, 1350,
0x25d9, 1353,
0x25e6, 1356,
0x263a, 1359,
0x263b, 1362,
0x263c, 1365,
0x2640, 1368,
0x2642, 1371,
0x2660, 1374,
0x2663, 1377,
0x2665, 1380,
0x2666, 1383,
0x266b, 1386,
0x266d, 1389,
0x266f, 1392,
0x2713, 1395,
0xfb1f, 1398,
0xfb2a, 1403,
0xfb2b, 1407,
0xfb2c, 1411,
0xfb2d, 1414,
0xfb31, 1417,
0xfb32, 1420,
0xfb33, 1423,
0xfb34, 1426,
0xfb35, 1429,
0xfb36, 1434,
0xfb38, 1437,
0xfb39, 1440,
0xfb3a, 1443,
0xfb3b, 1446,
0xfb3c, 1449,
0xfb3e, 1452,
0xfb40, 1455,
0xfb41, 1458,
0xfb44, 1461,
0xfb46, 1464,
0xfb47, 1467,
0xfb49, 1470,
0xfb4a, 1473,
0xfb4b, 1477,
0xfeea, 1481,
0xfef3, 1484,
0xfef4, 1487,
};

static const char *agl_dup_names[] = {
"space", "spacehackarabic", 0,
"hyphen", "hyphenchar", 0,
"bar", "verticalbar", 0,
"nbspace", "nonbreakingspace", 0,
"yen", "Yen", 0,
"sfthyphen", "softhyphen", 0,
"registered", "circleR", 0,
"macron", "overscore", 0,
"mu", "mu1", 0,
"middot", "periodcentered", 0,
"Cdot", "Cdotaccent", 0,
"cdot", "cdotaccent", 0,
"Dcroat", "Dslash", "Dbar", 0,
"dcroat", "dmacron", "dbar", 0,
"Edot", "Edotaccent", 0,
"edot", "edotaccent", 0,
"Gdot", "Gdotaccent", 0,
"gdot", "gdotaccent", 0,
"Gcedilla", "Gcommaaccent", 0,
"gcedilla", "gcommaaccent", 0,
"Idot", "Idotaccent", 0,
"Kcedilla", "Kcommaaccent", 0,
"kcedilla", "kcommaaccent", 0,
"Lcedilla", "Lcommaaccent", 0,
"lcedilla", "lcommaaccent", 0,
"Ldot", "Ldotaccent", 0,
"ldot", "ldotaccent", 0,
"Ncedilla", "Ncommaaccent", 0,
"ncedilla", "ncommaaccent", 0,
"napostrophe", "quoterightn", 0,
"Eng", "Ng", 0,
"eng", "ng", 0,
"Odblacute", "Ohungarumlaut", 0,
"odblacute", "ohungarumlaut", 0,
"Rcedilla", "Rcommaaccent", 0,
"rcedilla", "rcommaaccent", 0,
"Tcedilla", "Tcommaaccent", 0,
"tcedilla", "tcommaaccent", 0,
"Udblacute", "Uhungarumlaut", 0,
"udblacute", "uhungarumlaut", 0,
"Zdot", "Zdotaccent", 0,
"zdot", "zdotaccent", 0,
"longs", "slong", 0,
"Oslashacute", "Ostrokeacute", 0,
"oslashacute", "ostrokeacute", 0,
"afii57929", "apostrophemod", 0,
"afii64937", "commareversedmod", 0,
"ilde", "tilde", 0,
"gravecmb", "gravecomb", 0,
"acutecmb", "acutecomb", 0,
"tildecmb", "tildecomb", 0,
"hookabovecomb", "hookcmb", 0,
"dotbelowcmb", "dotbelowcomb", 0,
"soliduslongoverlaycmb", "negationslash", 0,
"dialytikatonos", "dieresistonos", 0,
"sigma1", "sigmafinal", 0,
"theta1", "thetasymbolgreek", 0,
"Upsilon1", "Upsilonhooksymbol", 0,
"phi1", "phisymbolgreek", 0,
"omega1", "pisymbolgreek", "pi1", 0,
"rhosymbolgreek", "rho1", 0,
"Iocyrillic", "afii10023", 0,
"Djecyrillic", "afii10051", 0,
"Gjecyrillic", "afii10052", 0,
"Ecyrillic", "afii10053", 0,
"Dzecyrillic", "afii10054", 0,
"Icyrillic", "afii10055", 0,
"Yicyrillic", "afii10056", 0,
"Jecyrillic", "afii10057", 0,
"Ljecyrillic", "afii10058", 0,
"Njecyrillic", "afii10059", 0,
"Tshecyrillic", "afii10060", 0,
"Kjecyrillic", "afii10061", 0,
"Ushortcyrillic", "afii10062", 0,
"Dzhecyrillic", "afii10145", 0,
"Acyrillic", "afii10017", 0,
"Becyrillic", "afii10018", 0,
"Vecyrillic", "afii10019", 0,
"Gecyrillic", "afii10020", 0,
"Decyrillic", "afii10021", 0,
"Iecyrillic", "afii10022", 0,
"Zhecyrillic", "afii10024", 0,
"Zecyrillic", "afii10025", 0,
"Iicyrillic", "afii10026", 0,
"Iishortcyrillic", "afii10027", 0,
"Kacyrillic", "afii10028", 0,
"Elcyrillic", "afii10029", 0,
"Emcyrillic", "afii10030", 0,
"Encyrillic", "afii10031", 0,
"Ocyrillic", "afii10032", 0,
"Pecyrillic", "afii10033", 0,
"Ercyrillic", "afii10034", 0,
"Escyrillic", "afii10035", 0,
"Tecyrillic", "afii10036", 0,
"Ucyrillic", "afii10037", 0,
"Efcyrillic", "afii10038", 0,
"Khacyrillic", "afii10039", 0,
"Tsecyrillic", "afii10040", 0,
"Checyrillic", "afii10041", 0,
"Shacyrillic", "afii10042", 0,
"Shchacyrillic", "afii10043", 0,
"Hardsigncyrillic", "afii10044", 0,
"Yericyrillic", "afii10045", 0,
"Softsigncyrillic", "afii10046", 0,
"Ereversedcyrillic", "afii10047", 0,
"IUcyrillic", "afii10048", 0,
"IAcyrillic", "afii10049", 0,
"acyrillic", "afii10065", 0,
"afii10066", "becyrillic", 0,
"afii10067", "vecyrillic", 0,
"afii10068", "gecyrillic", 0,
"afii10069", "decyrillic", 0,
"afii10070", "iecyrillic", 0,
"afii10072", "zhecyrillic", 0,
"afii10073", "zecyrillic", 0,
"afii10074", "iicyrillic", 0,
"afii10075", "iishortcyrillic", 0,
"afii10076", "kacyrillic", 0,
"afii10077", "elcyrillic", 0,
"afii10078", "emcyrillic", 0,
"afii10079", "encyrillic", 0,
"afii10080", "ocyrillic", 0,
"afii10081", "pecyrillic", 0,
"afii10082", "ercyrillic", 0,
"afii10083", "escyrillic", 0,
"afii10084", "tecyrillic", 0,
"afii10085", "ucyrillic", 0,
"afii10086", "efcyrillic", 0,
"afii10087", "khacyrillic", 0,
"afii10088", "tsecyrillic", 0,
"afii10089", "checyrillic", 0,
"afii10090", "shacyrillic", 0,
"afii10091", "shchacyrillic", 0,
"afii10092", "hardsigncyrillic", 0,
"afii10093", "yericyrillic", 0,
"afii10094", "softsigncyrillic", 0,
"afii10095", "ereversedcyrillic", 0,
"afii10096", "iucyrillic", 0,
"afii10097", "iacyrillic", 0,
"afii10071", "iocyrillic", 0,
"afii10099", "djecyrillic", 0,
"afii10100", "gjecyrillic", 0,
"afii10101", "ecyrillic", 0,
"afii10102", "dzecyrillic", 0,
"afii10103", "icyrillic", 0,
"afii10104", "yicyrillic", 0,
"afii10105", "jecyrillic", 0,
"afii10106", "ljecyrillic", 0,
"afii10107", "njecyrillic", 0,
"afii10108", "tshecyrillic", 0,
"afii10109", "kjecyrillic", 0,
"afii10110", "ushortcyrillic", 0,
"afii10193", "dzhecyrillic", 0,
"Yatcyrillic", "afii10146", 0,
"afii10194", "yatcyrillic", 0,
"Fitacyrillic", "afii10147", 0,
"afii10195", "fitacyrillic", 0,
"Izhitsacyrillic", "afii10148", 0,
"afii10196", "izhitsacyrillic", 0,
"Gheupturncyrillic", "afii10050", 0,
"afii10098", "gheupturncyrillic", 0,
"afii10846", "schwacyrillic", 0,
"etnahtafoukhhebrew", "etnahtafoukhlefthebrew", "etnahtahebrew", "etnahtalefthebrew", 0,
"tipehahebrew", "tipehalefthebrew", 0,
"reviahebrew", "reviamugrashhebrew", 0,
"tevirhebrew", "tevirlefthebrew", 0,
"munahhebrew", "munahlefthebrew", 0,
"mahapakhhebrew", "mahapakhlefthebrew", 0,
"merkhahebrew", "merkhalefthebrew", 0,
"merkhakefulahebrew", "merkhakefulalefthebrew", 0,
"dargahebrew", "dargalefthebrew", 0,
"yerahbenyomohebrew", "yerahbenyomolefthebrew", 0,
"afii57799", "sheva", "sheva115", "sheva15", "sheva22", "sheva2e", "shevahebrew", "shevanarrowhebrew", "shevaquarterhebrew", "shevawidehebrew", 0,
"afii57801", "hatafsegol", "hatafsegol17", "hatafsegol24", "hatafsegol30", "hatafsegolhebrew", "hatafsegolnarrowhebrew", "hatafsegolquarterhebrew", "hatafsegolwidehebrew", 0,
"afii57800", "hatafpatah", "hatafpatah16", "hatafpatah23", "hatafpatah2f", "hatafpatahhebrew", "hatafpatahnarrowhebrew", "hatafpatahquarterhebrew", "hatafpatahwidehebrew", 0,
"afii57802", "hatafqamats", "hatafqamats1b", "hatafqamats28", "hatafqamats34", "hatafqamatshebrew", "hatafqamatsnarrowhebrew", "hatafqamatsquarterhebrew", "hatafqamatswidehebrew", 0,
"afii57793", "hiriq", "hiriq14", "hiriq21", "hiriq2d", "hiriqhebrew", "hiriqnarrowhebrew", "hiriqquarterhebrew", "hiriqwidehebrew", 0,
"afii57794", "tsere", "tsere12", "tsere1e", "tsere2b", "tserehebrew", "tserenarrowhebrew", "tserequarterhebrew", "tserewidehebrew", 0,
"afii57795", "segol", "segol13", "segol1f", "segol2c", "segolhebrew", "segolnarrowhebrew", "segolquarterhebrew", "segolwidehebrew", 0,
"afii57798", "patah", "patah11", "patah1d", "patah2a", "patahhebrew", "patahnarrowhebrew", "patahquarterhebrew", "patahwidehebrew", 0,
"afii57797", "qamats", "qamats10", "qamats1a", "qamats1c", "qamats27", "qamats29", "qamats33", "qamatsde", "qamatshebrew", "qamatsnarrowhebrew", "qamatsqatanhebrew", "qamatsqatannarrowhebrew", "qamatsqatanquarterhebrew", "qamatsqatanwidehebrew", "qamatsquarterhebrew", "qamatswidehebrew", 0,
"afii57806", "holam", "holam19", "holam26", "holam32", "holamhebrew", "holamnarrowhebrew", "holamquarterhebrew", "holamwidehebrew", 0,
"afii57796", "qubuts", "qubuts18", "qubuts25", "qubuts31", "qubutshebrew", "qubutsnarrowhebrew", "qubutsquarterhebrew", "qubutswidehebrew", 0,
"afii57807", "dagesh", "dageshhebrew", 0,
"afii57839", "siluqhebrew", "siluqlefthebrew", 0,
"afii57645", "maqafhebrew", 0,
"afii57841", "rafe", "rafehebrew", 0,
"afii57842", "paseqhebrew", 0,
"afii57804", "shindothebrew", 0,
"afii57803", "sindothebrew", 0,
"afii57658", "sofpasuqhebrew", 0,
"afii57664", "alef", "alefhebrew", 0,
"afii57665", "bet", "bethebrew", 0,
"afii57666", "gimel", "gimelhebrew", 0,
"afii57667", "dalet", "dalethebrew", 0,
"afii57668", "he", "hehebrew", 0,
"afii57669", "vav", "vavhebrew", 0,
"afii57670", "zayin", "zayinhebrew", 0,
"afii57671", "het", "hethebrew", 0,
"afii57672", "tet", "tethebrew", 0,
"afii57673", "yod", "yodhebrew", 0,
"afii57674", "finalkaf", "finalkafhebrew", 0,
"afii57675", "kaf", "kafhebrew", 0,
"afii57676", "lamed", "lamedhebrew", 0,
"afii57677", "finalmem", "finalmemhebrew", 0,
"afii57678", "mem", "memhebrew", 0,
"afii57679", "finalnun", "finalnunhebrew", 0,
"afii57680", "nun", "nunhebrew", 0,
"afii57681", "samekh", "samekhhebrew", 0,
"afii57682", "ayin", "ayinhebrew", 0,
"afii57683", "finalpe", "finalpehebrew", 0,
"afii57684", "pe", "pehebrew", 0,
"afii57685", "finaltsadi", "finaltsadihebrew", 0,
"afii57686", "tsadi", "tsadihebrew", 0,
"afii57687", "qof", "qofhebrew", 0,
"afii57688", "resh", "reshhebrew", 0,
"afii57689", "shin", "shinhebrew", 0,
"afii57690", "tav", "tavhebrew", 0,
"afii57716", "vavvavhebrew", 0,
"afii57717", "vavyodhebrew", 0,
"afii57718", "yodyodhebrew", 0,
"afii57388", "commaarabic", 0,
"afii57403", "semicolonarabic", 0,
"afii57407", "questionarabic", 0,
"afii57409", "hamzaarabic", "hamzalowarabic", 0,
"afii57410", "alefmaddaabovearabic", 0,
"afii57411", "alefhamzaabovearabic", 0,
"afii57412", "wawhamzaabovearabic", 0,
"afii57413", "alefhamzabelowarabic", 0,
"afii57414", "yehhamzaabovearabic", 0,
"afii57415", "alefarabic", 0,
"afii57416", "beharabic", 0,
"afii57417", "tehmarbutaarabic", 0,
"afii57418", "teharabic", 0,
"afii57419", "theharabic", 0,
"afii57420", "jeemarabic", 0,
"afii57421", "haharabic", 0,
"afii57422", "khaharabic", 0,
"afii57423", "dalarabic", 0,
"afii57424", "thalarabic", 0,
"afii57425", "reharabic", 0,
"afii57426", "zainarabic", 0,
"afii57427", "seenarabic", 0,
"afii57428", "sheenarabic", 0,
"afii57429", "sadarabic", 0,
"afii57430", "dadarabic", 0,
"afii57431", "taharabic", 0,
"afii57432", "zaharabic", 0,
"afii57433", "ainarabic", 0,
"afii57434", "ghainarabic", 0,
"afii57440", "kashidaautoarabic", "kashidaautonosidebearingarabic", "tatweelarabic", 0,
"afii57441", "feharabic", 0,
"afii57442", "qafarabic", 0,
"afii57443", "kafarabic", 0,
"afii57444", "lamarabic", 0,
"afii57445", "meemarabic", 0,
"afii57446", "noonarabic", 0,
"afii57470", "heharabic", 0,
"afii57448", "wawarabic", 0,
"afii57449", "alefmaksuraarabic", 0,
"afii57450", "yeharabic", 0,
"afii57451", "fathatanarabic", 0,
"afii57452", "dammatanaltonearabic", "dammatanarabic", 0,
"afii57453", "kasratanarabic", 0,
"afii57454", "fathaarabic", "fathalowarabic", 0,
"afii57455", "dammaarabic", "dammalowarabic", 0,
"afii57456", "kasraarabic", 0,
"afii57457", "shaddaarabic", 0,
"afii57458", "sukunarabic", 0,
"afii57392", "zeroarabic", "zerohackarabic", 0,
"afii57393", "onearabic", "onehackarabic", 0,
"afii57394", "twoarabic", "twohackarabic", 0,
"afii57395", "threearabic", "threehackarabic", 0,
"afii57396", "fourarabic", "fourhackarabic", 0,
"afii57397", "fivearabic", "fivehackarabic", 0,
"afii57398", "sixarabic", "sixhackarabic", 0,
"afii57399", "sevenarabic", "sevenhackarabic", 0,
"afii57400", "eightarabic", "eighthackarabic", 0,
"afii57401", "ninearabic", "ninehackarabic", 0,
"afii57381", "percentarabic", 0,
"decimalseparatorarabic", "decimalseparatorpersian", 0,
"thousandsseparatorarabic", "thousandsseparatorpersian", 0,
"afii63167", "asteriskaltonearabic", "asteriskarabic", 0,
"afii57511", "tteharabic", 0,
"afii57506", "peharabic", 0,
"afii57507", "tcheharabic", 0,
"afii57512", "ddalarabic", 0,
"afii57513", "rreharabic", 0,
"afii57508", "jeharabic", 0,
"afii57505", "veharabic", 0,
"afii57509", "gafarabic", 0,
"afii57514", "noonghunnaarabic", 0,
"haaltonearabic", "hehaltonearabic", 0,
"afii57519", "yehbarreearabic", 0,
"afii61664", "zerowidthnonjoiner", "cwm", "compwordmark", 0,
"endash", "rangedash", 0,
"emdash", "punctdash", 0,
"afii00208", "horizontalbar", 0,
"dbllowline", "underscoredbl", 0,
"quoteleftreversed", "quotereversed", 0,
"twodotenleader", "twodotleader", 0,
"minute", "prime", 0,
"primereversed", "primereverse", 0,
"colonmonetary", "colonsign", 0,
"afii08941", "lira", 0,
"afii57636", "newsheqelsign", "sheqel", "sheqelhebrew", 0,
"Euro", "euro", 0,
"afii61248", "careof", 0,
"planckover2pi", "planckover2pi1", 0,
"Ifraktur", "Ifractur", 0,
"afii61289", "lsquare", "lscript", 0,
"afii61352", "numero", 0,
"Rfraktur", "Rfractur", 0,
"Ohm", "Omega", 0,
"arrowupdn", "arrowbothv", 0,
"arrowupleft", "arrownorthwest", 0,
"arrowupright", "arrownortheast", 0,
"arrowdownright", "arrowsoutheast", 0,
"arrowdownleft", "arrowsouthwest", 0,
"arrowupdnbse", "arrowupdownbase", 0,
"harpoonleftbarbup", "arrowlefttophalf", 0,
"harpoonrightbarbup", "arrowrighttophalf", 0,
"arrowrightoverleft", "arrowparrrightleft", 0,
"arrowleftoverright", "arrowparrleftright", 0,
"arrowleftdblstroke", "notdblarrowleft", 0,
"arrowrightdblstroke", "notdblarrowright", 0,
"arrowdblleft", "arrowleftdbl", 0,
"arrowdblright", "dblarrowright", 0,
"arrowdblboth", "dblarrowleft", 0,
"forall", "universal", 0,
"existential", "thereexists", 0,
"Delta", "increment", 0,
"gradient", "nabla", 0,
"notelement", "notelementof", 0,
"suchthat", "owner", 0,
"asteriskmath", "asteriskcentered", 0,
"orthogonal", "rightangle", 0,
"parallel", "bardbl", 0,
"similar", "tildeoperator", 0,
"reversedtilde", "revsimilar", 0,
"asymptoticallyequal", "similarequal", 0,
"approximatelyequal", "congruent", 0,
"geometricallyequal", "equalsdots", 0,
"approxequalorimage", "equaldotleftright", 0,
"imageorapproximatelyequal", "equaldotrightleft", 0,
"lessoverequal", "lessdblequal", 0,
"greateroverequal", "greaterdblequal", 0,
"lessornotdbleql", "lessornotequal", 0,
"greaterornotdbleql", "greaterornotequal", 0,
"muchless", "lessmuch", 0,
"muchgreater", "greatermuch", 0,
"notlessnorequal", "notlessequal", 0,
"notgreaternorequal", "notgreaterequal", 0,
"lessorequivalent", "lessorsimilar", 0,
"greaterorequivalent", "greaterorsimilar", 0,
"succeeds", "follows", 0,
"notsucceeds", "notfollows", 0,
"propersubset", "subset", 0,
"propersuperset", "superset", 0,
"reflexsubset", "subsetorequal", 0,
"reflexsuperset", "supersetorequal", 0,
"subsetnotequal", "notsubsetoreql", "subsetnoteql", 0,
"supersetnotequal", "notsupersetoreql", "supersetnoteql", 0,
"circleplus", "pluscircle", 0,
"minuscircle", "circleminus", 0,
"circlemultiply", "timescircle", 0,
"circleot", "circledot", 0,
"tackleft", "turnstileright", 0,
"tackdown", "latticetop", 0,
"curlyor", "downfall", 0,
"curlyand", "uprise", 0,
"lessequalorgreater", "lessequalgreater", 0,
"greaterequalorless", "greaterlessequal", 0,
"logicalnotreversed", "revlogicalnot", 0,
"integraltop", "integraltp", 0,
"integralbottom", "integralbt", 0,
"slurabove", "frown", 0,
"slurbelow", "smile", 0,
"blank", "visualspace", "visiblespace", 0,
"Scircle", "circleS", 0,
"ltshade", "shadelight", 0,
"shade", "shademedium", 0,
"dkshade", "shadedark", 0,
"blacksquare", "filledbox", "squaresolid", 0,
"H22073", "whitesquare", "square", 0,
"H18543", "blacksmallsquare", 0,
"H18551", "whitesmallsquare", 0,
"blackrectangle", "filledrect", 0,
"blackuppointingtriangle", "triagup", "trianglesolid", 0,
"whiteuppointingtriangle", "triangle", 0,
"blackrightpointingtriangle", "trianglerightsld", 0,
"whiterightpointingtriangle", "triangleright", 0,
"blackrightpointingpointer", "triagrt", 0,
"blackdownpointingtriangle", "triagdn", "triangledownsld", 0,
"whitedownpointingtriangle", "triangleinv", 0,
"blackleftpointingtriangle", "triangleleftsld", 0,
"whiteleftpointingtriangle", "triangleleft", 0,
"blackleftpointingpointer", "triaglf", 0,
"circle", "whitecircle", 0,
"H18533", "blackcircle", 0,
"bulletinverse", "invbullet", 0,
"invcircle", "whitecircleinverse", 0,
"openbullet", "whitebullet", 0,
"smileface", "whitesmilingface", 0,
"blacksmilingface", "invsmileface", 0,
"compass", "sun", 0,
"female", "venus", 0,
"male", "mars", 0,
"spade", "spadesuitblack", 0,
"club", "clubsuitblack", 0,
"heart", "heartsuitblack", 0,
"diamond", "diamondsolid", 0,
"eighthnotebeamed", "musicalnotedbl", 0,
"musicflatsign", "flat", 0,
"musicsharpsign", "sharp", 0,
"checkmark", "check", 0,
"afii57705", "doubleyodpatah", "doubleyodpatahhebrew", "yodyodpatahhebrew", 0,
"afii57694", "shinshindot", "shinshindothebrew", 0,
"afii57695", "shinsindot", "shinsindothebrew", 0,
"shindageshshindot", "shindageshshindothebrew", 0,
"shindageshsindot", "shindageshsindothebrew", 0,
"betdagesh", "betdageshhebrew", 0,
"gimeldagesh", "gimeldageshhebrew", 0,
"daletdagesh", "daletdageshhebrew", 0,
"hedagesh", "hedageshhebrew", 0,
"afii57723", "vavdagesh", "vavdagesh65", "vavdageshhebrew", 0,
"zayindagesh", "zayindageshhebrew", 0,
"tetdagesh", "tetdageshhebrew", 0,
"yoddagesh", "yoddageshhebrew", 0,
"finalkafdagesh", "finalkafdageshhebrew", 0,
"kafdagesh", "kafdageshhebrew", 0,
"lameddagesh", "lameddageshhebrew", 0,
"memdagesh", "memdageshhebrew", 0,
"nundagesh", "nundageshhebrew", 0,
"samekhdagesh", "samekhdageshhebrew", 0,
"pedagesh", "pedageshhebrew", 0,
"tsadidagesh", "tsadidageshhebrew", 0,
"qofdagesh", "qofdageshhebrew", 0,
"shindagesh", "shindageshhebrew", 0,
"tavdages", "tavdagesh", "tavdageshhebrew", 0,
"afii57700", "vavholam", "vavholamhebrew", 0,
"hehfinalalttwoarabic", "hehfinalarabic", 0,
"alefmaksurainitialarabic", "yehinitialarabic", 0,
"alefmaksuramedialarabic", "yehmedialarabic", 0,
};
