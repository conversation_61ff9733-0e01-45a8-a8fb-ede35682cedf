// Copyright (C) 2004-2021 Artifex Software, Inc.
//
// This file is part of MuPDF.
//
// MuPDF is free software: you can redistribute it and/or modify it under the
// terms of the GNU Affero General Public License as published by the Free
// Software Foundation, either version 3 of the License, or (at your option)
// any later version.
//
// MuPDF is distributed in the hope that it will be useful, but WITHOUT ANY
// WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
// FOR A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
// details.
//
// You should have received a copy of the GNU Affero General Public License
// along with MuPDF. If not, see <https://www.gnu.org/licenses/agpl-3.0.en.html>
//
// Alternative licensing terms are available from the licensor.
// For commercial licensing, see <https://www.artifex.com/> or contact
// Artifex Software, Inc., 39 Mesa Street, Suite 108A, San Francisco,
// CA 94129, USA, for further information.

#define _notdef NULL

const unsigned short fz_unicode_from_pdf_doc_encoding[256] =
{
	/* 0x0 to 0x17 except \t, \n and \r are really undefined */
	0x00, 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07,
	0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F,
	0x10, 0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17,
	0x02d8, 0x02c7, 0x02c6, 0x02d9, 0x02dd, 0x02db, 0x02da, 0x02dc,
	0x0020, 0x0021, 0x0022, 0x0023, 0x0024, 0x0025, 0x0026, 0x0027,
	0x0028, 0x0029, 0x002a, 0x002b, 0x002c, 0x002d, 0x002e, 0x002f,
	0x0030, 0x0031, 0x0032, 0x0033, 0x0034, 0x0035, 0x0036, 0x0037,
	0x0038, 0x0039, 0x003a, 0x003b, 0x003c, 0x003d, 0x003e, 0x003f,
	0x0040, 0x0041, 0x0042, 0x0043, 0x0044, 0x0045, 0x0046, 0x0047,
	0x0048, 0x0049, 0x004a, 0x004b, 0x004c, 0x004d, 0x004e, 0x004f,
	0x0050, 0x0051, 0x0052, 0x0053, 0x0054, 0x0055, 0x0056, 0x0057,
	0x0058, 0x0059, 0x005a, 0x005b, 0x005c, 0x005d, 0x005e, 0x005f,
	0x0060, 0x0061, 0x0062, 0x0063, 0x0064, 0x0065, 0x0066, 0x0067,
	0x0068, 0x0069, 0x006a, 0x006b, 0x006c, 0x006d, 0x006e, 0x006f,
	0x0070, 0x0071, 0x0072, 0x0073, 0x0074, 0x0075, 0x0076, 0x0077,
	0x0078, 0x0079, 0x007a, 0x007b, 0x007c, 0x007d, 0x007e, 0x0000,
	0x2022, 0x2020, 0x2021, 0x2026, 0x2014, 0x2013, 0x0192, 0x2044,
	0x2039, 0x203a, 0x2212, 0x2030, 0x201e, 0x201c, 0x201d, 0x2018,
	0x2019, 0x201a, 0x2122, 0xfb01, 0xfb02, 0x0141, 0x0152, 0x0160,
	0x0178, 0x017d, 0x0131, 0x0142, 0x0153, 0x0161, 0x017e, 0x0000,
	0x20ac, 0x00a1, 0x00a2, 0x00a3, 0x00a4, 0x00a5, 0x00a6, 0x00a7,
	0x00a8, 0x00a9, 0x00aa, 0x00ab, 0x00ac, 0x0000, 0x00ae, 0x00af,
	0x00b0, 0x00b1, 0x00b2, 0x00b3, 0x00b4, 0x00b5, 0x00b6, 0x00b7,
	0x00b8, 0x00b9, 0x00ba, 0x00bb, 0x00bc, 0x00bd, 0x00be, 0x00bf,
	0x00c0, 0x00c1, 0x00c2, 0x00c3, 0x00c4, 0x00c5, 0x00c6, 0x00c7,
	0x00c8, 0x00c9, 0x00ca, 0x00cb, 0x00cc, 0x00cd, 0x00ce, 0x00cf,
	0x00d0, 0x00d1, 0x00d2, 0x00d3, 0x00d4, 0x00d5, 0x00d6, 0x00d7,
	0x00d8, 0x00d9, 0x00da, 0x00db, 0x00dc, 0x00dd, 0x00de, 0x00df,
	0x00e0, 0x00e1, 0x00e2, 0x00e3, 0x00e4, 0x00e5, 0x00e6, 0x00e7,
	0x00e8, 0x00e9, 0x00ea, 0x00eb, 0x00ec, 0x00ed, 0x00ee, 0x00ef,
	0x00f0, 0x00f1, 0x00f2, 0x00f3, 0x00f4, 0x00f5, 0x00f6, 0x00f7,
	0x00f8, 0x00f9, 0x00fa, 0x00fb, 0x00fc, 0x00fd, 0x00fe, 0x00ff
};

const char *fz_glyph_name_from_adobe_standard[256] = {
	_notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef,
	_notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef,
	_notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef,
	_notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef,
	"space", "exclam", "quotedbl", "numbersign", "dollar", "percent",
	"ampersand", "quoteright", "parenleft", "parenright", "asterisk",
	"plus", "comma", "hyphen", "period", "slash", "zero", "one", "two",
	"three", "four", "five", "six", "seven", "eight", "nine", "colon",
	"semicolon", "less", "equal", "greater", "question", "at", "A", "B",
	"C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P",
	"Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "bracketleft",
	"backslash", "bracketright", "asciicircum", "underscore", "quoteleft",
	"a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n",
	"o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z",
	"braceleft", "bar", "braceright", "asciitilde", _notdef, _notdef,
	_notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef,
	_notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef,
	_notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef,
	_notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef,
	"exclamdown", "cent", "sterling", "fraction", "yen", "florin",
	"section", "currency", "quotesingle", "quotedblleft", "guillemotleft",
	"guilsinglleft", "guilsinglright", "fi", "fl", _notdef, "endash",
	"dagger", "daggerdbl", "periodcentered", _notdef, "paragraph",
	"bullet", "quotesinglbase", "quotedblbase", "quotedblright",
	"guillemotright", "ellipsis", "perthousand", _notdef, "questiondown",
	_notdef, "grave", "acute", "circumflex", "tilde", "macron", "breve",
	"dotaccent", "dieresis", _notdef, "ring", "cedilla", _notdef,
	"hungarumlaut", "ogonek", "caron", "emdash", _notdef, _notdef, _notdef,
	_notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef,
	_notdef, _notdef, _notdef, _notdef, _notdef, "AE", _notdef,
	"ordfeminine", _notdef, _notdef, _notdef, _notdef, "Lslash", "Oslash",
	"OE", "ordmasculine", _notdef, _notdef, _notdef, _notdef, _notdef,
	"ae", _notdef, _notdef, _notdef, "dotlessi", _notdef, _notdef,
	"lslash", "oslash", "oe", "germandbls", _notdef, _notdef, _notdef,
	_notdef
};

const char *fz_glyph_name_from_mac_roman[256] = {
	_notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef,
	_notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef,
	_notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef,
	_notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef,
	"space", "exclam", "quotedbl", "numbersign", "dollar", "percent",
	"ampersand", "quotesingle", "parenleft", "parenright", "asterisk",
	"plus", "comma", "hyphen", "period", "slash", "zero", "one", "two",
	"three", "four", "five", "six", "seven", "eight", "nine", "colon",
	"semicolon", "less", "equal", "greater", "question", "at", "A", "B",
	"C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P",
	"Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "bracketleft",
	"backslash", "bracketright", "asciicircum", "underscore", "grave", "a",
	"b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o",
	"p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z", "braceleft",
	"bar", "braceright", "asciitilde", _notdef, "Adieresis", "Aring",
	"Ccedilla", "Eacute", "Ntilde", "Odieresis", "Udieresis", "aacute",
	"agrave", "acircumflex", "adieresis", "atilde", "aring", "ccedilla",
	"eacute", "egrave", "ecircumflex", "edieresis", "iacute", "igrave",
	"icircumflex", "idieresis", "ntilde", "oacute", "ograve",
	"ocircumflex", "odieresis", "otilde", "uacute", "ugrave",
	"ucircumflex", "udieresis", "dagger", "degree", "cent", "sterling",
	"section", "bullet", "paragraph", "germandbls", "registered",
	"copyright", "trademark", "acute", "dieresis", _notdef, "AE", "Oslash",
	_notdef, "plusminus", _notdef, _notdef, "yen", "mu", _notdef, _notdef,
	_notdef, _notdef, _notdef, "ordfeminine", "ordmasculine", _notdef,
	"ae", "oslash", "questiondown", "exclamdown", "logicalnot", _notdef,
	"florin", _notdef, _notdef, "guillemotleft", "guillemotright",
	"ellipsis", "space", "Agrave", "Atilde", "Otilde", "OE", "oe",
	"endash", "emdash", "quotedblleft", "quotedblright", "quoteleft",
	"quoteright", "divide", _notdef, "ydieresis", "Ydieresis", "fraction",
	"currency", "guilsinglleft", "guilsinglright", "fi", "fl", "daggerdbl",
	"periodcentered", "quotesinglbase", "quotedblbase", "perthousand",
	"Acircumflex", "Ecircumflex", "Aacute", "Edieresis", "Egrave",
	"Iacute", "Icircumflex", "Idieresis", "Igrave", "Oacute",
	"Ocircumflex", _notdef, "Ograve", "Uacute", "Ucircumflex", "Ugrave",
	"dotlessi", "circumflex", "tilde", "macron", "breve", "dotaccent",
	"ring", "cedilla", "hungarumlaut", "ogonek", "caron"
};

const char *fz_glyph_name_from_mac_expert[256] = {
	_notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef,
	_notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef,
	_notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef,
	_notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef,
	"space", "exclamsmall", "Hungarumlautsmall", "centoldstyle",
	"dollaroldstyle", "dollarsuperior", "ampersandsmall", "Acutesmall",
	"parenleftsuperior", "parenrightsuperior", "twodotenleader",
	"onedotenleader", "comma", "hyphen", "period", "fraction",
	"zerooldstyle", "oneoldstyle", "twooldstyle", "threeoldstyle",
	"fouroldstyle", "fiveoldstyle", "sixoldstyle", "sevenoldstyle",
	"eightoldstyle", "nineoldstyle", "colon", "semicolon", _notdef,
	"threequartersemdash", _notdef, "questionsmall", _notdef, _notdef,
	_notdef, _notdef, "Ethsmall", _notdef, _notdef, "onequarter",
	"onehalf", "threequarters", "oneeighth", "threeeighths", "fiveeighths",
	"seveneighths", "onethird", "twothirds", _notdef, _notdef, _notdef,
	_notdef, _notdef, _notdef, "ff", "fi", "fl", "ffi", "ffl",
	"parenleftinferior", _notdef, "parenrightinferior", "Circumflexsmall",
	"hypheninferior", "Gravesmall", "Asmall", "Bsmall", "Csmall", "Dsmall",
	"Esmall", "Fsmall", "Gsmall", "Hsmall", "Ismall", "Jsmall", "Ksmall",
	"Lsmall", "Msmall", "Nsmall", "Osmall", "Psmall", "Qsmall", "Rsmall",
	"Ssmall", "Tsmall", "Usmall", "Vsmall", "Wsmall", "Xsmall", "Ysmall",
	"Zsmall", "colonmonetary", "onefitted", "rupiah", "Tildesmall",
	_notdef, _notdef, "asuperior", "centsuperior", _notdef, _notdef,
	_notdef, _notdef, "Aacutesmall", "Agravesmall", "Acircumflexsmall",
	"Adieresissmall", "Atildesmall", "Aringsmall", "Ccedillasmall",
	"Eacutesmall", "Egravesmall", "Ecircumflexsmall", "Edieresissmall",
	"Iacutesmall", "Igravesmall", "Icircumflexsmall", "Idieresissmall",
	"Ntildesmall", "Oacutesmall", "Ogravesmall", "Ocircumflexsmall",
	"Odieresissmall", "Otildesmall", "Uacutesmall", "Ugravesmall",
	"Ucircumflexsmall", "Udieresissmall", _notdef, "eightsuperior",
	"fourinferior", "threeinferior", "sixinferior", "eightinferior",
	"seveninferior", "Scaronsmall", _notdef, "centinferior", "twoinferior",
	_notdef, "Dieresissmall", _notdef, "Caronsmall", "osuperior",
	"fiveinferior", _notdef, "commainferior", "periodinferior",
	"Yacutesmall", _notdef, "dollarinferior", _notdef, _notdef,
	"Thornsmall", _notdef, "nineinferior", "zeroinferior", "Zcaronsmall",
	"AEsmall", "Oslashsmall", "questiondownsmall", "oneinferior",
	"Lslashsmall", _notdef, _notdef, _notdef, _notdef, _notdef, _notdef,
	"Cedillasmall", _notdef, _notdef, _notdef, _notdef, _notdef, "OEsmall",
	"figuredash", "hyphensuperior", _notdef, _notdef, _notdef, _notdef,
	"exclamdownsmall", _notdef, "Ydieresissmall", _notdef, "onesuperior",
	"twosuperior", "threesuperior", "foursuperior", "fivesuperior",
	"sixsuperior", "sevensuperior", "ninesuperior", "zerosuperior",
	_notdef, "esuperior", "rsuperior", "tsuperior", _notdef, _notdef,
	"isuperior", "ssuperior", "dsuperior", _notdef, _notdef, _notdef,
	_notdef, _notdef, "lsuperior", "Ogoneksmall", "Brevesmall",
	"Macronsmall", "bsuperior", "nsuperior", "msuperior", "commasuperior",
	"periodsuperior", "Dotaccentsmall", "Ringsmall", _notdef, _notdef,
	_notdef, _notdef
};

/* All unused codes > 32 map to 'bullet' */
const char *fz_glyph_name_from_win_ansi[256] = {
	_notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef,
	_notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef,
	_notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef,
	_notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef, _notdef,
	"space", "exclam", "quotedbl", "numbersign", "dollar", "percent",
	"ampersand", "quotesingle", "parenleft", "parenright", "asterisk",
	"plus", "comma", "hyphen", "period", "slash", "zero", "one", "two",
	"three", "four", "five", "six", "seven", "eight", "nine", "colon",
	"semicolon", "less", "equal", "greater", "question", "at", "A", "B",
	"C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P",
	"Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "bracketleft",
	"backslash", "bracketright", "asciicircum", "underscore", "grave", "a",
	"b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o",
	"p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z", "braceleft",
	"bar", "braceright", "asciitilde", "bullet", "Euro", "bullet",
	"quotesinglbase", "florin", "quotedblbase", "ellipsis", "dagger",
	"daggerdbl", "circumflex", "perthousand", "Scaron", "guilsinglleft",
	"OE", "bullet", "Zcaron", "bullet", "bullet", "quoteleft",
	"quoteright", "quotedblleft", "quotedblright", "bullet", "endash",
	"emdash", "tilde", "trademark", "scaron", "guilsinglright", "oe",
	"bullet", "zcaron", "Ydieresis", "space", "exclamdown", "cent",
	"sterling", "currency", "yen", "brokenbar", "section", "dieresis",
	"copyright", "ordfeminine", "guillemotleft", "logicalnot", "hyphen",
	"registered", "macron", "degree", "plusminus", "twosuperior",
	"threesuperior", "acute", "mu", "paragraph", "periodcentered",
	"cedilla", "onesuperior", "ordmasculine", "guillemotright",
	"onequarter", "onehalf", "threequarters", "questiondown", "Agrave",
	"Aacute", "Acircumflex", "Atilde", "Adieresis", "Aring", "AE",
	"Ccedilla", "Egrave", "Eacute", "Ecircumflex", "Edieresis", "Igrave",
	"Iacute", "Icircumflex", "Idieresis", "Eth", "Ntilde", "Ograve",
	"Oacute", "Ocircumflex", "Otilde", "Odieresis", "multiply", "Oslash",
	"Ugrave", "Uacute", "Ucircumflex", "Udieresis", "Yacute", "Thorn",
	"germandbls", "agrave", "aacute", "acircumflex", "atilde", "adieresis",
	"aring", "ae", "ccedilla", "egrave", "eacute", "ecircumflex",
	"edieresis", "igrave", "iacute", "icircumflex", "idieresis", "eth",
	"ntilde", "ograve", "oacute", "ocircumflex", "otilde", "odieresis",
	"divide", "oslash", "ugrave", "uacute", "ucircumflex", "udieresis",
	"yacute", "thorn", "ydieresis"
};

const unsigned short fz_unicode_from_iso8859_1[256] = {
	0,
	1,
	2,
	3,
	4,
	5,
	6,
	7,
	8,
	9,
	10,
	11,
	12,
	13,
	14,
	15,
	16,
	17,
	18,
	19,
	20,
	21,
	22,
	23,
	24,
	25,
	26,
	27,
	28,
	29,
	30,
	31,
	32,
	33,
	34,
	35,
	36,
	37,
	38,
	39,
	40,
	41,
	42,
	43,
	44,
	45,
	46,
	47,
	48,
	49,
	50,
	51,
	52,
	53,
	54,
	55,
	56,
	57,
	58,
	59,
	60,
	61,
	62,
	63,
	64,
	65,
	66,
	67,
	68,
	69,
	70,
	71,
	72,
	73,
	74,
	75,
	76,
	77,
	78,
	79,
	80,
	81,
	82,
	83,
	84,
	85,
	86,
	87,
	88,
	89,
	90,
	91,
	92,
	93,
	94,
	95,
	96,
	97,
	98,
	99,
	100,
	101,
	102,
	103,
	104,
	105,
	106,
	107,
	108,
	109,
	110,
	111,
	112,
	113,
	114,
	115,
	116,
	117,
	118,
	119,
	120,
	121,
	122,
	123,
	124,
	125,
	126,
	127,
	128,
	129,
	130,
	131,
	132,
	133,
	134,
	135,
	136,
	137,
	138,
	139,
	140,
	141,
	142,
	143,
	144,
	145,
	146,
	147,
	148,
	149,
	150,
	151,
	152,
	153,
	154,
	155,
	156,
	157,
	158,
	159,
	160,
	161,
	162,
	163,
	164,
	165,
	166,
	167,
	168,
	169,
	170,
	171,
	172,
	173,
	174,
	175,
	176,
	177,
	178,
	179,
	180,
	181,
	182,
	183,
	184,
	185,
	186,
	187,
	188,
	189,
	190,
	191,
	192,
	193,
	194,
	195,
	196,
	197,
	198,
	199,
	200,
	201,
	202,
	203,
	204,
	205,
	206,
	207,
	208,
	209,
	210,
	211,
	212,
	213,
	214,
	215,
	216,
	217,
	218,
	219,
	220,
	221,
	222,
	223,
	224,
	225,
	226,
	227,
	228,
	229,
	230,
	231,
	232,
	233,
	234,
	235,
	236,
	237,
	238,
	239,
	240,
	241,
	242,
	243,
	244,
	245,
	246,
	247,
	248,
	249,
	250,
	251,
	252,
	253,
	254,
	255,
};

const char *fz_glyph_name_from_iso8859_1[256] = {
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	"space",
	"exclam",
	"quotedbl",
	"numbersign",
	"dollar",
	"percent",
	"ampersand",
	"quotesingle",
	"parenleft",
	"parenright",
	"asterisk",
	"plus",
	"comma",
	"hyphen",
	"period",
	"slash",
	"zero",
	"one",
	"two",
	"three",
	"four",
	"five",
	"six",
	"seven",
	"eight",
	"nine",
	"colon",
	"semicolon",
	"less",
	"equal",
	"greater",
	"question",
	"at",
	"A",
	"B",
	"C",
	"D",
	"E",
	"F",
	"G",
	"H",
	"I",
	"J",
	"K",
	"L",
	"M",
	"N",
	"O",
	"P",
	"Q",
	"R",
	"S",
	"T",
	"U",
	"V",
	"W",
	"X",
	"Y",
	"Z",
	"bracketleft",
	"backslash",
	"bracketright",
	"asciicircum",
	"underscore",
	"grave",
	"a",
	"b",
	"c",
	"d",
	"e",
	"f",
	"g",
	"h",
	"i",
	"j",
	"k",
	"l",
	"m",
	"n",
	"o",
	"p",
	"q",
	"r",
	"s",
	"t",
	"u",
	"v",
	"w",
	"x",
	"y",
	"z",
	"braceleft",
	"bar",
	"braceright",
	"asciitilde",
	"controlDEL",
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	"nbspace",
	"exclamdown",
	"cent",
	"sterling",
	"currency",
	"yen",
	"brokenbar",
	"section",
	"dieresis",
	"copyright",
	"ordfeminine",
	"guillemotleft",
	"logicalnot",
	"sfthyphen",
	"registered",
	"macron",
	"degree",
	"plusminus",
	"twosuperior",
	"threesuperior",
	"acute",
	"mu",
	"paragraph",
	"periodcentered",
	"cedilla",
	"onesuperior",
	"ordmasculine",
	"guillemotright",
	"onequarter",
	"onehalf",
	"threequarters",
	"questiondown",
	"Agrave",
	"Aacute",
	"Acircumflex",
	"Atilde",
	"Adieresis",
	"Aring",
	"AE",
	"Ccedilla",
	"Egrave",
	"Eacute",
	"Ecircumflex",
	"Edieresis",
	"Igrave",
	"Iacute",
	"Icircumflex",
	"Idieresis",
	"Eth",
	"Ntilde",
	"Ograve",
	"Oacute",
	"Ocircumflex",
	"Otilde",
	"Odieresis",
	"multiply",
	"Oslash",
	"Ugrave",
	"Uacute",
	"Ucircumflex",
	"Udieresis",
	"Yacute",
	"Thorn",
	"germandbls",
	"agrave",
	"aacute",
	"acircumflex",
	"atilde",
	"adieresis",
	"aring",
	"ae",
	"ccedilla",
	"egrave",
	"eacute",
	"ecircumflex",
	"edieresis",
	"igrave",
	"iacute",
	"icircumflex",
	"idieresis",
	"eth",
	"ntilde",
	"ograve",
	"oacute",
	"ocircumflex",
	"otilde",
	"odieresis",
	"divide",
	"oslash",
	"ugrave",
	"uacute",
	"ucircumflex",
	"udieresis",
	"yacute",
	"thorn",
	"ydieresis",
};

static const struct { unsigned short u, c; } iso8859_1_from_unicode[] = {
	{0x00a0,160},
	{0x00a1,161},
	{0x00a2,162},
	{0x00a3,163},
	{0x00a4,164},
	{0x00a5,165},
	{0x00a6,166},
	{0x00a7,167},
	{0x00a8,168},
	{0x00a9,169},
	{0x00aa,170},
	{0x00ab,171},
	{0x00ac,172},
	{0x00ad,173},
	{0x00ae,174},
	{0x00af,175},
	{0x00b0,176},
	{0x00b1,177},
	{0x00b2,178},
	{0x00b3,179},
	{0x00b4,180},
	{0x00b5,181},
	{0x00b6,182},
	{0x00b7,183},
	{0x00b8,184},
	{0x00b9,185},
	{0x00ba,186},
	{0x00bb,187},
	{0x00bc,188},
	{0x00bd,189},
	{0x00be,190},
	{0x00bf,191},
	{0x00c0,192},
	{0x00c1,193},
	{0x00c2,194},
	{0x00c3,195},
	{0x00c4,196},
	{0x00c5,197},
	{0x00c6,198},
	{0x00c7,199},
	{0x00c8,200},
	{0x00c9,201},
	{0x00ca,202},
	{0x00cb,203},
	{0x00cc,204},
	{0x00cd,205},
	{0x00ce,206},
	{0x00cf,207},
	{0x00d0,208},
	{0x00d1,209},
	{0x00d2,210},
	{0x00d3,211},
	{0x00d4,212},
	{0x00d5,213},
	{0x00d6,214},
	{0x00d7,215},
	{0x00d8,216},
	{0x00d9,217},
	{0x00da,218},
	{0x00db,219},
	{0x00dc,220},
	{0x00dd,221},
	{0x00de,222},
	{0x00df,223},
	{0x00e0,224},
	{0x00e1,225},
	{0x00e2,226},
	{0x00e3,227},
	{0x00e4,228},
	{0x00e5,229},
	{0x00e6,230},
	{0x00e7,231},
	{0x00e8,232},
	{0x00e9,233},
	{0x00ea,234},
	{0x00eb,235},
	{0x00ec,236},
	{0x00ed,237},
	{0x00ee,238},
	{0x00ef,239},
	{0x00f0,240},
	{0x00f1,241},
	{0x00f2,242},
	{0x00f3,243},
	{0x00f4,244},
	{0x00f5,245},
	{0x00f6,246},
	{0x00f7,247},
	{0x00f8,248},
	{0x00f9,249},
	{0x00fa,250},
	{0x00fb,251},
	{0x00fc,252},
	{0x00fd,253},
	{0x00fe,254},
	{0x00ff,255},
};

const unsigned short fz_unicode_from_iso8859_7[256] = {
	0,
	1,
	2,
	3,
	4,
	5,
	6,
	7,
	8,
	9,
	10,
	11,
	12,
	13,
	14,
	15,
	16,
	17,
	18,
	19,
	20,
	21,
	22,
	23,
	24,
	25,
	26,
	27,
	28,
	29,
	30,
	31,
	32,
	33,
	34,
	35,
	36,
	37,
	38,
	39,
	40,
	41,
	42,
	43,
	44,
	45,
	46,
	47,
	48,
	49,
	50,
	51,
	52,
	53,
	54,
	55,
	56,
	57,
	58,
	59,
	60,
	61,
	62,
	63,
	64,
	65,
	66,
	67,
	68,
	69,
	70,
	71,
	72,
	73,
	74,
	75,
	76,
	77,
	78,
	79,
	80,
	81,
	82,
	83,
	84,
	85,
	86,
	87,
	88,
	89,
	90,
	91,
	92,
	93,
	94,
	95,
	96,
	97,
	98,
	99,
	100,
	101,
	102,
	103,
	104,
	105,
	106,
	107,
	108,
	109,
	110,
	111,
	112,
	113,
	114,
	115,
	116,
	117,
	118,
	119,
	120,
	121,
	122,
	123,
	124,
	125,
	126,
	127,
	128,
	129,
	130,
	131,
	132,
	133,
	134,
	135,
	136,
	137,
	138,
	139,
	140,
	141,
	142,
	143,
	144,
	145,
	146,
	147,
	148,
	149,
	150,
	151,
	152,
	153,
	154,
	155,
	156,
	157,
	158,
	159,
	160,
	8216,
	8217,
	163,
	8364,
	8367,
	166,
	167,
	168,
	169,
	890,
	171,
	172,
	173,
	0,
	8213,
	176,
	177,
	178,
	179,
	900,
	901,
	902,
	183,
	904,
	905,
	906,
	187,
	908,
	189,
	910,
	911,
	912,
	913,
	914,
	915,
	916,
	917,
	918,
	919,
	920,
	921,
	922,
	923,
	924,
	925,
	926,
	927,
	928,
	929,
	0,
	931,
	932,
	933,
	934,
	935,
	936,
	937,
	938,
	939,
	940,
	941,
	942,
	943,
	944,
	945,
	946,
	947,
	948,
	949,
	950,
	951,
	952,
	953,
	954,
	955,
	956,
	957,
	958,
	959,
	960,
	961,
	962,
	963,
	964,
	965,
	966,
	967,
	968,
	969,
	970,
	971,
	972,
	973,
	974,
	0,
};

const char *fz_glyph_name_from_iso8859_7[256] = {
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	"space",
	"exclam",
	"quotedbl",
	"numbersign",
	"dollar",
	"percent",
	"ampersand",
	"quotesingle",
	"parenleft",
	"parenright",
	"asterisk",
	"plus",
	"comma",
	"hyphen",
	"period",
	"slash",
	"zero",
	"one",
	"two",
	"three",
	"four",
	"five",
	"six",
	"seven",
	"eight",
	"nine",
	"colon",
	"semicolon",
	"less",
	"equal",
	"greater",
	"question",
	"at",
	"A",
	"B",
	"C",
	"D",
	"E",
	"F",
	"G",
	"H",
	"I",
	"J",
	"K",
	"L",
	"M",
	"N",
	"O",
	"P",
	"Q",
	"R",
	"S",
	"T",
	"U",
	"V",
	"W",
	"X",
	"Y",
	"Z",
	"bracketleft",
	"backslash",
	"bracketright",
	"asciicircum",
	"underscore",
	"grave",
	"a",
	"b",
	"c",
	"d",
	"e",
	"f",
	"g",
	"h",
	"i",
	"j",
	"k",
	"l",
	"m",
	"n",
	"o",
	"p",
	"q",
	"r",
	"s",
	"t",
	"u",
	"v",
	"w",
	"x",
	"y",
	"z",
	"braceleft",
	"bar",
	"braceright",
	"asciitilde",
	"controlDEL",
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	"nbspace",
	"quoteleft",
	"quoteright",
	"sterling",
	"Euro",
	_notdef,
	"brokenbar",
	"section",
	"dieresis",
	"copyright",
	"ypogegrammeni",
	"guillemotleft",
	"logicalnot",
	"sfthyphen",
	_notdef,
	"afii00208",
	"degree",
	"plusminus",
	"twosuperior",
	"threesuperior",
	"tonos",
	"dialytikatonos",
	"Alphatonos",
	"periodcentered",
	"Epsilontonos",
	"Etatonos",
	"Iotatonos",
	"guillemotright",
	"Omicrontonos",
	"onehalf",
	"Upsilontonos",
	"Omegatonos",
	"iotadieresistonos",
	"Alpha",
	"Beta",
	"Gamma",
	"Deltagreek",
	"Epsilon",
	"Zeta",
	"Eta",
	"Theta",
	"Iota",
	"Kappa",
	"Lambda",
	"Mu",
	"Nu",
	"Xi",
	"Omicron",
	"Pi",
	"Rho",
	_notdef,
	"Sigma",
	"Tau",
	"Upsilon",
	"Phi",
	"Chi",
	"Psi",
	"Omegagreek",
	"Iotadieresis",
	"Upsilondieresis",
	"alphatonos",
	"epsilontonos",
	"etatonos",
	"iotatonos",
	"upsilondieresistonos",
	"alpha",
	"beta",
	"gamma",
	"delta",
	"epsilon",
	"zeta",
	"eta",
	"theta",
	"iota",
	"kappa",
	"lambda",
	"mugreek",
	"nu",
	"xi",
	"omicron",
	"pi",
	"rho",
	"sigma1",
	"sigma",
	"tau",
	"upsilon",
	"phi",
	"chi",
	"psi",
	"omega",
	"iotadieresis",
	"upsilondieresis",
	"omicrontonos",
	"upsilontonos",
	"omegatonos",
	_notdef,
};

static const struct { unsigned short u, c; } iso8859_7_from_unicode[] = {
	{0x00a0,160},
	{0x00a3,163},
	{0x00a6,166},
	{0x00a7,167},
	{0x00a8,168},
	{0x00a9,169},
	{0x00ab,171},
	{0x00ac,172},
	{0x00ad,173},
	{0x00b0,176},
	{0x00b1,177},
	{0x00b2,178},
	{0x00b3,179},
	{0x00b7,183},
	{0x00bb,187},
	{0x00bd,189},
	{0x037a,170},
	{0x0384,180},
	{0x0385,181},
	{0x0386,182},
	{0x0388,184},
	{0x0389,185},
	{0x038a,186},
	{0x038c,188},
	{0x038e,190},
	{0x038f,191},
	{0x0390,192},
	{0x0391,193},
	{0x0392,194},
	{0x0393,195},
	{0x0394,196},
	{0x0395,197},
	{0x0396,198},
	{0x0397,199},
	{0x0398,200},
	{0x0399,201},
	{0x039a,202},
	{0x039b,203},
	{0x039c,204},
	{0x039d,205},
	{0x039e,206},
	{0x039f,207},
	{0x03a0,208},
	{0x03a1,209},
	{0x03a3,211},
	{0x03a4,212},
	{0x03a5,213},
	{0x03a6,214},
	{0x03a7,215},
	{0x03a8,216},
	{0x03a9,217},
	{0x03aa,218},
	{0x03ab,219},
	{0x03ac,220},
	{0x03ad,221},
	{0x03ae,222},
	{0x03af,223},
	{0x03b0,224},
	{0x03b1,225},
	{0x03b2,226},
	{0x03b3,227},
	{0x03b4,228},
	{0x03b5,229},
	{0x03b6,230},
	{0x03b7,231},
	{0x03b8,232},
	{0x03b9,233},
	{0x03ba,234},
	{0x03bb,235},
	{0x03bc,236},
	{0x03bd,237},
	{0x03be,238},
	{0x03bf,239},
	{0x03c0,240},
	{0x03c1,241},
	{0x03c2,242},
	{0x03c3,243},
	{0x03c4,244},
	{0x03c5,245},
	{0x03c6,246},
	{0x03c7,247},
	{0x03c8,248},
	{0x03c9,249},
	{0x03ca,250},
	{0x03cb,251},
	{0x03cc,252},
	{0x03cd,253},
	{0x03ce,254},
	{0x2015,175},
	{0x2018,161},
	{0x2019,162},
	{0x20ac,164},
};

const unsigned short fz_unicode_from_koi8u[256] = {
	0,
	1,
	2,
	3,
	4,
	5,
	6,
	7,
	8,
	9,
	10,
	11,
	12,
	13,
	14,
	15,
	16,
	17,
	18,
	19,
	20,
	21,
	22,
	23,
	24,
	25,
	26,
	27,
	28,
	29,
	30,
	31,
	32,
	33,
	34,
	35,
	36,
	37,
	38,
	39,
	40,
	41,
	42,
	43,
	44,
	45,
	46,
	47,
	48,
	49,
	50,
	51,
	52,
	53,
	54,
	55,
	56,
	57,
	58,
	59,
	60,
	61,
	62,
	63,
	64,
	65,
	66,
	67,
	68,
	69,
	70,
	71,
	72,
	73,
	74,
	75,
	76,
	77,
	78,
	79,
	80,
	81,
	82,
	83,
	84,
	85,
	86,
	87,
	88,
	89,
	90,
	91,
	92,
	93,
	94,
	95,
	96,
	97,
	98,
	99,
	100,
	101,
	102,
	103,
	104,
	105,
	106,
	107,
	108,
	109,
	110,
	111,
	112,
	113,
	114,
	115,
	116,
	117,
	118,
	119,
	120,
	121,
	122,
	123,
	124,
	125,
	126,
	127,
	9472,
	9474,
	9484,
	9488,
	9492,
	9496,
	9500,
	9508,
	9516,
	9524,
	9532,
	9600,
	9604,
	9608,
	9612,
	9616,
	9617,
	9618,
	9619,
	8992,
	9632,
	8729,
	8730,
	8776,
	8804,
	8805,
	160,
	8993,
	176,
	178,
	183,
	247,
	9552,
	9553,
	9554,
	1105,
	1108,
	9556,
	1110,
	1111,
	9559,
	9560,
	9561,
	9562,
	9563,
	1169,
	9565,
	9566,
	9567,
	9568,
	9569,
	1025,
	1028,
	9571,
	1030,
	1031,
	9574,
	9575,
	9576,
	9577,
	9578,
	1168,
	9580,
	169,
	1102,
	1072,
	1073,
	1094,
	1076,
	1077,
	1092,
	1075,
	1093,
	1080,
	1081,
	1082,
	1083,
	1084,
	1085,
	1086,
	1087,
	1103,
	1088,
	1089,
	1090,
	1091,
	1078,
	1074,
	1100,
	1099,
	1079,
	1096,
	1101,
	1097,
	1095,
	1098,
	1070,
	1040,
	1041,
	1062,
	1044,
	1045,
	1060,
	1043,
	1061,
	1048,
	1049,
	1050,
	1051,
	1052,
	1053,
	1054,
	1055,
	1071,
	1056,
	1057,
	1058,
	1059,
	1046,
	1042,
	1068,
	1067,
	1047,
	1064,
	1069,
	1065,
	1063,
	1066,
};

const char *fz_glyph_name_from_koi8u[256] = {
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	"space",
	"exclam",
	"quotedbl",
	"numbersign",
	"dollar",
	"percent",
	"ampersand",
	"quotesingle",
	"parenleft",
	"parenright",
	"asterisk",
	"plus",
	"comma",
	"hyphen",
	"period",
	"slash",
	"zero",
	"one",
	"two",
	"three",
	"four",
	"five",
	"six",
	"seven",
	"eight",
	"nine",
	"colon",
	"semicolon",
	"less",
	"equal",
	"greater",
	"question",
	"at",
	"A",
	"B",
	"C",
	"D",
	"E",
	"F",
	"G",
	"H",
	"I",
	"J",
	"K",
	"L",
	"M",
	"N",
	"O",
	"P",
	"Q",
	"R",
	"S",
	"T",
	"U",
	"V",
	"W",
	"X",
	"Y",
	"Z",
	"bracketleft",
	"backslash",
	"bracketright",
	"asciicircum",
	"underscore",
	"grave",
	"a",
	"b",
	"c",
	"d",
	"e",
	"f",
	"g",
	"h",
	"i",
	"j",
	"k",
	"l",
	"m",
	"n",
	"o",
	"p",
	"q",
	"r",
	"s",
	"t",
	"u",
	"v",
	"w",
	"x",
	"y",
	"z",
	"braceleft",
	"bar",
	"braceright",
	"asciitilde",
	"controlDEL",
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	"upblock",
	"dnblock",
	"block",
	"lfblock",
	"rtblock",
	"ltshade",
	"shade",
	"dkshade",
	"integraltop",
	"blacksquare",
	"bulletoperator",
	"radical",
	"approxequal",
	"lessequal",
	"greaterequal",
	"nbspace",
	"integralbottom",
	"degree",
	"twosuperior",
	"periodcentered",
	"divide",
	_notdef,
	_notdef,
	_notdef,
	"afii10071",
	"afii10101",
	_notdef,
	"afii10103",
	"afii10104",
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	"afii10098",
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	"Iocyrillic",
	"Ecyrillic",
	_notdef,
	"Icyrillic",
	"Yicyrillic",
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	"Gheupturncyrillic",
	_notdef,
	"copyright",
	"afii10096",
	"acyrillic",
	"afii10066",
	"afii10088",
	"afii10069",
	"afii10070",
	"afii10086",
	"afii10068",
	"afii10087",
	"afii10074",
	"afii10075",
	"afii10076",
	"afii10077",
	"afii10078",
	"afii10079",
	"afii10080",
	"afii10081",
	"afii10097",
	"afii10082",
	"afii10083",
	"afii10084",
	"afii10085",
	"afii10072",
	"afii10067",
	"afii10094",
	"afii10093",
	"afii10073",
	"afii10090",
	"afii10095",
	"afii10091",
	"afii10089",
	"afii10092",
	"IUcyrillic",
	"Acyrillic",
	"Becyrillic",
	"Tsecyrillic",
	"Decyrillic",
	"Iecyrillic",
	"Efcyrillic",
	"Gecyrillic",
	"Khacyrillic",
	"Iicyrillic",
	"Iishortcyrillic",
	"Kacyrillic",
	"Elcyrillic",
	"Emcyrillic",
	"Encyrillic",
	"Ocyrillic",
	"Pecyrillic",
	"IAcyrillic",
	"Ercyrillic",
	"Escyrillic",
	"Tecyrillic",
	"Ucyrillic",
	"Zhecyrillic",
	"Vecyrillic",
	"Softsigncyrillic",
	"Yericyrillic",
	"Zecyrillic",
	"Shacyrillic",
	"Ereversedcyrillic",
	"Shchacyrillic",
	"Checyrillic",
	"Hardsigncyrillic",
};

static const struct { unsigned short u, c; } koi8u_from_unicode[] = {
	{0x00a0,154},
	{0x00a9,191},
	{0x00b0,156},
	{0x00b2,157},
	{0x00b7,158},
	{0x00f7,159},
	{0x0401,179},
	{0x0404,180},
	{0x0406,182},
	{0x0407,183},
	{0x0410,225},
	{0x0411,226},
	{0x0412,247},
	{0x0413,231},
	{0x0414,228},
	{0x0415,229},
	{0x0416,246},
	{0x0417,250},
	{0x0418,233},
	{0x0419,234},
	{0x041a,235},
	{0x041b,236},
	{0x041c,237},
	{0x041d,238},
	{0x041e,239},
	{0x041f,240},
	{0x0420,242},
	{0x0421,243},
	{0x0422,244},
	{0x0423,245},
	{0x0424,230},
	{0x0425,232},
	{0x0426,227},
	{0x0427,254},
	{0x0428,251},
	{0x0429,253},
	{0x042a,255},
	{0x042b,249},
	{0x042c,248},
	{0x042d,252},
	{0x042e,224},
	{0x042f,241},
	{0x0430,193},
	{0x0431,194},
	{0x0432,215},
	{0x0433,199},
	{0x0434,196},
	{0x0435,197},
	{0x0436,214},
	{0x0437,218},
	{0x0438,201},
	{0x0439,202},
	{0x043a,203},
	{0x043b,204},
	{0x043c,205},
	{0x043d,206},
	{0x043e,207},
	{0x043f,208},
	{0x0440,210},
	{0x0441,211},
	{0x0442,212},
	{0x0443,213},
	{0x0444,198},
	{0x0445,200},
	{0x0446,195},
	{0x0447,222},
	{0x0448,219},
	{0x0449,221},
	{0x044a,223},
	{0x044b,217},
	{0x044c,216},
	{0x044d,220},
	{0x044e,192},
	{0x044f,209},
	{0x0451,163},
	{0x0454,164},
	{0x0456,166},
	{0x0457,167},
	{0x0490,189},
	{0x0491,173},
	{0x2219,149},
	{0x221a,150},
	{0x2248,151},
	{0x2264,152},
	{0x2265,153},
	{0x2320,147},
	{0x2321,155},
	{0x2580,139},
	{0x2584,140},
	{0x2588,141},
	{0x258c,142},
	{0x2590,143},
	{0x2591,144},
	{0x2592,145},
	{0x2593,146},
	{0x25a0,148},
};

const unsigned short fz_unicode_from_windows_1250[256] = {
	0,
	1,
	2,
	3,
	4,
	5,
	6,
	7,
	8,
	9,
	10,
	11,
	12,
	13,
	14,
	15,
	16,
	17,
	18,
	19,
	20,
	21,
	22,
	23,
	24,
	25,
	26,
	27,
	28,
	29,
	30,
	31,
	32,
	33,
	34,
	35,
	36,
	37,
	38,
	39,
	40,
	41,
	42,
	43,
	44,
	45,
	46,
	47,
	48,
	49,
	50,
	51,
	52,
	53,
	54,
	55,
	56,
	57,
	58,
	59,
	60,
	61,
	62,
	63,
	64,
	65,
	66,
	67,
	68,
	69,
	70,
	71,
	72,
	73,
	74,
	75,
	76,
	77,
	78,
	79,
	80,
	81,
	82,
	83,
	84,
	85,
	86,
	87,
	88,
	89,
	90,
	91,
	92,
	93,
	94,
	95,
	96,
	97,
	98,
	99,
	100,
	101,
	102,
	103,
	104,
	105,
	106,
	107,
	108,
	109,
	110,
	111,
	112,
	113,
	114,
	115,
	116,
	117,
	118,
	119,
	120,
	121,
	122,
	123,
	124,
	125,
	126,
	127,
	8364,
	0,
	8218,
	0,
	8222,
	8230,
	8224,
	8225,
	0,
	8240,
	352,
	8249,
	346,
	356,
	381,
	377,
	0,
	8216,
	8217,
	8220,
	8221,
	8226,
	8211,
	8212,
	0,
	8482,
	353,
	8250,
	347,
	357,
	382,
	378,
	160,
	711,
	728,
	321,
	164,
	260,
	166,
	167,
	168,
	169,
	350,
	171,
	172,
	173,
	174,
	379,
	176,
	177,
	731,
	322,
	180,
	181,
	182,
	183,
	184,
	261,
	351,
	187,
	317,
	733,
	318,
	380,
	340,
	193,
	194,
	258,
	196,
	313,
	262,
	199,
	268,
	201,
	280,
	203,
	282,
	205,
	206,
	270,
	272,
	323,
	327,
	211,
	212,
	336,
	214,
	215,
	344,
	366,
	218,
	368,
	220,
	221,
	354,
	223,
	341,
	225,
	226,
	259,
	228,
	314,
	263,
	231,
	269,
	233,
	281,
	235,
	283,
	237,
	238,
	271,
	273,
	324,
	328,
	243,
	244,
	337,
	246,
	247,
	345,
	367,
	250,
	369,
	252,
	253,
	355,
	729,
};

const char *fz_glyph_name_from_windows_1250[256] = {
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	"space",
	"exclam",
	"quotedbl",
	"numbersign",
	"dollar",
	"percent",
	"ampersand",
	"quotesingle",
	"parenleft",
	"parenright",
	"asterisk",
	"plus",
	"comma",
	"hyphen",
	"period",
	"slash",
	"zero",
	"one",
	"two",
	"three",
	"four",
	"five",
	"six",
	"seven",
	"eight",
	"nine",
	"colon",
	"semicolon",
	"less",
	"equal",
	"greater",
	"question",
	"at",
	"A",
	"B",
	"C",
	"D",
	"E",
	"F",
	"G",
	"H",
	"I",
	"J",
	"K",
	"L",
	"M",
	"N",
	"O",
	"P",
	"Q",
	"R",
	"S",
	"T",
	"U",
	"V",
	"W",
	"X",
	"Y",
	"Z",
	"bracketleft",
	"backslash",
	"bracketright",
	"asciicircum",
	"underscore",
	"grave",
	"a",
	"b",
	"c",
	"d",
	"e",
	"f",
	"g",
	"h",
	"i",
	"j",
	"k",
	"l",
	"m",
	"n",
	"o",
	"p",
	"q",
	"r",
	"s",
	"t",
	"u",
	"v",
	"w",
	"x",
	"y",
	"z",
	"braceleft",
	"bar",
	"braceright",
	"asciitilde",
	"controlDEL",
	"Euro",
	_notdef,
	"quotesinglbase",
	_notdef,
	"quotedblbase",
	"ellipsis",
	"dagger",
	"daggerdbl",
	_notdef,
	"perthousand",
	"Scaron",
	"guilsinglleft",
	"Sacute",
	"Tcaron",
	"Zcaron",
	"Zacute",
	_notdef,
	"quoteleft",
	"quoteright",
	"quotedblleft",
	"quotedblright",
	"bullet",
	"endash",
	"emdash",
	_notdef,
	"trademark",
	"scaron",
	"guilsinglright",
	"sacute",
	"tcaron",
	"zcaron",
	"zacute",
	"nbspace",
	"caron",
	"breve",
	"Lslash",
	"currency",
	"Aogonek",
	"brokenbar",
	"section",
	"dieresis",
	"copyright",
	"Scedilla",
	"guillemotleft",
	"logicalnot",
	"sfthyphen",
	"registered",
	"Zdot",
	"degree",
	"plusminus",
	"ogonek",
	"lslash",
	"acute",
	"mu",
	"paragraph",
	"periodcentered",
	"cedilla",
	"aogonek",
	"scedilla",
	"guillemotright",
	"Lcaron",
	"hungarumlaut",
	"lcaron",
	"zdot",
	"Racute",
	"Aacute",
	"Acircumflex",
	"Abreve",
	"Adieresis",
	"Lacute",
	"Cacute",
	"Ccedilla",
	"Ccaron",
	"Eacute",
	"Eogonek",
	"Edieresis",
	"Ecaron",
	"Iacute",
	"Icircumflex",
	"Dcaron",
	"Dcroat",
	"Nacute",
	"Ncaron",
	"Oacute",
	"Ocircumflex",
	"Odblacute",
	"Odieresis",
	"multiply",
	"Rcaron",
	"Uring",
	"Uacute",
	"Udblacute",
	"Udieresis",
	"Yacute",
	"Tcedilla",
	"germandbls",
	"racute",
	"aacute",
	"acircumflex",
	"abreve",
	"adieresis",
	"lacute",
	"cacute",
	"ccedilla",
	"ccaron",
	"eacute",
	"eogonek",
	"edieresis",
	"ecaron",
	"iacute",
	"icircumflex",
	"dcaron",
	"dcroat",
	"nacute",
	"ncaron",
	"oacute",
	"ocircumflex",
	"odblacute",
	"odieresis",
	"divide",
	"rcaron",
	"uring",
	"uacute",
	"udblacute",
	"udieresis",
	"yacute",
	"tcedilla",
	"dotaccent",
};

static const struct { unsigned short u, c; } windows_1250_from_unicode[] = {
	{0x00a0,160},
	{0x00a4,164},
	{0x00a6,166},
	{0x00a7,167},
	{0x00a8,168},
	{0x00a9,169},
	{0x00ab,171},
	{0x00ac,172},
	{0x00ad,173},
	{0x00ae,174},
	{0x00b0,176},
	{0x00b1,177},
	{0x00b4,180},
	{0x00b5,181},
	{0x00b6,182},
	{0x00b7,183},
	{0x00b8,184},
	{0x00bb,187},
	{0x00c1,193},
	{0x00c2,194},
	{0x00c4,196},
	{0x00c7,199},
	{0x00c9,201},
	{0x00cb,203},
	{0x00cd,205},
	{0x00ce,206},
	{0x00d3,211},
	{0x00d4,212},
	{0x00d6,214},
	{0x00d7,215},
	{0x00da,218},
	{0x00dc,220},
	{0x00dd,221},
	{0x00df,223},
	{0x00e1,225},
	{0x00e2,226},
	{0x00e4,228},
	{0x00e7,231},
	{0x00e9,233},
	{0x00eb,235},
	{0x00ed,237},
	{0x00ee,238},
	{0x00f3,243},
	{0x00f4,244},
	{0x00f6,246},
	{0x00f7,247},
	{0x00fa,250},
	{0x00fc,252},
	{0x00fd,253},
	{0x0102,195},
	{0x0103,227},
	{0x0104,165},
	{0x0105,185},
	{0x0106,198},
	{0x0107,230},
	{0x010c,200},
	{0x010d,232},
	{0x010e,207},
	{0x010f,239},
	{0x0110,208},
	{0x0111,240},
	{0x0118,202},
	{0x0119,234},
	{0x011a,204},
	{0x011b,236},
	{0x0139,197},
	{0x013a,229},
	{0x013d,188},
	{0x013e,190},
	{0x0141,163},
	{0x0142,179},
	{0x0143,209},
	{0x0144,241},
	{0x0147,210},
	{0x0148,242},
	{0x0150,213},
	{0x0151,245},
	{0x0154,192},
	{0x0155,224},
	{0x0158,216},
	{0x0159,248},
	{0x015a,140},
	{0x015b,156},
	{0x015e,170},
	{0x015f,186},
	{0x0160,138},
	{0x0161,154},
	{0x0162,222},
	{0x0163,254},
	{0x0164,141},
	{0x0165,157},
	{0x016e,217},
	{0x016f,249},
	{0x0170,219},
	{0x0171,251},
	{0x0179,143},
	{0x017a,159},
	{0x017b,175},
	{0x017c,191},
	{0x017d,142},
	{0x017e,158},
	{0x02c7,161},
	{0x02d8,162},
	{0x02d9,255},
	{0x02db,178},
	{0x02dd,189},
	{0x2013,150},
	{0x2014,151},
	{0x2018,145},
	{0x2019,146},
	{0x201a,130},
	{0x201c,147},
	{0x201d,148},
	{0x201e,132},
	{0x2020,134},
	{0x2021,135},
	{0x2022,149},
	{0x2026,133},
	{0x2030,137},
	{0x2039,139},
	{0x203a,155},
	{0x20ac,128},
	{0x2122,153},
};

const unsigned short fz_unicode_from_windows_1251[256] = {
	0,
	1,
	2,
	3,
	4,
	5,
	6,
	7,
	8,
	9,
	10,
	11,
	12,
	13,
	14,
	15,
	16,
	17,
	18,
	19,
	20,
	21,
	22,
	23,
	24,
	25,
	26,
	27,
	28,
	29,
	30,
	31,
	32,
	33,
	34,
	35,
	36,
	37,
	38,
	39,
	40,
	41,
	42,
	43,
	44,
	45,
	46,
	47,
	48,
	49,
	50,
	51,
	52,
	53,
	54,
	55,
	56,
	57,
	58,
	59,
	60,
	61,
	62,
	63,
	64,
	65,
	66,
	67,
	68,
	69,
	70,
	71,
	72,
	73,
	74,
	75,
	76,
	77,
	78,
	79,
	80,
	81,
	82,
	83,
	84,
	85,
	86,
	87,
	88,
	89,
	90,
	91,
	92,
	93,
	94,
	95,
	96,
	97,
	98,
	99,
	100,
	101,
	102,
	103,
	104,
	105,
	106,
	107,
	108,
	109,
	110,
	111,
	112,
	113,
	114,
	115,
	116,
	117,
	118,
	119,
	120,
	121,
	122,
	123,
	124,
	125,
	126,
	127,
	1026,
	1027,
	8218,
	1107,
	8222,
	8230,
	8224,
	8225,
	8364,
	8240,
	1033,
	8249,
	1034,
	1036,
	1035,
	1039,
	1106,
	8216,
	8217,
	8220,
	8221,
	8226,
	8211,
	8212,
	0,
	8482,
	1113,
	8250,
	1114,
	1116,
	1115,
	1119,
	160,
	1038,
	1118,
	1032,
	164,
	1168,
	166,
	167,
	1025,
	169,
	1028,
	171,
	172,
	173,
	174,
	1031,
	176,
	177,
	1030,
	1110,
	1169,
	181,
	182,
	183,
	1105,
	8470,
	1108,
	187,
	1112,
	1029,
	1109,
	1111,
	1040,
	1041,
	1042,
	1043,
	1044,
	1045,
	1046,
	1047,
	1048,
	1049,
	1050,
	1051,
	1052,
	1053,
	1054,
	1055,
	1056,
	1057,
	1058,
	1059,
	1060,
	1061,
	1062,
	1063,
	1064,
	1065,
	1066,
	1067,
	1068,
	1069,
	1070,
	1071,
	1072,
	1073,
	1074,
	1075,
	1076,
	1077,
	1078,
	1079,
	1080,
	1081,
	1082,
	1083,
	1084,
	1085,
	1086,
	1087,
	1088,
	1089,
	1090,
	1091,
	1092,
	1093,
	1094,
	1095,
	1096,
	1097,
	1098,
	1099,
	1100,
	1101,
	1102,
	1103,
};

const char *fz_glyph_name_from_windows_1251[256] = {
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	"space",
	"exclam",
	"quotedbl",
	"numbersign",
	"dollar",
	"percent",
	"ampersand",
	"quotesingle",
	"parenleft",
	"parenright",
	"asterisk",
	"plus",
	"comma",
	"hyphen",
	"period",
	"slash",
	"zero",
	"one",
	"two",
	"three",
	"four",
	"five",
	"six",
	"seven",
	"eight",
	"nine",
	"colon",
	"semicolon",
	"less",
	"equal",
	"greater",
	"question",
	"at",
	"A",
	"B",
	"C",
	"D",
	"E",
	"F",
	"G",
	"H",
	"I",
	"J",
	"K",
	"L",
	"M",
	"N",
	"O",
	"P",
	"Q",
	"R",
	"S",
	"T",
	"U",
	"V",
	"W",
	"X",
	"Y",
	"Z",
	"bracketleft",
	"backslash",
	"bracketright",
	"asciicircum",
	"underscore",
	"grave",
	"a",
	"b",
	"c",
	"d",
	"e",
	"f",
	"g",
	"h",
	"i",
	"j",
	"k",
	"l",
	"m",
	"n",
	"o",
	"p",
	"q",
	"r",
	"s",
	"t",
	"u",
	"v",
	"w",
	"x",
	"y",
	"z",
	"braceleft",
	"bar",
	"braceright",
	"asciitilde",
	"controlDEL",
	"Djecyrillic",
	"Gjecyrillic",
	"quotesinglbase",
	"afii10100",
	"quotedblbase",
	"ellipsis",
	"dagger",
	"daggerdbl",
	"Euro",
	"perthousand",
	"Ljecyrillic",
	"guilsinglleft",
	"Njecyrillic",
	"Kjecyrillic",
	"Tshecyrillic",
	"Dzhecyrillic",
	"afii10099",
	"quoteleft",
	"quoteright",
	"quotedblleft",
	"quotedblright",
	"bullet",
	"endash",
	"emdash",
	_notdef,
	"trademark",
	"afii10106",
	"guilsinglright",
	"afii10107",
	"afii10109",
	"afii10108",
	"afii10193",
	"nbspace",
	"Ushortcyrillic",
	"afii10110",
	"Jecyrillic",
	"currency",
	"Gheupturncyrillic",
	"brokenbar",
	"section",
	"Iocyrillic",
	"copyright",
	"Ecyrillic",
	"guillemotleft",
	"logicalnot",
	"sfthyphen",
	"registered",
	"Yicyrillic",
	"degree",
	"plusminus",
	"Icyrillic",
	"afii10103",
	"afii10098",
	"mu",
	"paragraph",
	"periodcentered",
	"afii10071",
	"afii61352",
	"afii10101",
	"guillemotright",
	"afii10105",
	"Dzecyrillic",
	"afii10102",
	"afii10104",
	"Acyrillic",
	"Becyrillic",
	"Vecyrillic",
	"Gecyrillic",
	"Decyrillic",
	"Iecyrillic",
	"Zhecyrillic",
	"Zecyrillic",
	"Iicyrillic",
	"Iishortcyrillic",
	"Kacyrillic",
	"Elcyrillic",
	"Emcyrillic",
	"Encyrillic",
	"Ocyrillic",
	"Pecyrillic",
	"Ercyrillic",
	"Escyrillic",
	"Tecyrillic",
	"Ucyrillic",
	"Efcyrillic",
	"Khacyrillic",
	"Tsecyrillic",
	"Checyrillic",
	"Shacyrillic",
	"Shchacyrillic",
	"Hardsigncyrillic",
	"Yericyrillic",
	"Softsigncyrillic",
	"Ereversedcyrillic",
	"IUcyrillic",
	"IAcyrillic",
	"acyrillic",
	"afii10066",
	"afii10067",
	"afii10068",
	"afii10069",
	"afii10070",
	"afii10072",
	"afii10073",
	"afii10074",
	"afii10075",
	"afii10076",
	"afii10077",
	"afii10078",
	"afii10079",
	"afii10080",
	"afii10081",
	"afii10082",
	"afii10083",
	"afii10084",
	"afii10085",
	"afii10086",
	"afii10087",
	"afii10088",
	"afii10089",
	"afii10090",
	"afii10091",
	"afii10092",
	"afii10093",
	"afii10094",
	"afii10095",
	"afii10096",
	"afii10097",
};

static const struct { unsigned short u, c; } windows_1251_from_unicode[] = {
	{0x00a0,160},
	{0x00a4,164},
	{0x00a6,166},
	{0x00a7,167},
	{0x00a9,169},
	{0x00ab,171},
	{0x00ac,172},
	{0x00ad,173},
	{0x00ae,174},
	{0x00b0,176},
	{0x00b1,177},
	{0x00b5,181},
	{0x00b6,182},
	{0x00b7,183},
	{0x00bb,187},
	{0x0401,168},
	{0x0402,128},
	{0x0403,129},
	{0x0404,170},
	{0x0405,189},
	{0x0406,178},
	{0x0407,175},
	{0x0408,163},
	{0x0409,138},
	{0x040a,140},
	{0x040b,142},
	{0x040c,141},
	{0x040e,161},
	{0x040f,143},
	{0x0410,192},
	{0x0411,193},
	{0x0412,194},
	{0x0413,195},
	{0x0414,196},
	{0x0415,197},
	{0x0416,198},
	{0x0417,199},
	{0x0418,200},
	{0x0419,201},
	{0x041a,202},
	{0x041b,203},
	{0x041c,204},
	{0x041d,205},
	{0x041e,206},
	{0x041f,207},
	{0x0420,208},
	{0x0421,209},
	{0x0422,210},
	{0x0423,211},
	{0x0424,212},
	{0x0425,213},
	{0x0426,214},
	{0x0427,215},
	{0x0428,216},
	{0x0429,217},
	{0x042a,218},
	{0x042b,219},
	{0x042c,220},
	{0x042d,221},
	{0x042e,222},
	{0x042f,223},
	{0x0430,224},
	{0x0431,225},
	{0x0432,226},
	{0x0433,227},
	{0x0434,228},
	{0x0435,229},
	{0x0436,230},
	{0x0437,231},
	{0x0438,232},
	{0x0439,233},
	{0x043a,234},
	{0x043b,235},
	{0x043c,236},
	{0x043d,237},
	{0x043e,238},
	{0x043f,239},
	{0x0440,240},
	{0x0441,241},
	{0x0442,242},
	{0x0443,243},
	{0x0444,244},
	{0x0445,245},
	{0x0446,246},
	{0x0447,247},
	{0x0448,248},
	{0x0449,249},
	{0x044a,250},
	{0x044b,251},
	{0x044c,252},
	{0x044d,253},
	{0x044e,254},
	{0x044f,255},
	{0x0451,184},
	{0x0452,144},
	{0x0453,131},
	{0x0454,186},
	{0x0455,190},
	{0x0456,179},
	{0x0457,191},
	{0x0458,188},
	{0x0459,154},
	{0x045a,156},
	{0x045b,158},
	{0x045c,157},
	{0x045e,162},
	{0x045f,159},
	{0x0490,165},
	{0x0491,180},
	{0x2013,150},
	{0x2014,151},
	{0x2018,145},
	{0x2019,146},
	{0x201a,130},
	{0x201c,147},
	{0x201d,148},
	{0x201e,132},
	{0x2020,134},
	{0x2021,135},
	{0x2022,149},
	{0x2026,133},
	{0x2030,137},
	{0x2039,139},
	{0x203a,155},
	{0x20ac,136},
	{0x2116,185},
	{0x2122,153},
};

const unsigned short fz_unicode_from_windows_1252[256] = {
	0,
	1,
	2,
	3,
	4,
	5,
	6,
	7,
	8,
	9,
	10,
	11,
	12,
	13,
	14,
	15,
	16,
	17,
	18,
	19,
	20,
	21,
	22,
	23,
	24,
	25,
	26,
	27,
	28,
	29,
	30,
	31,
	32,
	33,
	34,
	35,
	36,
	37,
	38,
	39,
	40,
	41,
	42,
	43,
	44,
	45,
	46,
	47,
	48,
	49,
	50,
	51,
	52,
	53,
	54,
	55,
	56,
	57,
	58,
	59,
	60,
	61,
	62,
	63,
	64,
	65,
	66,
	67,
	68,
	69,
	70,
	71,
	72,
	73,
	74,
	75,
	76,
	77,
	78,
	79,
	80,
	81,
	82,
	83,
	84,
	85,
	86,
	87,
	88,
	89,
	90,
	91,
	92,
	93,
	94,
	95,
	96,
	97,
	98,
	99,
	100,
	101,
	102,
	103,
	104,
	105,
	106,
	107,
	108,
	109,
	110,
	111,
	112,
	113,
	114,
	115,
	116,
	117,
	118,
	119,
	120,
	121,
	122,
	123,
	124,
	125,
	126,
	127,
	8364,
	0,
	8218,
	402,
	8222,
	8230,
	8224,
	8225,
	710,
	8240,
	352,
	8249,
	338,
	0,
	381,
	0,
	0,
	8216,
	8217,
	8220,
	8221,
	8226,
	8211,
	8212,
	732,
	8482,
	353,
	8250,
	339,
	0,
	382,
	376,
	160,
	161,
	162,
	163,
	164,
	165,
	166,
	167,
	168,
	169,
	170,
	171,
	172,
	173,
	174,
	175,
	176,
	177,
	178,
	179,
	180,
	181,
	182,
	183,
	184,
	185,
	186,
	187,
	188,
	189,
	190,
	191,
	192,
	193,
	194,
	195,
	196,
	197,
	198,
	199,
	200,
	201,
	202,
	203,
	204,
	205,
	206,
	207,
	208,
	209,
	210,
	211,
	212,
	213,
	214,
	215,
	216,
	217,
	218,
	219,
	220,
	221,
	222,
	223,
	224,
	225,
	226,
	227,
	228,
	229,
	230,
	231,
	232,
	233,
	234,
	235,
	236,
	237,
	238,
	239,
	240,
	241,
	242,
	243,
	244,
	245,
	246,
	247,
	248,
	249,
	250,
	251,
	252,
	253,
	254,
	255,
};

const char *fz_glyph_name_from_windows_1252[256] = {
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	_notdef,
	"space",
	"exclam",
	"quotedbl",
	"numbersign",
	"dollar",
	"percent",
	"ampersand",
	"quotesingle",
	"parenleft",
	"parenright",
	"asterisk",
	"plus",
	"comma",
	"hyphen",
	"period",
	"slash",
	"zero",
	"one",
	"two",
	"three",
	"four",
	"five",
	"six",
	"seven",
	"eight",
	"nine",
	"colon",
	"semicolon",
	"less",
	"equal",
	"greater",
	"question",
	"at",
	"A",
	"B",
	"C",
	"D",
	"E",
	"F",
	"G",
	"H",
	"I",
	"J",
	"K",
	"L",
	"M",
	"N",
	"O",
	"P",
	"Q",
	"R",
	"S",
	"T",
	"U",
	"V",
	"W",
	"X",
	"Y",
	"Z",
	"bracketleft",
	"backslash",
	"bracketright",
	"asciicircum",
	"underscore",
	"grave",
	"a",
	"b",
	"c",
	"d",
	"e",
	"f",
	"g",
	"h",
	"i",
	"j",
	"k",
	"l",
	"m",
	"n",
	"o",
	"p",
	"q",
	"r",
	"s",
	"t",
	"u",
	"v",
	"w",
	"x",
	"y",
	"z",
	"braceleft",
	"bar",
	"braceright",
	"asciitilde",
	"controlDEL",
	"Euro",
	_notdef,
	"quotesinglbase",
	"florin",
	"quotedblbase",
	"ellipsis",
	"dagger",
	"daggerdbl",
	"circumflex",
	"perthousand",
	"Scaron",
	"guilsinglleft",
	"OE",
	_notdef,
	"Zcaron",
	_notdef,
	_notdef,
	"quoteleft",
	"quoteright",
	"quotedblleft",
	"quotedblright",
	"bullet",
	"endash",
	"emdash",
	"tilde",
	"trademark",
	"scaron",
	"guilsinglright",
	"oe",
	_notdef,
	"zcaron",
	"Ydieresis",
	"nbspace",
	"exclamdown",
	"cent",
	"sterling",
	"currency",
	"yen",
	"brokenbar",
	"section",
	"dieresis",
	"copyright",
	"ordfeminine",
	"guillemotleft",
	"logicalnot",
	"sfthyphen",
	"registered",
	"macron",
	"degree",
	"plusminus",
	"twosuperior",
	"threesuperior",
	"acute",
	"mu",
	"paragraph",
	"periodcentered",
	"cedilla",
	"onesuperior",
	"ordmasculine",
	"guillemotright",
	"onequarter",
	"onehalf",
	"threequarters",
	"questiondown",
	"Agrave",
	"Aacute",
	"Acircumflex",
	"Atilde",
	"Adieresis",
	"Aring",
	"AE",
	"Ccedilla",
	"Egrave",
	"Eacute",
	"Ecircumflex",
	"Edieresis",
	"Igrave",
	"Iacute",
	"Icircumflex",
	"Idieresis",
	"Eth",
	"Ntilde",
	"Ograve",
	"Oacute",
	"Ocircumflex",
	"Otilde",
	"Odieresis",
	"multiply",
	"Oslash",
	"Ugrave",
	"Uacute",
	"Ucircumflex",
	"Udieresis",
	"Yacute",
	"Thorn",
	"germandbls",
	"agrave",
	"aacute",
	"acircumflex",
	"atilde",
	"adieresis",
	"aring",
	"ae",
	"ccedilla",
	"egrave",
	"eacute",
	"ecircumflex",
	"edieresis",
	"igrave",
	"iacute",
	"icircumflex",
	"idieresis",
	"eth",
	"ntilde",
	"ograve",
	"oacute",
	"ocircumflex",
	"otilde",
	"odieresis",
	"divide",
	"oslash",
	"ugrave",
	"uacute",
	"ucircumflex",
	"udieresis",
	"yacute",
	"thorn",
	"ydieresis",
};

static const struct { unsigned short u, c; } windows_1252_from_unicode[] = {
	{0x00a0,160},
	{0x00a1,161},
	{0x00a2,162},
	{0x00a3,163},
	{0x00a4,164},
	{0x00a5,165},
	{0x00a6,166},
	{0x00a7,167},
	{0x00a8,168},
	{0x00a9,169},
	{0x00aa,170},
	{0x00ab,171},
	{0x00ac,172},
	{0x00ad,173},
	{0x00ae,174},
	{0x00af,175},
	{0x00b0,176},
	{0x00b1,177},
	{0x00b2,178},
	{0x00b3,179},
	{0x00b4,180},
	{0x00b5,181},
	{0x00b6,182},
	{0x00b7,183},
	{0x00b8,184},
	{0x00b9,185},
	{0x00ba,186},
	{0x00bb,187},
	{0x00bc,188},
	{0x00bd,189},
	{0x00be,190},
	{0x00bf,191},
	{0x00c0,192},
	{0x00c1,193},
	{0x00c2,194},
	{0x00c3,195},
	{0x00c4,196},
	{0x00c5,197},
	{0x00c6,198},
	{0x00c7,199},
	{0x00c8,200},
	{0x00c9,201},
	{0x00ca,202},
	{0x00cb,203},
	{0x00cc,204},
	{0x00cd,205},
	{0x00ce,206},
	{0x00cf,207},
	{0x00d0,208},
	{0x00d1,209},
	{0x00d2,210},
	{0x00d3,211},
	{0x00d4,212},
	{0x00d5,213},
	{0x00d6,214},
	{0x00d7,215},
	{0x00d8,216},
	{0x00d9,217},
	{0x00da,218},
	{0x00db,219},
	{0x00dc,220},
	{0x00dd,221},
	{0x00de,222},
	{0x00df,223},
	{0x00e0,224},
	{0x00e1,225},
	{0x00e2,226},
	{0x00e3,227},
	{0x00e4,228},
	{0x00e5,229},
	{0x00e6,230},
	{0x00e7,231},
	{0x00e8,232},
	{0x00e9,233},
	{0x00ea,234},
	{0x00eb,235},
	{0x00ec,236},
	{0x00ed,237},
	{0x00ee,238},
	{0x00ef,239},
	{0x00f0,240},
	{0x00f1,241},
	{0x00f2,242},
	{0x00f3,243},
	{0x00f4,244},
	{0x00f5,245},
	{0x00f6,246},
	{0x00f7,247},
	{0x00f8,248},
	{0x00f9,249},
	{0x00fa,250},
	{0x00fb,251},
	{0x00fc,252},
	{0x00fd,253},
	{0x00fe,254},
	{0x00ff,255},
	{0x0152,140},
	{0x0153,156},
	{0x0160,138},
	{0x0161,154},
	{0x0178,159},
	{0x017d,142},
	{0x017e,158},
	{0x0192,131},
	{0x02c6,136},
	{0x02dc,152},
	{0x2013,150},
	{0x2014,151},
	{0x2018,145},
	{0x2019,146},
	{0x201a,130},
	{0x201c,147},
	{0x201d,148},
	{0x201e,132},
	{0x2020,134},
	{0x2021,135},
	{0x2022,149},
	{0x2026,133},
	{0x2030,137},
	{0x2039,139},
	{0x203a,155},
	{0x20ac,128},
	{0x2122,153},
};
