// Copyright (C) 2004-2021 Artifex Software, Inc.
//
// This file is part of MuPDF.
//
// MuPDF is free software: you can redistribute it and/or modify it under the
// terms of the GNU Affero General Public License as published by the Free
// Software Foundation, either version 3 of the License, or (at your option)
// any later version.
//
// MuPDF is distributed in the hope that it will be useful, but WITHOUT ANY
// WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
// FOR A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
// details.
//
// You should have received a copy of the GNU Affero General Public License
// along with MuPDF. If not, see <https://www.gnu.org/licenses/agpl-3.0.en.html>
//
// Alternative licensing terms are available from the licensor.
// For commercial licensing, see <https://www.artifex.com/> or contact
// Artifex Software, Inc., 39 Mesa Street, Suite 108A, San Francisco,
// CA 94129, USA, for further information.

static const struct { unsigned short u; const char *n; } glyph_name_from_unicode_sc[] = {
	{0x0061, "a.sc"},
	{0x0062, "b.sc"},
	{0x0063, "c.sc"},
	{0x0064, "d.sc"},
	{0x0065, "e.sc"},
	{0x0066, "f.sc"},
	{0x0067, "g.sc"},
	{0x0068, "h.sc"},
	{0x0069, "i.sc"},
	{0x006A, "j.sc"},
	{0x006B, "k.sc"},
	{0x006C, "l.sc"},
	{0x006D, "m.sc"},
	{0x006E, "n.sc"},
	{0x006F, "o.sc"},
	{0x0070, "p.sc"},
	{0x0071, "q.sc"},
	{0x0072, "r.sc"},
	{0x0073, "s.sc"},
	{0x0074, "t.sc"},
	{0x0075, "u.sc"},
	{0x0076, "v.sc"},
	{0x0077, "w.sc"},
	{0x0078, "x.sc"},
	{0x0079, "y.sc"},
	{0x007A, "z.sc"},
	{0x00DF, "germandbls.sc"},
	{0x00E0, "agrave.sc"},
	{0x00E1, "aacute.sc"},
	{0x00E2, "acircumflex.sc"},
	{0x00E3, "atilde.sc"},
	{0x00E4, "adieresis.sc"},
	{0x00E5, "aring.sc"},
	{0x00E6, "ae.sc"},
	{0x00E7, "ccedilla.sc"},
	{0x00E8, "egrave.sc"},
	{0x00E9, "eacute.sc"},
	{0x00EA, "ecircumflex.sc"},
	{0x00EB, "edieresis.sc"},
	{0x00EC, "igrave.sc"},
	{0x00ED, "iacute.sc"},
	{0x00EE, "icircumflex.sc"},
	{0x00EF, "idieresis.sc"},
	{0x00F0, "eth.sc"},
	{0x00F1, "ntilde.sc"},
	{0x00F2, "ograve.sc"},
	{0x00F3, "oacute.sc"},
	{0x00F4, "ocircumflex.sc"},
	{0x00F5, "otilde.sc"},
	{0x00F6, "odieresis.sc"},
	{0x00F8, "oslash.sc"},
	{0x00F9, "ugrave.sc"},
	{0x00FA, "uacute.sc"},
	{0x00FB, "ucircumflex.sc"},
	{0x00FC, "udieresis.sc"},
	{0x00FD, "yacute.sc"},
	{0x00FE, "thorn.sc"},
	{0x00FF, "ydieresis.sc"},
	{0x0101, "amacron.sc"},
	{0x0103, "abreve.sc"},
	{0x0105, "aogonek.sc"},
	{0x0107, "cacute.sc"},
	{0x0109, "ccircumflex.sc"},
	{0x010B, "cdot.sc"},
	{0x010B, "cdotaccent.sc"},
	{0x010D, "ccaron.sc"},
	{0x010F, "dcaron.sc"},
	{0x0111, "dcroat.sc"},
	{0x0113, "emacron.sc"},
	{0x0115, "ebreve.sc"},
	{0x0117, "edotaccent.sc"},
	{0x0119, "eogonek.sc"},
	{0x011B, "ecaron.sc"},
	{0x011D, "gcircumflex.sc"},
	{0x011F, "gbreve.sc"},
	{0x0121, "gdotaccent.sc"},
	{0x0123, "gcedilla.sc"},
	{0x0125, "hcircumflex.sc"},
	{0x0127, "hbar.sc"},
	{0x0129, "itilde.sc"},
	{0x012B, "imacron.sc"},
	{0x012D, "ibreve.sc"},
	{0x012F, "iogonek.sc"},
	{0x0131, "dotlessi.sc"},
	{0x0133, "ij.sc"},
	{0x0135, "jcircumflex.sc"},
	{0x0137, "kcedilla.sc"},
	{0x0138, "kgreenlandic.sc"},
	{0x013A, "lacute.sc"},
	{0x013C, "lcedilla.sc"},
	{0x013E, "lcaron.sc"},
	{0x0140, "ldotaccent.sc"},
	{0x0142, "lslash.sc"},
	{0x0144, "nacute.sc"},
	{0x0146, "ncedilla.sc"},
	{0x0148, "ncaron.sc"},
	{0x0149, "napostrophe.sc"},
	{0x0149, "quoterightn.sc"},
	{0x014B, "eng.sc"},
	{0x014D, "omacron.sc"},
	{0x014F, "obreve.sc"},
	{0x0151, "ohungarumlaut.sc"},
	{0x0153, "oe.sc"},
	{0x0155, "racute.sc"},
	{0x0157, "rcedilla.sc"},
	{0x0159, "rcaron.sc"},
	{0x015B, "sacute.sc"},
	{0x015D, "scircumflex.sc"},
	{0x015F, "scedilla.sc"},
	{0x0161, "scaron.sc"},
	{0x0163, "tcedilla.sc"},
	{0x0165, "tcaron.sc"},
	{0x0167, "tbar.sc"},
	{0x0169, "utilde.sc"},
	{0x016B, "umacron.sc"},
	{0x016D, "ubreve.sc"},
	{0x016F, "uring.sc"},
	{0x0171, "uhungarumlaut.sc"},
	{0x0173, "uogonek.sc"},
	{0x0175, "wcircumflex.sc"},
	{0x0177, "ycircumflex.sc"},
	{0x017A, "zacute.sc"},
	{0x017C, "zdotaccent.sc"},
	{0x017E, "zcaron.sc"},
	{0x017F, "longs.sc"},
	{0x017F, "slong.sc"},
	{0x0192, "florin.sc"},
	{0x01DF, "adieresismacron.sc"},
	{0x01E1, "adotmacron.sc"},
	{0x01E3, "aemacron.sc"},
	{0x01E5, "gstroke.sc"},
	{0x01E7, "gcaron.sc"},
	{0x01E9, "kcaron.sc"},
	{0x01EB, "oogonek.sc"},
	{0x01ED, "oogonekmacron.sc"},
	{0x01EF, "ezhcaron.sc"},
	{0x01FB, "aringacute.sc"},
	{0x01FD, "aeacute.sc"},
	{0x01FF, "oslashacute.sc"},
	{0x0219, "scommaaccent.sc"},
	{0x0259, "schwa.sc"},
	{0x027C, "rlongleg.sc"},
	{0x0292, "ezh.sc"},
	{0x0390, "iotadieresistonos.sc"},
	{0x03AC, "alphatonos.sc"},
	{0x03AD, "epsilontonos.sc"},
	{0x03AE, "etatonos.sc"},
	{0x03AF, "iotatonos.sc"},
	{0x03B0, "upsilondieresistonos.sc"},
	{0x03B1, "alpha.sc"},
	{0x03B2, "beta.sc"},
	{0x03B3, "gamma.sc"},
	{0x03B4, "delta.sc"},
	{0x03B5, "epsilon.sc"},
	{0x03B6, "zeta.sc"},
	{0x03B7, "eta.sc"},
	{0x03B8, "theta.sc"},
	{0x03B9, "iota.sc"},
	{0x03BA, "kappa.sc"},
	{0x03BB, "lambda.sc"},
	{0x03BC, "mugreek.sc"},
	{0x03BD, "nu.sc"},
	{0x03BE, "xi.sc"},
	{0x03BF, "omicron.sc"},
	{0x03C0, "pi.sc"},
	{0x03C1, "rho.sc"},
	{0x03C2, "sigmafinal.sc"},
	{0x03C3, "sigma.sc"},
	{0x03C4, "tau.sc"},
	{0x03C5, "upsilon.sc"},
	{0x03C6, "phi.sc"},
	{0x03C7, "chi.sc"},
	{0x03C8, "psi.sc"},
	{0x03C9, "omega.sc"},
	{0x03CA, "iotadieresis.sc"},
	{0x03CB, "upsilondieresis.sc"},
	{0x03CC, "omicrontonos.sc"},
	{0x03CD, "upsilontonos.sc"},
	{0x03CE, "omegatonos.sc"},
	{0x0430, "acyrillic.sc"},
	{0x0431, "becyrillic.sc"},
	{0x0432, "vecyrillic.sc"},
	{0x0433, "gecyrillic.sc"},
	{0x0434, "decyrillic.sc"},
	{0x0435, "iecyrillic.sc"},
	{0x0436, "zhecyrillic.sc"},
	{0x0437, "zecyrillic.sc"},
	{0x0438, "iicyrillic.sc"},
	{0x0439, "iishortcyrillic.sc"},
	{0x043A, "kacyrillic.sc"},
	{0x043B, "elcyrillic.sc"},
	{0x043C, "emcyrillic.sc"},
	{0x043D, "encyrillic.sc"},
	{0x043E, "ocyrillic.sc"},
	{0x043F, "pecyrillic.sc"},
	{0x0440, "ercyrillic.sc"},
	{0x0441, "escyrillic.sc"},
	{0x0442, "tecyrillic.sc"},
	{0x0443, "ucyrillic.sc"},
	{0x0444, "efcyrillic.sc"},
	{0x0445, "khacyrillic.sc"},
	{0x0446, "tsecyrillic.sc"},
	{0x0447, "checyrillic.sc"},
	{0x0448, "shacyrillic.sc"},
	{0x0449, "shchacyrillic.sc"},
	{0x044A, "hardsigncyrillic.sc"},
	{0x044B, "yericyrillic.sc"},
	{0x044C, "softsigncyrillic.sc"},
	{0x044D, "ereversedcyrillic.sc"},
	{0x044E, "iucyrillic.sc"},
	{0x044F, "iacyrillic.sc"},
	{0x0451, "iocyrillic.sc"},
	{0x0452, "djecyrillic.sc"},
	{0x0453, "gjecyrillic.sc"},
	{0x0454, "ecyrillic.sc"},
	{0x0455, "dzecyrillic.sc"},
	{0x0456, "icyrillic.sc"},
	{0x0457, "yicyrillic.sc"},
	{0x0458, "jecyrillic.sc"},
	{0x0459, "ljecyrillic.sc"},
	{0x045A, "njecyrillic.sc"},
	{0x045B, "tshecyrillic.sc"},
	{0x045C, "kjecyrillic.sc"},
	{0x045E, "ushortcyrillic.sc"},
	{0x045F, "dzhecyrillic.sc"},
	{0x0491, "gheupturncyrillic.sc"},
	{0x0493, "ghestrokecyrillic.sc"},
	{0x0495, "ghemiddlehookcyrillic.sc"},
	{0x0497, "zhedescendercyrillic.sc"},
	{0x0499, "zedescendercyrillic.sc"},
	{0x049B, "kadescendercyrillic.sc"},
	{0x049D, "kaverticalstrokecyrillic.sc"},
	{0x049F, "kastrokecyrillic.sc"},
	{0x04A1, "kabashkircyrillic.sc"},
	{0x04A3, "endescendercyrillic.sc"},
	{0x04A5, "enghecyrillic.sc"},
	{0x04A7, "pemiddlehookcyrillic.sc"},
	{0x04A9, "haabkhasiancyrillic.sc"},
	{0x04AB, "esdescendercyrillic.sc"},
	{0x04AD, "tedescendercyrillic.sc"},
	{0x04AF, "ustraightcyrillic.sc"},
	{0x04B1, "ustraightstrokecyrillic.sc"},
	{0x04B3, "hadescendercyrillic.sc"},
	{0x04B5, "tetsecyrillic.sc"},
	{0x04B7, "chedescendercyrillic.sc"},
	{0x04B9, "cheverticalstrokecyrillic.sc"},
	{0x04BB, "shhacyrillic.sc"},
	{0x04BD, "cheabkhasiancyrillic.sc"},
	{0x04BF, "chedescenderabkhasiancyrillic.sc"},
	{0x04C2, "zhebrevecyrillic.sc"},
	{0x04C4, "kahookcyrillic.sc"},
	{0x04C8, "enhookcyrillic.sc"},
	{0x04CC, "chekhakassiancyrillic.sc"},
	{0x04D1, "abrevecyrillic.sc"},
	{0x04D3, "adieresiscyrillic.sc"},
	{0x04D5, "aiecyrillic.sc"},
	{0x04D7, "iebrevecyrillic.sc"},
	{0x04D9, "schwacyrillic.sc"},
	{0x04DB, "schwadieresiscyrillic.sc"},
	{0x04DD, "zhedieresiscyrillic.sc"},
	{0x04DF, "zedieresiscyrillic.sc"},
	{0x04E1, "dzeabkhasiancyrillic.sc"},
	{0x04E3, "imacroncyrillic.sc"},
	{0x04E5, "idieresiscyrillic.sc"},
	{0x04E7, "odieresiscyrillic.sc"},
	{0x04E9, "obarredcyrillic.sc"},
	{0x04EB, "obarreddieresiscyrillic.sc"},
	{0x04EF, "umacroncyrillic.sc"},
	{0x04F1, "udieresiscyrillic.sc"},
	{0x04F3, "uhungarumlautcyrillic.sc"},
	{0x04F5, "chedieresiscyrillic.sc"},
	{0x04F9, "yerudieresiscyrillic.sc"},
	{0x1E03, "bdotaccent.sc"},
	{0x1E0B, "ddotaccent.sc"},
	{0x1E1F, "fdotaccent.sc"},
	{0x1E41, "mdotaccent.sc"},
	{0x1E57, "pdotaccent.sc"},
	{0x1E61, "sdotaccent.sc"},
	{0x1E6B, "tdotaccent.sc"},
	{0x1E81, "wgrave.sc"},
	{0x1E83, "wacute.sc"},
	{0x1E85, "wdieresis.sc"},
	{0x1E9B, "slongdotaccent.sc"},
	{0x1EF3, "ygrave.sc"},
	{0x207F, "nsuperior.sc"},
	{0xFB00, "f_f.sc"},
	{0xFB01, "f_i.sc"},
	{0xFB02, "f_l.sc"},
	{0xFB03, "f_f_i.sc"},
	{0xFB04, "f_f_l.sc"},
};
