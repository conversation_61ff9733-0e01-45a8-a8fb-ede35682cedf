// Copyright (C) 2004-2025 Artifex Software, Inc.
//
// This file is part of MuPDF.
//
// MuPDF is free software: you can redistribute it and/or modify it under the
// terms of the GNU Affero General Public License as published by the Free
// Software Foundation, either version 3 of the License, or (at your option)
// any later version.
//
// MuPDF is distributed in the hope that it will be useful, but WITHOUT ANY
// WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
// FOR A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
// details.
//
// You should have received a copy of the GNU Affero General Public License
// along with MuPDF. If not, see <https://www.gnu.org/licenses/agpl-3.0.en.html>
//
// Alternative licensing terms are available from the licensor.
// For commercial licensing, see <https://www.artifex.com/> or contact
// Artifex Software, Inc., 39 Mesa Street, Suite 108A, San Francisco,
// CA 94129, USA, for further information.

#ifndef TOFU_BASE14
FONT(urw,	NimbusMonoPS_Regular_cff,	"Courier",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)
ALIAS(urw,	NimbusMonoPS_Regular_cff,	"Nimbus Mono",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)
FONT(urw,	NimbusMonoPS_Italic_cff,	"Courier",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	ITALIC)
ALIAS(urw,	NimbusMonoPS_Italic_cff,	"Nimbus Mono",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	ITALIC)
FONT(urw,	NimbusMonoPS_Bold_cff,		"Courier",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	BOLD)
ALIAS(urw,	NimbusMonoPS_Bold_cff,		"Nimbus Mono",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	BOLD)
FONT(urw,	NimbusMonoPS_BoldItalic_cff,	"Courier",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	BOLD|ITALIC)
ALIAS(urw,	NimbusMonoPS_BoldItalic_cff,	"Nimbus Mono",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	BOLD|ITALIC)

FONT(urw,	NimbusSans_Regular_cff,		"Helvetica",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)
ALIAS(urw,	NimbusSans_Regular_cff,		"Arial",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)
ALIAS(urw,	NimbusSans_Regular_cff,		"Nimbus Sans",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)

FONT(urw,	NimbusSans_Italic_cff,		"Helvetica",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	ITALIC)
ALIAS(urw,	NimbusSans_Italic_cff,		"Arial",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	ITALIC)
ALIAS(urw,	NimbusSans_Italic_cff,		"Nimbus Sans",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	ITALIC)

FONT(urw,	NimbusSans_Bold_cff,		"Helvetica",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	BOLD)
ALIAS(urw,	NimbusSans_Bold_cff,		"Arial",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	BOLD)
ALIAS(urw,	NimbusSans_Bold_cff,		"Nimbus Sans",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	BOLD)

FONT(urw,	NimbusSans_BoldItalic_cff,	"Helvetica",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	BOLD|ITALIC)
ALIAS(urw,	NimbusSans_BoldItalic_cff,	"Arial",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	BOLD|ITALIC)
ALIAS(urw,	NimbusSans_BoldItalic_cff,	"Nimbus Sans",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	BOLD|ITALIC)

FONT(urw,	NimbusRoman_Regular_cff,	"Times",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)
ALIAS(urw,	NimbusRoman_Regular_cff,	"Times New Roman",	ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)
ALIAS(urw,	NimbusRoman_Regular_cff,	"Nimbus Roman",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)

FONT(urw,	NimbusRoman_Italic_cff,		"Times",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	ITALIC)
ALIAS(urw,	NimbusRoman_Italic_cff,		"Times Roman",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	ITALIC)
ALIAS(urw,	NimbusRoman_Italic_cff,		"Times New Roman",	ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	ITALIC)
ALIAS(urw,	NimbusRoman_Italic_cff,		"Nimbus Roman",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	ITALIC)

FONT(urw,	NimbusRoman_Bold_cff,		"Times",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	BOLD)
ALIAS(urw,	NimbusRoman_Bold_cff,		"Times Roman",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	BOLD)
ALIAS(urw,	NimbusRoman_Bold_cff,		"Times New Roman",	ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	BOLD)
ALIAS(urw,	NimbusRoman_Bold_cff,		"Nimbus Roman",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	BOLD)

FONT(urw,	NimbusRoman_BoldItalic_cff,	"Times",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	BOLD|ITALIC)
ALIAS(urw,	NimbusRoman_BoldItalic_cff,	"Times Roman",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	BOLD|ITALIC)
ALIAS(urw,	NimbusRoman_BoldItalic_cff,	"Times New Roman",	ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	BOLD|ITALIC)
ALIAS(urw,	NimbusRoman_BoldItalic_cff,	"Nimbus Roman",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	BOLD|ITALIC)

FONT(urw,	StandardSymbolsPS_cff,		"Symbol",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)
ALIAS(urw,	StandardSymbolsPS_cff,		"Standard Symbols PS",	ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)

FONT(urw,	Dingbats_cff,			"ZapfDingbats",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)
ALIAS(urw,	Dingbats_cff,			"Dingbats",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)
#endif

#ifndef TOFU_SIL
FONT(sil,	CharisSIL_cff,			"Charis SIL",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)
FONT(sil,	CharisSIL_Italic_cff,		"Charis SIL",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	ITALIC)
FONT(sil,	CharisSIL_Bold_cff,		"Charis SIL",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	BOLD)
FONT(sil,	CharisSIL_BoldItalic_cff,	"Charis SIL",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	BOLD|ITALIC)
#endif

#ifndef TOFU_NOTO
FONT(urw,	NimbusBoxes_Regular_cff,	"Nimbus Boxes",		ANY_SCRIPT,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)

FONT(noto,	NotoSerif_Regular_otf,	"Noto Serif",	UCDN_SCRIPT_LATIN,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)
ALIAS(noto,	NotoSerif_Regular_otf,	"Noto Serif",	UCDN_SCRIPT_GREEK,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)
ALIAS(noto,	NotoSerif_Regular_otf,	"Noto Serif",	UCDN_SCRIPT_CYRILLIC,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)
ALIAS(noto,	NotoSerif_Regular_otf,	"Noto Serif",	UCDN_SCRIPT_COMMON,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)
ALIAS(noto,	NotoSerif_Regular_otf,	"Noto Serif",	UCDN_SCRIPT_INHERITED,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)
ALIAS(noto,	NotoSerif_Regular_otf,	"Noto Serif",	UCDN_SCRIPT_UNKNOWN,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)

FONT(noto,	NotoNastaliqUrdu_Regular_otf,	"Noto Nastaliq Urdu",	UCDN_SCRIPT_ARABIC,	FZ_LANG_ur,	NO_SUBFONT,	REGULAR)
ALIAS(noto,	NotoNastaliqUrdu_Regular_otf,	"Noto Nastaliq Urdu",	UCDN_SCRIPT_ARABIC,	FZ_LANG_urd,	NO_SUBFONT,	REGULAR)
FONT(noto,	NotoNaskhArabic_Regular_otf,	"Noto Nastaliq Urdu",	UCDN_SCRIPT_ARABIC,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)

#define DEFNOTO(symbol,name,script) FONT(noto, symbol, name, script, FZ_LANG_UNSET, NO_SUBFONT, REGULAR)

DEFNOTO(NotoSansAdlam_Regular_otf,			"Noto Sans Adlam",			UCDN_SCRIPT_ADLAM)
DEFNOTO(NotoSerifAhom_Regular_otf,			"Noto Serif Ahom",			UCDN_SCRIPT_AHOM)
DEFNOTO(NotoSansAnatolianHieroglyphs_Regular_otf,	"Noto Sans Anatolian Hieroglyphs",	UCDN_SCRIPT_ANATOLIAN_HIEROGLYPHS)
DEFNOTO(NotoSerifArmenian_Regular_otf,			"Noto Serif Armenian",			UCDN_SCRIPT_ARMENIAN)
DEFNOTO(NotoSansAvestan_Regular_otf,			"Noto Sans Avestan",			UCDN_SCRIPT_AVESTAN)
DEFNOTO(NotoSerifBalinese_Regular_otf,			"Noto Serif Balinese",			UCDN_SCRIPT_BALINESE)
DEFNOTO(NotoSansBamum_Regular_otf,			"Noto Sans Bamum",			UCDN_SCRIPT_BAMUM)
DEFNOTO(NotoSansBassaVah_Regular_otf,			"Noto Sans Bassa Vah",			UCDN_SCRIPT_BASSA_VAH)
DEFNOTO(NotoSansBatak_Regular_otf,			"Noto Sans Batak",			UCDN_SCRIPT_BATAK)
DEFNOTO(NotoSerifBengali_Regular_otf,			"Noto Serif Bengali",			UCDN_SCRIPT_BENGALI)
DEFNOTO(NotoSansBhaiksuki_Regular_otf,			"Noto Sans Bhaiksuki",			UCDN_SCRIPT_BHAIKSUKI)
DEFNOTO(NotoSansBrahmi_Regular_otf,			"Noto Sans Brahmi",			UCDN_SCRIPT_BRAHMI)
DEFNOTO(NotoSansBuginese_Regular_otf,			"Noto Sans Buginese",			UCDN_SCRIPT_BUGINESE)
DEFNOTO(NotoSansBuhid_Regular_otf,			"Noto Sans Buhid",			UCDN_SCRIPT_BUHID)
DEFNOTO(NotoSansCanadianAboriginal_Regular_otf,		"Noto Sans Canadian Aboriginal",	UCDN_SCRIPT_CANADIAN_ABORIGINAL)
DEFNOTO(NotoSansCarian_Regular_otf,			"Noto Sans Carian",			UCDN_SCRIPT_CARIAN)
DEFNOTO(NotoSansCaucasianAlbanian_Regular_otf,		"Noto Sans Caucasian Albanian",		UCDN_SCRIPT_CAUCASIAN_ALBANIAN)
DEFNOTO(NotoSansChakma_Regular_otf,			"Noto Sans Chakma",			UCDN_SCRIPT_CHAKMA)
DEFNOTO(NotoSansCham_Regular_otf,			"Noto Sans Cham",			UCDN_SCRIPT_CHAM)
DEFNOTO(NotoSansCherokee_Regular_otf,			"Noto Sans Cherokee",			UCDN_SCRIPT_CHEROKEE)
DEFNOTO(NotoSansChorasmian_Regular_otf,			"Noto Sans Chorasmian",			UCDN_SCRIPT_CHORASMIAN)
DEFNOTO(NotoSansCoptic_Regular_otf,			"Noto Sans Coptic",			UCDN_SCRIPT_COPTIC)
DEFNOTO(NotoSansCuneiform_Regular_otf,			"Noto Sans Cuneiform",			UCDN_SCRIPT_CUNEIFORM)
DEFNOTO(NotoSansCypriot_Regular_otf,			"Noto Sans Cypriot",			UCDN_SCRIPT_CYPRIOT)
DEFNOTO(NotoSansCyproMinoan_Regular_otf,		"Noto Sans Cypro Minoan",		UCDN_SCRIPT_CYPRO_MINOAN)
DEFNOTO(NotoSansDeseret_Regular_otf,			"Noto Sans Deseret",			UCDN_SCRIPT_DESERET)
DEFNOTO(NotoSerifDevanagari_Regular_otf,		"Noto Serif Devanagari",		UCDN_SCRIPT_DEVANAGARI)
DEFNOTO(NotoSerifDivesAkuru_Regular_otf,		"Noto Serif Dives Akuru",		UCDN_SCRIPT_DIVES_AKURU)
DEFNOTO(NotoSerifDogra_Regular_otf,			"Noto Serif Dogra",			UCDN_SCRIPT_DOGRA)
DEFNOTO(NotoSansDuployan_Regular_otf,			"Noto Sans Duployan",			UCDN_SCRIPT_DUPLOYAN)
DEFNOTO(NotoSansEgyptianHieroglyphs_Regular_otf,	"Noto Sans Egyptian Hieroglyphs",	UCDN_SCRIPT_EGYPTIAN_HIEROGLYPHS)
DEFNOTO(NotoSansElbasan_Regular_otf,			"Noto Sans Elbasan",			UCDN_SCRIPT_ELBASAN)
DEFNOTO(NotoSansElymaic_Regular_otf,			"Noto Sans Elymaic",			UCDN_SCRIPT_ELYMAIC)
DEFNOTO(NotoSerifEthiopic_Regular_otf,			"Noto Serif Ethiopic",			UCDN_SCRIPT_ETHIOPIC)
DEFNOTO(NotoSerifGeorgian_Regular_otf,			"Noto Serif Georgian",			UCDN_SCRIPT_GEORGIAN)
DEFNOTO(NotoSansGlagolitic_Regular_otf,			"Noto Sans Glagolitic",			UCDN_SCRIPT_GLAGOLITIC)
DEFNOTO(NotoSansGothic_Regular_otf,			"Noto Sans Gothic",			UCDN_SCRIPT_GOTHIC)
DEFNOTO(NotoSerifGrantha_Regular_otf,			"Noto Serif Grantha",			UCDN_SCRIPT_GRANTHA)
DEFNOTO(NotoSerifGujarati_Regular_otf,			"Noto Serif Gujarati",			UCDN_SCRIPT_GUJARATI)
DEFNOTO(NotoSansGunjalaGondi_Regular_otf,		"Noto Sans Gunjala Gondi",		UCDN_SCRIPT_GUNJALA_GONDI)
DEFNOTO(NotoSerifGurmukhi_Regular_otf,			"Noto Serif Gurmukhi",			UCDN_SCRIPT_GURMUKHI)
DEFNOTO(NotoSansHanifiRohingya_Regular_otf,		"Noto Sans Hanifi Rohingya",		UCDN_SCRIPT_HANIFI_ROHINGYA)
DEFNOTO(NotoSansHanunoo_Regular_otf,			"Noto Sans Hanunoo",			UCDN_SCRIPT_HANUNOO)
DEFNOTO(NotoSansHatran_Regular_otf,			"Noto Sans Hatran",			UCDN_SCRIPT_HATRAN)
DEFNOTO(NotoSerifHebrew_Regular_otf,			"Noto Serif Hebrew",			UCDN_SCRIPT_HEBREW)
DEFNOTO(NotoSansImperialAramaic_Regular_otf,		"Noto Sans Imperial Aramaic",		UCDN_SCRIPT_IMPERIAL_ARAMAIC)
DEFNOTO(NotoSansInscriptionalPahlavi_Regular_otf,	"Noto Sans Inscriptional Pahlavi",	UCDN_SCRIPT_INSCRIPTIONAL_PAHLAVI)
DEFNOTO(NotoSansInscriptionalParthian_Regular_otf,	"Noto Sans Inscriptional Parthian",	UCDN_SCRIPT_INSCRIPTIONAL_PARTHIAN)
DEFNOTO(NotoSansJavanese_Regular_otf,			"Noto Sans Javanese",			UCDN_SCRIPT_JAVANESE)
DEFNOTO(NotoSansKaithi_Regular_otf,			"Noto Sans Kaithi",			UCDN_SCRIPT_KAITHI)
DEFNOTO(NotoSerifKannada_Regular_otf,			"Noto Serif Kannada",			UCDN_SCRIPT_KANNADA)
DEFNOTO(NotoSansKayahLi_Regular_otf,			"Noto Sans Kayah Li",			UCDN_SCRIPT_KAYAH_LI)
DEFNOTO(NotoSansKawi_Regular_otf,			"Noto Sans Kawi",			UCDN_SCRIPT_KAWI)
DEFNOTO(NotoSansKharoshthi_Regular_otf,			"Noto Sans Kharoshthi",			UCDN_SCRIPT_KHAROSHTHI)
DEFNOTO(NotoSerifKhitanSmallScript_Regular_otf,		"Noto Serif Khitan Small Script",	UCDN_SCRIPT_KHITAN_SMALL_SCRIPT)
DEFNOTO(NotoSerifKhmer_Regular_otf,			"Noto Serif Khmer",			UCDN_SCRIPT_KHMER)
DEFNOTO(NotoSerifKhojki_Regular_otf,			"Noto Serif Khojki",			UCDN_SCRIPT_KHOJKI)
DEFNOTO(NotoSansKhudawadi_Regular_otf,			"Noto Sans Khudawadi",			UCDN_SCRIPT_KHUDAWADI)
DEFNOTO(NotoSerifLao_Regular_otf,			"Noto Serif Lao",			UCDN_SCRIPT_LAO)
DEFNOTO(NotoSansLepcha_Regular_otf,			"Noto Sans Lepcha",			UCDN_SCRIPT_LEPCHA)
DEFNOTO(NotoSansLimbu_Regular_otf,			"Noto Sans Limbu",			UCDN_SCRIPT_LIMBU)
DEFNOTO(NotoSansLinearA_Regular_otf,			"Noto Sans LinearA",			UCDN_SCRIPT_LINEAR_A)
DEFNOTO(NotoSansLinearB_Regular_otf,			"Noto Sans LinearB",			UCDN_SCRIPT_LINEAR_B)
DEFNOTO(NotoSansLisu_Regular_otf,			"Noto Sans Lisu",			UCDN_SCRIPT_LISU)
DEFNOTO(NotoSansLycian_Regular_otf,			"Noto Sans Lycian",			UCDN_SCRIPT_LYCIAN)
DEFNOTO(NotoSansLydian_Regular_otf,			"Noto Sans Lydian",			UCDN_SCRIPT_LYDIAN)
DEFNOTO(NotoSansMahajani_Regular_otf,			"Noto Sans Mahajani",			UCDN_SCRIPT_MAHAJANI)
DEFNOTO(NotoSerifMakasar_Regular_otf,			"Noto Serif Makasar",			UCDN_SCRIPT_MAKASAR)
DEFNOTO(NotoSerifMalayalam_Regular_otf,			"Noto Serif Malayalam",			UCDN_SCRIPT_MALAYALAM)
DEFNOTO(NotoSansMandaic_Regular_otf,			"Noto Sans Mandaic",			UCDN_SCRIPT_MANDAIC)
DEFNOTO(NotoSansManichaean_Regular_otf,			"Noto Sans Manichaean",			UCDN_SCRIPT_MANICHAEAN)
DEFNOTO(NotoSansMarchen_Regular_otf,			"Noto Sans Marchen",			UCDN_SCRIPT_MARCHEN)
DEFNOTO(NotoSansMasaramGondi_Regular_otf,		"Noto Sans Masaram Gondi",		UCDN_SCRIPT_MASARAM_GONDI)
DEFNOTO(NotoSansMedefaidrin_Regular_otf,		"Noto Sans Medefaidrin",		UCDN_SCRIPT_MEDEFAIDRIN)
DEFNOTO(NotoSansMeeteiMayek_Regular_otf,		"Noto Sans MeeteiMayek",		UCDN_SCRIPT_MEETEI_MAYEK)
DEFNOTO(NotoSansMendeKikakui_Regular_otf,		"Noto Sans MendeKikakui",		UCDN_SCRIPT_MENDE_KIKAKUI)
DEFNOTO(NotoSansMeroitic_Regular_otf,			"Noto Sans Meroitic",			UCDN_SCRIPT_MEROITIC_CURSIVE)
DEFNOTO(NotoSansMeroitic_Regular_otf,			"Noto Sans Meroitic",			UCDN_SCRIPT_MEROITIC_HIEROGLYPHS)
DEFNOTO(NotoSansMiao_Regular_otf,			"Noto Sans Miao",			UCDN_SCRIPT_MIAO)
DEFNOTO(NotoSansModi_Regular_otf,			"Noto Sans Modi",			UCDN_SCRIPT_MODI)
DEFNOTO(NotoSansMongolian_Regular_otf,			"Noto Sans Mongolian",			UCDN_SCRIPT_MONGOLIAN)
DEFNOTO(NotoSansMro_Regular_otf,			"Noto Sans Mro",			UCDN_SCRIPT_MRO)
DEFNOTO(NotoSansMultani_Regular_otf,			"Noto Sans Multani",			UCDN_SCRIPT_MULTANI)
DEFNOTO(NotoSerifMyanmar_Regular_otf,			"Noto Serif Myanmar",			UCDN_SCRIPT_MYANMAR)
DEFNOTO(NotoSansNabataean_Regular_otf,			"Noto Sans Nabataean",			UCDN_SCRIPT_NABATAEAN)
DEFNOTO(NotoSansNagMundari_Regular_otf,			"Noto Sans Nag Mundari",		UCDN_SCRIPT_NAG_MUNDARI)
DEFNOTO(NotoSansNandinagari_Regular_otf,		"Noto Sans Nandinagari",		UCDN_SCRIPT_NANDINAGARI)
DEFNOTO(NotoSansNewa_Regular_otf,			"Noto Sans Newa",			UCDN_SCRIPT_NEWA)
DEFNOTO(NotoSansNewTaiLue_Regular_otf,			"Noto Sans New Tai Lue",		UCDN_SCRIPT_NEW_TAI_LUE)
DEFNOTO(NotoSansNKo_Regular_otf,			"Noto Sans N Ko",			UCDN_SCRIPT_NKO)
DEFNOTO(NotoSansNushu_Regular_otf,			"Noto Sans Nushu",			UCDN_SCRIPT_NUSHU)
DEFNOTO(NotoSerifNyiakengPuachueHmong_Regular_otf,	"Noto Serif Nyiakeng Puachue Hmong",	UCDN_SCRIPT_NYIAKENG_PUACHUE_HMONG)
DEFNOTO(NotoSansOgham_Regular_otf,			"Noto Sans Ogham",			UCDN_SCRIPT_OGHAM)
DEFNOTO(NotoSansOldHungarian_Regular_otf,		"Noto Sans Old Hungarian",		UCDN_SCRIPT_OLD_HUNGARIAN)
DEFNOTO(NotoSansOldItalic_Regular_otf,			"Noto Sans Old Italic",			UCDN_SCRIPT_OLD_ITALIC)
DEFNOTO(NotoSansOldNorthArabian_Regular_otf,		"Noto Sans Old North Arabian",		UCDN_SCRIPT_OLD_NORTH_ARABIAN)
DEFNOTO(NotoSansOldPermic_Regular_otf,			"Noto Sans Old Permic",			UCDN_SCRIPT_OLD_PERMIC)
DEFNOTO(NotoSansOldPersian_Regular_otf,			"Noto Sans Old Persian",		UCDN_SCRIPT_OLD_PERSIAN)
DEFNOTO(NotoSansOldSogdian_Regular_otf,			"Noto Sans Old Sogdian",		UCDN_SCRIPT_OLD_SOGDIAN)
DEFNOTO(NotoSansOldSouthArabian_Regular_otf,		"Noto Sans Old South Arabian",		UCDN_SCRIPT_OLD_SOUTH_ARABIAN)
DEFNOTO(NotoSansOldTurkic_Regular_otf,			"Noto Sans Old Turkic",			UCDN_SCRIPT_OLD_TURKIC)
DEFNOTO(NotoSerifOldUyghur_Regular_otf,			"Noto Serif Old Uyghur",		UCDN_SCRIPT_OLD_UYGHUR)
DEFNOTO(NotoSansOlChiki_Regular_otf,			"Noto Sans Ol Chiki",			UCDN_SCRIPT_OL_CHIKI)
DEFNOTO(NotoSerifOriya_Regular_otf,			"Noto Serif Oriya",			UCDN_SCRIPT_ORIYA)
DEFNOTO(NotoSansOsage_Regular_otf,			"Noto Sans Osage",			UCDN_SCRIPT_OSAGE)
DEFNOTO(NotoSansOsmanya_Regular_otf,			"Noto Sans Osmanya",			UCDN_SCRIPT_OSMANYA)
DEFNOTO(NotoSansPahawhHmong_Regular_otf,		"Noto Sans Pahawh Hmong",		UCDN_SCRIPT_PAHAWH_HMONG)
DEFNOTO(NotoSansPalmyrene_Regular_otf,			"Noto Sans Palmyrene",			UCDN_SCRIPT_PALMYRENE)
DEFNOTO(NotoSansPauCinHau_Regular_otf,			"Noto Sans Pau Cin Hau",		UCDN_SCRIPT_PAU_CIN_HAU)
DEFNOTO(NotoSansPhagsPa_Regular_otf,			"Noto Sans Phags Pa",			UCDN_SCRIPT_PHAGS_PA)
DEFNOTO(NotoSansPhoenician_Regular_otf,			"Noto Sans Phoenician",			UCDN_SCRIPT_PHOENICIAN)
DEFNOTO(NotoSansPsalterPahlavi_Regular_otf,		"Noto Sans PsalterPahlavi",		UCDN_SCRIPT_PSALTER_PAHLAVI)
DEFNOTO(NotoSansRejang_Regular_otf,			"Noto Sans Rejang",			UCDN_SCRIPT_REJANG)
DEFNOTO(NotoSansRunic_Regular_otf,			"Noto Sans Runic",			UCDN_SCRIPT_RUNIC)
DEFNOTO(NotoSansSamaritan_Regular_otf,			"Noto Sans Samaritan",			UCDN_SCRIPT_SAMARITAN)
DEFNOTO(NotoSansSaurashtra_Regular_otf,			"Noto Sans Saurashtra",			UCDN_SCRIPT_SAURASHTRA)
DEFNOTO(NotoSansSharada_Regular_otf,			"Noto Sans Sharada",			UCDN_SCRIPT_SHARADA)
DEFNOTO(NotoSansShavian_Regular_otf,			"Noto Sans Shavian",			UCDN_SCRIPT_SHAVIAN)
DEFNOTO(NotoSansSiddham_Regular_otf,			"Noto Sans Siddham",			UCDN_SCRIPT_SIDDHAM)
DEFNOTO(NotoSerifSinhala_Regular_otf,			"Noto Serif Sinhala",			UCDN_SCRIPT_SINHALA)
DEFNOTO(NotoSansSogdian_Regular_otf,			"Noto Sans Sogdian",			UCDN_SCRIPT_SOGDIAN)
DEFNOTO(NotoSansSoraSompeng_Regular_otf,		"Noto Sans SoraSompeng",		UCDN_SCRIPT_SORA_SOMPENG)
DEFNOTO(NotoSansSoyombo_Regular_otf,			"Noto Sans Soyombo",			UCDN_SCRIPT_SOYOMBO)
DEFNOTO(NotoSansSundanese_Regular_otf,			"Noto Sans Sundanese",			UCDN_SCRIPT_SUNDANESE)
DEFNOTO(NotoSansSylotiNagri_Regular_otf,		"Noto Sans Syloti Nagri",		UCDN_SCRIPT_SYLOTI_NAGRI)
DEFNOTO(NotoSansSyriac_Regular_otf,			"Noto Sans Syriac",			UCDN_SCRIPT_SYRIAC)
DEFNOTO(NotoSansTagalog_Regular_otf,			"Noto Sans Tagalog",			UCDN_SCRIPT_TAGALOG)
DEFNOTO(NotoSansTagbanwa_Regular_otf,			"Noto Sans Tagbanwa",			UCDN_SCRIPT_TAGBANWA)
DEFNOTO(NotoSansTaiLe_Regular_otf,			"Noto Sans TaiLe",			UCDN_SCRIPT_TAI_LE)
DEFNOTO(NotoSansTaiTham_Regular_otf,			"Noto Sans Tai Tham",			UCDN_SCRIPT_TAI_THAM)
DEFNOTO(NotoSansTaiViet_Regular_otf,			"Noto Sans Tai Viet",			UCDN_SCRIPT_TAI_VIET)
DEFNOTO(NotoSansTakri_Regular_otf,			"Noto Sans Takri",			UCDN_SCRIPT_TAKRI)
DEFNOTO(NotoSansTangsa_Regular_otf,			"Noto Sans Tangsa",			UCDN_SCRIPT_TANGSA)
DEFNOTO(NotoSerifTamil_Regular_otf,			"Noto Serif Tamil",			UCDN_SCRIPT_TAMIL)
DEFNOTO(NotoSerifTelugu_Regular_otf,			"Noto Serif Telugu",			UCDN_SCRIPT_TELUGU)
DEFNOTO(NotoSansThaana_Regular_otf,			"Noto Sans Thaana",			UCDN_SCRIPT_THAANA)
DEFNOTO(NotoSerifThai_Regular_otf,			"Noto Serif Thai",			UCDN_SCRIPT_THAI)
DEFNOTO(NotoSerifTibetan_Regular_otf,			"Noto Serif Tibetan",			UCDN_SCRIPT_TIBETAN)
DEFNOTO(NotoSansTifinagh_Regular_otf,			"Noto Sans Tifinagh",			UCDN_SCRIPT_TIFINAGH)
DEFNOTO(NotoSansTirhuta_Regular_otf,			"Noto Sans Tirhuta",			UCDN_SCRIPT_TIRHUTA)
DEFNOTO(NotoSerifToto_Regular_otf,			"Noto Serif Toto",			UCDN_SCRIPT_TOTO)
DEFNOTO(NotoSansUgaritic_Regular_otf,			"Noto Sans Ugaritic",			UCDN_SCRIPT_UGARITIC)
DEFNOTO(NotoSansVai_Regular_otf,			"Noto Sans Vai",			UCDN_SCRIPT_VAI)
DEFNOTO(NotoSerifVithkuqi_Regular_otf,			"Noto Serif Vithukqi",			UCDN_SCRIPT_VITHKUQI)
DEFNOTO(NotoSansWancho_Regular_otf,			"Noto Sans Wancho",			UCDN_SCRIPT_WANCHO)
DEFNOTO(NotoSansWarangCiti_Regular_otf,			"Noto Sans Warang Citi",		UCDN_SCRIPT_WARANG_CITI)
DEFNOTO(NotoSerifYezidi_Regular_otf,			"Noto Serif Yezidi",			UCDN_SCRIPT_YEZIDI)
DEFNOTO(NotoSansYi_Regular_otf,				"Noto Sans Yi",				UCDN_SCRIPT_YI)
DEFNOTO(NotoSansZanabazarSquare_Regular_otf,		"Noto Sans Zanabazar Square",		UCDN_SCRIPT_ZANABAZAR_SQUARE)

#if NOTO_TANGUT
DEFNOTO(NotoSerifTangut_Regular_otf,			"Noto Serif Tangut",			UCDN_SCRIPT_TANGUT)
#endif

#if NOTO_SIGNWRITING
DEFNOTO(NotoSansSignWriting_Regular_otf,		"Noto Sans SignWriting",		UCDN_SCRIPT_SIGNWRITING)
#endif

#undef DEFNOTO

#define DEFNOTO(symbol, name, script) EMPTY(script)

DEFNOTO(NotoSansTodhri_Regular_otf,			"Noto Sans Todhri",			UCDN_SCRIPT_TODHRI)
DEFNOTO(NotoSansGaray_Regular_otf,			"Noto Sans Garay",			UCDN_SCRIPT_GARAY)
DEFNOTO(NotoSansTuluTigalari_Regular_otf,		"Noto Sans Tulu Tigalari",		UCDN_SCRIPT_TULU_TIGALARI)
DEFNOTO(NotoSansSunuwar_Regular_otf,			"Noto Sans Sunuwar",			UCDN_SCRIPT_SUNUWAR)
DEFNOTO(NotoSansGurungKhema_Regular_otf,		"Noto Sans Gurung Khema",		UCDN_SCRIPT_GURUNG_KHEMA)
DEFNOTO(NotoSansKiratRai_Regular_otf,			"Noto Sans Kirat Rai",			UCDN_SCRIPT_KIRAT_RAI)
DEFNOTO(NotoSansOlOnal_Regular_otf,			"Noto Sans Ol Onal",			UCDN_SCRIPT_OL_ONAL)

#undef DEFNOTO

#ifndef TOFU_SYMBOL
FONT(noto,	NotoSansMath_Regular_otf,	"Noto Sans Math",	MUPDF_SCRIPT_MATH,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)
FONT(noto,	NotoMusic_Regular_otf,		"Noto Music",		MUPDF_SCRIPT_MUSIC,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)
FONT(noto,	NotoSansSymbols_Regular_otf,	"Noto Sans Symbols",	MUPDF_SCRIPT_SYMBOLS,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)
ALIAS(noto,	NotoSansSymbols_Regular_otf,	"Noto Sans Symbols",	UCDN_SCRIPT_BRAILLE,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)
FONT(noto,	NotoSansSymbols2_Regular_otf,	"Noto Sans Symbols2",	MUPDF_SCRIPT_SYMBOLS2,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)
FONT(noto,	NotoEmoji_Regular_ttf,		"Noto Emoji",		MUPDF_SCRIPT_EMOJI,	FZ_LANG_UNSET,	NO_SUBFONT,	REGULAR)
#endif

#endif /* TOFU_NOTO */

#ifndef TOFU_CJK
#ifndef TOFU_CJK_EXT
#ifndef TOFU_CJK_LANG

FONT(han,	SourceHanSerif_Regular_ttc,	"Source Han Serif",		UCDN_SCRIPT_HAN,	FZ_LANG_ja,		0,	REGULAR)
ALIAS(han,	SourceHanSerif_Regular_ttc,	"Source Han Serif",		UCDN_SCRIPT_HIRAGANA,	FZ_LANG_UNSET,		0,	REGULAR)
ALIAS(han,	SourceHanSerif_Regular_ttc,	"Source Han Serif",		UCDN_SCRIPT_KATAKANA,	FZ_LANG_UNSET,		0,	REGULAR)

FONT(han,	SourceHanSerif_Regular_ttc,	"Source Han Serif",		UCDN_SCRIPT_HAN,	FZ_LANG_ko,		1,	REGULAR)
ALIAS(han,	SourceHanSerif_Regular_ttc,	"Source Han Serif",		UCDN_SCRIPT_HANGUL,	FZ_LANG_UNSET,		1,	REGULAR)

FONT(han,	SourceHanSerif_Regular_ttc,	"Source Han Serif",		UCDN_SCRIPT_HAN,	FZ_LANG_zh_Hans,	2,	REGULAR)

FONT(han,	SourceHanSerif_Regular_ttc,	"Source Han Serif",		UCDN_SCRIPT_HAN,	FZ_LANG_zh_Hant,	3,	REGULAR)
ALIAS(han,	SourceHanSerif_Regular_ttc,	"Source Han Serif",		UCDN_SCRIPT_HAN,	FZ_LANG_UNSET,		3,	REGULAR)
ALIAS(han,	SourceHanSerif_Regular_ttc,	"Source Han Serif",		UCDN_SCRIPT_BOPOMOFO,	FZ_LANG_UNSET,		3,	REGULAR)

#else

FONT(droid,	DroidSansFallbackFull_ttf,	"Droid Sans Fallback Full",	UCDN_SCRIPT_HAN,	FZ_LANG_UNSET,		0,	REGULAR)
ALIAS(droid,	DroidSansFallbackFull_ttf,	"Droid Sans Fallback Full",	UCDN_SCRIPT_HANGUL,	FZ_LANG_UNSET,		0,	REGULAR)
ALIAS(droid,	DroidSansFallbackFull_ttf,	"Droid Sans Fallback Full",	UCDN_SCRIPT_HIRAGANA,	FZ_LANG_UNSET,		0,	REGULAR)
ALIAS(droid,	DroidSansFallbackFull_ttf,	"Droid Sans Fallback Full",	UCDN_SCRIPT_KATAKANA,	FZ_LANG_UNSET,		0,	REGULAR)
ALIAS(droid,	DroidSansFallbackFull_ttf,	"Droid Sans Fallback Full",	UCDN_SCRIPT_BOPOMOFO,	FZ_LANG_UNSET,		0,	REGULAR)

#endif
#else

FONT(droid,	DroidSansFallback_ttf,		"Droid Sans Fallback",		UCDN_SCRIPT_HAN,	FZ_LANG_UNSET,		0,	REGULAR)
ALIAS(droid,	DroidSansFallback_ttf,		"Droid Sans Fallback",		UCDN_SCRIPT_HANGUL,	FZ_LANG_UNSET,		0,	REGULAR)
ALIAS(droid,	DroidSansFallback_ttf,		"Droid Sans Fallback",		UCDN_SCRIPT_HIRAGANA,	FZ_LANG_UNSET,		0,	REGULAR)
ALIAS(droid,	DroidSansFallback_ttf,		"Droid Sans Fallback",		UCDN_SCRIPT_KATAKANA,	FZ_LANG_UNSET,		0,	REGULAR)
ALIAS(droid,	DroidSansFallback_ttf,		"Droid Sans Fallback",		UCDN_SCRIPT_BOPOMOFO,	FZ_LANG_UNSET,		0,	REGULAR)

#endif
#endif
