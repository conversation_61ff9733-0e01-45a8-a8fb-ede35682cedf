 <!DOCTYPE html PUBLIC "-//W3C//DTD XHTML+RDFa 1.0//EN" "http://www.w3.org/MarkUp/DTD/xhtml-rdfa-1.dtd"> <html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en"> <head profile="http://dublincore.org/documents/dcq-html/"> <!-- Barlesque 2.70.0 --> <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" /> <meta name="description" content="Breaking news, sport, TV, radio and a whole lot more. The BBC informs, educates and entertains - wherever you are, whatever your age." /> <meta name="keywords" content="BBC, bbc.co.uk, bbc.com, Search, British Broadcasting Corporation, BBC iPlayer, BBCi" />  <meta name="DCTERMS.created" content="" />    <title>BBC - Homepage</title>     
   <meta http-equiv="X-UA-Compatible" content="IE=8" />  
  <link rel="schema.dcterms" href="http://purl.org/dc/terms/" />  <link rel="index" href="http://www.bbc.co.uk/a-z/" title="A to Z" /> <link rel="copyright" href="http://www.bbc.co.uk/terms/" title="Terms of Use" /> <link rel="icon" href="http://www.bbc.co.uk/favicon.ico" type="image/x-icon" />  <meta name="viewport" content="width = 996" /> 

<script type="text/javascript">window.bbcredirection={geo:true,device:true}</script><script type="text/javascript">/*<![CDATA[*/
window.orb = window.orb || {};
(function() {
    'use strict';
    window.fig = window.fig || {};
    window.fig.manager = {
                include: function(w) {
            w = w || window;
            var d = w.document,
                c = d.cookie,
                f = c.match(/ckns_orb_fig=([^;]+)/);

            if ( !f && c.indexOf('ckns_orb_nofig=1') > -1 ) {
                this.setFig(w, {no:1});
            }
            else {
                if (f) {
                    eval('f = ' + decodeURIComponent(RegExp.$1) + ';')
                    this.setFig(w, f);                  
                }
                d.write('<script src="https://ssl.bbc.co.uk/frameworks/fig/1/fig.js"><'+'/script>');
            }         
  
        },
                confirm: function(w) {
            w = w || window;
            if (w.orb && w.orb.fig && w.orb.fig('no')) {
               this.setFigCookie(w);
            }
            
            if (w.orb === undefined || w.orb.fig === undefined) {
                this.setFig(w, {no:1});
                this.setFigCookie(w);
            }
        },
                setFigCookie: function(w) {
            w.document.cookie = 'ckns_orb_nofig=1; expires=' + new Date(new Date().getTime() + 1000 * 60 * 10).toGMTString() + ';';
        },
                setFig: function(w, f){
            (function(){var o=f;w.orb=w.orb||{};w.orb.fig=function(k){return (arguments.length)? o[k]:o};})();
        }
    }
})();
fig.manager.include();
/*]]>*/</script>
<script type="text/javascript"> fig.manager.confirm(); </script>

 <link rel="stylesheet" type="text/css" href="http://static.bbci.co.uk/frameworks/barlesque/2.70.0/desktop/3.5/style/main.css"  />  <link rel="stylesheet" type="text/css" href="http://static.bbci.co.uk/wwhp/1.45.0/desktop/css/homepage-compiled.css"  />  <script type="text/javascript">/*<![CDATA[*/ (function(undefined){if(!window.bbc){window.bbc={}}var ROLLING_PERIOD_DAYS=30;window.bbc.Mandolin=function(id,segments,opts){var now=new Date().getTime(),storedItem,DEFAULT_START=now,DEFAULT_RATE=1,COOKIE_NAME="ckpf_mandolin";opts=opts||{};this._id=id;this._segmentSet=segments;this._store=new window.window.bbc.Mandolin.Storage(COOKIE_NAME);this._opts=opts;this._rate=(opts.rate!==undefined)?+opts.rate:DEFAULT_RATE;this._startTs=(opts.start!==undefined)?new Date(opts.start).getTime():new Date(DEFAULT_START).getTime();this._endTs=(opts.end!==undefined)?new Date(opts.end).getTime():daysFromNow(ROLLING_PERIOD_DAYS);this._signupEndTs=(opts.signupEnd!==undefined)?new Date(opts.signupEnd).getTime():this._endTs;this._segment=null;if(typeof id!=="string"){throw new Error("Invalid Argument: id must be defined and be a string")}if(Object.prototype.toString.call(segments)!=="[object Array]"){throw new Error("Invalid Argument: Segments are required.")}if(opts.rate!==undefined&&(opts.rate<0||opts.rate>1)){throw new Error("Invalid Argument: Rate must be between 0 and 1.")}if(this._startTs>this._endTs){throw new Error("Invalid Argument: end date must occur after start date.")}if(!(this._startTs<this._signupEndTs&&this._signupEndTs<=this._endTs)){throw new Error("Invalid Argument: SignupEnd must be between start and end date")}removeExpired.call(this,now);if((storedItem=this._store.getItem(this._id))){this._segment=storedItem.segment}else{if(this._startTs<=now&&now<this._signupEndTs&&now<=this._endTs&&this._store.isEnabled()===true){this._segment=pick(segments,this._rate);if(opts.end===undefined){this._store.setItem(this._id,{segment:this._segment})}else{this._store.setItem(this._id,{segment:this._segment,end:this._endTs})}log.call(this,"mandolin_segment")}}log.call(this,"mandolin_view")};window.bbc.Mandolin.prototype.getSegment=function(){return this._segment};function log(actionType,params){var that=this;require(["istats-1"],function(istats){istats.log(actionType,that._id+":"+that._segment,params?params:{})})}function removeExpired(expires){var items=this._store.getItems(),expiresInt=+expires;for(var key in items){if(items[key].end!==undefined&&+items[key].end<expiresInt){this._store.removeItem(key)}}}function getLastExpirationDate(data){var winner=0,rollingExpire=daysFromNow(ROLLING_PERIOD_DAYS);for(var key in data){if(data[key].end===undefined&&rollingExpire>winner){winner=rollingExpire}else{if(+data[key].end>winner){winner=+data[key].end}}}return(winner)?new Date(winner):new Date(rollingExpire)}window.bbc.Mandolin.prototype.log=function(params){log.call(this,"mandolin_log",params)};window.bbc.Mandolin.prototype.convert=function(params){log.call(this,"mandolin_convert",params);this.convert=function(){}};function daysFromNow(n){var endDate;endDate=new Date().getTime()+(n*60*60*24)*1000;return endDate}function pick(segments,rate){var picked,min=0,max=segments.length-1;if(typeof rate==="number"&&Math.random()>rate){return null}do{picked=Math.floor(Math.random()*(max-min+1))+min}while(picked>max);return segments[picked]}window.bbc.Mandolin.Storage=function(name){this._cookieName=name;this._isEnabled=(bbccookies.isAllowed(this._cookieName)===true&&bbccookies.cookiesEnabled()===true)};window.bbc.Mandolin.Storage.prototype.setItem=function(key,value){var storeData=this.getItems();storeData[key]=value;this.save(storeData);return value};window.bbc.Mandolin.Storage.prototype.isEnabled=function(){return this._isEnabled};window.bbc.Mandolin.Storage.prototype.getItem=function(key){var storeData=this.getItems();return storeData[key]};window.bbc.Mandolin.Storage.prototype.removeItem=function(key){var storeData=this.getItems();delete storeData[key];this.save(storeData)};window.bbc.Mandolin.Storage.prototype.getItems=function(){return deserialise(this.readCookie(this._cookieName)||"")};window.bbc.Mandolin.Storage.prototype.save=function(data){window.bbccookies.set(this._cookieName+"="+encodeURIComponent(serialise(data))+"; expires="+getLastExpirationDate(data).toUTCString()+";")};window.bbc.Mandolin.Storage.prototype.readCookie=function(name){var nameEQ=name+"=",ca=window.bbccookies.get().split(";"),i,c;for(i=0;i<ca.length;i++){c=ca[i];while(c.charAt(0)===" "){c=c.substring(1,c.length)}if(c.indexOf(nameEQ)===0){return decodeURIComponent(c.substring(nameEQ.length,c.length))}}return null};function serialise(o){var str="";for(var p in o){if(o.hasOwnProperty(p)){str+='"'+p+'"'+":"+(typeof o[p]==="object"?(o[p]===null?"null":"{"+serialise(o[p])+"}"):'"'+o[p].toString().replace(/"/g,'\\"')+'"')+","}}return str.replace(/,\}/g,"}").replace(/,$/g,"")}function deserialise(str){var o;eval("o = {"+str+"}");return o}})(); /*]]>*/</script>  <script type="text/javascript">/*<![CDATA[*/ if (typeof bbccookies_flag === 'undefined') { bbccookies_flag = 'ON'; } showCTA_flag = false; cta_enabled = (showCTA_flag && (bbccookies_flag === 'ON') ); (function(){var e="ckns_policy",m="Thu, 01 Jan 1970 00:00:00 GMT",k={ads:true,personalisation:true,performance:true,necessary:true};function f(p){if(f.cache[p]){return f.cache[p]}var o=p.split("/"),q=[""];do{q.unshift((o.join("/")||"/"));o.pop()}while(q[0]!=="/");f.cache[p]=q;return q}f.cache={};function a(p){if(a.cache[p]){return a.cache[p]}var q=p.split("."),o=[];while(q.length&&"|co.uk|com|".indexOf("|"+q.join(".")+"|")===-1){if(q.length){o.push(q.join("."))}q.shift()}f.cache[p]=o;return o}a.cache={};function i(o,t,p){var z=[""].concat(a(window.location.hostname)),w=f(window.location.pathname),y="",r,x;for(var s=0,v=z.length;s<v;s++){r=z[s];for(var q=0,u=w.length;q<u;q++){x=w[q];y=o+"="+t+";"+(r?"domain="+r+";":"")+(x?"path="+x+";":"")+(p?"expires="+p+";":"");bbccookies.set(y,true)}}}window.bbccookies={_setEverywhere:i,cookiesEnabled:function(){var o="ckns_testcookie"+Math.floor(Math.random()*100000);this.set(o+"=1");if(this.get().indexOf(o)>-1){g(o);return true}return false},set:function(o){return document.cookie=o},get:function(){return document.cookie},_setPolicy:function(o){return h.apply(this,arguments)},readPolicy:function(o){return b.apply(this,arguments)},_deletePolicy:function(){i(e,"",m)},isAllowed:function(){return true},_isConfirmed:function(){return c()!==null},_acceptsAll:function(){var o=b();return o&&!(j(o).indexOf("0")>-1)},_getCookieName:function(){return d.apply(this,arguments)},_showPrompt:function(){var o=(!this._isConfirmed()&&window.cta_enabled&&this.cookiesEnabled()&&!window.bbccookies_disable);return(window.orb&&window.orb.fig)?o&&(window.orb.fig("no")||window.orb.fig("ck")):o}};bbccookies._getPolicy=bbccookies.readPolicy;function d(p){var o=(""+p).match(/^([^=]+)(?==)/);return(o&&o.length?o[0]:"")}function j(o){return""+(o.ads?1:0)+(o.personalisation?1:0)+(o.performance?1:0)}function h(r){if(typeof r==="undefined"){r=k}if(typeof arguments[0]==="string"){var o=arguments[0],q=arguments[1];if(o==="necessary"){q=true}r=b();r[o]=q}else{if(typeof arguments[0]==="object"){r.necessary=true}}var p=new Date();p.setYear(p.getFullYear()+1);p=p.toUTCString();bbccookies.set(e+"="+j(r)+";domain=bbc.co.uk;path=/;expires="+p+";");bbccookies.set(e+"="+j(r)+";domain=bbc.com;path=/;expires="+p+";");return r}function l(o){if(o===null){return null}var p=o.split("");return{ads:!!+p[0],personalisation:!!+p[1],performance:!!+p[2],necessary:true}}function c(){var o=new RegExp("(?:^|; ?)"+e+"=(\\d\\d\\d)($|;)"),p=document.cookie.match(o);if(!p){return null}return p[1]}function b(o){var p=l(c());if(!p){p=k}if(o){return p[o]}else{return p}}function g(o){return document.cookie=o+"=;expires="+m+";"}function n(){var o='<script type="text/javascript" src="http://static.bbci.co.uk/frameworks/bbccookies/0.6.6/script/bbccookies.js"><\/script>';if(window.bbccookies_flag==="ON"&&!bbccookies._acceptsAll()&&!window.bbccookies_disable){document.write(o)}}n()})(); /*]]>*/</script>      <script type="text/javascript"> if (! window.gloader) { window.gloader = [ "glow", {map: "http://node1.bbcimg.co.uk/glow/glow/map.1.7.7.js"}]; } </script>  <script type="text/javascript" src="http://node1.bbcimg.co.uk/glow/gloader.0.1.6.js"></script>   <script type="text/javascript" src="http://static.bbci.co.uk/frameworks/requirejs/0.14.6/sharedmodules/require.js"></script> <script type="text/javascript">  bbcRequireMap = {"jquery-1":"http://static.bbci.co.uk/frameworks/jquery/0.3.0/sharedmodules/jquery-1.7.2", "jquery-1.4":"http://static.bbci.co.uk/frameworks/jquery/0.3.0/sharedmodules/jquery-1.4", "jquery-1.9":"http://static.bbci.co.uk/frameworks/jquery/0.3.0/sharedmodules/jquery-1.9.1", "swfobject-2":"http://static.bbci.co.uk/frameworks/swfobject/0.1.10/sharedmodules/swfobject-2", "demi-1":"http://static.bbci.co.uk/frameworks/demi/0.10.0/sharedmodules/demi-1", "gelui-1":"http://static.bbci.co.uk/frameworks/gelui/0.9.13/sharedmodules/gelui-1", "cssp!gelui-1/overlay":"http://static.bbci.co.uk/frameworks/gelui/0.9.13/sharedmodules/gelui-1/overlay.css", "istats-1":"http://static.bbci.co.uk/frameworks/istats/0.19.0/modules/istats-1", "relay-1":"http://static.bbci.co.uk/frameworks/relay/0.2.4/sharedmodules/relay-1", "clock-1":"http://static.bbci.co.uk/frameworks/clock/0.1.9/sharedmodules/clock-1", "canvas-clock-1":"http://static.bbci.co.uk/frameworks/clock/0.1.9/sharedmodules/canvas-clock-1", "cssp!clock-1":"http://static.bbci.co.uk/frameworks/clock/0.1.9/sharedmodules/clock-1.css", "jssignals-1":"http://static.bbci.co.uk/frameworks/jssignals/0.3.6/modules/jssignals-1", "jcarousel-1":"http://static.bbci.co.uk/frameworks/jcarousel/0.1.10/modules/jcarousel-1", "desktop/ui/carouselfactory":"http://static.bbci.co.uk/wwhp/1.45.0/modules/desktop/ui/carouselfactory", "desktop/ui/tabs":"http://static.bbci.co.uk/wwhp/1.45.0/modules/desktop/ui/tabs", "desktop/ui/tennisresults":"http://static.bbci.co.uk/wwhp/1.45.0/modules/desktop/ui/tennisresults", "desktop/ui/weather":"http://static.bbci.co.uk/wwhp/1.45.0/modules/desktop/ui/weather", "desktop/utils/date":"http://static.bbci.co.uk/wwhp/1.45.0/modules/desktop/utils/date", "desktop/utils/popup":"http://static.bbci.co.uk/wwhp/1.45.0/modules/desktop/utils/popup", "homepage":"http://static.bbci.co.uk/wwhp/1.45.0/modules/homepage", "lib/external/moodular":"http://static.bbci.co.uk/wwhp/1.45.0/modules/lib/external/moodular"}; require({ baseUrl: 'http://static.bbci.co.uk/', paths: bbcRequireMap, waitSeconds: 30 }); </script>      <script type="text/javascript" src="http://static.bbci.co.uk/frameworks/barlesque/2.70.0/desktop/3.5/script/barlesque.js"></script>
  
<!--[if IE 6]>
        <script type="text/javascript">
        try {
            document.execCommand("BackgroundImageCache",false,true);
        } catch(e) {}
    </script>
        <style type="text/css">
        /* Use filters for IE6 */
        #blq-blocks a {
            background-image: none;
            filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src='http://static.bbci.co.uk/frameworks/barlesque/2.70.0/desktop/3.5//img/blq-blocks_white_alpha.png', sizingMethod='image');
        }
        .blq-masthead-focus #blq-blocks a,
        .blq-mast-text-dark #blq-blocks a {
            background-image: none;
            filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src='http://static.bbci.co.uk/frameworks/barlesque/2.70.0/desktop/3.5//img/blq-blocks_grey_alpha.png', sizingMethod='image');
        }
        #blq-nav-search button span {
            background-image: none;
            filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src='http://static.bbci.co.uk/frameworks/barlesque/2.70.0/desktop/3.5//img/blq-search_grey_alpha.png', sizingMethod='image');
        }
        #blq-nav-search button img {
            position: absolute;
            left: -999em;    
        }
    </style>
<![endif]-->

<!--[if (IE 7])|(IE 8)>
    <style type="text/css">
        .blq-clearfix {
            display: inline-block;
        }
    </style>
<![endif]-->

<script type="text/javascript">
     blq.setEnvironment('live');  if (blq.setLabel) blq.setLabel('searchSuggestion', "Search");  if (! /bbc\.co\.uk$/i.test(window.location.hostname) ) { document.documentElement.className += ' blq-not-bbc-co-uk'; } </script>

  <script type="text/javascript"> /*<![CDATA[*/ function oqsSurveyManager(w, flag) {  var defaultThreshold = 0, usePulseThreshold = (flag === 'OFF')? 1 : defaultThreshold, activeThreshold; w.oqs = w.oqs || {}; /* we support cookie override for testing */ if ( w.document.cookie.match(/(?:^|; *)ckns_oqs_usePulseThreshold=([\d.]+)/) ) { activeThreshold = RegExp.$1; } /* we support clientside override */ else if (typeof w.oqs_usePulseThreshold !== 'undefined') { activeThreshold = w.oqs_usePulseThreshold; } else { activeThreshold = usePulseThreshold; } w.oqs.usePulse = (w.Math.random() < activeThreshold); if (!w.oqs.usePulse) {  w.document.write('<script type="text/javascript" src="http://static.bbci.co.uk/frameworks/barlesque/2.70.0/desktop/3.5/script/vendor/edr.js"><'+'/script>'); } } oqsSurveyManager(window, 'ON'); /*]]>*/ </script> 
        <!-- BBCDOTCOM template: desktop journalismVariant: false ipIsAdvertiseCombined: true adsEnabled: true flagpole: ON -->
            <script type="text/javascript">
            /*<![CDATA[*/
            if (window.bbcdotcom === undefined) {
                var bbcdotcom = {
                    data: {},
                    advert: {
                        write: function () {},
                        show: function () {},
                        isActive: function () {
                            return false;
                        }
                    },
                    objects: function(str) {
                        return false;
                    }
                };
            }
            bbcdotcom.objects = function (strName, action, obj) {
                var nameParts, i, len;
                nameParts = strName.split(".");
                if (action === undefined) {
                    action = 'valid';
                }
                if (obj === undefined) {
                    obj = window;
                }
                for (i = 0, len = nameParts.length; i < len; i++) {
                    if (obj[nameParts[i]] === undefined) {
                        if (action === 'create') {
                            obj[nameParts[i]] = {};
                        } else {
                            return false;
                        }
                    } else if (typeof obj[nameParts[i]] === 'function') {
                        if(obj[nameParts[i]]() !== undefined) {
                            return obj[nameParts[i]]();
                        }
                    }
                    obj = obj[nameParts[i]];
                }
                return obj;
            };
            /*]]>*/
        </script>
        <style type="text/css">.bbccom_display_none{display:none;}</style>
        <script type="text/javascript">
            /*<![CDATA[*/
            bbcdotcom.objects('bbcdotcom.config', 'create');
            bbcdotcom.config =(function(ads, analytics) {
                var adsEnabled=ads,
                    analyticsEnabled=analytics,
                    jsPrefix="http://static.bbci.co.uk/bbcdotcom/0.3.267/script",
                    swfPrefix="http://static.bbci.co.uk/bbcdotcom/0.3.267/swf",
                    cssPrefix="http://static.bbci.co.uk/bbcdotcom/0.3.267/style";
                return {
                    setAdsEnabled: function(enabled) {
                        /* Once it has been disable to not allow it to be enabled */
                        adsEnabled = (adsEnabled !== 0) ? enabled : 0;
                    },
                    isAdsEnabled: function() {
                        return adsEnabled;
                    },
                    setAnalyticsEnabled: function(enabled) {
                        /* Once it has been disable to not allow it to be enabled */
                        analyticsEnabled = (analyticsEnabled !== 0) ? enabled : 0;
                    },
                    isAnalyticsEnabled: function() {
                        return analyticsEnabled;
                    },
                    setJsPrefix: function (prefix) {
                        jsPrefix = prefix;
                    },
                    getJsPrefix: function () {
                        return jsPrefix;
                    },
                    setSwfPrefix: function (prefix) {
                        swfPrefix = prefix;
                    },
                    getSwfPrefix: function () {
                        return swfPrefix;
                    },
                    setCssPrefix: function (prefix) {
                        cssPrefix = prefix;
                    },
                    getCssPrefix: function () {
                        return cssPrefix;
                    }
                };
            }(1, 1));
            bbcdotcom.objects('bbcdotcom.siteCatalyst', 'create');
            bbcdotcom.siteCatalyst = {"ch":"","cdp":"3","ce":"UTF-8"};
            bbcdotcom.objects('bbcdotcom.stats', 'create');
            /* Create BBC.adverts skeleton */
            if (window.BBC === undefined) {
                var BBC = {};
            }
            if (window.BBC.adverts === undefined) {
                BBC.adverts = {
                    setZone: function () {},
                    configure: function () {},
                    write: function () {},
                    show: function () {},
                    isActive: function () {
                        return false;
                    },
                    setScriptRoot: function () {},
                    setImgRoot: function () {},
                    getAdvertTag: function () {},
                    getSectionPath: function() {},
                    showPartnerButtons: function() {}
                };
            }
            (function(){
                if(typeof require !== 'undefined') {
                    require({
                        paths:{
                            "bbcdotcom":"http://static.bbci.co.uk/bbcdotcom/0.3.267/script"
                        }
                    });
                }
            })();
            if (typeof orb !== 'undefined' && typeof orb.fig === 'function') {
                bbcdotcom.data = {
                    a: orb.fig('ad')? 1 : 0,
                    b: (0 == orb.fig('uk')) ? 1 : 0,
                    c: orb.fig('ap')
                };
            } else {
                document.write('<script type="text/javascript" src="http://tps.bbc.com/wwscripts/data">\x3C/script>');
            }
            /*]]>*/
        </script>
        <script type="text/javascript">
            /*<![CDATA[*/
            if (typeof bbcdotcom.data == 'undefined' || typeof bbcdotcom.data.a == 'undefined' || typeof bbcdotcom.data.b == 'undefined' || typeof bbcdotcom.data.c == 'undefined') {
                bbcdotcom.data = {a:0,b:0,c:0};
            }
            if (bbcdotcom.data.a == 1) {
                document.write('<script type="text/javascript" src="http://www.bbc.co.uk/wwscripts/flag">\x3C/script>');
            }
            /*]]>*/
        </script>                                   <script type="text/javascript">                 /*<![CDATA[*/
            if (typeof bbcdotcom.flag == 'undefined' || bbcdotcom.flag.a != 1) {
                                bbcdotcom.data.a = 0;
            }
        </script>

                            <!-- Source: /snippet/controltag?confid=JA8mItOH&site=BBC.com%20Homepage&edit=1 -->
            <script class="kxct" data-id="JA8mItOH" data-timing="async" data-version="1.9" type="text/javascript">
            /*<![CDATA[*/
                if (bbcdotcom.data.a == 1 || bbcdotcom.data.b == 1) {
                    window.Krux||((Krux=function(){Krux.q.push(arguments)}).q=[]);
                    (function(){
                        var k=document.createElement('script');k.type='text/javascript';k.async=true;
                        var m,src=(m=location.href.match(/\bkxsrc=([^&]+)/))&&decodeURIComponent(m[1]);
                        k.src = /^https?:\/\/([a-z0-9_\-\.]+\.)?krxd\.net(:\d{1,5})?\//i.test(src) ? src : src === "disable" ? "" :
                        (location.protocol==="https:"?"https:":"http:")+"//cdn.krxd.net/controltag?confid=JA8mItOH";
                        var s=document.getElementsByTagName('script')[0];s.parentNode.insertBefore(k,s);
                    }());
                }
            /*]]>*/
            </script>
            <script type="text/javascript">
                /*<![CDATA[*/
                if (bbcdotcom.data.a == 1 || bbcdotcom.data.b == 1) {
                    window.Krux||((Krux=function(){Krux.q.push(arguments);}).q=[]);
                    (function(){
                        function  retrieve(n){
                            var  m,  k='kx'+n;
                            if  (window.localStorage)  {
                                return  window.localStorage[k]  ||  "";
                            }  else  if  (navigator.cookieEnabled)  {
                                m  =  document.cookie.match(k+'=([^;]*)');
                                return  (m  &&  unescape(m[1]))  ||  "";
                            }  else  {
                                return  '';
                            }
                        }
                        Krux.user  =  retrieve('user');
                        Krux.segments  =  retrieve('segs')  ?  retrieve('segs').split(',')  :  [];
                        //  DFP  Premium
                        var  dfpp  =  [];
                        if  (Krux.user)  {
                            dfpp.push('khost='  +  encodeURIComponent(location.hostname));
                            dfpp.push('kuid='  +  Krux.user);
                        }
                        for  (var  i  =  0;  i  <  Krux.segments.length;  i++  )  {
                            dfpp.push('ksg='  +  Krux.segments[i]);
                        }
                        Krux.dfppKeyValues  =  dfpp.length  ?  dfpp.join(';')  +  ';'  :  '';
                    })();
                }
                /*]]>*/
            </script>
        
        <script type="text/javascript">
            /*<![CDATA[*/
            if(bbcdotcom.data.a == 1 || bbcdotcom.data.b == 1) {
                document.write('<link rel="stylesheet" type="text/css" href="http://static.bbci.co.uk/bbcdotcom/0.3.267/style/3pt_ads.css" />');document.write('<script type="text/javascript" src="http://static.bbci.co.uk/bbcdotcom/0.3.267/script/bbccom.js">\x3C/script>');            }
            /*]]>*/
        </script>                                   <script type="text/javascript">                 /*<![CDATA[*/
            if (bbcdotcom.data.a == 1 || bbcdotcom.data.b == 1) {
                                BBC.adverts.setData(bbcdotcom.data);
                bbcdotcom.config.setAdsEnabled(bbcdotcom.data.a);
                bbcdotcom.config.setAnalyticsEnabled(bbcdotcom.data.b);
            }
            if (bbcdotcom.data.a == 1) {
                                (function(){
                    var zoneFile = "3pt_zone_file";

                    bbcdotcom.objects('page', 'create', bbcdotcom);
                    
                                        BBC.adverts.setScriptRoot("http://static.bbci.co.uk/bbcdotcom/0.3.267/script/");
                    BBC.adverts.setImgRoot("http://static.bbci.co.uk/bbcdotcom/0.3.267/img/");
                    BBC.adverts.init({
                        domain: "www.bbc.com",
                        location: window.location.pathname,
                        zoneVersion: zoneFile,
                        zoneAuto: false,
                        siteAuto: false,
                        keywordsAuto: false,
                        zoneReferrer: document.referrer
                    });
                })();
                if(typeof bbcdotcom !== 'undefined' && typeof bbcdotcom.survey !== 'undefined' && typeof bbcdotcom.survey.init === 'function') {
                    bbcdotcom.survey.init();
                }
            }
            /*]]>*/
        </script>
     <script type="text/javascript">/*<![CDATA[*/ window.istats = (window.istats || {}); (istats.head = function(w,d) { w.istats._linkTracked = w.istats._trackingCookie = decodeURIComponent( (d.cookie.match(/\bsa_labels=([^;]+)/)||[]).pop() || '' ); var host = w.location.host, m = host.match(/(bbc(?:\.co\.uk|\.com))$/i); d.cookie = 'sa_labels=; expires=Thu, 01 Jan 1970 00:00:01 GMT; domain=' + (m? m[1] : host) + '; path=/'; })(window,document); /*]]>*/</script>  <!-- Webapp: WWHP international homepage --> <meta property="fb:page_id" content="228735667216" /> <meta property="fb:admins" content="297814326937641" /> <meta property="fb:app_id" content="187214818032936" /> <meta property="og:title" content="" /> <meta property="og:type" content="website" /> <meta property="og:image" content="/img/iphone.png" /> <meta property="og:url" content="http://www.bbc.co.uk/" /> <meta name="msvalidate.01" content="A09EF0BF1FC5CDBB37D921CBC3776943" /> <link rel="apple-touch-icon" href="/img/iphone.png"/>  <link rel="canonical" href="http://www.bbc.com/" /> <link rel="alternate" hreflang="en-gb" href="http://www.bbc.co.uk/" /> <link rel="alternate" hreflang="gd-gb" href="http://www.bbc.co.uk/alba/" /> <link rel="alternate" hreflang="cy-gb" href="http://www.bbc.co.uk/cymru/" /> <link rel="alternate" hreflang="en" href="http://www.bbc.com/" />  <!--[if IE]><link href="http://static.bbci.co.uk/wwhp/1.45.0/desktop/css/ie.css" type="text/css" rel="stylesheet"><![endif]--> <!--[if lte IE 6]><link href="http://static.bbci.co.uk/wwhp/1.45.0/desktop/css/ie-6.css" type="text/css" rel="stylesheet"><![endif]--> <!--[if IE 7]><link href="http://static.bbci.co.uk/wwhp/1.45.0/desktop/css/ie-7.css" type="text/css" rel="stylesheet"><![endif]--> <!--[if lte IE 8]><link href="http://static.bbci.co.uk/wwhp/1.45.0/desktop/css/ie-lte-8.css" type="text/css" rel="stylesheet"><![endif]-->  <script type="text/javascript"> /*<![CDATA[*/ if (!window.console) { window.console = { log: function() { return; }, warn: function() { return; }, info: function() {return; }, error: function() { return; }, debug: function() { return; } }; } if(typeof(Homepage) === "undefined") { Homepage = {}; Homepage.VSGet = function() { return ''; };  Homepage.edition = 'i'; Homepage.env = 'live'; Homepage.fetchBbcCookie = function () { if (!navigator.cookieEnabled) { return ''; } var nameEQ = "BBC-UID="; var ca = document.cookie.split(';'); for(i=0;i < ca.length;i++) { var c = ca[i]; while (c.charAt(0)===' ') { c = c.substring(1,c.length); } if (c.indexOf(nameEQ) === 0) { return c.substring(nameEQ.length,c.length); } } return ''; }; } if(typeof(wwhomepage) === 'undefined'){ wwhomepage = {}; wwhomepage.configs = []; if(Homepage !== 'undefined'){ wwhomepage.legacy = Homepage; } wwhomepage.createObject = function(strName) { var nameParts = strName.split("."), i = 0, len = nameParts.length, obj = window; for (; i < len; i++){ if (obj[nameParts[i]] === undefined) { obj[nameParts[i]] = {}; } obj = obj[nameParts[i]]; } return obj; } } /*]]>*/ </script>                         </head> <body id="wwhp" class="disable-wide-advert desktop default us desktop-default desktop-us default-us">        <!-- BBCDOTCOM body first -->
                <script type="text/javascript">BBC.adverts.write("wallpaper",false);</script>
        <script type="text/javascript">
            /*<![CDATA[*/
            body= document.getElementsByTagName('body');
            aTags = document.getElementsByTagName('a');
            if('undefined' != typeof aTags && 'undefined' != typeof aTags[0] && -1 !== aTags[0].href.indexOf('http://ad.doubleclick.net')) {
                body[0].removeChild(aTags[0]);
            }
            /*]]>*/
        </script>
      <script type="text/javascript">/*<![CDATA[*/ bbcFlagpoles_istats = 'ON'; istatsTrackingUrl = '//sa.bbc.co.uk/bbc/bbc/s?name=SET-COUNTER&pal_route=index&ml_name=barlesque&app_type=web&language=en-GB&ml_version=0.19.0&pal_webapp=wwhp'; if (window.istats_countername) { istatsTrackingUrl = istatsTrackingUrl.replace(/([?&]name=)[^&]+/ig, '$1' + istats_countername); } (function() { if ( /\bIDENTITY=/.test(document.cookie) ) { istatsTrackingUrl += '&bbc_identity=1'; } var c = (document.cookie.match(/\bckns_policy=(\d\d\d)/)||[]).pop() || ''; istatsTrackingUrl += '&bbc_mc=' + (c? 'ad'+c.charAt(0)+'ps'+c.charAt(1)+'pf'+c.charAt(2) : 'not_set'); if ( /\bckns_policy=\d\d0/.test(document.cookie) ) { istatsTrackingUrl += '&ns_nc=1'; } var screenWidthAndHeight = 'unavailable'; if (window.screen && screen.width && screen.height) { screenWidthAndHeight = screen.width + 'x' + screen.height; } istatsTrackingUrl += ('&screen_resolution=' + screenWidthAndHeight); istatsTrackingUrl += '&blq_s=3.5d&blq_r=3.5&blq_v=default-worldwide'; })(); /*]]>*/</script>  <!-- Begin iStats 20100118 (UX-CMC 1.1009.3) --> <script type="text/javascript">/*<![CDATA[*/ (function() { window.istats || (istats = {}); var cookieDisabled = (document.cookie.indexOf('NO-SA=') != -1), hasCookieLabels = (document.cookie.indexOf('sa_labels=') != -1), hasClickThrough = /^#sa-(.*?)(?:-sa(.*))?$/.test(document.location.hash), runSitestat = !cookieDisabled && !hasCookieLabels && !hasClickThrough && !istats._linkTracked; if (runSitestat && bbcFlagpoles_istats === 'ON') { sitestat(istatsTrackingUrl); } else { window.ns_pixelUrl = istatsTrackingUrl; /* used by Flash library to track */ } function sitestat(n){var j=document,f=j.location,b="";if(j.cookie.indexOf("st_ux=")!=-1){var k=j.cookie.split(";");var e="st_ux",h=document.domain,a="/";if(typeof ns_!="undefined"&&typeof ns_.ux!="undefined"){e=ns_.ux.cName||e;h=ns_.ux.cDomain||h;a=ns_.ux.cPath||a}for(var g=0,f=k.length;g<f;g++){var m=k[g].indexOf("st_ux=");if(m!=-1){b="&"+unescape(k[g].substring(m+6))}}document.cookie=e+"=; expires="+new Date(new Date().getTime()-60).toGMTString()+"; path="+a+"; domain="+h}ns_pixelUrl=n;n=ns_pixelUrl+"&ns__t="+(new Date().getTime())+"&ns_c="+((j.characterSet)?j.characterSet:j.defaultCharset)+"&ns_ti="+escape(j.title)+b+"&ns_jspageurl="+escape(f&&f.href?f.href:j.URL)+"&ns_referrer="+escape(j.referrer);if(n.length>2000&&n.lastIndexOf("&")){n=n.substring(0,n.lastIndexOf("&")+1)+"ns_cut="+n.substring(n.lastIndexOf("&")+1,n.lastIndexOf("=")).substring(0,40)}(j.images)?new Image().src=n:j.write('<p><i'+'mg src="'+n+'" height="1" width="1" alt="" /></p>')}; })(); /*]]>*/</script> <noscript><p style="position: absolute; top: -999em;"><img src="//sa.bbc.co.uk/bbc/bbc/s?name=SET-COUNTER&amp;pal_route=index&amp;ml_name=barlesque&amp;app_type=web&amp;language=en-GB&amp;ml_version=0.19.0&amp;pal_webapp=wwhp&amp;blq_js_enabled=0&amp;blq_s=3.5d&amp;blq_r=3.5&amp;blq_v=default-worldwide" height="1" width="1" alt="" /></p></noscript> <!-- End iStats (UX-CMC) -->   <div id="blq-global"> <noscript> <div id="blq-no-js-banner"> <p>For a better experience on your device, try our <a href="http://m.bbc.co.uk">mobile site</a>.</p> </div> </noscript> <div id="blq-pre-mast" xml:lang="en-GB"> <!-- Pre mast --> 
<!-- BBCDOTCOM leaderboard template:client-side journalismVariant: false ipIsAdvertiseCombined: true adsEnabled: true flagpole: ON showAdAboveBlq: true blqLeaderboardAd: true -->
 </div> </div>  <script type="text/html" id="blq-bbccookies-tmpl"><![CDATA[ <div id="bbccookies-prompt" class="bbccookies-w"> <h2> Cookies on the BBC website </h2> <p> We use cookies to ensure that we give you the best experience on our website.<span class="bbccookies-international-message"> We also use cookies to ensure we show you advertising that is relevant to you.</span> If you continue without changing your settings, we'll assume that you are happy to receive all cookies on the BBC website. However, if you would like to, you can <a href="/privacy/cookies/managing/cookie-settings.html">change your cookie settings</a> at any time. </p> <ul> <li id="bbccookies-continue"> <button type="button" id="bbccookies-continue-button">Continue</button> </li> <li id="bbccookies-more"><a href="/privacy/cookies/bbc">Find out more</a></li></ul> </div> ]]></script> <script type="text/javascript">/*<![CDATA[*/ (function(){if(bbccookies._showPrompt()){var i=document,b=i.getElementById("blq-pre-mast"),f=i.getElementById("blq-global"),h=i.getElementById("blq-container"),c=i.getElementById("blq-bbccookies-tmpl"),a,g,e;if(b&&i.createElement){a=i.createElement("div");a.id="bbccookies";e=c.innerHTML;e=e.replace("<"+"![CDATA[","").replace("]]"+">","");a.innerHTML=e;if(f){f.insertBefore(a,b)}else{h.insertBefore(a,b)}g=i.getElementById("bbccookies-continue-button");g.onclick=function(){a.parentNode.removeChild(a);return false};bbccookies._setPolicy()}}})(); /*]]>*/</script>  <div id="blq-masthead" class="blq-clearfix blq-mast-bg-white blq-masthead-focus blq-lang-en blq-ltr"> <span id="blq-mast-background"><span></span></span>  <div id="blq-mast" class="blq-rst">  <div id="blq-mast-bar" class="blq-masthead-container blq-default-worldwide"> <div id="blq-blocks"> <a href="/" hreflang="en-GB"> <abbr title="British Broadcasting Corporation" class="blq-home"> <img src="http://static.bbci.co.uk/frameworks/barlesque/2.70.0/desktop/3.5/img/blq-blocks_grey_alpha.png" alt="BBC" width="84" height="24" /> </abbr> </a> </div> <div id="blq-acc-links"> <h2 id="page-top">Accessibility links</h2> <ul>  <li><a href="#blq-content">Skip to content</a></li>  <li><a href="#blq-local-nav">Skip to local navigation</a></li>  <li><a href="/accessibility/">Accessibility Help</a></li> </ul> </div> <div id="blq-sign-in" class="blq-gel">  </div> <div id="blq-nav"> <h2>BBC navigation</h2>     <ul id="blq-nav-main">   <li id="blq-nav-news"> <a href="http://www.bbc.com/news/">News</a> </li>    <li id="blq-nav-sport"> <a href="http://www.bbc.co.uk/sport/">Sport</a> </li>    <li id="blq-nav-weather"> <a href="http://www.bbc.co.uk/weather/">Weather</a> </li>    <li id="blq-nav-capital"> <a href="http://www.bbc.com/capital/">Capital</a> </li>    <li id="blq-nav-future"> <a href="http://www.bbc.com/future/">Future</a> </li>    <li id="blq-nav-shop"> <a href="http://shop.bbc.com/">Shop</a> </li>    <li id="blq-nav-tv"> <a href="/tv/">TV</a> </li>    <li id="blq-nav-radio"> <a href="/radio/">Radio</a> </li>    <li id="blq-nav-more"> <a href="/a-z/">More&hellip;</a> </li>   </ul>   <div id="blq-nav-search"> <form method="get" action="http://search.bbc.co.uk/search" accept-charset="utf-8" id="blq-search-form"> <div>  <input type="hidden" name="go" value="toolbar" />  <input type="hidden" name="uri" value="/" />    <label for="blq-search-q" class="blq-hide">Search term:</label> <input id="blq-search-q" type="text" name="q" value="" maxlength="128" /> <button id="blq-search-btn" type="submit"><span><img src="http://static.bbci.co.uk/frameworks/barlesque/2.70.0/desktop/3.5/img/blq-search_grey_alpha.png" width="13" height="13" alt="Search"/></span></button> </div> </form> </div>  </div> </div> </div> </div> <div id="blq-container-outer" class="blq-default-worldwide blq-ltr" >  <div id="blq-container" class="blq-lang-en blq-dotcom"> <div id="blq-container-inner" xml:lang="en">   <div id="blq-main" class="blq-clearfix">               <h1 class="hide">BBC Homepage</h1> <div class="wwhp"> <div class="colA column">  <div id="adLeaderboard"> <div id="adLeaderboard_container" class="container"> <div class="contentBlocks cbg0"> <div class="advert"> <div id="bbccom_leaderboard" class="bbccom_display_none"> <script type="text/javascript">BBC.adverts.write("leaderboard");</script> </div> <script type="text/javascript">BBC.adverts.show("leaderboard");</script> </div> </div> </div> </div>  </div> <div class="colB column">   <h2 id="page_title" class="page_title"></h2> <script type="text/javascript"> /*<![CDATA[*/ require(["desktop/utils/date"], function (dateUtil) { var date = new Date(); document.getElementById('page_title').innerHTML = dateUtil.getDayName(date) + ', ' + date.getDate() + ' ' + dateUtil.getMonthName(date); }); /*]]>*/ </script>     <script type="text/javascript"> /*<![CDATA[*/ var bodyTag = document.getElementsByTagName('body')[0]; var carouselClass = 'wwhp_promo_standard'; var bodyPromoClass = ''; bodyTag.setAttribute('class', bodyTag.getAttribute('class') + ' ' + carouselClass + ' ' + bodyPromoClass); delete bodyTag; /*]]>*/ </script>   <div class="promo module2 clearfix">   <div class="hero2">   <a class="hero_image_link" href="/news/world-middle-east-29100285" title="US 'to go on offensive' against IS" rev="promo2|homepage|na|r|t|i|hero|content"> <img src="http://ichef.bbci.co.uk/wwhp/ic/news/624-351/77427000/jpg/_77427153_f246p8u8.jpg" width="624" height="351" alt="USS George HW Bush, 29 Aug" class="hero_image" /> <span class="hero_image_overlay">  <span class="hero_image_overlay_text">  <span class="hero_title"> US 'to go on offensive' against IS </span>   </span> </span> </a>     <p class="hero_summary"> US President Barack Obama is to set out his &quot;game plan&quot; against Islamic State militants this week, saying &quot;we will defeat them&quot;. </p>   </div>   <div class="container">  <div class="grid_5 first">   <a class="media_link" href="/sport/cricket/29088722" title="England v India" rev="promo2|homepage|na|r|t|i|image|content"> <span class="media_image"> <img src="http://ichef.bbci.co.uk/wwhp/ic/news/144-81/77428000/jpg/_77428053_eoin_morgan_pa.jpg" width="144" height="81" alt="England's Eoin Morgan" /> <span class="icon icon_small icon_live"></span> </span> <span class="media_title"> England v India </span> </a>   </div>  <div class="grid_5">   <a class="media_link" href="http://www.bbc.com/news/magazine-29050369" title="The 100-year-old German board game still making people angry" rev="promo2|homepage|na|r|t|i|image|content"> <span class="media_image"> <img src="http://ichef.bbci.co.uk/wwhp/ic/ibroadcast/624-351/images/live/p0/26/3m/p0263mmx.jpg" width="144" height="81" alt="Boardgame" />  </span> <span class="media_title"> The 100-year-old German board game still making people angry </span> </a>   </div>  <div class="grid_5">   <a class="media_link" href="http://www.bbc.com/news/magazine-29060814" title="The elephant that flew" rev="promo2|homepage|na|r|t|i|image|content"> <span class="media_image"> <img src="http://ichef.bbci.co.uk/wwhp/ic/ibroadcast/624-351/images/live/p0/26/3d/p0263dch.jpg" width="144" height="81" alt="Elephant." />  </span> <span class="media_title"> The elephant that flew </span> </a>   </div>  <div class="grid_5 last">   <a class="media_link" href="http://www.bbc.com/culture/story/20140828-the-man-who-invented-a-colour" title="Blue man: Meet the artist who created a colour" rev="promo2|homepage|na|r|t|i|image|content"> <span class="media_image"> <img src="http://ichef.bbci.co.uk/wwhp/ic/ibroadcast/624-351/images/live/p0/25/m9/p025m9lg.jpg" width="144" height="81" alt="Blue man" />  </span> <span class="media_title"> Blue man: Meet the artist who created a colour </span> </a>   </div>  </div> </div>      <div class="module2 clearfix news">  <h2 class="module_title">  <a href="/news" rev="news|homepage|na|r|t|i|text|headline"> News </a>  </h2>  <div class="container"> <div class="grid_10 first">   <div class="hero2">   <a class="hero_image_link" href="/news/world-europe-29098808" title="Ukraine truce shaken by new shelling" rev="news|homepage|na|r|t|i|hero|content"> <img src="http://ichef.bbci.co.uk/wwhp/ic/news/304-171/77424000/jpg/_77424723_jwmjj9lo.jpg" width="304" height="171" alt="Shelling in Donetsk, 7 Sept" class="hero_image" /> <span class="hero_image_overlay">      </span> </a>     <h3 class="hero_title">  <a href="/news/world-europe-29098808" title="Ukraine truce shaken by new shelling" rev="news|homepage|na|r|t|i|hero|content"> Ukraine truce shaken by new shelling</a> </h3>    <p class="hero_summary"> There has been fresh shelling near Donetsk airport, but European observers say the fighting is not sufficient to make Ukraine's ceasefire collapse. </p>   </div>   </div> <div class="grid_5">   <a class="media_link" href="/news/entertainment-arts-29098929" title="Stars attend Joan Rivers' funeral" rev="news|homepage|na|r|t|i|image|content"> <span class="media_image"> <img src="http://ichef.bbci.co.uk/wwhp/ic/news/144-81/77428000/jpg/_77428352_index1.jpg" width="144" height="81" alt="Left-right: Sarah Jessica Parker, Donald Trump, Whoopi Goldberg" />  </span> <span class="media_title"> Stars attend Joan Rivers' funeral </span> </a>      <ul class="media_link_list">   <li class="media_link_list_item first"> <a class="media_link" href="/news/entertainment-arts-29098926" title="South Korean star dies after crash" rev="news|homepage|na|r|t|i|text|content"> South Korean star dies after crash </a> </li>    <li class="media_link_list_item"> <a class="media_link" href="/news/world-middle-east-29099239" title="Abbas warns Hamas on unity deal" rev="news|homepage|na|r|t|i|text|content"> Abbas warns Hamas on unity deal </a> </li>    <li class="media_link_list_item last"> <a class="media_link" href="/news/world-middle-east-29100961" title="Arrests over Egypt 'gay wedding'" rev="news|homepage|na|r|t|i|text|content"> Arrests over Egypt 'gay wedding' </a> </li>   </ul>   </div> <div class="grid_5 last">   <a class="media_link" href="/news/uk-scotland-29096458" title="Scottish referendum 'neck and neck'" rev="news|homepage|na|r|t|i|image|content"> <span class="media_image"> <img src="http://ichef.bbci.co.uk/wwhp/ic/news/144-81/77426000/jpg/_77426872_signs_pa.jpg" width="144" height="81" alt="Yes/No banners" />  </span> <span class="media_title"> Scottish referendum 'neck and neck' </span> </a>      <ul class="media_link_list">   <li class="media_link_list_item first"> <a class="media_link" href="/news/world-asia-29100226" title="Kashmir city hit by huge flood" rev="news|homepage|na|r|t|i|text|content"> Kashmir city hit by huge flood </a> </li>    <li class="media_link_list_item"> <a class="media_link" href="/news/world-middle-east-29098679" title="Abu Qatada condemns reporter killings" rev="news|homepage|na|r|t|i|text|content"> Abu Qatada condemns reporter killings </a> </li>    <li class="media_link_list_item last"> <a class="media_link" href="/news/world-asia-29099617" title="Death sentences after Afghan rapes" rev="news|homepage|na|r|t|i|text|content"> Death sentences after Afghan rapes </a> </li>   </ul>   </div> </div> </div>   <div class="container module_wrapper module_compound">    <div class="module2 half_module business">  <h2 class="module_title">  <a href="/news/business" rev="business|homepage|na|r|t|i|text|headline"> Business </a>  </h2>    <div class="hero2">   <a class="hero_image_link" href="/news/business-********" title="UK set to challenge EU bonus cap" rev="business|homepage|na|r|t|i|hero|content"> <img src="http://ichef.bbci.co.uk/wwhp/ic/news/304-171/********/jpg/_66115591_66115590.jpg" width="304" height="171" alt="City of London skyline" class="hero_image" /> <span class="hero_image_overlay">      </span> </a>     <h3 class="hero_title">  <a href="/news/business-********" title="UK set to challenge EU bonus cap" rev="business|homepage|na|r|t|i|hero|content"> UK set to challenge EU bonus cap</a> </h3>    <p class="hero_summary"> The UK's objection to the European Union's plans to cap bankers' bonuses is set to be heard in the bloc's highest court on Monday. </p>   </div>      <ul class="media_link_list">   <li class="media_link_list_item first"> <a class="media_link" href="/news/business-********" title="Alibaba announces share sale details" rev="business|homepage|na|r|t|i|text|content"> Alibaba announces share sale details </a> </li>    <li class="media_link_list_item"> <a class="media_link" href="/news/technology-********" title="Apple to tighten iCloud security" rev="business|homepage|na|r|t|i|text|content"> Apple to tighten iCloud security </a> </li>    <li class="media_link_list_item"> <a class="media_link" href="/news/business-********" title="The cost of skimming the small print" rev="business|homepage|na|r|t|i|text|content"> <span class="icon icon_inline icon_video"></span>The cost of skimming the small print </a> </li>    <li class="media_link_list_item last"> <a class="media_link" href="/news/business-********" title="Watch firms thrive in digital age" rev="business|homepage|na|r|t|i|text|content"> <span class="icon icon_inline icon_video"></span>Watch firms thrive in digital age </a> </li>   </ul>   </div>      <div class="module2 half_module sport">  <h2 class="module_title">  <a href="/sport" rev="sport|homepage|na|r|t|i|text|headline"> Sport </a>  </h2>    <div class="hero2">   <a class="hero_image_link" href="/sport/live/football/29038832" title="Sunday’s Euro 2016 qualifiers" rev="sport|homepage|na|r|t|i|hero|content"> <img src="http://ichef.bbci.co.uk/wwhp/ic/news/304-171/77427000/jpg/_77427606_brunt_nireland_getty_hero.jpg" width="304" height="171" alt="Chris Brunt of Northern Ireland" class="hero_image" /> <span class="hero_image_overlay">      </span> </a>     <h3 class="hero_title">  <a href="/sport/live/football/29038832" title="Sunday’s Euro 2016 qualifiers" rev="sport|homepage|na|r|t|i|hero|content"> Sunday’s Euro 2016 qualifiers</a> </h3>    <p class="hero_summary"> Live text and radio commentary of European Championship qualifiers featuring Northern Ireland, Republic of Ireland and Scotland. </p>   </div>      <ul class="media_link_list">   <li class="media_link_list_item first"> <a class="media_link" href="/sport/formula1/29100244" title="Hamilton wins after Rosberg error" rev="sport|homepage|na|r|t|i|text|content"> Hamilton wins after Rosberg error </a> </li>    <li class="media_link_list_item"> <a class="media_link" href="/sport/football/29075461" title="'The papers said we had no chance'" rev="sport|homepage|na|r|t|i|text|content"> 'The papers said we had no chance' </a> </li>    <li class="media_link_list_item"> <a class="media_link" href="/sport/football/29099046" title="Fletcher eyes famous win for Scots" rev="sport|homepage|na|r|t|i|text|content"> Fletcher eyes famous win for Scots </a> </li>    <li class="media_link_list_item last"> <a class="media_link" href="/sport/tennis/29096450" title="Williams and Wozniacki set for final" rev="sport|homepage|na|r|t|i|text|content"> Williams and Wozniacki set for final </a> </li>   </ul>   </div>   </div>   <!-- SpecialReport: not ModuleInterface (not an object) -->
   <!-- Sport\SportInterface: not ModuleInterface (not an object) -->
   <!-- Sport\SportInterface: not ModuleInterface (not an object) -->
   <!-- Sport\SportInterface: not ModuleInterface (not an object) -->
   <!-- Sport\SportTheAshesScorecard: not ModuleInterface (not an object) -->
    <!-- Sport\SportInterface: not ModuleInterface (not an object) -->
   <!-- not CompoundModuleInterface (not an object) -->
<!-- SportTennis: no submodules -->   <!-- Sport\SportInterface: not ModuleInterface (not an object) -->
    <div class="module2 sub_module video">  <h2 class="module_title">  <span>Watch/Listen</span>  </h2>  <div class="container"> <div class="grid_13 first">   <div class="hero2">   <a class="hero_image_link" href="/news/uk-england-29069084" title="Lee Miller's 'selfie' in Hitler's bath" rev="video|homepage|na|r|t|i|hero|content"> <img src="http://ichef.bbci.co.uk/wwhp/ic/news/400-225/77380000/jpg/_77380238_leemiller-bath.jpg" width="400" height="225" alt="Lee Miller in Hitler's bath" class="hero_image" /> <span class="hero_image_overlay"> <span class="icon icon_large icon_video"></span>     </span> </a>     <h3 class="hero_title">  <a href="/news/uk-england-29069084" title="Lee Miller's 'selfie' in Hitler's bath" rev="video|homepage|na|r|t|i|hero|content"> Lee Miller's 'selfie' in Hitler's bath</a> </h3>    <p class="hero_summary"> BBC Inside Out discovers top model and photographer Lee Miller's life on Farley Farm in Sussex and talks to her son about her war time work. </p>   </div>   </div> <div class="grid_7 last media_items_landscape">    <ul class="media_list">  <li class="media_list_item first">   <a class="media_link" href="/news/entertainment-arts-29098475" title="Swedish film scoops top Venice award" rev="video|homepage|na|r|t|i|image|content"> <span class="media_image"> <img src="http://ichef.bbci.co.uk/wwhp/ic/news/96-54/77422000/jpg/_77422242_77422240.jpg" width="96" height="54" alt="Swedish director Roy Andersson poses with the Golden Lion for Best Film he received for his movie &quot;A Pigeon sat on a branch reflecting on existence&quot;" /> <span class="icon icon_small icon_video"></span> </span> <span class="media_title"> Swedish film scoops top Venice award </span> </a>   </li>  <li class="media_list_item">   <a class="media_link" href="/news/entertainment-arts-29098280" title="'Every day is Bill Murray day'" rev="video|homepage|na|r|t|i|image|content"> <span class="media_image"> <img src="http://ichef.bbci.co.uk/wwhp/ic/news/96-54/77422000/jpg/_77422317_77422316.jpg" width="96" height="54" alt="The actor Bill Murray" /> <span class="icon icon_small icon_video"></span> </span> <span class="media_title"> 'Every day is Bill Murray day' </span> </a>   </li>  <li class="media_list_item">   <a class="media_link" href="/news/10462520" title="One-minute World News" rev="video|homepage|na|r|t|i|image|content"> <span class="media_image"> <img src="http://ichef.bbci.co.uk/wwhp/ic/news/96-54/48351000/gif/_48351578_640x360-world-news.gif" width="96" height="54" alt="One-minute World News summary" /> <span class="icon icon_small icon_video"></span> </span> <span class="media_title"> One-minute World News </span> </a>   </li>  <li class="media_list_item last">   <a class="media_link" href="/news/science-environment-29093700" title="Scientists 'make telepathy breakthrough'" rev="video|homepage|na|r|t|i|image|content"> <span class="media_image"> <img src="http://ichef.bbci.co.uk/wwhp/ic/news/96-54/77413000/jpg/_77413683_brainwavesspl.jpg" width="96" height="54" alt="Telepathy" /> <span class="icon icon_small icon_video"></span> </span> <span class="media_title"> Scientists 'make telepathy breakthrough' </span> </a>   </li>  </ul>   </div> </div> </div>    <!-- Video: not ModuleInterface (not an object) -->
    <div class="module2 more sub_module clearfix"> <div class="container">    <div class="grid_5 first">  <h2 class="module_title">  <a href="http://www.bbc.co.uk/news/entertainment_and_arts/" rev="entertainment|homepage|na|r|t|i|text|headline"> Entertainment &amp; Arts </a>  </h2>    <a class="media_link" href="/news/entertainment-arts-29098103" title="Swedish film scoops top Venice award" rev="entertainment|homepage|na|r|t|i|image|content"> <span class="media_image"> <img src="http://ichef.bbci.co.uk/wwhp/ic/news/144-81/77422000/jpg/_77422098_023784165.jpg" width="144" height="81" alt="Director Roy Andersson with his Golden Lion for Best Film at the Venice Film Festival. 6 Sept 2014" />  </span> <span class="media_title"> Swedish film scoops top Venice award </span> </a>      <ul class="media_link_list">   <li class="media_link_list_item first"> <a class="media_link" href="/news/entertainment-arts-29096907" title="Toronto festival in Bill Murray Day" rev="entertainment|homepage|na|r|t|i|text|content"> Toronto festival in Bill Murray Day </a> </li>    <li class="media_link_list_item last"> <a class="media_link" href="http://www.bbc.co.uk/newsbeat/29093310" title="Cowell pays tribute to Simone Battle" rev="entertainment|homepage|na|r|t|i|text|content"> Cowell pays tribute to Simone Battle </a> </li>   </ul>   </div>     <div class="grid_5">  <h2 class="module_title">  <a href="http://www.bbc.co.uk/news/health/" rev="health|homepage|na|r|t|i|text|headline"> Health </a>  </h2>    <a class="media_link" href="/news/health-29084254" title="Use Ebola survivors' blood - WHO" rev="health|homepage|na|r|t|i|image|content"> <span class="media_image"> <img src="http://ichef.bbci.co.uk/wwhp/ic/news/144-81/77408000/jpg/_77408890_bloodbag.jpg" width="144" height="81" alt="Bag of blood" />  </span> <span class="media_title"> Use Ebola survivors' blood - WHO </span> </a>      <ul class="media_link_list">   <li class="media_link_list_item first"> <a class="media_link" href="/news/health-29061169" title="E-cigarette criticisms 'alarmist'" rev="health|homepage|na|r|t|i|text|content"> E-cigarette criticisms 'alarmist' </a> </li>    <li class="media_link_list_item last"> <a class="media_link" href="/news/health-29060238" title="Suicide death 'every 40 seconds'" rev="health|homepage|na|r|t|i|text|content"> Suicide death 'every 40 seconds' </a> </li>   </ul>   </div>     <div class="grid_5">  <h2 class="module_title">  <a href="http://www.bbc.co.uk/news/technology/" rev="technology|homepage|na|r|t|i|text|headline"> Technology </a>  </h2>    <a class="media_link" href="/news/technology-29039293" title="Motorola releases round smartwatch" rev="technology|homepage|na|r|t|i|image|content"> <span class="media_image"> <img src="http://ichef.bbci.co.uk/wwhp/ic/news/144-81/77391000/jpg/_77391045_fa19f86b-9a42-47fa-b764-6618eb631ae5.jpg" width="144" height="81" alt="Moto 360" />  </span> <span class="media_title"> Motorola releases round smartwatch </span> </a>      <ul class="media_link_list">   <li class="media_link_list_item first"> <a class="media_link" href="/news/technology-29077461" title="Twitpic to close over legal dispute" rev="technology|homepage|na|r|t|i|text|content"> Twitpic to close over legal dispute </a> </li>    <li class="media_link_list_item last"> <a class="media_link" href="/news/technology-29077101" title="Nvidia sues over graphics patents" rev="technology|homepage|na|r|t|i|text|content"> Nvidia sues over graphics patents </a> </li>   </ul>   </div>     <div class="grid_5 last">  <h2 class="module_title">  <a href="http://www.bbc.co.uk/news/science_and_environment/" rev="science|homepage|na|r|t|i|text|headline"> Science &amp; Environment </a>  </h2>    <a class="media_link" href="/news/science-environment-29069515" title="Blue whale numbers bounce back" rev="science|homepage|na|r|t|i|image|content"> <span class="media_image"> <img src="http://ichef.bbci.co.uk/wwhp/ic/news/144-81/77394000/jpg/_77394287_z9200381-blue_whale-spl.jpg" width="144" height="81" alt="whales" />  </span> <span class="media_title"> Blue whale numbers bounce back </span> </a>      <ul class="media_link_list">   <li class="media_link_list_item first"> <a class="media_link" href="/news/science-environment-29050114" title="Dinosaur titan's big bone haul" rev="science|homepage|na|r|t|i|text|content"> Dinosaur titan's big bone haul </a> </li>    <li class="media_link_list_item last"> <a class="media_link" href="/news/science-environment-29098632" title="House-sized asteroid to fly by Earth" rev="science|homepage|na|r|t|i|text|content"> House-sized asteroid to fly by Earth </a> </li>   </ul>   </div>   </div> </div>       <div id="bbccom_native_main_large" class="bbccom_display_none">
        <script type="text/javascript">BBC.adverts.write("native_main_large",false);</script>
    </div>
    <script type="text/javascript">BBC.adverts.show("native_main_large");</script>
        <div class="module2 module_landscape clearfix capital">  <h2 class="module_title">  <a href="http://www.bbc.com/capital" rev="capital|homepage|na|r|t|i|text|headline"> Capital </a>  </h2>     <div class="sponsor sponsor_capital">  <div id="bbccom_module_000dc" class="bbccom_display_none bbccom_module"> <div class="moduleAdvertContent"> <div class="bbccom_text bbccom_module_adlabel">In Association With</div> <div class="bbccom_image bbccom_module_image"> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.write("module_000dc",false, {title:""}); } /*]]>*/ </script> </div> </div> </div> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.show("module_000dc"); } /*]]>*/ </script>  </div>      <div class="hero2">   <a class="hero_image_link" href="http://www.bbc.com/capital/story/20140904-jitters-act-like-a-starfish" title="Jitters? Act like a starfish" rev="capital|homepage|na|r|t|i|hero|content"> <img src="http://ichef.bbci.co.uk/wwfeatures/304_171/images/live/p0/25/k6/p025k67v.jpg" width="304" height="171" alt="Jitters? Act like a starfish" class="hero_image" /> <span class="hero_image_overlay"> <span class="icon icon_medium icon_video"></span>     </span> </a>     <h3 class="hero_title">  <a href="http://www.bbc.com/capital/story/20140904-jitters-act-like-a-starfish" title="Jitters? Act like a starfish" rev="capital|homepage|na|r|t|i|hero|content"> Jitters? Act like a starfish</a> </h3>    <p class="hero_summary"> Change your posture to boost your confidence, says TED talk sensation Amy Cuddy </p>   </div>   <div class="module_keep_right">    <ul class="media_link_list">   <li class="media_link_list_item first"> <a class="media_link" href="http://www.bbc.com/capital/story/20140902-how-to-pull-an-all-nighter" title="How to pull an all-nighter" rev="capital|homepage|na|r|t|i|text|content"> How to pull an all-nighter </a> </li>    <li class="media_link_list_item"> <a class="media_link" href="http://www.bbc.com/capital/story/20140827-beachside-paradise-at-a-price" title="Beachside paradise — at a price" rev="capital|homepage|na|r|t|i|text|content"> Beachside paradise — at a price </a> </li>    <li class="media_link_list_item last"> <a class="media_link" href="http://www.bbc.com/capital/story/20140903-relax-or-else" title="Forced to take time off?" rev="capital|homepage|na|r|t|i|text|content"> Forced to take time off? </a> </li>   </ul>   </div> </div>     <div class="features module_wrapper"> <div class="container module_compound">    <div class="module2 half_module autos">  <h2 class="module_title">  <a href="/autos" rev="autos|homepage|na|r|t|i|text|headline"> Autos </a>  </h2>     <div class="sponsor sponsor_autos">  <div id="bbccom_module_000d6" class="bbccom_display_none bbccom_module"> <div class="moduleAdvertContent"> <div class="bbccom_text bbccom_module_adlabel">In Association With</div> <div class="bbccom_image bbccom_module_image"> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.write("module_000d6",false, {title:""}); } /*]]>*/ </script> </div> </div> </div> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.show("module_000d6"); } /*]]>*/ </script>  </div>      <div class="hero2">   <a class="hero_image_link" href="http://www.bbc.com/autos/story/20140905-cyling-the-seven-seas" title="Cycling the seven seas" rev="autos|homepage|na|r|t|i|hero|content"> <img src="http://ichef.bbci.co.uk/wwfeatures/304_171/images/live/p0/26/1v/p0261vzf.jpg" width="304" height="171" alt="Cycling the seven seas" class="hero_image" /> <span class="hero_image_overlay">      </span> </a>     <h3 class="hero_title">  <a href="http://www.bbc.com/autos/story/20140905-cyling-the-seven-seas" title="Cycling the seven seas" rev="autos|homepage|na|r|t|i|hero|content"> Cycling the seven seas</a> </h3>    <p class="hero_summary"> Meet the pedal-powered X1 water bike </p>   </div>      <ul class="media_link_list">   <li class="media_link_list_item first"> <a class="media_link" href="http://www.bbc.com/autos/story/20140905-lexus-hunts-bavarians" title="Lexus's 4 Series beater?" rev="autos|homepage|na|r|t|i|text|content"> Lexus's 4 Series beater? </a> </li>    <li class="media_link_list_item last"> <a class="media_link" href="http://www.bbc.com/autos/story/20140903-land-rovers-new-middle-child" title="Land Rover’s new middle child" rev="autos|homepage|na|r|t|i|text|content"> Land Rover’s new middle child </a> </li>   </ul>   </div>      <div class="module2 half_module culture">  <h2 class="module_title">  <a href="/culture" rev="culture|homepage|na|r|t|i|text|headline"> Culture </a>  </h2>     <div class="sponsor sponsor_culture">  <div id="bbccom_module_000d7" class="bbccom_display_none bbccom_module"> <div class="moduleAdvertContent"> <div class="bbccom_text bbccom_module_adlabel">In Association With</div> <div class="bbccom_image bbccom_module_image"> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.write("module_000d7",false, {title:""}); } /*]]>*/ </script> </div> </div> </div> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.show("module_000d7"); } /*]]>*/ </script>  </div>      <div class="hero2">   <a class="hero_image_link" href="http://www.bbc.com/culture/story/20140905-who-says-the-camera-never-lies" title="Who says the camera never lies?" rev="culture|homepage|na|r|t|i|hero|content"> <img src="http://ichef.bbci.co.uk/wwfeatures/304_171/images/live/p0/26/33/p02633mg.jpg" width="304" height="171" alt="Who says the camera never lies?" class="hero_image" /> <span class="hero_image_overlay"> <span class="icon icon_medium icon_slideshow"></span>     </span> </a>     <h3 class="hero_title">  <a href="http://www.bbc.com/culture/story/20140905-who-says-the-camera-never-lies" title="Who says the camera never lies?" rev="culture|homepage|na|r|t|i|hero|content"> Who says the camera never lies?</a> </h3>    <p class="hero_summary"> Street photos that aren’t what they seem </p>   </div>      <ul class="media_link_list">   <li class="media_link_list_item first"> <a class="media_link" href="http://www.bbc.com/culture/story/20140907-is-michael-moore-still-relevant" title="Is Michael Moore still relevant?" rev="culture|homepage|na|r|t|i|text|content"> <span class="icon icon_inline icon_video"></span>Is Michael Moore still relevant? </a> </li>    <li class="media_link_list_item last"> <a class="media_link" href="http://www.bbc.com/culture/story/20140907-the-movies-next-big-star" title="The movies’ next big star?" rev="culture|homepage|na|r|t|i|text|content"> The movies’ next big star? </a> </li>   </ul>   </div>   </div><div class="container module_compound">     <div class="module2 half_module future">  <h2 class="module_title">  <a href="/future" rev="future|homepage|na|r|t|i|text|headline"> Future </a>  </h2>     <div class="sponsor sponsor_future">  <div id="bbccom_module_000d8" class="bbccom_display_none bbccom_module"> <div class="moduleAdvertContent"> <div class="bbccom_text bbccom_module_adlabel">In Association With</div> <div class="bbccom_image bbccom_module_image"> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.write("module_000d8",false, {title:""}); } /*]]>*/ </script> </div> </div> </div> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.show("module_000d8"); } /*]]>*/ </script>  </div>      <div class="hero2">   <a class="hero_image_link" href="http://www.bbc.com/future/story/20140905-the-secret-to-acing-exams" title="The secret to acing exams" rev="future|homepage|na|r|t|i|hero|content"> <img src="http://ichef.bbci.co.uk/wwfeatures/304_171/images/live/p0/26/3q/p0263qp3.jpg" width="304" height="171" alt="The secret to acing exams" class="hero_image" /> <span class="hero_image_overlay">      </span> </a>     <h3 class="hero_title">  <a href="http://www.bbc.com/future/story/20140905-the-secret-to-acing-exams" title="The secret to acing exams" rev="future|homepage|na|r|t|i|hero|content"> The secret to acing exams</a> </h3>    <p class="hero_summary"> Hidden patterns that reveal test answers </p>   </div>      <ul class="media_link_list">   <li class="media_link_list_item first"> <a class="media_link" href="http://www.bbc.com/future/story/20140903-low-cost-fighter-jets-take-off" title="Low-cost fighter jets take off" rev="future|homepage|na|r|t|i|text|content"> Low-cost fighter jets take off </a> </li>    <li class="media_link_list_item last"> <a class="media_link" href="http://www.bbc.com/future/story/20140905-a-plane-on-the-moon" title="A plane on the moon" rev="future|homepage|na|r|t|i|text|content"> <span class="icon icon_inline icon_slideshow"></span>A plane on the moon </a> </li>   </ul>   </div>      <div class="module2 half_module travel">  <h2 class="module_title">  <a href="/travel" rev="travel|homepage|na|r|t|i|text|headline"> Travel </a>  </h2>     <div class="sponsor sponsor_travel">  <div id="bbccom_module_000d9" class="bbccom_display_none bbccom_module"> <div class="moduleAdvertContent"> <div class="bbccom_text bbccom_module_adlabel">In Association With</div> <div class="bbccom_image bbccom_module_image"> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.write("module_000d9",false, {title:""}); } /*]]>*/ </script> </div> </div> </div> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.show("module_000d9"); } /*]]>*/ </script>  </div>      <div class="hero2">   <a class="hero_image_link" href="http://www.bbc.com/travel/feature/20140828-londons-weekly-beer-pilgrimage" title="London’s weekly beer pilgrimage" rev="travel|homepage|na|r|t|i|hero|content"> <img src="http://ichef.bbci.co.uk/wwtravel/432_258/images/live/p0/25/mc/p025mcnl.jpg" width="304" height="171" alt="London’s weekly beer pilgrimage" class="hero_image" /> <span class="hero_image_overlay">      </span> </a>     <h3 class="hero_title">  <a href="http://www.bbc.com/travel/feature/20140828-londons-weekly-beer-pilgrimage" title="London’s weekly beer pilgrimage" rev="travel|homepage|na|r|t|i|hero|content"> London’s weekly beer pilgrimage</a> </h3>    <p class="hero_summary"> Saunter – and sample – six artisanal breweries </p>   </div>      <ul class="media_link_list">   <li class="media_link_list_item first"> <a class="media_link" href="http://www.bbc.com/travel/slideshow/20140814-in-ethiopia-an-adrenaline-filled-act-of-faith" title="No ropes, no shoes and 300m drops" rev="travel|homepage|na|r|t|i|text|content"> No ropes, no shoes and 300m drops </a> </li>    <li class="media_link_list_item last"> <a class="media_link" href="http://www.bbc.com/travel/feature/20140828-how-i-quit-my-job-to-travel-the-married-couple" title="'How we quit our jobs to travel'" rev="travel|homepage|na|r|t|i|text|content"> 'How we quit our jobs to travel' </a> </li>   </ul>   </div>    </div> </div>     <div class="module2 module_padded bbcworldservice">  <h2 class="module_title">  <a href="http://www.bbc.co.uk/worldserviceradio/" rev="bbcworldservice|homepage|na|r|t|i|text|headline"> BBC World Service </a>  </h2>  <div class="module_contents clearfix"> <div class="programme">   <h3 class="hero_title">  <span class="hero_title_prefix">On air:</span>  <a href="http://www.bbc.co.uk/programmes/p025s25j" title="BBC News" rev="bbcworldservice|homepage|na|r|t|i|hero|content"> <span class="icon icon_inline icon_audio"></span>BBC News</a> </h3>   </div>  <ul class="media_link_list metadata_list">  <li class="media_link_list_item clearfix first last">  <a class="media_link first last" href="http://www.bbc.co.uk/worldserviceradio/programmes/schedules#on-now" title="Full schedule" rev="bbcworldservice|homepage|na|r|t|i|text|content"> Full schedule </a>  </li>  </ul>  </div> </div>     <div class="module2 clearfix worldservice" id="worldService">  <h2 class="module_title">  <a href="http://www.bbc.co.uk/worldservice/languages/index.shtml" rev="worldservice|homepage|na|r|t|i|text|headline"> BBC in your language </a>  </h2>  <div class="container" id="worldService_container"> <div class="worldservice_stories contentBlocks cbg0">   <div class="contentBlock" id="worldService_spanish"> <h3 class="draggable" id="worldService_spanish_title"> <a href="http://www.bbc.co.uk/mundo/" rev="worldservice|homepage|na|r|t|i|text|sub headline"> Spanish </a> </h3> <div class="title image_first" dir="ltr"> <a class="" title="EE.UU. &quot;tomará la ofensiva&quot; contra Estado Islámico, dice Obama" href="/mundo/ultimas_noticias/2014/09/140907_ultnot_eeuu_obama_estado_islamico_men" rev="worldservice|homepage|na|r|t|i|hero|content"> <span>EE.UU. &quot;tomará la ofensiva&quot; contra Estado Islámico, dice Obama</span> </a> <p id="worldService_spanish_summary" class="summary"></p> </div> </div>   <div class="contentBlock" id="worldService_chinese"> <h3 class="draggable" id="worldService_chinese_title"> <a href="http://www.bbc.co.uk/zhongwen/simp/" rev="worldservice|homepage|na|r|t|i|text|sub headline"> Chinese </a> </h3> <div class="title image_first" dir="ltr"> <a class="" title="奥巴马将宣布打击伊斯兰国“进攻”战略" href="http://www.bbc.co.uk/zhongwen/simp/world/2014/09/140907_world_us_isil_offensive.shtml" rev="worldservice|homepage|na|r|t|i|hero|content"> <span>奥巴马将宣布打击伊斯兰国“进攻”战略</span> </a> <p id="worldService_chinese_summary" class="summary">美国官方消息称，奥巴马总统即将在周三的电视讲话中公布打击伊斯兰国的战略。</p> </div> </div>   <div class="contentBlock" id="worldService_arabic"> <h3 class="draggable" id="worldService_arabic_title"> <a href="http://www.bbc.co.uk/arabic/" rev="worldservice|homepage|na|r|t|i|text|sub headline"> Arabic </a> </h3> <div class="title image_first" dir="rtl"> <a class="" title="عباس يحذر حماس من انهاء الشراكة الوطنية" href="/arabic/middleeast/2014/09/140907_abbas_warns_unity" rev="worldservice|homepage|na|r|t|i|hero|content"> <span>عباس يحذر حماس من انهاء الشراكة الوطنية</span> </a> <p id="worldService_arabic_summary" class="summary"></p> </div> </div>   <div class="contentBlock" id="worldService_persian"> <h3 class="draggable" id="worldService_persian_title"> <a href="http://www.bbc.co.uk/persian/" rev="worldservice|homepage|na|r|t|i|text|sub headline"> Persian </a> </h3> <div class="title image_first" dir="rtl"> <a class="" title="اوباما: مشکل ایران نیست؛ افراط گرایی سنی است" href="/persian/iran/2014/09/140907_l30_iran_is_isis_us_sunni_obama_remarks" rev="worldservice|homepage|na|r|t|i|hero|content"> <span>اوباما: مشکل ایران نیست؛ افراط گرایی سنی است</span> </a> <p id="worldService_persian_summary" class="summary"></p> </div> </div>  </div> <div class="worldservice_languages contentBlocks cbg1"> <div id="worldService_languages" class="list contentBlock fourColumn"> <h3 id="worldService_languages_title" class="draggable"> <a rev="worldservice|homepage|na|r|t|i|text|sub headline" href="http://www.bbc.co.uk/worldservice/languages/index.shtml"> More languages </a> </h3> <ul class="blq-clearfix"> <li class="ar"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/arabic/" xml:lang="ar" class="ar" title="Arabic service">Arabic</a></li> <li class="as"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/azeri/" xml:lang="as" class="as" title="Azeri service">Azeri</a></li> <li class="bn"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/bengali/" xml:lang="bn" class="bn" title="Bangla service">Bangla</a></li> <li class="my"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/burmese/" xml:lang="my" class="my" title="Burmese service">Burmese</a></li> <li class="zh"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/zhongwen/simp/" xml:lang="zh" class="zh" title="Chinese service">Chinese</a></li> <li class="fr"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/afrique/" xml:lang="fr" class="fr" title="French (for Africa) service">French</a></li> <li class="ha"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/hausa/" xml:lang="ha" class="ha" title="Hausa service">Hausa</a></li> <li class="hi"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/hindi/" xml:lang="hi" class="hi" title="Hindi service">Hindi</a></li> <li class="id"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/indonesia/" xml:lang="id" class="id" title="Indonesian service">Indonesian</a></li> <li class="rw"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/gahuza/" xml:lang="rw" class="rw" title="Kirundi service">Kinyarwanda</a></li> <li class="rn"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/gahuza/" xml:lang="rn" class="rn" title="Kirundi service">Kirundi</a></li> <li class="ky"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/kyrgyz/" xml:lang="ky" class="ky" title="Kyrgyz service">Kyrgyz</a></li> <li class="ne"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/nepali/" xml:lang="ne" class="ne" title="Nepali service">Nepali</a></li> <li class="ps"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/pashto/" xml:lang="ps" class="ps" title="Pashto service">Pashto</a></li> <li class="fa"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/persian/" xml:lang="fa" class="fa" title="Persian service">Persian</a></li> <li class="pt-BR"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/portuguese/" xml:lang="pt-BR" class="pt-BR" title="Portuguese (for Brazil) service">Portuguese</a></li> <li class="ru"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/russian/" xml:lang="ru" class="ru" title="Russian service">Russian</a></li> <li class="si"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/sinhala/" xml:lang="si" class="si" title="Sinhala service">Sinhala</a></li> <li class="so"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/somali/" xml:lang="so" class="so" title="Somali service">Somali</a></li> <li class="es"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/mundo/" xml:lang="es" class="es" title="Spanish service">Spanish</a></li> <li class="sw"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/swahili/" xml:lang="sw" class="sw" title="Swahili service">Swahili</a></li> <li class="ta"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/tamil/" xml:lang="ta" class="ta" title="Tamil service">Tamil</a></li> <li class="tr"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/turkce/" xml:lang="tr" class="tr" title="Turkish service">Turkish</a></li> <li class="uk"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/ukrainian/" xml:lang="uk" class="uk" title="Ukrainian service">Ukrainian</a></li> <li class="ur"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/urdu/" xml:lang="ur" class="ur" title="Urdu service">Urdu</a></li> <li class="uz"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/uzbek/" xml:lang="uz" class="uz" title="Uzbek service">Uzbek</a></li> <li class="vi"><a rev="worldservice|homepage|na|r|t|i|text|content" href="http://www.bbc.co.uk/vietnamese/" xml:lang="vi" class="vi" title="Vietnamese service">Vietnamese</a></li> </ul> </div> </div> </div> </div>   </div> <div class="colC column">  <div id="adMpu"> <div id="adMpu_container" class="container"> <div class="contentBlocks cbg0"> <div class="advert"> <div id="bbccom_mpu" class="bbccom_display_none"> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.write("mpu",true); } /*]]>*/ </script> </div> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.show("mpu"); } /*]]>*/ </script> </div> </div> </div> </div>          <div id="bbccom_native_promo_top" class="bbccom_display_none">
        <script type="text/javascript">BBC.adverts.write("native_promo_top",false);</script>
    </div>
    <script type="text/javascript">BBC.adverts.show("native_promo_top");</script>
       <!-- ModuleAbstract: not ModuleInterface (not an object) -->
 <div class="module2 module_inverse bbcchannel bbcamerica">  <h2 class="module_title">  <a href="http://www.bbcamerica.com/" rev="bbcamerica|homepage|na|r|t|i|text|headline"> <span class="icon icon_inline icon_tv"></span>BBC America </a>  </h2>     <div class="sponsor sponsor_bbcamerica">  <div id="bbccom_module_bbcamericachannel" class="bbccom_display_none bbccom_module"> <div class="moduleAdvertContent"> <div class="bbccom_text bbccom_module_adlabel">Launch Partner</div> <div class="bbccom_image bbccom_module_image"> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.write("module_bbcamericachannel",false, {title:""}); } /*]]>*/ </script> </div> </div> </div> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.show("module_bbcamericachannel"); } /*]]>*/ </script>  </div>     <div class="bbcchannel_promo">   <div class="hero2">   <a class="hero_image_link" href="http://www.bbcamerica.com/doctor-who/videos/no-such-thing-as-robin-hood/?utm_source=bbc.com&amp;utm_medium=promo%20module&amp;utm_campaign=Channel%20Module" title="No Such Thing as Robin Hood?" rev="bbcamerica|homepage|na|r|t|i|hero|content"> <img src="http://ichef.bbci.co.uk/wwhp/ic/bbc-america/336-189/files/2014/08/bbc-module-robin-hood.jpg" width="336" height="189" alt="Robin Hood, Doctor Who" class="hero_image" /> <span class="hero_image_overlay">  <span class="hero_image_overlay_text">  <span class="hero_title"> No Such Thing as Robin Hood? </span>   </span> </span> </a>   </div>   </div>    <div class="module_contents bbcchannel_links">  <ul class="media_link_list metadata_list">  <li class="media_link_list_item clearfix first last">  <a class="media_link first" href="http://www.bbcamerica.com/schedule/?utm_source=bbc.com&amp;utm_medium=promo%20module&amp;utm_campaign=Channel%20Module" title="Full Schedule" rev="bbcamerica|homepage|na|r|t|i|text|content"> Full Schedule </a>  <a class="media_link last" href="http://www.bbcamerica.com/channel-finder/?utm_source=bbc.com&amp;utm_medium=promo%20module&amp;utm_campaign=Channel%20Module" title="Channel Finder" rev="bbcamerica|homepage|na|r|t|i|text|content"> Channel Finder </a>  </li>  </ul>  </div>   </div>     <div class="module2 module_inverse bbcchannel bbcworldnews">  <h2 class="module_title">  <a href="http://www.bbc.com/news/world_radio_and_tv/" rev="bbcworldnews|homepage|na|r|t|i|text|headline"> <span class="icon icon_inline icon_tv"></span>BBC World News </a>  </h2>      <div class="bbcchannel_promo">   <div class="hero2">   <a class="hero_image_link" href="/news/technology-29065359" title="Click" rev="bbcworldnews|homepage|na|r|t|i|hero|content"> <img src="http://ichef.bbci.co.uk/wwhp/ic/news/336-189/77379000/jpg/_77379027_earth_spl1024.jpg" width="336" height="189" alt="A satellite image of the Earth" class="hero_image" /> <span class="hero_image_overlay">  <span class="hero_image_overlay_text">  <span class="hero_title"> Click </span>    <span class="hero_summary"> A 3D printer could help astronauts make the objects they need, plus other technology news </span>   </span> </span> </a>   </div>   </div>   <div class="bbcchannel_now clearfix">    <div class="hero2">   <a class="hero_image_link" href="http://www.bbc.co.uk/programmes/n3csvx4v" title="BBC World News" rev="bbcworldnews|homepage|na|r|t|i|hero|content"> <img src="http://ichef.bbci.co.uk/images/ic/336x189/p01t67p4.jpg" width="144" height="81" alt="The latest international news from the BBC." class="hero_image" /> <span class="hero_image_overlay">      </span> </a>     <h3 class="hero_title">  <span class="hero_title_prefix">On air:</span>  <a href="http://www.bbc.co.uk/programmes/n3csvx4v" title="BBC World News" rev="bbcworldnews|homepage|na|r|t|i|hero|content"> BBC World News</a> </h3>     </div>   </div>   <div class="module_contents bbcchannel_links">  <ul class="media_link_list metadata_list">  <li class="media_link_list_item clearfix first last">  <a class="media_link first last" href="http://www.bbc.co.uk/worldnews/programmes/schedules/northamerica" title="Full schedule" rev="bbcworldnews|homepage|na|r|t|i|text|content"> Full schedule </a>  </li>  </ul>  </div>   </div>     <div class="module_wrapper">   <div class="module2 spotlight sub_module module_padded" id="spotlight_0">  <h2 class="module_title">  <a href="http://www.bbc.com/future/tags/psychology" rev="spotlight20|homepage|na|r|t|i|text|headline"> Psychology </a>  </h2>  <div class="sponsor sponsor_spotlight2">  <div id="bbccom_module_spotlight20" class="bbccom_display_none bbccom_module"> <div class="moduleAdvertContent"> <div class="bbccom_text bbccom_module_adlabel">In Association With</div> <div class="bbccom_image bbccom_module_image"> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.write("module_spotlight20",false, {title:"psychology"}); } /*]]>*/ </script> </div> </div> </div> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.show("module_spotlight20"); } /*]]>*/ </script>  </div>  <div class="module_contents">   <div class="hero2">   <a class="hero_image_link" href="http://www.bbc.com/future/story/20140905-how-to-win-at-almost-everything" title="How to win at (almost) everything" rev="spotlight20|homepage|na|r|t|i|hero|content"> <img src="http://ichef.bbci.co.uk/wwhp/ic/ibroadcast/336-189/images/live/p0/26/44/p02644x8.jpg" width="336" height="189" alt="Winning." class="hero_image" /> <span class="hero_image_overlay">      </span> </a>     <h3 class="hero_title">  <a href="http://www.bbc.com/future/story/20140905-how-to-win-at-almost-everything" title="How to win at (almost) everything" rev="spotlight20|homepage|na|r|t|i|hero|content"> How to win at (almost) everything</a> </h3>    <p class="hero_summary"> In games, we are hopelessly predictable. Spot this hidden behaviour to crush your puny-minded opponents in everything from the lottery to Rock, Paper, Scissors. </p>   </div>      </div> </div>    <div class="module2 spotlight sub_module module_padded" id="spotlight_1">  <h2 class="module_title">  <a href="http://www.bbc.com/capital/columns/how-to-buy-it" rev="spotlight21|homepage|na|r|t|i|text|headline"> How to Buy It </a>  </h2>  <div class="sponsor sponsor_spotlight2">  <div id="bbccom_module_spotlight21" class="bbccom_display_none bbccom_module"> <div class="moduleAdvertContent"> <div class="bbccom_text bbccom_module_adlabel">In Association With</div> <div class="bbccom_image bbccom_module_image"> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.write("module_spotlight21",false, {title:"howtobuyit"}); } /*]]>*/ </script> </div> </div> </div> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.show("module_spotlight21"); } /*]]>*/ </script>  </div>  <div class="module_contents">   <div class="hero2">   <a class="hero_image_link" href="http://www.bbc.com/capital/story/20140905-trendsetter-buy-this-not-that" title="Get the wardrobe of your dreams" rev="spotlight21|homepage|na|r|t|i|hero|content"> <img src="http://ichef.bbci.co.uk/wwhp/ic/ibroadcast/336-189/images/live/p0/25/6c/p0256c4r.jpg" width="336" height="189" alt="Clothes." class="hero_image" /> <span class="hero_image_overlay">      </span> </a>     <h3 class="hero_title">  <a href="http://www.bbc.com/capital/story/20140905-trendsetter-buy-this-not-that" title="Get the wardrobe of your dreams" rev="spotlight21|homepage|na|r|t|i|hero|content"> Get the wardrobe of your dreams</a> </h3>    <p class="hero_summary"> Buying a designer wardrobe is rarely as simple as a stroll down Fifth Avenue or the Champs Elysees. BBC Capital takes a strategic approach to getting the look you covet. </p>   </div>      </div> </div>    <div class="module2 spotlight sub_module module_padded" id="spotlight_2">  <h2 class="module_title">  <a href="http://www.bbc.com/news/blogs/trending/" rev="spotlight22|homepage|na|r|t|i|text|headline"> BBC Trending </a>  </h2>  <div class="sponsor sponsor_spotlight2">  <div id="bbccom_module_spotlight22" class="bbccom_display_none bbccom_module"> <div class="moduleAdvertContent"> <div class="bbccom_text bbccom_module_adlabel">In Association With</div> <div class="bbccom_image bbccom_module_image"> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.write("module_spotlight22",false, {title:"bbctrending"}); } /*]]>*/ </script> </div> </div> </div> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.show("module_spotlight22"); } /*]]>*/ </script>  </div>  <div class="module_contents">   <div class="hero2">   <a class="hero_image_link" href="http://www.bbc.com/news/blogs-trending-29079705" title="The man who mocks women's profiles" rev="spotlight22|homepage|na|r|t|i|hero|content"> <img src="http://ichef.bbci.co.uk/wwhp/ic/ibroadcast/336-189/images/live/p0/26/3k/p0263kjc.jpg" width="336" height="189" alt="Tinder" class="hero_image" /> <span class="hero_image_overlay">      </span> </a>     <h3 class="hero_title">  <a href="http://www.bbc.com/news/blogs-trending-29079705" title="The man who mocks women's profiles" rev="spotlight22|homepage|na|r|t|i|hero|content"> The man who mocks women's profiles</a> </h3>    <p class="hero_summary"> Meet Tindafella. The bearded Aussie recreating women's Tinder profile pictures with &quot;hilarious&quot; results. But is he taking things a step too far? </p>   </div>      </div> </div>   </div>           <div id="bbccom_native_promo_bottom" class="bbccom_display_none">
        <script type="text/javascript">BBC.adverts.write("native_promo_bottom",false);</script>
    </div>
    <script type="text/javascript">BBC.adverts.show("native_promo_bottom");</script>
       <div class="module2 sub_module clearfix most_popular">  <h2 class="module_title">  <span>Most Popular in News</span>  </h2>  <div class="most_popular_content clearfix"> <div class="livestats livestats-tabbed contentBlock topTabs" id="most_popular_tabs">   <h3 class="tab"> <a href="#" id="most_popular_tabs_shared_link">Shared</a> </h3> <div class="panel" id="most_popular_tabs_shared"> <ul class="most_popular_list clearfix">   <li class="first-child"> <a href="http://www.bbc.co.uk/news/magazine-29060814" rev="mostpopular|homepage|na|r|t|i|text|content"> <span class="livestats-icon livestats-1">1</span> The elephant that flew </a> </li>   <li class=""> <a href="http://www.bbc.co.uk/news/uk-scotland-29096458" rev="mostpopular|homepage|na|r|t|i|text|content"> <span class="livestats-icon livestats-2">2</span> Scottish referendum 'neck and neck' </a> </li>   <li class=""> <a href="http://www.bbc.co.uk/news/science-environment-29098632" rev="mostpopular|homepage|na|r|t|i|text|content"> <span class="livestats-icon livestats-3">3</span> House-sized asteroid to fly by Earth </a> </li>   <li class=""> <a href="http://www.bbc.co.uk/news/magazine-24653643" rev="mostpopular|homepage|na|r|t|i|text|content"> <span class="livestats-icon livestats-4">4</span> The woman who saved a man with an SS tattoo </a> </li>   <li class=""> <a href="http://www.bbc.co.uk/news/uk-northern-ireland-29068832" rev="mostpopular|homepage|na|r|t|i|text|content"> <span class="livestats-icon livestats-5">5</span> Ulster kings' coronation site dig </a> </li>  </ul> </div>   <h3 class="tab"> <a href="#" id="most_popular_tabs_read_link">Read</a> </h3> <div class="panel" id="most_popular_tabs_read"> <ul class="most_popular_list clearfix">   <li class="first-child"> <a href="http://www.bbc.co.uk/news/world-middle-east-29100285" rev="mostpopular|homepage|na|r|t|i|text|content"> <span class="livestats-icon livestats-1">1</span> US 'to go on offensive' against IS </a> </li>   <li class=""> <a href="http://www.bbc.co.uk/news/entertainment-arts-29098929" rev="mostpopular|homepage|na|r|t|i|text|content"> <span class="livestats-icon livestats-2">2</span> Joan Rivers' funeral to be held </a> </li>   <li class=""> <a href="http://www.bbc.co.uk/news/entertainment-arts-29098926" rev="mostpopular|homepage|na|r|t|i|text|content"> <span class="livestats-icon livestats-3">3</span> South Korean singer dies after crash </a> </li>   <li class=""> <a href="http://www.bbc.co.uk/news/magazine-29050369" rev="mostpopular|homepage|na|r|t|i|text|content"> <span class="livestats-icon livestats-4">4</span> The WW1 game that's still popular today </a> </li>   <li class=""> <a href="http://www.bbc.co.uk/news/entertainment-arts-23333090" rev="mostpopular|homepage|na|r|t|i|text|content"> <span class="livestats-icon livestats-5">5</span> Hollywood’s golden couple laid bare </a> </li>  </ul> </div>   <h3 class="tab selected"> <a href="#" id="most_popular_tabs_media_link">Watched/Listened</a> </h3> <div class="panel selected" id="most_popular_tabs_media"> <ul class="most_popular_list clearfix">   <li class="first-child"> <a href="http://www.bbc.co.uk/news/world-middle-east-29099729" rev="mostpopular|homepage|na|r|t|i|text|content"> <span class="livestats-icon livestats-1">1</span> Haditha dam 'important facility' </a> </li>   <li class=""> <a href="http://www.bbc.co.uk/news/world-us-canada-29087184" rev="mostpopular|homepage|na|r|t|i|text|content"> <span class="livestats-icon livestats-2">2</span> A giant boulder's journey to LA </a> </li>   <li class=""> <a href="http://www.bbc.co.uk/news/video_and_audio/" rev="mostpopular|homepage|na|r|t|i|text|content"> <span class="livestats-icon livestats-3">3</span> One-minute World News </a> </li>   <li class=""> <a href="http://www.bbc.co.uk/news/uk-england-29069084" rev="mostpopular|homepage|na|r|t|i|text|content"> <span class="livestats-icon livestats-4">4</span> Lee Miller's 'selfie' in Hitler's bath </a> </li>   <li class=""> <a href="http://www.bbc.co.uk/news/science-environment-29093700" rev="mostpopular|homepage|na|r|t|i|text|content"> <span class="livestats-icon livestats-5">5</span> Scientists 'make telepathy breakthrough' </a> </li>  </ul> </div>  <script type="text/javascript"> /*<![CDATA[*/ require(["desktop/ui/tabs"], function (tabs) { var methodOnReady = function(){ tabs.registerTabs('#most_popular_tabs'); }; if (typeof require.ready === 'function') { require.ready(methodOnReady);  } else { require(['domReady'], function (domReady) {  domReady(methodOnReady); }); } }); /*]]>*/; </script> </div> </div> </div>      <div id="weather" class="module2 clearfix weather">  <h2 class="module_title">  <a href="/weather/" rev="weather|homepage|na|r|t|i|text|headline"> Weather </a>  </h2>     <div class="sponsor sponsor_weather">  <div id="bbccom_module_00080" class="bbccom_display_none bbccom_module"> <div class="moduleAdvertContent"> <div class="bbccom_text bbccom_module_adlabel">In Association With</div> <div class="bbccom_image bbccom_module_image"> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.write("module_00080",false, {title:""}); } /*]]>*/ </script> </div> </div> </div> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.show("module_00080"); } /*]]>*/ </script>  </div>    <div id="weather_forecast" class="weatherforecast contentBlock weather_loading">  <h3>London</h3> <div id="weather_searchPanel" class="searchPanel editPanel contentBlock"> <form id="weather_searchPanel_form" method="get" action="/wwhp"> <fieldset> <input type="hidden" name="module" value="weather" /> <input type="hidden" name="action" value="search" /> <div class="field"> <label>Enter city, town or region</label> <input type="text" size="25" maxlength="40" value="Enter city, town or region" class="searchPanelSearch" name="location_term" /> <button type="submit" class="submit">Search</button> </div> </fieldset> </form> <div id="searchResults" class="results"> <p>&nbsp;</p> <ul><li>&nbsp;</li></ul> </div> </div> <dl class="dayForecast">  <dt>Sun</dt> <dd>  <a href="/weather/2643743"> <img width="57" height="57" alt="Clear Sky" title="Clear Sky" src="http://static.bbci.co.uk/weather/0.5.284/images/icons/individual_56_icons/en_on_light_bg/0.gif" class="icon" /> </a>  <p class="temp min"> <span class="label">Min<span class="hide"> Temperature</span>:</span> <span class="value"> 13&#176;C</span> <span class="hide">55&#176;F</span> </p> </dd> <dt>Mon</dt> <dd>  <a href="/weather/2643743"> <img width="57" height="57" alt="Sunny" title="Sunny" src="http://static.bbci.co.uk/weather/0.5.284/images/icons/individual_56_icons/en_on_light_bg/1.gif" class="icon" /> </a>  <p class="temp max"> <span class="label">Max<span class="hide"> Temperature</span>:</span> <span class="value"> 22&#176;C</span> <span class="hide">72&#176;F</span> </p>  <p class="temp min"> <span class="label">Min<span class="hide"> Temperature</span>:</span> <span class="value"> 13&#176;C</span> <span class="hide">55&#176;F</span> </p> </dd> <dt class="last">Tue</dt> <dd class="last">  <a href="/weather/2643743"> <img width="57" height="57" alt="Sunny Intervals" title="Sunny Intervals" src="http://static.bbci.co.uk/weather/0.5.284/images/icons/individual_56_icons/en_on_light_bg/3.gif" class="icon" /> </a>  <p class="temp max"> <span class="label">Max<span class="hide"> Temperature</span>:</span> <span class="value"> 21&#176;C</span> <span class="hide">70&#176;F</span> </p>  <p class="temp min"> <span class="label">Min<span class="hide"> Temperature</span>:</span> <span class="value"> 12&#176;C</span> <span class="hide">54&#176;F</span> </p> </dd>  </dl>   <div class="module_footer_links clearfix">  <ul>  <li class="first last"> <a class="media_link" href="/weather/2643743" title="Detailed forecast">Detailed forecast</a> </li>  </ul> </div>   <!-- <p class="detail"><a></a></p> -->  </div> </div> <script type="text/javascript"> /*<![CDATA[*/ require(["desktop/ui/weather"], function (weather) { var methodOnReady = function(){ weather = new weather; weather.renderWeather('#weather',{"autocomplete":{"enabled":false,"minLength":4}}); }; if (typeof require.ready === 'function') { require.ready(methodOnReady);  } else { require(['domReady'], function (domReady) {  domReady(methodOnReady); }); } }); /*]]>*/ </script>    <div id="adMpuBottom"> <div id="adMpuBottom_container" class="container"> <div class="contentBlocks cbg0"> <div class="advert"> <div id="bbccom_mpu_bottom" class="bbccom_display_none"> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.write("mpu_bottom",true); } /*]]>*/ </script> </div> <script type="text/javascript"> /*<![CDATA[*/ if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){ BBC.adverts.show("mpu_bottom"); } /*]]>*/ </script> </div> </div> </div> </div>  </div>   <div id="exploreTray"> <h2>Explore the BBC</h2> <div class="directory"> <div class="directoryColumn hpDir1"> <h3><a href="http://www.bbc.com/news/">News</a></h3> <h4 class="offscreen">World Regions</h4> <ul> <li><a href="http://www.bbc.co.uk/news/2/hi/africa/default.stm">Africa</a></li> <li><a href="http://www.bbc.co.uk/news/world/asia/">Asia</a></li> <li><a href="http://www.bbc.co.uk/news/2/hi/europe/default.stm">Europe</a></li> <li><a href="http://www.bbc.co.uk/news/world/latin_america/">Latin America</a></li> <li><a href="http://www.bbc.co.uk/news/2/hi/middle_east/default.stm">Middle East</a></li> <li><a href="http://www.bbc.co.uk/news/2/hi/uk_news/default.stm">UK</a></li> <li><a href="http://www.bbc.co.uk/news/world/us_and_canada/">US &amp; Canada</a></li> </ul> <h4 class="offscreen">Types of news</h4> <ul> <li><a href="http://www.bbc.co.uk/news/2/hi/business/default.stm">Business</a></li> <li><a href="http://news.bbc.co.uk/2/hi/health/default.stm">Health</a></li> <li><a href="http://www.bbc.co.uk/news/2/hi/science/nature/default.stm">Science &amp; Environment</a></li> <li><a href="http://www.bbc.com/news/technology/">Technology</a></li> <li><a href="http://www.bbc.co.uk/news/entertainment_and_arts/">Entertainment &amp; Arts</a></li> <li><a href="http://www.bbc.co.uk/news/2/hi/also_in_the_news/">Also in the News</a></li> </ul> <ul> <li><a href="http://www.bbc.co.uk/worldservice/languages/">BBC in your language</a></li> </ul> </div> <div class="directoryColumn hpDir2"> <h3><a href="http://www.bbc.com/sport">Sport</a></h3> <ul> <li><a href="http://www.bbc.co.uk/sport/0/football/">Football</a></li> <li><a href="http://www.bbc.co.uk/sport/0/cricket/">Cricket</a></li> <li><a href="http://www.bbc.co.uk/sport/0/tennis/">Tennis</a></li> <li><a href="http://www.bbc.co.uk/sport/0/formula1/">Formula 1</a></li> </ul> <h3><a href="http://www.bbc.co.uk/news/2/hi/business/default.stm">Business</a></h3> <ul> <li><a href="http://www.bbc.co.uk/news/2/shared/fds/hi/business/market_data/overview/default.stm">Market Data</a></li> <li><a href="http://www.bbc.co.uk/news/2/hi/business/economy/default.stm">Economy</a></li> <li><a href="http://www.bbc.co.uk/news/2/hi/business/companies/default.stm">Companies</a></li> </ul> <h3 class="noSubSections"><a href="http://www.bbc.co.uk/weather">Weather</a></h3> <h3 class="noSubSections"><a href="http://www.bbc.com/future">Future</a></h3> <h3 class="noSubSections"><a href="http://www.bbc.com/travel">Travel</a></h3> </div> <div class="directoryColumn hpDir3"> <h3><a href="http://www.bbc.co.uk/entertainment/">Entertainment</a></h3> <ul> <li><a href="http://www.bbc.co.uk/news/2/hi/entertainment/default.stm">Entertainment News</a></li> <li><a href="http://www.bbc.co.uk/comedy/">Comedy</a></li> <li><a href="http://www.bbc.co.uk/drama/">Drama</a></li> </ul> <h3><a href="http://www.bbc.co.uk/music/">Music</a></h3> <ul> <li><a href="http://www.bbc.co.uk/music/genres/">Genres</a></li> <li><a href="http://www.bbc.co.uk/music/reviews">Reviews</a></li> <li><a href="http://www.bbc.co.uk/music/artists/">Artists</a></li> <li><a href="http://www.bbc.co.uk/music/news/">News</a></li> </ul> <h3><a href="http://www.bbc.co.uk/arts/">Arts &amp; Culture</a></h3> <ul> <li><a href="http://www.bbc.co.uk/film/">Film</a></li> </ul> <h3><a href="http://www.bbc.co.uk/food/">Food</a></h3> <ul> <li><a href="http://www.bbc.co.uk/food/techniques">Techniques</a></li> <li><a href="http://www.bbc.co.uk/food/recipes/">Recipes</a></li> </ul> </div> <div class="directoryColumn hpDir4"> <h3><a href="http://www.bbc.co.uk/sn/">Science</a></h3> <ul> <li><a href="http://www.bbc.co.uk/science/humanbody/">Humans</a></li> <li><a href="http://www.bbc.co.uk/space/">Space</a></li> </ul> <h3><a href="http://www.bbc.co.uk/nature/">Nature</a></h3> <ul> <li><a href="http://www.bbc.co.uk/nature/animals/">Animals</a></li> </ul> <h3><a href="http://www.bbc.co.uk/gardening/">Gardening</a></h3> <ul> <li><a href="http://www.bbc.co.uk/gardening/plants/plant_finder/">Plant finder</a></li> <li><a href="http://www.bbc.co.uk/gardening/advice/">Advice</a></li> </ul> <h3><a href="http://www.bbc.co.uk/religion/">Religion</a></h3> <ul> <li><a href="http://www.bbc.co.uk/religion/tools/calendar/">Multifaith Calendar</a></li> <li><a href="http://www.bbc.co.uk/religion/tools/quizzes">Quizzes</a></li> </ul> <h3><a href="http://www.bbc.co.uk/ethics/">Ethics</a></h3> </div> <div class="directoryColumn hpDir5"> <h3><a href="http://www.bbc.co.uk/go/homepage/i/int/br/learningenglish/t/-/worldservice/learningenglish/">Learning English</a></h3> <ul> <li><a href="http://www.bbc.co.uk/worldservice/learningenglish/">Online courses</a></li> <li><a href="http://www.bbc.co.uk/worldservice/learningenglish/grammar/index.shtml">Vocabulary &amp; Grammar</a></li> <li class="clearLeft nosep"><a href="http://www.bbc.co.uk/worldservice/learningenglish/quizzes/index.shtml">Quizzes</a></li> </ul> <h3><a href="http://www.bbc.co.uk/languages/">Learning Other Languages</a></h3> <ul> <li><a href="http://www.bbc.co.uk/languages/french/">French</a></li> <li><a href="http://www.bbc.co.uk/languages/spanish/">Spanish</a></li> <li><a href="http://www.bbc.co.uk/languages/german/">German</a></li> <li><a href="http://www.bbc.co.uk/languages/italian/">Italian</a></li> </ul> <h3><a href="http://www.bbc.co.uk/tv/i/">TV Channels</a></h3> <ul> <li><a href="http://www.bbc.com/tvschedule/">BBC World News</a></li> <li><a href="http://www.bbcamerica.com/">BBC America</a></li> </ul> <h3><a href="http://www.bbc.co.uk/radio/">Radio</a></h3> <ul> <li><a href="http://www.bbc.co.uk/worldservice/">World Service</a></li> </ul> <p class="aToz">Can't find it? Try the <a href="http://www.bbc.co.uk/a-z/">A to Z</a></p> </div> </div> </div>  </div>                              <script type="text/javascript"> (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){ (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o), m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m) })(window,document,'script','//www.google-analytics.com/analytics.js','ga'); ga('create', 'UA-47973230-2', 'bbc.com'); ga('send', 'pageview'); </script>     </div>   <!--[if IE 6]> <div id="blq-ie6-upgrade"> <p> <span>You're using the Internet Explorer 6 browser to view the BBC website. Our site will work much better if you change to a more modern browser. It's free, quick and easy.</span> <a href="http://www.browserchoice.eu/">Find out more <span>about upgrading your browser</span> here&hellip;</a> </p> </div> <![endif]-->  <div id="blq-foot" xml:lang="en" class="blq-rst blq-clearfix blq-foot-grey"> <div id="blq-footlinks"> <h2 class="blq-hide">BBC links</h2>       <ul>                    <li class="blq-footlinks-row"> <ul class="blq-footlinks-row-list"> <li><a href="http://m.bbc.co.uk/" id="blq-footer-mobile">Mobile site</a></li><li><a href="/terms/">Terms of Use</a></li><li><a href="/aboutthebbc/">About the BBC</a></li> </ul> </li>                     <li class="blq-footlinks-row"> <ul class="blq-footlinks-row-list"> <li><a href="http://advertising.bbcworldwide.com">Advertise With Us</a></li><li><a href="/privacy/">Privacy</a></li><li><a href="/accessibility/">Accessibility Help</a></li> </ul> </li>                     <li class="blq-footlinks-row"> <ul class="blq-footlinks-row-list"> <li><a href="/privacy/cookies/international/">Ad Choices</a></li><li><a href="/privacy/bbc-cookies-policy.shtml">Cookies</a></li><li><a href="/contact/">Contact the BBC</a></li> </ul> </li>           <li class="blq-footlinks-row"> <ul class="blq-footlinks-row-list"> <li><a href="/guidance/">Parental Guidance</a></li> </ul> </li>             </ul> <script type="text/javascript">/*<![CDATA[*/ (function() { var mLink = document.getElementById('blq-footer-mobile'), stick = function() { var d = new Date (); d.setYear(d.getFullYear() + 1); d = d.toUTCString(); window.bbccookies.set('ckps_d=m;domain=.bbc.co.uk;path=/;expires=' + d ); window.bbccookies.set('ckps_d=m;domain=.bbc.com;path=/;expires=' + d ); }; if (mLink) {  if (mLink.addEventListener) { mLink.addEventListener('click', stick, false); } else if (mLink.attachEvent) { mLink.attachEvent('onclick', stick); } } })(); /*]]>*/</script>  </div>  <div id="blq-foot-blocks" class="blq-footer-image-light"><img src="http://static.bbci.co.uk/frameworks/barlesque/2.70.0/desktop/3.5/img/blocks/light.png" width="84" height="24" alt="BBC" /></div>             <div id="bbccom_bottom" class="bbccom_display_none" style="width:468px; text-align:right;">
            <script type="text/javascript">BBC.adverts.write("bottom",true);</script>
        </div>
        <script type="text/javascript">BBC.adverts.show("bottom");</script>
     <p id="blq-disclaim"><span id="blq-copy">BBC &copy; 2014</span> <a href="/help/web/links/">The BBC is not responsible for the content of external sites. Read more.</a></p> <div id="blq-obit"><p><strong>This page is best viewed in an up-to-date web browser with style sheets (CSS) enabled. While you will be able to view the content of this page in your current browser, you will not be able to get the full visual experience. Please consider upgrading your browser software or enabling style sheets (CSS) if you are able to do so.</strong></p></div> </div> </div> 
<!-- BBCDOTCOM analytics template:client-side journalismVariant: false ipIsAdvertiseCombined: true adsEnabled: true flagpole: ON -->

    
            <script type="text/javascript">
            /*<![CDATA[*/
            if (BBC.adverts.isActive('analytics')) {
                if(typeof(bbcdotcom) != 'undefined' && typeof(bbcdotcom.stats) != 'undefined') {
                    bbcdotcom.stats.contentType = "HTML";
                                    }
            }
            /*]]>*/
        </script>


            <!-- Start AudienceScience -->
        <script type="text/javascript">
            /*<![CDATA[*/
            if (BBC.adverts.isActive('analytics')) {
                document.write('<script type="text/javascript" src="http://js.revsci.net/gateway/gw.js?csid=J08781">\x3C/script>');
            }
            /*]]>*/
        </script>
        <script type="text/javascript">
            /*<![CDATA[*/
            if (BBC.adverts.isActive('analytics')) {
                DM_tag();
            }
            /*]]>*/
        </script>
        <!-- End AudienceScience -->

            <!-- Start SiteCatalyst -->
                    <script type="text/javascript">
                /*<![CDATA[*/
                if (BBC.adverts.isActive('analytics')) {
                    var s_account = "bbcwglobalprod";                    document.write('<script type="text/javascript" src="http://static.bbci.co.uk/bbcdotcom/0.3.267/script/s_code.js">\x3C/script>');
                }
                /*]]>*/
            </script>                                       <script type="text/javascript">                     /*<![CDATA[*/
                if (BBC.adverts.isActive('analytics')) {
                    var s_code=scw.t();if(s_code)document.write(s_code)
                }
                /*]]>*/
            </script>
                <!-- End SiteCatalyst -->


            <!-- Start comScore Tag -->
        <script type="text/javascript">
            /*<![CDATA[*/
            if (BBC.adverts.isActive('analytics')) {
                document.write("\x3Cscript src='" + (document.location.protocol == "https:" ? "https://sb" : "http://b") + ".scorecardresearch.com/beacon.js'>\x3C/script>");
            }
            /*]]>*/
        </script>
        <script type="text/javascript">
            /*<![CDATA[*/
            if (BBC.adverts.isActive('analytics')) {
                COMSCORE.beacon({
                    c1:2,
                    c2:"6035051",
                    c3:"",
                    c4:"www.bbc.com/",
                    c5:"",
                    c6:"",
                    c15:""
                });
            }
            /*]]>*/
        </script>
        <!-- End comScore Tag -->

            <!-- Begin Sitestat4 code -->
        <script type="text/javascript">
            /*<![CDATA[*/
            if (BBC.adverts.isActive('sitestat')) {
                function sitestat(ns_l){ns_l+='&amp;ns__t='+(new Date()).getTime();
                ns_0=document.referrer;
                ns_0=(ns_0.lastIndexOf('/')==ns_0.length-1)?ns_0.substring(ns_0.lastIndexOf('/'),0):ns_0;
                if(ns_0.length>0)ns_l+='&amp;ns_referrer='+encodeURI(ns_0);
                if(document.images){ns_1=new Image();ns_1.src=ns_l;}else
                document.write('<img src="'+ns_l+'" width="1" height="1" alt="">');}
                sitestat("http://nl.sitestat.com/adfab/bbc/s?teller");
            }
            /*]]>*/
        </script>
        <!-- End Sitestat4 code -->


            <!-- Start Nielsen Online SiteCensus V6.0 -->
        <script type="text/javascript">
            /*<![CDATA[*/
            if (BBC.adverts.isActive('nielsen-au')) {
                <!-- COPYRIGHT 2009 Nielsen Online -->
                document.write('<script type="text/javascript" src="' + ('https:' == document.location.protocol ? 'https:' : 'http:') + '//secure-au.imrworldwide.com/v60.js">\x3C/script>');
            }
            /*]]>*/
        </script>                                   <script type="text/javascript">                 /*<![CDATA[*/
            if (BBC.adverts.isActive('nielsen-au')) {
                nol_t({cid:"au-bbc",content:"0",server:"secure-au"}).record().post();
            }
            /*]]>*/
        </script>
        <!-- End Nielsen Online SiteCensus V6.0 -->


            <!-- Start Nielsen Online SiteCensus V6.0 -->
        <script type="text/javascript">
            /*<![CDATA[*/
            if (BBC.adverts.isActive('nielsen-nz')) {
                <!-- COPYRIGHT 2009 Nielsen Online -->
                document.write('<script type="text/javascript" src="' + ('https:' == document.location.protocol ? 'https:' : 'http:') + '//secure-nz.imrworldwide.com/v60.js">\x3C/script>');
            }
            /*]]>*/
        </script>                                   <script type="text/javascript">                 /*<![CDATA[*/
            if (BBC.adverts.isActive('nielsen-nz')) {
                nol_t({cid:"nz-bbc",content:"0",server:"secure-nz"}).record().post();
            }
            /*]]>*/
        </script>
        <!-- End Nielsen Online SiteCensus V6.0 -->


            <!-- Start Nielsen Online SiteCensus V6.0 -->
        <script type="text/javascript">
            /*<![CDATA[*/
            if (BBC.adverts.isActive('nielsen-us')) {
                <!-- COPYRIGHT 2010 Nielsen Online -->
               (function () {
                   var d = new Image(1, 1);
                   d.onerror = d.onload = function () {
                       d.onerror = d.onload = null;
                   };
                   d.src = [('https:' == document.location.protocol ? 'https:' : 'http:') + "//secure-us.imrworldwide.com/cgi-bin/m?ci=us-804789h&amp;cg=0&amp;cc=1&amp;si=", encodeURI(window.location.href), "&amp;rp=",
                   encodeURI(document.referrer), "&amp;ts=compact&amp;rnd=", (new Date()).getTime()].join('');
                })();
            }
            /*]]>*/
        </script>
        <!-- End Nielsen Online SiteCensus V6.0 -->


            <!-- Effective Measure BBCCOM-1195 -->
        <script type="text/javascript">
            /*<![CDATA[*/
            if (BBC.adverts.isActive('analytics')) {
                (function() {
                    var em = document.createElement('script'); em.type = 'text/javascript'; em.async = true;
                    em.src = ('https:' == document.location.protocol ? 'https://me-ssl' : 'http://me-cdn') + '.effectivemeasure.net/em.js';
                    var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(em, s);
                })();
            }
            /*]]>*/
        </script>
        <!-- End Effective Measure -->


            <!-- AGOF BBCCOM-1271 -->
        <script type="text/javascript">
            /*<![CDATA[*/
            if (BBC.adverts.isActive('agof')) {
                var ivwcode="default";
                if(typeof BBC != 'undefined' && typeof BBC.adverts != 'undefined'){
                    ivwcode = BBC.adverts.getSectionPath();
                }
                var IVW="http://bbc.ivwbox.de/cgi-bin/ivw/CP/"+ivwcode+";";
                document.write("<img src=\""+IVW+"?r="+encodeURI(document.referrer)+"&d="+(Math.random()*100000)+"\" width=\"1\" height=\"1\" alt=\"szmtag\" />");
            }
            /*]]>*/
        </script>
        <script type="text/javascript">
            /*<![CDATA[*/
            if (BBC.adverts.isActive('agof')) {
                var szmvars="bbc//CP/"+ivwcode;
                document.write('<script src="http://bbc.ivwbox.de/2004/01/survey.js" type="text/javascript">\x3C/script>');
            }
            /*]]>*/
        </script>
        <!-- End AGOF -->


    
<!-- BBCDOTCOM Body Last -->
 </div> </div>  <script type="text/javascript"> if (typeof require !== 'undefined') { require(['istats-1'], function(istats){ istats.track('external', { region: document.getElementById('blq-main') }); istats.track('download', { region: document.getElementById('blq-main') }); }); } </script>  <script type="text/html" id="blq-panel-template-promo"><![CDATA[ <div id="blq-panel-promo" class="blq-masthead-container"></div> ]]></script> <script type="text/html" id="blq-panel-template-more"><![CDATA[ <div id="blq-panel-more" class="blq-masthead-container  blq-clearfix" xml:lang="en-GB" dir="ltr"> <div class="blq-panel-container panel-paneltype-more"> <div class="panel-header"> <h2> <a href="/a-z/">  More&hellip;  </a> </h2>  <a href="/a-z/" class="panel-header-links panel-header-link">Full A-Z<span class="blq-hide"> of BBC sites</span></a>  </div> <div class="panel-component panel-links">       <ul>   <li> <a href="/cbbc/"  >CBBC</a> </li>    <li> <a href="/cbeebies/"  >CBeebies</a> </li>    <li> <a href="/comedy/"  >Comedy</a> </li>   </ul>  <ul>   <li> <a href="/food/"  >Food</a> </li>    <li> <a href="/history/"  >History</a> </li>    <li> <a href="/learning/"  >Learning</a> </li>   </ul>  <ul>   <li> <a href="/music/"  >Music</a> </li>    <li> <a href="/science/"  >Science</a> </li>    <li> <a href="/nature/"  >Nature</a> </li>   </ul>  <ul>   <li> <a href="/local/"  >Local</a> </li>    <li> <a href="/travel/"  >Travel</a> </li>   </ul>   </div> </div> ]]></script>              </body> </html>           