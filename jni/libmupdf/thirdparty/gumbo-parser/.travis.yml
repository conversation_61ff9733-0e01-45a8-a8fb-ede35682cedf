language: c++

compiler:
  - gcc
  - clang

os:
  - linux
  - osx

install:
  - wget 'https://googletest.googlecode.com/files/gtest-1.7.0.zip'
  - unzip gtest-1.7.0.zip
  - ln -s gtest-1.7.0 gtest
  - sudo pip install BeautifulSoup
  - sudo pip install html5lib==0.95

script:
  - ./autogen.sh && ./configure && make && make check
  - python python/gumbo/gumboc_test.py
  - python python/gumbo/html5lib_adapter_test.py
  - python python/gumbo/soup_adapter_test.py
  - sudo make install
  - g++ examples/clean_text.cc `pkg-config --cflags --libs gumbo`
  - sudo python setup.py sdist install
  - python -c 'import gumbo; gumbo.parse("Foo")'
