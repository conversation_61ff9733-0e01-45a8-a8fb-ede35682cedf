apply plugin: 'com.android.library'
apply plugin: 'maven-publish'

group = 'com.artifex.mupdf'
version = '1.26.3'

buildscript {
	repositories {
		if (project.hasProperty('MAVEN_REPO')) {
			maven { url MAVEN_REPO }
		} else {
			maven { url "file://${System.properties['user.home']}/MAVEN" }
		}
		google()
		mavenCentral()
	}
	dependencies {
		classpath 'com.android.tools.build:gradle:7.2.2'
	}
}

allprojects {
	repositories {
		if (project.hasProperty('MAVEN_REPO')) {
			maven { url MAVEN_REPO }
		} else {
			maven { url "file://${System.properties['user.home']}/MAVEN" }
		}
		google()
		mavenCentral()
	}
}

android {
	compileSdkVersion 33

	defaultConfig {
		minSdkVersion 21
		targetSdkVersion 34

		externalNativeBuild.ndkBuild.arguments '-j4'

		// Set ABI_FILTERS in your gradle.properties file
		if (project.hasProperty('ABI_FILTERS')) {
			ndk.abiFilters = []
			ndk.abiFilters.addAll(ABI_FILTERS.split(',').collect{it as String})
		}
	}

	ndkVersion "27.0.12077973"

	sourceSets {
		main {
			manifest.srcFile 'AndroidManifest.xml'
			java.srcDirs 'libmupdf/platform/java/src'
		}
	}

	externalNativeBuild {
		ndkBuild.path 'libmupdf/platform/java/Android.mk'
	}
}

project.afterEvaluate {
	publishing {
		publications {
			release(MavenPublication) {
				artifactId 'fitz'
				artifact(bundleReleaseAar)

				pom {
					name = 'fitz'
					url = 'http://www.mupdf.com'
					licenses {
						license {
							name = 'GNU Affero General Public License'
							url = 'https://www.gnu.org/licenses/agpl-3.0.html'
						}
					}
				}
				pom.withXml {
					final dependenciesNode = asNode().appendNode('dependencies')
					configurations.implementation.allDependencies.each {
						def dependencyNode = dependenciesNode.appendNode('dependency')
						dependencyNode.appendNode('groupId', it.group)
						dependencyNode.appendNode('artifactId', it.name)
						dependencyNode.appendNode('version', it.version)
					}
				}
			}
		}
		repositories {
			maven {
				name 'Local'
				if (project.hasProperty('MAVEN_REPO')) {
					url = MAVEN_REPO
				} else {
					url = "file://${System.properties['user.home']}/MAVEN"
				}
			}
		}
	}
}
